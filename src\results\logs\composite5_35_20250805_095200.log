2025-08-05 09:52:00,362 - __main__ - INFO - composite5_35 开始进化第 1 代
2025-08-05 09:52:00,362 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:00,363 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:00,365 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9265.000, 多样性=0.962
2025-08-05 09:52:00,367 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:00,369 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.962
2025-08-05 09:52:00,370 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:00,372 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:00,372 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:00,372 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:00,372 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:00,384 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -2784.020, 聚类评分: 0.000, 覆盖率: 0.102, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:00,384 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:00,384 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:00,384 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 09:52:00,486 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_81_20250805_095200.html
2025-08-05 09:52:00,541 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_81_20250805_095200.html
2025-08-05 09:52:00,541 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 81
2025-08-05 09:52:00,541 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:00,541 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1690秒
2025-08-05 09:52:00,541 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 162, 'max_size': 500, 'hits': 0, 'misses': 162, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 530, 'misses': 290, 'hit_rate': 0.6463414634146342, 'evictions': 190, 'ttl': 7200}}
2025-08-05 09:52:00,542 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2784.019999999999, 'local_optima_density': 0.1, 'gradient_variance': 873957243.5396, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1017, 'fitness_entropy': 0.8427376486136672, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2784.020)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.102)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358720.3841512, 'performance_metrics': {}}}
2025-08-05 09:52:00,542 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:00,542 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:00,542 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:00,542 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:00,544 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,544 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:00,544 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,544 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:00,544 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:00,544 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,545 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:00,545 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:00,545 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:00,545 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:00,545 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:00,545 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,548 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,549 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36041.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,549 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,549 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 36041.00)
2025-08-05 09:52:00,549 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:00,549 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:00,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,550 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12648.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,551 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12648.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,551 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 12648.00)
2025-08-05 09:52:00,551 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:00,552 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:00,552 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,555 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,555 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35287.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,555 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35287.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,555 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 35287.00)
2025-08-05 09:52:00,555 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:00,556 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:00,556 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:00,556 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 64104.0
2025-08-05 09:52:00,563 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:00,563 - ExploitationExpert - INFO - res_population_costs: [9082.0, 9061]
2025-08-05 09:52:00,563 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64)]
2025-08-05 09:52:00,564 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:00,564 - ExploitationExpert - INFO - populations: [{'tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12648.0}, {'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35287.0}, {'tour': array([20, 27,  8, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34], dtype=int64), 'cur_cost': 64104.0}, {'tour': array([10,  4,  3, 25, 24, 20, 13, 26,  0,  1, 15, 22, 31, 16, 17, 21, 32,
        5, 11, 14, 33, 19,  8, 27,  7,  6, 29,  2, 28, 34, 23,  9, 12, 30,
       18], dtype=int64), 'cur_cost': 61314.0}, {'tour': array([15,  2,  6, 18,  0,  7,  3, 32, 31, 20, 23, 17, 10, 11, 29, 27, 33,
       34, 26,  8,  4,  5, 13, 25, 22, 28, 24, 30,  1, 21, 19, 14, 12,  9,
       16], dtype=int64), 'cur_cost': 44920.0}, {'tour': array([ 4, 34, 26, 16, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65923.0}, {'tour': array([16,  3, 32, 12, 29, 19, 22,  2,  6, 20, 17, 28, 23,  0,  7, 15,  9,
       34, 13,  5, 14, 33, 10, 21, 24, 30, 11, 18, 27, 25,  1, 31,  4, 26,
        8], dtype=int64), 'cur_cost': 64347.0}, {'tour': array([ 4, 24, 22, 18, 17,  6, 28, 13, 34,  2, 12, 21, 15, 32, 10, 27,  7,
        5, 30,  8, 29, 31,  9, 33,  1,  0, 19, 20,  3, 14, 16, 23, 25, 26,
       11], dtype=int64), 'cur_cost': 59459.0}, {'tour': array([ 0, 12, 16, 18, 34, 26, 21, 33, 14, 22, 15, 23, 31, 27, 10, 28,  3,
        1, 30,  2,  8, 25, 20, 24, 11, 13,  7, 32,  4, 17, 19, 29,  6,  5,
        9], dtype=int64), 'cur_cost': 61297.0}]
2025-08-05 09:52:00,566 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:00,566 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 209, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 209, 'cache_hits': 0, 'similarity_calculations': 966, 'cache_hit_rate': 0.0, 'cache_size': 966}}
2025-08-05 09:52:00,566 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([20, 27,  8, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34], dtype=int64), 'cur_cost': 64104.0, 'intermediate_solutions': [{'tour': array([26, 17,  6, 19,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66635.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 26, 17,  6,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 64358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 19, 26, 17,  6,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 19, 26, 17,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  4, 19, 26, 17,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:00,567 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 64104.00)
2025-08-05 09:52:00,567 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:00,567 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:00,567 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,569 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28774.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,570 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 28774.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,570 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 28774.00)
2025-08-05 09:52:00,570 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:00,570 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:00,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,571 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14318.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,571 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14318.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,571 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14318.00)
2025-08-05 09:52:00,572 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:00,572 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:00,572 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:00,572 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 57405.0
2025-08-05 09:52:00,581 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 09:52:00,581 - ExploitationExpert - INFO - res_population_costs: [9082.0, 9061]
2025-08-05 09:52:00,581 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64)]
2025-08-05 09:52:00,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:00,582 - ExploitationExpert - INFO - populations: [{'tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12648.0}, {'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35287.0}, {'tour': array([20, 27,  8, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34], dtype=int64), 'cur_cost': 64104.0}, {'tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 28774.0}, {'tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14318.0}, {'tour': array([30,  5,  1,  2, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9], dtype=int64), 'cur_cost': 57405.0}, {'tour': array([16,  3, 32, 12, 29, 19, 22,  2,  6, 20, 17, 28, 23,  0,  7, 15,  9,
       34, 13,  5, 14, 33, 10, 21, 24, 30, 11, 18, 27, 25,  1, 31,  4, 26,
        8], dtype=int64), 'cur_cost': 64347.0}, {'tour': array([ 4, 24, 22, 18, 17,  6, 28, 13, 34,  2, 12, 21, 15, 32, 10, 27,  7,
        5, 30,  8, 29, 31,  9, 33,  1,  0, 19, 20,  3, 14, 16, 23, 25, 26,
       11], dtype=int64), 'cur_cost': 59459.0}, {'tour': array([ 0, 12, 16, 18, 34, 26, 21, 33, 14, 22, 15, 23, 31, 27, 10, 28,  3,
        1, 30,  2,  8, 25, 20, 24, 11, 13,  7, 32,  4, 17, 19, 29,  6,  5,
        9], dtype=int64), 'cur_cost': 61297.0}]
2025-08-05 09:52:00,584 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:00,584 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 210, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 210, 'cache_hits': 0, 'similarity_calculations': 967, 'cache_hit_rate': 0.0, 'cache_size': 967}}
2025-08-05 09:52:00,585 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([30,  5,  1,  2, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9], dtype=int64), 'cur_cost': 57405.0, 'intermediate_solutions': [{'tour': array([26, 34,  4, 16, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65837.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 26, 34,  4, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 67911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 16, 26, 34,  4, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 16, 26, 34, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 67887.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 20, 16, 26, 34, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:00,585 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 57405.00)
2025-08-05 09:52:00,585 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:00,586 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:00,586 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,587 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:00,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46760.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,588 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 23, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 46760.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,588 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 46760.00)
2025-08-05 09:52:00,588 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:00,588 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:00,588 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,589 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:00,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,589 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49344.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,589 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 49344.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,590 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 49344.00)
2025-08-05 09:52:00,590 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:00,590 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:00,590 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,591 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,591 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9388.0, 路径长度: 35, 收集中间解: 0
2025-08-05 09:52:00,592 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9388.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,592 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 9388.00)
2025-08-05 09:52:00,592 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:00,592 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:00,593 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12648.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35287.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 27,  8, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34], dtype=int64), 'cur_cost': 64104.0, 'intermediate_solutions': [{'tour': array([26, 17,  6, 19,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66635.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 26, 17,  6,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 64358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 19, 26, 17,  6,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 19, 26, 17,  4,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  4, 19, 26, 17,  2, 20,  3, 25, 18, 10, 29, 31, 21, 14, 30, 23,
       28,  1,  0,  5, 13, 12, 16,  7,  8, 33, 15, 24,  9, 34, 11, 27, 32,
       22], dtype=int64), 'cur_cost': 66696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 28774.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14318.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([30,  5,  1,  2, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9], dtype=int64), 'cur_cost': 57405.0, 'intermediate_solutions': [{'tour': array([26, 34,  4, 16, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65837.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 26, 34,  4, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 67911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 16, 26, 34,  4, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 16, 26, 34, 20, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 67887.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 20, 16, 26, 34, 11, 24, 31, 29, 22,  2, 32, 19, 10, 25, 21,  5,
       13, 30,  0, 17, 12,  7, 28, 14,  9, 18, 33,  3, 27,  1, 23,  8,  6,
       15], dtype=int64), 'cur_cost': 65894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 23, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 46760.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 49344.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9388.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:00,594 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:00,594 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:00,596 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9388.000, 多样性=0.947
2025-08-05 09:52:00,596 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:00,596 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:00,596 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:00,596 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08049638488596778, 'best_improvement': -0.013275769023205613}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.015181518151815081}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.13225427730635278, 'recent_improvements': [-0.09928248418884407, -0.17837900511363125, 0.16522607042386148], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.14285714285714285, 'new_diversity': 0.14285714285714285, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:00,597 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:00,597 - __main__ - INFO - composite5_35 开始进化第 2 代
2025-08-05 09:52:00,598 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:00,598 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:00,598 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9388.000, 多样性=0.947
2025-08-05 09:52:00,598 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:00,600 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.947
2025-08-05 09:52:00,600 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:00,601 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.143
2025-08-05 09:52:00,602 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:00,603 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:00,603 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 09:52:00,603 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 09:52:00,619 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: -1119.883, 聚类评分: 0.000, 覆盖率: 0.103, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:00,619 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:00,619 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:00,619 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 09:52:00,624 - visualization.landscape_visualizer - INFO - 插值约束: 160 个点被约束到最小值 9061.00
2025-08-05 09:52:00,720 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_82_20250805_095200.html
2025-08-05 09:52:00,775 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_82_20250805_095200.html
2025-08-05 09:52:00,775 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 82
2025-08-05 09:52:00,775 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:00,775 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1725秒
2025-08-05 09:52:00,775 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1119.883333333333, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 358879346.98305553, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1029, 'fitness_entropy': 0.8730017623570717, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1119.883)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.103)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358720.619834, 'performance_metrics': {}}}
2025-08-05 09:52:00,775 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:00,775 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:00,776 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:00,776 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:00,776 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,777 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:00,777 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,777 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:00,777 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:00,777 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:00,777 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:00,778 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:00,778 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:00,778 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:00,778 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:00,778 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,779 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,781 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11711.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,781 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11711.0, 'intermediate_solutions': [{'tour': [7, 23, 1, 19, 30, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 17, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 46678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 34, 18, 12, 24, 2, 25, 29, 28, 27], 'cur_cost': 36038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 23, 1, 19, 17, 16, 0, 15, 14, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,781 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 11711.00)
2025-08-05 09:52:00,781 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:00,781 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:00,781 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,782 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,782 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,783 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,783 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,783 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9638.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,783 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9638.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 17, 32, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 23, 29, 33, 26], 'cur_cost': 23602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 26, 33, 29, 32, 27, 31], 'cur_cost': 12692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 22, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17360.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,783 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 9638.00)
2025-08-05 09:52:00,783 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:00,783 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:00,784 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,785 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,786 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31282.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,786 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31282.0, 'intermediate_solutions': [{'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 29, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 0, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 41936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 1, 15, 8, 13, 16, 23, 4, 31, 32], 'cur_cost': 35287.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 7, 17, 11, 5, 6, 2, 20, 3, 22, 0, 24, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,787 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 31282.00)
2025-08-05 09:52:00,787 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:00,787 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:00,787 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:00,787 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 63626.0
2025-08-05 09:52:00,796 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:00,796 - ExploitationExpert - INFO - res_population_costs: [9061, 9082.0, 9061]
2025-08-05 09:52:00,796 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 09:52:00,797 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:00,797 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11711.0}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9638.0}, {'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31282.0}, {'tour': array([27,  0,  1, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6], dtype=int64), 'cur_cost': 63626.0}, {'tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 28774.0}, {'tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14318.0}, {'tour': [30, 5, 1, 2, 27, 15, 10, 24, 26, 11, 13, 6, 7, 12, 33, 3, 18, 16, 14, 8, 34, 21, 0, 19, 32, 20, 28, 25, 4, 22, 31, 29, 23, 17, 9], 'cur_cost': 57405.0}, {'tour': [7, 23, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 46760.0}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 49344.0}, {'tour': [0, 1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9388.0}]
2025-08-05 09:52:00,798 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:00,798 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 211, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 211, 'cache_hits': 0, 'similarity_calculations': 969, 'cache_hit_rate': 0.0, 'cache_size': 969}}
2025-08-05 09:52:00,799 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([27,  0,  1, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6], dtype=int64), 'cur_cost': 63626.0, 'intermediate_solutions': [{'tour': array([ 8, 27, 20, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26,  8, 27, 20, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 26,  8, 27, 20, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 26,  8, 27, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 28, 26,  8, 27, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:00,799 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 63626.00)
2025-08-05 09:52:00,799 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:00,799 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:00,799 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,800 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:00,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,801 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45017.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,801 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 27, 32, 29, 31, 12], 'cur_cost': 45017.0, 'intermediate_solutions': [{'tour': [33, 26, 28, 29, 24, 19, 25, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 22, 32, 27, 31, 1, 5, 10], 'cur_cost': 39445.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 24, 29, 28, 26, 33, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 30792.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 5, 1, 10], 'cur_cost': 28839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,801 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 45017.00)
2025-08-05 09:52:00,801 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:00,802 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:00,802 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,803 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30200.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,804 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30200.0, 'intermediate_solutions': [{'tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 33, 12, 30, 25, 34, 28, 31, 27, 32, 29, 8, 26], 'cur_cost': 26230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 21, 3, 5, 1, 7, 14, 10, 13, 24, 22, 16, 20, 19, 18, 17, 23, 2, 4, 6, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 21, 3, 5, 1, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 7, 31, 27, 32, 29, 33, 26], 'cur_cost': 18068.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,805 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 30200.00)
2025-08-05 09:52:00,805 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:00,805 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:00,805 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:00,805 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 57079.0
2025-08-05 09:52:00,814 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:00,814 - ExploitationExpert - INFO - res_population_costs: [9061, 9082.0, 9061, 9061.0]
2025-08-05 09:52:00,815 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64)]
2025-08-05 09:52:00,816 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:00,817 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11711.0}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9638.0}, {'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31282.0}, {'tour': array([27,  0,  1, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6], dtype=int64), 'cur_cost': 63626.0}, {'tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 27, 32, 29, 31, 12], 'cur_cost': 45017.0}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30200.0}, {'tour': array([ 3,  0, 20, 15, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30], dtype=int64), 'cur_cost': 57079.0}, {'tour': [7, 23, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 46760.0}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 49344.0}, {'tour': [0, 1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9388.0}]
2025-08-05 09:52:00,818 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:00,818 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 212, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 212, 'cache_hits': 0, 'similarity_calculations': 972, 'cache_hit_rate': 0.0, 'cache_size': 972}}
2025-08-05 09:52:00,819 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 3,  0, 20, 15, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30], dtype=int64), 'cur_cost': 57079.0, 'intermediate_solutions': [{'tour': array([ 1,  5, 30,  2, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  1,  5, 30, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 54180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27,  2,  1,  5, 30, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30,  2,  1,  5, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 27,  2,  1,  5, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 54113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:00,820 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 57079.00)
2025-08-05 09:52:00,820 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:00,820 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:00,820 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,822 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:00,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,824 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50762.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,824 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [22, 26, 11, 16, 1, 4, 7, 21, 23, 5, 6, 0, 2, 8, 19, 24, 20, 30, 14, 25, 13, 32, 33, 31, 34, 28, 18, 3, 10, 29, 17, 27, 12, 15, 9], 'cur_cost': 50762.0, 'intermediate_solutions': [{'tour': [7, 23, 2, 17, 13, 0, 3, 22, 4, 24, 21, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 49377.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 2, 32, 15, 9, 12, 20, 10, 16, 14, 6, 11, 13, 24, 4, 22, 3, 0, 21, 17, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 50098.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 23, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 49753.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,824 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 50762.00)
2025-08-05 09:52:00,824 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:00,824 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:00,825 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,827 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:00,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,828 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36340.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,828 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36340.0, 'intermediate_solutions': [{'tour': [34, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 7, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 56335.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 18, 12, 28, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 44065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 34, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 52694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,828 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 36340.00)
2025-08-05 09:52:00,828 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:00,828 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:00,829 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:00,830 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11664.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:00,831 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11664.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 5, 3, 4, 28, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 7, 31, 27, 32, 29, 33, 26], 'cur_cost': 16813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 4, 3, 5, 6, 1, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9375.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 0, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12369.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:00,831 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 11664.00)
2025-08-05 09:52:00,831 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:00,831 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:00,832 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11711.0, 'intermediate_solutions': [{'tour': [7, 23, 1, 19, 30, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 17, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 46678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 1, 19, 17, 16, 0, 14, 15, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 34, 18, 12, 24, 2, 25, 29, 28, 27], 'cur_cost': 36038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 23, 1, 19, 17, 16, 0, 15, 14, 4, 3, 13, 11, 6, 22, 21, 9, 10, 20, 5, 8, 33, 32, 26, 30, 31, 28, 29, 25, 2, 24, 12, 18, 34, 27], 'cur_cost': 36041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9638.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 17, 32, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 23, 29, 33, 26], 'cur_cost': 23602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 26, 33, 29, 32, 27, 31], 'cur_cost': 12692.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 17, 23, 21, 19, 18, 20, 16, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 22, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17360.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31282.0, 'intermediate_solutions': [{'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 29, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 0, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 41936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 7, 17, 11, 5, 2, 20, 3, 22, 0, 24, 6, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 1, 15, 8, 13, 16, 23, 4, 31, 32], 'cur_cost': 35287.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 7, 17, 11, 5, 6, 2, 20, 3, 22, 0, 24, 12, 10, 14, 21, 18, 9, 19, 30, 29, 27, 28, 33, 26, 25, 32, 31, 4, 23, 16, 13, 8, 15, 1], 'cur_cost': 35003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([27,  0,  1, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6], dtype=int64), 'cur_cost': 63626.0, 'intermediate_solutions': [{'tour': array([ 8, 27, 20, 26, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26,  8, 27, 20, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 26,  8, 27, 20, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 26,  8, 27, 28, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 28, 26,  8, 27, 29, 23, 10,  1, 16, 25, 12,  5, 19, 17, 30, 13,
       22,  9, 21, 15,  6,  3, 18,  7, 32, 33,  4,  0,  2, 11, 24, 31, 14,
       34]), 'cur_cost': 64065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 27, 32, 29, 31, 12], 'cur_cost': 45017.0, 'intermediate_solutions': [{'tour': [33, 26, 28, 29, 24, 19, 25, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 22, 32, 27, 31, 1, 5, 10], 'cur_cost': 39445.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 24, 29, 28, 26, 33, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 1, 5, 10], 'cur_cost': 30792.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 26, 28, 29, 24, 19, 22, 20, 3, 11, 4, 21, 13, 14, 15, 7, 16, 18, 6, 12, 8, 9, 23, 17, 2, 0, 30, 34, 25, 32, 27, 31, 5, 1, 10], 'cur_cost': 28839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30200.0, 'intermediate_solutions': [{'tour': [0, 11, 21, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 33, 12, 30, 25, 34, 28, 31, 27, 32, 29, 8, 26], 'cur_cost': 26230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 21, 3, 5, 1, 7, 14, 10, 13, 24, 22, 16, 20, 19, 18, 17, 23, 2, 4, 6, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17494.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 21, 3, 5, 1, 6, 4, 2, 23, 17, 18, 19, 20, 16, 22, 24, 13, 10, 14, 9, 15, 8, 12, 30, 25, 34, 28, 7, 31, 27, 32, 29, 33, 26], 'cur_cost': 18068.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  0, 20, 15, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30], dtype=int64), 'cur_cost': 57079.0, 'intermediate_solutions': [{'tour': array([ 1,  5, 30,  2, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  1,  5, 30, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 54180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27,  2,  1,  5, 30, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30,  2,  1,  5, 27, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 57405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 27,  2,  1,  5, 15, 10, 24, 26, 11, 13,  6,  7, 12, 33,  3, 18,
       16, 14,  8, 34, 21,  0, 19, 32, 20, 28, 25,  4, 22, 31, 29, 23, 17,
        9]), 'cur_cost': 54113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [22, 26, 11, 16, 1, 4, 7, 21, 23, 5, 6, 0, 2, 8, 19, 24, 20, 30, 14, 25, 13, 32, 33, 31, 34, 28, 18, 3, 10, 29, 17, 27, 12, 15, 9], 'cur_cost': 50762.0, 'intermediate_solutions': [{'tour': [7, 23, 2, 17, 13, 0, 3, 22, 4, 24, 21, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 49377.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 23, 2, 32, 15, 9, 12, 20, 10, 16, 14, 6, 11, 13, 24, 4, 22, 3, 0, 21, 17, 26, 31, 29, 5, 18, 34, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 50098.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 2, 17, 21, 0, 3, 22, 4, 24, 13, 11, 6, 14, 16, 10, 20, 12, 9, 15, 32, 26, 31, 29, 5, 18, 34, 23, 33, 8, 1, 28, 25, 27, 19, 30], 'cur_cost': 49753.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36340.0, 'intermediate_solutions': [{'tour': [34, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 7, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 56335.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 34, 29, 8, 24, 18, 12, 28, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 44065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 17, 11, 23, 16, 0, 14, 22, 4, 3, 34, 13, 6, 1, 9, 10, 19, 33, 32, 26, 2, 31, 29, 8, 24, 28, 12, 18, 25, 27, 30, 15, 20, 21, 5], 'cur_cost': 52694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11664.0, 'intermediate_solutions': [{'tour': [0, 1, 6, 5, 3, 4, 28, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 7, 31, 27, 32, 29, 33, 26], 'cur_cost': 16813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 4, 3, 5, 6, 1, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9375.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 5, 3, 4, 7, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 0, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12369.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:00,833 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:00,833 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:00,835 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9638.000, 多样性=0.926
2025-08-05 09:52:00,835 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:00,835 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:00,835 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:00,836 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -3.634213076268161e-05, 'best_improvement': -0.026629740093736684}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02278820375335145}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.12943769499979954, 'recent_improvements': [-0.17837900511363125, 0.16522607042386148, 0.08049638488596778], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.5523809523809523, 'new_diversity': 0.5523809523809523, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:00,836 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:00,836 - __main__ - INFO - composite5_35 开始进化第 3 代
2025-08-05 09:52:00,836 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:00,837 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:00,837 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9638.000, 多样性=0.926
2025-08-05 09:52:00,838 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:00,839 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.926
2025-08-05 09:52:00,840 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:00,841 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.552
2025-08-05 09:52:00,843 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:00,843 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:00,843 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:00,843 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:00,864 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -1489.329, 聚类评分: 0.000, 覆盖率: 0.104, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:00,864 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:00,864 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:00,864 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 09:52:00,869 - visualization.landscape_visualizer - INFO - 插值约束: 232 个点被约束到最小值 9061.00
2025-08-05 09:52:00,963 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_83_20250805_095200.html
2025-08-05 09:52:01,018 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_83_20250805_095200.html
2025-08-05 09:52:01,018 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 83
2025-08-05 09:52:01,018 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:01,018 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1753秒
2025-08-05 09:52:01,019 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1489.3285714285719, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 342713267.9806123, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1041, 'fitness_entropy': 0.8053204460925903, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1489.329)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.104)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358720.8642488, 'performance_metrics': {}}}
2025-08-05 09:52:01,019 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:01,019 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:01,019 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:01,019 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:01,020 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:52:01,020 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:01,020 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:52:01,020 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,020 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:01,020 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore']
2025-08-05 09:52:01,021 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,021 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:01,021 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:01,021 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:01,021 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:01,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,024 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,024 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41353.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,025 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 41353.0, 'intermediate_solutions': [{'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 28, 24, 30, 25, 34, 16, 31, 27, 32, 29, 33, 26], 'cur_cost': 22410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 19, 26, 33, 29, 32, 27, 31, 28, 34, 25, 30, 24, 16, 20, 18, 21, 17, 23, 2, 4, 6, 7, 1, 5, 3, 12, 13, 10, 8, 15, 9, 14, 11], 'cur_cost': 11738.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 4, 13, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14775.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,025 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 41353.00)
2025-08-05 09:52:01,025 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:01,026 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:01,026 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,026 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,026 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,028 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14427.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,028 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14427.0, 'intermediate_solutions': [{'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 21, 23, 17, 2, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 24, 22, 16, 20, 18, 19, 21, 17, 23, 2, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 16, 21, 19, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,028 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 14427.00)
2025-08-05 09:52:01,028 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:01,028 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:01,028 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,029 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:01,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,030 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,030 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54619.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,030 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54619.0, 'intermediate_solutions': [{'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 27, 24, 16, 0, 14, 22, 19, 13, 30, 33, 18, 31, 28, 29, 2, 15], 'cur_cost': 40039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 22, 14, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 33294.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 25, 26, 34, 3, 7, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31292.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,030 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 54619.00)
2025-08-05 09:52:01,030 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:01,030 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,030 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,031 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 66182.0
2025-08-05 09:52:01,042 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:01,042 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9082.0, 9061.0]
2025-08-05 09:52:01,042 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 09:52:01,044 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,044 - ExploitationExpert - INFO - populations: [{'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 41353.0}, {'tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14427.0}, {'tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54619.0}, {'tour': array([10, 29,  1,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5], dtype=int64), 'cur_cost': 66182.0}, {'tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 27, 32, 29, 31, 12], 'cur_cost': 45017.0}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30200.0}, {'tour': [3, 0, 20, 15, 14, 9, 26, 13, 28, 8, 29, 32, 12, 11, 4, 27, 1, 24, 21, 33, 17, 18, 25, 6, 34, 2, 19, 31, 5, 7, 22, 16, 23, 10, 30], 'cur_cost': 57079.0}, {'tour': [22, 26, 11, 16, 1, 4, 7, 21, 23, 5, 6, 0, 2, 8, 19, 24, 20, 30, 14, 25, 13, 32, 33, 31, 34, 28, 18, 3, 10, 29, 17, 27, 12, 15, 9], 'cur_cost': 50762.0}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36340.0}, {'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11664.0}]
2025-08-05 09:52:01,045 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:01,045 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 213, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 213, 'cache_hits': 0, 'similarity_calculations': 976, 'cache_hit_rate': 0.0, 'cache_size': 976}}
2025-08-05 09:52:01,045 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([10, 29,  1,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5], dtype=int64), 'cur_cost': 66182.0, 'intermediate_solutions': [{'tour': array([ 1,  0, 27, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 59999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  1,  0, 27, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 63608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 30,  1,  0, 27, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 60310.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 30,  1,  0, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 60354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 12, 30,  1,  0, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 63626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,046 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 66182.00)
2025-08-05 09:52:01,046 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:01,046 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:01,046 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,047 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12310.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,048 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12310.0, 'intermediate_solutions': [{'tour': [22, 5, 34, 10, 20, 3, 21, 27, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 9, 32, 29, 31, 12], 'cur_cost': 48359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 8, 26, 11, 28, 25, 33, 30, 13, 0, 18, 27, 32, 29, 31, 12], 'cur_cost': 45437.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 22, 18, 27, 32, 29, 31, 12], 'cur_cost': 42963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,048 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 12310.00)
2025-08-05 09:52:01,048 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:01,048 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:01,048 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,049 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9844.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,050 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9844.0, 'intermediate_solutions': [{'tour': [5, 20, 24, 4, 1, 16, 3, 19, 7, 2, 21, 22, 6, 15, 8, 18, 0, 23, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 11, 12, 25, 32, 10, 14, 9, 34], 'cur_cost': 36172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 32, 27, 30, 26, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,050 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9844.00)
2025-08-05 09:52:01,050 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:01,050 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,050 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,050 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 67342.0
2025-08-05 09:52:01,062 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:01,062 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9082.0, 9061.0, 9061.0]
2025-08-05 09:52:01,062 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 09:52:01,064 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,064 - ExploitationExpert - INFO - populations: [{'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 41353.0}, {'tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14427.0}, {'tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54619.0}, {'tour': array([10, 29,  1,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5], dtype=int64), 'cur_cost': 66182.0}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12310.0}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9844.0}, {'tour': array([15,  0, 13, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7], dtype=int64), 'cur_cost': 67342.0}, {'tour': [22, 26, 11, 16, 1, 4, 7, 21, 23, 5, 6, 0, 2, 8, 19, 24, 20, 30, 14, 25, 13, 32, 33, 31, 34, 28, 18, 3, 10, 29, 17, 27, 12, 15, 9], 'cur_cost': 50762.0}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36340.0}, {'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11664.0}]
2025-08-05 09:52:01,065 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,065 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 214, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 214, 'cache_hits': 0, 'similarity_calculations': 981, 'cache_hit_rate': 0.0, 'cache_size': 981}}
2025-08-05 09:52:01,066 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([15,  0, 13, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7], dtype=int64), 'cur_cost': 67342.0, 'intermediate_solutions': [{'tour': array([20,  0,  3, 15, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 57021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 20,  0,  3, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 59685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 15, 20,  0,  3,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 59712.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 15, 20,  0, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 60092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 14, 15, 20,  0,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 60106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,066 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 67342.00)
2025-08-05 09:52:01,066 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:01,066 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,066 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,067 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 58781.0
2025-08-05 09:52:01,078 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:01,078 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9082.0, 9061.0, 9061.0, 9061]
2025-08-05 09:52:01,078 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64)]
2025-08-05 09:52:01,080 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,080 - ExploitationExpert - INFO - populations: [{'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 41353.0}, {'tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14427.0}, {'tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54619.0}, {'tour': array([10, 29,  1,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5], dtype=int64), 'cur_cost': 66182.0}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12310.0}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9844.0}, {'tour': array([15,  0, 13, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7], dtype=int64), 'cur_cost': 67342.0}, {'tour': array([11, 19,  3, 10, 32, 30,  1, 28,  0,  7, 26, 20, 24, 25, 27, 12,  2,
       18, 13, 31, 33,  8,  5, 23, 21, 16, 15,  4,  9, 17, 22, 29, 14,  6,
       34], dtype=int64), 'cur_cost': 58781.0}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36340.0}, {'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11664.0}]
2025-08-05 09:52:01,081 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:01,081 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 215, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 215, 'cache_hits': 0, 'similarity_calculations': 987, 'cache_hit_rate': 0.0, 'cache_size': 987}}
2025-08-05 09:52:01,082 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([11, 19,  3, 10, 32, 30,  1, 28,  0,  7, 26, 20, 24, 25, 27, 12,  2,
       18, 13, 31, 33,  8,  5, 23, 21, 16, 15,  4,  9, 17, 22, 29, 14,  6,
       34], dtype=int64), 'cur_cost': 58781.0, 'intermediate_solutions': [{'tour': array([11, 26, 22, 16,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 46056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 11, 26, 22,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 50753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 16, 11, 26, 22,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 51097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 16, 11, 26,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 48759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  1, 16, 11, 26,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 51155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,082 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 58781.00)
2025-08-05 09:52:01,082 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:01,082 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:01,082 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,085 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35652.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,086 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35652.0, 'intermediate_solutions': [{'tour': [29, 27, 26, 32, 28, 5, 22, 6, 8, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 24, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 34435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 13, 9], 'cur_cost': 36338.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 4, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,086 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 35652.00)
2025-08-05 09:52:01,086 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:01,086 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:01,086 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,087 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:01,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67837.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,088 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [32, 20, 1, 7, 11, 16, 13, 24, 3, 6, 15, 4, 19, 17, 8, 22, 10, 26, 12, 27, 25, 23, 0, 28, 30, 14, 21, 34, 18, 33, 9, 31, 2, 29, 5], 'cur_cost': 67837.0, 'intermediate_solutions': [{'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 32, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 11, 29, 33, 26], 'cur_cost': 23718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 32, 27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 23, 2, 4, 6, 7, 29, 33, 26], 'cur_cost': 15435.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 17, 14, 9, 15, 8, 10, 11, 13, 3, 12, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14622.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,088 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 67837.00)
2025-08-05 09:52:01,088 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:01,088 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:01,090 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 41353.0, 'intermediate_solutions': [{'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 28, 24, 30, 25, 34, 16, 31, 27, 32, 29, 33, 26], 'cur_cost': 22410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 19, 26, 33, 29, 32, 27, 31, 28, 34, 25, 30, 24, 16, 20, 18, 21, 17, 23, 2, 4, 6, 7, 1, 5, 3, 12, 13, 10, 8, 15, 9, 14, 11], 'cur_cost': 11738.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 19, 11, 14, 9, 15, 8, 10, 4, 13, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 18, 20, 16, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14775.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14427.0, 'intermediate_solutions': [{'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 21, 23, 17, 2, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12033.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 24, 22, 16, 20, 18, 19, 21, 17, 23, 2, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9941.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 12, 14, 9, 15, 8, 10, 11, 13, 3, 1, 7, 6, 2, 23, 17, 16, 21, 19, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54619.0, 'intermediate_solutions': [{'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 27, 24, 16, 0, 14, 22, 19, 13, 30, 33, 18, 31, 28, 29, 2, 15], 'cur_cost': 40039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 25, 26, 34, 7, 3, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 22, 14, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 33294.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 25, 26, 34, 3, 7, 1, 4, 20, 21, 23, 11, 9, 10, 6, 17, 5, 12, 8, 18, 24, 16, 0, 14, 22, 19, 13, 30, 33, 27, 31, 28, 29, 2, 15], 'cur_cost': 31292.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 29,  1,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5], dtype=int64), 'cur_cost': 66182.0, 'intermediate_solutions': [{'tour': array([ 1,  0, 27, 30, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 59999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  1,  0, 27, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 63608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 30,  1,  0, 27, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 60310.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 30,  1,  0, 12, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 60354.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 12, 30,  1,  0, 31,  7,  2, 20, 10, 32, 18, 33, 17,  9, 19, 16,
       26, 13, 14, 25, 34, 24, 22,  4, 28,  8, 11, 23,  5, 29,  3, 15, 21,
        6]), 'cur_cost': 63626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12310.0, 'intermediate_solutions': [{'tour': [22, 5, 34, 10, 20, 3, 21, 27, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 18, 9, 32, 29, 31, 12], 'cur_cost': 48359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 8, 26, 11, 28, 25, 33, 30, 13, 0, 18, 27, 32, 29, 31, 12], 'cur_cost': 45437.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 34, 10, 20, 3, 21, 9, 1, 7, 6, 4, 17, 23, 19, 24, 16, 15, 14, 2, 0, 13, 30, 33, 25, 28, 11, 26, 8, 22, 18, 27, 32, 29, 31, 12], 'cur_cost': 42963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9844.0, 'intermediate_solutions': [{'tour': [5, 20, 24, 4, 1, 16, 3, 19, 7, 2, 21, 22, 6, 15, 8, 18, 0, 23, 17, 13, 33, 29, 31, 28, 27, 30, 26, 32, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 27, 30, 26, 11, 12, 25, 32, 10, 14, 9, 34], 'cur_cost': 36172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 20, 24, 4, 1, 16, 3, 23, 7, 2, 21, 22, 6, 15, 8, 18, 0, 19, 17, 13, 33, 29, 31, 28, 32, 27, 30, 26, 25, 12, 11, 10, 14, 9, 34], 'cur_cost': 30151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([15,  0, 13, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7], dtype=int64), 'cur_cost': 67342.0, 'intermediate_solutions': [{'tour': array([20,  0,  3, 15, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 57021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 20,  0,  3, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 59685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 15, 20,  0,  3,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 59712.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 15, 20,  0, 14,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 60092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 14, 15, 20,  0,  9, 26, 13, 28,  8, 29, 32, 12, 11,  4, 27,  1,
       24, 21, 33, 17, 18, 25,  6, 34,  2, 19, 31,  5,  7, 22, 16, 23, 10,
       30]), 'cur_cost': 60106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 19,  3, 10, 32, 30,  1, 28,  0,  7, 26, 20, 24, 25, 27, 12,  2,
       18, 13, 31, 33,  8,  5, 23, 21, 16, 15,  4,  9, 17, 22, 29, 14,  6,
       34], dtype=int64), 'cur_cost': 58781.0, 'intermediate_solutions': [{'tour': array([11, 26, 22, 16,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 46056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 11, 26, 22,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 50753.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 16, 11, 26, 22,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 51097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 16, 11, 26,  1,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 48759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  1, 16, 11, 26,  4,  7, 21, 23,  5,  6,  0,  2,  8, 19, 24, 20,
       30, 14, 25, 13, 32, 33, 31, 34, 28, 18,  3, 10, 29, 17, 27, 12, 15,
        9]), 'cur_cost': 51155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35652.0, 'intermediate_solutions': [{'tour': [29, 27, 26, 32, 28, 5, 22, 6, 8, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 24, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 34435.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 4, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 13, 9], 'cur_cost': 36338.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 27, 26, 32, 28, 5, 22, 6, 24, 3, 0, 19, 23, 2, 10, 7, 14, 15, 1, 12, 4, 17, 20, 18, 11, 16, 21, 8, 30, 33, 31, 34, 25, 9, 13], 'cur_cost': 36673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [32, 20, 1, 7, 11, 16, 13, 24, 3, 6, 15, 4, 19, 17, 8, 22, 10, 26, 12, 27, 25, 23, 0, 28, 30, 14, 21, 34, 18, 33, 9, 31, 2, 29, 5], 'cur_cost': 67837.0, 'intermediate_solutions': [{'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 32, 13, 3, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 11, 29, 33, 26], 'cur_cost': 23718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 17, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 32, 27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 23, 2, 4, 6, 7, 29, 33, 26], 'cur_cost': 15435.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 17, 14, 9, 15, 8, 10, 11, 13, 3, 12, 5, 1, 7, 6, 4, 2, 23, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14622.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:01,090 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:01,090 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,092 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9844.000, 多样性=0.934
2025-08-05 09:52:01,093 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:01,093 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:01,093 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:01,093 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.10695099348534214, 'best_improvement': -0.02137372898941689}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008916323731138587}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.08263120627731207, 'recent_improvements': [0.16522607042386148, 0.08049638488596778, -3.634213076268161e-05], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.710204081632653, 'new_diversity': 0.710204081632653, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:01,094 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:01,094 - __main__ - INFO - composite5_35 开始进化第 4 代
2025-08-05 09:52:01,094 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:01,094 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,095 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9844.000, 多样性=0.934
2025-08-05 09:52:01,095 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:01,097 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.934
2025-08-05 09:52:01,097 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:01,099 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.710
2025-08-05 09:52:01,101 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:01,101 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:01,101 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:01,101 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:01,131 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.353, 适应度梯度: 1029.024, 聚类评分: 0.000, 覆盖率: 0.105, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:01,131 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:01,131 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:01,131 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 09:52:01,137 - visualization.landscape_visualizer - INFO - 插值约束: 57 个点被约束到最小值 9061.00
2025-08-05 09:52:01,225 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_84_20250805_095201.html
2025-08-05 09:52:01,276 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_84_20250805_095201.html
2025-08-05 09:52:01,276 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 84
2025-08-05 09:52:01,276 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:01,276 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1750秒
2025-08-05 09:52:01,276 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35294117647058826, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 1029.0235294117647, 'local_optima_density': 0.35294117647058826, 'gradient_variance': 445132053.72650516, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1053, 'fitness_entropy': 0.717104494152742, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.105)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1029.024)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358721.1318865, 'performance_metrics': {}}}
2025-08-05 09:52:01,277 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:01,277 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:01,277 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:01,277 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:01,277 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,277 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:01,277 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,278 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,278 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:01,278 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,278 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,278 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:01,278 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:01,278 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:01,278 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:01,279 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,279 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,279 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,280 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,280 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,280 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,280 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9627.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,280 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9627.0, 'intermediate_solutions': [{'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 26, 19, 12, 17, 9, 8, 21, 14, 22, 30, 4, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 48073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 32, 27, 31, 26, 30, 22, 14, 21, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 44659.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 0, 20, 1, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 5, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 45096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,281 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 9627.00)
2025-08-05 09:52:01,281 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:01,281 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:01,281 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,283 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,283 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,283 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,284 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30824.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,284 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30824.0, 'intermediate_solutions': [{'tour': [10, 9, 18, 15, 14, 11, 0, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 31, 28, 34, 27, 32, 29, 33, 26], 'cur_cost': 14508.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14465.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,284 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 30824.00)
2025-08-05 09:52:01,284 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:01,285 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:01,285 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39919.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,288 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31], 'cur_cost': 39919.0, 'intermediate_solutions': [{'tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 26, 18, 14, 22, 24, 21, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 60104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 32, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 51002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 9, 1, 30, 7, 31, 10, 8, 4, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54599.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,288 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 39919.00)
2025-08-05 09:52:01,288 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:01,288 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,288 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,288 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 64607.0
2025-08-05 09:52:01,300 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:01,300 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9082.0]
2025-08-05 09:52:01,300 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64)]
2025-08-05 09:52:01,303 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,303 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9627.0}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30824.0}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31], 'cur_cost': 39919.0}, {'tour': array([13, 31, 24,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4], dtype=int64), 'cur_cost': 64607.0}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12310.0}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9844.0}, {'tour': [15, 0, 13, 33, 16, 25, 28, 1, 3, 12, 27, 10, 29, 2, 31, 20, 26, 4, 18, 5, 11, 6, 30, 9, 23, 8, 21, 32, 14, 24, 22, 19, 17, 34, 7], 'cur_cost': 67342.0}, {'tour': [11, 19, 3, 10, 32, 30, 1, 28, 0, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 58781.0}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35652.0}, {'tour': [32, 20, 1, 7, 11, 16, 13, 24, 3, 6, 15, 4, 19, 17, 8, 22, 10, 26, 12, 27, 25, 23, 0, 28, 30, 14, 21, 34, 18, 33, 9, 31, 2, 29, 5], 'cur_cost': 67837.0}]
2025-08-05 09:52:01,303 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,303 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 216, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 216, 'cache_hits': 0, 'similarity_calculations': 994, 'cache_hit_rate': 0.0, 'cache_size': 994}}
2025-08-05 09:52:01,304 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([13, 31, 24,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4], dtype=int64), 'cur_cost': 64607.0, 'intermediate_solutions': [{'tour': array([ 1, 29, 10,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  1, 29, 10, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 65798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26,  3,  1, 29, 10, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  3,  1, 29, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 62788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 26,  3,  1, 29, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,304 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 64607.00)
2025-08-05 09:52:01,305 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:01,305 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:01,305 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,307 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,308 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34116.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,308 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 34116.0, 'intermediate_solutions': [{'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 4, 2, 5, 3, 1, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12353.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 25, 30, 13, 10, 8, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 10, 13, 30, 25, 34, 8, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,308 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 34116.00)
2025-08-05 09:52:01,308 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:01,308 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:01,308 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,309 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,310 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12741.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,310 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12741.0, 'intermediate_solutions': [{'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 34, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 2, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 26, 33, 29, 32, 27, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 14], 'cur_cost': 12493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 4, 11, 14, 9, 15, 8, 10, 13, 12, 5, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 10179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,310 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12741.00)
2025-08-05 09:52:01,310 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:01,310 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,311 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,311 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 62376.0
2025-08-05 09:52:01,322 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:01,322 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9082.0]
2025-08-05 09:52:01,322 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64)]
2025-08-05 09:52:01,324 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,324 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9627.0}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30824.0}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31], 'cur_cost': 39919.0}, {'tour': array([13, 31, 24,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4], dtype=int64), 'cur_cost': 64607.0}, {'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 34116.0}, {'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12741.0}, {'tour': array([ 9, 11,  6,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26], dtype=int64), 'cur_cost': 62376.0}, {'tour': [11, 19, 3, 10, 32, 30, 1, 28, 0, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 58781.0}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35652.0}, {'tour': [32, 20, 1, 7, 11, 16, 13, 24, 3, 6, 15, 4, 19, 17, 8, 22, 10, 26, 12, 27, 25, 23, 0, 28, 30, 14, 21, 34, 18, 33, 9, 31, 2, 29, 5], 'cur_cost': 67837.0}]
2025-08-05 09:52:01,325 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:01,326 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 217, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 217, 'cache_hits': 0, 'similarity_calculations': 1002, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,326 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 9, 11,  6,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26], dtype=int64), 'cur_cost': 62376.0, 'intermediate_solutions': [{'tour': array([13,  0, 15, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 13,  0, 15, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67289.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 33, 13,  0, 15, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 33, 13,  0, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 16, 33, 13,  0, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67367.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,326 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 62376.00)
2025-08-05 09:52:01,327 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:01,327 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:01,327 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,328 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11744.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,329 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11744.0, 'intermediate_solutions': [{'tour': [11, 19, 3, 22, 32, 30, 1, 28, 0, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 10, 29, 14, 6, 34], 'cur_cost': 60759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 3, 10, 0, 28, 1, 30, 32, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 59141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 3, 10, 32, 30, 1, 28, 0, 7, 26, 33, 20, 24, 25, 27, 12, 2, 18, 13, 31, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 58840.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,329 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 11744.00)
2025-08-05 09:52:01,330 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:01,330 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:01,330 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,332 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,333 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35343.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,333 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 35343.0, 'intermediate_solutions': [{'tour': [22, 23, 7, 0, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 19, 21, 20, 12, 8, 30, 31], 'cur_cost': 35229.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 20, 21, 0, 34, 12, 8, 30, 31], 'cur_cost': 38993.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 25, 27, 32, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35699.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,333 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 35343.00)
2025-08-05 09:52:01,333 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:01,333 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,333 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 58078.0
2025-08-05 09:52:01,345 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:52:01,345 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9082.0, 9061]
2025-08-05 09:52:01,345 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 26, 25, 34, 30,
        2], dtype=int64)]
2025-08-05 09:52:01,348 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,348 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9627.0}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30824.0}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31], 'cur_cost': 39919.0}, {'tour': array([13, 31, 24,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4], dtype=int64), 'cur_cost': 64607.0}, {'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 34116.0}, {'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12741.0}, {'tour': array([ 9, 11,  6,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26], dtype=int64), 'cur_cost': 62376.0}, {'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11744.0}, {'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 35343.0}, {'tour': array([ 9, 12,  5, 17,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32], dtype=int64), 'cur_cost': 58078.0}]
2025-08-05 09:52:01,349 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,349 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 218, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 218, 'cache_hits': 0, 'similarity_calculations': 1011, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,350 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 9, 12,  5, 17,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32], dtype=int64), 'cur_cost': 58078.0, 'intermediate_solutions': [{'tour': array([ 1, 20, 32,  7, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67840.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  1, 20, 32, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  7,  1, 20, 32, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67795.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  7,  1, 20, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67747.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 11,  7,  1, 20, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 65765.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,350 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 58078.00)
2025-08-05 09:52:01,350 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:01,350 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:01,352 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9627.0, 'intermediate_solutions': [{'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 26, 19, 12, 17, 9, 8, 21, 14, 22, 30, 4, 31, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 48073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 0, 20, 1, 5, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 32, 27, 31, 26, 30, 22, 14, 21, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 44659.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 0, 20, 1, 7, 16, 2, 24, 3, 6, 15, 13, 4, 19, 12, 17, 9, 8, 21, 14, 22, 30, 26, 31, 5, 27, 32, 25, 28, 29, 33, 18, 11, 10, 34], 'cur_cost': 45096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30824.0, 'intermediate_solutions': [{'tour': [10, 9, 18, 15, 14, 11, 0, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 31, 28, 34, 27, 32, 29, 33, 26], 'cur_cost': 14508.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 9, 18, 15, 14, 11, 10, 13, 8, 12, 3, 5, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14465.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31], 'cur_cost': 39919.0, 'intermediate_solutions': [{'tour': [32, 9, 1, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 26, 18, 14, 22, 24, 21, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 60104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 32, 30, 7, 31, 10, 4, 8, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 51002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 9, 1, 30, 7, 31, 10, 8, 4, 12, 3, 5, 13, 17, 2, 19, 0, 23, 21, 18, 14, 22, 24, 26, 25, 27, 11, 34, 33, 29, 20, 28, 15, 16, 6], 'cur_cost': 54599.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 31, 24,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4], dtype=int64), 'cur_cost': 64607.0, 'intermediate_solutions': [{'tour': array([ 1, 29, 10,  3, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3,  1, 29, 10, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 65798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26,  3,  1, 29, 10, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66149.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  3,  1, 29, 26, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 62788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10, 26,  3,  1, 29, 17, 30,  0, 21,  7, 18, 16,  9, 34, 19, 27, 13,
       20, 28, 12, 14,  6,  8, 23, 31, 32,  4, 33,  2, 24, 25, 22, 15, 11,
        5]), 'cur_cost': 66204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 34116.0, 'intermediate_solutions': [{'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 4, 2, 5, 3, 1, 12, 15, 9, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12353.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 25, 30, 13, 10, 8, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 11, 17, 23, 21, 19, 18, 20, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 12, 15, 9, 10, 13, 30, 25, 34, 8, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12741.0, 'intermediate_solutions': [{'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 34, 5, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 2, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 5, 4, 11, 26, 33, 29, 32, 27, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 14], 'cur_cost': 12493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 18, 22, 16, 21, 19, 20, 17, 23, 24, 6, 7, 1, 2, 4, 11, 14, 9, 15, 8, 10, 13, 12, 5, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 10179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 11,  6,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26], dtype=int64), 'cur_cost': 62376.0, 'intermediate_solutions': [{'tour': array([13,  0, 15, 33, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 13,  0, 15, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67289.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 33, 13,  0, 15, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 33, 13,  0, 16, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 16, 33, 13,  0, 25, 28,  1,  3, 12, 27, 10, 29,  2, 31, 20, 26,
        4, 18,  5, 11,  6, 30,  9, 23,  8, 21, 32, 14, 24, 22, 19, 17, 34,
        7]), 'cur_cost': 67367.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11744.0, 'intermediate_solutions': [{'tour': [11, 19, 3, 22, 32, 30, 1, 28, 0, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 10, 29, 14, 6, 34], 'cur_cost': 60759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 19, 3, 10, 0, 28, 1, 30, 32, 7, 26, 20, 24, 25, 27, 12, 2, 18, 13, 31, 33, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 59141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 19, 3, 10, 32, 30, 1, 28, 0, 7, 26, 33, 20, 24, 25, 27, 12, 2, 18, 13, 31, 8, 5, 23, 21, 16, 15, 4, 9, 17, 22, 29, 14, 6, 34], 'cur_cost': 58840.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 35343.0, 'intermediate_solutions': [{'tour': [22, 23, 7, 0, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 34, 19, 21, 20, 12, 8, 30, 31], 'cur_cost': 35229.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 27, 32, 25, 26, 28, 20, 21, 0, 34, 12, 8, 30, 31], 'cur_cost': 38993.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 23, 7, 19, 24, 3, 1, 11, 6, 2, 18, 13, 4, 17, 9, 10, 15, 5, 16, 14, 33, 29, 25, 27, 32, 26, 28, 34, 0, 21, 20, 12, 8, 30, 31], 'cur_cost': 35699.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 12,  5, 17,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32], dtype=int64), 'cur_cost': 58078.0, 'intermediate_solutions': [{'tour': array([ 1, 20, 32,  7, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67840.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  1, 20, 32, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  7,  1, 20, 32, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67795.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  7,  1, 20, 11, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 67747.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 11,  7,  1, 20, 16, 13, 24,  3,  6, 15,  4, 19, 17,  8, 22, 10,
       26, 12, 27, 25, 23,  0, 28, 30, 14, 21, 34, 18, 33,  9, 31,  2, 29,
        5]), 'cur_cost': 65765.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:01,352 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:01,353 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,355 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9627.000, 多样性=0.932
2025-08-05 09:52:01,355 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:01,355 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:01,355 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:01,356 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07689233514077545, 'best_improvement': 0.022043884599756196}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0020394289598912947}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.09372368918565495, 'recent_improvements': [0.08049638488596778, -3.634213076268161e-05, -0.10695099348534214], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7683673469387755, 'new_diversity': 0.7683673469387755, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:01,357 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:01,357 - __main__ - INFO - composite5_35 开始进化第 5 代
2025-08-05 09:52:01,357 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:01,357 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,358 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9627.000, 多样性=0.932
2025-08-05 09:52:01,358 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:01,360 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.932
2025-08-05 09:52:01,360 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:01,363 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.768
2025-08-05 09:52:01,365 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:01,365 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:01,365 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 09:52:01,365 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 09:52:01,394 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.389, 适应度梯度: -5866.800, 聚类评分: 0.000, 覆盖率: 0.106, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:01,395 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:01,395 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:01,395 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 09:52:01,402 - visualization.landscape_visualizer - INFO - 插值约束: 124 个点被约束到最小值 9061.00
2025-08-05 09:52:01,493 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_85_20250805_095201.html
2025-08-05 09:52:01,550 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_85_20250805_095201.html
2025-08-05 09:52:01,550 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 85
2025-08-05 09:52:01,550 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:01,550 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1852秒
2025-08-05 09:52:01,551 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3888888888888889, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -5866.8, 'local_optima_density': 0.3888888888888889, 'gradient_variance': 366288103.2222222, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1062, 'fitness_entropy': 0.7093355042598104, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5866.800)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.106)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358721.3956141, 'performance_metrics': {}}}
2025-08-05 09:52:01,551 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:01,551 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:01,551 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:01,551 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:01,551 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,552 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:01,552 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:52:01,552 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:01,552 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:01,553 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:01,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,554 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,555 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11681.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,555 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 16, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11681.0, 'intermediate_solutions': [{'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 31, 25, 34, 28, 30, 27, 32, 29, 33, 26], 'cur_cost': 9824.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 23, 4, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 13319.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 10, 34, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 15629.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,555 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 11681.00)
2025-08-05 09:52:01,555 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:01,555 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:01,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,557 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,558 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37308.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,558 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 1, 23, 11, 0, 7, 19, 17, 3, 21, 14, 12, 16, 22, 2, 5, 20, 10, 24, 8, 15, 30, 31, 27, 32, 25, 26, 34, 29, 33, 4, 13, 9, 18, 28], 'cur_cost': 37308.0, 'intermediate_solutions': [{'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 33, 30, 34, 26, 27, 32, 31, 29, 9, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 31, 32, 27, 26, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30859.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 28, 25], 'cur_cost': 30829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,558 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 37308.00)
2025-08-05 09:52:01,558 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:01,558 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:01,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,559 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,560 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9595.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,560 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 15, 9, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9595.0, 'intermediate_solutions': [{'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 31, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 8], 'cur_cost': 46025.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 31, 33, 10], 'cur_cost': 42594.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31, 9], 'cur_cost': 42650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,561 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 9595.00)
2025-08-05 09:52:01,561 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:01,561 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,561 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,562 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 60066.0
2025-08-05 09:52:01,573 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:52:01,573 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9061, 9082.0]
2025-08-05 09:52:01,573 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 26, 25, 34, 30,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64)]
2025-08-05 09:52:01,575 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,575 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 16, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11681.0}, {'tour': [6, 1, 23, 11, 0, 7, 19, 17, 3, 21, 14, 12, 16, 22, 2, 5, 20, 10, 24, 8, 15, 30, 31, 27, 32, 25, 26, 34, 29, 33, 4, 13, 9, 18, 28], 'cur_cost': 37308.0}, {'tour': [0, 2, 15, 9, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9595.0}, {'tour': array([ 4,  7,  3,  8, 12, 24, 14, 34, 33, 19, 13,  2, 20, 29,  6, 31, 18,
        1, 23, 16, 27,  0, 15, 26, 25,  5, 10, 28, 17, 22,  9, 32, 30, 21,
       11], dtype=int64), 'cur_cost': 60066.0}, {'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 34116.0}, {'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12741.0}, {'tour': [9, 11, 6, 2, 25, 19, 20, 32, 29, 1, 4, 3, 17, 14, 27, 13, 31, 16, 23, 8, 28, 18, 33, 30, 24, 15, 0, 7, 10, 21, 5, 12, 34, 22, 26], 'cur_cost': 62376.0}, {'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11744.0}, {'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 35343.0}, {'tour': [9, 12, 5, 17, 8, 19, 20, 14, 21, 22, 0, 30, 11, 16, 24, 34, 1, 3, 18, 29, 7, 27, 23, 26, 31, 28, 33, 13, 25, 6, 10, 4, 15, 2, 32], 'cur_cost': 58078.0}]
2025-08-05 09:52:01,576 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,576 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 219, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 219, 'cache_hits': 0, 'similarity_calculations': 1021, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,577 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4,  7,  3,  8, 12, 24, 14, 34, 33, 19, 13,  2, 20, 29,  6, 31, 18,
        1, 23, 16, 27,  0, 15, 26, 25,  5, 10, 28, 17, 22,  9, 32, 30, 21,
       11], dtype=int64), 'cur_cost': 60066.0, 'intermediate_solutions': [{'tour': array([24, 31, 13,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 24, 31, 13, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 61515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  7, 24, 31, 13, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  7, 24, 31, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 10,  7, 24, 31, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 61928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,577 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 60066.00)
2025-08-05 09:52:01,577 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:01,577 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:01,577 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,578 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 09:52:01,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,579 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,579 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,579 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9582.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,579 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 1, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 4, 7, 6, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9582.0, 'intermediate_solutions': [{'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 11, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 21, 15], 'cur_cost': 38839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 11, 23, 25, 34, 30, 28, 15], 'cur_cost': 40090.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 0, 17, 2, 6, 12, 13, 26, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 37401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,579 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9582.00)
2025-08-05 09:52:01,579 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:01,580 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:01,580 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,580 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:01,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,581 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51449.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,581 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 21, 16, 0, 7, 19, 17, 3, 11, 12, 27, 5, 10, 24, 23, 15, 30, 31, 25, 32, 34, 26, 4, 33, 13, 22, 1, 14, 28, 9, 29, 8, 20, 2, 18], 'cur_cost': 51449.0, 'intermediate_solutions': [{'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 26, 25, 34, 28, 31, 27, 32, 29, 33, 30], 'cur_cost': 12776.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 31, 28, 27, 32, 29, 33, 26], 'cur_cost': 12777.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 6, 10, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 11, 27, 32, 29, 33, 26], 'cur_cost': 18784.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,581 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 51449.00)
2025-08-05 09:52:01,582 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:01,582 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,582 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,582 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 53706.0
2025-08-05 09:52:01,595 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:01,595 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9061, 9082.0, 9061.0]
2025-08-05 09:52:01,595 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 26, 25, 34, 30,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 34, 25, 26, 30,
        2], dtype=int64)]
2025-08-05 09:52:01,598 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,598 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 16, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11681.0}, {'tour': [6, 1, 23, 11, 0, 7, 19, 17, 3, 21, 14, 12, 16, 22, 2, 5, 20, 10, 24, 8, 15, 30, 31, 27, 32, 25, 26, 34, 29, 33, 4, 13, 9, 18, 28], 'cur_cost': 37308.0}, {'tour': [0, 2, 15, 9, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9595.0}, {'tour': array([ 4,  7,  3,  8, 12, 24, 14, 34, 33, 19, 13,  2, 20, 29,  6, 31, 18,
        1, 23, 16, 27,  0, 15, 26, 25,  5, 10, 28, 17, 22,  9, 32, 30, 21,
       11], dtype=int64), 'cur_cost': 60066.0}, {'tour': [0, 2, 1, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 4, 7, 6, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9582.0}, {'tour': [6, 21, 16, 0, 7, 19, 17, 3, 11, 12, 27, 5, 10, 24, 23, 15, 30, 31, 25, 32, 34, 26, 4, 33, 13, 22, 1, 14, 28, 9, 29, 8, 20, 2, 18], 'cur_cost': 51449.0}, {'tour': array([25, 30, 15, 14, 26, 29, 18,  2, 16, 24, 10, 11, 20,  9, 23, 27,  5,
       12,  3, 32,  8,  4, 17,  0,  6,  1, 28, 33,  7, 13, 19, 21, 34, 31,
       22], dtype=int64), 'cur_cost': 53706.0}, {'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11744.0}, {'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 35343.0}, {'tour': [9, 12, 5, 17, 8, 19, 20, 14, 21, 22, 0, 30, 11, 16, 24, 34, 1, 3, 18, 29, 7, 27, 23, 26, 31, 28, 33, 13, 25, 6, 10, 4, 15, 2, 32], 'cur_cost': 58078.0}]
2025-08-05 09:52:01,599 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,599 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 220, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 220, 'cache_hits': 0, 'similarity_calculations': 1032, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,600 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([25, 30, 15, 14, 26, 29, 18,  2, 16, 24, 10, 11, 20,  9, 23, 27,  5,
       12,  3, 32,  8,  4, 17,  0,  6,  1, 28, 33,  7, 13, 19, 21, 34, 31,
       22], dtype=int64), 'cur_cost': 53706.0, 'intermediate_solutions': [{'tour': array([ 6, 11,  9,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 62754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  6, 11,  9, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 62387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25,  2,  6, 11,  9, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 59060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  2,  6, 11, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 65086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 25,  2,  6, 11, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 65003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,600 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 53706.00)
2025-08-05 09:52:01,600 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:01,600 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:01,601 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,602 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 09:52:01,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54267.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,604 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 7, 10, 17, 13, 14, 16, 5, 20, 23, 24, 19, 15, 30, 27, 32, 25, 26, 9, 34, 18, 11, 6, 3, 29, 12, 2, 0, 28, 4, 21, 33, 22, 31, 8], 'cur_cost': 54267.0, 'intermediate_solutions': [{'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 19, 4, 2, 23, 17, 6, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16453.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 21, 14, 9, 19, 17, 23, 2, 4, 6, 7, 1, 5, 3, 13, 12, 11, 10, 8, 15, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16524.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 30, 25, 34, 28, 31, 27, 32, 29, 24, 33, 26], 'cur_cost': 17096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,604 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 54267.00)
2025-08-05 09:52:01,604 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:01,605 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:01,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:01,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34268.0, 路径长度: 35, 收集中间解: 3
2025-08-05 09:52:01,609 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [26, 25, 32, 2, 5, 7, 0, 18, 1, 17, 16, 3, 4, 21, 6, 10, 8, 23, 14, 12, 19, 20, 15, 30, 31, 28, 34, 29, 27, 24, 11, 9, 13, 22, 33], 'cur_cost': 34268.0, 'intermediate_solutions': [{'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 19, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 23, 21, 20, 9, 15, 16, 26], 'cur_cost': 35388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 0, 31, 34, 33, 25, 27, 32, 28, 29, 30, 23, 8, 3, 5, 22, 2, 11, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 34927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 17, 4, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 18, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 37323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:01,609 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 34268.00)
2025-08-05 09:52:01,609 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:01,609 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:01,609 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:01,609 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 60278.0
2025-08-05 09:52:01,621 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:01,621 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061.0, 9061.0, 9061, 9061, 9082.0, 9061.0]
2025-08-05 09:52:01,621 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 34,
       25, 26, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 26, 25, 34, 30,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 27, 31, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10,  8, 15,  9, 14, 12, 11,  3,
        5], dtype=int64), array([ 0,  5,  3,  4,  1,  7,  6, 24, 22, 16, 21, 19, 20, 18, 17, 23, 13,
       10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28, 34, 25, 26, 30,
        2], dtype=int64)]
2025-08-05 09:52:01,624 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:01,624 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 16, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11681.0}, {'tour': [6, 1, 23, 11, 0, 7, 19, 17, 3, 21, 14, 12, 16, 22, 2, 5, 20, 10, 24, 8, 15, 30, 31, 27, 32, 25, 26, 34, 29, 33, 4, 13, 9, 18, 28], 'cur_cost': 37308.0}, {'tour': [0, 2, 15, 9, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9595.0}, {'tour': array([ 4,  7,  3,  8, 12, 24, 14, 34, 33, 19, 13,  2, 20, 29,  6, 31, 18,
        1, 23, 16, 27,  0, 15, 26, 25,  5, 10, 28, 17, 22,  9, 32, 30, 21,
       11], dtype=int64), 'cur_cost': 60066.0}, {'tour': [0, 2, 1, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 4, 7, 6, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9582.0}, {'tour': [6, 21, 16, 0, 7, 19, 17, 3, 11, 12, 27, 5, 10, 24, 23, 15, 30, 31, 25, 32, 34, 26, 4, 33, 13, 22, 1, 14, 28, 9, 29, 8, 20, 2, 18], 'cur_cost': 51449.0}, {'tour': array([25, 30, 15, 14, 26, 29, 18,  2, 16, 24, 10, 11, 20,  9, 23, 27,  5,
       12,  3, 32,  8,  4, 17,  0,  6,  1, 28, 33,  7, 13, 19, 21, 34, 31,
       22], dtype=int64), 'cur_cost': 53706.0}, {'tour': [1, 7, 10, 17, 13, 14, 16, 5, 20, 23, 24, 19, 15, 30, 27, 32, 25, 26, 9, 34, 18, 11, 6, 3, 29, 12, 2, 0, 28, 4, 21, 33, 22, 31, 8], 'cur_cost': 54267.0}, {'tour': [26, 25, 32, 2, 5, 7, 0, 18, 1, 17, 16, 3, 4, 21, 6, 10, 8, 23, 14, 12, 19, 20, 15, 30, 31, 28, 34, 29, 27, 24, 11, 9, 13, 22, 33], 'cur_cost': 34268.0}, {'tour': array([16, 23,  7, 20,  1,  6, 26,  2, 10, 24,  8, 32, 25, 11, 12,  0, 14,
       17,  4,  9, 27,  3, 31, 15, 18, 34, 22, 21, 28, 13, 30, 29, 33, 19,
        5], dtype=int64), 'cur_cost': 60278.0}]
2025-08-05 09:52:01,625 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:01,625 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 221, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 221, 'cache_hits': 0, 'similarity_calculations': 1044, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:01,626 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([16, 23,  7, 20,  1,  6, 26,  2, 10, 24,  8, 32, 25, 11, 12,  0, 14,
       17,  4,  9, 27,  3, 31, 15, 18, 34, 22, 21, 28, 13, 30, 29, 33, 19,
        5], dtype=int64), 'cur_cost': 60278.0, 'intermediate_solutions': [{'tour': array([ 5, 12,  9, 17,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17,  5, 12,  9,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 55450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 17,  5, 12,  9, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 17,  5, 12,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  8, 17,  5, 12, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:01,626 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 60278.00)
2025-08-05 09:52:01,626 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:01,627 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:01,629 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 16, 11, 14, 9, 15, 8, 10, 13, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 18, 19, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11681.0, 'intermediate_solutions': [{'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 31, 25, 34, 28, 30, 27, 32, 29, 33, 26], 'cur_cost': 9824.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 10, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 23, 4, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 13319.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 10, 34, 15, 9, 14, 11, 12, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 15629.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 23, 11, 0, 7, 19, 17, 3, 21, 14, 12, 16, 22, 2, 5, 20, 10, 24, 8, 15, 30, 31, 27, 32, 25, 26, 34, 29, 33, 4, 13, 9, 18, 28], 'cur_cost': 37308.0, 'intermediate_solutions': [{'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 33, 30, 34, 26, 27, 32, 31, 29, 9, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 31, 32, 27, 26, 29, 33, 5, 11, 13, 15, 10, 3, 2, 25, 28], 'cur_cost': 30859.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 12, 1, 16, 7, 21, 22, 24, 18, 4, 17, 6, 0, 23, 19, 8, 20, 9, 30, 34, 26, 27, 32, 31, 29, 33, 5, 11, 13, 15, 10, 3, 2, 28, 25], 'cur_cost': 30829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 15, 9, 14, 11, 10, 13, 8, 12, 3, 5, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9595.0, 'intermediate_solutions': [{'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 31, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 8], 'cur_cost': 46025.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 9, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 31, 33, 10], 'cur_cost': 42594.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 5, 19, 7, 18, 13, 6, 17, 0, 2, 11, 21, 20, 4, 16, 1, 12, 23, 8, 15, 26, 28, 27, 34, 30, 29, 25, 32, 3, 24, 14, 10, 33, 31, 9], 'cur_cost': 42650.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  7,  3,  8, 12, 24, 14, 34, 33, 19, 13,  2, 20, 29,  6, 31, 18,
        1, 23, 16, 27,  0, 15, 26, 25,  5, 10, 28, 17, 22,  9, 32, 30, 21,
       11], dtype=int64), 'cur_cost': 60066.0, 'intermediate_solutions': [{'tour': array([24, 31, 13,  7, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 24, 31, 13, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 61515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  7, 24, 31, 13, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([13,  7, 24, 31, 10, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 64592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([13, 10,  7, 24, 31, 16,  1, 23, 11, 28, 29,  6, 27, 19, 15, 12, 18,
        8,  0, 26, 34,  3, 21,  2,  9, 25, 20, 33, 17, 30, 32,  5, 14, 22,
        4]), 'cur_cost': 61928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 4, 7, 6, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9582.0, 'intermediate_solutions': [{'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 11, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 21, 15], 'cur_cost': 38839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 0, 17, 2, 6, 12, 13, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 26, 29, 31, 33, 27, 32, 11, 23, 25, 34, 30, 28, 15], 'cur_cost': 40090.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 0, 17, 2, 6, 12, 13, 26, 7, 1, 24, 10, 18, 3, 4, 9, 20, 21, 22, 5, 19, 14, 8, 29, 31, 33, 27, 32, 28, 30, 34, 25, 23, 11, 15], 'cur_cost': 37401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 21, 16, 0, 7, 19, 17, 3, 11, 12, 27, 5, 10, 24, 23, 15, 30, 31, 25, 32, 34, 26, 4, 33, 13, 22, 1, 14, 28, 9, 29, 8, 20, 2, 18], 'cur_cost': 51449.0, 'intermediate_solutions': [{'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 26, 25, 34, 28, 31, 27, 32, 29, 33, 30], 'cur_cost': 12776.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 6, 10, 11, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 31, 28, 27, 32, 29, 33, 26], 'cur_cost': 12777.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 6, 10, 12, 15, 9, 8, 13, 3, 5, 1, 7, 2, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 11, 27, 32, 29, 33, 26], 'cur_cost': 18784.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 30, 15, 14, 26, 29, 18,  2, 16, 24, 10, 11, 20,  9, 23, 27,  5,
       12,  3, 32,  8,  4, 17,  0,  6,  1, 28, 33,  7, 13, 19, 21, 34, 31,
       22], dtype=int64), 'cur_cost': 53706.0, 'intermediate_solutions': [{'tour': array([ 6, 11,  9,  2, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 62754.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  6, 11,  9, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 62387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25,  2,  6, 11,  9, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 59060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  2,  6, 11, 25, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 65086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 25,  2,  6, 11, 19, 20, 32, 29,  1,  4,  3, 17, 14, 27, 13, 31,
       16, 23,  8, 28, 18, 33, 30, 24, 15,  0,  7, 10, 21,  5, 12, 34, 22,
       26]), 'cur_cost': 65003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 10, 17, 13, 14, 16, 5, 20, 23, 24, 19, 15, 30, 27, 32, 25, 26, 9, 34, 18, 11, 6, 3, 29, 12, 2, 0, 28, 4, 21, 33, 22, 31, 8], 'cur_cost': 54267.0, 'intermediate_solutions': [{'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 19, 4, 2, 23, 17, 6, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16453.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 21, 14, 9, 19, 17, 23, 2, 4, 6, 7, 1, 5, 3, 13, 12, 11, 10, 8, 15, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16524.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 21, 14, 9, 15, 8, 10, 11, 12, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 19, 20, 16, 22, 30, 25, 34, 28, 31, 27, 32, 29, 24, 33, 26], 'cur_cost': 17096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [26, 25, 32, 2, 5, 7, 0, 18, 1, 17, 16, 3, 4, 21, 6, 10, 8, 23, 14, 12, 19, 20, 15, 30, 31, 28, 34, 29, 27, 24, 11, 9, 13, 22, 33], 'cur_cost': 34268.0, 'intermediate_solutions': [{'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 19, 30, 29, 28, 32, 27, 25, 33, 34, 31, 0, 23, 21, 20, 9, 15, 16, 26], 'cur_cost': 35388.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 17, 4, 18, 24, 13, 14, 12, 6, 1, 7, 0, 31, 34, 33, 25, 27, 32, 28, 29, 30, 23, 8, 3, 5, 22, 2, 11, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 34927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 17, 4, 24, 13, 14, 12, 6, 1, 7, 11, 2, 22, 5, 3, 8, 23, 30, 29, 28, 32, 27, 25, 33, 34, 31, 18, 0, 19, 21, 20, 9, 15, 16, 26], 'cur_cost': 37323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 23,  7, 20,  1,  6, 26,  2, 10, 24,  8, 32, 25, 11, 12,  0, 14,
       17,  4,  9, 27,  3, 31, 15, 18, 34, 22, 21, 28, 13, 30, 29, 33, 19,
        5], dtype=int64), 'cur_cost': 60278.0, 'intermediate_solutions': [{'tour': array([ 5, 12,  9, 17,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58081.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17,  5, 12,  9,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 55450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 17,  5, 12,  9, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 17,  5, 12,  8, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  8, 17,  5, 12, 19, 20, 14, 21, 22,  0, 30, 11, 16, 24, 34,  1,
        3, 18, 29,  7, 27, 23, 26, 31, 28, 33, 13, 25,  6, 10,  4, 15,  2,
       32]), 'cur_cost': 58026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:01,629 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:01,629 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:01,632 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9582.000, 多样性=0.928
2025-08-05 09:52:01,632 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:01,632 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:01,632 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:01,633 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.002722144395569812, 'best_improvement': 0.004674353381115612}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.004087193460490354}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03846433863576907, 'recent_improvements': [-3.634213076268161e-05, -0.10695099348534214, 0.07689233514077545], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7825396825396825, 'new_diversity': 0.7825396825396825, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:01,634 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:01,641 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite5_35_solution.json
2025-08-05 09:52:01,641 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite5_35_20250805_095201.solution
2025-08-05 09:52:01,641 - __main__ - INFO - 实例执行完成 - 运行时间: 1.28s, 最佳成本: 9061
2025-08-05 09:52:01,641 - __main__ - INFO - 实例 composite5_35 处理完成
