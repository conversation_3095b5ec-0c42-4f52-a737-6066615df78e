"""
Individual state modeling for the intelligent strategy selection system.

This module implements the core data structures for representing individual
states in the fitness landscape-driven strategy selection system.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
import numpy as np
from enum import Enum


class StagnationLevel(Enum):
    """Enumeration for stagnation levels."""
    NONE = "none"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class IndividualState:
    """
    Individual state representation with multi-dimensional features.
    
    This class models the complete state of an individual in the population,
    including fitness information, landscape features, historical data,
    and relative positioning information.
    """
    
    # Basic state information
    individual_id: int
    fitness_value: float
    fitness_rank: int
    fitness_percentile: float
    current_solution: List[int] = field(default_factory=list)
    
    # Landscape features
    local_ruggedness: float = 0.0
    local_gradient: np.ndarray = field(default_factory=lambda: np.array([]))
    improvement_potential: float = 0.0
    neighborhood_diversity: float = 0.0
    
    # Historical information
    stagnation_duration: int = 0
    stagnation_level: StagnationLevel = StagnationLevel.NONE
    exploration_history: List[Dict] = field(default_factory=list)
    performance_trend: float = 0.0
    recent_improvements: List[float] = field(default_factory=list)
    
    # Relative position information
    distance_to_best: float = float('inf')
    distance_to_worst: float = 0.0
    local_density: float = 0.0
    diversity_contribution: float = 0.0
    
    # Strategy execution history
    last_strategy_type: Optional[str] = None
    last_strategy_success: bool = False
    strategy_success_rate: float = 0.5
    preferred_strategies: Dict[str, float] = field(default_factory=dict)
    
    # Computational resources
    allocated_time_budget: float = 0.0
    resource_efficiency: float = 1.0
    
    def update_stagnation_level(self):
        """Update stagnation level based on duration."""
        if self.stagnation_duration == 0:
            self.stagnation_level = StagnationLevel.NONE
        elif self.stagnation_duration <= 2:
            self.stagnation_level = StagnationLevel.LOW
        elif self.stagnation_duration <= 5:
            self.stagnation_level = StagnationLevel.MODERATE
        elif self.stagnation_duration <= 10:
            self.stagnation_level = StagnationLevel.HIGH
        else:
            self.stagnation_level = StagnationLevel.CRITICAL
    
    def add_exploration_record(self, strategy_type: str, parameters: Dict, 
                             improvement: float, success: bool):
        """Add a new exploration record to history."""
        record = {
            'strategy_type': strategy_type,
            'parameters': parameters,
            'improvement': improvement,
            'success': success,
            'timestamp': len(self.exploration_history)
        }
        self.exploration_history.append(record)
        
        # Keep only recent history (last 20 records)
        if len(self.exploration_history) > 20:
            self.exploration_history = self.exploration_history[-20:]
        
        # Update strategy preferences
        if strategy_type not in self.preferred_strategies:
            self.preferred_strategies[strategy_type] = 0.5
        
        # Update preference based on success
        current_pref = self.preferred_strategies[strategy_type]
        if success:
            self.preferred_strategies[strategy_type] = min(1.0, current_pref + 0.1)
        else:
            self.preferred_strategies[strategy_type] = max(0.0, current_pref - 0.05)
    
    def calculate_performance_trend(self, window_size: int = 5) -> float:
        """Calculate recent performance trend."""
        if len(self.recent_improvements) < 2:
            return 0.0
        
        recent_data = self.recent_improvements[-window_size:]
        if len(recent_data) < 2:
            return 0.0
        
        # Simple linear trend calculation
        x = np.arange(len(recent_data))
        y = np.array(recent_data)
        
        if np.std(x) == 0:
            return 0.0
        
        correlation = np.corrcoef(x, y)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def get_state_summary(self) -> Dict:
        """Get a summary of the current state."""
        return {
            'individual_id': self.individual_id,
            'fitness_value': self.fitness_value,
            'fitness_rank': self.fitness_rank,
            'fitness_percentile': self.fitness_percentile,
            'stagnation_level': self.stagnation_level.value,
            'stagnation_duration': self.stagnation_duration,
            'local_ruggedness': self.local_ruggedness,
            'improvement_potential': self.improvement_potential,
            'performance_trend': self.performance_trend,
            'distance_to_best': self.distance_to_best,
            'diversity_contribution': self.diversity_contribution,
            'strategy_success_rate': self.strategy_success_rate,
            'last_strategy_type': self.last_strategy_type,
            'last_strategy_success': self.last_strategy_success
        }


@dataclass
class IndividualContext:
    """
    Context information for strategy execution.
    
    This class provides the necessary context for executing strategies
    on a specific individual, including current state and environmental factors.
    """
    
    individual_state: IndividualState
    current_solution: List[int]
    current_fitness: float
    available_time_budget: float
    
    # Environmental context
    population_size: int
    current_iteration: int
    total_iterations: int
    
    # Landscape context
    local_ruggedness: float
    gradient_strength: float
    neighborhood_quality: Dict[str, float] = field(default_factory=dict)
    
    # Coordination context
    nearby_individuals: List[int] = field(default_factory=list)
    collaboration_opportunities: List[Dict] = field(default_factory=list)
    
    @property
    def progress_ratio(self) -> float:
        """Calculate progress ratio through the optimization process."""
        if self.total_iterations == 0:
            return 0.0
        return self.current_iteration / self.total_iterations
    
    @property
    def is_early_stage(self) -> bool:
        """Check if we're in the early stage of optimization."""
        return self.progress_ratio < 0.3
    
    @property
    def is_late_stage(self) -> bool:
        """Check if we're in the late stage of optimization."""
        return self.progress_ratio > 0.7
    
    def get_context_summary(self) -> Dict:
        """Get a summary of the current context."""
        return {
            'individual_id': self.individual_state.individual_id,
            'current_fitness': self.current_fitness,
            'available_time_budget': self.available_time_budget,
            'progress_ratio': self.progress_ratio,
            'is_early_stage': self.is_early_stage,
            'is_late_stage': self.is_late_stage,
            'local_ruggedness': self.local_ruggedness,
            'gradient_strength': self.gradient_strength,
            'nearby_individuals_count': len(self.nearby_individuals),
            'collaboration_opportunities_count': len(self.collaboration_opportunities)
        }
