2025-08-04 11:52:41,504 - __main__ - INFO - pr76 开始进化第 1 代
2025-08-04 11:52:41,504 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 11:52:41,510 - StatsExpert - INFO - 开始统计分析
2025-08-04 11:52:41,581 - StatsExpert - INFO - 统计分析完成: 种群大小=50, 最优成本=130921.000, 多样性=0.984
2025-08-04 11:52:41,592 - PathExpert - INFO - 开始路径结构分析
2025-08-04 11:52:41,609 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.984
2025-08-04 11:52:41,615 - EliteExpert - INFO - 开始精英解分析
2025-08-04 11:52:41,617 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/20)
2025-08-04 11:52:41,617 - LandscapeExpert - INFO - 使用直接传入的种群数据: 50个个体
2025-08-04 11:52:41,617 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 11:52:41,617 - LandscapeExpert - INFO - 数据提取成功: 50个路径, 50个适应度值
2025-08-04 11:52:42,023 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.120, 适应度梯度: -33052.656, 聚类评分: 0.000, 覆盖率: 0.943, 收敛趋势: 0.000, 多样性: 1.000
2025-08-04 11:52:42,023 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 11:52:42,023 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 11:52:42,023 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-04 11:52:42,033 - visualization.landscape_visualizer - INFO - 插值约束: 52 个点被约束到最小值 130921.00
2025-08-04 11:52:42,037 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 11:52:42,109 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_561_20250804_115242.html
2025-08-04 11:52:42,141 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_561_20250804_115242.html
2025-08-04 11:52:42,141 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 561
2025-08-04 11:52:42,141 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 11:52:42,142 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.5245秒
2025-08-04 11:52:42,142 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 500, 'max_size': 500, 'hits': 0, 'misses': 1122, 'hit_rate': 0.0, 'evictions': 622, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 4574, 'misses': 1478, 'hit_rate': 0.755783212161269, 'evictions': 1378, 'ttl': 7200}}
2025-08-04 11:52:42,142 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.12, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -33052.656, 'local_optima_density': 0.12, 'gradient_variance': 33889155033.905663, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.9433, 'fitness_entropy': 0.9763794231541455, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -33052.656)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 20, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754279562.0237556, 'performance_metrics': {}}}
2025-08-04 11:52:42,142 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 11:52:42,143 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 11:52:42,143 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 11:52:42,143 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 11:52:42,174 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 11:52:42,175 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 11:52:42,175 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 11:52:42,175 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 42
- 利用个体数量: 8
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 11:52:42,175 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 11:52:42,175 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 11:52:42,175 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 42
- 利用个体数量: 8
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 11:52:42,176 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 11:52:42,176 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 6, 7, 8, 9, 10, 11, 13} (总数: 10, 保护比例: 0.20)
2025-08-04 11:52:42,176 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 11:52:42,176 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 11:52:42,176 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,195 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-04 11:52:42,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,196 - ExplorationExpert - INFO - 探索路径生成完成，成本: 611055.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,196 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 1, 9, 49, 13, 30, 69, 63, 7, 8, 38, 10, 67, 0, 25, 23, 58, 20, 70, 53, 68, 37, 21, 75, 62, 18, 27, 42, 33, 47, 16, 55, 72, 59, 66, 19, 45, 64, 40, 11, 60, 73, 4, 44, 24, 43, 54, 31, 2, 32, 51, 39, 52, 41, 46, 6, 35, 5, 56, 28, 57, 15, 3, 36, 71, 74, 50, 14, 26, 34, 48, 22, 17, 61, 65, 12], 'cur_cost': 611055.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,196 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 611055.00)
2025-08-04 11:52:42,196 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 11:52:42,196 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 11:52:42,196 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,214 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,215 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,215 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155326.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,215 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 13, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 15, 14, 12, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 155326.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,215 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 155326.00)
2025-08-04 11:52:42,215 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 11:52:42,215 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 11:52:42,215 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,266 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,267 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,267 - ExplorationExpert - INFO - 探索路径生成完成，成本: 416571.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,267 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 38, 37, 32, 34, 8, 0, 47, 28, 3, 33, 50, 48, 27, 24, 51, 62, 56, 55, 68, 64, 67, 41, 35, 13, 4, 29, 30, 42, 25, 2, 19, 36, 58, 10, 17, 57, 43, 26, 23, 18, 7, 20, 16, 9, 53, 39, 40, 15, 14, 61, 31, 21, 6, 44, 5, 45, 59, 52, 46, 49, 54, 66, 65, 71, 72, 63, 69, 1, 74, 12, 73, 22, 75, 60, 70], 'cur_cost': 416571.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,267 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 416571.00)
2025-08-04 11:52:42,267 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 11:52:42,267 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 11:52:42,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,286 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 153699.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,287 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 13, 11, 26, 25, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 32, 31, 37, 38, 15, 14, 12, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 153699.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,287 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 153699.00)
2025-08-04 11:52:42,287 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 11:52:42,287 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 11:52:42,288 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,332 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 368662.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,333 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 20, 43, 46, 31, 39, 51, 38, 19, 30, 24, 0, 23, 44, 55, 47, 29, 37, 40, 65, 41, 48, 42, 32, 56, 64, 60, 63, 62, 67, 45, 21, 27, 58, 52, 66, 68, 70, 59, 34, 18, 26, 33, 16, 13, 6, 8, 11, 35, 28, 4, 1, 5, 15, 14, 17, 7, 10, 2, 22, 75, 9, 57, 49, 25, 3, 74, 36, 54, 61, 71, 72, 50, 53, 69, 73], 'cur_cost': 368662.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,333 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 368662.00)
2025-08-04 11:52:42,333 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 11:52:42,333 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 11:52:42,333 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,378 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 399175.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,379 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [52, 31, 59, 38, 28, 2, 25, 3, 27, 35, 40, 33, 14, 19, 37, 5, 42, 64, 48, 65, 39, 58, 29, 32, 50, 53, 18, 30, 10, 20, 47, 23, 9, 15, 24, 4, 1, 43, 34, 6, 11, 0, 74, 21, 16, 51, 67, 66, 45, 44, 55, 70, 63, 72, 71, 68, 22, 75, 13, 36, 41, 60, 56, 62, 17, 26, 54, 49, 46, 57, 61, 8, 7, 12, 73, 69], 'cur_cost': 399175.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,379 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 399175.00)
2025-08-04 11:52:42,379 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 11:52:42,379 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 11:52:42,379 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,427 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 403312.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,428 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [37, 40, 28, 9, 73, 5, 10, 7, 25, 34, 8, 0, 74, 30, 52, 44, 29, 23, 75, 35, 15, 12, 14, 26, 41, 64, 43, 18, 20, 36, 38, 11, 6, 45, 19, 51, 60, 31, 24, 17, 2, 1, 68, 32, 53, 56, 59, 58, 50, 63, 57, 70, 61, 65, 49, 62, 48, 54, 55, 27, 16, 42, 66, 71, 39, 33, 47, 21, 3, 13, 4, 22, 46, 67, 69, 72], 'cur_cost': 403312.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,428 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 403312.00)
2025-08-04 11:52:42,428 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 11:52:42,428 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 11:52:42,428 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,447 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-04 11:52:42,447 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,448 - ExplorationExpert - INFO - 探索路径生成完成，成本: 560465.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,448 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [30, 8, 4, 50, 42, 64, 55, 10, 60, 7, 68, 44, 1, 32, 73, 14, 45, 9, 21, 75, 72, 12, 52, 74, 27, 59, 3, 53, 67, 41, 22, 15, 13, 48, 16, 40, 20, 54, 46, 2, 19, 57, 43, 58, 47, 25, 69, 29, 18, 28, 61, 66, 70, 63, 17, 56, 23, 62, 37, 49, 51, 34, 39, 6, 35, 36, 65, 71, 33, 5, 24, 0, 26, 31, 38, 11], 'cur_cost': 560465.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,448 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 560465.00)
2025-08-04 11:52:42,448 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 11:52:42,449 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 11:52:42,449 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,471 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,472 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,472 - ExplorationExpert - INFO - 探索路径生成完成，成本: 160791.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,472 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 27, 19, 30, 18, 29, 28, 31, 32, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 25, 26, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 38, 37, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 160791.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,473 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 160791.00)
2025-08-04 11:52:42,473 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 11:52:42,473 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 11:52:42,473 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,503 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,504 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,504 - ExplorationExpert - INFO - 探索路径生成完成，成本: 167845.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,504 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 19, 4, 5, 6, 2, 3, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 167845.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,505 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 167845.00)
2025-08-04 11:52:42,505 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-04 11:52:42,505 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-04 11:52:42,505 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,537 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,538 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169760.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,539 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [0, 3, 17, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 4, 19, 18, 30, 29, 28, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 169760.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,539 - experts.management.collaboration_manager - INFO - 个体 10 保留原路径 (成本: 169760.00)
2025-08-04 11:52:42,539 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-04 11:52:42,539 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-04 11:52:42,539 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,572 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 177187.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,573 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 10, 27, 5, 6, 7, 8, 9, 4, 19, 18, 30, 29, 28, 31, 32, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 26, 25, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177187.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,573 - experts.management.collaboration_manager - INFO - 个体 11 保留原路径 (成本: 177187.00)
2025-08-04 11:52:42,573 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-04 11:52:42,573 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-04 11:52:42,573 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,635 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,636 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 393148.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,637 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [41, 54, 49, 62, 38, 30, 34, 11, 36, 2, 8, 17, 35, 57, 26, 46, 31, 51, 43, 48, 58, 72, 56, 70, 52, 55, 50, 25, 44, 66, 39, 65, 33, 16, 15, 12, 7, 13, 9, 32, 29, 53, 61, 27, 59, 3, 1, 18, 19, 24, 4, 22, 74, 73, 75, 0, 5, 20, 28, 42, 23, 6, 47, 21, 68, 64, 40, 37, 60, 63, 67, 69, 71, 45, 10, 14], 'cur_cost': 393148.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,637 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 393148.00)
2025-08-04 11:52:42,637 - experts.management.collaboration_manager - INFO - 为个体 13 生成探索路径
2025-08-04 11:52:42,637 - ExplorationExpert - INFO - 开始为个体 13 生成探索路径（算法实现）
2025-08-04 11:52:42,637 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,699 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,700 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,700 - ExplorationExpert - INFO - 探索路径生成完成，成本: 416733.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,700 - experts.management.collaboration_manager - INFO - 个体 13 探索路径生成报告: {'new_tour': [43, 23, 19, 26, 53, 51, 47, 29, 16, 3, 7, 24, 9, 2, 0, 6, 32, 30, 13, 12, 42, 36, 10, 17, 73, 39, 54, 41, 65, 27, 50, 62, 58, 64, 55, 63, 52, 68, 28, 37, 11, 18, 15, 60, 38, 5, 25, 20, 22, 8, 31, 34, 33, 61, 66, 70, 56, 35, 4, 14, 57, 46, 48, 67, 49, 69, 72, 44, 40, 45, 59, 21, 1, 74, 75, 71], 'cur_cost': 416733.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,701 - experts.management.collaboration_manager - INFO - 个体 13 保留原路径 (成本: 416733.00)
2025-08-04 11:52:42,701 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-04 11:52:42,701 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-04 11:52:42,701 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,722 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,722 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,722 - ExplorationExpert - INFO - 探索路径生成完成，成本: 144986.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,722 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 27, 16, 13, 12, 11, 10, 15, 14, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 26, 25, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 73, 74, 75], 'cur_cost': 144986.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,723 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 144986.00)
2025-08-04 11:52:42,723 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-04 11:52:42,723 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-04 11:52:42,723 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,747 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,748 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,749 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162747.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,749 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 17, 29, 3, 2, 5, 6, 7, 8, 9, 4, 19, 18, 30, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 162747.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,749 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 162747.00)
2025-08-04 11:52:42,749 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-04 11:52:42,749 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 11:52:42,749 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 11:52:42,750 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 612450.0
2025-08-04 11:52:42,758 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 11:52:42,759 - ExploitationExpert - INFO - res_population_costs: [112399.0, 110905, 109477]
2025-08-04 11:52:42,759 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 28, 32, 33, 39, 38, 37, 17, 36, 35,
       34, 31, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7, 73, 13, 12, 14, 15, 16, 10, 11,
        8,  9,  4, 19, 18, 30, 29, 31, 34, 35, 36, 17, 37, 38, 39, 33, 32,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 56, 57, 58, 59,
       40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 42, 41, 53, 52, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64)]
2025-08-04 11:52:42,761 - ExploitationExpert - INFO - populations_num: 50
2025-08-04 11:52:42,761 - ExploitationExpert - INFO - populations: [{'tour': [29, 1, 9, 49, 13, 30, 69, 63, 7, 8, 38, 10, 67, 0, 25, 23, 58, 20, 70, 53, 68, 37, 21, 75, 62, 18, 27, 42, 33, 47, 16, 55, 72, 59, 66, 19, 45, 64, 40, 11, 60, 73, 4, 44, 24, 43, 54, 31, 2, 32, 51, 39, 52, 41, 46, 6, 35, 5, 56, 28, 57, 15, 3, 36, 71, 74, 50, 14, 26, 34, 48, 22, 17, 61, 65, 12], 'cur_cost': 611055.0}, {'tour': [0, 7, 13, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 15, 14, 12, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 155326.0}, {'tour': [11, 38, 37, 32, 34, 8, 0, 47, 28, 3, 33, 50, 48, 27, 24, 51, 62, 56, 55, 68, 64, 67, 41, 35, 13, 4, 29, 30, 42, 25, 2, 19, 36, 58, 10, 17, 57, 43, 26, 23, 18, 7, 20, 16, 9, 53, 39, 40, 15, 14, 61, 31, 21, 6, 44, 5, 45, 59, 52, 46, 49, 54, 66, 65, 71, 72, 63, 69, 1, 74, 12, 73, 22, 75, 60, 70], 'cur_cost': 416571.0}, {'tour': [0, 13, 11, 26, 25, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 32, 31, 37, 38, 15, 14, 12, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 153699.0}, {'tour': [12, 20, 43, 46, 31, 39, 51, 38, 19, 30, 24, 0, 23, 44, 55, 47, 29, 37, 40, 65, 41, 48, 42, 32, 56, 64, 60, 63, 62, 67, 45, 21, 27, 58, 52, 66, 68, 70, 59, 34, 18, 26, 33, 16, 13, 6, 8, 11, 35, 28, 4, 1, 5, 15, 14, 17, 7, 10, 2, 22, 75, 9, 57, 49, 25, 3, 74, 36, 54, 61, 71, 72, 50, 53, 69, 73], 'cur_cost': 368662.0}, {'tour': [52, 31, 59, 38, 28, 2, 25, 3, 27, 35, 40, 33, 14, 19, 37, 5, 42, 64, 48, 65, 39, 58, 29, 32, 50, 53, 18, 30, 10, 20, 47, 23, 9, 15, 24, 4, 1, 43, 34, 6, 11, 0, 74, 21, 16, 51, 67, 66, 45, 44, 55, 70, 63, 72, 71, 68, 22, 75, 13, 36, 41, 60, 56, 62, 17, 26, 54, 49, 46, 57, 61, 8, 7, 12, 73, 69], 'cur_cost': 399175.0}, {'tour': [37, 40, 28, 9, 73, 5, 10, 7, 25, 34, 8, 0, 74, 30, 52, 44, 29, 23, 75, 35, 15, 12, 14, 26, 41, 64, 43, 18, 20, 36, 38, 11, 6, 45, 19, 51, 60, 31, 24, 17, 2, 1, 68, 32, 53, 56, 59, 58, 50, 63, 57, 70, 61, 65, 49, 62, 48, 54, 55, 27, 16, 42, 66, 71, 39, 33, 47, 21, 3, 13, 4, 22, 46, 67, 69, 72], 'cur_cost': 403312.0}, {'tour': [30, 8, 4, 50, 42, 64, 55, 10, 60, 7, 68, 44, 1, 32, 73, 14, 45, 9, 21, 75, 72, 12, 52, 74, 27, 59, 3, 53, 67, 41, 22, 15, 13, 48, 16, 40, 20, 54, 46, 2, 19, 57, 43, 58, 47, 25, 69, 29, 18, 28, 61, 66, 70, 63, 17, 56, 23, 62, 37, 49, 51, 34, 39, 6, 35, 36, 65, 71, 33, 5, 24, 0, 26, 31, 38, 11], 'cur_cost': 560465.0}, {'tour': [0, 27, 19, 30, 18, 29, 28, 31, 32, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 25, 26, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 38, 37, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 160791.0}, {'tour': [0, 7, 19, 4, 5, 6, 2, 3, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 167845.0}, {'tour': [0, 3, 17, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 4, 19, 18, 30, 29, 28, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 169760.0}, {'tour': [0, 10, 27, 5, 6, 7, 8, 9, 4, 19, 18, 30, 29, 28, 31, 32, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 26, 25, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177187.0}, {'tour': [41, 54, 49, 62, 38, 30, 34, 11, 36, 2, 8, 17, 35, 57, 26, 46, 31, 51, 43, 48, 58, 72, 56, 70, 52, 55, 50, 25, 44, 66, 39, 65, 33, 16, 15, 12, 7, 13, 9, 32, 29, 53, 61, 27, 59, 3, 1, 18, 19, 24, 4, 22, 74, 73, 75, 0, 5, 20, 28, 42, 23, 6, 47, 21, 68, 64, 40, 37, 60, 63, 67, 69, 71, 45, 10, 14], 'cur_cost': 393148.0}, {'tour': [43, 23, 19, 26, 53, 51, 47, 29, 16, 3, 7, 24, 9, 2, 0, 6, 32, 30, 13, 12, 42, 36, 10, 17, 73, 39, 54, 41, 65, 27, 50, 62, 58, 64, 55, 63, 52, 68, 28, 37, 11, 18, 15, 60, 38, 5, 25, 20, 22, 8, 31, 34, 33, 61, 66, 70, 56, 35, 4, 14, 57, 46, 48, 67, 49, 69, 72, 44, 40, 45, 59, 21, 1, 74, 75, 71], 'cur_cost': 416733.0}, {'tour': [0, 27, 16, 13, 12, 11, 10, 15, 14, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 26, 25, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 73, 74, 75], 'cur_cost': 144986.0}, {'tour': [0, 17, 29, 3, 2, 5, 6, 7, 8, 9, 4, 19, 18, 30, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 162747.0}, {'tour': array([ 0, 52,  1, 59,  6, 70, 32, 46, 67,  9, 27, 56, 65, 18, 10, 69, 55,
       23, 13, 25, 61, 15, 33, 73, 74, 30, 31, 48, 53, 39, 35,  2, 14, 63,
       12, 47, 58,  4, 75, 62, 60, 36, 29, 24, 11, 42, 16, 72, 44, 41, 71,
       45, 54, 38, 20, 34, 68, 22, 66,  8,  7, 51, 49, 37, 50, 28, 64, 19,
       26, 21,  3, 57, 43,  5, 40, 17], dtype=int64), 'cur_cost': 612450.0}, {'tour': array([35, 31, 58, 50, 14, 52, 70, 24, 27, 69, 49,  7,  1,  0, 75, 10, 45,
       32, 57, 37, 74, 38, 36, 72, 18, 48, 53, 61, 54, 12, 59, 43,  9, 56,
       39, 68, 46,  5,  4, 15,  6, 66, 13,  8, 60, 42, 16, 23, 71, 11, 55,
       73, 21, 65, 20, 33, 62, 51, 41, 44, 34, 63, 17, 26, 64,  2, 67,  3,
       25, 22, 28, 30, 40, 47, 19, 29], dtype=int64), 'cur_cost': 580904.0}, {'tour': array([42, 39, 72, 62, 73,  3, 34, 60, 26, 10,  5, 27, 46, 21,  1, 28, 17,
       41,  8,  4, 48, 50,  7, 24, 13, 66, 63, 70,  9, 30, 32, 36, 57, 52,
       59, 44, 47, 61, 67, 56, 22, 74, 69, 51, 14, 55, 37, 45, 68, 12, 31,
       38, 75,  6, 25, 33,  0, 65, 11, 43, 64, 16, 53,  2, 71, 23, 49, 29,
       20, 15, 54, 40, 35, 18, 58, 19], dtype=int64), 'cur_cost': 572449.0}, {'tour': array([34, 23, 60, 45, 35, 63, 62, 42,  4, 50, 72, 59, 39, 53,  0, 32, 10,
       36, 69, 64, 41, 33, 38, 58, 17, 68, 49, 19,  9, 28, 47, 74, 57, 13,
       30, 75, 26,  3, 51, 43,  6, 27, 31, 67, 56, 65, 46, 24, 15,  5, 66,
        8, 21, 25, 55, 16, 54, 12,  1, 44, 20, 70, 29, 71, 48, 40, 37, 11,
        7, 18, 61, 22, 52, 73, 14,  2], dtype=int64), 'cur_cost': 559756.0}, {'tour': array([57, 73,  1, 26, 68, 64, 59, 27,  9, 31, 35, 32, 21, 51, 38, 36, 20,
       24, 43, 63, 44, 49, 13,  2, 41, 45, 52,  8,  5, 23, 61, 34, 19, 71,
       29, 30, 47, 17, 39, 50, 25, 18, 53, 66, 42, 28, 22,  4, 54, 48, 37,
       16,  7, 11, 56, 65, 33, 14, 74, 69, 40, 67, 75,  0, 55, 72,  6, 10,
       15, 58, 60, 70,  3, 62, 12, 46], dtype=int64), 'cur_cost': 542102.0}, {'tour': array([47, 15, 55, 49, 33,  2, 75, 32, 51, 27,  9, 43, 35, 29, 19, 36, 46,
       37,  5, 68, 20, 53, 54, 25, 48, 34, 22, 31, 73,  4, 60, 21, 71, 52,
       64,  8, 56, 57, 63,  6, 10,  3, 28, 12, 44,  1, 24, 72, 30, 17, 67,
       69, 58, 61, 11, 59, 16, 42, 74, 62, 65, 50, 38, 18, 13, 26,  7, 70,
       41, 39, 45, 23, 66, 40, 14,  0], dtype=int64), 'cur_cost': 582031.0}, {'tour': array([35, 54, 12, 51, 49, 45, 15, 42, 46, 34, 33, 18, 66, 32, 74, 67, 28,
       10, 11, 70, 13, 56,  0, 14, 16, 44,  4,  5, 52, 20, 62, 68, 43,  8,
        2, 17, 71, 72, 24, 65, 55,  1, 57, 30,  3, 75, 53, 36, 23, 29, 38,
       61, 26, 48, 73,  9, 39, 59, 19, 40, 63, 58, 25,  7, 31, 69, 22, 27,
       64,  6, 41, 47, 50, 60, 37, 21], dtype=int64), 'cur_cost': 586214.0}, {'tour': array([49,  0, 11,  6,  3, 17, 19,  1, 70, 26,  2, 36, 56, 46, 28, 53,  8,
       15, 14, 12, 69, 72, 67, 16, 18, 41, 58, 42, 48, 37, 30, 21, 59, 64,
       33, 35, 68, 29, 22, 20, 57, 23, 66, 25, 47, 39, 51, 74, 63,  5, 52,
        9, 71,  4, 54,  7, 61, 62, 40, 34, 75, 13, 73, 10, 50, 60, 31, 38,
       65, 45, 43, 24, 32, 27, 44, 55], dtype=int64), 'cur_cost': 548148.0}, {'tour': array([52, 58, 68, 20, 28,  4, 53, 36, 19,  0, 55, 35, 38, 22, 27, 65, 23,
       42,  9, 10, 21, 45, 73, 14, 13, 72, 32, 24, 74, 31, 11,  3, 62, 18,
       69, 67, 49, 63, 71, 57, 25, 48,  2,  5, 59, 56, 34, 16, 44, 70,  8,
       40, 12, 47, 60, 51, 66, 64, 61,  7, 30, 41, 15,  1, 37, 26, 54, 33,
        6, 75, 17, 46, 43, 50, 39, 29], dtype=int64), 'cur_cost': 527423.0}, {'tour': array([17, 56, 32, 37, 48, 14, 66,  3, 71, 12, 41, 25, 38, 33, 55, 65, 43,
       59, 63, 30, 52, 31, 26, 54, 69,  2, 21, 44, 72, 53, 15, 18, 51, 16,
       35,  5, 20, 22, 57, 27, 62, 75, 64,  0, 23, 34,  6,  9, 42,  8, 68,
       67, 11,  7, 70, 36, 74, 58,  4, 10, 29, 49,  1, 24, 19, 46, 45, 40,
       61, 73, 39, 50, 47, 13, 60, 28], dtype=int64), 'cur_cost': 595476.0}, {'tour': array([27,  9, 47, 25, 54, 39,  8, 38, 70, 28, 49,  4, 57, 33, 60, 48, 44,
       15, 34,  6, 58, 61, 13, 31, 11, 37, 35, 71,  3,  5, 53, 20, 55, 19,
       52, 10, 67, 50, 59,  7, 73, 46, 64, 16, 42, 72, 22, 23, 62, 56, 21,
       14,  2, 69, 65, 12, 43, 29, 68, 36, 63, 74, 32, 40,  1, 45, 41, 26,
       30, 51, 66, 24,  0, 75, 18, 17], dtype=int64), 'cur_cost': 584770.0}, {'tour': array([11, 61, 12, 55, 69, 15, 20,  0, 58, 43, 34,  1, 74, 62, 51,  6,  9,
       45, 38, 75, 50, 67, 63, 30, 70, 49, 10, 47, 66, 17,  7, 42, 60, 68,
       36, 56, 21, 73, 14, 54,  3, 40, 16,  4, 64, 39, 13, 23, 29, 18, 22,
       44, 59, 65, 41, 27, 37, 72, 57, 28, 24, 33, 53, 46,  8, 48, 26, 32,
       52, 31, 25, 35,  2, 71, 19,  5], dtype=int64), 'cur_cost': 594051.0}, {'tour': array([34, 70, 25, 58, 33, 18, 65, 48, 12, 53, 60, 39, 16, 36, 28, 64, 51,
       63, 41,  9, 74, 20,  5,  1, 56, 75,  8, 10, 37,  2, 32, 68, 73,  0,
       54, 30, 55, 59, 13, 11, 66, 47,  4, 52,  3, 45, 71, 22, 43, 31, 19,
       49, 61, 35, 42, 62, 38, 21, 15, 44, 69, 14, 46, 17, 24, 27, 26, 23,
       40, 57,  6,  7, 72, 50, 29, 67], dtype=int64), 'cur_cost': 580535.0}, {'tour': array([49, 37, 43, 27, 16, 39,  5, 53, 23, 40, 46,  4,  2, 50, 60, 30,  9,
       58, 38, 48,  0, 54, 11, 18, 65,  6, 75, 47, 68, 72, 45,  1, 63, 55,
       17, 41, 28, 19, 73, 69, 14, 29, 25, 10, 21, 74, 34, 66, 32, 64, 12,
       70,  8, 62, 44, 26, 33, 56, 57, 13, 15, 67, 31,  7,  3, 51, 42, 35,
       20, 24, 52, 71, 59, 36, 61, 22], dtype=int64), 'cur_cost': 602491.0}, {'tour': array([47, 61, 30, 37, 59, 48,  5, 55, 54, 27, 33, 41, 36,  1, 58, 31, 18,
       16,  3, 43, 29,  9, 75, 69, 49, 23, 68,  7, 63, 56, 50, 57, 24, 26,
       11, 10, 19, 39, 42, 34, 73,  8, 17, 67, 15, 60, 65, 52, 21, 74, 32,
       44, 64, 25, 45, 72, 13, 28, 22, 66, 12, 40, 51, 38, 70, 46,  4, 14,
       20,  0, 35, 62, 53, 71,  6,  2], dtype=int64), 'cur_cost': 551799.0}, {'tour': array([ 6, 24, 32, 52, 42, 74, 33,  4, 23, 40, 11, 27, 18, 66,  9, 64, 65,
       22, 21,  3,  8,  2, 46, 38,  1, 20, 53, 60, 44, 34, 26, 55, 14, 50,
       39, 62, 19, 36, 56, 29, 13, 68, 57, 35, 28, 69, 72, 45, 37, 73, 10,
       48, 75, 47, 54, 30, 51, 43, 63, 61, 59, 41, 49, 58, 17, 31, 16,  5,
       67, 12,  0, 25, 71, 15,  7, 70], dtype=int64), 'cur_cost': 584843.0}, {'tour': array([24, 50, 16, 56, 37, 63, 72, 27, 70, 45, 30, 18, 26, 20, 35, 10, 74,
       40,  6, 60, 25,  5,  8, 73, 51, 21, 28,  9,  4,  1, 66, 17, 64,  7,
       68, 54,  3, 33, 31, 11, 14, 29, 55, 59, 15, 34, 65, 39, 22, 38, 61,
        2, 49, 46, 75, 71, 67, 44, 41, 53, 58, 52, 47,  0, 32, 36, 23, 13,
       62, 69, 48, 19, 43, 12, 42, 57], dtype=int64), 'cur_cost': 579532.0}, {'tour': array([40,  5, 73, 68, 10, 28, 55, 31, 17, 15, 52, 37, 39, 33, 53, 13, 23,
       14, 42, 44, 58, 46,  2, 26, 72, 74, 70, 32, 38, 29, 57, 65,  7, 22,
       24, 18, 63, 54, 16, 62, 67,  8, 11, 75,  9, 50, 41, 56, 66,  4, 34,
       30, 60, 45, 51, 59,  6, 47, 49,  1, 27, 25, 36, 64, 12,  0, 21, 69,
       35, 71,  3, 19, 43, 20, 48, 61], dtype=int64), 'cur_cost': 590627.0}, {'tour': array([ 8, 50, 56, 35, 59, 11, 41,  7, 65, 48, 44, 23,  1, 18, 54, 62, 72,
        0, 31, 58, 12, 22, 14, 29, 52, 19,  9, 33, 38, 55, 40, 60, 10, 13,
       36, 67, 69, 26, 53, 49, 24,  4,  5, 39, 25, 20, 68, 46, 30, 32, 71,
       75, 61, 42, 64, 74, 16, 63, 47, 45, 43, 27, 66, 37, 28, 73, 70,  3,
        6, 34, 15, 21, 57, 51,  2, 17], dtype=int64), 'cur_cost': 536780.0}, {'tour': array([10, 47, 54, 66,  3, 62, 18,  4, 43, 42, 40,  5, 22, 34,  8, 36, 46,
       74, 16, 67, 32, 13, 21, 15, 51, 20, 44, 35, 27, 37, 60, 68,  2, 64,
       19, 25, 17, 11, 58,  9, 57, 33, 71, 12, 39, 23, 73, 31, 30,  6, 28,
       69, 53, 45, 52,  0, 75, 55,  7, 24, 14, 56, 38, 41, 65, 50,  1, 29,
       72, 70, 49, 59, 48, 63, 61, 26], dtype=int64), 'cur_cost': 581997.0}, {'tour': array([28, 15, 12, 54, 34,  1, 16, 10, 39, 59, 37, 49, 45, 56, 71, 22, 47,
       40, 53, 64, 44, 30, 51, 27, 50, 74, 58, 33, 60, 29, 38, 32, 67, 25,
       41,  9, 61, 66,  0,  8, 20,  6, 55, 62, 24, 70, 21, 73,  5, 18,  2,
        4, 63, 75, 31, 48, 42,  7, 46, 65, 13, 17, 52, 14, 68,  3, 11, 19,
       72, 57, 36, 23, 69, 35, 26, 43], dtype=int64), 'cur_cost': 580188.0}, {'tour': array([58, 47, 61, 53, 25, 17, 67, 34,  9, 23, 16, 22, 45, 63, 64,  3, 37,
       68, 40,  2, 11, 42,  4, 59, 10, 14,  6, 13, 38, 28, 19, 51, 71, 55,
       12, 48, 32, 70, 30,  7, 44, 27, 65, 29, 60, 49, 33, 15, 21, 39, 72,
       20,  1,  8, 46, 73, 43, 66, 56, 18, 69, 62, 41, 50, 74, 35, 75, 26,
       57, 31, 54,  5,  0, 36, 24, 52], dtype=int64), 'cur_cost': 594975.0}, {'tour': array([41,  2, 45, 73,  4, 51, 62, 32, 56, 11, 67, 28, 71, 66, 46,  7, 69,
       74, 48, 49, 27, 42,  1, 31, 18, 70, 61, 36, 68, 39, 65, 64, 50, 37,
       59, 60, 72, 33, 54, 43, 10, 17, 19, 34, 13, 29, 75, 15, 23, 35, 63,
        5, 40, 16,  0, 24, 57, 38, 22, 55, 25,  8, 30, 26, 21, 14, 20,  9,
       44,  3, 53,  6, 52, 12, 47, 58], dtype=int64), 'cur_cost': 578591.0}, {'tour': array([15, 17, 60, 25, 48, 28, 19, 66, 63, 74, 22, 47, 55, 50, 59, 32, 24,
        2,  8, 64, 13, 54, 40,  7, 49, 30, 44, 36, 26, 20, 12, 38, 33, 73,
       71, 39, 11,  3, 51, 62, 35, 52,  5, 56,  4,  9, 16, 14, 41, 69, 37,
       58, 46, 43, 23, 34,  6, 21, 53, 72, 65, 68, 18, 67, 45, 61, 27, 29,
       57,  0,  1, 42, 31, 70, 10, 75], dtype=int64), 'cur_cost': 554512.0}, {'tour': array([38, 74, 64, 59, 75, 70,  1, 24, 47, 66, 56, 55,  7,  2, 69, 57, 60,
       36, 67, 63, 13, 68, 65, 53, 14, 22, 19, 50, 44,  6, 28, 18, 43, 61,
       42,  9, 37, 32, 39,  0, 58, 52, 29, 54, 27, 33,  3, 11,  5, 26, 48,
       41, 35, 46, 45, 12, 51,  8, 62, 16, 31, 21, 17, 34, 15, 25, 49, 73,
       23, 20, 72,  4, 40, 10, 30, 71], dtype=int64), 'cur_cost': 599009.0}, {'tour': array([66, 11,  6, 58, 63, 43, 45, 34, 14, 40, 31, 56, 74, 32,  9, 71, 70,
       35, 60, 24, 50, 18, 68,  2, 21, 53, 46, 33, 28, 42, 67, 38, 41, 27,
       15,  1, 64, 30,  7, 52, 12, 39, 54, 19, 36, 65, 23,  0, 61, 55, 22,
       44, 59, 49, 75,  5, 29, 51, 10, 48,  8, 69, 73, 17, 25, 47, 20, 37,
        4, 16, 72, 26,  3, 62, 13, 57], dtype=int64), 'cur_cost': 616932.0}, {'tour': array([23, 69, 28,  3, 11, 22,  2, 50, 45, 46, 52, 36, 21, 67, 37, 68,  4,
        6, 10, 25,  5, 20, 15, 30, 72, 59, 61, 64, 49, 33, 43, 38, 51, 62,
       73, 44,  0, 42, 29, 66,  8, 27, 19, 26, 58, 24, 14, 18, 12, 65, 74,
       75, 53, 70, 55, 13,  7, 48, 32, 57, 54, 60, 41,  9,  1, 35, 31, 56,
       34, 47, 63, 39, 40, 71, 17, 16], dtype=int64), 'cur_cost': 537490.0}, {'tour': array([33, 44, 27, 36, 70, 40, 56, 51, 74, 38, 71, 39, 16, 31,  1, 28, 46,
       65, 48, 12, 29, 43, 53, 54, 61, 69, 11, 26, 60, 55, 13, 37, 52, 22,
       42,  7, 19, 30,  6, 10,  8, 49, 32, 63, 62,  0, 47, 17, 59, 67, 57,
       20, 34, 25, 18, 24, 15,  5, 50, 35, 41, 66, 72,  3, 45,  4,  2, 58,
       73,  9, 75, 14, 21, 23, 64, 68], dtype=int64), 'cur_cost': 550766.0}, {'tour': array([23, 50, 17, 43,  0,  6, 26, 28, 27, 31,  5, 35,  8, 74, 72, 66, 41,
       56, 40, 12, 70, 48, 69, 64, 15,  2, 63, 45, 25, 46, 65, 58, 68, 16,
       32, 38, 59, 47,  3, 62, 67, 13, 73,  9, 36, 71, 22, 42, 51, 37, 24,
        7, 11, 75, 54, 39, 19, 61, 57, 49,  4, 29, 14, 18, 21, 30, 34, 60,
       52, 55,  1, 33, 53, 10, 20, 44], dtype=int64), 'cur_cost': 537057.0}, {'tour': array([18, 20, 53, 71, 47, 19, 50, 31, 23, 39, 38, 28, 46,  9, 54, 74,  1,
       55, 42, 63,  4, 11, 30, 22, 51, 59, 57, 36, 70, 49,  6, 75, 25, 67,
       45, 14, 16, 58,  7, 69, 34, 17, 29, 27, 66, 64,  0, 65, 15, 24, 73,
       35, 62, 13, 52, 12,  5, 37, 21, 56, 44, 10,  3,  8, 41, 32, 40,  2,
       72, 61, 43, 26, 33, 60, 48, 68], dtype=int64), 'cur_cost': 581678.0}, {'tour': array([60, 26, 65, 16, 67, 71, 72, 43, 12, 74, 58, 27, 18, 32, 50, 62, 41,
       46, 29, 59, 10, 14,  0, 49, 61, 22, 11,  6, 21, 37,  1,  9,  3, 19,
       66, 31, 24, 44, 47, 23, 52, 75, 35,  8, 73, 39, 45,  4, 25, 57, 51,
       48, 13, 28, 42,  2, 34, 17, 54, 68, 15, 40, 69,  5, 33, 64, 70, 38,
       53,  7, 36, 63, 56, 55, 20, 30], dtype=int64), 'cur_cost': 539999.0}, {'tour': array([70, 28, 71, 40, 48, 29, 51, 67, 11, 72, 62,  4, 20, 57, 68, 37, 50,
       52, 41, 61, 73,  1, 65, 19, 69, 36,  0, 59, 47, 26, 15, 31, 60, 30,
        7, 53,  2, 14, 45, 12, 23, 54, 74, 43,  6, 34, 24, 46, 27, 35, 55,
       49, 21, 17, 56,  8,  5, 18, 75, 58, 63, 32,  9, 33, 10, 25, 16,  3,
       13, 38, 44, 39, 64, 22, 42, 66], dtype=int64), 'cur_cost': 607783.0}, {'tour': array([45, 29,  3, 28, 62, 13, 47, 23, 24,  1, 57, 52, 32, 56, 35, 10,  8,
       22, 66, 19,  7, 25, 34, 15, 43, 58, 59, 71, 30, 17, 64,  0, 18, 63,
        5, 27, 40, 49, 60,  2, 44, 26, 33, 42, 61, 36, 69, 39, 37, 48, 67,
       70,  4, 72, 11, 46,  6, 41, 65, 51, 20, 50, 75, 53, 31, 21, 68, 54,
       55, 74,  9, 73, 12, 14, 38, 16], dtype=int64), 'cur_cost': 562222.0}, {'tour': array([59, 56, 12, 75, 34, 65, 70, 38, 36, 54, 47, 39, 46,  5, 44, 68, 31,
       57, 74, 30, 18, 48, 41, 71, 73, 23, 27, 37,  4, 28,  3, 29, 14, 16,
       42,  8, 19, 60, 10,  2, 24,  6,  0, 49, 69, 51, 15, 45, 21, 11, 63,
       17, 35, 67, 43, 62, 72, 53,  9, 22,  7, 26, 20, 58, 32, 25, 52, 55,
       50, 13, 61, 66, 64, 33, 40,  1], dtype=int64), 'cur_cost': 534855.0}]
2025-08-04 11:52:42,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-04 11:52:42,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 31, 'total_searches': 12993, 'skip_rate': 0.002385900100053875, 'estimated_time_saved': 155, 'path_optimizer': {'path_count': 12962, 'cache_hits': 0, 'similarity_calculations': 4410, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-04 11:52:42,784 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([ 0, 52,  1, 59,  6, 70, 32, 46, 67,  9, 27, 56, 65, 18, 10, 69, 55,
       23, 13, 25, 61, 15, 33, 73, 74, 30, 31, 48, 53, 39, 35,  2, 14, 63,
       12, 47, 58,  4, 75, 62, 60, 36, 29, 24, 11, 42, 16, 72, 44, 41, 71,
       45, 54, 38, 20, 34, 68, 22, 66,  8,  7, 51, 49, 37, 50, 28, 64, 19,
       26, 21,  3, 57, 43,  5, 40, 17], dtype=int64), 'cur_cost': 612450.0, 'intermediate_solutions': [{'tour': array([56,  9, 48, 72, 35, 73, 28, 41, 19, 59, 27, 60, 57, 74, 50, 40, 71,
       52,  2, 51, 31, 13, 61, 25, 63, 26, 17, 67, 45, 62, 20,  1, 14, 33,
        8, 68, 55, 49,  4, 21,  0, 58, 65, 42, 54, 11, 70, 44, 53, 22, 39,
        6, 23, 37, 18, 43, 29,  3, 34, 10, 69,  7, 12, 36, 30, 38, 16, 66,
       75, 47,  5, 15, 46, 32, 64, 24], dtype=int64), 'cur_cost': 613028.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([72, 56,  9, 48, 35, 73, 28, 41, 19, 59, 27, 60, 57, 74, 50, 40, 71,
       52,  2, 51, 31, 13, 61, 25, 63, 26, 17, 67, 45, 62, 20,  1, 14, 33,
        8, 68, 55, 49,  4, 21,  0, 58, 65, 42, 54, 11, 70, 44, 53, 22, 39,
        6, 23, 37, 18, 43, 29,  3, 34, 10, 69,  7, 12, 36, 30, 38, 16, 66,
       75, 47,  5, 15, 46, 32, 64, 24], dtype=int64), 'cur_cost': 610822.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 72, 56,  9, 48, 73, 28, 41, 19, 59, 27, 60, 57, 74, 50, 40, 71,
       52,  2, 51, 31, 13, 61, 25, 63, 26, 17, 67, 45, 62, 20,  1, 14, 33,
        8, 68, 55, 49,  4, 21,  0, 58, 65, 42, 54, 11, 70, 44, 53, 22, 39,
        6, 23, 37, 18, 43, 29,  3, 34, 10, 69,  7, 12, 36, 30, 38, 16, 66,
       75, 47,  5, 15, 46, 32, 64, 24], dtype=int64), 'cur_cost': 614391.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 72, 56,  9, 35, 73, 28, 41, 19, 59, 27, 60, 57, 74, 50, 40, 71,
       52,  2, 51, 31, 13, 61, 25, 63, 26, 17, 67, 45, 62, 20,  1, 14, 33,
        8, 68, 55, 49,  4, 21,  0, 58, 65, 42, 54, 11, 70, 44, 53, 22, 39,
        6, 23, 37, 18, 43, 29,  3, 34, 10, 69,  7, 12, 36, 30, 38, 16, 66,
       75, 47,  5, 15, 46, 32, 64, 24], dtype=int64), 'cur_cost': 598109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 35, 72, 56,  9, 73, 28, 41, 19, 59, 27, 60, 57, 74, 50, 40, 71,
       52,  2, 51, 31, 13, 61, 25, 63, 26, 17, 67, 45, 62, 20,  1, 14, 33,
        8, 68, 55, 49,  4, 21,  0, 58, 65, 42, 54, 11, 70, 44, 53, 22, 39,
        6, 23, 37, 18, 43, 29,  3, 34, 10, 69,  7, 12, 36, 30, 38, 16, 66,
       75, 47,  5, 15, 46, 32, 64, 24], dtype=int64), 'cur_cost': 603200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 11:52:42,785 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 612450.00)
2025-08-04 11:52:42,785 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-04 11:52:42,785 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-04 11:52:42,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,839 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-04 11:52:42,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,840 - ExplorationExpert - INFO - 探索路径生成完成，成本: 431099.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,840 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [72, 48, 59, 41, 50, 53, 52, 40, 16, 15, 5, 22, 44, 54, 27, 24, 47, 34, 12, 11, 29, 25, 10, 17, 9, 42, 57, 60, 26, 35, 14, 1, 30, 23, 18, 6, 37, 33, 65, 63, 31, 36, 32, 4, 8, 45, 3, 38, 13, 28, 55, 61, 70, 69, 64, 68, 62, 66, 49, 56, 43, 21, 7, 0, 19, 39, 71, 46, 20, 74, 73, 2, 75, 51, 58, 67], 'cur_cost': 431099.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,840 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 431099.00)
2025-08-04 11:52:42,840 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-04 11:52:42,841 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-04 11:52:42,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,874 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,874 - ExplorationExpert - INFO - 探索路径生成完成，成本: 140467.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,875 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 73], 'cur_cost': 140467.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,875 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 140467.00)
2025-08-04 11:52:42,875 - experts.management.collaboration_manager - INFO - 为个体 19 生成探索路径
2025-08-04 11:52:42,875 - ExplorationExpert - INFO - 开始为个体 19 生成探索路径（算法实现）
2025-08-04 11:52:42,875 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,896 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,897 - ExplorationExpert - INFO - 探索路径生成完成，成本: 148496.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,897 - experts.management.collaboration_manager - INFO - 个体 19 探索路径生成报告: {'new_tour': [0, 35, 28, 26, 25, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 32, 31, 37, 38, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 148496.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,898 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 148496.00)
2025-08-04 11:52:42,898 - experts.management.collaboration_manager - INFO - 为个体 20 生成探索路径
2025-08-04 11:52:42,898 - ExplorationExpert - INFO - 开始为个体 20 生成探索路径（算法实现）
2025-08-04 11:52:42,898 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,917 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-04 11:52:42,917 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,918 - ExplorationExpert - INFO - 探索路径生成完成，成本: 137864.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,918 - experts.management.collaboration_manager - INFO - 个体 20 探索路径生成报告: {'new_tour': [0, 4, 3, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 9, 8, 5, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 10, 11, 12, 13, 14, 15, 73, 74, 75], 'cur_cost': 137864.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,918 - experts.management.collaboration_manager - INFO - 个体 20 接受新路径 (成本: 137864.00)
2025-08-04 11:52:42,918 - experts.management.collaboration_manager - INFO - 为个体 21 生成探索路径
2025-08-04 11:52:42,918 - ExplorationExpert - INFO - 开始为个体 21 生成探索路径（算法实现）
2025-08-04 11:52:42,918 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 11:52:42,936 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-04 11:52:42,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 11:52:42,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 585536.0, 路径长度: 76, 收集中间解: 0
2025-08-04 11:52:42,938 - experts.management.collaboration_manager - INFO - 个体 21 探索路径生成报告: {'new_tour': [31, 9, 50, 18, 7, 52, 47, 33, 8, 24, 44, 27, 57, 51, 59, 64, 35, 72, 56, 74, 40, 42, 60, 63, 2, 62, 39, 29, 16, 36, 55, 46, 32, 10, 61, 1, 30, 14, 25, 37, 38, 3, 34, 67, 20, 68, 70, 43, 22, 15, 45, 65, 75, 71, 11, 48, 69, 26, 58, 12, 6, 41, 49, 19, 66, 53, 17, 4, 13, 5, 28, 0, 54, 73, 21, 23], 'cur_cost': 585536.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 11:52:42,938 - experts.management.collaboration_manager - INFO - 个体 21 接受新路径 (成本: 585536.00)
2025-08-04 11:52:42,938 - experts.management.collaboration_manager - INFO - 为个体 22 生成探索路径
2025-08-04 11:52:42,938 - ExplorationExpert - INFO - 开始为个体 22 生成探索路径（算法实现）
2025-08-04 11:52:42,938 - ExplorationExpert - INFO - 开始生成多样化探索路径
