2025-08-05 10:29:11,952 - __main__ - INFO - st70 开始进化第 1 代
2025-08-05 10:29:11,952 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:11,954 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:11,958 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=815.000, 多样性=0.988
2025-08-05 10:29:11,960 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:11,964 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.988
2025-08-05 10:29:11,965 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:11,967 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:11,967 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:11,967 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:11,968 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:11,990 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -186.980, 聚类评分: 0.000, 覆盖率: 0.153, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:11,991 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:11,991 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:11,991 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 10:29:11,999 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.8%, 梯度: 103.33 → 95.28
2025-08-05 10:29:12,097 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_136_20250805_102912.html
2025-08-05 10:29:12,166 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_136_20250805_102912.html
2025-08-05 10:29:12,166 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 136
2025-08-05 10:29:12,166 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:12,166 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1987秒
2025-08-05 10:29:12,167 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 272, 'max_size': 500, 'hits': 0, 'misses': 272, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 901, 'misses': 464, 'hit_rate': 0.6600732600732601, 'evictions': 364, 'ttl': 7200}}
2025-08-05 10:29:12,167 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -186.97999999999993, 'local_optima_density': 0.2, 'gradient_variance': 2447712.9955999996, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1535, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -186.980)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.153)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360951.9917955, 'performance_metrics': {}}}
2025-08-05 10:29:12,167 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:12,167 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:12,167 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:12,168 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:12,169 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,169 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:12,169 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,169 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,169 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:12,169 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,170 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,170 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:12,170 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:12,170 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:12,170 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:12,170 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,173 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 860.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,174 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 860.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,174 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 860.00)
2025-08-05 10:29:12,174 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:12,174 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:12,174 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,176 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3065.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,177 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 3065.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,178 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 3065.00)
2025-08-05 10:29:12,178 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:12,178 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:12,178 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,180 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,181 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,181 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1042.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,181 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1042.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,182 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1042.00)
2025-08-05 10:29:12,182 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:12,182 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:12,182 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,196 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,197 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,197 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2421.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,197 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2421.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,198 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2421.00)
2025-08-05 10:29:12,198 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:12,198 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,198 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,198 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3586.0
2025-08-05 10:29:12,208 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:12,208 - ExploitationExpert - INFO - res_population_costs: [689.0, 689, 688, 688, 688]
2025-08-05 10:29:12,209 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-08-05 10:29:12,211 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,211 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 860.0}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 3065.0}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1042.0}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2421.0}, {'tour': array([68, 61, 34, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44], dtype=int64), 'cur_cost': 3586.0}, {'tour': array([30,  4,  5, 31, 22, 48, 60, 34,  0, 35, 61, 24, 66, 50, 17, 32, 10,
       19, 65, 18, 49, 40, 69, 28, 13, 25, 53,  6, 29, 33, 57,  7,  2, 26,
       20, 38, 68, 44, 45, 51, 62, 67,  8, 64, 52, 37, 55,  9, 21, 39, 47,
       27, 14, 56, 63, 23, 16, 12,  3,  1, 59, 58, 36, 42, 43, 41, 11, 54,
       46, 15], dtype=int64), 'cur_cost': 3496.0}, {'tour': array([61,  8,  9, 26, 46, 14, 58, 47, 28, 68, 37, 24,  3,  4, 60, 50, 40,
       65, 18, 55, 34, 67, 23, 15,  1,  2, 43, 59, 66, 57, 20, 12, 62, 56,
       22, 69, 19, 11, 64, 39, 33, 38, 32, 36, 49, 25, 27, 29, 13, 17, 53,
       45, 31,  6, 16, 51, 35, 41,  7,  0,  5, 10, 21, 44, 54, 52, 48, 30,
       63, 42], dtype=int64), 'cur_cost': 3435.0}, {'tour': array([66, 18, 23, 68, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3957.0}, {'tour': array([52,  7, 47, 67, 10,  5, 65, 46, 20, 25, 18, 24,  0,  9, 26, 12, 44,
        2, 41, 15, 37, 68, 34, 28, 22, 60,  1, 17, 14, 23, 63, 49, 58,  8,
       55, 21, 40, 19, 13, 31, 32, 62, 30, 51,  4, 48, 27, 33, 11, 53, 29,
       57, 38, 43, 59, 54, 64, 66, 56,  6, 35, 69, 39, 16, 50, 61,  3, 42,
       45, 36], dtype=int64), 'cur_cost': 3406.0}, {'tour': array([19, 42, 29, 23, 24, 33,  7, 50,  1, 11, 21, 28, 69, 17, 35, 48, 10,
       44,  0, 16, 36, 55, 68, 41,  4, 26, 57, 40, 66, 32,  5, 39, 52, 65,
       49, 63,  3, 43, 45, 27, 56, 18, 67, 37,  6, 14, 22, 62, 58, 12, 30,
       20, 54, 61, 53, 13, 51,  8,  2, 47, 25, 60, 31,  9, 59, 46, 34, 64,
       38, 15], dtype=int64), 'cur_cost': 3660.0}]
2025-08-05 10:29:12,215 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:12,216 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 352, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 352, 'cache_hits': 0, 'similarity_calculations': 1824, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,217 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([68, 61, 34, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44], dtype=int64), 'cur_cost': 3586.0, 'intermediate_solutions': [{'tour': array([17, 15,  4, 30, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 17, 15,  4, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30, 17, 15,  4, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 30, 17, 15, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 18, 30, 17, 15, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,218 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3586.00)
2025-08-05 10:29:12,218 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:12,218 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:12,218 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,223 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,223 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 865.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,224 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 865.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,224 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 865.00)
2025-08-05 10:29:12,224 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:12,225 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:12,225 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,228 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3104.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,229 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3104.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,230 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 3104.00)
2025-08-05 10:29:12,230 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:12,230 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,230 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,231 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3820.0
2025-08-05 10:29:12,243 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:12,243 - ExploitationExpert - INFO - res_population_costs: [689.0, 689, 688, 688, 688]
2025-08-05 10:29:12,243 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-08-05 10:29:12,246 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,246 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 860.0}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 3065.0}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1042.0}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2421.0}, {'tour': array([68, 61, 34, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44], dtype=int64), 'cur_cost': 3586.0}, {'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 865.0}, {'tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3104.0}, {'tour': array([57, 20, 36, 30, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58], dtype=int64), 'cur_cost': 3820.0}, {'tour': array([52,  7, 47, 67, 10,  5, 65, 46, 20, 25, 18, 24,  0,  9, 26, 12, 44,
        2, 41, 15, 37, 68, 34, 28, 22, 60,  1, 17, 14, 23, 63, 49, 58,  8,
       55, 21, 40, 19, 13, 31, 32, 62, 30, 51,  4, 48, 27, 33, 11, 53, 29,
       57, 38, 43, 59, 54, 64, 66, 56,  6, 35, 69, 39, 16, 50, 61,  3, 42,
       45, 36], dtype=int64), 'cur_cost': 3406.0}, {'tour': array([19, 42, 29, 23, 24, 33,  7, 50,  1, 11, 21, 28, 69, 17, 35, 48, 10,
       44,  0, 16, 36, 55, 68, 41,  4, 26, 57, 40, 66, 32,  5, 39, 52, 65,
       49, 63,  3, 43, 45, 27, 56, 18, 67, 37,  6, 14, 22, 62, 58, 12, 30,
       20, 54, 61, 53, 13, 51,  8,  2, 47, 25, 60, 31,  9, 59, 46, 34, 64,
       38, 15], dtype=int64), 'cur_cost': 3660.0}]
2025-08-05 10:29:12,250 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:12,250 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 353, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 353, 'cache_hits': 0, 'similarity_calculations': 1825, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,252 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([57, 20, 36, 30, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58], dtype=int64), 'cur_cost': 3820.0, 'intermediate_solutions': [{'tour': array([23, 18, 66, 68, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 23, 18, 66, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3849.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([61, 68, 23, 18, 66, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([66, 68, 23, 18, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([66, 61, 68, 23, 18, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,252 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 3820.00)
2025-08-05 10:29:12,252 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:12,253 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:12,253 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,257 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,257 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,258 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2946.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,258 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,258 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2946.00)
2025-08-05 10:29:12,259 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:12,259 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:12,259 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,266 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,267 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2274.0, 路径长度: 70, 收集中间解: 0
2025-08-05 10:29:12,267 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2274.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,267 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2274.00)
2025-08-05 10:29:12,267 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:12,267 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:12,269 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 860.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 3065.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1042.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2421.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([68, 61, 34, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44], dtype=int64), 'cur_cost': 3586.0, 'intermediate_solutions': [{'tour': array([17, 15,  4, 30, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3786.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 17, 15,  4, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30, 17, 15,  4, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 30, 17, 15, 18, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 18, 30, 17, 15, 65, 37, 48, 53, 69,  6, 43, 67, 36, 63, 61, 23,
       38,  7, 10, 56,  5, 20, 11, 28, 68,  8, 21, 44,  9, 50,  3, 64, 24,
       14, 46, 66, 42, 22,  2,  1, 59, 49, 27, 33, 34, 32, 35, 41, 16, 54,
       29, 45, 55, 58, 62, 19, 31, 47, 13, 40, 51, 12, 52, 25, 39, 60,  0,
       57, 26], dtype=int64), 'cur_cost': 3828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 865.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3104.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([57, 20, 36, 30, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58], dtype=int64), 'cur_cost': 3820.0, 'intermediate_solutions': [{'tour': array([23, 18, 66, 68, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 23, 18, 66, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3849.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([61, 68, 23, 18, 66, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3949.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([66, 68, 23, 18, 61, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([66, 61, 68, 23, 18, 55,  4, 43, 50,  9, 64, 56, 52,  7, 20, 30, 33,
       15, 39, 46, 29, 14, 26, 63, 27, 57, 38, 11, 10, 35, 21, 37, 51, 49,
       58, 60, 62,  1, 59, 48, 44, 32, 65, 17, 69, 41, 16, 34, 24, 54,  3,
       19,  5, 53, 12, 40,  0,  2, 67, 42, 36,  6, 45, 25, 28, 13, 31,  8,
       47, 22], dtype=int64), 'cur_cost': 3947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2274.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:12,270 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:12,270 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:12,274 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=860.000, 多样性=0.963
2025-08-05 10:29:12,275 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:12,275 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:12,275 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:12,275 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03818768887803537, 'best_improvement': -0.05521472392638037}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.026019916479280304}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05386144450230809, 'recent_improvements': [-0.07753448356921665, -0.053369877261297084, 0.03018840543539954], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 688, 'new_best_cost': 688, 'quality_improvement': 0.0, 'old_diversity': 0.7785714285714286, 'new_diversity': 0.7785714285714286, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:12,276 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:12,276 - __main__ - INFO - st70 开始进化第 2 代
2025-08-05 10:29:12,276 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:12,276 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:12,277 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=860.000, 多样性=0.963
2025-08-05 10:29:12,277 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:12,280 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.963
2025-08-05 10:29:12,281 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:12,283 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.779
2025-08-05 10:29:12,288 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:12,288 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:12,288 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:12,288 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:12,334 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.267, 适应度梯度: -161.227, 聚类评分: 0.000, 覆盖率: 0.155, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:12,334 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:12,334 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:12,334 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 10:29:12,339 - visualization.landscape_visualizer - INFO - 插值约束: 487 个点被约束到最小值 688.00
2025-08-05 10:29:12,340 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.3%, 梯度: 122.58 → 114.89
2025-08-05 10:29:12,474 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_137_20250805_102912.html
2025-08-05 10:29:12,517 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_137_20250805_102912.html
2025-08-05 10:29:12,517 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 137
2025-08-05 10:29:12,517 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:12,517 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2302秒
2025-08-05 10:29:12,517 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.26666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -161.2266666666666, 'local_optima_density': 0.26666666666666666, 'gradient_variance': 1001136.9166222222, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.155, 'fitness_entropy': 0.8670089610455386, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -161.227)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.155)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360952.3341172, 'performance_metrics': {}}}
2025-08-05 10:29:12,518 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:12,518 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:12,518 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:12,518 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:12,519 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,519 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:12,519 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,519 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,519 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:12,519 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:12,520 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,520 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:12,520 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:12,520 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:12,520 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:12,521 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,523 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3116.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,524 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 3116.0, 'intermediate_solutions': [{'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 25, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 12, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 67, 43, 29, 19, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 16, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,524 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 3116.00)
2025-08-05 10:29:12,524 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:12,524 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:12,524 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,527 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,528 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,528 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1012.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,528 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 19, 4, 48, 30, 35, 0, 66, 61, 44, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 2994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 68, 8, 24, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 2995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 1, 69, 50, 36, 38, 54, 51], 'cur_cost': 3041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,528 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1012.00)
2025-08-05 10:29:12,528 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:12,528 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:12,528 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,530 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,532 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,533 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3196.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,533 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3196.0, 'intermediate_solutions': [{'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 61, 35, 51, 59, 11, 20, 33, 32, 53, 65, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 63, 50, 64, 55, 66, 47, 61, 53, 32, 33, 20, 11, 59, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 44, 47, 66, 55, 64, 50, 63, 60, 39, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,534 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 3196.00)
2025-08-05 10:29:12,534 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:12,534 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:12,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,543 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,543 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,544 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2416.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,544 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2416.0, 'intermediate_solutions': [{'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 40, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 50, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2502.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 37, 62, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2429.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 67, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2483.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,544 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2416.00)
2025-08-05 10:29:12,544 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:12,545 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,545 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,545 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3331.0
2025-08-05 10:29:12,559 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:12,559 - ExploitationExpert - INFO - res_population_costs: [688, 688, 688, 689.0, 689, 687, 684, 681]
2025-08-05 10:29:12,560 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-08-05 10:29:12,563 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,563 - ExploitationExpert - INFO - populations: [{'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 3116.0}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1012.0}, {'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3196.0}, {'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2416.0}, {'tour': array([42, 50, 43, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28], dtype=int64), 'cur_cost': 3331.0}, {'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 865.0}, {'tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3104.0}, {'tour': [57, 20, 36, 30, 61, 29, 15, 0, 8, 11, 62, 12, 48, 49, 41, 51, 6, 47, 46, 39, 32, 9, 44, 18, 5, 64, 17, 4, 60, 13, 37, 66, 31, 25, 34, 28, 35, 21, 63, 7, 52, 65, 2, 26, 10, 68, 43, 59, 45, 40, 27, 33, 53, 19, 56, 38, 14, 16, 22, 55, 50, 1, 23, 24, 67, 69, 3, 42, 54, 58], 'cur_cost': 3820.0}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2274.0}]
2025-08-05 10:29:12,564 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:12,564 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 354, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 354, 'cache_hits': 0, 'similarity_calculations': 1827, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,565 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([42, 50, 43, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28], dtype=int64), 'cur_cost': 3331.0, 'intermediate_solutions': [{'tour': array([34, 61, 68, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 34, 61, 68, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 42, 34, 61, 68, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([68, 42, 34, 61, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([68, 40, 42, 34, 61, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,565 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3331.00)
2025-08-05 10:29:12,566 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:12,566 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:12,566 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,568 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3282.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,569 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3282.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 7, 2, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 25, 27, 7, 2, 31, 6, 18, 23, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 14, 1, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 25, 63], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,569 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 3282.00)
2025-08-05 10:29:12,569 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:12,569 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:12,569 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,575 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,575 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,576 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,576 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,576 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2535.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,576 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2535.0, 'intermediate_solutions': [{'tour': [28, 33, 17, 52, 42, 1, 3, 12, 6, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 58, 38, 53, 10, 60, 63, 50, 40, 55, 24, 39, 47, 32, 20, 8, 43, 15, 19, 61, 66, 0, 35, 57, 4, 44, 46, 45, 34, 67, 69, 41, 30, 68, 37, 22, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 17, 52, 6, 1, 3, 28, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,576 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 2535.00)
2025-08-05 10:29:12,576 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:12,576 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,577 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,577 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3579.0
2025-08-05 10:29:12,586 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:12,586 - ExploitationExpert - INFO - res_population_costs: [688, 688, 688, 689.0, 689, 687, 684, 681]
2025-08-05 10:29:12,586 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-08-05 10:29:12,590 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,590 - ExploitationExpert - INFO - populations: [{'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 3116.0}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1012.0}, {'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3196.0}, {'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2416.0}, {'tour': array([42, 50, 43, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28], dtype=int64), 'cur_cost': 3331.0}, {'tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3282.0}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2535.0}, {'tour': array([38, 43, 50, 68, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58], dtype=int64), 'cur_cost': 3579.0}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2274.0}]
2025-08-05 10:29:12,591 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:12,591 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 355, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 355, 'cache_hits': 0, 'similarity_calculations': 1830, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,592 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([38, 43, 50, 68, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58], dtype=int64), 'cur_cost': 3579.0, 'intermediate_solutions': [{'tour': array([36, 20, 57, 30, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 36, 20, 57, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([61, 30, 36, 20, 57, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 30, 36, 20, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3746.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 61, 30, 36, 20, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3816.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,592 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 3579.00)
2025-08-05 10:29:12,592 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:12,592 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:12,593 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,602 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2766.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,604 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2766.0, 'intermediate_solutions': [{'tour': [5, 46, 32, 18, 42, 57, 4, 59, 67, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 40, 27], 'cur_cost': 2968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 16, 37, 22, 56, 14, 60, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 58, 64, 50, 45, 2, 65, 51, 38, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,605 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2766.00)
2025-08-05 10:29:12,605 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:12,605 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:12,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,608 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3413.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,609 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 9, 31, 40, 15, 12, 8, 1, 62, 30, 7, 27, 42, 19, 56, 14, 65, 67, 25, 21, 20, 59, 64, 4, 37, 69, 46, 68, 16, 57, 5, 41, 39, 3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10, 60, 6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58, 2, 47, 18, 44, 35], 'cur_cost': 3413.0, 'intermediate_solutions': [{'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 22, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 40, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2347.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 10, 15, 56, 12, 69, 35, 68, 22, 46, 25, 31, 3, 11, 17, 43, 42, 8, 6, 13, 5, 19, 26, 60, 59, 63, 51, 24, 47, 16, 39], 'cur_cost': 2378.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 56, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 15, 10, 47, 16, 39], 'cur_cost': 2227.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,610 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3413.00)
2025-08-05 10:29:12,610 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:12,610 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:12,613 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 3116.0, 'intermediate_solutions': [{'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 25, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 12, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 67, 43, 29, 19, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 17, 18, 6, 1, 3, 41, 40, 42, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 16, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 19, 4, 48, 30, 35, 0, 66, 61, 44, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 2994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 68, 8, 24, 40, 46, 49, 63, 18, 53, 25, 57, 42, 69, 50, 36, 38, 54, 51, 1], 'cur_cost': 2995.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 33, 9, 52, 21, 62, 58, 37, 12, 28, 34, 23, 31, 2, 7, 27, 13, 29, 39, 60, 65, 17, 22, 56, 16, 59, 64, 11, 67, 10, 55, 3, 45, 6, 44, 4, 48, 30, 35, 0, 66, 61, 19, 15, 43, 26, 41, 32, 20, 47, 14, 24, 8, 68, 40, 46, 49, 63, 18, 53, 25, 57, 42, 1, 69, 50, 36, 38, 54, 51], 'cur_cost': 3041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3196.0, 'intermediate_solutions': [{'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 61, 35, 51, 59, 11, 20, 33, 32, 53, 65, 47, 66, 55, 64, 50, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1228.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 63, 50, 64, 55, 66, 47, 61, 53, 32, 33, 20, 11, 59, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 10, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 44, 47, 66, 55, 64, 50, 63, 60, 39, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2416.0, 'intermediate_solutions': [{'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 40, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 50, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2502.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 67, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 37, 62, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2429.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 46, 32, 20, 10, 57, 4, 59, 60, 8, 44, 53, 50, 64, 38, 19, 13, 2, 43, 31, 54, 1, 23, 14, 17, 41, 18, 21, 69, 52, 65, 62, 37, 51, 0, 22, 30, 9, 40, 56, 6, 7, 26, 3, 29, 33, 47, 15, 11, 39, 61, 16, 36, 67, 55, 42, 45, 66, 35, 68, 34, 58, 5, 25, 27, 48, 49, 12, 63, 24], 'cur_cost': 2483.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([42, 50, 43, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28], dtype=int64), 'cur_cost': 3331.0, 'intermediate_solutions': [{'tour': array([34, 61, 68, 42, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 34, 61, 68, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 42, 34, 61, 68, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([68, 42, 34, 61, 40, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([68, 40, 42, 34, 61, 62, 21, 18, 45, 36, 49, 28, 35, 25, 20, 10, 55,
       41, 26,  4, 50, 67, 58, 17, 65, 47,  0, 33, 59, 38, 48,  2, 51,  9,
       63, 43, 53, 12, 31, 39, 52, 27, 15, 30, 11, 57,  6, 56, 14, 54,  5,
       32, 23, 60, 19, 13,  3, 66,  7,  8, 22, 46,  1, 16, 24, 64, 37, 69,
       29, 44]), 'cur_cost': 3591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3282.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 7, 2, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 25, 27, 7, 2, 31, 6, 18, 23, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 14, 1, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 6, 31, 2, 7, 27, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 25, 63], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2535.0, 'intermediate_solutions': [{'tour': [28, 33, 17, 52, 42, 1, 3, 12, 6, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3156.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 33, 17, 52, 6, 1, 3, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 58, 38, 53, 10, 60, 63, 50, 40, 55, 24, 39, 47, 32, 20, 8, 43, 15, 19, 61, 66, 0, 35, 57, 4, 44, 46, 45, 34, 67, 69, 41, 30, 68, 37, 22, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 17, 52, 6, 1, 3, 28, 12, 42, 16, 23, 31, 2, 27, 25, 9, 29, 65, 14, 56, 22, 37, 68, 30, 41, 69, 67, 34, 45, 46, 44, 4, 57, 35, 0, 66, 61, 19, 15, 43, 8, 20, 32, 47, 39, 24, 55, 40, 50, 63, 60, 10, 53, 38, 58, 64, 36, 59, 7, 51, 62, 21, 26, 18, 11, 49, 5, 48, 13, 54], 'cur_cost': 3027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 43, 50, 68, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58], dtype=int64), 'cur_cost': 3579.0, 'intermediate_solutions': [{'tour': array([36, 20, 57, 30, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 36, 20, 57, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([61, 30, 36, 20, 57, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3882.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 30, 36, 20, 61, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3746.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 61, 30, 36, 20, 29, 15,  0,  8, 11, 62, 12, 48, 49, 41, 51,  6,
       47, 46, 39, 32,  9, 44, 18,  5, 64, 17,  4, 60, 13, 37, 66, 31, 25,
       34, 28, 35, 21, 63,  7, 52, 65,  2, 26, 10, 68, 43, 59, 45, 40, 27,
       33, 53, 19, 56, 38, 14, 16, 22, 55, 50,  1, 23, 24, 67, 69,  3, 42,
       54, 58]), 'cur_cost': 3816.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2766.0, 'intermediate_solutions': [{'tour': [5, 46, 32, 18, 42, 57, 4, 59, 67, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 40, 27], 'cur_cost': 2968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 16, 37, 22, 56, 14, 60, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 64, 50, 45, 2, 65, 51, 38, 58, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 46, 32, 18, 42, 57, 4, 59, 40, 1, 34, 23, 53, 11, 7, 25, 48, 54, 52, 60, 14, 56, 22, 37, 16, 30, 12, 28, 69, 10, 55, 3, 62, 6, 44, 36, 61, 49, 0, 66, 35, 15, 43, 26, 41, 47, 20, 63, 39, 24, 8, 68, 58, 64, 50, 45, 2, 65, 51, 38, 9, 21, 33, 19, 29, 13, 17, 31, 67, 27], 'cur_cost': 2880.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 9, 31, 40, 15, 12, 8, 1, 62, 30, 7, 27, 42, 19, 56, 14, 65, 67, 25, 21, 20, 59, 64, 4, 37, 69, 46, 68, 16, 57, 5, 41, 39, 3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10, 60, 6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58, 2, 47, 18, 44, 35], 'cur_cost': 3413.0, 'intermediate_solutions': [{'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 22, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 40, 68, 35, 69, 12, 56, 15, 10, 47, 16, 39], 'cur_cost': 2347.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 54, 67, 20, 53, 45, 10, 15, 56, 12, 69, 35, 68, 22, 46, 25, 31, 3, 11, 17, 43, 42, 8, 6, 13, 5, 19, 26, 60, 59, 63, 51, 24, 47, 16, 39], 'cur_cost': 2378.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 34, 28, 57, 49, 66, 4, 64, 61, 38, 29, 7, 27, 65, 21, 40, 32, 52, 41, 2, 48, 1, 62, 36, 0, 37, 50, 55, 44, 33, 9, 30, 58, 18, 14, 56, 54, 67, 20, 53, 45, 24, 51, 63, 59, 60, 26, 19, 5, 13, 6, 8, 42, 43, 17, 11, 3, 31, 25, 46, 22, 68, 35, 69, 12, 15, 10, 47, 16, 39], 'cur_cost': 2227.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:12,613 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:12,613 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:12,618 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1012.000, 多样性=0.967
2025-08-05 10:29:12,618 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:12,618 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:12,618 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:12,619 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.10269485609790324, 'best_improvement': -0.17674418604651163}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0049472295514511695}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04577878306966623, 'recent_improvements': [-0.053369877261297084, 0.03018840543539954, 0.03818768887803537], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 681, 'new_best_cost': 681, 'quality_improvement': 0.0, 'old_diversity': 0.7734693877551021, 'new_diversity': 0.7734693877551021, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:12,620 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:12,621 - __main__ - INFO - st70 开始进化第 3 代
2025-08-05 10:29:12,621 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:12,621 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:12,622 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1012.000, 多样性=0.967
2025-08-05 10:29:12,623 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:12,627 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.967
2025-08-05 10:29:12,628 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:12,632 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.773
2025-08-05 10:29:12,634 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:12,635 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:12,635 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:29:12,635 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:29:12,723 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.111, 适应度梯度: -236.278, 聚类评分: 0.000, 覆盖率: 0.156, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:12,723 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:12,724 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:12,724 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 10:29:12,731 - visualization.landscape_visualizer - INFO - 插值约束: 201 个点被约束到最小值 681.00
2025-08-05 10:29:12,733 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 153.59 → 142.58
2025-08-05 10:29:12,856 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_138_20250805_102912.html
2025-08-05 10:29:12,916 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_138_20250805_102912.html
2025-08-05 10:29:12,916 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 138
2025-08-05 10:29:12,916 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:12,916 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2819秒
2025-08-05 10:29:12,916 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1111111111111111, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -236.27777777777777, 'local_optima_density': 0.1111111111111111, 'gradient_variance': 925810.8450617284, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1561, 'fitness_entropy': 0.8222226610532919, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -236.278)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.156)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360952.7234654, 'performance_metrics': {}}}
2025-08-05 10:29:12,916 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:12,916 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:12,917 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:12,917 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:12,917 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:12,917 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:12,917 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:12,918 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,918 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:12,918 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:12,918 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:12,918 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:12,918 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:12,919 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:12,919 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:12,919 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,921 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 931.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,922 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 931.0, 'intermediate_solutions': [{'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 45, 67, 34, 69, 18, 30, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 2997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 13, 62, 31, 23, 36, 25, 26, 22, 63, 49, 42, 53, 6, 54, 60, 52], 'cur_cost': 3221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 17, 13, 6, 54, 60, 52], 'cur_cost': 3104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,923 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 931.00)
2025-08-05 10:29:12,923 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:12,923 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:12,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,924 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3456.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,926 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3456.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 33, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 23, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1225.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 26, 45, 24, 38, 44, 8], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 46, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,926 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 3456.00)
2025-08-05 10:29:12,926 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:12,926 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:12,926 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,928 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 963.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,930 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 963.0, 'intermediate_solutions': [{'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 13, 60, 12, 9, 15, 23, 22, 0, 14], 'cur_cost': 3040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 51, 39, 63, 46, 36, 64, 49, 50, 24, 55, 44, 47, 56, 53, 32, 3, 11, 59, 61, 66, 17, 35, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 18, 20, 16, 10, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 40, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,930 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 963.00)
2025-08-05 10:29:12,930 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:12,930 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:12,930 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,935 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:12,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2458.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,937 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2458.0, 'intermediate_solutions': [{'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 19, 27, 28, 0], 'cur_cost': 2419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 13, 48, 43, 31, 41, 21, 20, 51, 7, 25, 67, 16, 5, 49, 11, 42, 59, 52, 9, 62, 3, 23, 30, 35, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 37, 7, 51, 20, 55, 46, 64, 4, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,937 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 2458.00)
2025-08-05 10:29:12,938 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:12,938 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,938 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,938 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3449.0
2025-08-05 10:29:12,947 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:12,947 - ExploitationExpert - INFO - res_population_costs: [681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:12,947 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:12,951 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,951 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 931.0}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3456.0}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 963.0}, {'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2458.0}, {'tour': array([29, 18, 33, 42, 45, 64, 38, 52,  8, 50, 49, 21, 59, 34, 43, 47, 53,
        2,  5,  6, 54,  3,  0, 17, 31, 66, 37,  1, 58, 51, 15, 69, 68, 26,
        9, 11, 44, 56,  4, 36, 10, 57, 27, 35, 41, 39,  7, 67, 62, 30, 55,
       16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48,
       14, 32], dtype=int64), 'cur_cost': 3449.0}, {'tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3282.0}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2535.0}, {'tour': [38, 43, 50, 68, 44, 21, 35, 27, 9, 48, 12, 13, 37, 31, 45, 33, 62, 69, 34, 4, 52, 65, 40, 42, 16, 5, 19, 59, 32, 25, 2, 39, 46, 7, 28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56, 0, 17, 22, 30, 24, 53, 64, 67, 60, 55, 6, 1, 3, 8, 15, 23, 66, 49, 63, 14, 58], 'cur_cost': 3579.0}, {'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2766.0}, {'tour': [0, 13, 9, 31, 40, 15, 12, 8, 1, 62, 30, 7, 27, 42, 19, 56, 14, 65, 67, 25, 21, 20, 59, 64, 4, 37, 69, 46, 68, 16, 57, 5, 41, 39, 3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10, 60, 6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58, 2, 47, 18, 44, 35], 'cur_cost': 3413.0}]
2025-08-05 10:29:12,952 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:12,952 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 356, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 356, 'cache_hits': 0, 'similarity_calculations': 1834, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,953 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([29, 18, 33, 42, 45, 64, 38, 52,  8, 50, 49, 21, 59, 34, 43, 47, 53,
        2,  5,  6, 54,  3,  0, 17, 31, 66, 37,  1, 58, 51, 15, 69, 68, 26,
        9, 11, 44, 56,  4, 36, 10, 57, 27, 35, 41, 39,  7, 67, 62, 30, 55,
       16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48,
       14, 32], dtype=int64), 'cur_cost': 3449.0, 'intermediate_solutions': [{'tour': array([43, 50, 42, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 43, 50, 42, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([57, 22, 43, 50, 42, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([42, 22, 43, 50, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([42, 57, 22, 43, 50, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,953 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3449.00)
2025-08-05 10:29:12,954 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:12,954 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:12,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,957 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 872.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,960 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [40, 3, 48, 20, 4, 52, 21, 25, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 37, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3348.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 46, 69, 67, 64, 55, 14, 51, 7, 17, 65, 26, 56, 9, 42, 59, 12, 44, 16, 28, 30, 37, 21, 52, 4, 20, 48, 3, 40, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 35, 18, 6, 63, 2, 47, 27, 58, 8, 45, 15, 49, 53, 0, 38], 'cur_cost': 3176.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,960 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 872.00)
2025-08-05 10:29:12,961 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:12,961 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:12,961 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,964 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:12,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1029.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,965 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 21, 40, 41, 11, 5, 29, 60, 1, 25, 27, 22, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 1, 60, 29, 5, 11, 41, 40, 22, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2594.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 69, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7], 'cur_cost': 2616.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,965 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1029.00)
2025-08-05 10:29:12,965 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:12,965 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,965 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,966 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3712.0
2025-08-05 10:29:12,977 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:12,977 - ExploitationExpert - INFO - res_population_costs: [681, 684, 687, 688, 688, 688, 689.0, 689, 676, 675]
2025-08-05 10:29:12,977 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-08-05 10:29:12,981 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:12,981 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 931.0}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3456.0}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 963.0}, {'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2458.0}, {'tour': array([29, 18, 33, 42, 45, 64, 38, 52,  8, 50, 49, 21, 59, 34, 43, 47, 53,
        2,  5,  6, 54,  3,  0, 17, 31, 66, 37,  1, 58, 51, 15, 69, 68, 26,
        9, 11, 44, 56,  4, 36, 10, 57, 27, 35, 41, 39,  7, 67, 62, 30, 55,
       16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48,
       14, 32], dtype=int64), 'cur_cost': 3449.0}, {'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 872.0}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1029.0}, {'tour': array([17, 62, 43, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27], dtype=int64), 'cur_cost': 3712.0}, {'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2766.0}, {'tour': [0, 13, 9, 31, 40, 15, 12, 8, 1, 62, 30, 7, 27, 42, 19, 56, 14, 65, 67, 25, 21, 20, 59, 64, 4, 37, 69, 46, 68, 16, 57, 5, 41, 39, 3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10, 60, 6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58, 2, 47, 18, 44, 35], 'cur_cost': 3413.0}]
2025-08-05 10:29:12,983 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:12,983 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 357, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 357, 'cache_hits': 0, 'similarity_calculations': 1839, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:12,984 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([17, 62, 43, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27], dtype=int64), 'cur_cost': 3712.0, 'intermediate_solutions': [{'tour': array([50, 43, 38, 68, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3584.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 50, 43, 38, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([44, 68, 50, 43, 38, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 68, 50, 43, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 44, 68, 50, 43, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:12,984 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 3712.00)
2025-08-05 10:29:12,984 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:12,984 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:12,984 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:12,986 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:12,986 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:12,987 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3584.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:12,987 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 11, 18, 9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49, 30, 67, 6, 69, 3, 17, 32, 46, 35, 15, 38, 19, 7, 27, 25, 61, 1, 41, 50, 39, 42, 4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60, 8, 26, 45, 2, 51, 34, 63, 22, 43, 14, 65, 0, 13, 23, 53, 16, 44, 47, 56], 'cur_cost': 3584.0, 'intermediate_solutions': [{'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 41, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 49, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2823.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 51, 35, 63, 22, 46, 57, 16, 23, 69, 62, 54, 48, 2, 26, 41, 24, 66, 0, 15, 64, 8, 39, 47, 59, 27, 45, 60, 11, 37, 50, 36, 53, 49, 33, 19, 25, 14, 68, 6, 21, 5, 55, 38, 13, 7, 3, 9, 56, 52, 31, 34, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 4, 58, 17, 65, 28, 27, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:12,987 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 3584.00)
2025-08-05 10:29:12,987 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:12,987 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:12,988 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:12,988 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3552.0
2025-08-05 10:29:12,999 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:12,999 - ExploitationExpert - INFO - res_population_costs: [681, 684, 687, 688, 688, 688, 689.0, 689, 676, 675]
2025-08-05 10:29:12,999 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-08-05 10:29:13,003 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,003 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 931.0}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3456.0}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 963.0}, {'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2458.0}, {'tour': array([29, 18, 33, 42, 45, 64, 38, 52,  8, 50, 49, 21, 59, 34, 43, 47, 53,
        2,  5,  6, 54,  3,  0, 17, 31, 66, 37,  1, 58, 51, 15, 69, 68, 26,
        9, 11, 44, 56,  4, 36, 10, 57, 27, 35, 41, 39,  7, 67, 62, 30, 55,
       16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48,
       14, 32], dtype=int64), 'cur_cost': 3449.0}, {'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 872.0}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1029.0}, {'tour': array([17, 62, 43, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27], dtype=int64), 'cur_cost': 3712.0}, {'tour': [5, 11, 18, 9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49, 30, 67, 6, 69, 3, 17, 32, 46, 35, 15, 38, 19, 7, 27, 25, 61, 1, 41, 50, 39, 42, 4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60, 8, 26, 45, 2, 51, 34, 63, 22, 43, 14, 65, 0, 13, 23, 53, 16, 44, 47, 56], 'cur_cost': 3584.0}, {'tour': array([46, 16, 19, 56, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67], dtype=int64), 'cur_cost': 3552.0}]
2025-08-05 10:29:13,005 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,005 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 358, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 358, 'cache_hits': 0, 'similarity_calculations': 1845, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,006 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([46, 16, 19, 56, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67], dtype=int64), 'cur_cost': 3552.0, 'intermediate_solutions': [{'tour': array([ 9, 13,  0, 31, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  9, 13,  0, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 31,  9, 13,  0, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 31,  9, 13, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 40, 31,  9, 13, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,006 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3552.00)
2025-08-05 10:29:13,006 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:13,006 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:13,008 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 931.0, 'intermediate_solutions': [{'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 45, 67, 34, 69, 18, 30, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 13, 6, 54, 60, 52], 'cur_cost': 2997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 17, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 13, 62, 31, 23, 36, 25, 26, 22, 63, 49, 42, 53, 6, 54, 60, 52], 'cur_cost': 3221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [68, 3, 10, 20, 40, 29, 58, 37, 12, 28, 16, 44, 33, 7, 27, 38, 9, 4, 2, 65, 21, 1, 56, 14, 59, 64, 30, 67, 34, 69, 18, 45, 46, 15, 0, 48, 19, 35, 39, 66, 61, 32, 51, 43, 57, 41, 47, 50, 11, 5, 24, 8, 55, 53, 42, 49, 63, 22, 26, 25, 36, 23, 31, 62, 17, 13, 6, 54, 60, 52], 'cur_cost': 3104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3456.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 33, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 23, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1225.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10, 60, 39, 26, 45, 24, 38, 44, 8], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 51, 46, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 15, 22, 35, 63, 10, 60, 39, 44, 38, 24, 45, 26, 8], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 963.0, 'intermediate_solutions': [{'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 13, 60, 12, 9, 15, 23, 22, 0, 14], 'cur_cost': 3040.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 18, 20, 16, 10, 40, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 7, 57, 30, 51, 39, 63, 46, 36, 64, 49, 50, 24, 55, 44, 47, 56, 53, 32, 3, 11, 59, 61, 66, 17, 35, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 18, 20, 16, 10, 58, 21, 62, 8, 37, 68, 33, 25, 28, 69, 19, 4, 26, 43, 1, 6, 31, 2, 65, 27, 41, 48, 54, 34, 52, 29, 45, 67, 42, 40, 7, 57, 30, 35, 17, 66, 61, 59, 11, 3, 32, 53, 56, 47, 44, 55, 24, 50, 49, 64, 36, 46, 63, 39, 51, 38, 0, 60, 12, 9, 15, 23, 22, 13, 14], 'cur_cost': 3169.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2458.0, 'intermediate_solutions': [{'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 7, 51, 20, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 19, 27, 28, 0], 'cur_cost': 2419.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 13, 48, 43, 31, 41, 21, 20, 51, 7, 25, 67, 16, 5, 49, 11, 42, 59, 52, 9, 62, 3, 23, 30, 35, 55, 46, 64, 4, 37, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 13, 48, 43, 31, 41, 21, 35, 30, 23, 3, 62, 9, 52, 59, 42, 11, 49, 5, 16, 67, 25, 37, 7, 51, 20, 55, 46, 64, 4, 36, 17, 58, 56, 68, 34, 2, 18, 57, 22, 6, 12, 14, 54, 8, 29, 33, 50, 10, 63, 69, 65, 15, 61, 47, 66, 44, 60, 45, 24, 39, 32, 38, 26, 53, 1, 27, 19, 28, 0], 'cur_cost': 2495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 18, 33, 42, 45, 64, 38, 52,  8, 50, 49, 21, 59, 34, 43, 47, 53,
        2,  5,  6, 54,  3,  0, 17, 31, 66, 37,  1, 58, 51, 15, 69, 68, 26,
        9, 11, 44, 56,  4, 36, 10, 57, 27, 35, 41, 39,  7, 67, 62, 30, 55,
       16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48,
       14, 32], dtype=int64), 'cur_cost': 3449.0, 'intermediate_solutions': [{'tour': array([43, 50, 42, 22, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 43, 50, 42, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3303.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([57, 22, 43, 50, 42, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([42, 22, 43, 50, 57, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([42, 57, 22, 43, 50, 68, 65, 44, 55, 20,  3, 39, 15, 33, 29,  6, 30,
       14, 31, 40, 49,  4, 25, 18,  1, 62, 67, 47, 46, 38, 41, 45, 53, 10,
       16, 61, 48, 54, 63, 24, 12, 17, 52, 21, 34, 58,  9, 56, 37,  7, 13,
        8, 59, 69, 35, 23, 26,  2, 19,  0, 66, 51, 64, 36,  5, 11, 60, 32,
       27, 28]), 'cur_cost': 3357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [40, 3, 48, 20, 4, 52, 21, 25, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 37, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3348.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 46, 69, 67, 64, 55, 14, 51, 7, 17, 65, 26, 56, 9, 42, 59, 12, 44, 16, 28, 30, 37, 21, 52, 4, 20, 48, 3, 40, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 18, 6, 63, 2, 47, 27, 58, 8, 35, 45, 15, 49, 53, 0, 38], 'cur_cost': 3384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 3, 48, 20, 4, 52, 21, 37, 30, 28, 16, 44, 12, 59, 42, 9, 56, 26, 65, 17, 7, 51, 14, 55, 64, 67, 69, 46, 68, 34, 57, 5, 41, 39, 13, 19, 29, 54, 43, 33, 61, 10, 11, 24, 50, 32, 66, 36, 60, 22, 25, 1, 23, 31, 62, 35, 18, 6, 63, 2, 47, 27, 58, 8, 45, 15, 49, 53, 0, 38], 'cur_cost': 3176.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 21, 40, 41, 11, 5, 29, 60, 1, 25, 27, 22, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 1, 60, 29, 5, 11, 41, 40, 22, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7, 69], 'cur_cost': 2594.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 16, 49, 64, 63, 28, 15, 55, 33, 17, 3, 9, 8, 32, 4, 52, 20, 31, 56, 35, 59, 22, 40, 41, 11, 5, 29, 60, 1, 25, 27, 21, 0, 68, 62, 36, 51, 46, 50, 37, 30, 58, 18, 14, 48, 2, 19, 26, 39, 42, 24, 45, 69, 67, 43, 6, 23, 12, 57, 34, 13, 65, 54, 38, 61, 66, 10, 47, 44, 7], 'cur_cost': 2616.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 62, 43, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27], dtype=int64), 'cur_cost': 3712.0, 'intermediate_solutions': [{'tour': array([50, 43, 38, 68, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3584.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 50, 43, 38, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([44, 68, 50, 43, 38, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 68, 50, 43, 44, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38, 44, 68, 50, 43, 21, 35, 27,  9, 48, 12, 13, 37, 31, 45, 33, 62,
       69, 34,  4, 52, 65, 40, 42, 16,  5, 19, 59, 32, 25,  2, 39, 46,  7,
       28, 20, 51, 41, 18, 57, 61, 36, 11, 26, 47, 10, 54, 29, 56,  0, 17,
       22, 30, 24, 53, 64, 67, 60, 55,  6,  1,  3,  8, 15, 23, 66, 49, 63,
       14, 58]), 'cur_cost': 3514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 11, 18, 9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49, 30, 67, 6, 69, 3, 17, 32, 46, 35, 15, 38, 19, 7, 27, 25, 61, 1, 41, 50, 39, 42, 4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60, 8, 26, 45, 2, 51, 34, 63, 22, 43, 14, 65, 0, 13, 23, 53, 16, 44, 47, 56], 'cur_cost': 3584.0, 'intermediate_solutions': [{'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 41, 53, 36, 50, 37, 11, 60, 45, 27, 59, 47, 39, 8, 64, 15, 0, 66, 24, 49, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2823.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 4, 58, 17, 65, 28, 40, 20, 42, 43, 1, 51, 35, 63, 22, 46, 57, 16, 23, 69, 62, 54, 48, 2, 26, 41, 24, 66, 0, 15, 64, 8, 39, 47, 59, 27, 45, 60, 11, 37, 50, 36, 53, 49, 33, 19, 25, 14, 68, 6, 21, 5, 55, 38, 13, 7, 3, 9, 56, 52, 31, 34, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 4, 58, 17, 65, 28, 27, 40, 20, 42, 43, 1, 34, 31, 52, 56, 9, 3, 7, 13, 38, 55, 5, 21, 6, 68, 14, 25, 19, 33, 49, 53, 36, 50, 37, 11, 60, 45, 59, 47, 39, 8, 64, 15, 0, 66, 24, 41, 26, 2, 48, 54, 62, 69, 23, 16, 57, 46, 22, 63, 35, 51, 10, 32, 67, 29, 44, 61, 18, 30], 'cur_cost': 2755.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 16, 19, 56, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67], dtype=int64), 'cur_cost': 3552.0, 'intermediate_solutions': [{'tour': array([ 9, 13,  0, 31, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  9, 13,  0, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 31,  9, 13,  0, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3427.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0, 31,  9, 13, 40, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 40, 31,  9, 13, 15, 12,  8,  1, 62, 30,  7, 27, 42, 19, 56, 14,
       65, 67, 25, 21, 20, 59, 64,  4, 37, 69, 46, 68, 16, 57,  5, 41, 39,
        3, 32, 51, 33, 66, 61, 50, 11, 24, 23, 55, 36, 49, 34, 26, 63, 10,
       60,  6, 54, 38, 28, 52, 48, 29, 53, 43, 22, 45, 17, 58,  2, 47, 18,
       44, 35]), 'cur_cost': 3414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:13,009 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:13,009 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,012 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=872.000, 多样性=0.970
2025-08-05 10:29:13,012 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:13,012 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:13,012 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:13,013 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08771864737879666, 'best_improvement': 0.1383399209486166}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002297341647521938}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0664416307666514, 'recent_improvements': [0.03018840543539954, 0.03818768887803537, -0.10269485609790324], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 675, 'new_best_cost': 675, 'quality_improvement': 0.0, 'old_diversity': 0.7803174603174603, 'new_diversity': 0.7803174603174603, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:13,015 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:13,015 - __main__ - INFO - st70 开始进化第 4 代
2025-08-05 10:29:13,016 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:13,016 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,017 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=872.000, 多样性=0.970
2025-08-05 10:29:13,017 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:13,021 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.970
2025-08-05 10:29:13,022 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:13,027 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.780
2025-08-05 10:29:13,029 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:13,030 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:13,030 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:29:13,030 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:29:13,113 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -317.480, 聚类评分: 0.000, 覆盖率: 0.157, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:13,113 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:13,114 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:13,114 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 10:29:13,121 - visualization.landscape_visualizer - INFO - 插值约束: 203 个点被约束到最小值 675.00
2025-08-05 10:29:13,123 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.6%, 梯度: 120.16 → 109.88
2025-08-05 10:29:13,224 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_139_20250805_102913.html
2025-08-05 10:29:13,283 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_139_20250805_102913.html
2025-08-05 10:29:13,283 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 139
2025-08-05 10:29:13,283 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:13,283 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2532秒
2025-08-05 10:29:13,283 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -317.47999999999996, 'local_optima_density': 0.15, 'gradient_variance': 929009.9616000003, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1573, 'fitness_entropy': 0.6680801271869059, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -317.480)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.157)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360953.1138508, 'performance_metrics': {}}}
2025-08-05 10:29:13,284 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:13,284 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:13,284 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:13,284 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:13,284 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,284 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:13,285 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,285 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:13,285 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:13,286 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:13,286 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,288 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,288 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,288 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,288 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,288 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,289 - ExplorationExpert - INFO - 探索路径生成完成，成本: 904.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,289 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 33, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 29, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 46, 57, 36, 63, 10, 15, 22, 35], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 40, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,289 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 904.00)
2025-08-05 10:29:13,289 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:13,289 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:13,290 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,295 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:13,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,296 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2389.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,296 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2389.0, 'intermediate_solutions': [{'tour': [5, 50, 43, 55, 31, 60, 58, 37, 9, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3467.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 68, 53, 32, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3393.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 42, 40, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3474.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,297 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 2389.00)
2025-08-05 10:29:13,297 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:13,297 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:13,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,299 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,300 - ExplorationExpert - INFO - 探索路径生成完成，成本: 930.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,300 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 45, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 56, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 53, 32, 51, 59, 11, 33, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 33, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,301 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 930.00)
2025-08-05 10:29:13,301 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:13,301 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:13,301 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,303 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 915.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,304 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 61, 51, 33, 8, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2486.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 6, 23, 48, 13, 18, 31, 27, 17, 26, 11, 24, 67, 55, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2499.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 65, 4, 52, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 30, 64, 60, 44], 'cur_cost': 2458.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,304 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 915.00)
2025-08-05 10:29:13,304 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:13,304 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:13,304 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,307 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,308 - ExplorationExpert - INFO - 探索路径生成完成，成本: 953.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,308 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 26, 9, 11, 44, 56, 4, 36, 10, 57, 14, 35, 41, 39, 7, 67, 62, 30, 55, 16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48, 27, 32], 'cur_cost': 3352.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 26, 9, 11, 44, 56, 4, 36, 10, 57, 27, 25, 63, 46, 19, 40, 20, 61, 16, 55, 30, 62, 67, 7, 39, 41, 35, 65, 22, 23, 13, 60, 24, 12, 28, 48, 14, 32], 'cur_cost': 3355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 40, 26, 9, 11, 44, 56, 4, 36, 10, 57, 27, 35, 41, 39, 7, 67, 62, 30, 55, 16, 61, 20, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48, 14, 32], 'cur_cost': 3440.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,308 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 953.00)
2025-08-05 10:29:13,308 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:13,309 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:13,309 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,310 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,310 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,311 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1010.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,311 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 32, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 51, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 18, 54, 48, 25, 7, 27, 13, 19, 29, 43, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 46, 15, 35, 28, 69, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 22, 3, 24, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,312 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1010.00)
2025-08-05 10:29:13,312 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:13,312 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:13,312 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,313 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:13,313 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,314 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,314 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,314 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,314 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3121.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,314 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3121.0, 'intermediate_solutions': [{'tour': [0, 9, 10, 12, 28, 69, 30, 33, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 68, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 24, 44, 38, 61, 32, 53, 47, 66, 64, 55, 50, 34, 35, 22, 15, 46, 36, 57, 49, 52, 4, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 65, 58, 62, 21, 37, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 57, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,315 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 3121.00)
2025-08-05 10:29:13,315 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:13,315 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,315 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,315 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3484.0
2025-08-05 10:29:13,328 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,328 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,328 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,332 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,332 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 904.0}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2389.0}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 930.0}, {'tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 915.0}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 953.0}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1010.0}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3121.0}, {'tour': array([39, 64, 44, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29], dtype=int64), 'cur_cost': 3484.0}, {'tour': [5, 11, 18, 9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49, 30, 67, 6, 69, 3, 17, 32, 46, 35, 15, 38, 19, 7, 27, 25, 61, 1, 41, 50, 39, 42, 4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60, 8, 26, 45, 2, 51, 34, 63, 22, 43, 14, 65, 0, 13, 23, 53, 16, 44, 47, 56], 'cur_cost': 3584.0}, {'tour': [46, 16, 19, 56, 59, 2, 24, 1, 65, 68, 53, 5, 43, 22, 31, 18, 37, 21, 39, 49, 69, 57, 35, 52, 11, 63, 4, 32, 29, 8, 36, 30, 58, 13, 44, 48, 47, 51, 42, 23, 66, 50, 25, 3, 10, 9, 34, 26, 15, 0, 41, 54, 62, 45, 38, 20, 28, 61, 12, 6, 14, 7, 27, 17, 60, 40, 64, 33, 55, 67], 'cur_cost': 3552.0}]
2025-08-05 10:29:13,333 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,333 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 359, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 359, 'cache_hits': 0, 'similarity_calculations': 1852, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,335 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([39, 64, 44, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29], dtype=int64), 'cur_cost': 3484.0, 'intermediate_solutions': [{'tour': array([43, 62, 17, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([66, 43, 62, 17,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 66, 43, 62, 17, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 66, 43, 62,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3749.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17,  9, 66, 43, 62, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3752.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,335 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 3484.00)
2025-08-05 10:29:13,335 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:13,335 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,336 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,336 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3616.0
2025-08-05 10:29:13,349 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,349 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,350 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,353 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,353 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 904.0}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2389.0}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 930.0}, {'tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 915.0}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 953.0}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1010.0}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3121.0}, {'tour': array([39, 64, 44, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29], dtype=int64), 'cur_cost': 3484.0}, {'tour': array([21, 43, 41,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51], dtype=int64), 'cur_cost': 3616.0}, {'tour': [46, 16, 19, 56, 59, 2, 24, 1, 65, 68, 53, 5, 43, 22, 31, 18, 37, 21, 39, 49, 69, 57, 35, 52, 11, 63, 4, 32, 29, 8, 36, 30, 58, 13, 44, 48, 47, 51, 42, 23, 66, 50, 25, 3, 10, 9, 34, 26, 15, 0, 41, 54, 62, 45, 38, 20, 28, 61, 12, 6, 14, 7, 27, 17, 60, 40, 64, 33, 55, 67], 'cur_cost': 3552.0}]
2025-08-05 10:29:13,355 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,355 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 360, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 360, 'cache_hits': 0, 'similarity_calculations': 1860, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,356 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([21, 43, 41,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51], dtype=int64), 'cur_cost': 3616.0, 'intermediate_solutions': [{'tour': array([18, 11,  5,  9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 18, 11,  5, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31,  9, 18, 11,  5, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  9, 18, 11, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 31,  9, 18, 11, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3525.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,356 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 3616.00)
2025-08-05 10:29:13,356 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:13,356 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,356 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,357 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3539.0
2025-08-05 10:29:13,368 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,368 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,368 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,372 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,372 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 904.0}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2389.0}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 930.0}, {'tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 915.0}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 953.0}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1010.0}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3121.0}, {'tour': array([39, 64, 44, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29], dtype=int64), 'cur_cost': 3484.0}, {'tour': array([21, 43, 41,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51], dtype=int64), 'cur_cost': 3616.0}, {'tour': array([34, 45, 22, 65, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68], dtype=int64), 'cur_cost': 3539.0}]
2025-08-05 10:29:13,374 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,374 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 361, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 361, 'cache_hits': 0, 'similarity_calculations': 1869, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,375 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([34, 45, 22, 65, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68], dtype=int64), 'cur_cost': 3539.0, 'intermediate_solutions': [{'tour': array([19, 16, 46, 56, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 19, 16, 46, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([59, 56, 19, 16, 46,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 56, 19, 16, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3506.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 59, 56, 19, 16,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,375 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3539.00)
2025-08-05 10:29:13,375 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:13,375 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:13,380 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 33, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 29, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 1095.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 46, 57, 36, 63, 10, 15, 22, 35], 'cur_cost': 988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 18, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 3, 17, 41, 5, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 40, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 51, 59, 10, 63, 36, 57, 46, 15, 22, 35], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2389.0, 'intermediate_solutions': [{'tour': [5, 50, 43, 55, 31, 60, 58, 37, 9, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3467.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 40, 42, 38, 19, 17, 68, 53, 32, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3393.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 50, 43, 9, 31, 60, 58, 37, 55, 45, 26, 62, 33, 12, 29, 13, 27, 56, 25, 48, 67, 4, 1, 51, 7, 41, 46, 42, 40, 38, 19, 17, 32, 53, 68, 47, 66, 30, 64, 22, 49, 61, 52, 21, 65, 2, 54, 36, 10, 44, 69, 24, 28, 57, 34, 18, 14, 23, 39, 35, 63, 59, 8, 15, 20, 11, 3, 16, 0, 6], 'cur_cost': 3474.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 45, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 56, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1226.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 53, 32, 51, 59, 11, 33, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 10, 47, 66, 55, 64, 50, 49, 9, 4, 33, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 11, 59, 51, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 57, 36, 46, 22, 35, 63], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 61, 51, 33, 8, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2486.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 65, 4, 52, 30, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 6, 23, 48, 13, 18, 31, 27, 17, 26, 11, 24, 67, 55, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 64, 60, 44], 'cur_cost': 2499.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 65, 4, 52, 36, 12, 68, 2, 54, 5, 8, 51, 33, 61, 50, 57, 40, 49, 62, 25, 14, 15, 63, 0, 32, 20, 53, 42, 41, 7, 19, 45, 38, 9, 1, 3, 29, 16, 66, 10, 59, 47, 39, 55, 67, 24, 11, 26, 17, 27, 31, 18, 13, 48, 23, 6, 43, 21, 28, 58, 69, 34, 56, 35, 37, 46, 30, 64, 60, 44], 'cur_cost': 2458.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 26, 9, 11, 44, 56, 4, 36, 10, 57, 14, 35, 41, 39, 7, 67, 62, 30, 55, 16, 61, 20, 40, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48, 27, 32], 'cur_cost': 3352.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 26, 9, 11, 44, 56, 4, 36, 10, 57, 27, 25, 63, 46, 19, 40, 20, 61, 16, 55, 30, 62, 67, 7, 39, 41, 35, 65, 22, 23, 13, 60, 24, 12, 28, 48, 14, 32], 'cur_cost': 3355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 18, 33, 42, 45, 64, 38, 52, 8, 50, 49, 21, 59, 34, 43, 47, 53, 2, 5, 6, 54, 3, 0, 17, 31, 66, 37, 1, 58, 51, 15, 69, 68, 40, 26, 9, 11, 44, 56, 4, 36, 10, 57, 27, 35, 41, 39, 7, 67, 62, 30, 55, 16, 61, 20, 19, 46, 63, 25, 65, 22, 23, 13, 60, 24, 12, 28, 48, 14, 32], 'cur_cost': 3440.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 32, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 51, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 22, 3, 17, 41, 5, 40, 42, 16, 20, 18, 54, 48, 25, 7, 27, 13, 19, 29, 43, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 46, 15, 35, 28, 69, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 22, 3, 24, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 15, 46, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 31, 2, 23, 14, 56, 34, 63], 'cur_cost': 996.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3121.0, 'intermediate_solutions': [{'tour': [0, 9, 10, 12, 28, 69, 30, 33, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 68, 11, 59, 51, 4, 52, 49, 57, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1275.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 24, 44, 38, 61, 32, 53, 47, 66, 64, 55, 50, 34, 35, 22, 15, 46, 36, 57, 49, 52, 4, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 65, 58, 62, 21, 37, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 10, 12, 28, 69, 30, 68, 37, 21, 62, 58, 65, 56, 14, 23, 1, 6, 31, 2, 57, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 49, 36, 46, 15, 22, 35, 34, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 64, 44, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29], dtype=int64), 'cur_cost': 3484.0, 'intermediate_solutions': [{'tour': array([43, 62, 17, 66,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([66, 43, 62, 17,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3776.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 66, 43, 62, 17, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 66, 43, 62,  9, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3749.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17,  9, 66, 43, 62, 61, 38, 69, 41, 46, 15, 58, 28, 20, 56, 35,  4,
       32, 33, 68, 24, 30, 60, 10,  6,  2,  0, 54, 42,  8, 53, 36,  5, 34,
       11, 47, 67, 65, 25, 45, 49, 26, 64, 16, 22, 59,  3, 63, 19, 31, 14,
       12,  7, 13, 55, 21,  1, 29, 40, 44, 37, 57, 51, 23, 18, 50, 52, 39,
       48, 27]), 'cur_cost': 3752.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 43, 41,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51], dtype=int64), 'cur_cost': 3616.0, 'intermediate_solutions': [{'tour': array([18, 11,  5,  9, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 18, 11,  5, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31,  9, 18, 11,  5, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  9, 18, 11, 31, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 31,  9, 18, 11, 66, 58, 37, 55, 54, 20, 62, 33, 12, 29, 40, 49,
       30, 67,  6, 69,  3, 17, 32, 46, 35, 15, 38, 19,  7, 27, 25, 61,  1,
       41, 50, 39, 42,  4, 52, 24, 21, 59, 36, 10, 68, 64, 28, 57, 48, 60,
        8, 26, 45,  2, 51, 34, 63, 22, 43, 14, 65,  0, 13, 23, 53, 16, 44,
       47, 56]), 'cur_cost': 3525.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 45, 22, 65, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68], dtype=int64), 'cur_cost': 3539.0, 'intermediate_solutions': [{'tour': array([19, 16, 46, 56, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 19, 16, 46, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([59, 56, 19, 16, 46,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 56, 19, 16, 59,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3506.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 59, 56, 19, 16,  2, 24,  1, 65, 68, 53,  5, 43, 22, 31, 18, 37,
       21, 39, 49, 69, 57, 35, 52, 11, 63,  4, 32, 29,  8, 36, 30, 58, 13,
       44, 48, 47, 51, 42, 23, 66, 50, 25,  3, 10,  9, 34, 26, 15,  0, 41,
       54, 62, 45, 38, 20, 28, 61, 12,  6, 14,  7, 27, 17, 60, 40, 64, 33,
       55, 67]), 'cur_cost': 3514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:13,381 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:13,381 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,388 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=904.000, 多样性=0.964
2025-08-05 10:29:13,388 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:13,388 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:13,388 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:13,391 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04005791189905079, 'best_improvement': -0.03669724770642202}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005566470203012447}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.024765479250380643, 'recent_improvements': [0.03818768887803537, -0.10269485609790324, 0.08771864737879666], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 675, 'new_best_cost': 675, 'quality_improvement': 0.0, 'old_diversity': 0.7803174603174603, 'new_diversity': 0.7803174603174603, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:13,394 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:13,394 - __main__ - INFO - st70 开始进化第 5 代
2025-08-05 10:29:13,394 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:13,395 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,395 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=904.000, 多样性=0.964
2025-08-05 10:29:13,396 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:13,399 - PathExpert - INFO - 路径结构分析完成: 公共边数量=6, 路径相似性=0.964
2025-08-05 10:29:13,400 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:13,404 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.780
2025-08-05 10:29:13,407 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:13,408 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:13,408 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:29:13,408 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:29:13,517 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -332.860, 聚类评分: 0.000, 覆盖率: 0.158, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:13,518 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:13,519 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:13,519 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 10:29:13,526 - visualization.landscape_visualizer - INFO - 插值约束: 314 个点被约束到最小值 675.00
2025-08-05 10:29:13,529 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=11.1%, 梯度: 129.31 → 115.01
2025-08-05 10:29:13,634 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_140_20250805_102913.html
2025-08-05 10:29:13,708 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_140_20250805_102913.html
2025-08-05 10:29:13,708 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 140
2025-08-05 10:29:13,708 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:13,708 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3015秒
2025-08-05 10:29:13,709 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -332.86, 'local_optima_density': 0.15, 'gradient_variance': 740835.1484, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1581, 'fitness_entropy': 0.611145227736996, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -332.860)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.158)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360953.5186262, 'performance_metrics': {}}}
2025-08-05 10:29:13,709 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:13,709 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:13,709 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:13,709 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:13,710 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,710 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:13,710 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,710 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:13,710 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:13,710 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:29:13,710 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:13,711 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:13,711 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:13,711 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:13,711 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:13,711 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,717 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 10:29:13,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,718 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,718 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,718 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,719 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2440.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,719 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 13, 2, 1, 37, 5, 14, 3, 33, 26, 41, 19, 27, 23, 17, 54, 25, 51, 61, 32, 55, 49, 20, 50, 16, 7, 45, 31, 21, 69, 68, 36, 11, 59, 10, 4, 42, 40, 52, 6, 56, 34, 12, 35, 30, 22, 15, 46, 62, 57, 66, 39, 24, 53, 38, 8, 64, 47, 44, 9, 67, 60, 65, 58, 18, 43, 0, 28, 63, 48], 'cur_cost': 2440.0, 'intermediate_solutions': [{'tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 53, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 39, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 9, 20, 33, 11, 59, 51, 57, 49, 50, 64, 55, 66, 47, 61, 53, 32, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38, 60, 39, 8, 16, 42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 65, 52, 4, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 9, 20, 33, 11, 59, 19, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,719 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 2440.00)
2025-08-05 10:29:13,719 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:13,719 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:13,720 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,722 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,722 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,722 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,723 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,723 - ExplorationExpert - INFO - 探索路径生成完成，成本: 892.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,723 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 5, 18, 6, 1, 3, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 892.0, 'intermediate_solutions': [{'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 4, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 11, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2397.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 42, 67, 26, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2403.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 11, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,723 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 892.00)
2025-08-05 10:29:13,723 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:13,724 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:13,724 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,726 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,727 - ExplorationExpert - INFO - 探索路径生成完成，成本: 959.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,727 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 11, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 59, 51, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 34, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 27, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 11, 33, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 55, 64, 10, 47, 66, 53, 50, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,727 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 959.00)
2025-08-05 10:29:13,727 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:13,727 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:13,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,730 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,730 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 925.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,731 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 8, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 3, 17, 41, 5, 40, 42, 16, 2, 31, 23, 14, 56, 34, 63], 'cur_cost': 925.0, 'intermediate_solutions': [{'tour': [0, 13, 18, 2, 31, 6, 36, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 1, 46, 15, 22, 35, 63], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 44, 38, 60, 39, 8, 67, 43, 29, 19, 54, 48, 27, 7, 25, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 18, 13, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 23], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,731 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 925.00)
2025-08-05 10:29:13,731 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:13,731 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:13,731 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,733 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,733 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,734 - ExplorationExpert - INFO - 探索路径生成完成，成本: 902.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,734 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 22, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 65, 52, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 35, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 10, 63], 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 49, 20, 33, 11, 59, 51, 9, 16, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 61, 32, 53, 66, 47, 10, 64, 55, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 39, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 60, 8, 26, 45, 63], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,735 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 902.00)
2025-08-05 10:29:13,735 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:13,735 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:13,735 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,737 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 10:29:13,737 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,737 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,737 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,738 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,738 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1068.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,738 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 13, 9, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 35, 63], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [0, 3, 8, 15, 46, 22, 1, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 35, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 36, 57, 49, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 18, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 5, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,738 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1068.00)
2025-08-05 10:29:13,739 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:13,739 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:13,739 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:13,740 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 10:29:13,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:13,741 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3082.0, 路径长度: 70, 收集中间解: 3
2025-08-05 10:29:13,741 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [29, 12, 2, 3, 17, 5, 14, 51, 42, 16, 41, 33, 20, 62, 61, 59, 25, 9, 64, 32, 65, 21, 46, 50, 37, 68, 45, 58, 28, 49, 30, 56, 66, 31, 10, 4, 27, 40, 48, 54, 60, 19, 7, 35, 43, 22, 15, 57, 38, 44, 24, 39, 26, 18, 53, 47, 55, 0, 1, 6, 67, 8, 11, 36, 23, 63, 52, 13, 34, 69], 'cur_cost': 3082.0, 'intermediate_solutions': [{'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 20, 12, 62, 51, 29, 56, 22, 42, 33, 48, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 59, 63, 9, 14, 54, 2, 11, 34, 50, 30, 55, 66, 35, 47, 10, 64, 13, 19, 49, 0, 36, 15, 45, 8, 67, 38, 60, 39, 69, 57, 6, 46, 16, 23, 17, 3, 18, 26, 28, 25, 27, 7, 24, 31, 20, 33, 42, 22, 56, 29, 51, 62, 12, 48, 68, 1], 'cur_cost': 3155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 31, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:13,742 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 3082.00)
2025-08-05 10:29:13,742 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:13,742 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,742 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,743 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3889.0
2025-08-05 10:29:13,754 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,754 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,755 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,760 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,760 - ExploitationExpert - INFO - populations: [{'tour': [29, 13, 2, 1, 37, 5, 14, 3, 33, 26, 41, 19, 27, 23, 17, 54, 25, 51, 61, 32, 55, 49, 20, 50, 16, 7, 45, 31, 21, 69, 68, 36, 11, 59, 10, 4, 42, 40, 52, 6, 56, 34, 12, 35, 30, 22, 15, 46, 62, 57, 66, 39, 24, 53, 38, 8, 64, 47, 44, 9, 67, 60, 65, 58, 18, 43, 0, 28, 63, 48], 'cur_cost': 2440.0}, {'tour': [0, 14, 5, 18, 6, 1, 3, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 892.0}, {'tour': [0, 1, 11, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 59, 51, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 959.0}, {'tour': [0, 12, 8, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 3, 17, 41, 5, 40, 42, 16, 2, 31, 23, 14, 56, 34, 63], 'cur_cost': 925.0}, {'tour': [0, 22, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 65, 52, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 35, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 10, 63], 'cur_cost': 902.0}, {'tour': [0, 13, 9, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 35, 63], 'cur_cost': 1068.0}, {'tour': [29, 12, 2, 3, 17, 5, 14, 51, 42, 16, 41, 33, 20, 62, 61, 59, 25, 9, 64, 32, 65, 21, 46, 50, 37, 68, 45, 58, 28, 49, 30, 56, 66, 31, 10, 4, 27, 40, 48, 54, 60, 19, 7, 35, 43, 22, 15, 57, 38, 44, 24, 39, 26, 18, 53, 47, 55, 0, 1, 6, 67, 8, 11, 36, 23, 63, 52, 13, 34, 69], 'cur_cost': 3082.0}, {'tour': array([26,  4, 23, 59, 32, 58, 43, 22, 35,  2,  8,  7, 38, 41, 25, 53,  3,
       21, 12,  5, 48, 46, 30, 29, 36, 44, 39, 11, 52, 28, 55, 37, 16, 51,
        6, 57, 13, 24, 27, 17, 50,  1, 56, 60, 65,  0, 18, 63, 40, 47, 62,
       42, 10, 34, 67, 68, 69, 45, 49, 19, 54, 64,  9, 14, 31, 20, 61, 15,
       66, 33], dtype=int64), 'cur_cost': 3889.0}, {'tour': [21, 43, 41, 5, 67, 2, 9, 27, 10, 0, 12, 40, 60, 55, 31, 56, 50, 20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36, 7, 8, 66, 22, 62, 17, 29, 49, 35, 6, 34, 24, 69, 64, 25, 45, 32, 61, 4, 11, 42, 44, 26, 1, 3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28, 47, 51], 'cur_cost': 3616.0}, {'tour': [34, 45, 22, 65, 62, 28, 9, 33, 66, 54, 3, 27, 21, 41, 10, 63, 50, 16, 40, 64, 20, 53, 38, 32, 1, 15, 60, 30, 42, 39, 23, 17, 19, 24, 55, 12, 61, 48, 47, 8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43, 2, 37, 13, 51, 44, 56, 0, 4, 46, 59, 31, 69, 6, 5, 36, 52, 49, 7, 68], 'cur_cost': 3539.0}]
2025-08-05 10:29:13,761 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,761 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 362, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 362, 'cache_hits': 0, 'similarity_calculations': 1879, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,762 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([26,  4, 23, 59, 32, 58, 43, 22, 35,  2,  8,  7, 38, 41, 25, 53,  3,
       21, 12,  5, 48, 46, 30, 29, 36, 44, 39, 11, 52, 28, 55, 37, 16, 51,
        6, 57, 13, 24, 27, 17, 50,  1, 56, 60, 65,  0, 18, 63, 40, 47, 62,
       42, 10, 34, 67, 68, 69, 45, 49, 19, 54, 64,  9, 14, 31, 20, 61, 15,
       66, 33], dtype=int64), 'cur_cost': 3889.0, 'intermediate_solutions': [{'tour': array([44, 64, 39, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([65, 44, 64, 39, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([68, 65, 44, 64, 39, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 65, 44, 64, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 68, 65, 44, 64, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,762 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 3889.00)
2025-08-05 10:29:13,763 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:13,763 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,763 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,763 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3894.0
2025-08-05 10:29:13,778 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,778 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,778 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,782 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,782 - ExploitationExpert - INFO - populations: [{'tour': [29, 13, 2, 1, 37, 5, 14, 3, 33, 26, 41, 19, 27, 23, 17, 54, 25, 51, 61, 32, 55, 49, 20, 50, 16, 7, 45, 31, 21, 69, 68, 36, 11, 59, 10, 4, 42, 40, 52, 6, 56, 34, 12, 35, 30, 22, 15, 46, 62, 57, 66, 39, 24, 53, 38, 8, 64, 47, 44, 9, 67, 60, 65, 58, 18, 43, 0, 28, 63, 48], 'cur_cost': 2440.0}, {'tour': [0, 14, 5, 18, 6, 1, 3, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 892.0}, {'tour': [0, 1, 11, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 59, 51, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 959.0}, {'tour': [0, 12, 8, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 3, 17, 41, 5, 40, 42, 16, 2, 31, 23, 14, 56, 34, 63], 'cur_cost': 925.0}, {'tour': [0, 22, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 65, 52, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 35, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 10, 63], 'cur_cost': 902.0}, {'tour': [0, 13, 9, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 35, 63], 'cur_cost': 1068.0}, {'tour': [29, 12, 2, 3, 17, 5, 14, 51, 42, 16, 41, 33, 20, 62, 61, 59, 25, 9, 64, 32, 65, 21, 46, 50, 37, 68, 45, 58, 28, 49, 30, 56, 66, 31, 10, 4, 27, 40, 48, 54, 60, 19, 7, 35, 43, 22, 15, 57, 38, 44, 24, 39, 26, 18, 53, 47, 55, 0, 1, 6, 67, 8, 11, 36, 23, 63, 52, 13, 34, 69], 'cur_cost': 3082.0}, {'tour': array([26,  4, 23, 59, 32, 58, 43, 22, 35,  2,  8,  7, 38, 41, 25, 53,  3,
       21, 12,  5, 48, 46, 30, 29, 36, 44, 39, 11, 52, 28, 55, 37, 16, 51,
        6, 57, 13, 24, 27, 17, 50,  1, 56, 60, 65,  0, 18, 63, 40, 47, 62,
       42, 10, 34, 67, 68, 69, 45, 49, 19, 54, 64,  9, 14, 31, 20, 61, 15,
       66, 33], dtype=int64), 'cur_cost': 3889.0}, {'tour': array([58, 22, 47, 38, 53,  0, 68, 40, 55, 41, 36, 50, 42, 31, 23,  9,  2,
       52, 13, 26,  5,  6, 66, 67, 56, 29, 37, 24, 16, 44, 28, 27, 34, 15,
       33, 35, 17, 61, 46, 11, 30, 65, 62, 14, 19,  4, 25, 59, 43, 48, 49,
       64, 57,  8, 21, 60, 69, 20, 10, 54,  1, 39, 18, 45,  3, 63,  7, 32,
       51, 12], dtype=int64), 'cur_cost': 3894.0}, {'tour': [34, 45, 22, 65, 62, 28, 9, 33, 66, 54, 3, 27, 21, 41, 10, 63, 50, 16, 40, 64, 20, 53, 38, 32, 1, 15, 60, 30, 42, 39, 23, 17, 19, 24, 55, 12, 61, 48, 47, 8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43, 2, 37, 13, 51, 44, 56, 0, 4, 46, 59, 31, 69, 6, 5, 36, 52, 49, 7, 68], 'cur_cost': 3539.0}]
2025-08-05 10:29:13,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,784 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 363, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 363, 'cache_hits': 0, 'similarity_calculations': 1890, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,785 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([58, 22, 47, 38, 53,  0, 68, 40, 55, 41, 36, 50, 42, 31, 23,  9,  2,
       52, 13, 26,  5,  6, 66, 67, 56, 29, 37, 24, 16, 44, 28, 27, 34, 15,
       33, 35, 17, 61, 46, 11, 30, 65, 62, 14, 19,  4, 25, 59, 43, 48, 49,
       64, 57,  8, 21, 60, 69, 20, 10, 54,  1, 39, 18, 45,  3, 63,  7, 32,
       51, 12], dtype=int64), 'cur_cost': 3894.0, 'intermediate_solutions': [{'tour': array([41, 43, 21,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 41, 43, 21, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([67,  5, 41, 43, 21,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21,  5, 41, 43, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 67,  5, 41, 43,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,785 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 3894.00)
2025-08-05 10:29:13,785 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:13,785 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:13,785 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:13,785 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3495.0
2025-08-05 10:29:13,798 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:13,798 - ExploitationExpert - INFO - res_population_costs: [675, 676, 681, 684, 687, 688, 688, 688, 689.0, 689]
2025-08-05 10:29:13,798 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43,
       67, 26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51,
       59, 11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 52,  4,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 48, 54, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 25, 54, 48, 27,  7, 31,  2, 13, 19, 29, 43, 67, 26, 45, 24,
       44, 38, 60, 39,  8, 16, 42, 40,  5, 41, 17,  3, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 17,  3, 31,  2, 41, 40, 42, 16,
        8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48,
       54, 18,  6,  1, 23, 14, 56, 62, 65, 21, 37, 58, 34, 68, 30, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 30, 68, 34, 58, 37, 21, 65, 62, 56, 14, 23,  1,
        6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 44, 24, 38,
       60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 37, 68, 30, 12, 28, 69, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 22, 37, 12, 28, 69, 30, 68, 34, 58, 21, 65, 62, 56, 14, 23,
        1,  6, 18, 54, 48, 25,  7, 27, 13, 19, 29, 43, 67, 26, 45, 24, 44,
       38, 60, 39,  8, 16, 42, 40, 41,  2, 31,  3, 17,  5, 52,  4,  9, 51,
       59, 11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 10:29:13,802 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:13,802 - ExploitationExpert - INFO - populations: [{'tour': [29, 13, 2, 1, 37, 5, 14, 3, 33, 26, 41, 19, 27, 23, 17, 54, 25, 51, 61, 32, 55, 49, 20, 50, 16, 7, 45, 31, 21, 69, 68, 36, 11, 59, 10, 4, 42, 40, 52, 6, 56, 34, 12, 35, 30, 22, 15, 46, 62, 57, 66, 39, 24, 53, 38, 8, 64, 47, 44, 9, 67, 60, 65, 58, 18, 43, 0, 28, 63, 48], 'cur_cost': 2440.0}, {'tour': [0, 14, 5, 18, 6, 1, 3, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 892.0}, {'tour': [0, 1, 11, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 59, 51, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 959.0}, {'tour': [0, 12, 8, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 3, 17, 41, 5, 40, 42, 16, 2, 31, 23, 14, 56, 34, 63], 'cur_cost': 925.0}, {'tour': [0, 22, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 65, 52, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 35, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 10, 63], 'cur_cost': 902.0}, {'tour': [0, 13, 9, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 35, 63], 'cur_cost': 1068.0}, {'tour': [29, 12, 2, 3, 17, 5, 14, 51, 42, 16, 41, 33, 20, 62, 61, 59, 25, 9, 64, 32, 65, 21, 46, 50, 37, 68, 45, 58, 28, 49, 30, 56, 66, 31, 10, 4, 27, 40, 48, 54, 60, 19, 7, 35, 43, 22, 15, 57, 38, 44, 24, 39, 26, 18, 53, 47, 55, 0, 1, 6, 67, 8, 11, 36, 23, 63, 52, 13, 34, 69], 'cur_cost': 3082.0}, {'tour': array([26,  4, 23, 59, 32, 58, 43, 22, 35,  2,  8,  7, 38, 41, 25, 53,  3,
       21, 12,  5, 48, 46, 30, 29, 36, 44, 39, 11, 52, 28, 55, 37, 16, 51,
        6, 57, 13, 24, 27, 17, 50,  1, 56, 60, 65,  0, 18, 63, 40, 47, 62,
       42, 10, 34, 67, 68, 69, 45, 49, 19, 54, 64,  9, 14, 31, 20, 61, 15,
       66, 33], dtype=int64), 'cur_cost': 3889.0}, {'tour': array([58, 22, 47, 38, 53,  0, 68, 40, 55, 41, 36, 50, 42, 31, 23,  9,  2,
       52, 13, 26,  5,  6, 66, 67, 56, 29, 37, 24, 16, 44, 28, 27, 34, 15,
       33, 35, 17, 61, 46, 11, 30, 65, 62, 14, 19,  4, 25, 59, 43, 48, 49,
       64, 57,  8, 21, 60, 69, 20, 10, 54,  1, 39, 18, 45,  3, 63,  7, 32,
       51, 12], dtype=int64), 'cur_cost': 3894.0}, {'tour': array([43, 18, 32, 26, 41, 29, 60, 44, 42, 63, 40, 20, 46, 38, 35, 67, 19,
       27, 36, 25, 50,  2, 61, 37, 65, 12, 69, 59, 31, 23, 16, 52, 68, 49,
       22, 64,  1, 11, 24,  7,  4, 34, 21, 58,  5, 15, 13,  6,  0, 54, 55,
       10, 47, 39, 66, 45,  8, 33, 17, 28, 14, 53, 62, 56, 48, 57, 30,  9,
        3, 51], dtype=int64), 'cur_cost': 3495.0}]
2025-08-05 10:29:13,804 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:13,804 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 364, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 364, 'cache_hits': 0, 'similarity_calculations': 1902, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:13,805 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([43, 18, 32, 26, 41, 29, 60, 44, 42, 63, 40, 20, 46, 38, 35, 67, 19,
       27, 36, 25, 50,  2, 61, 37, 65, 12, 69, 59, 31, 23, 16, 52, 68, 49,
       22, 64,  1, 11, 24,  7,  4, 34, 21, 58,  5, 15, 13,  6,  0, 54, 55,
       10, 47, 39, 66, 45,  8, 33, 17, 28, 14, 53, 62, 56, 48, 57, 30,  9,
        3, 51], dtype=int64), 'cur_cost': 3495.0, 'intermediate_solutions': [{'tour': array([22, 45, 34, 65, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([65, 22, 45, 34, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([62, 65, 22, 45, 34, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 65, 22, 45, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 62, 65, 22, 45, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3535.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:13,806 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3495.00)
2025-08-05 10:29:13,806 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:13,806 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:13,808 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [29, 13, 2, 1, 37, 5, 14, 3, 33, 26, 41, 19, 27, 23, 17, 54, 25, 51, 61, 32, 55, 49, 20, 50, 16, 7, 45, 31, 21, 69, 68, 36, 11, 59, 10, 4, 42, 40, 52, 6, 56, 34, 12, 35, 30, 22, 15, 46, 62, 57, 66, 39, 24, 53, 38, 8, 64, 47, 44, 9, 67, 60, 65, 58, 18, 43, 0, 28, 63, 48], 'cur_cost': 2440.0, 'intermediate_solutions': [{'tour': [0, 21, 9, 20, 33, 11, 59, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 53, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 39, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 9, 20, 33, 11, 59, 51, 57, 49, 50, 64, 55, 66, 47, 61, 53, 32, 13, 19, 29, 43, 67, 26, 45, 24, 44, 38, 60, 39, 8, 16, 42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 65, 52, 4, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 9, 20, 33, 11, 59, 19, 51, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 5, 18, 6, 1, 3, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 892.0, 'intermediate_solutions': [{'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 4, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 11, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2397.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 11, 3, 27, 42, 67, 26, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2403.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 39, 32, 43, 44, 16, 59, 61, 55, 5, 40, 41, 11, 18, 25, 31, 52, 15, 62, 21, 34, 58, 14, 48, 33, 47, 8, 24, 60, 3, 27, 26, 67, 42, 1, 54, 56, 46, 65, 57, 28, 69, 12, 36, 51, 9, 17, 7, 23, 0, 64, 50, 66, 22, 6, 68, 35, 63, 4, 10, 20, 38, 13, 2, 29, 45, 49, 37, 30, 19], 'cur_cost': 2446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 11, 3, 17, 41, 5, 40, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 59, 51, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 34, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 27, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 11, 33, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 18, 5, 40, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 55, 64, 10, 47, 66, 53, 50, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63, 35], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 8, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 18, 6, 1, 3, 17, 41, 5, 40, 42, 16, 2, 31, 23, 14, 56, 34, 63], 'cur_cost': 925.0, 'intermediate_solutions': [{'tour': [0, 13, 18, 2, 31, 6, 36, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 1, 46, 15, 22, 35, 63], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 44, 38, 60, 39, 8, 67, 43, 29, 19, 54, 48, 27, 7, 25, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 18, 13, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 18, 2, 31, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 25, 7, 27, 48, 54, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 23], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 65, 52, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 35, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 10, 63], 'cur_cost': 902.0, 'intermediate_solutions': [{'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 49, 20, 33, 11, 59, 51, 9, 16, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 1075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 50, 61, 32, 53, 66, 47, 10, 64, 55, 38, 44, 24, 39, 60, 8, 26, 45, 63], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 1, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 49, 57, 36, 46, 15, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 3, 17, 41, 5, 40, 52, 39, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 60, 8, 26, 45, 63], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 9, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 35, 63], 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': [0, 3, 8, 15, 46, 22, 1, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 35, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 36, 57, 49, 52, 4, 9, 51, 59, 11, 33, 20, 16, 42, 40, 5, 41, 17, 18, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 8, 15, 46, 22, 35, 28, 12, 30, 68, 5, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 17, 41, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 49, 57, 36, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [29, 12, 2, 3, 17, 5, 14, 51, 42, 16, 41, 33, 20, 62, 61, 59, 25, 9, 64, 32, 65, 21, 46, 50, 37, 68, 45, 58, 28, 49, 30, 56, 66, 31, 10, 4, 27, 40, 48, 54, 60, 19, 7, 35, 43, 22, 15, 57, 38, 44, 24, 39, 26, 18, 53, 47, 55, 0, 1, 6, 67, 8, 11, 36, 23, 63, 52, 13, 34, 69], 'cur_cost': 3082.0, 'intermediate_solutions': [{'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 20, 12, 62, 51, 29, 56, 22, 42, 33, 48, 31, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 59, 63, 9, 14, 54, 2, 11, 34, 50, 30, 55, 66, 35, 47, 10, 64, 13, 19, 49, 0, 36, 15, 45, 8, 67, 38, 60, 39, 69, 57, 6, 46, 16, 23, 17, 3, 18, 26, 28, 25, 27, 7, 24, 31, 20, 33, 42, 22, 56, 29, 51, 62, 12, 48, 68, 1], 'cur_cost': 3155.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [53, 21, 32, 5, 44, 41, 43, 61, 4, 52, 65, 40, 58, 37, 68, 48, 12, 62, 51, 29, 56, 22, 42, 33, 20, 24, 7, 27, 25, 28, 26, 18, 3, 17, 23, 16, 46, 6, 57, 69, 39, 60, 38, 67, 8, 45, 15, 36, 0, 49, 19, 13, 64, 10, 47, 35, 66, 55, 30, 50, 31, 34, 11, 2, 54, 14, 9, 63, 59, 1], 'cur_cost': 3104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([26,  4, 23, 59, 32, 58, 43, 22, 35,  2,  8,  7, 38, 41, 25, 53,  3,
       21, 12,  5, 48, 46, 30, 29, 36, 44, 39, 11, 52, 28, 55, 37, 16, 51,
        6, 57, 13, 24, 27, 17, 50,  1, 56, 60, 65,  0, 18, 63, 40, 47, 62,
       42, 10, 34, 67, 68, 69, 45, 49, 19, 54, 64,  9, 14, 31, 20, 61, 15,
       66, 33], dtype=int64), 'cur_cost': 3889.0, 'intermediate_solutions': [{'tour': array([44, 64, 39, 65, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3481.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([65, 44, 64, 39, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([68, 65, 44, 64, 39, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 65, 44, 64, 68, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 68, 65, 44, 64, 36, 48, 22, 30,  3, 58, 19, 59, 34, 46, 11, 67,
        5, 60,  8,  4, 35,  7, 33, 38, 31, 53,  6, 23, 49, 40, 26, 62, 66,
       32,  9, 41, 61, 21, 17, 28, 69,  0, 37, 50, 15,  1,  2, 63, 56, 20,
       55, 16, 52, 25, 27, 43, 12, 18, 45, 13, 54, 42, 47, 10, 51, 14, 24,
       57, 29]), 'cur_cost': 3509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([58, 22, 47, 38, 53,  0, 68, 40, 55, 41, 36, 50, 42, 31, 23,  9,  2,
       52, 13, 26,  5,  6, 66, 67, 56, 29, 37, 24, 16, 44, 28, 27, 34, 15,
       33, 35, 17, 61, 46, 11, 30, 65, 62, 14, 19,  4, 25, 59, 43, 48, 49,
       64, 57,  8, 21, 60, 69, 20, 10, 54,  1, 39, 18, 45,  3, 63,  7, 32,
       51, 12], dtype=int64), 'cur_cost': 3894.0, 'intermediate_solutions': [{'tour': array([41, 43, 21,  5, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 41, 43, 21, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([67,  5, 41, 43, 21,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3651.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21,  5, 41, 43, 67,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 67,  5, 41, 43,  2,  9, 27, 10,  0, 12, 40, 60, 55, 31, 56, 50,
       20, 39, 13, 68, 54, 19, 48, 46, 14, 52, 57, 58, 59, 65, 36,  7,  8,
       66, 22, 62, 17, 29, 49, 35,  6, 34, 24, 69, 64, 25, 45, 32, 61,  4,
       11, 42, 44, 26,  1,  3, 38, 15, 18, 63, 30, 16, 37, 23, 53, 33, 28,
       47, 51]), 'cur_cost': 3614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 18, 32, 26, 41, 29, 60, 44, 42, 63, 40, 20, 46, 38, 35, 67, 19,
       27, 36, 25, 50,  2, 61, 37, 65, 12, 69, 59, 31, 23, 16, 52, 68, 49,
       22, 64,  1, 11, 24,  7,  4, 34, 21, 58,  5, 15, 13,  6,  0, 54, 55,
       10, 47, 39, 66, 45,  8, 33, 17, 28, 14, 53, 62, 56, 48, 57, 30,  9,
        3, 51], dtype=int64), 'cur_cost': 3495.0, 'intermediate_solutions': [{'tour': array([22, 45, 34, 65, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([65, 22, 45, 34, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([62, 65, 22, 45, 34, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 65, 22, 45, 62, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3539.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 62, 65, 22, 45, 28,  9, 33, 66, 54,  3, 27, 21, 41, 10, 63, 50,
       16, 40, 64, 20, 53, 38, 32,  1, 15, 60, 30, 42, 39, 23, 17, 19, 24,
       55, 12, 61, 48, 47,  8, 26, 35, 14, 29, 67, 11, 18, 57, 58, 25, 43,
        2, 37, 13, 51, 44, 56,  0,  4, 46, 59, 31, 69,  6,  5, 36, 52, 49,
        7, 68]), 'cur_cost': 3535.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:13,809 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:13,809 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,813 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=892.000, 多样性=0.965
2025-08-05 10:29:13,813 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:13,813 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:13,813 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:13,814 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03214752932000109, 'best_improvement': 0.01327433628318584}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0009878169245967302}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07137638399847702, 'recent_improvements': [-0.10269485609790324, 0.08771864737879666, 0.04005791189905079], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 675, 'new_best_cost': 675, 'quality_improvement': 0.0, 'old_diversity': 0.7803174603174603, 'new_diversity': 0.7803174603174603, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:13,816 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:13,819 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\st70_solution.json
2025-08-05 10:29:13,819 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\st70_20250805_102913.solution
2025-08-05 10:29:13,819 - __main__ - INFO - 实例执行完成 - 运行时间: 1.87s, 最佳成本: 675
2025-08-05 10:29:13,819 - __main__ - INFO - 实例 st70 处理完成
