# -*- coding: utf-8 -*-
"""
适应度景观分析系统配置

提供零修改集成的配置管理，支持在LLM和算法化实现之间切换。
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Type
from enum import Enum


class AnalysisMode(Enum):
    """分析模式枚举"""
    LLM = "llm"           # 使用原有的LLM实现
    ALGORITHMIC = "algorithmic"  # 使用新的算法化实现
    HYBRID = "hybrid"     # 混合模式（未来扩展）


class LandscapeConfig:
    """适应度景观分析配置管理器"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        参数:
            config_file: 配置文件路径
        """
        self.config_file = config_file or "config/landscape_analysis.json"
        self.config = self._load_config()
        self.logger = logging.getLogger(__name__)

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            # 分析模式配置
            "analysis_mode": AnalysisMode.ALGORITHMIC.value,
            "fallback_to_llm": True,  # 算法化失败时是否回退到LLM

            # 算法化分析配置
            "algorithmic_config": {
                "k_neighbors": 5,
                "clustering_eps": 0.5,
                "clustering_min_samples": 3,
                "grid_size": 100,
                "window_size": 10,
                "cache_size": 1000,
                "max_history_length": 50
            },

            # 可视化配置
            "visualization_config": {
                "enabled": True,
                "grid_size": 50,
                "update_interval": 1000,
                "auto_save": True,
                "save_dir": "visualization_output",
                "port": 8050,
                "host": "127.0.0.1"
            },

            # 性能配置
            "performance_config": {
                "enable_jit": True,
                "parallel_processing": True,
                "max_workers": 4,
                "memory_limit_mb": 1024
            },

            # 日志配置
            "logging_config": {
                "level": "INFO",
                "file": "logs/landscape_analysis.log",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置，保持默认值
                    default_config.update(loaded_config)
            except Exception as e:
                logging.warning(f"加载配置文件失败，使用默认配置: {e}")

        return default_config

    def save_config(self):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    def get_analysis_mode(self) -> AnalysisMode:
        """获取当前分析模式"""
        mode_str = self.config.get("analysis_mode", AnalysisMode.ALGORITHMIC.value)
        try:
            return AnalysisMode(mode_str)
        except ValueError:
            self.logger.warning(f"无效的分析模式: {mode_str}，使用默认模式")
            return AnalysisMode.ALGORITHMIC

    def set_analysis_mode(self, mode: AnalysisMode):
        """设置分析模式"""
        self.config["analysis_mode"] = mode.value
        self.logger.info(f"分析模式已设置为: {mode.value}")

    def get_algorithmic_config(self) -> Dict[str, Any]:
        """获取算法化分析配置"""
        return self.config.get("algorithmic_config", {})

    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return self.config.get("visualization_config", {})

    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.config.get("performance_config", {})

    def is_visualization_enabled(self) -> bool:
        """检查是否启用可视化"""
        return self.config.get("visualization_config", {}).get("enabled", True)

    def should_fallback_to_llm(self) -> bool:
        """检查是否应该回退到LLM"""
        return self.config.get("fallback_to_llm", True)


class LandscapeExpertFactory:
    """适应度景观专家工厂类，实现零修改集成"""

    def __init__(self, config: Optional[LandscapeConfig] = None):
        """
        初始化工厂

        参数:
            config: 配置管理器
        """
        self.config = config or LandscapeConfig()
        self.logger = logging.getLogger(__name__)

    def create_landscape_expert(self):
        """
        创建适应度景观专家实例

        返回:
            景观专家实例（LandscapeExpert或AlgorithmicLandscapeExpert）
        """
        mode = self.config.get_analysis_mode()

        try:
            if mode == AnalysisMode.ALGORITHMIC:
                return self._create_algorithmic_expert()
            elif mode == AnalysisMode.LLM:
                return self._create_llm_expert()
            else:
                self.logger.warning(f"不支持的分析模式: {mode}，使用算法化实现")
                return self._create_algorithmic_expert()

        except Exception as e:
            self.logger.error(f"创建{mode.value}专家失败: {e}")

            # 如果启用了回退机制
            if self.config.should_fallback_to_llm() and mode != AnalysisMode.LLM:
                self.logger.info("回退到LLM实现")
                try:
                    return self._create_llm_expert()
                except Exception as fallback_error:
                    self.logger.error(f"LLM回退也失败: {fallback_error}")

            raise RuntimeError(f"无法创建适应度景观专家: {e}")

    def _create_algorithmic_expert(self):
        """创建算法化专家"""
        try:
            try:
                from ..experts.analysis.algorithmic_landscape_expert import AlgorithmicLandscapeExpert
            except ImportError:
                from experts.analysis.algorithmic_landscape_expert import AlgorithmicLandscapeExpert

            algorithmic_config = self.config.get_algorithmic_config()
            expert = AlgorithmicLandscapeExpert(algorithmic_config)

            self.logger.info("算法化适应度景观专家创建成功")
            return expert

        except ImportError as e:
            raise ImportError(f"无法导入算法化专家模块: {e}")

    def _create_llm_expert(self):
        """创建LLM专家"""
        try:
            try:
                from ..experts.analysis.landscape_expert import LandscapeExpert
            except ImportError:
                from experts.analysis.landscape_expert import LandscapeExpert

            expert = LandscapeExpert()
            self.logger.info("LLM适应度景观专家创建成功")
            return expert

        except ImportError as e:
            raise ImportError(f"无法导入LLM专家模块: {e}")


# 全局配置实例
_global_config = None


def get_global_config() -> LandscapeConfig:
    """获取全局配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = LandscapeConfig()
    return _global_config


def set_global_config(config: LandscapeConfig):
    """设置全局配置实例"""
    global _global_config
    _global_config = config


def create_landscape_expert():
    """
    便捷函数：使用全局配置创建景观专家

    返回:
        景观专家实例
    """
    factory = LandscapeExpertFactory(get_global_config())
    return factory.create_landscape_expert()


# 配置切换便捷函数
def switch_to_algorithmic():
    """切换到算法化模式"""
    config = get_global_config()
    config.set_analysis_mode(AnalysisMode.ALGORITHMIC)
    config.save_config()


def switch_to_llm():
    """切换到LLM模式"""
    config = get_global_config()
    config.set_analysis_mode(AnalysisMode.LLM)
    config.save_config()


def is_algorithmic_mode() -> bool:
    """检查是否为算法化模式"""
    return get_global_config().get_analysis_mode() == AnalysisMode.ALGORITHMIC