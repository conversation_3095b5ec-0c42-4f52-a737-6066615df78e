2025-07-31 17:45:44,260 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-31 17:45:44,260 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-31 17:45:44,260 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:44,278 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9941.0, 多样性=0.921
2025-07-31 17:45:44,278 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:45:44,285 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.026
2025-07-31 17:45:44,320 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:45:44,323 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-07-31 17:45:44,323 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955144.3235772, 'status': 'default_fallback'}}
2025-07-31 17:45:44,323 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:45:44,323 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:45:44,323 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9941.0
  • mean_cost: 75954.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:45:44,324 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:45:44,324 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:45:46,000 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity warrants heavy exploration. Best cost far from mean cost suggests potential gains from wider search."
}
```
2025-07-31 17:45:46,001 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:45:46,001 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:46,001 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:46,001 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity warrants heavy exploration. Best cost far from mean cost suggests potential gains from wider search."
}
```
2025-07-31 17:45:46,002 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:45:46,002 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:46,002 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity warrants heavy exploration. Best cost far from mean cost suggests potential gains from wider search."
}
```
2025-07-31 17:45:46,002 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:45:46,002 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:45:46,002 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:45:46,003 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,006 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:46,006 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,126 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12815.0, 路径长度: 66
2025-07-31 17:45:46,126 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12815.0}
2025-07-31 17:45:46,126 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:45:46,126 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:45:46,126 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,129 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:46,129 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,129 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14862.0, 路径长度: 66
2025-07-31 17:45:46,129 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 7, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0}
2025-07-31 17:45:46,130 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:45:46,130 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:45:46,130 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,132 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:46,132 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12863.0, 路径长度: 66
2025-07-31 17:45:46,133 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 19, 11, 9, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}
2025-07-31 17:45:46,133 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-07-31 17:45:46,133 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-07-31 17:45:46,133 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,136 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:46,136 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12269.0, 路径长度: 66
2025-07-31 17:45:46,136 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 5, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12269.0}
2025-07-31 17:45:46,136 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-07-31 17:45:46,137 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-31 17:45:46,137 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,140 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:46,140 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,140 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14768.0, 路径长度: 66
2025-07-31 17:45:46,140 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 13, 4, 17, 12, 22, 23, 16, 18, 19, 21, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}
2025-07-31 17:45:46,141 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-07-31 17:45:46,141 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-07-31 17:45:46,141 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,143 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:46,143 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,144 - ExplorationExpert - INFO - 探索路径生成完成，成本: 82641.0, 路径长度: 66
2025-07-31 17:45:46,144 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}
2025-07-31 17:45:46,144 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-07-31 17:45:46,144 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-31 17:45:46,144 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:46,146 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:46,146 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:46,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85867.0, 路径长度: 66
2025-07-31 17:45:46,146 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}
2025-07-31 17:45:46,146 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-31 17:45:46,147 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:46,148 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:46,149 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 112089.0
2025-07-31 17:45:47,547 - ExploitationExpert - INFO - res_population_num: 1
2025-07-31 17:45:47,547 - ExploitationExpert - INFO - res_population_costs: [88678.0]
2025-07-31 17:45:47,547 - ExploitationExpert - INFO - res_populations: [array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64)]
2025-07-31 17:45:47,548 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:47,548 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12815.0}, {'tour': [0, 19, 7, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0}, {'tour': [0, 7, 19, 11, 9, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [0, 10, 5, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12269.0}, {'tour': [0, 13, 4, 17, 12, 22, 23, 16, 18, 19, 21, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}, {'tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}, {'tour': array([ 0, 18, 46, 33, 60, 15, 22, 34,  3, 57, 47, 48,  4, 39, 51, 63, 43,
       30, 55, 10,  5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45,  8, 62,
       41, 11,  7, 31, 12, 49, 40, 56, 65, 42, 25, 35,  2, 29,  1, 37, 54,
       52, 17, 16, 64, 28, 58, 23, 19,  9, 50,  6, 13, 61, 32, 38],
      dtype=int64), 'cur_cost': 112089.0}, {'tour': array([28, 13, 45, 42, 44, 50, 22, 12,  6,  0, 15, 64, 10,  5, 38, 62, 56,
       40, 57, 29, 27,  2, 58, 16, 63, 33, 34, 52, 39, 25, 47,  9, 14,  8,
       35, 11, 43, 17,  1, 41, 18, 65, 20, 37, 19, 48, 60,  3, 54, 49, 30,
       59, 61, 26, 53, 51, 23, 32, 46, 21, 55, 31, 24,  4, 36,  7],
      dtype=int64), 'cur_cost': 109929.0}, {'tour': array([22, 11, 25, 64, 42, 48, 17, 26,  7, 19, 13, 30,  4, 39, 10, 38,  6,
       53, 49, 47, 29, 31, 15,  2, 14, 36, 50, 33,  8, 27, 16, 35,  3, 23,
        9, 62, 44, 52,  1, 55, 58, 65, 20, 12, 60, 24, 21, 37, 51, 28, 54,
       57,  0, 59, 18, 46, 63, 56, 40, 41, 32, 43, 61, 45, 34,  5],
      dtype=int64), 'cur_cost': 106331.0}]
2025-07-31 17:45:47,550 - ExploitationExpert - INFO - 局部搜索耗时: 1.40秒
2025-07-31 17:45:47,551 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-31 17:45:47,551 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 0, 18, 46, 33, 60, 15, 22, 34,  3, 57, 47, 48,  4, 39, 51, 63, 43,
       30, 55, 10,  5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45,  8, 62,
       41, 11,  7, 31, 12, 49, 40, 56, 65, 42, 25, 35,  2, 29,  1, 37, 54,
       52, 17, 16, 64, 28, 58, 23, 19,  9, 50,  6, 13, 61, 32, 38],
      dtype=int64), 'cur_cost': 112089.0}
2025-07-31 17:45:47,551 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-07-31 17:45:47,551 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:47,552 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:47,552 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 96572.0
2025-07-31 17:45:49,437 - ExploitationExpert - INFO - res_population_num: 2
2025-07-31 17:45:49,437 - ExploitationExpert - INFO - res_population_costs: [88678.0, 9560.0]
2025-07-31 17:45:49,437 - ExploitationExpert - INFO - res_populations: [array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:49,438 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:49,438 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12815.0}, {'tour': [0, 19, 7, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0}, {'tour': [0, 7, 19, 11, 9, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [0, 10, 5, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12269.0}, {'tour': [0, 13, 4, 17, 12, 22, 23, 16, 18, 19, 21, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}, {'tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}, {'tour': array([ 0, 18, 46, 33, 60, 15, 22, 34,  3, 57, 47, 48,  4, 39, 51, 63, 43,
       30, 55, 10,  5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45,  8, 62,
       41, 11,  7, 31, 12, 49, 40, 56, 65, 42, 25, 35,  2, 29,  1, 37, 54,
       52, 17, 16, 64, 28, 58, 23, 19,  9, 50,  6, 13, 61, 32, 38],
      dtype=int64), 'cur_cost': 112089.0}, {'tour': array([63, 51, 47, 13,  7, 64, 12, 43, 38, 31, 21, 29, 18, 19,  0, 46, 28,
       40, 61, 15, 30, 35, 58, 39, 59, 45, 27,  1, 23,  8, 33, 57, 44, 26,
       25, 36, 53, 22, 16, 41, 20, 34,  2,  4, 65, 56, 48,  6,  5,  3, 55,
       62, 50, 49, 42, 60, 54, 17, 24, 37,  9, 11, 32, 52, 14, 10],
      dtype=int64), 'cur_cost': 96572.0}, {'tour': array([22, 11, 25, 64, 42, 48, 17, 26,  7, 19, 13, 30,  4, 39, 10, 38,  6,
       53, 49, 47, 29, 31, 15,  2, 14, 36, 50, 33,  8, 27, 16, 35,  3, 23,
        9, 62, 44, 52,  1, 55, 58, 65, 20, 12, 60, 24, 21, 37, 51, 28, 54,
       57,  0, 59, 18, 46, 63, 56, 40, 41, 32, 43, 61, 45, 34,  5],
      dtype=int64), 'cur_cost': 106331.0}]
2025-07-31 17:45:49,440 - ExploitationExpert - INFO - 局部搜索耗时: 1.89秒
2025-07-31 17:45:49,440 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-31 17:45:49,441 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([63, 51, 47, 13,  7, 64, 12, 43, 38, 31, 21, 29, 18, 19,  0, 46, 28,
       40, 61, 15, 30, 35, 58, 39, 59, 45, 27,  1, 23,  8, 33, 57, 44, 26,
       25, 36, 53, 22, 16, 41, 20, 34,  2,  4, 65, 56, 48,  6,  5,  3, 55,
       62, 50, 49, 42, 60, 54, 17, 24, 37,  9, 11, 32, 52, 14, 10],
      dtype=int64), 'cur_cost': 96572.0}
2025-07-31 17:45:49,441 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-31 17:45:49,441 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:49,441 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:49,441 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 118416.0
2025-07-31 17:45:49,507 - ExploitationExpert - INFO - res_population_num: 6
2025-07-31 17:45:49,507 - ExploitationExpert - INFO - res_population_costs: [88678.0, 9560.0, 9557.0, 9557, 9538, 9526]
2025-07-31 17:45:49,507 - ExploitationExpert - INFO - res_populations: [array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:49,510 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:49,510 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12815.0}, {'tour': [0, 19, 7, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0}, {'tour': [0, 7, 19, 11, 9, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [0, 10, 5, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12269.0}, {'tour': [0, 13, 4, 17, 12, 22, 23, 16, 18, 19, 21, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}, {'tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}, {'tour': array([ 0, 18, 46, 33, 60, 15, 22, 34,  3, 57, 47, 48,  4, 39, 51, 63, 43,
       30, 55, 10,  5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45,  8, 62,
       41, 11,  7, 31, 12, 49, 40, 56, 65, 42, 25, 35,  2, 29,  1, 37, 54,
       52, 17, 16, 64, 28, 58, 23, 19,  9, 50,  6, 13, 61, 32, 38],
      dtype=int64), 'cur_cost': 112089.0}, {'tour': array([63, 51, 47, 13,  7, 64, 12, 43, 38, 31, 21, 29, 18, 19,  0, 46, 28,
       40, 61, 15, 30, 35, 58, 39, 59, 45, 27,  1, 23,  8, 33, 57, 44, 26,
       25, 36, 53, 22, 16, 41, 20, 34,  2,  4, 65, 56, 48,  6,  5,  3, 55,
       62, 50, 49, 42, 60, 54, 17, 24, 37,  9, 11, 32, 52, 14, 10],
      dtype=int64), 'cur_cost': 96572.0}, {'tour': array([60, 24, 58, 42,  5, 19, 50, 53,  3, 43, 30, 46, 36,  7, 41, 48,  9,
        6, 59, 14, 37, 52, 40, 39, 63, 55,  1, 65, 34, 17, 35, 23, 11, 29,
       10, 18, 62, 22, 27, 20, 44, 49, 56, 33,  0, 12, 25, 15, 54, 13, 64,
       31, 47, 26, 51, 32, 21, 57, 16, 61, 38,  2,  4, 28, 45,  8],
      dtype=int64), 'cur_cost': 118416.0}]
2025-07-31 17:45:49,511 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:49,512 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-31 17:45:49,512 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([60, 24, 58, 42,  5, 19, 50, 53,  3, 43, 30, 46, 36,  7, 41, 48,  9,
        6, 59, 14, 37, 52, 40, 39, 63, 55,  1, 65, 34, 17, 35, 23, 11, 29,
       10, 18, 62, 22, 27, 20, 44, 49, 56, 33,  0, 12, 25, 15, 54, 13, 64,
       31, 47, 26, 51, 32, 21, 57, 16, 61, 38,  2,  4, 28, 45,  8],
      dtype=int64), 'cur_cost': 118416.0}
2025-07-31 17:45:49,513 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 13, 20, 21, 19, 18, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12815.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 7, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14862.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 19, 11, 9, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12269.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 4, 17, 12, 22, 23, 16, 18, 19, 21, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 18, 46, 33, 60, 15, 22, 34,  3, 57, 47, 48,  4, 39, 51, 63, 43,
       30, 55, 10,  5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45,  8, 62,
       41, 11,  7, 31, 12, 49, 40, 56, 65, 42, 25, 35,  2, 29,  1, 37, 54,
       52, 17, 16, 64, 28, 58, 23, 19,  9, 50,  6, 13, 61, 32, 38],
      dtype=int64), 'cur_cost': 112089.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 51, 47, 13,  7, 64, 12, 43, 38, 31, 21, 29, 18, 19,  0, 46, 28,
       40, 61, 15, 30, 35, 58, 39, 59, 45, 27,  1, 23,  8, 33, 57, 44, 26,
       25, 36, 53, 22, 16, 41, 20, 34,  2,  4, 65, 56, 48,  6,  5,  3, 55,
       62, 50, 49, 42, 60, 54, 17, 24, 37,  9, 11, 32, 52, 14, 10],
      dtype=int64), 'cur_cost': 96572.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 24, 58, 42,  5, 19, 50, 53,  3, 43, 30, 46, 36,  7, 41, 48,  9,
        6, 59, 14, 37, 52, 40, 39, 63, 55,  1, 65, 34, 17, 35, 23, 11, 29,
       10, 18, 62, 22, 27, 20, 44, 49, 56, 33,  0, 12, 25, 15, 54, 13, 64,
       31, 47, 26, 51, 32, 21, 57, 16, 61, 38,  2,  4, 28, 45,  8],
      dtype=int64), 'cur_cost': 118416.0}}]
2025-07-31 17:45:49,513 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:45:49,513 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:49,529 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12269.0, 多样性=0.781
2025-07-31 17:45:49,529 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-31 17:45:49,529 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-07-31 17:45:49,529 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:45:49,530 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.009618786096548164, 'best_improvement': -0.23418167186399758}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.1514264813460133}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9526, 'new_best_cost': 9526, 'quality_improvement': 0.0, 'old_diversity': 0.9434343434343434, 'new_diversity': 0.9434343434343434, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-31 17:45:49,531 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-31 17:45:49,531 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-31 17:45:49,531 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-31 17:45:49,531 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:49,548 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12269.0, 多样性=0.781
2025-07-31 17:45:49,548 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:45:49,550 - PathExpert - INFO - 路径结构分析完成: 公共边数量=15, 路径相似性=0.071
2025-07-31 17:45:49,550 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:45:49,551 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.943
2025-07-31 17:45:49,554 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-07-31 17:45:49,555 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955149.5544703, 'status': 'default_fallback'}}
2025-07-31 17:45:49,555 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:45:49,555 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:45:49,555 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12269.0
  • mean_cost: 56316.2
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:45:49,555 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:45:49,556 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:45:51,169 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial exploration; iteration is 1 and landscape focus is balance, diversity is 0.5. Prioritize exploration for the majority."
}
```
2025-07-31 17:45:51,170 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:45:51,170 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:51,171 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:51,171 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial exploration; iteration is 1 and landscape focus is balance, diversity is 0.5. Prioritize exploration for the majority."
}
```
2025-07-31 17:45:51,172 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:45:51,172 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:51,173 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial exploration; iteration is 1 and landscape focus is balance, diversity is 0.5. Prioritize exploration for the majority."
}
```
2025-07-31 17:45:51,174 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:45:51,175 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:45:51,175 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:45:51,175 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,182 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:51,182 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59179.0, 路径长度: 66
2025-07-31 17:45:51,182 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}
2025-07-31 17:45:51,182 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:45:51,182 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:45:51,183 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,188 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:51,189 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,189 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55196.0, 路径长度: 66
2025-07-31 17:45:51,189 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}
2025-07-31 17:45:51,189 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:45:51,189 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:45:51,190 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,191 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:51,191 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97546.0, 路径长度: 66
2025-07-31 17:45:51,192 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}
2025-07-31 17:45:51,192 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-07-31 17:45:51,192 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-07-31 17:45:51,192 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,193 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:51,194 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98145.0, 路径长度: 66
2025-07-31 17:45:51,194 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}
2025-07-31 17:45:51,194 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:45:51,194 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:51,194 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:51,194 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 109111.0
2025-07-31 17:45:51,254 - ExploitationExpert - INFO - res_population_num: 7
2025-07-31 17:45:51,254 - ExploitationExpert - INFO - res_population_costs: [9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9524]
2025-07-31 17:45:51,254 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:51,257 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:51,257 - ExploitationExpert - INFO - populations: [{'tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}, {'tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}, {'tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}, {'tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}, {'tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}, {'tour': [7, 19, 11, 5, 3, 2, 18, 4, 8, 21, 6, 1, 55, 61, 53, 59, 26, 58, 35, 60, 57, 54, 52, 63, 14, 15, 9, 12, 23, 20, 32, 30, 64, 29, 39, 38, 43, 41, 16, 37, 47, 44, 22, 40, 33, 17, 31, 56, 25, 51, 36, 27, 50, 24, 45, 65, 0, 13, 34, 42, 62, 28, 49, 48, 46, 10], 'cur_cost': 82641.0}, {'tour': [7, 5, 4, 22, 8, 1, 18, 9, 11, 2, 3, 14, 10, 61, 27, 37, 59, 56, 58, 60, 28, 64, 54, 33, 52, 63, 29, 15, 20, 12, 17, 13, 16, 21, 19, 6, 55, 47, 44, 36, 40, 41, 31, 25, 51, 50, 32, 0, 42, 62, 26, 46, 39, 57, 49, 43, 35, 45, 24, 30, 53, 48, 38, 23, 65, 34], 'cur_cost': 85867.0}, {'tour': [0, 18, 46, 33, 60, 15, 22, 34, 3, 57, 47, 48, 4, 39, 51, 63, 43, 30, 55, 10, 5, 53, 44, 14, 24, 21, 59, 27, 20, 26, 36, 45, 8, 62, 41, 11, 7, 31, 12, 49, 40, 56, 65, 42, 25, 35, 2, 29, 1, 37, 54, 52, 17, 16, 64, 28, 58, 23, 19, 9, 50, 6, 13, 61, 32, 38], 'cur_cost': 112089.0}, {'tour': [63, 51, 47, 13, 7, 64, 12, 43, 38, 31, 21, 29, 18, 19, 0, 46, 28, 40, 61, 15, 30, 35, 58, 39, 59, 45, 27, 1, 23, 8, 33, 57, 44, 26, 25, 36, 53, 22, 16, 41, 20, 34, 2, 4, 65, 56, 48, 6, 5, 3, 55, 62, 50, 49, 42, 60, 54, 17, 24, 37, 9, 11, 32, 52, 14, 10], 'cur_cost': 96572.0}, {'tour': [60, 24, 58, 42, 5, 19, 50, 53, 3, 43, 30, 46, 36, 7, 41, 48, 9, 6, 59, 14, 37, 52, 40, 39, 63, 55, 1, 65, 34, 17, 35, 23, 11, 29, 10, 18, 62, 22, 27, 20, 44, 49, 56, 33, 0, 12, 25, 15, 54, 13, 64, 31, 47, 26, 51, 32, 21, 57, 16, 61, 38, 2, 4, 28, 45, 8], 'cur_cost': 118416.0}]
2025-07-31 17:45:51,258 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-07-31 17:45:51,258 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-31 17:45:51,259 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}
2025-07-31 17:45:51,259 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-07-31 17:45:51,259 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-07-31 17:45:51,259 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,269 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:51,269 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,270 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63745.0, 路径长度: 66
2025-07-31 17:45:51,270 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}
2025-07-31 17:45:51,270 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-07-31 17:45:51,270 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-31 17:45:51,270 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:51,277 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:51,277 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:51,278 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55853.0, 路径长度: 66
2025-07-31 17:45:51,278 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}
2025-07-31 17:45:51,278 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-31 17:45:51,278 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:51,278 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:51,279 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107850.0
2025-07-31 17:45:51,346 - ExploitationExpert - INFO - res_population_num: 8
2025-07-31 17:45:51,347 - ExploitationExpert - INFO - res_population_costs: [9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9524, 9521]
2025-07-31 17:45:51,347 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:51,350 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:51,350 - ExploitationExpert - INFO - populations: [{'tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}, {'tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}, {'tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}, {'tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}, {'tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}, {'tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}, {'tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}, {'tour': array([18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65,
       26,  2, 51, 64,  3,  1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29,  8,
       45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47,
       11, 36,  9, 28,  4, 60,  6, 37,  0, 21, 50,  5, 56, 31,  7],
      dtype=int64), 'cur_cost': 107850.0}, {'tour': [63, 51, 47, 13, 7, 64, 12, 43, 38, 31, 21, 29, 18, 19, 0, 46, 28, 40, 61, 15, 30, 35, 58, 39, 59, 45, 27, 1, 23, 8, 33, 57, 44, 26, 25, 36, 53, 22, 16, 41, 20, 34, 2, 4, 65, 56, 48, 6, 5, 3, 55, 62, 50, 49, 42, 60, 54, 17, 24, 37, 9, 11, 32, 52, 14, 10], 'cur_cost': 96572.0}, {'tour': [60, 24, 58, 42, 5, 19, 50, 53, 3, 43, 30, 46, 36, 7, 41, 48, 9, 6, 59, 14, 37, 52, 40, 39, 63, 55, 1, 65, 34, 17, 35, 23, 11, 29, 10, 18, 62, 22, 27, 20, 44, 49, 56, 33, 0, 12, 25, 15, 54, 13, 64, 31, 47, 26, 51, 32, 21, 57, 16, 61, 38, 2, 4, 28, 45, 8], 'cur_cost': 118416.0}]
2025-07-31 17:45:51,351 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:51,351 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-31 17:45:51,352 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65,
       26,  2, 51, 64,  3,  1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29,  8,
       45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47,
       11, 36,  9, 28,  4, 60,  6, 37,  0, 21, 50,  5, 56, 31,  7],
      dtype=int64), 'cur_cost': 107850.0}
2025-07-31 17:45:51,352 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-07-31 17:45:51,352 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:51,352 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:51,353 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108163.0
2025-07-31 17:45:51,427 - ExploitationExpert - INFO - res_population_num: 9
2025-07-31 17:45:51,427 - ExploitationExpert - INFO - res_population_costs: [9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9524, 9521, 9521]
2025-07-31 17:45:51,428 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-31 17:45:51,432 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:51,432 - ExploitationExpert - INFO - populations: [{'tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}, {'tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}, {'tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}, {'tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}, {'tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}, {'tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}, {'tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}, {'tour': array([18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65,
       26,  2, 51, 64,  3,  1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29,  8,
       45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47,
       11, 36,  9, 28,  4, 60,  6, 37,  0, 21, 50,  5, 56, 31,  7],
      dtype=int64), 'cur_cost': 107850.0}, {'tour': array([21, 15, 34,  6, 46, 31, 55,  3,  0, 29, 59,  7, 52, 25, 23, 57, 27,
       63, 36, 64,  2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47,  4, 50,
       49, 33, 37, 58, 54, 10, 19,  8, 62, 12, 24, 40,  9, 42,  1, 41, 18,
       44, 65, 60, 51, 22, 26,  5, 61, 11, 39, 32, 53, 45, 30, 56],
      dtype=int64), 'cur_cost': 108163.0}, {'tour': [60, 24, 58, 42, 5, 19, 50, 53, 3, 43, 30, 46, 36, 7, 41, 48, 9, 6, 59, 14, 37, 52, 40, 39, 63, 55, 1, 65, 34, 17, 35, 23, 11, 29, 10, 18, 62, 22, 27, 20, 44, 49, 56, 33, 0, 12, 25, 15, 54, 13, 64, 31, 47, 26, 51, 32, 21, 57, 16, 61, 38, 2, 4, 28, 45, 8], 'cur_cost': 118416.0}]
2025-07-31 17:45:51,433 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:51,433 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-31 17:45:51,434 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([21, 15, 34,  6, 46, 31, 55,  3,  0, 29, 59,  7, 52, 25, 23, 57, 27,
       63, 36, 64,  2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47,  4, 50,
       49, 33, 37, 58, 54, 10, 19,  8, 62, 12, 24, 40,  9, 42,  1, 41, 18,
       44, 65, 60, 51, 22, 26,  5, 61, 11, 39, 32, 53, 45, 30, 56],
      dtype=int64), 'cur_cost': 108163.0}
2025-07-31 17:45:51,434 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-31 17:45:51,434 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:51,434 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:51,434 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 98988.0
2025-07-31 17:45:51,496 - ExploitationExpert - INFO - res_population_num: 9
2025-07-31 17:45:51,496 - ExploitationExpert - INFO - res_population_costs: [9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9524, 9521, 9521]
2025-07-31 17:45:51,496 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-31 17:45:51,500 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:51,500 - ExploitationExpert - INFO - populations: [{'tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}, {'tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}, {'tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}, {'tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}, {'tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}, {'tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}, {'tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}, {'tour': array([18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65,
       26,  2, 51, 64,  3,  1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29,  8,
       45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47,
       11, 36,  9, 28,  4, 60,  6, 37,  0, 21, 50,  5, 56, 31,  7],
      dtype=int64), 'cur_cost': 107850.0}, {'tour': array([21, 15, 34,  6, 46, 31, 55,  3,  0, 29, 59,  7, 52, 25, 23, 57, 27,
       63, 36, 64,  2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47,  4, 50,
       49, 33, 37, 58, 54, 10, 19,  8, 62, 12, 24, 40,  9, 42,  1, 41, 18,
       44, 65, 60, 51, 22, 26,  5, 61, 11, 39, 32, 53, 45, 30, 56],
      dtype=int64), 'cur_cost': 108163.0}, {'tour': array([27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52,  6,  8, 54, 40, 33,
       38,  9, 56, 61, 57,  2, 37, 30, 60, 16, 39, 15, 50, 59,  1, 34, 32,
        7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53,
       25, 45,  3, 47, 21, 17, 43, 48, 28,  0,  5, 63,  4, 19, 41],
      dtype=int64), 'cur_cost': 98988.0}]
2025-07-31 17:45:51,503 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:51,503 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-31 17:45:51,503 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52,  6,  8, 54, 40, 33,
       38,  9, 56, 61, 57,  2, 37, 30, 60, 16, 39, 15, 50, 59,  1, 34, 32,
        7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53,
       25, 45,  3, 47, 21, 17, 43, 48, 28,  0,  5, 63,  4, 19, 41],
      dtype=int64), 'cur_cost': 98988.0}
2025-07-31 17:45:51,504 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [39, 43, 20, 32, 9, 5, 14, 24, 0, 58, 53, 55, 60, 11, 13, 27, 35, 22, 19, 28, 8, 57, 63, 21, 23, 2, 65, 18, 17, 3, 37, 1, 59, 49, 47, 16, 7, 52, 6, 15, 4, 10, 31, 12, 36, 30, 25, 33, 34, 44, 38, 41, 40, 50, 46, 42, 26, 29, 45, 56, 62, 64, 61, 54, 48, 51], 'cur_cost': 59179.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [45, 37, 33, 3, 23, 16, 12, 30, 6, 61, 52, 14, 21, 11, 9, 17, 28, 10, 56, 2, 22, 1, 20, 27, 35, 19, 25, 26, 7, 63, 57, 8, 55, 53, 18, 0, 4, 5, 13, 43, 40, 49, 39, 41, 48, 38, 51, 44, 42, 46, 32, 15, 36, 24, 31, 29, 50, 58, 59, 60, 54, 64, 65, 62, 47, 34], 'cur_cost': 55196.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [45, 7, 19, 11, 23, 16, 14, 30, 6, 8, 52, 1, 55, 13, 61, 53, 37, 10, 36, 28, 22, 57, 63, 27, 65, 2, 25, 26, 17, 20, 12, 31, 59, 49, 47, 0, 4, 5, 44, 15, 40, 41, 39, 35, 48, 38, 51, 56, 34, 46, 32, 62, 50, 24, 64, 42, 58, 29, 18, 60, 54, 9, 3, 43, 21, 33], 'cur_cost': 97546.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [63, 43, 19, 3, 22, 23, 16, 24, 17, 58, 53, 55, 14, 39, 9, 59, 35, 26, 56, 36, 57, 1, 20, 21, 65, 31, 27, 18, 7, 12, 37, 8, 64, 5, 4, 2, 6, 52, 13, 49, 47, 10, 51, 41, 33, 30, 32, 44, 25, 62, 38, 15, 40, 50, 46, 29, 0, 45, 34, 42, 54, 28, 61, 48, 60, 11], 'cur_cost': 98145.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 29,  8, 33, 61, 51, 40,  7, 45, 60, 48, 20, 59, 22, 58, 39,  2,
       11, 18, 17, 15, 24, 64, 52, 28, 44, 14, 56, 38, 31, 37, 13, 16,  4,
       47, 63, 34, 53,  1, 57, 10, 54, 35,  3,  6, 65, 19, 12, 43, 62, 21,
       23, 26, 27, 36, 25,  9, 32, 49,  0, 30, 55, 42,  5, 46, 50],
      dtype=int64), 'cur_cost': 109111.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65,
       26,  2, 51, 64,  3,  1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29,  8,
       45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47,
       11, 36,  9, 28,  4, 60,  6, 37,  0, 21, 50,  5, 56, 31,  7],
      dtype=int64), 'cur_cost': 107850.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 15, 34,  6, 46, 31, 55,  3,  0, 29, 59,  7, 52, 25, 23, 57, 27,
       63, 36, 64,  2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47,  4, 50,
       49, 33, 37, 58, 54, 10, 19,  8, 62, 12, 24, 40,  9, 42,  1, 41, 18,
       44, 65, 60, 51, 22, 26,  5, 61, 11, 39, 32, 53, 45, 30, 56],
      dtype=int64), 'cur_cost': 108163.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52,  6,  8, 54, 40, 33,
       38,  9, 56, 61, 57,  2, 37, 30, 60, 16, 39, 15, 50, 59,  1, 34, 32,
        7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53,
       25, 45,  3, 47, 21, 17, 43, 48, 28,  0,  5, 63,  4, 19, 41],
      dtype=int64), 'cur_cost': 98988.0}}]
2025-07-31 17:45:51,504 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:45:51,504 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:51,522 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=55196.0, 多样性=0.961
2025-07-31 17:45:51,522 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-31 17:45:51,522 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-07-31 17:45:51,523 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:45:51,524 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -1.3120930136500182, 'best_improvement': -3.4988181595892085}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.23060344827586213}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.9124579124579124, 'new_diversity': 0.9124579124579124, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-31 17:45:51,525 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-31 17:45:51,525 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-31 17:45:51,525 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-31 17:45:51,526 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:51,544 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=55196.0, 多样性=0.961
2025-07-31 17:45:51,544 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:45:51,547 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.037
2025-07-31 17:45:51,547 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:45:51,549 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.912
2025-07-31 17:45:51,551 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-07-31 17:45:51,551 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955151.551669, 'status': 'default_fallback'}}
2025-07-31 17:45:51,551 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:45:51,552 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:45:51,552 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 55196.0
  • mean_cost: 85377.6
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:45:51,552 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:45:51,552 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:45:53,227 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploring. Diversity is at 0.5, but previous iteration showed cost deterioration. Prioritize exploration across most individuals."
}
```
2025-07-31 17:45:53,227 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:45:53,227 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:53,227 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:53,227 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploring. Diversity is at 0.5, but previous iteration showed cost deterioration. Prioritize exploration across most individuals."
}
```
2025-07-31 17:45:53,227 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:45:53,227 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:53,227 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploring. Diversity is at 0.5, but previous iteration showed cost deterioration. Prioritize exploration across most individuals."
}
```
2025-07-31 17:45:53,229 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:45:53,229 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:45:53,229 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:45:53,229 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,231 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:53,231 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,231 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14743.0, 路径长度: 66
2025-07-31 17:45:53,232 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}
2025-07-31 17:45:53,232 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:45:53,232 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:45:53,232 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,234 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:53,234 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,234 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12787.0, 路径长度: 66
2025-07-31 17:45:53,236 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}
2025-07-31 17:45:53,236 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:45:53,236 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:45:53,236 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,241 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:53,241 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,241 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64465.0, 路径长度: 66
2025-07-31 17:45:53,241 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}
2025-07-31 17:45:53,242 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-07-31 17:45:53,242 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-07-31 17:45:53,242 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,247 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-31 17:45:53,247 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,247 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61336.0, 路径长度: 66
2025-07-31 17:45:53,247 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}
2025-07-31 17:45:53,248 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:45:53,248 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:53,248 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:53,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99014.0
2025-07-31 17:45:53,306 - ExploitationExpert - INFO - res_population_num: 9
2025-07-31 17:45:53,306 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0]
2025-07-31 17:45:53,306 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64)]
2025-07-31 17:45:53,310 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:53,310 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}, {'tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}, {'tour': [35, 5, 54, 63, 4, 9, 53, 61, 22, 23, 32, 13, 36, 3, 11, 7, 60, 2, 57, 39, 21, 12, 1, 52, 0, 15, 17, 28, 43, 16, 27, 40, 20, 19, 37, 14, 49, 45, 25, 30, 29, 34, 48, 50, 44, 31, 8, 10, 65, 18, 41, 26, 47, 38, 51, 42, 46, 24, 33, 55, 56, 58, 62, 59, 64, 6], 'cur_cost': 63745.0}, {'tour': [42, 43, 49, 25, 22, 12, 34, 27, 26, 17, 6, 7, 60, 64, 8, 15, 0, 11, 4, 54, 3, 65, 57, 61, 62, 53, 5, 58, 2, 55, 20, 31, 23, 21, 13, 29, 9, 24, 18, 40, 14, 28, 1, 59, 39, 51, 19, 32, 37, 36, 33, 10, 16, 46, 44, 50, 45, 47, 41, 38, 30, 35, 56, 52, 63, 48], 'cur_cost': 55853.0}, {'tour': [18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65, 26, 2, 51, 64, 3, 1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29, 8, 45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47, 11, 36, 9, 28, 4, 60, 6, 37, 0, 21, 50, 5, 56, 31, 7], 'cur_cost': 107850.0}, {'tour': [21, 15, 34, 6, 46, 31, 55, 3, 0, 29, 59, 7, 52, 25, 23, 57, 27, 63, 36, 64, 2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47, 4, 50, 49, 33, 37, 58, 54, 10, 19, 8, 62, 12, 24, 40, 9, 42, 1, 41, 18, 44, 65, 60, 51, 22, 26, 5, 61, 11, 39, 32, 53, 45, 30, 56], 'cur_cost': 108163.0}, {'tour': [27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52, 6, 8, 54, 40, 33, 38, 9, 56, 61, 57, 2, 37, 30, 60, 16, 39, 15, 50, 59, 1, 34, 32, 7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53, 25, 45, 3, 47, 21, 17, 43, 48, 28, 0, 5, 63, 4, 19, 41], 'cur_cost': 98988.0}]
2025-07-31 17:45:53,310 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-07-31 17:45:53,311 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-31 17:45:53,311 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}
2025-07-31 17:45:53,311 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-07-31 17:45:53,311 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-07-31 17:45:53,311 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,313 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:53,313 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 74223.0, 路径长度: 66
2025-07-31 17:45:53,313 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}
2025-07-31 17:45:53,314 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-07-31 17:45:53,314 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:53,314 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:53,314 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104567.0
2025-07-31 17:45:53,376 - ExploitationExpert - INFO - res_population_num: 11
2025-07-31 17:45:53,376 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:53,376 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:53,380 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:53,380 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}, {'tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': array([12,  4, 11, 20,  7, 30,  2, 50, 37,  9, 26, 60, 53, 58, 29, 64, 51,
       31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36,
       52, 35,  6, 56, 63,  8, 55, 33,  5, 13, 10, 17, 46, 48, 28, 62, 23,
       41, 54, 61, 34,  3, 39, 21, 22,  1, 18, 24, 32,  0, 16, 14],
      dtype=int64), 'cur_cost': 104567.0}, {'tour': [18, 40, 22, 58, 24, 44, 57, 33, 14, 16, 48, 32, 52, 55, 12, 46, 65, 26, 2, 51, 64, 3, 1, 35, 27, 10, 43, 41, 23, 17, 13, 20, 29, 8, 45, 54, 61, 49, 38, 53, 42, 25, 30, 15, 19, 62, 63, 59, 39, 34, 47, 11, 36, 9, 28, 4, 60, 6, 37, 0, 21, 50, 5, 56, 31, 7], 'cur_cost': 107850.0}, {'tour': [21, 15, 34, 6, 46, 31, 55, 3, 0, 29, 59, 7, 52, 25, 23, 57, 27, 63, 36, 64, 2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47, 4, 50, 49, 33, 37, 58, 54, 10, 19, 8, 62, 12, 24, 40, 9, 42, 1, 41, 18, 44, 65, 60, 51, 22, 26, 5, 61, 11, 39, 32, 53, 45, 30, 56], 'cur_cost': 108163.0}, {'tour': [27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52, 6, 8, 54, 40, 33, 38, 9, 56, 61, 57, 2, 37, 30, 60, 16, 39, 15, 50, 59, 1, 34, 32, 7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53, 25, 45, 3, 47, 21, 17, 43, 48, 28, 0, 5, 63, 4, 19, 41], 'cur_cost': 98988.0}]
2025-07-31 17:45:53,381 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:53,381 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-31 17:45:53,381 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([12,  4, 11, 20,  7, 30,  2, 50, 37,  9, 26, 60, 53, 58, 29, 64, 51,
       31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36,
       52, 35,  6, 56, 63,  8, 55, 33,  5, 13, 10, 17, 46, 48, 28, 62, 23,
       41, 54, 61, 34,  3, 39, 21, 22,  1, 18, 24, 32,  0, 16, 14],
      dtype=int64), 'cur_cost': 104567.0}
2025-07-31 17:45:53,381 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-31 17:45:53,382 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:53,382 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:53,382 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 97186.0
2025-07-31 17:45:53,445 - ExploitationExpert - INFO - res_population_num: 11
2025-07-31 17:45:53,445 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:53,445 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:53,449 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:53,449 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}, {'tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': array([12,  4, 11, 20,  7, 30,  2, 50, 37,  9, 26, 60, 53, 58, 29, 64, 51,
       31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36,
       52, 35,  6, 56, 63,  8, 55, 33,  5, 13, 10, 17, 46, 48, 28, 62, 23,
       41, 54, 61, 34,  3, 39, 21, 22,  1, 18, 24, 32,  0, 16, 14],
      dtype=int64), 'cur_cost': 104567.0}, {'tour': array([55, 36, 29, 33, 25, 16, 22,  4, 40, 14, 61, 12, 35, 32, 17,  5, 27,
       13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21,  8, 24, 28, 53, 15,
        7, 34, 57, 26,  1, 39, 50, 63, 64, 48,  2, 58, 20, 51, 45, 18, 38,
       37, 19, 47, 52, 56, 30, 54,  3, 46, 44, 11,  9,  0, 62,  6],
      dtype=int64), 'cur_cost': 97186.0}, {'tour': [21, 15, 34, 6, 46, 31, 55, 3, 0, 29, 59, 7, 52, 25, 23, 57, 27, 63, 36, 64, 2, 35, 20, 16, 14, 17, 28, 13, 38, 48, 43, 47, 4, 50, 49, 33, 37, 58, 54, 10, 19, 8, 62, 12, 24, 40, 9, 42, 1, 41, 18, 44, 65, 60, 51, 22, 26, 5, 61, 11, 39, 32, 53, 45, 30, 56], 'cur_cost': 108163.0}, {'tour': [27, 51, 36, 58, 20, 31, 24, 62, 46, 42, 14, 52, 6, 8, 54, 40, 33, 38, 9, 56, 61, 57, 2, 37, 30, 60, 16, 39, 15, 50, 59, 1, 34, 32, 7, 26, 11, 12, 23, 22, 49, 10, 64, 44, 13, 35, 29, 55, 18, 65, 53, 25, 45, 3, 47, 21, 17, 43, 48, 28, 0, 5, 63, 4, 19, 41], 'cur_cost': 98988.0}]
2025-07-31 17:45:53,451 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:53,451 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-31 17:45:53,452 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([55, 36, 29, 33, 25, 16, 22,  4, 40, 14, 61, 12, 35, 32, 17,  5, 27,
       13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21,  8, 24, 28, 53, 15,
        7, 34, 57, 26,  1, 39, 50, 63, 64, 48,  2, 58, 20, 51, 45, 18, 38,
       37, 19, 47, 52, 56, 30, 54,  3, 46, 44, 11,  9,  0, 62,  6],
      dtype=int64), 'cur_cost': 97186.0}
2025-07-31 17:45:53,452 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-07-31 17:45:53,452 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-31 17:45:53,452 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:53,454 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:53,454 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:53,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14768.0, 路径长度: 66
2025-07-31 17:45:53,455 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}
2025-07-31 17:45:53,455 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-31 17:45:53,455 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:53,455 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:53,456 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104391.0
2025-07-31 17:45:53,522 - ExploitationExpert - INFO - res_population_num: 11
2025-07-31 17:45:53,522 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:53,522 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:53,526 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:53,526 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}, {'tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': array([12,  4, 11, 20,  7, 30,  2, 50, 37,  9, 26, 60, 53, 58, 29, 64, 51,
       31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36,
       52, 35,  6, 56, 63,  8, 55, 33,  5, 13, 10, 17, 46, 48, 28, 62, 23,
       41, 54, 61, 34,  3, 39, 21, 22,  1, 18, 24, 32,  0, 16, 14],
      dtype=int64), 'cur_cost': 104567.0}, {'tour': array([55, 36, 29, 33, 25, 16, 22,  4, 40, 14, 61, 12, 35, 32, 17,  5, 27,
       13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21,  8, 24, 28, 53, 15,
        7, 34, 57, 26,  1, 39, 50, 63, 64, 48,  2, 58, 20, 51, 45, 18, 38,
       37, 19, 47, 52, 56, 30, 54,  3, 46, 44, 11,  9,  0, 62,  6],
      dtype=int64), 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': array([46, 50, 28,  4, 27, 45, 13, 23, 41, 33,  2, 39, 53, 57, 47, 58, 40,
       55, 32, 12, 51, 65,  3, 54, 26, 31, 20, 21,  9, 14, 42,  7, 34, 19,
       17, 11,  8, 60,  6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62,
       49,  0, 56, 52, 29, 36,  5, 22,  1, 48, 61, 24, 37, 35, 44],
      dtype=int64), 'cur_cost': 104391.0}]
2025-07-31 17:45:53,528 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:53,528 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-31 17:45:53,528 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([46, 50, 28,  4, 27, 45, 13, 23, 41, 33,  2, 39, 53, 57, 47, 58, 40,
       55, 32, 12, 51, 65,  3, 54, 26, 31, 20, 21,  9, 14, 42,  7, 34, 19,
       17, 11,  8, 60,  6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62,
       49,  0, 56, 52, 29, 36,  5, 22,  1, 48, 61, 24, 37, 35, 44],
      dtype=int64), 'cur_cost': 104391.0}
2025-07-31 17:45:53,529 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 8, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14743.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  5, 14, 37, 22, 52,  7,  1, 51, 40, 32, 28, 64, 60,  4,  3,  9,
       12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45,  8,
       17, 15, 50, 13, 59, 34,  6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44,
       16, 36, 27, 63, 62, 39, 19, 55, 21, 57,  0, 61, 35, 29, 48],
      dtype=int64), 'cur_cost': 99014.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  4, 11, 20,  7, 30,  2, 50, 37,  9, 26, 60, 53, 58, 29, 64, 51,
       31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36,
       52, 35,  6, 56, 63,  8, 55, 33,  5, 13, 10, 17, 46, 48, 28, 62, 23,
       41, 54, 61, 34,  3, 39, 21, 22,  1, 18, 24, 32,  0, 16, 14],
      dtype=int64), 'cur_cost': 104567.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 36, 29, 33, 25, 16, 22,  4, 40, 14, 61, 12, 35, 32, 17,  5, 27,
       13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21,  8, 24, 28, 53, 15,
        7, 34, 57, 26,  1, 39, 50, 63, 64, 48,  2, 58, 20, 51, 45, 18, 38,
       37, 19, 47, 52, 56, 30, 54,  3, 46, 44, 11,  9,  0, 62,  6],
      dtype=int64), 'cur_cost': 97186.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 50, 28,  4, 27, 45, 13, 23, 41, 33,  2, 39, 53, 57, 47, 58, 40,
       55, 32, 12, 51, 65,  3, 54, 26, 31, 20, 21,  9, 14, 42,  7, 34, 19,
       17, 11,  8, 60,  6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62,
       49,  0, 56, 52, 29, 36,  5, 22,  1, 48, 61, 24, 37, 35, 44],
      dtype=int64), 'cur_cost': 104391.0}}]
2025-07-31 17:45:53,529 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:45:53,530 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:53,546 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12787.0, 多样性=0.902
2025-07-31 17:45:53,546 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-31 17:45:53,546 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-07-31 17:45:53,546 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:45:53,548 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.3505360353703775, 'best_improvement': 0.768334661932024}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.06129597197898425}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8798898071625344, 'new_diversity': 0.8798898071625344, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:45:53,549 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-31 17:45:53,550 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-31 17:45:53,550 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-31 17:45:53,550 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:53,568 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12787.0, 多样性=0.902
2025-07-31 17:45:53,568 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:45:53,571 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.044
2025-07-31 17:45:53,572 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:45:53,576 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.880
2025-07-31 17:45:53,580 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-07-31 17:45:53,580 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955153.5806966, 'status': 'default_fallback'}}
2025-07-31 17:45:53,581 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:45:53,581 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:45:53,581 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12787.0
  • mean_cost: 64748.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploitation
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:45:53,581 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:45:53,582 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:45:55,284 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.20,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, significant improvement in the last iteration. High convergence and balance focus. Prioritize exploiting promising regions."
}
```
2025-07-31 17:45:55,284 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:45:55,284 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:55,285 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:55,285 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.20,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, significant improvement in the last iteration. High convergence and balance focus. Prioritize exploiting promising regions."
}
```
2025-07-31 17:45:55,285 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:45:55,285 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-31 17:45:55,285 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.20,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, significant improvement in the last iteration. High convergence and balance focus. Prioritize exploiting promising regions."
}
```
2025-07-31 17:45:55,285 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:45:55,285 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-07-31 17:45:55,285 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,285 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,285 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 98282.0
2025-07-31 17:45:55,349 - ExploitationExpert - INFO - res_population_num: 14
2025-07-31 17:45:55,349 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521]
2025-07-31 17:45:55,349 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-31 17:45:55,355 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,355 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': [0, 23, 17, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12787.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': [2, 5, 14, 37, 22, 52, 7, 1, 51, 40, 32, 28, 64, 60, 4, 3, 9, 12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45, 8, 17, 15, 50, 13, 59, 34, 6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44, 16, 36, 27, 63, 62, 39, 19, 55, 21, 57, 0, 61, 35, 29, 48], 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,356 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:55,357 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-31 17:45:55,357 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}
2025-07-31 17:45:55,357 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-31 17:45:55,357 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,357 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,358 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 114497.0
2025-07-31 17:45:55,427 - ExploitationExpert - INFO - res_population_num: 15
2025-07-31 17:45:55,428 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,428 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:55,433 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,433 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': [15, 37, 33, 0, 6, 18, 36, 11, 12, 29, 7, 52, 1, 63, 56, 58, 60, 9, 64, 2, 65, 21, 17, 25, 4, 54, 22, 19, 30, 31, 14, 34, 32, 27, 8, 3, 16, 43, 41, 47, 44, 46, 50, 20, 28, 23, 5, 59, 62, 40, 45, 13, 39, 42, 49, 38, 35, 48, 26, 10, 53, 57, 61, 55, 51, 24], 'cur_cost': 64465.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': [2, 5, 14, 37, 22, 52, 7, 1, 51, 40, 32, 28, 64, 60, 4, 3, 9, 12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45, 8, 17, 15, 50, 13, 59, 34, 6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44, 16, 36, 27, 63, 62, 39, 19, 55, 21, 57, 0, 61, 35, 29, 48], 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,435 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:55,435 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-31 17:45:55,435 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}
2025-07-31 17:45:55,435 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-07-31 17:45:55,435 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,436 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,436 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 104323.0
2025-07-31 17:45:55,502 - ExploitationExpert - INFO - res_population_num: 15
2025-07-31 17:45:55,502 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,502 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:55,508 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,508 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': [15, 26, 20, 9, 18, 8, 63, 60, 4, 57, 55, 11, 22, 30, 37, 34, 16, 2, 21, 19, 13, 14, 10, 27, 6, 61, 64, 59, 23, 31, 33, 49, 38, 40, 25, 7, 24, 28, 5, 53, 52, 39, 42, 41, 36, 29, 35, 43, 17, 3, 12, 48, 44, 50, 46, 1, 62, 58, 54, 56, 65, 0, 47, 45, 51, 32], 'cur_cost': 61336.0}, {'tour': [2, 5, 14, 37, 22, 52, 7, 1, 51, 40, 32, 28, 64, 60, 4, 3, 9, 12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45, 8, 17, 15, 50, 13, 59, 34, 6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44, 16, 36, 27, 63, 62, 39, 19, 55, 21, 57, 0, 61, 35, 29, 48], 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,510 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-31 17:45:55,510 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-31 17:45:55,510 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}
2025-07-31 17:45:55,510 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:45:55,511 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,511 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,511 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 113101.0
2025-07-31 17:45:55,579 - ExploitationExpert - INFO - res_population_num: 17
2025-07-31 17:45:55,579 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,579 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:55,586 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,586 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': [2, 5, 14, 37, 22, 52, 7, 1, 51, 40, 32, 28, 64, 60, 4, 3, 9, 12, 58, 49, 18, 47, 56, 11, 31, 30, 65, 24, 10, 54, 33, 25, 45, 8, 17, 15, 50, 13, 59, 34, 6, 42, 23, 41, 26, 20, 38, 46, 53, 43, 44, 16, 36, 27, 63, 62, 39, 19, 55, 21, 57, 0, 61, 35, 29, 48], 'cur_cost': 99014.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,588 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:55,588 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-31 17:45:55,588 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}
2025-07-31 17:45:55,588 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:45:55,588 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,588 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,589 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 107189.0
2025-07-31 17:45:55,659 - ExploitationExpert - INFO - res_population_num: 18
2025-07-31 17:45:55,659 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,659 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-31 17:45:55,665 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,665 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': [35, 18, 17, 7, 3, 22, 11, 60, 15, 14, 8, 21, 1, 10, 27, 37, 25, 9, 64, 19, 28, 30, 34, 33, 4, 24, 29, 32, 43, 20, 12, 38, 5, 13, 6, 2, 45, 55, 61, 53, 62, 59, 56, 58, 23, 63, 57, 31, 40, 52, 48, 44, 49, 46, 42, 51, 50, 39, 0, 47, 41, 16, 36, 65, 26, 54], 'cur_cost': 74223.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,668 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:55,668 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-31 17:45:55,668 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}
2025-07-31 17:45:55,668 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-07-31 17:45:55,668 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,669 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,669 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 98592.0
2025-07-31 17:45:55,749 - ExploitationExpert - INFO - res_population_num: 18
2025-07-31 17:45:55,749 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,749 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-31 17:45:55,755 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,755 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}, {'tour': [12, 4, 11, 20, 7, 30, 2, 50, 37, 9, 26, 60, 53, 58, 29, 64, 51, 31, 44, 27, 59, 45, 15, 49, 65, 57, 25, 40, 47, 42, 38, 19, 43, 36, 52, 35, 6, 56, 63, 8, 55, 33, 5, 13, 10, 17, 46, 48, 28, 62, 23, 41, 54, 61, 34, 3, 39, 21, 22, 1, 18, 24, 32, 0, 16, 14], 'cur_cost': 104567.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,757 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-31 17:45:55,758 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-31 17:45:55,758 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}
2025-07-31 17:45:55,758 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-07-31 17:45:55,758 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,758 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,759 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 102706.0
2025-07-31 17:45:55,830 - ExploitationExpert - INFO - res_population_num: 19
2025-07-31 17:45:55,831 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,831 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:55,838 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,838 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}, {'tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}, {'tour': [55, 36, 29, 33, 25, 16, 22, 4, 40, 14, 61, 12, 35, 32, 17, 5, 27, 13, 60, 49, 41, 42, 65, 59, 31, 23, 43, 10, 21, 8, 24, 28, 53, 15, 7, 34, 57, 26, 1, 39, 50, 63, 64, 48, 2, 58, 20, 51, 45, 18, 38, 37, 19, 47, 52, 56, 30, 54, 3, 46, 44, 11, 9, 0, 62, 6], 'cur_cost': 97186.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,841 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:55,841 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-31 17:45:55,841 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}
2025-07-31 17:45:55,842 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-31 17:45:55,842 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,842 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,842 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102148.0
2025-07-31 17:45:55,917 - ExploitationExpert - INFO - res_population_num: 19
2025-07-31 17:45:55,917 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:55,917 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:55,923 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:55,923 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}, {'tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}, {'tour': array([60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51,
        7,  9,  1, 54, 48, 28, 31, 27,  6, 62, 45, 22, 53, 19, 24,  5, 21,
       16,  8, 38, 15, 18, 44, 33, 30,  4,  0, 63, 56, 13, 17, 10, 41, 50,
       47, 52, 32, 25,  2, 35, 64,  3, 59, 36, 58, 12, 26, 57, 20],
      dtype=int64), 'cur_cost': 102148.0}, {'tour': [0, 20, 4, 12, 22, 23, 16, 18, 17, 15, 14, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14768.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:55,926 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:55,926 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-31 17:45:55,927 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51,
        7,  9,  1, 54, 48, 28, 31, 27,  6, 62, 45, 22, 53, 19, 24,  5, 21,
       16,  8, 38, 15, 18, 44, 33, 30,  4,  0, 63, 56, 13, 17, 10, 41, 50,
       47, 52, 32, 25,  2, 35, 64,  3, 59, 36, 58, 12, 26, 57, 20],
      dtype=int64), 'cur_cost': 102148.0}
2025-07-31 17:45:55,927 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-07-31 17:45:55,927 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:55,927 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:55,928 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106786.0
2025-07-31 17:45:56,825 - ExploitationExpert - INFO - res_population_num: 21
2025-07-31 17:45:56,825 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:56,825 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:56,834 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:56,834 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}, {'tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}, {'tour': array([60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51,
        7,  9,  1, 54, 48, 28, 31, 27,  6, 62, 45, 22, 53, 19, 24,  5, 21,
       16,  8, 38, 15, 18, 44, 33, 30,  4,  0, 63, 56, 13, 17, 10, 41, 50,
       47, 52, 32, 25,  2, 35, 64,  3, 59, 36, 58, 12, 26, 57, 20],
      dtype=int64), 'cur_cost': 102148.0}, {'tour': array([64, 23, 27,  3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33,
        2, 13, 39, 25, 58, 38, 41, 11,  5, 16, 51, 42, 44, 55, 46,  0, 43,
       61,  6, 19, 32, 24, 52,  8, 57,  9, 48, 62, 59, 47, 29,  4, 26, 63,
       36, 35, 21,  1, 15, 30, 40, 18, 20, 49, 14, 31, 65,  7, 10],
      dtype=int64), 'cur_cost': 106786.0}, {'tour': [46, 50, 28, 4, 27, 45, 13, 23, 41, 33, 2, 39, 53, 57, 47, 58, 40, 55, 32, 12, 51, 65, 3, 54, 26, 31, 20, 21, 9, 14, 42, 7, 34, 19, 17, 11, 8, 60, 6, 15, 18, 30, 64, 10, 38, 25, 63, 59, 43, 16, 62, 49, 0, 56, 52, 29, 36, 5, 22, 1, 48, 61, 24, 37, 35, 44], 'cur_cost': 104391.0}]
2025-07-31 17:45:56,838 - ExploitationExpert - INFO - 局部搜索耗时: 0.91秒
2025-07-31 17:45:56,838 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-07-31 17:45:56,838 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([64, 23, 27,  3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33,
        2, 13, 39, 25, 58, 38, 41, 11,  5, 16, 51, 42, 44, 55, 46,  0, 43,
       61,  6, 19, 32, 24, 52,  8, 57,  9, 48, 62, 59, 47, 29,  4, 26, 63,
       36, 35, 21,  1, 15, 30, 40, 18, 20, 49, 14, 31, 65,  7, 10],
      dtype=int64), 'cur_cost': 106786.0}
2025-07-31 17:45:56,839 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-31 17:45:56,839 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:56,839 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:56,839 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105649.0
2025-07-31 17:45:56,921 - ExploitationExpert - INFO - res_population_num: 21
2025-07-31 17:45:56,922 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-31 17:45:56,922 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:56,929 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:56,929 - ExploitationExpert - INFO - populations: [{'tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}, {'tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}, {'tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}, {'tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}, {'tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}, {'tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}, {'tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}, {'tour': array([60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51,
        7,  9,  1, 54, 48, 28, 31, 27,  6, 62, 45, 22, 53, 19, 24,  5, 21,
       16,  8, 38, 15, 18, 44, 33, 30,  4,  0, 63, 56, 13, 17, 10, 41, 50,
       47, 52, 32, 25,  2, 35, 64,  3, 59, 36, 58, 12, 26, 57, 20],
      dtype=int64), 'cur_cost': 102148.0}, {'tour': array([64, 23, 27,  3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33,
        2, 13, 39, 25, 58, 38, 41, 11,  5, 16, 51, 42, 44, 55, 46,  0, 43,
       61,  6, 19, 32, 24, 52,  8, 57,  9, 48, 62, 59, 47, 29,  4, 26, 63,
       36, 35, 21,  1, 15, 30, 40, 18, 20, 49, 14, 31, 65,  7, 10],
      dtype=int64), 'cur_cost': 106786.0}, {'tour': array([40, 17, 43, 48, 20, 29, 33,  6, 60, 56, 14, 39, 23, 27,  9, 44, 21,
        1, 47,  5, 38, 53, 25, 62, 15, 45, 35, 46, 34,  2,  8, 16,  4, 55,
       28, 42, 52, 63, 59,  3, 36, 32,  7, 37, 13, 61, 51, 64, 11, 30, 50,
       24, 22, 10, 57, 65, 18, 19, 58,  0, 26, 54, 31, 41, 49, 12],
      dtype=int64), 'cur_cost': 105649.0}]
2025-07-31 17:45:56,933 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-31 17:45:56,933 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:56,934 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([40, 17, 43, 48, 20, 29, 33,  6, 60, 56, 14, 39, 23, 27,  9, 44, 21,
        1, 47,  5, 38, 53, 25, 62, 15, 45, 35, 46, 34,  2,  8, 16,  4, 55,
       28, 42, 52, 63, 59,  3, 36, 32,  7, 37, 13, 61, 51, 64, 11, 30, 50,
       24, 22, 10, 57, 65, 18, 19, 58,  0, 26, 54, 31, 41, 49, 12],
      dtype=int64), 'cur_cost': 105649.0}
2025-07-31 17:45:56,935 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 38, 16, 58, 36, 44, 46, 51, 24, 26,  8, 39, 62, 56, 21,  2, 48,
       47,  5, 52, 12, 18, 64,  9, 33, 50, 61, 53,  0, 59,  7, 15, 11,  1,
       27, 28, 49, 37, 35,  4, 14, 29,  3, 55, 30, 10, 20, 54, 25, 17, 13,
       34, 32, 45, 22, 60, 63, 43, 41, 23, 57, 19,  6, 42, 40, 65],
      dtype=int64), 'cur_cost': 98282.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 50, 40,  4, 51, 21,  9, 48,  8,  3, 18, 55, 65, 24, 63,  2, 42,
       14, 54, 20, 60, 30, 43, 32, 26, 16,  5, 62, 17, 19, 25, 59, 22,  7,
       52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12,  1, 31,  0,
       29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61],
      dtype=int64), 'cur_cost': 114497.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 13, 30,  6, 65,  0, 63, 50, 19, 48, 41, 61, 60,  5, 12, 35, 20,
       31, 49,  7, 38, 25, 54, 22, 53, 47, 10, 42, 58,  3, 18,  1, 17, 34,
       32, 37, 39, 45, 33, 21, 23, 52,  8, 40,  4,  9, 24, 62, 55, 57, 29,
       64, 59, 14, 16,  2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15],
      dtype=int64), 'cur_cost': 104323.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([65, 28, 57,  9, 32,  4, 64, 52, 55,  3, 22, 29, 33, 15, 31, 43,  6,
       59, 62, 42, 24, 38, 17, 27,  0, 49, 13,  7, 54, 47,  1, 18, 19, 39,
        2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61,
       60, 46,  8, 41, 53, 25, 63, 10, 45, 36, 11, 50,  5, 14, 48],
      dtype=int64), 'cur_cost': 113101.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53,
       19, 37, 25, 49,  5, 63, 31, 13, 27, 58, 60, 54,  1, 46,  0, 40, 21,
       28, 50, 17, 39, 41, 56,  9,  6, 24, 65, 55, 26, 23,  4, 59, 57, 14,
       12, 42,  3, 32,  2, 36, 61, 52, 43, 20, 47, 64, 45,  7, 33],
      dtype=int64), 'cur_cost': 107189.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 15, 19,  9, 45, 48, 23, 42, 54, 52, 58, 40, 33,  4, 64, 65, 28,
       43, 47, 27, 31, 32, 38, 39,  2, 51, 18, 41,  7, 20, 21, 29, 59,  6,
        5, 63, 49, 36,  3, 34, 16, 57, 35,  1, 56, 44,  8, 30, 24, 50, 62,
       55,  0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12],
      dtype=int64), 'cur_cost': 98592.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42,  8,  6,
       53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34,  2,  0, 12, 43,
       44,  7, 18, 46, 41,  4, 20, 17, 28,  9, 38, 61,  3, 22, 56, 14,  1,
       26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35,  5, 13],
      dtype=int64), 'cur_cost': 102706.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51,
        7,  9,  1, 54, 48, 28, 31, 27,  6, 62, 45, 22, 53, 19, 24,  5, 21,
       16,  8, 38, 15, 18, 44, 33, 30,  4,  0, 63, 56, 13, 17, 10, 41, 50,
       47, 52, 32, 25,  2, 35, 64,  3, 59, 36, 58, 12, 26, 57, 20],
      dtype=int64), 'cur_cost': 102148.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 23, 27,  3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33,
        2, 13, 39, 25, 58, 38, 41, 11,  5, 16, 51, 42, 44, 55, 46,  0, 43,
       61,  6, 19, 32, 24, 52,  8, 57,  9, 48, 62, 59, 47, 29,  4, 26, 63,
       36, 35, 21,  1, 15, 30, 40, 18, 20, 49, 14, 31, 65,  7, 10],
      dtype=int64), 'cur_cost': 106786.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 17, 43, 48, 20, 29, 33,  6, 60, 56, 14, 39, 23, 27,  9, 44, 21,
        1, 47,  5, 38, 53, 25, 62, 15, 45, 35, 46, 34,  2,  8, 16,  4, 55,
       28, 42, 52, 63, 59,  3, 36, 32,  7, 37, 13, 61, 51, 64, 11, 30, 50,
       24, 22, 10, 57, 65, 18, 19, 58,  0, 26, 54, 31, 41, 49, 12],
      dtype=int64), 'cur_cost': 105649.0}}]
2025-07-31 17:45:56,935 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:45:56,935 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:56,962 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=98282.0, 多样性=0.973
2025-07-31 17:45:56,962 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-31 17:45:56,962 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-07-31 17:45:56,962 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:45:56,967 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -2.469259054953889, 'best_improvement': -6.686087432548682}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0779850746268656}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.18007741073346287, 'recent_improvements': [-0.009618786096548164, -1.3120930136500182, 0.3505360353703775], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 21, 'new_count': 21, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8315295815295816, 'new_diversity': 0.8315295815295816, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:45:56,974 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-31 17:45:56,975 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-31 17:45:56,975 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-31 17:45:56,975 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:56,996 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=98282.0, 多样性=0.973
2025-07-31 17:45:56,996 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:45:57,001 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.014
2025-07-31 17:45:57,002 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:45:57,012 - EliteExpert - INFO - 精英解分析完成: 精英解数量=21, 多样性=0.832
2025-07-31 17:45:57,015 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-07-31 17:45:57,016 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753955157.0162563, 'status': 'default_fallback'}}
2025-07-31 17:45:57,016 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:45:57,016 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:45:57,016 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 98282.0
  • mean_cost: 105327.3
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploitation
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:45:57,016 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:45:57,017 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:45:58,665 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape focus suggests balance. The population's diversity is moderate, and the overall cost shows a deterioration trend"
}
```
2025-07-31 17:45:58,665 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:45:58,665 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:58,665 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:58,665 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape focus suggests balance. The population's diversity is moderate, and the overall cost shows a deterioration trend"
}
```
2025-07-31 17:45:58,665 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:45:58,665 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-31 17:45:58,665 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation is favored as the landscape focus suggests balance. The population's diversity is moderate, and the overall cost shows a deterioration trend"
}
```
2025-07-31 17:45:58,666 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:45:58,666 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-07-31 17:45:58,666 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:58,666 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:58,666 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 117994.0
2025-07-31 17:45:58,763 - ExploitationExpert - INFO - res_population_num: 21
2025-07-31 17:45:58,763 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0]
2025-07-31 17:45:58,763 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64)]
2025-07-31 17:45:58,773 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:58,774 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': [6, 50, 40, 4, 51, 21, 9, 48, 8, 3, 18, 55, 65, 24, 63, 2, 42, 14, 54, 20, 60, 30, 43, 32, 26, 16, 5, 62, 17, 19, 25, 59, 22, 7, 52, 47, 41, 11, 13, 15, 28, 38, 58, 34, 37, 56, 35, 12, 1, 31, 0, 29, 45, 27, 64, 53, 36, 39, 23, 46, 33, 44, 10, 49, 57, 61], 'cur_cost': 114497.0}, {'tour': [11, 13, 30, 6, 65, 0, 63, 50, 19, 48, 41, 61, 60, 5, 12, 35, 20, 31, 49, 7, 38, 25, 54, 22, 53, 47, 10, 42, 58, 3, 18, 1, 17, 34, 32, 37, 39, 45, 33, 21, 23, 52, 8, 40, 4, 9, 24, 62, 55, 57, 29, 64, 59, 14, 16, 2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15], 'cur_cost': 104323.0}, {'tour': [65, 28, 57, 9, 32, 4, 64, 52, 55, 3, 22, 29, 33, 15, 31, 43, 6, 59, 62, 42, 24, 38, 17, 27, 0, 49, 13, 7, 54, 47, 1, 18, 19, 39, 2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61, 60, 46, 8, 41, 53, 25, 63, 10, 45, 36, 11, 50, 5, 14, 48], 'cur_cost': 113101.0}, {'tour': [8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53, 19, 37, 25, 49, 5, 63, 31, 13, 27, 58, 60, 54, 1, 46, 0, 40, 21, 28, 50, 17, 39, 41, 56, 9, 6, 24, 65, 55, 26, 23, 4, 59, 57, 14, 12, 42, 3, 32, 2, 36, 61, 52, 43, 20, 47, 64, 45, 7, 33], 'cur_cost': 107189.0}, {'tour': [17, 15, 19, 9, 45, 48, 23, 42, 54, 52, 58, 40, 33, 4, 64, 65, 28, 43, 47, 27, 31, 32, 38, 39, 2, 51, 18, 41, 7, 20, 21, 29, 59, 6, 5, 63, 49, 36, 3, 34, 16, 57, 35, 1, 56, 44, 8, 30, 24, 50, 62, 55, 0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12], 'cur_cost': 98592.0}, {'tour': [60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42, 8, 6, 53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34, 2, 0, 12, 43, 44, 7, 18, 46, 41, 4, 20, 17, 28, 9, 38, 61, 3, 22, 56, 14, 1, 26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35, 5, 13], 'cur_cost': 102706.0}, {'tour': [60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51, 7, 9, 1, 54, 48, 28, 31, 27, 6, 62, 45, 22, 53, 19, 24, 5, 21, 16, 8, 38, 15, 18, 44, 33, 30, 4, 0, 63, 56, 13, 17, 10, 41, 50, 47, 52, 32, 25, 2, 35, 64, 3, 59, 36, 58, 12, 26, 57, 20], 'cur_cost': 102148.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:58,774 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-07-31 17:45:58,774 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:58,774 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}
2025-07-31 17:45:58,775 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-31 17:45:58,775 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:58,775 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:58,776 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 116167.0
2025-07-31 17:45:58,868 - ExploitationExpert - INFO - res_population_num: 22
2025-07-31 17:45:58,868 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521]
2025-07-31 17:45:58,868 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-31 17:45:58,876 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:58,876 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [11, 13, 30, 6, 65, 0, 63, 50, 19, 48, 41, 61, 60, 5, 12, 35, 20, 31, 49, 7, 38, 25, 54, 22, 53, 47, 10, 42, 58, 3, 18, 1, 17, 34, 32, 37, 39, 45, 33, 21, 23, 52, 8, 40, 4, 9, 24, 62, 55, 57, 29, 64, 59, 14, 16, 2, 43, 26, 51, 56, 27, 36, 46, 28, 44, 15], 'cur_cost': 104323.0}, {'tour': [65, 28, 57, 9, 32, 4, 64, 52, 55, 3, 22, 29, 33, 15, 31, 43, 6, 59, 62, 42, 24, 38, 17, 27, 0, 49, 13, 7, 54, 47, 1, 18, 19, 39, 2, 21, 51, 35, 40, 34, 12, 16, 37, 26, 58, 20, 56, 23, 30, 44, 61, 60, 46, 8, 41, 53, 25, 63, 10, 45, 36, 11, 50, 5, 14, 48], 'cur_cost': 113101.0}, {'tour': [8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53, 19, 37, 25, 49, 5, 63, 31, 13, 27, 58, 60, 54, 1, 46, 0, 40, 21, 28, 50, 17, 39, 41, 56, 9, 6, 24, 65, 55, 26, 23, 4, 59, 57, 14, 12, 42, 3, 32, 2, 36, 61, 52, 43, 20, 47, 64, 45, 7, 33], 'cur_cost': 107189.0}, {'tour': [17, 15, 19, 9, 45, 48, 23, 42, 54, 52, 58, 40, 33, 4, 64, 65, 28, 43, 47, 27, 31, 32, 38, 39, 2, 51, 18, 41, 7, 20, 21, 29, 59, 6, 5, 63, 49, 36, 3, 34, 16, 57, 35, 1, 56, 44, 8, 30, 24, 50, 62, 55, 0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12], 'cur_cost': 98592.0}, {'tour': [60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42, 8, 6, 53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34, 2, 0, 12, 43, 44, 7, 18, 46, 41, 4, 20, 17, 28, 9, 38, 61, 3, 22, 56, 14, 1, 26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35, 5, 13], 'cur_cost': 102706.0}, {'tour': [60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51, 7, 9, 1, 54, 48, 28, 31, 27, 6, 62, 45, 22, 53, 19, 24, 5, 21, 16, 8, 38, 15, 18, 44, 33, 30, 4, 0, 63, 56, 13, 17, 10, 41, 50, 47, 52, 32, 25, 2, 35, 64, 3, 59, 36, 58, 12, 26, 57, 20], 'cur_cost': 102148.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:58,877 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-31 17:45:58,877 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:58,878 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}
2025-07-31 17:45:58,878 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:45:58,878 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:45:58,878 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:58,880 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:58,881 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:58,881 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12973.0, 路径长度: 66
2025-07-31 17:45:58,881 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}
2025-07-31 17:45:58,881 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:45:58,881 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:58,881 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:58,882 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 112565.0
2025-07-31 17:45:58,958 - ExploitationExpert - INFO - res_population_num: 23
2025-07-31 17:45:58,958 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:58,958 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:58,967 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:58,967 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}, {'tour': [8, 29, 51, 22, 16, 35, 44, 30, 15, 11, 38, 18, 48, 34, 62, 10, 53, 19, 37, 25, 49, 5, 63, 31, 13, 27, 58, 60, 54, 1, 46, 0, 40, 21, 28, 50, 17, 39, 41, 56, 9, 6, 24, 65, 55, 26, 23, 4, 59, 57, 14, 12, 42, 3, 32, 2, 36, 61, 52, 43, 20, 47, 64, 45, 7, 33], 'cur_cost': 107189.0}, {'tour': [17, 15, 19, 9, 45, 48, 23, 42, 54, 52, 58, 40, 33, 4, 64, 65, 28, 43, 47, 27, 31, 32, 38, 39, 2, 51, 18, 41, 7, 20, 21, 29, 59, 6, 5, 63, 49, 36, 3, 34, 16, 57, 35, 1, 56, 44, 8, 30, 24, 50, 62, 55, 0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12], 'cur_cost': 98592.0}, {'tour': [60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42, 8, 6, 53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34, 2, 0, 12, 43, 44, 7, 18, 46, 41, 4, 20, 17, 28, 9, 38, 61, 3, 22, 56, 14, 1, 26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35, 5, 13], 'cur_cost': 102706.0}, {'tour': [60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51, 7, 9, 1, 54, 48, 28, 31, 27, 6, 62, 45, 22, 53, 19, 24, 5, 21, 16, 8, 38, 15, 18, 44, 33, 30, 4, 0, 63, 56, 13, 17, 10, 41, 50, 47, 52, 32, 25, 2, 35, 64, 3, 59, 36, 58, 12, 26, 57, 20], 'cur_cost': 102148.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:58,969 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-31 17:45:58,969 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:58,969 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}
2025-07-31 17:45:58,969 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:45:58,970 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:58,970 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:58,970 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 97429.0
2025-07-31 17:45:59,072 - ExploitationExpert - INFO - res_population_num: 23
2025-07-31 17:45:59,072 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:59,072 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:59,080 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:59,080 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}, {'tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}, {'tour': [17, 15, 19, 9, 45, 48, 23, 42, 54, 52, 58, 40, 33, 4, 64, 65, 28, 43, 47, 27, 31, 32, 38, 39, 2, 51, 18, 41, 7, 20, 21, 29, 59, 6, 5, 63, 49, 36, 3, 34, 16, 57, 35, 1, 56, 44, 8, 30, 24, 50, 62, 55, 0, 60, 46, 26, 61, 14, 10, 25, 13, 53, 11, 22, 37, 12], 'cur_cost': 98592.0}, {'tour': [60, 27, 31, 58, 57, 23, 11, 55, 64, 51, 48, 29, 19, 36, 42, 8, 6, 53, 40, 10, 30, 33, 24, 15, 39, 63, 32, 21, 37, 34, 2, 0, 12, 43, 44, 7, 18, 46, 41, 4, 20, 17, 28, 9, 38, 61, 3, 22, 56, 14, 1, 26, 49, 62, 50, 65, 52, 59, 16, 47, 25, 54, 45, 35, 5, 13], 'cur_cost': 102706.0}, {'tour': [60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51, 7, 9, 1, 54, 48, 28, 31, 27, 6, 62, 45, 22, 53, 19, 24, 5, 21, 16, 8, 38, 15, 18, 44, 33, 30, 4, 0, 63, 56, 13, 17, 10, 41, 50, 47, 52, 32, 25, 2, 35, 64, 3, 59, 36, 58, 12, 26, 57, 20], 'cur_cost': 102148.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:59,082 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-07-31 17:45:59,082 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:59,082 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}
2025-07-31 17:45:59,082 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-07-31 17:45:59,082 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-07-31 17:45:59,083 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:59,084 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-31 17:45:59,085 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:59,085 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106265.0, 路径长度: 66
2025-07-31 17:45:59,085 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 55, 61, 39, 63, 56, 34, 24, 50, 29, 62, 47, 65, 58, 6, 38, 53, 64, 4, 44, 14, 41, 3, 0, 26, 60, 27, 52, 32, 18, 36, 49, 12, 13, 35, 51, 7, 48, 23, 37, 57, 46, 42, 43, 16, 17, 54, 31, 59, 30, 8, 1, 10, 22, 20, 9, 45, 11, 19, 5, 28, 33, 25, 21, 40, 15], 'cur_cost': 106265.0}
2025-07-31 17:45:59,085 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-07-31 17:45:59,085 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:59,085 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:59,086 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 113957.0
2025-07-31 17:45:59,154 - ExploitationExpert - INFO - res_population_num: 23
2025-07-31 17:45:59,154 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:59,155 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:59,163 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:59,163 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}, {'tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}, {'tour': [2, 55, 61, 39, 63, 56, 34, 24, 50, 29, 62, 47, 65, 58, 6, 38, 53, 64, 4, 44, 14, 41, 3, 0, 26, 60, 27, 52, 32, 18, 36, 49, 12, 13, 35, 51, 7, 48, 23, 37, 57, 46, 42, 43, 16, 17, 54, 31, 59, 30, 8, 1, 10, 22, 20, 9, 45, 11, 19, 5, 28, 33, 25, 21, 40, 15], 'cur_cost': 106265.0}, {'tour': array([21, 58,  2, 15, 42, 37, 56, 46, 44, 48,  3,  0, 33, 18, 61, 29,  9,
       31, 23,  1, 47, 34, 63, 43, 12, 52, 14, 62, 16,  7, 65, 20, 27, 45,
       40, 13, 64, 25, 30, 17, 49, 50, 11, 22,  4, 38, 59, 10,  5, 51, 54,
       36, 53,  6, 26, 55, 24, 32, 35, 19, 60,  8, 39, 28, 41, 57],
      dtype=int64), 'cur_cost': 113957.0}, {'tour': [60, 42, 55, 37, 39, 34, 23, 43, 49, 11, 61, 65, 40, 14, 46, 29, 51, 7, 9, 1, 54, 48, 28, 31, 27, 6, 62, 45, 22, 53, 19, 24, 5, 21, 16, 8, 38, 15, 18, 44, 33, 30, 4, 0, 63, 56, 13, 17, 10, 41, 50, 47, 52, 32, 25, 2, 35, 64, 3, 59, 36, 58, 12, 26, 57, 20], 'cur_cost': 102148.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:59,165 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-31 17:45:59,165 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:59,165 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([21, 58,  2, 15, 42, 37, 56, 46, 44, 48,  3,  0, 33, 18, 61, 29,  9,
       31, 23,  1, 47, 34, 63, 43, 12, 52, 14, 62, 16,  7, 65, 20, 27, 45,
       40, 13, 64, 25, 30, 17, 49, 50, 11, 22,  4, 38, 59, 10,  5, 51, 54,
       36, 53,  6, 26, 55, 24, 32, 35, 19, 60,  8, 39, 28, 41, 57],
      dtype=int64), 'cur_cost': 113957.0}
2025-07-31 17:45:59,166 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-31 17:45:59,166 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:59,166 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:59,166 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102032.0
2025-07-31 17:45:59,248 - ExploitationExpert - INFO - res_population_num: 23
2025-07-31 17:45:59,248 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:59,248 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:59,256 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:59,256 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}, {'tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}, {'tour': [2, 55, 61, 39, 63, 56, 34, 24, 50, 29, 62, 47, 65, 58, 6, 38, 53, 64, 4, 44, 14, 41, 3, 0, 26, 60, 27, 52, 32, 18, 36, 49, 12, 13, 35, 51, 7, 48, 23, 37, 57, 46, 42, 43, 16, 17, 54, 31, 59, 30, 8, 1, 10, 22, 20, 9, 45, 11, 19, 5, 28, 33, 25, 21, 40, 15], 'cur_cost': 106265.0}, {'tour': array([21, 58,  2, 15, 42, 37, 56, 46, 44, 48,  3,  0, 33, 18, 61, 29,  9,
       31, 23,  1, 47, 34, 63, 43, 12, 52, 14, 62, 16,  7, 65, 20, 27, 45,
       40, 13, 64, 25, 30, 17, 49, 50, 11, 22,  4, 38, 59, 10,  5, 51, 54,
       36, 53,  6, 26, 55, 24, 32, 35, 19, 60,  8, 39, 28, 41, 57],
      dtype=int64), 'cur_cost': 113957.0}, {'tour': array([ 7, 20,  8, 25, 37, 22, 57, 46, 60, 65, 11, 51, 36, 32, 13, 14, 43,
       31, 10, 33, 38, 58, 45, 54, 55, 18, 52,  1,  6, 29, 30, 61, 24, 42,
       28, 27, 19, 64, 62, 35, 40, 56,  4, 53, 47, 21, 41,  0,  2, 34, 48,
       23,  3, 50, 39, 12, 49, 44, 26, 15, 63, 17,  9, 16, 59,  5],
      dtype=int64), 'cur_cost': 102032.0}, {'tour': [64, 23, 27, 3, 12, 37, 28, 56, 53, 50, 17, 54, 34, 22, 45, 60, 33, 2, 13, 39, 25, 58, 38, 41, 11, 5, 16, 51, 42, 44, 55, 46, 0, 43, 61, 6, 19, 32, 24, 52, 8, 57, 9, 48, 62, 59, 47, 29, 4, 26, 63, 36, 35, 21, 1, 15, 30, 40, 18, 20, 49, 14, 31, 65, 7, 10], 'cur_cost': 106786.0}, {'tour': [40, 17, 43, 48, 20, 29, 33, 6, 60, 56, 14, 39, 23, 27, 9, 44, 21, 1, 47, 5, 38, 53, 25, 62, 15, 45, 35, 46, 34, 2, 8, 16, 4, 55, 28, 42, 52, 63, 59, 3, 36, 32, 7, 37, 13, 61, 51, 64, 11, 30, 50, 24, 22, 10, 57, 65, 18, 19, 58, 0, 26, 54, 31, 41, 49, 12], 'cur_cost': 105649.0}]
2025-07-31 17:45:59,259 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-31 17:45:59,259 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:59,259 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7, 20,  8, 25, 37, 22, 57, 46, 60, 65, 11, 51, 36, 32, 13, 14, 43,
       31, 10, 33, 38, 58, 45, 54, 55, 18, 52,  1,  6, 29, 30, 61, 24, 42,
       28, 27, 19, 64, 62, 35, 40, 56,  4, 53, 47, 21, 41,  0,  2, 34, 48,
       23,  3, 50, 39, 12, 49, 44, 26, 15, 63, 17,  9, 16, 59,  5],
      dtype=int64), 'cur_cost': 102032.0}
2025-07-31 17:45:59,259 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-07-31 17:45:59,260 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-31 17:45:59,260 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:45:59,262 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-31 17:45:59,262 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:45:59,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12833.0, 路径长度: 66
2025-07-31 17:45:59,263 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 9, 20, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}
2025-07-31 17:45:59,263 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-31 17:45:59,263 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:45:59,263 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:45:59,264 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 111880.0
2025-07-31 17:45:59,353 - ExploitationExpert - INFO - res_population_num: 23
2025-07-31 17:45:59,353 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9538, 9557.0, 9557, 9560.0, 88678.0, 9521, 9521]
2025-07-31 17:45:59,353 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64,
       57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 36, 26, 25, 35,
       34, 30, 28, 32, 29, 33, 31, 24, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 64, 43, 18, 27, 28,  5, 61, 15, 59, 10,  7, 34,  2, 55, 14, 29,
       51, 46, 52,  8, 13, 32, 30, 35, 20, 60, 48, 40, 24, 23, 31,  9, 65,
       56, 53, 22,  4, 63, 49, 36, 42, 41, 25, 12, 11, 33, 44, 39, 37,  3,
       16, 17,  1, 58,  6, 50, 19, 57, 21, 26, 38, 45, 47, 54, 62],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-31 17:45:59,362 - ExploitationExpert - INFO - populations_num: 10
2025-07-31 17:45:59,362 - ExploitationExpert - INFO - populations: [{'tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}, {'tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}, {'tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}, {'tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}, {'tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}, {'tour': [2, 55, 61, 39, 63, 56, 34, 24, 50, 29, 62, 47, 65, 58, 6, 38, 53, 64, 4, 44, 14, 41, 3, 0, 26, 60, 27, 52, 32, 18, 36, 49, 12, 13, 35, 51, 7, 48, 23, 37, 57, 46, 42, 43, 16, 17, 54, 31, 59, 30, 8, 1, 10, 22, 20, 9, 45, 11, 19, 5, 28, 33, 25, 21, 40, 15], 'cur_cost': 106265.0}, {'tour': array([21, 58,  2, 15, 42, 37, 56, 46, 44, 48,  3,  0, 33, 18, 61, 29,  9,
       31, 23,  1, 47, 34, 63, 43, 12, 52, 14, 62, 16,  7, 65, 20, 27, 45,
       40, 13, 64, 25, 30, 17, 49, 50, 11, 22,  4, 38, 59, 10,  5, 51, 54,
       36, 53,  6, 26, 55, 24, 32, 35, 19, 60,  8, 39, 28, 41, 57],
      dtype=int64), 'cur_cost': 113957.0}, {'tour': array([ 7, 20,  8, 25, 37, 22, 57, 46, 60, 65, 11, 51, 36, 32, 13, 14, 43,
       31, 10, 33, 38, 58, 45, 54, 55, 18, 52,  1,  6, 29, 30, 61, 24, 42,
       28, 27, 19, 64, 62, 35, 40, 56,  4, 53, 47, 21, 41,  0,  2, 34, 48,
       23,  3, 50, 39, 12, 49, 44, 26, 15, 63, 17,  9, 16, 59,  5],
      dtype=int64), 'cur_cost': 102032.0}, {'tour': [0, 9, 20, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}, {'tour': array([ 9, 34, 25, 48,  0, 16, 50, 60, 27, 31, 23, 42, 63, 47, 11, 28, 54,
       35, 40, 59, 29, 21, 17, 12, 44, 57, 38, 36,  3, 24, 51, 15,  7, 30,
       33,  1, 26,  2, 10, 37,  5, 53, 41, 62, 58, 13, 19, 65, 56, 43, 49,
       22, 52, 20,  6, 45, 55,  4, 61, 39, 46, 18, 14, 64, 32,  8],
      dtype=int64), 'cur_cost': 111880.0}]
2025-07-31 17:45:59,365 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-31 17:45:59,365 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-31 17:45:59,366 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 9, 34, 25, 48,  0, 16, 50, 60, 27, 31, 23, 42, 63, 47, 11, 28, 54,
       35, 40, 59, 29, 21, 17, 12, 44, 57, 38, 36,  3, 24, 51, 15,  7, 30,
       33,  1, 26,  2, 10, 37,  5, 53, 41, 62, 58, 13, 19, 65, 56, 43, 49,
       22, 52, 20,  6, 45, 55,  4, 61, 39, 46, 18, 14, 64, 32,  8],
      dtype=int64), 'cur_cost': 111880.0}
2025-07-31 17:45:59,367 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 64, 52, 36, 25,  2, 50, 21, 12,  9, 47, 24, 40, 62, 38, 14,  6,
       63, 22, 55, 27, 53,  3, 35, 57, 39, 19,  0,  7, 20,  8, 17, 10, 41,
       32, 15, 48,  1, 13, 30, 28, 46, 26,  5, 23, 51, 37, 43, 42, 54, 34,
       45, 16, 61, 18, 60, 65, 29, 56, 31, 44, 59, 58, 33, 11, 49],
      dtype=int64), 'cur_cost': 117994.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([53,  8,  0, 20, 36,  2, 30, 40, 59, 33, 35,  6, 37, 57, 24, 47, 32,
       60, 61, 19, 13, 49, 58, 14, 43, 34, 44, 18, 31, 27, 15, 41,  7, 17,
       45, 11, 42,  5, 21, 64,  1, 63, 65, 23, 28, 52, 39, 54, 48, 26, 62,
       16,  4, 22, 51, 12,  9, 55, 10, 46,  3, 38, 50, 56, 25, 29],
      dtype=int64), 'cur_cost': 116167.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 2, 7, 3, 9, 11, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12973.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 61, 45, 58, 57, 13, 17,  3, 62, 43,  9, 51, 26, 23, 65,  6, 38,
       16, 14,  0,  2, 35, 59, 37, 21, 11, 12, 40, 53,  5, 60, 24, 56, 30,
       27,  8, 29, 33, 32, 48,  1, 52, 47,  7, 41, 63, 15,  4, 22, 31, 39,
       25, 42, 50, 28, 19, 34, 64, 54, 44, 20, 10, 18, 36, 55, 49],
      dtype=int64), 'cur_cost': 112565.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 39, 13, 48, 38, 29,  6, 51, 35, 25, 58, 31,  9, 62, 24,  2, 10,
       28, 44, 34, 26, 21, 19, 22,  8, 52, 50, 33, 46, 53, 63, 61, 27, 47,
       30, 36, 55, 11, 59, 64, 65, 56, 49, 12, 14,  3, 23,  5,  0, 41, 15,
       42, 45,  7, 17,  4, 16, 20, 18, 57, 60, 37, 54, 40, 32, 43],
      dtype=int64), 'cur_cost': 97429.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 55, 61, 39, 63, 56, 34, 24, 50, 29, 62, 47, 65, 58, 6, 38, 53, 64, 4, 44, 14, 41, 3, 0, 26, 60, 27, 52, 32, 18, 36, 49, 12, 13, 35, 51, 7, 48, 23, 37, 57, 46, 42, 43, 16, 17, 54, 31, 59, 30, 8, 1, 10, 22, 20, 9, 45, 11, 19, 5, 28, 33, 25, 21, 40, 15], 'cur_cost': 106265.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 58,  2, 15, 42, 37, 56, 46, 44, 48,  3,  0, 33, 18, 61, 29,  9,
       31, 23,  1, 47, 34, 63, 43, 12, 52, 14, 62, 16,  7, 65, 20, 27, 45,
       40, 13, 64, 25, 30, 17, 49, 50, 11, 22,  4, 38, 59, 10,  5, 51, 54,
       36, 53,  6, 26, 55, 24, 32, 35, 19, 60,  8, 39, 28, 41, 57],
      dtype=int64), 'cur_cost': 113957.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 20,  8, 25, 37, 22, 57, 46, 60, 65, 11, 51, 36, 32, 13, 14, 43,
       31, 10, 33, 38, 58, 45, 54, 55, 18, 52,  1,  6, 29, 30, 61, 24, 42,
       28, 27, 19, 64, 62, 35, 40, 56,  4, 53, 47, 21, 41,  0,  2, 34, 48,
       23,  3, 50, 39, 12, 49, 44, 26, 15, 63, 17,  9, 16, 59,  5],
      dtype=int64), 'cur_cost': 102032.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 20, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12833.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 34, 25, 48,  0, 16, 50, 60, 27, 31, 23, 42, 63, 47, 11, 28, 54,
       35, 40, 59, 29, 21, 17, 12, 44, 57, 38, 36,  3, 24, 51, 15,  7, 30,
       33,  1, 26,  2, 10, 37,  5, 53, 41, 62, 58, 13, 19, 65, 56, 43, 49,
       22, 52, 20,  6, 45, 55,  4, 61, 39, 46, 18, 14, 64, 32,  8],
      dtype=int64), 'cur_cost': 111880.0}}]
2025-07-31 17:45:59,367 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:45:59,367 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:45:59,390 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12833.0, 多样性=0.958
2025-07-31 17:45:59,391 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-31 17:45:59,391 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-07-31 17:45:59,391 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:45:59,401 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.3268390875657943, 'best_improvement': 0.8694267515923567}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01523018345448243}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.5785830206519353, 'recent_improvements': [-1.3120930136500182, 0.3505360353703775, -2.469259054953889], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 23, 'new_count': 23, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8375853395616242, 'new_diversity': 0.8375853395616242, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:45:59,408 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-31 17:45:59,430 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-07-31 17:45:59,430 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250731_174559.solution
2025-07-31 17:45:59,431 - __main__ - INFO - 实例 composite13_66 处理完成
