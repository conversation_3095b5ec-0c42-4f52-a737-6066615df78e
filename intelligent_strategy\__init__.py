"""
Intelligent Strategy Selection System

This module implements a fitness landscape-driven strategy selection system
for the EoH-TSP-Solver framework. It provides intelligent exploration and
exploitation strategy selection based on LLM reasoning and landscape analysis.

Main Components:
- Core framework for individual state monitoring and landscape analysis
- Exploration strategies (Strong, Balanced, Intelligent)
- Exploitation strategies with local optima escape mechanisms
- LLM-based strategy selection interface
- Performance monitoring and optimization
"""

from .core import *
from .strategies import *
from .llm_interface import *
from .monitoring import *
from .coordination import *
from .integration import *
from .system import IntelligentStrategySystem

__version__ = "1.0.0"
__author__ = "EoH-TSP-Solver Development Team"
