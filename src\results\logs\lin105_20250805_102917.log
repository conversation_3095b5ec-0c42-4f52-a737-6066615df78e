2025-08-05 10:29:17,908 - __main__ - INFO - lin105 开始进化第 1 代
2025-08-05 10:29:17,908 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:17,912 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:17,920 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=16996.000, 多样性=0.991
2025-08-05 10:29:17,924 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:17,930 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.991
2025-08-05 10:29:17,932 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:17,938 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:17,938 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:17,938 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:17,939 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:17,976 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -267.280, 聚类评分: 0.000, 覆盖率: 0.168, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:17,976 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:17,976 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:17,976 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 10:29:17,982 - visualization.landscape_visualizer - INFO - 插值约束: 19 个点被约束到最小值 16996.00
2025-08-05 10:29:17,983 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.5%, 梯度: 3577.39 → 3379.34
2025-08-05 10:29:18,087 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_151_20250805_102918.html
2025-08-05 10:29:18,129 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_151_20250805_102918.html
2025-08-05 10:29:18,129 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 151
2025-08-05 10:29:18,129 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:18,130 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1908秒
2025-08-05 10:29:18,130 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 302, 'max_size': 500, 'hits': 0, 'misses': 302, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 1002, 'misses': 516, 'hit_rate': 0.6600790513833992, 'evictions': 416, 'ttl': 7200}}
2025-08-05 10:29:18,130 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -267.27999999999884, 'local_optima_density': 0.3, 'gradient_variance': 3434736774.9856005, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1681, 'fitness_entropy': 0.8812908992306927, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -267.280)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.168)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360957.9765522, 'performance_metrics': {}}}
2025-08-05 10:29:18,130 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:18,130 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:18,130 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:18,130 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:18,132 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,132 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:18,132 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,132 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:18,132 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:18,132 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,133 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:18,133 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:18,133 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:18,133 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:18,133 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:18,133 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,412 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 10:29:18,412 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,412 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78397.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,412 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78397.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,413 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 78397.00)
2025-08-05 10:29:18,413 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:18,413 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:18,413 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,417 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,417 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,417 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21558.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,417 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21558.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,418 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 21558.00)
2025-08-05 10:29:18,418 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:18,418 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:18,418 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,421 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 10:29:18,421 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,422 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107813.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,422 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107813.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,422 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 107813.00)
2025-08-05 10:29:18,423 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:18,423 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:18,423 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,427 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,427 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20911.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,428 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20911.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,428 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 20911.00)
2025-08-05 10:29:18,428 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:18,428 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:18,428 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,433 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,433 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20432.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,434 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20432.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,434 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 20432.00)
2025-08-05 10:29:18,434 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:18,434 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:18,435 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,439 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,440 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,440 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20737.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,440 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20737.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,440 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 20737.00)
2025-08-05 10:29:18,441 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:18,441 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:18,441 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,445 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,445 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,445 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21664.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,446 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21664.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,446 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21664.00)
2025-08-05 10:29:18,446 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:18,446 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:18,446 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,450 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,451 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21215.0, 路径长度: 105, 收集中间解: 0
2025-08-05 10:29:18,451 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21215.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,451 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 21215.00)
2025-08-05 10:29:18,451 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:18,451 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:18,451 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:18,451 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 130544.0
2025-08-05 10:29:18,465 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:18,466 - ExploitationExpert - INFO - res_population_costs: [14936.0, 14498]
2025-08-05 10:29:18,466 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:18,467 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:18,467 - ExploitationExpert - INFO - populations: [{'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78397.0}, {'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21558.0}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107813.0}, {'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20911.0}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20432.0}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20737.0}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21664.0}, {'tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21215.0}, {'tour': array([ 18,  75,  26,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11], dtype=int64), 'cur_cost': 130544.0}, {'tour': array([ 21,   6,  52,  72,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 130772.0}]
2025-08-05 10:29:18,469 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:18,469 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 391, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 391, 'cache_hits': 0, 'similarity_calculations': 2058, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:18,470 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 18,  75,  26,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11], dtype=int64), 'cur_cost': 130544.0, 'intermediate_solutions': [{'tour': array([103,   3,  49, 101,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([101, 103,   3,  49,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 22, 101, 103,   3,  49,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 49, 101, 103,   3,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 130758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 49,  22, 101, 103,   3,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 131026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:18,470 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 130544.00)
2025-08-05 10:29:18,471 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:18,471 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:18,471 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:18,471 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 123944.0
2025-08-05 10:29:18,482 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:18,482 - ExploitationExpert - INFO - res_population_costs: [14936.0, 14498, 14490]
2025-08-05 10:29:18,482 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:18,484 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:18,484 - ExploitationExpert - INFO - populations: [{'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78397.0}, {'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21558.0}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107813.0}, {'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20911.0}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20432.0}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20737.0}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21664.0}, {'tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21215.0}, {'tour': array([ 18,  75,  26,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11], dtype=int64), 'cur_cost': 130544.0}, {'tour': array([ 55, 102,  53,  45,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52], dtype=int64), 'cur_cost': 123944.0}]
2025-08-05 10:29:18,486 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:18,486 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 392, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 392, 'cache_hits': 0, 'similarity_calculations': 2059, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:18,487 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 55, 102,  53,  45,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52], dtype=int64), 'cur_cost': 123944.0, 'intermediate_solutions': [{'tour': array([ 52,   6,  21,  72,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 130690.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 72,  52,   6,  21,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 129284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 38,  72,  52,   6,  21,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 130502.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 21,  72,  52,   6,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 132226.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 21,  38,  72,  52,   6,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 132236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:18,488 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 123944.00)
2025-08-05 10:29:18,488 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:18,488 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:18,490 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78397.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21558.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107813.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20911.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20432.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20737.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21664.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21215.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 18,  75,  26,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11], dtype=int64), 'cur_cost': 130544.0, 'intermediate_solutions': [{'tour': array([103,   3,  49, 101,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([101, 103,   3,  49,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 22, 101, 103,   3,  49,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 132978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 49, 101, 103,   3,  22,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 130758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 49,  22, 101, 103,   3,  33, 104,  77,  88,   7,  70,  52,  54,
        30,  60,  79,  37,  24,  74,  12,  56,  71,   6,  65,  25,   0,
        63,   4,  41,  26,  21,  72,  82,  53,  27,  91,  85,  78,  45,
        94,   2,  61,  19,  98,  34,  99,  48,  11,  76,  55,  95,   9,
        62,   5,  89,  81,  51,  57,  13,  20,  38,  64,  67,  84,  68,
        59,  92,   1,  16,  80,  28,  31,  29,  14,  86,  40,  42,  90,
        17,  18,  58,  69,  66, 102,  15,  50,  87,  32,  96,  23,  36,
        97,  83,  35,  43,  44, 100,  73,  39,  10,  75,  93,  46,   8,
        47], dtype=int64), 'cur_cost': 131026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 55, 102,  53,  45,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52], dtype=int64), 'cur_cost': 123944.0, 'intermediate_solutions': [{'tour': array([ 52,   6,  21,  72,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 130690.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 72,  52,   6,  21,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 129284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 38,  72,  52,   6,  21,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 130502.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 21,  72,  52,   6,  38,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 132226.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 21,  38,  72,  52,   6,  42,  35,  14,  37,  93,  66,  88,   8,
        45,  55,  70,  62,   4,  43,   2,   3,  89,  58,  81,  95,   9,
       101,  28,  75,  64,   7,  90,  41,  91,  94,  25,  83,  15,  84,
        80,  30,  32,  33,  50,  17,  12,  78,   0,  71,  16,  22,  79,
       100,  40,  39,  98,  56,  57,  61,  47,  77,  96,  85, 104,  13,
        67,  26,  24,  68,  59,  53,  99,  54,   5,  51,  74,  19,  92,
       102,  29,  20,  82,   1,  87,  60,  34,  36,  31,  97,  48,  76,
        49,  18,  86,  27,  10,  63,  73,  23,  11,  44,  46,  65, 103,
        69], dtype=int64), 'cur_cost': 132236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:18,491 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:18,491 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:18,496 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20432.000, 多样性=0.951
2025-08-05 10:29:18,496 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:18,496 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:18,496 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:18,497 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07019275262106082, 'best_improvement': -0.202165215344787}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0403673643741991}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05277913418492671, 'recent_improvements': [-0.0984551267580625, -0.06765728175044695, 0.007103141611790932], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 14490, 'new_best_cost': 14490, 'quality_improvement': 0.0, 'old_diversity': 0.7396825396825397, 'new_diversity': 0.7396825396825397, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:18,497 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:18,497 - __main__ - INFO - lin105 开始进化第 2 代
2025-08-05 10:29:18,497 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:18,497 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:18,498 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20432.000, 多样性=0.951
2025-08-05 10:29:18,499 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:18,503 - PathExpert - INFO - 路径结构分析完成: 公共边数量=74, 路径相似性=0.951
2025-08-05 10:29:18,503 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:18,505 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.740
2025-08-05 10:29:18,507 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:18,507 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:18,507 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:29:18,507 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:29:18,555 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.154, 适应度梯度: -18491.492, 聚类评分: 0.000, 覆盖率: 0.169, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:18,555 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:18,555 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:18,555 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 10:29:18,560 - visualization.landscape_visualizer - INFO - 插值约束: 20 个点被约束到最小值 14490.00
2025-08-05 10:29:18,562 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.9%, 梯度: 3582.51 → 3334.43
2025-08-05 10:29:18,693 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_152_20250805_102918.html
2025-08-05 10:29:18,769 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_152_20250805_102918.html
2025-08-05 10:29:18,769 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 152
2025-08-05 10:29:18,769 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:18,770 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2629秒
2025-08-05 10:29:18,770 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15384615384615385, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -18491.492307692308, 'local_optima_density': 0.15384615384615385, 'gradient_variance': 1339262896.8222482, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.169, 'fitness_entropy': 0.6760150508789764, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -18491.492)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.169)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360958.555509, 'performance_metrics': {}}}
2025-08-05 10:29:18,770 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:18,770 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:18,770 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:18,770 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:18,771 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,771 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:18,771 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,771 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:18,771 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:18,771 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:18,771 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:18,772 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:18,772 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:18,772 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:18,772 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:18,772 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,784 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 10:29:18,784 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,785 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,785 - ExplorationExpert - INFO - 探索路径生成完成，成本: 75256.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,786 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [74, 95, 78, 61, 47, 26, 51, 30, 17, 45, 62, 76, 77, 63, 60, 103, 54, 19, 50, 56, 66, 101, 99, 46, 32, 14, 27, 28, 6, 25, 24, 13, 57, 72, 65, 73, 85, 96, 55, 75, 90, 87, 98, 100, 44, 71, 53, 48, 21, 7, 1, 34, 42, 40, 35, 8, 15, 22, 23, 38, 49, 83, 81, 84, 82, 52, 36, 104, 39, 41, 12, 102, 2, 33, 59, 88, 94, 97, 58, 92, 89, 93, 70, 68, 67, 43, 20, 29, 31, 9, 18, 16, 11, 3, 0, 4, 64, 69, 80, 79, 86, 91, 37, 10, 5], 'cur_cost': 75256.0, 'intermediate_solutions': [{'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 75, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 62, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78385.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 58, 42, 31, 52, 11, 8, 22, 43, 7, 12, 24, 45, 86, 90, 80, 87, 89, 69, 76, 61, 18, 13, 103, 38, 25, 41, 19, 50, 51, 39, 37, 57, 81, 93, 95, 72, 54, 1, 32, 104, 56, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78218.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 66, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 79447.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,786 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 75256.00)
2025-08-05 10:29:18,786 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:18,786 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:18,786 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,790 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20954.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,791 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20954.0, 'intermediate_solutions': [{'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 61, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 29, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25713.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 38, 41, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 57, 52, 51, 45, 42, 40, 43, 46, 50, 53, 56, 104, 58, 55, 54, 49, 47, 44, 39, 103, 32, 35, 36, 16, 15, 17, 24, 25, 26, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23280.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 4, 8, 7, 2, 1, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 5, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,792 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 20954.00)
2025-08-05 10:29:18,792 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:18,792 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:18,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,796 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21884.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,797 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21884.0, 'intermediate_solutions': [{'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 88, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 50, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 110039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 30, 74, 60, 11, 64, 12, 79, 23, 28, 80, 18, 96, 62], 'cur_cost': 107888.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 38, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,797 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21884.00)
2025-08-05 10:29:18,798 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:18,798 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:18,798 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,801 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21630.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,803 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21630.0, 'intermediate_solutions': [{'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 90, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 32, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 28029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 87, 86, 65, 64, 60, 59, 38, 37, 34, 33, 93, 94, 99], 'cur_cost': 24243.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 16, 15, 17, 24, 25, 75, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,803 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21630.00)
2025-08-05 10:29:18,803 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:18,803 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:18,803 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,807 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,808 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,808 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,808 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,809 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21006.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,809 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21006.0, 'intermediate_solutions': [{'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 102, 19, 22, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20633.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 64, 60, 59, 38, 37, 34, 33, 13, 12, 3, 4, 8, 7, 11, 1, 5, 6, 9, 14, 48, 63, 101, 100, 96, 95, 65, 86, 87, 93, 94, 99], 'cur_cost': 22947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 58, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22408.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,809 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 21006.00)
2025-08-05 10:29:18,809 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:18,809 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:18,809 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,813 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,815 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21729.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,815 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21729.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 87, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 46, 93, 94, 99], 'cur_cost': 25904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 44, 39, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,815 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 21729.00)
2025-08-05 10:29:18,816 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:18,816 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:18,816 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,820 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,821 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,821 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,821 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21106.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,821 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21106.0, 'intermediate_solutions': [{'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 65, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 75, 86, 87, 93, 94, 99], 'cur_cost': 24224.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 83, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25661.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,822 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21106.00)
2025-08-05 10:29:18,822 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:18,822 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:18,822 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:18,826 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:18,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:18,827 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22825.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:18,827 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22825.0, 'intermediate_solutions': [{'tour': [0, 19, 7, 21, 20, 68, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 28, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27285.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 38, 37, 34, 33, 13, 12, 3, 4, 8, 16, 15, 17, 24, 25, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24506.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 7, 68, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:18,828 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 22825.00)
2025-08-05 10:29:18,828 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:18,828 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:18,828 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:18,828 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 124663.0
2025-08-05 10:29:18,839 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:18,839 - ExploitationExpert - INFO - res_population_costs: [14490, 14498, 14936.0]
2025-08-05 10:29:18,839 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 10:29:18,841 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:18,841 - ExploitationExpert - INFO - populations: [{'tour': [74, 95, 78, 61, 47, 26, 51, 30, 17, 45, 62, 76, 77, 63, 60, 103, 54, 19, 50, 56, 66, 101, 99, 46, 32, 14, 27, 28, 6, 25, 24, 13, 57, 72, 65, 73, 85, 96, 55, 75, 90, 87, 98, 100, 44, 71, 53, 48, 21, 7, 1, 34, 42, 40, 35, 8, 15, 22, 23, 38, 49, 83, 81, 84, 82, 52, 36, 104, 39, 41, 12, 102, 2, 33, 59, 88, 94, 97, 58, 92, 89, 93, 70, 68, 67, 43, 20, 29, 31, 9, 18, 16, 11, 3, 0, 4, 64, 69, 80, 79, 86, 91, 37, 10, 5], 'cur_cost': 75256.0}, {'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20954.0}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21884.0}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21630.0}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21006.0}, {'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21729.0}, {'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21106.0}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22825.0}, {'tour': array([ 82,  17,  50,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18], dtype=int64), 'cur_cost': 124663.0}, {'tour': [55, 102, 53, 45, 58, 65, 78, 54, 27, 25, 100, 82, 13, 30, 91, 4, 87, 10, 49, 61, 103, 32, 29, 12, 92, 22, 80, 46, 3, 70, 86, 44, 28, 7, 34, 99, 63, 51, 14, 24, 26, 0, 8, 83, 11, 17, 1, 43, 64, 5, 67, 31, 89, 57, 40, 19, 21, 47, 39, 96, 18, 59, 68, 77, 104, 71, 20, 97, 48, 6, 35, 72, 9, 38, 36, 93, 33, 42, 69, 74, 84, 98, 60, 81, 16, 41, 23, 85, 62, 73, 50, 88, 101, 90, 66, 2, 94, 56, 79, 37, 95, 76, 15, 75, 52], 'cur_cost': 123944.0}]
2025-08-05 10:29:18,842 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:18,842 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 393, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 393, 'cache_hits': 0, 'similarity_calculations': 2061, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:18,843 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 82,  17,  50,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18], dtype=int64), 'cur_cost': 124663.0, 'intermediate_solutions': [{'tour': array([ 26,  75,  18,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 130591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 17,  26,  75,  18,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 130671.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 40,  17,  26,  75,  18,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 131858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 18,  17,  26,  75,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 129389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 18,  40,  17,  26,  75,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 128516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:18,843 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 124663.00)
2025-08-05 10:29:18,843 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:18,843 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:18,843 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:18,843 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 125362.0
2025-08-05 10:29:18,855 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:18,855 - ExploitationExpert - INFO - res_population_costs: [14490, 14498, 14936.0, 14401]
2025-08-05 10:29:18,856 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:18,858 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:18,858 - ExploitationExpert - INFO - populations: [{'tour': [74, 95, 78, 61, 47, 26, 51, 30, 17, 45, 62, 76, 77, 63, 60, 103, 54, 19, 50, 56, 66, 101, 99, 46, 32, 14, 27, 28, 6, 25, 24, 13, 57, 72, 65, 73, 85, 96, 55, 75, 90, 87, 98, 100, 44, 71, 53, 48, 21, 7, 1, 34, 42, 40, 35, 8, 15, 22, 23, 38, 49, 83, 81, 84, 82, 52, 36, 104, 39, 41, 12, 102, 2, 33, 59, 88, 94, 97, 58, 92, 89, 93, 70, 68, 67, 43, 20, 29, 31, 9, 18, 16, 11, 3, 0, 4, 64, 69, 80, 79, 86, 91, 37, 10, 5], 'cur_cost': 75256.0}, {'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20954.0}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21884.0}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21630.0}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21006.0}, {'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21729.0}, {'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21106.0}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22825.0}, {'tour': array([ 82,  17,  50,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18], dtype=int64), 'cur_cost': 124663.0}, {'tour': array([ 77,  29,  78,  53,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103], dtype=int64), 'cur_cost': 125362.0}]
2025-08-05 10:29:18,860 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:18,860 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 394, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 394, 'cache_hits': 0, 'similarity_calculations': 2064, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:18,861 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 77,  29,  78,  53,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103], dtype=int64), 'cur_cost': 125362.0, 'intermediate_solutions': [{'tour': array([ 53, 102,  55,  45,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 45,  53, 102,  55,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 58,  45,  53, 102,  55,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 124035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 55,  45,  53, 102,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 55,  58,  45,  53, 102,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:18,861 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 125362.00)
2025-08-05 10:29:18,861 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:18,861 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:18,864 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [74, 95, 78, 61, 47, 26, 51, 30, 17, 45, 62, 76, 77, 63, 60, 103, 54, 19, 50, 56, 66, 101, 99, 46, 32, 14, 27, 28, 6, 25, 24, 13, 57, 72, 65, 73, 85, 96, 55, 75, 90, 87, 98, 100, 44, 71, 53, 48, 21, 7, 1, 34, 42, 40, 35, 8, 15, 22, 23, 38, 49, 83, 81, 84, 82, 52, 36, 104, 39, 41, 12, 102, 2, 33, 59, 88, 94, 97, 58, 92, 89, 93, 70, 68, 67, 43, 20, 29, 31, 9, 18, 16, 11, 3, 0, 4, 64, 69, 80, 79, 86, 91, 37, 10, 5], 'cur_cost': 75256.0, 'intermediate_solutions': [{'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 75, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 62, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78385.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 58, 42, 31, 52, 11, 8, 22, 43, 7, 12, 24, 45, 86, 90, 80, 87, 89, 69, 76, 61, 18, 13, 103, 38, 25, 41, 19, 50, 51, 39, 37, 57, 81, 93, 95, 72, 54, 1, 32, 104, 56, 46, 16, 62, 83, 73, 70, 66, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 78218.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 44, 26, 29, 49, 67, 53, 27, 20, 102, 14, 56, 104, 32, 1, 54, 72, 95, 93, 81, 57, 37, 39, 51, 50, 19, 41, 25, 38, 103, 13, 18, 61, 76, 69, 89, 87, 80, 90, 86, 45, 24, 12, 7, 43, 22, 8, 11, 52, 31, 42, 58, 46, 16, 62, 83, 73, 70, 88, 78, 96, 79, 91, 100, 77, 47, 15, 36, 9, 28, 55, 66, 48, 68, 92, 82, 74, 99, 63, 60, 59, 35, 5, 21, 0, 17, 10, 2, 6, 4, 23, 75, 71, 84, 98, 65, 85, 101, 94, 40, 34, 3, 33, 64, 97], 'cur_cost': 79447.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20954.0, 'intermediate_solutions': [{'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 61, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 29, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25713.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 4, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 38, 41, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 57, 52, 51, 45, 42, 40, 43, 46, 50, 53, 56, 104, 58, 55, 54, 49, 47, 44, 39, 103, 32, 35, 36, 16, 15, 17, 24, 25, 26, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23280.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 4, 8, 7, 2, 1, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 5, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 13, 12, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25921.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21884.0, 'intermediate_solutions': [{'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 88, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 50, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 110039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 38, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 30, 74, 60, 11, 64, 12, 79, 23, 28, 80, 18, 96, 62], 'cur_cost': 107888.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 44, 4, 8, 49, 67, 1, 6, 9, 10, 14, 104, 54, 72, 31, 27, 57, 37, 51, 26, 19, 41, 25, 16, 35, 32, 103, 89, 87, 90, 55, 58, 24, 56, 50, 22, 43, 42, 45, 52, 38, 61, 83, 66, 68, 75, 91, 100, 71, 47, 15, 77, 81, 82, 48, 88, 98, 63, 92, 59, 101, 2, 34, 46, 65, 20, 94, 36, 3, 70, 76, 7, 17, 13, 40, 102, 97, 69, 5, 53, 93, 21, 84, 39, 99, 73, 85, 33, 78, 95, 86, 29, 28, 23, 79, 12, 64, 11, 60, 74, 30, 80, 18, 96, 62], 'cur_cost': 107576.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21630.0, 'intermediate_solutions': [{'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 90, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 32, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 28029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 16, 15, 17, 24, 25, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 87, 86, 65, 64, 60, 59, 38, 37, 34, 33, 93, 94, 99], 'cur_cost': 24243.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 16, 15, 17, 24, 25, 75, 26, 23, 22, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21006.0, 'intermediate_solutions': [{'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 102, 19, 22, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20633.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 64, 60, 59, 38, 37, 34, 33, 13, 12, 3, 4, 8, 7, 11, 1, 5, 6, 9, 14, 48, 63, 101, 100, 96, 95, 65, 86, 87, 93, 94, 99], 'cur_cost': 22947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 2, 15, 17, 24, 25, 16, 18, 23, 58, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22408.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21729.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 87, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 46, 93, 94, 99], 'cur_cost': 25904.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 44, 39, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21106.0, 'intermediate_solutions': [{'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 65, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 75, 86, 87, 93, 94, 99], 'cur_cost': 24224.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 5, 19, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 83, 6, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25661.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22825.0, 'intermediate_solutions': [{'tour': [0, 19, 7, 21, 20, 68, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 28, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27285.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 7, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 38, 37, 34, 33, 13, 12, 3, 4, 8, 16, 15, 17, 24, 25, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24506.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 7, 68, 21, 20, 28, 29, 30, 31, 27, 22, 102, 14, 10, 9, 6, 5, 1, 2, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 82,  17,  50,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18], dtype=int64), 'cur_cost': 124663.0, 'intermediate_solutions': [{'tour': array([ 26,  75,  18,  17,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 130591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 17,  26,  75,  18,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 130671.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 40,  17,  26,  75,  18,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 131858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 18,  17,  26,  75,  40,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 129389.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 18,  40,  17,  26,  75,  79,  49,  84,  51,   8,   1, 103,  86,
        23,  15,  59,  33,  99,  37,  53,  85,  76,  20,  90,   9,  73,
        29,  83,  74,  67,  45,  68,  31,   5, 104,  92,  34, 100,  55,
        72,  69,  32,  98,  63,  27, 101,  70,  52,  12,  60,  24,  41,
        22,  58,  91,  10,   7,  42,  47,   4,  80,  39,  65,  43,  36,
        66,   0,  13,  89,  35,  25,  14,  50,  64,   3,  48,  56,  87,
         6,  19,  88,  97,  93,  54,  94,  81,   2,  44,  71,  62,  82,
        21,  61,  46,  78,  16, 102,  96,  57,  28,  30,  77,  38,  95,
        11]), 'cur_cost': 128516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 77,  29,  78,  53,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103], dtype=int64), 'cur_cost': 125362.0, 'intermediate_solutions': [{'tour': array([ 53, 102,  55,  45,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 45,  53, 102,  55,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 58,  45,  53, 102,  55,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 124035.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 55,  45,  53, 102,  58,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 55,  58,  45,  53, 102,  65,  78,  54,  27,  25, 100,  82,  13,
        30,  91,   4,  87,  10,  49,  61, 103,  32,  29,  12,  92,  22,
        80,  46,   3,  70,  86,  44,  28,   7,  34,  99,  63,  51,  14,
        24,  26,   0,   8,  83,  11,  17,   1,  43,  64,   5,  67,  31,
        89,  57,  40,  19,  21,  47,  39,  96,  18,  59,  68,  77, 104,
        71,  20,  97,  48,   6,  35,  72,   9,  38,  36,  93,  33,  42,
        69,  74,  84,  98,  60,  81,  16,  41,  23,  85,  62,  73,  50,
        88, 101,  90,  66,   2,  94,  56,  79,  37,  95,  76,  15,  75,
        52]), 'cur_cost': 123820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:18,864 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:18,864 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:18,869 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20954.000, 多样性=0.875
2025-08-05 10:29:18,869 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:18,869 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:18,869 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:18,870 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05807588713609615, 'best_improvement': -0.025548159749412686}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0796795014466948}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06892501718575389, 'recent_improvements': [-0.06765728175044695, 0.007103141611790932, 0.07019275262106082], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 14401, 'new_best_cost': 14401, 'quality_improvement': 0.0, 'old_diversity': 0.6619047619047619, 'new_diversity': 0.6619047619047619, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:18,870 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:18,870 - __main__ - INFO - lin105 开始进化第 3 代
2025-08-05 10:29:18,870 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:18,871 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:18,872 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20954.000, 多样性=0.875
2025-08-05 10:29:18,872 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:18,875 - PathExpert - INFO - 路径结构分析完成: 公共边数量=90, 路径相似性=0.875
2025-08-05 10:29:18,875 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:18,878 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.662
2025-08-05 10:29:18,880 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:18,880 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:18,880 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:18,880 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:18,927 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -14218.129, 聚类评分: 0.000, 覆盖率: 0.170, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:18,928 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:18,928 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:18,928 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 10:29:18,934 - visualization.landscape_visualizer - INFO - 插值约束: 47 个点被约束到最小值 14401.00
2025-08-05 10:29:18,936 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.8%, 梯度: 2847.77 → 2626.34
2025-08-05 10:29:19,040 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_153_20250805_102918.html
2025-08-05 10:29:19,107 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_153_20250805_102918.html
2025-08-05 10:29:19,108 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 153
2025-08-05 10:29:19,108 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:19,109 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2280秒
2025-08-05 10:29:19,109 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14218.128571428571, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 1231783164.3063264, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.17, 'fitness_entropy': 0.5970948432879916, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14218.129)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.170)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360958.9288676, 'performance_metrics': {}}}
2025-08-05 10:29:19,109 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:19,109 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:19,109 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:19,109 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:19,110 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,110 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:19,110 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,110 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:19,110 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:19,110 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,111 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:19,111 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:19,111 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:19,112 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:19,112 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,112 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,112 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 123941.0
2025-08-05 10:29:19,125 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:19,125 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0]
2025-08-05 10:29:19,125 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 10:29:19,127 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,127 - ExploitationExpert - INFO - populations: [{'tour': array([ 48,  73,  76,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63], dtype=int64), 'cur_cost': 123941.0}, {'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20954.0}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21884.0}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21630.0}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21006.0}, {'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21729.0}, {'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21106.0}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22825.0}, {'tour': [82, 17, 50, 79, 2, 94, 46, 87, 89, 69, 84, 100, 47, 23, 41, 30, 72, 54, 25, 35, 55, 8, 70, 29, 101, 43, 63, 73, 3, 10, 19, 68, 98, 86, 77, 81, 34, 80, 62, 1, 59, 88, 96, 13, 0, 15, 37, 85, 38, 16, 7, 48, 36, 75, 14, 45, 32, 104, 60, 21, 76, 78, 58, 71, 64, 92, 99, 44, 22, 5, 28, 52, 40, 103, 95, 42, 4, 53, 11, 57, 49, 91, 93, 31, 61, 9, 66, 6, 65, 12, 83, 56, 67, 102, 74, 26, 27, 97, 51, 90, 39, 33, 20, 24, 18], 'cur_cost': 124663.0}, {'tour': [77, 29, 78, 53, 28, 32, 24, 20, 0, 46, 12, 85, 26, 57, 16, 30, 56, 104, 70, 93, 2, 45, 80, 51, 76, 15, 37, 86, 61, 50, 40, 74, 21, 34, 81, 22, 64, 62, 8, 100, 43, 68, 18, 101, 95, 69, 38, 87, 73, 98, 92, 47, 52, 27, 83, 55, 97, 39, 7, 5, 19, 35, 65, 67, 60, 89, 88, 90, 9, 48, 96, 3, 66, 25, 54, 84, 63, 99, 44, 49, 82, 11, 94, 10, 33, 14, 75, 58, 41, 17, 1, 91, 59, 72, 23, 42, 102, 71, 13, 36, 79, 4, 6, 31, 103], 'cur_cost': 125362.0}]
2025-08-05 10:29:19,129 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,129 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 395, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 395, 'cache_hits': 0, 'similarity_calculations': 2068, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,130 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 48,  73,  76,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63], dtype=int64), 'cur_cost': 123941.0, 'intermediate_solutions': [{'tour': array([ 78,  95,  74,  61,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 61,  78,  95,  74,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 47,  61,  78,  95,  74,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 74,  61,  78,  95,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75731.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 74,  47,  61,  78,  95,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 76878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,130 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 123941.00)
2025-08-05 10:29:19,130 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:19,130 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:19,130 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,133 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 10:29:19,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97268.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,135 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 97268.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 87, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 43, 93, 94, 99], 'cur_cost': 26190.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 16, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 14, 102, 19, 22, 27, 26, 23, 15, 25, 24, 17, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24074.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 80, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22863.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,135 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 97268.00)
2025-08-05 10:29:19,135 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:19,135 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:19,135 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,140 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,141 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23301.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,142 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23301.0, 'intermediate_solutions': [{'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 52, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 28, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25531.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25320.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 45, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23385.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,142 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 23301.00)
2025-08-05 10:29:19,142 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:19,142 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:19,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,146 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,147 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,147 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22034.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,147 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22034.0, 'intermediate_solutions': [{'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 95, 98, 97, 92, 91, 90, 89, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23729.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 33, 3, 12, 13, 8, 11, 2, 1, 5, 6, 9, 10, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 67, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22374.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,147 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 22034.00)
2025-08-05 10:29:19,148 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:19,148 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:19,148 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,151 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22616.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,153 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22616.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 16, 17, 24, 25, 75, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 15, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27420.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 20, 102, 19, 22, 27, 26, 23, 18, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 54, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23215.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,153 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 22616.00)
2025-08-05 10:29:19,153 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:19,153 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:19,153 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,157 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,157 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,158 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,159 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21341.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,159 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21341.0, 'intermediate_solutions': [{'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 92, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 8, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 30738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 18, 23, 26, 25, 24, 17, 15, 8, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 14, 10, 9, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 6, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24008.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,159 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 21341.00)
2025-08-05 10:29:19,159 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:19,159 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:19,159 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,163 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21536.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,164 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21536.0, 'intermediate_solutions': [{'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 59, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 102, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 31, 30, 29, 27, 22, 19, 18, 16, 0, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21327.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 77, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23730.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,165 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21536.00)
2025-08-05 10:29:19,165 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:19,165 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:19,165 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,178 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 10:29:19,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68595.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,180 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68595.0, 'intermediate_solutions': [{'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 11, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 40, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 37, 34, 33, 3, 4, 13, 11, 1, 5, 6, 9, 10, 14, 48, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 61, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23708.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,180 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 68595.00)
2025-08-05 10:29:19,180 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:19,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,181 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,181 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 122502.0
2025-08-05 10:29:19,193 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:19,193 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0]
2025-08-05 10:29:19,193 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 10:29:19,196 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,196 - ExploitationExpert - INFO - populations: [{'tour': array([ 48,  73,  76,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63], dtype=int64), 'cur_cost': 123941.0}, {'tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 97268.0}, {'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23301.0}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22034.0}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22616.0}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21341.0}, {'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21536.0}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68595.0}, {'tour': array([ 38,  45,  58, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6], dtype=int64), 'cur_cost': 122502.0}, {'tour': [77, 29, 78, 53, 28, 32, 24, 20, 0, 46, 12, 85, 26, 57, 16, 30, 56, 104, 70, 93, 2, 45, 80, 51, 76, 15, 37, 86, 61, 50, 40, 74, 21, 34, 81, 22, 64, 62, 8, 100, 43, 68, 18, 101, 95, 69, 38, 87, 73, 98, 92, 47, 52, 27, 83, 55, 97, 39, 7, 5, 19, 35, 65, 67, 60, 89, 88, 90, 9, 48, 96, 3, 66, 25, 54, 84, 63, 99, 44, 49, 82, 11, 94, 10, 33, 14, 75, 58, 41, 17, 1, 91, 59, 72, 23, 42, 102, 71, 13, 36, 79, 4, 6, 31, 103], 'cur_cost': 125362.0}]
2025-08-05 10:29:19,198 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,198 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 396, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 396, 'cache_hits': 0, 'similarity_calculations': 2073, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,200 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 38,  45,  58, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6], dtype=int64), 'cur_cost': 122502.0, 'intermediate_solutions': [{'tour': array([ 50,  17,  82,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 123203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 79,  50,  17,  82,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 124637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([  2,  79,  50,  17,  82,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 121681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 82,  79,  50,  17,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 121538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 82,   2,  79,  50,  17,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 124546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,200 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 122502.00)
2025-08-05 10:29:19,200 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:19,200 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,200 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,200 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 114488.0
2025-08-05 10:29:19,213 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:19,213 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0]
2025-08-05 10:29:19,213 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 10:29:19,215 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,216 - ExploitationExpert - INFO - populations: [{'tour': array([ 48,  73,  76,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63], dtype=int64), 'cur_cost': 123941.0}, {'tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 97268.0}, {'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23301.0}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22034.0}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22616.0}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21341.0}, {'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21536.0}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68595.0}, {'tour': array([ 38,  45,  58, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6], dtype=int64), 'cur_cost': 122502.0}, {'tour': array([ 43,  11,  25,  54, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30], dtype=int64), 'cur_cost': 114488.0}]
2025-08-05 10:29:19,218 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,218 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 397, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 397, 'cache_hits': 0, 'similarity_calculations': 2079, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,219 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 43,  11,  25,  54, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30], dtype=int64), 'cur_cost': 114488.0, 'intermediate_solutions': [{'tour': array([ 78,  29,  77,  53,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 53,  78,  29,  77,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125289.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 28,  53,  78,  29,  77,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 77,  53,  78,  29,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 123829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 77,  28,  53,  78,  29,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,219 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 114488.00)
2025-08-05 10:29:19,219 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:19,219 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:19,223 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 48,  73,  76,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63], dtype=int64), 'cur_cost': 123941.0, 'intermediate_solutions': [{'tour': array([ 78,  95,  74,  61,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 61,  78,  95,  74,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 47,  61,  78,  95,  74,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 74,  61,  78,  95,  47,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 75731.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 74,  47,  61,  78,  95,  26,  51,  30,  17,  45,  62,  76,  77,
        63,  60, 103,  54,  19,  50,  56,  66, 101,  99,  46,  32,  14,
        27,  28,   6,  25,  24,  13,  57,  72,  65,  73,  85,  96,  55,
        75,  90,  87,  98, 100,  44,  71,  53,  48,  21,   7,   1,  34,
        42,  40,  35,   8,  15,  22,  23,  38,  49,  83,  81,  84,  82,
        52,  36, 104,  39,  41,  12, 102,   2,  33,  59,  88,  94,  97,
        58,  92,  89,  93,  70,  68,  67,  43,  20,  29,  31,   9,  18,
        16,  11,   3,   0,   4,  64,  69,  80,  79,  86,  91,  37,  10,
         5]), 'cur_cost': 76878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 97268.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 87, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 43, 93, 94, 99], 'cur_cost': 26190.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 16, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 14, 102, 19, 22, 27, 26, 23, 15, 25, 24, 17, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24074.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 16, 17, 24, 25, 15, 23, 26, 27, 22, 19, 102, 14, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 80, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22863.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23301.0, 'intermediate_solutions': [{'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 52, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 28, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25531.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25320.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 6, 5, 1, 2, 7, 11, 13, 12, 4, 3, 45, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23385.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22034.0, 'intermediate_solutions': [{'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 95, 98, 97, 92, 91, 90, 89, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23729.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 33, 3, 12, 13, 8, 11, 2, 1, 5, 6, 9, 10, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24439.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 67, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 13, 12, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22374.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22616.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 16, 17, 24, 25, 75, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 15, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27420.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 31, 30, 29, 28, 21, 20, 102, 19, 22, 27, 26, 23, 18, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 54, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 5, 1, 2, 7, 8, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23215.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21341.0, 'intermediate_solutions': [{'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 92, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 8, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 30738.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 14, 10, 9, 6, 5, 1, 2, 7, 18, 23, 26, 25, 24, 17, 15, 8, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 14, 10, 9, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 6, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24008.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21536.0, 'intermediate_solutions': [{'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 59, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 102, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26357.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 31, 30, 29, 27, 22, 19, 18, 16, 0, 8, 15, 17, 24, 25, 26, 23, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21327.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 7, 8, 15, 17, 24, 25, 26, 23, 32, 77, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23730.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68595.0, 'intermediate_solutions': [{'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 11, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 40, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 37, 34, 33, 3, 4, 13, 11, 1, 5, 6, 9, 10, 14, 48, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24384.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 22, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 61, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 13, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23708.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 38,  45,  58, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6], dtype=int64), 'cur_cost': 122502.0, 'intermediate_solutions': [{'tour': array([ 50,  17,  82,  79,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 123203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 79,  50,  17,  82,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 124637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([  2,  79,  50,  17,  82,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 121681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 82,  79,  50,  17,   2,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 121538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 82,   2,  79,  50,  17,  94,  46,  87,  89,  69,  84, 100,  47,
        23,  41,  30,  72,  54,  25,  35,  55,   8,  70,  29, 101,  43,
        63,  73,   3,  10,  19,  68,  98,  86,  77,  81,  34,  80,  62,
         1,  59,  88,  96,  13,   0,  15,  37,  85,  38,  16,   7,  48,
        36,  75,  14,  45,  32, 104,  60,  21,  76,  78,  58,  71,  64,
        92,  99,  44,  22,   5,  28,  52,  40, 103,  95,  42,   4,  53,
        11,  57,  49,  91,  93,  31,  61,   9,  66,   6,  65,  12,  83,
        56,  67, 102,  74,  26,  27,  97,  51,  90,  39,  33,  20,  24,
        18]), 'cur_cost': 124546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 43,  11,  25,  54, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30], dtype=int64), 'cur_cost': 114488.0, 'intermediate_solutions': [{'tour': array([ 78,  29,  77,  53,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 53,  78,  29,  77,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125289.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 28,  53,  78,  29,  77,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 77,  53,  78,  29,  28,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 123829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 77,  28,  53,  78,  29,  32,  24,  20,   0,  46,  12,  85,  26,
        57,  16,  30,  56, 104,  70,  93,   2,  45,  80,  51,  76,  15,
        37,  86,  61,  50,  40,  74,  21,  34,  81,  22,  64,  62,   8,
       100,  43,  68,  18, 101,  95,  69,  38,  87,  73,  98,  92,  47,
        52,  27,  83,  55,  97,  39,   7,   5,  19,  35,  65,  67,  60,
        89,  88,  90,   9,  48,  96,   3,  66,  25,  54,  84,  63,  99,
        44,  49,  82,  11,  94,  10,  33,  14,  75,  58,  41,  17,   1,
        91,  59,  72,  23,  42, 102,  71,  13,  36,  79,   4,   6,  31,
       103]), 'cur_cost': 125323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:19,224 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:19,224 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:19,229 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21341.000, 多样性=0.967
2025-08-05 10:29:19,229 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:19,229 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:19,229 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:19,230 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11516967005640565, 'best_improvement': -0.01846902739333779}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.10447400241837967}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.025486372762152616, 'recent_improvements': [0.007103141611790932, 0.07019275262106082, 0.05807588713609615], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 14401, 'new_best_cost': 14401, 'quality_improvement': 0.0, 'old_diversity': 0.6619047619047619, 'new_diversity': 0.6619047619047619, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:19,230 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:19,230 - __main__ - INFO - lin105 开始进化第 4 代
2025-08-05 10:29:19,230 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:19,231 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:19,231 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21341.000, 多样性=0.967
2025-08-05 10:29:19,232 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:19,235 - PathExpert - INFO - 路径结构分析完成: 公共边数量=22, 路径相似性=0.967
2025-08-05 10:29:19,236 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:19,238 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.662
2025-08-05 10:29:19,240 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:19,240 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:19,241 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:19,241 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:19,292 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -20761.157, 聚类评分: 0.000, 覆盖率: 0.171, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:19,293 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:19,293 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:19,293 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 10:29:19,298 - visualization.landscape_visualizer - INFO - 插值约束: 22 个点被约束到最小值 14401.00
2025-08-05 10:29:19,299 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.9%, 梯度: 3537.07 → 3362.37
2025-08-05 10:29:19,409 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_154_20250805_102919.html
2025-08-05 10:29:19,487 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_154_20250805_102919.html
2025-08-05 10:29:19,487 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 154
2025-08-05 10:29:19,487 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:19,487 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2471秒
2025-08-05 10:29:19,488 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -20761.15714285714, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 1234548352.9038777, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1709, 'fitness_entropy': 0.7149555854879349, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -20761.157)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.171)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360959.2934613, 'performance_metrics': {}}}
2025-08-05 10:29:19,488 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:19,488 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:19,488 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:19,488 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:19,489 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,489 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:19,489 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,489 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:19,489 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:19,490 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:19,490 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:19,490 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:19,491 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:19,491 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:19,491 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,491 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,491 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 129522.0
2025-08-05 10:29:19,506 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:19,507 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0, 14379]
2025-08-05 10:29:19,507 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:19,512 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,512 - ExploitationExpert - INFO - populations: [{'tour': array([ 95,  35,  44,  57,  98,  63,  25,  49, 102,  52,  77,  32,  24,
        90,  55,  65,  56,  41,  20,   0,  93,  81,  89,  80,  37,  71,
        86,  16,  83,   3,   8,  17,   4,  73,  88,  84,  38,  31,  61,
        27,  53,  42,  85,  30,  12,  46,  26,  60,  62,  94,  15,  10,
        50,  21,  33,  66,  34,   9,  43,  74,  29,  64,  47,  13,  39,
        14, 101,  68,   2,  40,  75,  48,  67,  92,   5,  19,  70,  97,
        45,  58,  91,  87,  54,  82,  28,  96,  22,  99,  36,  18,  76,
         6,   1,  79,  78,  59,  51,  69,  11, 100,   7, 103,  23,  72,
       104], dtype=int64), 'cur_cost': 129522.0}, {'tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 97268.0}, {'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23301.0}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22034.0}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22616.0}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21341.0}, {'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21536.0}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68595.0}, {'tour': [38, 45, 58, 104, 67, 84, 85, 57, 66, 68, 92, 88, 9, 83, 39, 30, 95, 48, 4, 89, 33, 47, 55, 54, 53, 36, 76, 100, 87, 8, 21, 5, 24, 81, 42, 49, 56, 27, 74, 34, 23, 72, 16, 31, 19, 102, 98, 60, 64, 43, 86, 99, 35, 59, 96, 0, 37, 14, 18, 71, 32, 12, 46, 101, 10, 75, 41, 51, 25, 2, 3, 73, 52, 80, 13, 93, 62, 29, 26, 97, 20, 11, 94, 40, 69, 70, 65, 90, 15, 61, 63, 28, 7, 103, 17, 91, 22, 77, 78, 44, 50, 1, 79, 82, 6], 'cur_cost': 122502.0}, {'tour': [43, 11, 25, 54, 100, 102, 50, 17, 48, 2, 41, 72, 38, 97, 1, 35, 39, 9, 14, 33, 60, 21, 26, 53, 57, 90, 77, 56, 78, 7, 16, 63, 99, 19, 86, 98, 4, 46, 22, 32, 73, 64, 66, 27, 31, 28, 23, 96, 81, 61, 3, 52, 62, 79, 82, 13, 89, 91, 44, 20, 5, 45, 92, 65, 101, 42, 69, 0, 18, 8, 104, 95, 51, 70, 71, 83, 59, 76, 47, 103, 80, 6, 29, 84, 88, 12, 87, 94, 67, 49, 55, 40, 10, 58, 74, 37, 75, 93, 85, 24, 36, 15, 34, 68, 30], 'cur_cost': 114488.0}]
2025-08-05 10:29:19,514 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,515 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 398, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 398, 'cache_hits': 0, 'similarity_calculations': 2086, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,516 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 95,  35,  44,  57,  98,  63,  25,  49, 102,  52,  77,  32,  24,
        90,  55,  65,  56,  41,  20,   0,  93,  81,  89,  80,  37,  71,
        86,  16,  83,   3,   8,  17,   4,  73,  88,  84,  38,  31,  61,
        27,  53,  42,  85,  30,  12,  46,  26,  60,  62,  94,  15,  10,
        50,  21,  33,  66,  34,   9,  43,  74,  29,  64,  47,  13,  39,
        14, 101,  68,   2,  40,  75,  48,  67,  92,   5,  19,  70,  97,
        45,  58,  91,  87,  54,  82,  28,  96,  22,  99,  36,  18,  76,
         6,   1,  79,  78,  59,  51,  69,  11, 100,   7, 103,  23,  72,
       104], dtype=int64), 'cur_cost': 129522.0, 'intermediate_solutions': [{'tour': array([ 76,  73,  48,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 122570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  6,  76,  73,  48,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 124003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 69,   6,  76,  73,  48,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 123305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 48,   6,  76,  73,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 122134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 48,  69,   6,  76,  73,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 123985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,517 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 129522.00)
2025-08-05 10:29:19,517 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:19,517 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:19,517 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,521 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 10:29:19,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,523 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114574.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,523 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 21, 23, 10, 30, 46, 26, 39, 76, 5, 101, 14, 100, 36, 33, 51, 12, 74, 73, 85, 50, 40, 79, 53, 20, 0, 92, 88, 17, 58, 4, 61, 62, 78, 72, 71, 70, 67, 45, 48, 18, 28, 6, 87, 35, 91, 89, 95, 99, 63, 11, 38, 97, 32, 103, 104, 60, 56, 31, 42, 83, 19, 52, 37, 34, 65, 54, 44, 1, 16, 77, 86, 25, 29, 22, 3, 81, 49, 90, 59, 9, 82, 43, 96, 98, 2, 55, 15, 102, 75, 66, 93, 24, 47, 27, 7, 68, 94, 80, 64, 57, 41, 84, 69, 13], 'cur_cost': 114574.0, 'intermediate_solutions': [{'tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 45, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 62, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 9, 5, 18, 53, 102, 42, 59, 4, 54, 17, 65, 19, 88, 92, 0, 20, 37, 79, 25, 16, 85, 31, 73, 74, 12, 33, 34, 3, 11, 100, 14, 63, 101, 84, 83, 77, 67, 64, 76, 104, 69, 39, 44, 43, 46, 50, 57, 21, 36, 30, 29, 10, 22, 27, 26, 23, 1, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98265.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 9, 5, 1, 23, 26, 27, 60, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,523 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 114574.00)
2025-08-05 10:29:19,524 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:19,524 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:19,524 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,528 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 10:29:19,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,530 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110398.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,530 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [14, 20, 28, 39, 5, 18, 33, 73, 79, 1, 92, 88, 62, 78, 34, 45, 57, 46, 6, 91, 43, 99, 63, 54, 60, 74, 72, 68, 76, 67, 25, 85, 22, 49, 90, 89, 96, 16, 100, 55, 94, 38, 37, 4, 7, 103, 104, 23, 86, 97, 84, 53, 52, 47, 58, 64, 10, 2, 0, 51, 69, 59, 65, 9, 81, 40, 8, 71, 41, 17, 11, 19, 75, 3, 98, 30, 26, 50, 102, 32, 66, 21, 29, 77, 82, 87, 27, 15, 36, 12, 48, 80, 83, 24, 56, 44, 93, 95, 70, 61, 13, 35, 31, 42, 101], 'cur_cost': 110398.0, 'intermediate_solutions': [{'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 78, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 58, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 59, 3, 4, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24700.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 12, 14, 102, 20, 28, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 29, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,530 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 110398.00)
2025-08-05 10:29:19,531 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:19,531 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:19,531 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,545 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 10:29:19,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,546 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,546 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,546 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,546 - ExplorationExpert - INFO - 探索路径生成完成，成本: 77763.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,547 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [86, 68, 78, 44, 41, 30, 40, 36, 52, 54, 79, 77, 56, 29, 53, 104, 74, 59, 45, 39, 19, 2, 18, 12, 57, 55, 47, 23, 43, 35, 6, 58, 76, 92, 93, 80, 62, 67, 51, 38, 60, 73, 88, 49, 17, 3, 42, 21, 1, 0, 8, 33, 102, 22, 24, 50, 71, 85, 72, 63, 98, 70, 96, 87, 90, 75, 84, 99, 69, 46, 27, 5, 10, 11, 34, 13, 26, 37, 48, 15, 14, 61, 66, 65, 81, 94, 83, 64, 25, 20, 9, 7, 31, 82, 101, 89, 97, 100, 91, 32, 28, 16, 4, 103, 95], 'cur_cost': 77763.0, 'intermediate_solutions': [{'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 54, 46, 43, 40, 103, 39, 44, 47, 49, 50, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22674.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 94, 93, 99], 'cur_cost': 22076.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 48, 32, 35, 36, 60, 63, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,547 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 77763.00)
2025-08-05 10:29:19,547 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:19,547 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:19,547 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,550 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 10:29:19,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115313.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,552 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 21, 23, 39, 33, 73, 79, 14, 100, 78, 45, 74, 6, 85, 50, 40, 18, 20, 92, 76, 25, 61, 49, 90, 96, 51, 37, 91, 95, 11, 38, 97, 32, 58, 72, 83, 87, 81, 65, 17, 70, 59, 29, 102, 15, 89, 82, 44, 41, 13, 86, 93, 24, 7, 88, 57, 103, 10, 104, 35, 0, 46, 60, 52, 55, 12, 48, 98, 101, 77, 5, 66, 54, 43, 27, 42, 62, 63, 56, 47, 53, 80, 99, 68, 64, 71, 94, 1, 75, 22, 4, 19, 69, 34, 3, 84, 16, 31, 36, 26, 9, 67, 2, 28, 30], 'cur_cost': 115313.0, 'intermediate_solutions': [{'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 43, 46, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22715.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 69, 44, 47, 49, 54, 55, 58, 104, 61, 62, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24518.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,552 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 115313.00)
2025-08-05 10:29:19,552 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:19,552 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:19,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,559 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,560 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22236.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,560 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 13, 2, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 6, 5, 1, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 4, 3, 12, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22236.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 96, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 1, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 32078.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 94, 93, 87, 86, 65, 64, 99], 'cur_cost': 22817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 28, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22466.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,561 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 22236.00)
2025-08-05 10:29:19,561 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:19,561 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:19,561 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,565 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,566 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22379.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,566 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 15, 6, 13, 12, 4, 3, 8, 7, 2, 1, 5, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 37, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 53, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24514.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 93, 87, 86, 65, 64, 60, 59, 38, 37, 34, 33, 4, 12, 13, 11, 2, 1, 5, 6, 9, 10, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 94, 99], 'cur_cost': 24680.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 26, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22135.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,567 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 22379.00)
2025-08-05 10:29:19,567 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:19,567 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:19,567 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:19,571 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 10:29:19,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:19,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21513.0, 路径长度: 105, 收集中间解: 3
2025-08-05 10:29:19,573 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 18, 7, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 8, 16, 17, 24, 25, 15, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21513.0, 'intermediate_solutions': [{'tour': [32, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 9, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68586.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 38, 60, 31, 53, 54, 35, 20, 14, 21, 23, 29, 15, 19, 4, 6, 1, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 56, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 70325.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:19,573 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 21513.00)
2025-08-05 10:29:19,573 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:19,573 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,573 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,574 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 128575.0
2025-08-05 10:29:19,585 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:19,585 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0, 14379]
2025-08-05 10:29:19,585 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:19,588 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,588 - ExploitationExpert - INFO - populations: [{'tour': array([ 95,  35,  44,  57,  98,  63,  25,  49, 102,  52,  77,  32,  24,
        90,  55,  65,  56,  41,  20,   0,  93,  81,  89,  80,  37,  71,
        86,  16,  83,   3,   8,  17,   4,  73,  88,  84,  38,  31,  61,
        27,  53,  42,  85,  30,  12,  46,  26,  60,  62,  94,  15,  10,
        50,  21,  33,  66,  34,   9,  43,  74,  29,  64,  47,  13,  39,
        14, 101,  68,   2,  40,  75,  48,  67,  92,   5,  19,  70,  97,
        45,  58,  91,  87,  54,  82,  28,  96,  22,  99,  36,  18,  76,
         6,   1,  79,  78,  59,  51,  69,  11, 100,   7, 103,  23,  72,
       104], dtype=int64), 'cur_cost': 129522.0}, {'tour': [8, 21, 23, 10, 30, 46, 26, 39, 76, 5, 101, 14, 100, 36, 33, 51, 12, 74, 73, 85, 50, 40, 79, 53, 20, 0, 92, 88, 17, 58, 4, 61, 62, 78, 72, 71, 70, 67, 45, 48, 18, 28, 6, 87, 35, 91, 89, 95, 99, 63, 11, 38, 97, 32, 103, 104, 60, 56, 31, 42, 83, 19, 52, 37, 34, 65, 54, 44, 1, 16, 77, 86, 25, 29, 22, 3, 81, 49, 90, 59, 9, 82, 43, 96, 98, 2, 55, 15, 102, 75, 66, 93, 24, 47, 27, 7, 68, 94, 80, 64, 57, 41, 84, 69, 13], 'cur_cost': 114574.0}, {'tour': [14, 20, 28, 39, 5, 18, 33, 73, 79, 1, 92, 88, 62, 78, 34, 45, 57, 46, 6, 91, 43, 99, 63, 54, 60, 74, 72, 68, 76, 67, 25, 85, 22, 49, 90, 89, 96, 16, 100, 55, 94, 38, 37, 4, 7, 103, 104, 23, 86, 97, 84, 53, 52, 47, 58, 64, 10, 2, 0, 51, 69, 59, 65, 9, 81, 40, 8, 71, 41, 17, 11, 19, 75, 3, 98, 30, 26, 50, 102, 32, 66, 21, 29, 77, 82, 87, 27, 15, 36, 12, 48, 80, 83, 24, 56, 44, 93, 95, 70, 61, 13, 35, 31, 42, 101], 'cur_cost': 110398.0}, {'tour': [86, 68, 78, 44, 41, 30, 40, 36, 52, 54, 79, 77, 56, 29, 53, 104, 74, 59, 45, 39, 19, 2, 18, 12, 57, 55, 47, 23, 43, 35, 6, 58, 76, 92, 93, 80, 62, 67, 51, 38, 60, 73, 88, 49, 17, 3, 42, 21, 1, 0, 8, 33, 102, 22, 24, 50, 71, 85, 72, 63, 98, 70, 96, 87, 90, 75, 84, 99, 69, 46, 27, 5, 10, 11, 34, 13, 26, 37, 48, 15, 14, 61, 66, 65, 81, 94, 83, 64, 25, 20, 9, 7, 31, 82, 101, 89, 97, 100, 91, 32, 28, 16, 4, 103, 95], 'cur_cost': 77763.0}, {'tour': [8, 21, 23, 39, 33, 73, 79, 14, 100, 78, 45, 74, 6, 85, 50, 40, 18, 20, 92, 76, 25, 61, 49, 90, 96, 51, 37, 91, 95, 11, 38, 97, 32, 58, 72, 83, 87, 81, 65, 17, 70, 59, 29, 102, 15, 89, 82, 44, 41, 13, 86, 93, 24, 7, 88, 57, 103, 10, 104, 35, 0, 46, 60, 52, 55, 12, 48, 98, 101, 77, 5, 66, 54, 43, 27, 42, 62, 63, 56, 47, 53, 80, 99, 68, 64, 71, 94, 1, 75, 22, 4, 19, 69, 34, 3, 84, 16, 31, 36, 26, 9, 67, 2, 28, 30], 'cur_cost': 115313.0}, {'tour': [0, 13, 2, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 6, 5, 1, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 4, 3, 12, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22236.0}, {'tour': [0, 15, 6, 13, 12, 4, 3, 8, 7, 2, 1, 5, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0}, {'tour': [0, 18, 7, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 8, 16, 17, 24, 25, 15, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21513.0}, {'tour': array([ 91,  61,  70,  30,  77,  35,  95,  15,   8,  48,  75,  31,  99,
        17,  78,  93,  37,  58,  12,  33,  66,  28, 103,  52,  14,  72,
       100,  73,   9,  53,  69,  64,  76,  56,  34,  43,  20,   6,  81,
         7,  36,  71,  22,  96,  39,  51,  74,  41,  87,  42,   1,  47,
        98,  67,  13,  45,  62,  29,  25,  46, 104,  49,  26,  60,  83,
        97,  79,  84,  88,  65,  40,  59,  32, 101,  92,  89,  27,  90,
        11,  10,  80,  18,  68,  16,  94,  54,  57,  85, 102,  19,  55,
        63,   5,   2,  44,  50,  21,  24,  23,  86,   0,  82,   3,  38,
         4], dtype=int64), 'cur_cost': 128575.0}, {'tour': [43, 11, 25, 54, 100, 102, 50, 17, 48, 2, 41, 72, 38, 97, 1, 35, 39, 9, 14, 33, 60, 21, 26, 53, 57, 90, 77, 56, 78, 7, 16, 63, 99, 19, 86, 98, 4, 46, 22, 32, 73, 64, 66, 27, 31, 28, 23, 96, 81, 61, 3, 52, 62, 79, 82, 13, 89, 91, 44, 20, 5, 45, 92, 65, 101, 42, 69, 0, 18, 8, 104, 95, 51, 70, 71, 83, 59, 76, 47, 103, 80, 6, 29, 84, 88, 12, 87, 94, 67, 49, 55, 40, 10, 58, 74, 37, 75, 93, 85, 24, 36, 15, 34, 68, 30], 'cur_cost': 114488.0}]
2025-08-05 10:29:19,590 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,590 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 399, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 399, 'cache_hits': 0, 'similarity_calculations': 2094, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,591 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 91,  61,  70,  30,  77,  35,  95,  15,   8,  48,  75,  31,  99,
        17,  78,  93,  37,  58,  12,  33,  66,  28, 103,  52,  14,  72,
       100,  73,   9,  53,  69,  64,  76,  56,  34,  43,  20,   6,  81,
         7,  36,  71,  22,  96,  39,  51,  74,  41,  87,  42,   1,  47,
        98,  67,  13,  45,  62,  29,  25,  46, 104,  49,  26,  60,  83,
        97,  79,  84,  88,  65,  40,  59,  32, 101,  92,  89,  27,  90,
        11,  10,  80,  18,  68,  16,  94,  54,  57,  85, 102,  19,  55,
        63,   5,   2,  44,  50,  21,  24,  23,  86,   0,  82,   3,  38,
         4], dtype=int64), 'cur_cost': 128575.0, 'intermediate_solutions': [{'tour': array([ 58,  45,  38, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123307.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([104,  58,  45,  38,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 67, 104,  58,  45,  38,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 124357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 38, 104,  58,  45,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 38,  67, 104,  58,  45,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 124244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,591 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 128575.00)
2025-08-05 10:29:19,591 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:19,592 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:19,592 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:19,592 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 136696.0
2025-08-05 10:29:19,603 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:19,603 - ExploitationExpert - INFO - res_population_costs: [14401, 14490, 14498, 14936.0, 14379]
2025-08-05 10:29:19,604 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  71,  76,  78,
        85,  83,  66,  67,  70,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  16,  15,  17,  24,  25,
        26,  23,  18,  11,  19,  22,  27,  32,  35,  36,  34,  33,  37,
        38,  59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,
        88,  92, 101, 100,  96,  95,  91,  90,  84,  82,  81,  77,  70,
        67,  66,  83,  85,  78,  76,  71,  63,  72,  75,  79,  80,  74,
        73,  68,  69,  62,  61,  57,  52,  51,  45,  42,  41,  40,  43,
        46,  50,  53,  56, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 10:29:19,607 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:19,607 - ExploitationExpert - INFO - populations: [{'tour': array([ 95,  35,  44,  57,  98,  63,  25,  49, 102,  52,  77,  32,  24,
        90,  55,  65,  56,  41,  20,   0,  93,  81,  89,  80,  37,  71,
        86,  16,  83,   3,   8,  17,   4,  73,  88,  84,  38,  31,  61,
        27,  53,  42,  85,  30,  12,  46,  26,  60,  62,  94,  15,  10,
        50,  21,  33,  66,  34,   9,  43,  74,  29,  64,  47,  13,  39,
        14, 101,  68,   2,  40,  75,  48,  67,  92,   5,  19,  70,  97,
        45,  58,  91,  87,  54,  82,  28,  96,  22,  99,  36,  18,  76,
         6,   1,  79,  78,  59,  51,  69,  11, 100,   7, 103,  23,  72,
       104], dtype=int64), 'cur_cost': 129522.0}, {'tour': [8, 21, 23, 10, 30, 46, 26, 39, 76, 5, 101, 14, 100, 36, 33, 51, 12, 74, 73, 85, 50, 40, 79, 53, 20, 0, 92, 88, 17, 58, 4, 61, 62, 78, 72, 71, 70, 67, 45, 48, 18, 28, 6, 87, 35, 91, 89, 95, 99, 63, 11, 38, 97, 32, 103, 104, 60, 56, 31, 42, 83, 19, 52, 37, 34, 65, 54, 44, 1, 16, 77, 86, 25, 29, 22, 3, 81, 49, 90, 59, 9, 82, 43, 96, 98, 2, 55, 15, 102, 75, 66, 93, 24, 47, 27, 7, 68, 94, 80, 64, 57, 41, 84, 69, 13], 'cur_cost': 114574.0}, {'tour': [14, 20, 28, 39, 5, 18, 33, 73, 79, 1, 92, 88, 62, 78, 34, 45, 57, 46, 6, 91, 43, 99, 63, 54, 60, 74, 72, 68, 76, 67, 25, 85, 22, 49, 90, 89, 96, 16, 100, 55, 94, 38, 37, 4, 7, 103, 104, 23, 86, 97, 84, 53, 52, 47, 58, 64, 10, 2, 0, 51, 69, 59, 65, 9, 81, 40, 8, 71, 41, 17, 11, 19, 75, 3, 98, 30, 26, 50, 102, 32, 66, 21, 29, 77, 82, 87, 27, 15, 36, 12, 48, 80, 83, 24, 56, 44, 93, 95, 70, 61, 13, 35, 31, 42, 101], 'cur_cost': 110398.0}, {'tour': [86, 68, 78, 44, 41, 30, 40, 36, 52, 54, 79, 77, 56, 29, 53, 104, 74, 59, 45, 39, 19, 2, 18, 12, 57, 55, 47, 23, 43, 35, 6, 58, 76, 92, 93, 80, 62, 67, 51, 38, 60, 73, 88, 49, 17, 3, 42, 21, 1, 0, 8, 33, 102, 22, 24, 50, 71, 85, 72, 63, 98, 70, 96, 87, 90, 75, 84, 99, 69, 46, 27, 5, 10, 11, 34, 13, 26, 37, 48, 15, 14, 61, 66, 65, 81, 94, 83, 64, 25, 20, 9, 7, 31, 82, 101, 89, 97, 100, 91, 32, 28, 16, 4, 103, 95], 'cur_cost': 77763.0}, {'tour': [8, 21, 23, 39, 33, 73, 79, 14, 100, 78, 45, 74, 6, 85, 50, 40, 18, 20, 92, 76, 25, 61, 49, 90, 96, 51, 37, 91, 95, 11, 38, 97, 32, 58, 72, 83, 87, 81, 65, 17, 70, 59, 29, 102, 15, 89, 82, 44, 41, 13, 86, 93, 24, 7, 88, 57, 103, 10, 104, 35, 0, 46, 60, 52, 55, 12, 48, 98, 101, 77, 5, 66, 54, 43, 27, 42, 62, 63, 56, 47, 53, 80, 99, 68, 64, 71, 94, 1, 75, 22, 4, 19, 69, 34, 3, 84, 16, 31, 36, 26, 9, 67, 2, 28, 30], 'cur_cost': 115313.0}, {'tour': [0, 13, 2, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 6, 5, 1, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 4, 3, 12, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22236.0}, {'tour': [0, 15, 6, 13, 12, 4, 3, 8, 7, 2, 1, 5, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0}, {'tour': [0, 18, 7, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 8, 16, 17, 24, 25, 15, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21513.0}, {'tour': array([ 91,  61,  70,  30,  77,  35,  95,  15,   8,  48,  75,  31,  99,
        17,  78,  93,  37,  58,  12,  33,  66,  28, 103,  52,  14,  72,
       100,  73,   9,  53,  69,  64,  76,  56,  34,  43,  20,   6,  81,
         7,  36,  71,  22,  96,  39,  51,  74,  41,  87,  42,   1,  47,
        98,  67,  13,  45,  62,  29,  25,  46, 104,  49,  26,  60,  83,
        97,  79,  84,  88,  65,  40,  59,  32, 101,  92,  89,  27,  90,
        11,  10,  80,  18,  68,  16,  94,  54,  57,  85, 102,  19,  55,
        63,   5,   2,  44,  50,  21,  24,  23,  86,   0,  82,   3,  38,
         4], dtype=int64), 'cur_cost': 128575.0}, {'tour': array([ 33,  21,  58,  20, 102,  89,  56,   0,  50,  65,  91,  84,   4,
        11,  77,  18,   2,  78,  12,  99,  46,  70,  87,  95,  48,  59,
        25,  75,  27,  98,  67,  92,  24,  86,  79,  44,  54,  13,  62,
        32,  81,  49,  69,   8,  51,   6, 104,  28,  22,  80,  45,  76,
        29,  85,  37,  73,  55, 101,  82,  15,  41,  34,  39,  16,  68,
         3,  43, 100,  93,   9,  97,  72,  31,  88,  23,  63,  26,  60,
         7,  94,  17,  14,  52,  57,  19,  61,  36,  96,  53,  38,  10,
        35, 103,  42,  30,  83,  71,  47,  40,  74,  90,   5,   1,  64,
        66], dtype=int64), 'cur_cost': 136696.0}]
2025-08-05 10:29:19,609 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:19,609 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 400, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 400, 'cache_hits': 0, 'similarity_calculations': 2103, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:19,610 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 33,  21,  58,  20, 102,  89,  56,   0,  50,  65,  91,  84,   4,
        11,  77,  18,   2,  78,  12,  99,  46,  70,  87,  95,  48,  59,
        25,  75,  27,  98,  67,  92,  24,  86,  79,  44,  54,  13,  62,
        32,  81,  49,  69,   8,  51,   6, 104,  28,  22,  80,  45,  76,
        29,  85,  37,  73,  55, 101,  82,  15,  41,  34,  39,  16,  68,
         3,  43, 100,  93,   9,  97,  72,  31,  88,  23,  63,  26,  60,
         7,  94,  17,  14,  52,  57,  19,  61,  36,  96,  53,  38,  10,
        35, 103,  42,  30,  83,  71,  47,  40,  74,  90,   5,   1,  64,
        66], dtype=int64), 'cur_cost': 136696.0, 'intermediate_solutions': [{'tour': array([ 25,  11,  43,  54, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 113695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 54,  25,  11,  43, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([100,  54,  25,  11,  43, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 43,  54,  25,  11, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 43, 100,  54,  25,  11, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 113075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:19,611 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 136696.00)
2025-08-05 10:29:19,611 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:19,611 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:19,614 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 95,  35,  44,  57,  98,  63,  25,  49, 102,  52,  77,  32,  24,
        90,  55,  65,  56,  41,  20,   0,  93,  81,  89,  80,  37,  71,
        86,  16,  83,   3,   8,  17,   4,  73,  88,  84,  38,  31,  61,
        27,  53,  42,  85,  30,  12,  46,  26,  60,  62,  94,  15,  10,
        50,  21,  33,  66,  34,   9,  43,  74,  29,  64,  47,  13,  39,
        14, 101,  68,   2,  40,  75,  48,  67,  92,   5,  19,  70,  97,
        45,  58,  91,  87,  54,  82,  28,  96,  22,  99,  36,  18,  76,
         6,   1,  79,  78,  59,  51,  69,  11, 100,   7, 103,  23,  72,
       104], dtype=int64), 'cur_cost': 129522.0, 'intermediate_solutions': [{'tour': array([ 76,  73,  48,   6,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 122570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  6,  76,  73,  48,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 124003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 69,   6,  76,  73,  48,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 123305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 48,   6,  76,  73,  69,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 122134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 48,  69,   6,  76,  73,  45,  67,  20,  92,   1,  11,  64,  32,
        89,  25, 103,  55,  23,  79,  80,  94,  91,  16,  87,  59,  44,
        78,  62,   5,  54,  58,  29,   8,   3,  88,  75,  97,  90,  72,
        81,  50,  61,  38,  18,   0,  17,  10,  95,  70,  71,  82,  43,
        68,  42,  13,   7,  56,  93,  19,   2,  96,  99,  41,  53,  84,
        28,  30,  60,  49,  22,  98,  27,  83,  35, 102,  52,  36,  86,
        15,  65,  26,  66,  21,  31,  24,  47,   4,  40,   9,  77, 100,
        51, 101,  34,  57,  14, 104,  74,  46,  33,  85,  39,  37,  12,
        63]), 'cur_cost': 123985.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 21, 23, 10, 30, 46, 26, 39, 76, 5, 101, 14, 100, 36, 33, 51, 12, 74, 73, 85, 50, 40, 79, 53, 20, 0, 92, 88, 17, 58, 4, 61, 62, 78, 72, 71, 70, 67, 45, 48, 18, 28, 6, 87, 35, 91, 89, 95, 99, 63, 11, 38, 97, 32, 103, 104, 60, 56, 31, 42, 83, 19, 52, 37, 34, 65, 54, 44, 1, 16, 77, 86, 25, 29, 22, 3, 81, 49, 90, 59, 9, 82, 43, 96, 98, 2, 55, 15, 102, 75, 66, 93, 24, 47, 27, 7, 68, 94, 80, 64, 57, 41, 84, 69, 13], 'cur_cost': 114574.0, 'intermediate_solutions': [{'tour': [8, 7, 9, 5, 1, 23, 26, 27, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 45, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 62, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 9, 5, 18, 53, 102, 42, 59, 4, 54, 17, 65, 19, 88, 92, 0, 20, 37, 79, 25, 16, 85, 31, 73, 74, 12, 33, 34, 3, 11, 100, 14, 63, 101, 84, 83, 77, 67, 64, 76, 104, 69, 39, 44, 43, 46, 50, 57, 21, 36, 30, 29, 10, 22, 27, 26, 23, 1, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 60, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98265.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 9, 5, 1, 23, 26, 27, 60, 22, 10, 29, 30, 36, 21, 57, 50, 46, 43, 44, 39, 69, 104, 76, 64, 67, 77, 83, 84, 101, 63, 14, 100, 11, 3, 34, 33, 12, 74, 73, 31, 85, 16, 25, 79, 37, 20, 0, 92, 88, 19, 65, 17, 54, 4, 59, 42, 102, 53, 18, 82, 62, 38, 52, 40, 94, 97, 58, 56, 71, 13, 24, 45, 48, 81, 28, 6, 87, 66, 78, 55, 15, 89, 98, 99, 90, 68, 51, 61, 70, 96, 80, 47, 95, 2, 72, 75, 93, 91, 32, 103, 86, 49, 41, 35], 'cur_cost': 98113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [14, 20, 28, 39, 5, 18, 33, 73, 79, 1, 92, 88, 62, 78, 34, 45, 57, 46, 6, 91, 43, 99, 63, 54, 60, 74, 72, 68, 76, 67, 25, 85, 22, 49, 90, 89, 96, 16, 100, 55, 94, 38, 37, 4, 7, 103, 104, 23, 86, 97, 84, 53, 52, 47, 58, 64, 10, 2, 0, 51, 69, 59, 65, 9, 81, 40, 8, 71, 41, 17, 11, 19, 75, 3, 98, 30, 26, 50, 102, 32, 66, 21, 29, 77, 82, 87, 27, 15, 36, 12, 48, 80, 83, 24, 56, 44, 93, 95, 70, 61, 13, 35, 31, 42, 101], 'cur_cost': 110398.0, 'intermediate_solutions': [{'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 78, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 58, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25329.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 12, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 59, 3, 4, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24700.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 12, 14, 102, 20, 28, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 2, 1, 5, 6, 9, 10, 11, 32, 35, 36, 41, 29, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [86, 68, 78, 44, 41, 30, 40, 36, 52, 54, 79, 77, 56, 29, 53, 104, 74, 59, 45, 39, 19, 2, 18, 12, 57, 55, 47, 23, 43, 35, 6, 58, 76, 92, 93, 80, 62, 67, 51, 38, 60, 73, 88, 49, 17, 3, 42, 21, 1, 0, 8, 33, 102, 22, 24, 50, 71, 85, 72, 63, 98, 70, 96, 87, 90, 75, 84, 99, 69, 46, 27, 5, 10, 11, 34, 13, 26, 37, 48, 15, 14, 61, 66, 65, 81, 94, 83, 64, 25, 20, 9, 7, 31, 82, 101, 89, 97, 100, 91, 32, 28, 16, 4, 103, 95], 'cur_cost': 77763.0, 'intermediate_solutions': [{'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 54, 46, 43, 40, 103, 39, 44, 47, 49, 50, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22674.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 94, 93, 99], 'cur_cost': 22076.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 12, 10, 9, 6, 5, 1, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 48, 32, 35, 36, 60, 63, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 21, 23, 39, 33, 73, 79, 14, 100, 78, 45, 74, 6, 85, 50, 40, 18, 20, 92, 76, 25, 61, 49, 90, 96, 51, 37, 91, 95, 11, 38, 97, 32, 58, 72, 83, 87, 81, 65, 17, 70, 59, 29, 102, 15, 89, 82, 44, 41, 13, 86, 93, 24, 7, 88, 57, 103, 10, 104, 35, 0, 46, 60, 52, 55, 12, 48, 98, 101, 77, 5, 66, 54, 43, 27, 42, 62, 63, 56, 47, 53, 80, 99, 68, 64, 71, 94, 1, 75, 22, 4, 19, 69, 34, 3, 84, 16, 31, 36, 26, 9, 67, 2, 28, 30], 'cur_cost': 115313.0, 'intermediate_solutions': [{'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 43, 46, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22715.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 21, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 69, 44, 47, 49, 54, 55, 58, 104, 61, 62, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24518.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 2, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 6, 5, 1, 11, 18, 23, 26, 25, 24, 17, 15, 16, 8, 7, 4, 3, 12, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22236.0, 'intermediate_solutions': [{'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 96, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 1, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 32078.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 94, 93, 87, 86, 65, 64, 99], 'cur_cost': 22817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 12, 13, 4, 3, 8, 7, 2, 1, 5, 6, 9, 10, 14, 102, 20, 21, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 15, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 28, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22466.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 6, 13, 12, 4, 3, 8, 7, 2, 1, 5, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 16, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22379.0, 'intermediate_solutions': [{'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 37, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 53, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24514.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 93, 87, 86, 65, 64, 60, 59, 38, 37, 34, 33, 4, 12, 13, 11, 2, 1, 5, 6, 9, 10, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 94, 99], 'cur_cost': 24680.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 26, 7, 3, 8, 16, 17, 24, 25, 15, 18, 23, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22135.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 7, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 1, 2, 8, 16, 17, 24, 25, 15, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21513.0, 'intermediate_solutions': [{'tour': [32, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 9, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68586.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 56, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 38, 60, 31, 53, 54, 35, 20, 14, 21, 23, 29, 15, 19, 4, 6, 1, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 68761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 48, 61, 50, 52, 51, 103, 18, 34, 36, 42, 55, 22, 49, 24, 3, 102, 40, 59, 47, 44, 58, 88, 94, 69, 73, 77, 80, 67, 63, 72, 43, 104, 90, 82, 87, 70, 71, 79, 86, 83, 100, 78, 84, 89, 57, 39, 64, 37, 46, 75, 45, 66, 65, 32, 17, 7, 12, 25, 41, 11, 2, 5, 30, 0, 27, 26, 1, 6, 4, 19, 15, 29, 23, 21, 14, 20, 35, 54, 53, 31, 60, 38, 16, 28, 13, 10, 8, 68, 97, 98, 96, 81, 85, 56, 95, 91, 92, 101, 76, 74, 62, 93, 99, 33], 'cur_cost': 70325.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 91,  61,  70,  30,  77,  35,  95,  15,   8,  48,  75,  31,  99,
        17,  78,  93,  37,  58,  12,  33,  66,  28, 103,  52,  14,  72,
       100,  73,   9,  53,  69,  64,  76,  56,  34,  43,  20,   6,  81,
         7,  36,  71,  22,  96,  39,  51,  74,  41,  87,  42,   1,  47,
        98,  67,  13,  45,  62,  29,  25,  46, 104,  49,  26,  60,  83,
        97,  79,  84,  88,  65,  40,  59,  32, 101,  92,  89,  27,  90,
        11,  10,  80,  18,  68,  16,  94,  54,  57,  85, 102,  19,  55,
        63,   5,   2,  44,  50,  21,  24,  23,  86,   0,  82,   3,  38,
         4], dtype=int64), 'cur_cost': 128575.0, 'intermediate_solutions': [{'tour': array([ 58,  45,  38, 104,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123307.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([104,  58,  45,  38,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123508.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 67, 104,  58,  45,  38,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 124357.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 38, 104,  58,  45,  67,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 123362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 38,  67, 104,  58,  45,  84,  85,  57,  66,  68,  92,  88,   9,
        83,  39,  30,  95,  48,   4,  89,  33,  47,  55,  54,  53,  36,
        76, 100,  87,   8,  21,   5,  24,  81,  42,  49,  56,  27,  74,
        34,  23,  72,  16,  31,  19, 102,  98,  60,  64,  43,  86,  99,
        35,  59,  96,   0,  37,  14,  18,  71,  32,  12,  46, 101,  10,
        75,  41,  51,  25,   2,   3,  73,  52,  80,  13,  93,  62,  29,
        26,  97,  20,  11,  94,  40,  69,  70,  65,  90,  15,  61,  63,
        28,   7, 103,  17,  91,  22,  77,  78,  44,  50,   1,  79,  82,
         6]), 'cur_cost': 124244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 33,  21,  58,  20, 102,  89,  56,   0,  50,  65,  91,  84,   4,
        11,  77,  18,   2,  78,  12,  99,  46,  70,  87,  95,  48,  59,
        25,  75,  27,  98,  67,  92,  24,  86,  79,  44,  54,  13,  62,
        32,  81,  49,  69,   8,  51,   6, 104,  28,  22,  80,  45,  76,
        29,  85,  37,  73,  55, 101,  82,  15,  41,  34,  39,  16,  68,
         3,  43, 100,  93,   9,  97,  72,  31,  88,  23,  63,  26,  60,
         7,  94,  17,  14,  52,  57,  19,  61,  36,  96,  53,  38,  10,
        35, 103,  42,  30,  83,  71,  47,  40,  74,  90,   5,   1,  64,
        66], dtype=int64), 'cur_cost': 136696.0, 'intermediate_solutions': [{'tour': array([ 25,  11,  43,  54, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 113695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 54,  25,  11,  43, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([100,  54,  25,  11,  43, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 43,  54,  25,  11, 100, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 114948.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 43, 100,  54,  25,  11, 102,  50,  17,  48,   2,  41,  72,  38,
        97,   1,  35,  39,   9,  14,  33,  60,  21,  26,  53,  57,  90,
        77,  56,  78,   7,  16,  63,  99,  19,  86,  98,   4,  46,  22,
        32,  73,  64,  66,  27,  31,  28,  23,  96,  81,  61,   3,  52,
        62,  79,  82,  13,  89,  91,  44,  20,   5,  45,  92,  65, 101,
        42,  69,   0,  18,   8, 104,  95,  51,  70,  71,  83,  59,  76,
        47, 103,  80,   6,  29,  84,  88,  12,  87,  94,  67,  49,  55,
        40,  10,  58,  74,  37,  75,  93,  85,  24,  36,  15,  34,  68,
        30]), 'cur_cost': 113075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:19,615 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:19,615 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:19,620 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21513.000, 多样性=0.986
2025-08-05 10:29:19,620 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:19,620 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:19,620 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:19,621 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.16316085523875498, 'best_improvement': -0.00805960357996345}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.020582439238011896}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.09268121133873325, 'recent_improvements': [0.07019275262106082, 0.05807588713609615, -0.11516967005640565], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 14379, 'new_best_cost': 14379, 'quality_improvement': 0.0, 'old_diversity': 0.5495238095238095, 'new_diversity': 0.5495238095238095, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:19,622 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:19,622 - __main__ - INFO - lin105 开始进化第 5 代
2025-08-05 10:29:19,622 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:19,622 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:19,623 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21513.000, 多样性=0.986
2025-08-05 10:29:19,624 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:19,629 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.986
2025-08-05 10:29:19,629 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:19,632 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.550
2025-08-05 10:29:19,635 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:19,635 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:19,635 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:19,635 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:19,705 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -15047.493, 聚类评分: 0.000, 覆盖率: 0.172, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:19,706 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:19,706 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:19,706 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 10:29:19,710 - visualization.landscape_visualizer - INFO - 插值约束: 10 个点被约束到最小值 14379.00
2025-08-05 10:29:19,712 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.1%, 梯度: 4366.25 → 3968.18
2025-08-05 10:29:19,821 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_155_20250805_102919.html
