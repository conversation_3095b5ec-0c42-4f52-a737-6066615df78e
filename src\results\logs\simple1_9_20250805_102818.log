2025-08-05 10:28:18,006 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-05 10:28:18,007 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:18,009 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:18,011 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.862
2025-08-05 10:28:18,012 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:18,013 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.862
2025-08-05 10:28:18,014 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:18,051 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:18,051 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:18,051 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:18,052 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:18,596 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -14.100, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.776
2025-08-05 10:28:18,596 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:18,597 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:18,597 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 10:28:18,637 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 12.27 → 11.40
2025-08-05 10:28:23,356 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250805_102823.html
2025-08-05 10:28:23,394 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250805_102823.html
2025-08-05 10:28:23,394 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-05 10:28:23,394 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:23,394 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 5.3430秒
2025-08-05 10:28:23,394 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:23,394 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14.09999999999999, 'local_optima_density': 0.1, 'gradient_variance': 24684.538, 'cluster_count': 0}, 'population_state': {'diversity': 0.7755555555555556, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9426807148172599, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.776)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360898.5960863, 'performance_metrics': {}}}
2025-08-05 10:28:23,394 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:23,394 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:23,394 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:23,394 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:23,395 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:23,395 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:23,395 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:23,396 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:23,396 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:23,396 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:23,396 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:23,396 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:23,396 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:23,396 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:23,396 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:23,396 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:23,408 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:23,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:23,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1056.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:23,604 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 4, 0, 5, 6, 8, 2, 3], 'cur_cost': 1056.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:23,604 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1056.00)
2025-08-05 10:28:23,604 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:23,604 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:23,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:23,605 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:23,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:23,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 873.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:23,606 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 5, 4, 7, 8, 2, 0, 1, 6], 'cur_cost': 873.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:23,606 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 873.00)
2025-08-05 10:28:23,606 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:23,607 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:23,607 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:23,607 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:23,608 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:23,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 987.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:23,608 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 4, 0, 1, 7, 8, 5, 3, 2], 'cur_cost': 987.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:23,608 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 987.00)
2025-08-05 10:28:23,608 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:23,608 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:23,611 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:23,613 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1106.0
2025-08-05 10:28:26,187 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:28:26,187 - ExploitationExpert - INFO - res_population_costs: [796.0]
2025-08-05 10:28:26,187 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:26,188 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:26,188 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 4, 0, 5, 6, 8, 2, 3], 'cur_cost': 1056.0}, {'tour': [3, 5, 4, 7, 8, 2, 0, 1, 6], 'cur_cost': 873.0}, {'tour': [6, 4, 0, 1, 7, 8, 5, 3, 2], 'cur_cost': 987.0}, {'tour': array([8, 2, 5, 4, 0, 7, 6, 3, 1], dtype=int64), 'cur_cost': 1106.0}, {'tour': array([7, 3, 4, 1, 8, 5, 0, 6, 2], dtype=int64), 'cur_cost': 1107.0}, {'tour': array([3, 0, 2, 4, 8, 5, 1, 7, 6], dtype=int64), 'cur_cost': 972.0}, {'tour': array([2, 7, 3, 0, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1162.0}, {'tour': array([7, 3, 0, 6, 8, 4, 2, 1, 5], dtype=int64), 'cur_cost': 946.0}, {'tour': array([1, 5, 0, 7, 6, 8, 4, 3, 2], dtype=int64), 'cur_cost': 1102.0}, {'tour': array([8, 7, 1, 6, 5, 0, 3, 2, 4], dtype=int64), 'cur_cost': 951.0}]
2025-08-05 10:28:26,190 - ExploitationExpert - INFO - 局部搜索耗时: 2.58秒，最大迭代次数: 10
2025-08-05 10:28:26,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-05 10:28:26,191 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([8, 2, 5, 4, 0, 7, 6, 3, 1], dtype=int64), 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 2, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 0, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 8, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 1, 8, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 2, 1, 8, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:26,191 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1106.00)
2025-08-05 10:28:26,191 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:26,191 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:26,191 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:26,192 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:26,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:26,192 - ExplorationExpert - INFO - 探索路径生成完成，成本: 810.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:26,192 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:26,192 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 810.00)
2025-08-05 10:28:26,192 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:26,193 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:26,193 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:26,193 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:26,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:26,193 - ExplorationExpert - INFO - 探索路径生成完成，成本: 828.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:26,193 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 5, 8, 7, 0, 4, 2, 1, 6], 'cur_cost': 828.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:26,194 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 828.00)
2025-08-05 10:28:26,194 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:26,194 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:26,194 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:26,194 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1064.0
2025-08-05 10:28:27,864 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:27,865 - ExploitationExpert - INFO - res_population_costs: [796.0, 680.0]
2025-08-05 10:28:27,865 - ExploitationExpert - INFO - res_populations: [array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-05 10:28:27,865 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:27,865 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 4, 0, 5, 6, 8, 2, 3], 'cur_cost': 1056.0}, {'tour': [3, 5, 4, 7, 8, 2, 0, 1, 6], 'cur_cost': 873.0}, {'tour': [6, 4, 0, 1, 7, 8, 5, 3, 2], 'cur_cost': 987.0}, {'tour': array([8, 2, 5, 4, 0, 7, 6, 3, 1], dtype=int64), 'cur_cost': 1106.0}, {'tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0}, {'tour': [3, 5, 8, 7, 0, 4, 2, 1, 6], 'cur_cost': 828.0}, {'tour': array([5, 4, 7, 3, 1, 2, 8, 6, 0], dtype=int64), 'cur_cost': 1064.0}, {'tour': array([7, 3, 0, 6, 8, 4, 2, 1, 5], dtype=int64), 'cur_cost': 946.0}, {'tour': array([1, 5, 0, 7, 6, 8, 4, 3, 2], dtype=int64), 'cur_cost': 1102.0}, {'tour': array([8, 7, 1, 6, 5, 0, 3, 2, 4], dtype=int64), 'cur_cost': 951.0}]
2025-08-05 10:28:27,866 - ExploitationExpert - INFO - 局部搜索耗时: 1.67秒，最大迭代次数: 10
2025-08-05 10:28:27,867 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-05 10:28:27,867 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([5, 4, 7, 3, 1, 2, 8, 6, 0], dtype=int64), 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': array([3, 7, 2, 0, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 7, 2, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 3, 7, 2, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 3, 7, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 3, 7, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:27,867 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1064.00)
2025-08-05 10:28:27,868 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:27,868 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:27,868 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:27,868 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:27,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:27,869 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1029.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:27,869 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 8, 1, 7, 5, 3, 0, 2, 4], 'cur_cost': 1029.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:27,869 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1029.00)
2025-08-05 10:28:27,869 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:27,869 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:27,869 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:27,870 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:27,870 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:27,870 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:27,870 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 1, 7, 6, 3, 0, 4, 2, 5], 'cur_cost': 1018.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:27,870 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1018.00)
2025-08-05 10:28:27,871 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:27,871 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:27,871 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:27,871 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:27,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:27,872 - ExplorationExpert - INFO - 探索路径生成完成，成本: 720.0, 路径长度: 9, 收集中间解: 0
2025-08-05 10:28:27,872 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 0, 6, 7, 5, 3, 8, 4, 2], 'cur_cost': 720.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:27,872 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 720.00)
2025-08-05 10:28:27,872 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:27,872 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:27,874 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 4, 0, 5, 6, 8, 2, 3], 'cur_cost': 1056.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 4, 7, 8, 2, 0, 1, 6], 'cur_cost': 873.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 4, 0, 1, 7, 8, 5, 3, 2], 'cur_cost': 987.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 2, 5, 4, 0, 7, 6, 3, 1], dtype=int64), 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 2, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 0, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 8, 0, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 2, 1, 8, 7, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 2, 1, 8, 3, 4, 6, 5], dtype=int64), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 8, 7, 0, 4, 2, 1, 6], 'cur_cost': 828.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 7, 3, 1, 2, 8, 6, 0], dtype=int64), 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': array([3, 7, 2, 0, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 3, 7, 2, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 0, 3, 7, 2, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 0, 3, 7, 8, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 0, 3, 7, 4, 5, 1, 6], dtype=int64), 'cur_cost': 1131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 1, 7, 5, 3, 0, 2, 4], 'cur_cost': 1029.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 1, 7, 6, 3, 0, 4, 2, 5], 'cur_cost': 1018.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 6, 7, 5, 3, 8, 4, 2], 'cur_cost': 720.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:27,875 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:27,875 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:27,877 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=720.000, 多样性=0.899
2025-08-05 10:28:27,877 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:27,877 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:27,877 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:27,877 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03878727556836217, 'best_improvement': 0.03614457831325301}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04297994269340968}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5555555555555556, 'new_diversity': 0.5555555555555556, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:27,889 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:27,889 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-05 10:28:27,889 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:27,890 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:27,891 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=720.000, 多样性=0.899
2025-08-05 10:28:27,891 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:27,892 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.899
2025-08-05 10:28:27,892 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:27,893 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.556
2025-08-05 10:28:27,895 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:27,895 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:27,895 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:27,895 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:27,901 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 19.417, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.674
2025-08-05 10:28:27,901 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:27,901 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:27,901 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 10:28:27,905 - visualization.landscape_visualizer - INFO - 插值约束: 11 个点被约束到最小值 680.00
2025-08-05 10:28:27,908 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.2%, 梯度: 12.70 → 11.92
2025-08-05 10:28:28,038 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250805_102827.html
2025-08-05 10:28:28,159 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250805_102827.html
2025-08-05 10:28:28,159 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-05 10:28:28,159 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:28,159 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2633秒
2025-08-05 10:28:28,160 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 19.416666666666668, 'local_optima_density': 0.25, 'gradient_variance': 27805.32972222222, 'cluster_count': 0}, 'population_state': {'diversity': 0.6742424242424243, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9801500856796054, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 19.417)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360907.9019432, 'performance_metrics': {}}}
2025-08-05 10:28:28,160 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:28,160 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:28,160 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:28,160 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:28,160 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:28,160 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:28,160 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:28,161 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:28,161 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:28,161 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:28,161 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:28,161 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:28,161 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:28,161 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:28,161 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:28,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,161 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 959.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,162 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [1, 7, 4, 2, 5, 6, 8, 0, 3], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 4, 0, 5, 3, 2, 8, 6], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 5, 6, 1, 8, 2, 3], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,162 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 959.00)
2025-08-05 10:28:28,162 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:28,162 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,163 - ExplorationExpert - INFO - 探索路径生成完成，成本: 732.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,163 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 5, 8, 2, 4, 0, 1, 6, 3], 'cur_cost': 732.0, 'intermediate_solutions': [{'tour': [3, 5, 4, 6, 8, 2, 0, 1, 7], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 8, 7, 4, 5, 3, 1, 6], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 7, 3, 8, 2, 0, 1, 6], 'cur_cost': 833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,163 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 732.00)
2025-08-05 10:28:28,164 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 799.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,165 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 3, 0, 1, 6, 5, 4, 2, 8], 'cur_cost': 799.0, 'intermediate_solutions': [{'tour': [6, 4, 5, 1, 7, 8, 0, 3, 2], 'cur_cost': 1267.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 0, 1, 7, 2, 3, 5, 8], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 4, 7, 8, 5, 3, 2], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,165 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 799.00)
2025-08-05 10:28:28,165 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:28,165 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:28,165 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:28,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1133.0
2025-08-05 10:28:28,585 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:28,585 - ExploitationExpert - INFO - res_population_costs: [680.0, 796.0, 680.0]
2025-08-05 10:28:28,585 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-05 10:28:28,586 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:28,587 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0}, {'tour': [7, 5, 8, 2, 4, 0, 1, 6, 3], 'cur_cost': 732.0}, {'tour': [7, 3, 0, 1, 6, 5, 4, 2, 8], 'cur_cost': 799.0}, {'tour': array([8, 7, 3, 1, 4, 0, 5, 2, 6], dtype=int64), 'cur_cost': 1133.0}, {'tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0}, {'tour': [3, 5, 8, 7, 0, 4, 2, 1, 6], 'cur_cost': 828.0}, {'tour': [5, 4, 7, 3, 1, 2, 8, 6, 0], 'cur_cost': 1064.0}, {'tour': [6, 8, 1, 7, 5, 3, 0, 2, 4], 'cur_cost': 1029.0}, {'tour': [8, 1, 7, 6, 3, 0, 4, 2, 5], 'cur_cost': 1018.0}, {'tour': [1, 0, 6, 7, 5, 3, 8, 4, 2], 'cur_cost': 720.0}]
2025-08-05 10:28:28,587 - ExploitationExpert - INFO - 局部搜索耗时: 0.42秒，最大迭代次数: 10
2025-08-05 10:28:28,587 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-05 10:28:28,588 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([8, 7, 3, 1, 4, 0, 5, 2, 6], dtype=int64), 'cur_cost': 1133.0, 'intermediate_solutions': [{'tour': array([5, 2, 8, 4, 0, 7, 6, 3, 1]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 2, 8, 0, 7, 6, 3, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 5, 2, 8, 7, 6, 3, 1]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 5, 2, 0, 7, 6, 3, 1]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 4, 5, 2, 7, 6, 3, 1]), 'cur_cost': 1189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:28,588 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1133.00)
2025-08-05 10:28:28,588 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:28,588 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:28,588 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,589 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 941.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,590 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 3, 6, 0, 7, 5, 4, 2, 1], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 0, 1, 5, 2, 4, 6], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,590 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 941.00)
2025-08-05 10:28:28,590 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:28,590 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:28,590 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,591 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 832.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,592 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 0, 6, 1, 4, 2, 8, 5, 7], 'cur_cost': 832.0, 'intermediate_solutions': [{'tour': [3, 5, 8, 4, 0, 7, 2, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 7, 0, 4, 1, 2, 6], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 0, 8, 7, 4, 2, 1, 6], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,592 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 832.00)
2025-08-05 10:28:28,592 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:28,592 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:28,592 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:28,592 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 817.0
2025-08-05 10:28:28,598 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:28,598 - ExploitationExpert - INFO - res_population_costs: [680.0, 796.0, 680.0, 680]
2025-08-05 10:28:28,598 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-05 10:28:28,599 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:28,600 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0}, {'tour': [7, 5, 8, 2, 4, 0, 1, 6, 3], 'cur_cost': 732.0}, {'tour': [7, 3, 0, 1, 6, 5, 4, 2, 8], 'cur_cost': 799.0}, {'tour': array([8, 7, 3, 1, 4, 0, 5, 2, 6], dtype=int64), 'cur_cost': 1133.0}, {'tour': [8, 3, 6, 0, 7, 5, 4, 2, 1], 'cur_cost': 941.0}, {'tour': [3, 0, 6, 1, 4, 2, 8, 5, 7], 'cur_cost': 832.0}, {'tour': array([3, 5, 1, 0, 6, 8, 2, 4, 7], dtype=int64), 'cur_cost': 817.0}, {'tour': [6, 8, 1, 7, 5, 3, 0, 2, 4], 'cur_cost': 1029.0}, {'tour': [8, 1, 7, 6, 3, 0, 4, 2, 5], 'cur_cost': 1018.0}, {'tour': [1, 0, 6, 7, 5, 3, 8, 4, 2], 'cur_cost': 720.0}]
2025-08-05 10:28:28,600 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:28,600 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-05 10:28:28,601 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 5, 1, 0, 6, 8, 2, 4, 7], dtype=int64), 'cur_cost': 817.0, 'intermediate_solutions': [{'tour': array([7, 4, 5, 3, 1, 2, 8, 6, 0]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 7, 4, 5, 1, 2, 8, 6, 0]), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 7, 4, 5, 2, 8, 6, 0]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 7, 4, 1, 2, 8, 6, 0]), 'cur_cost': 926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 7, 4, 2, 8, 6, 0]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:28,601 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 817.00)
2025-08-05 10:28:28,601 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:28,601 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:28,601 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1058.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,602 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 1, 8, 6, 7, 2, 3, 5, 0], 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': [6, 8, 1, 7, 5, 3, 4, 2, 0], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 1, 5, 7, 3, 0, 2, 4], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 1, 7, 5, 3, 0, 4], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,603 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1058.00)
2025-08-05 10:28:28,603 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:28,603 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:28,603 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,603 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1252.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,604 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 8, 0, 7, 3, 2, 6, 4, 5], 'cur_cost': 1252.0, 'intermediate_solutions': [{'tour': [8, 1, 7, 6, 0, 3, 4, 2, 5], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 1, 7, 2, 4, 0, 3, 6, 5], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 1, 7, 6, 3, 0, 4, 5], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,604 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1252.00)
2025-08-05 10:28:28,604 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:28,604 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:28,604 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,605 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1176.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,605 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 5, 1, 3, 4, 6, 7, 0, 8], 'cur_cost': 1176.0, 'intermediate_solutions': [{'tour': [5, 0, 6, 7, 1, 3, 8, 4, 2], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 7, 5, 4, 8, 3, 2], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 7, 3, 5, 8, 4, 2], 'cur_cost': 730.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,606 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1176.00)
2025-08-05 10:28:28,606 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:28,606 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:28,607 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0, 'intermediate_solutions': [{'tour': [1, 7, 4, 2, 5, 6, 8, 0, 3], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 4, 0, 5, 3, 2, 8, 6], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 5, 6, 1, 8, 2, 3], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 8, 2, 4, 0, 1, 6, 3], 'cur_cost': 732.0, 'intermediate_solutions': [{'tour': [3, 5, 4, 6, 8, 2, 0, 1, 7], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 8, 7, 4, 5, 3, 1, 6], 'cur_cost': 992.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 7, 3, 8, 2, 0, 1, 6], 'cur_cost': 833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 0, 1, 6, 5, 4, 2, 8], 'cur_cost': 799.0, 'intermediate_solutions': [{'tour': [6, 4, 5, 1, 7, 8, 0, 3, 2], 'cur_cost': 1267.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 0, 1, 7, 2, 3, 5, 8], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 4, 7, 8, 5, 3, 2], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 7, 3, 1, 4, 0, 5, 2, 6], dtype=int64), 'cur_cost': 1133.0, 'intermediate_solutions': [{'tour': array([5, 2, 8, 4, 0, 7, 6, 3, 1]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 2, 8, 0, 7, 6, 3, 1]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 5, 2, 8, 7, 6, 3, 1]), 'cur_cost': 975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 4, 5, 2, 0, 7, 6, 3, 1]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 4, 5, 2, 7, 6, 3, 1]), 'cur_cost': 1189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 6, 0, 7, 5, 4, 2, 1], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [8, 3, 7, 0, 1, 5, 2, 4, 6], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 6, 5], 'cur_cost': 785.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 7, 0, 1, 4, 2, 5, 6], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 0, 6, 1, 4, 2, 8, 5, 7], 'cur_cost': 832.0, 'intermediate_solutions': [{'tour': [3, 5, 8, 4, 0, 7, 2, 1, 6], 'cur_cost': 912.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 8, 7, 0, 4, 1, 2, 6], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 0, 8, 7, 4, 2, 1, 6], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 5, 1, 0, 6, 8, 2, 4, 7], dtype=int64), 'cur_cost': 817.0, 'intermediate_solutions': [{'tour': array([7, 4, 5, 3, 1, 2, 8, 6, 0]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 7, 4, 5, 1, 2, 8, 6, 0]), 'cur_cost': 1061.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 7, 4, 5, 2, 8, 6, 0]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 7, 4, 1, 2, 8, 6, 0]), 'cur_cost': 926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 7, 4, 2, 8, 6, 0]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 1, 8, 6, 7, 2, 3, 5, 0], 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': [6, 8, 1, 7, 5, 3, 4, 2, 0], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 1, 5, 7, 3, 0, 2, 4], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 1, 7, 5, 3, 0, 4], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 7, 3, 2, 6, 4, 5], 'cur_cost': 1252.0, 'intermediate_solutions': [{'tour': [8, 1, 7, 6, 0, 3, 4, 2, 5], 'cur_cost': 1051.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 1, 7, 2, 4, 0, 3, 6, 5], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 1, 7, 6, 3, 0, 4, 5], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 1, 3, 4, 6, 7, 0, 8], 'cur_cost': 1176.0, 'intermediate_solutions': [{'tour': [5, 0, 6, 7, 1, 3, 8, 4, 2], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 6, 7, 5, 4, 8, 3, 2], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 7, 3, 5, 8, 4, 2], 'cur_cost': 730.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:28,607 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:28,607 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:28,607 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=732.000, 多样性=0.884
2025-08-05 10:28:28,608 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:28,608 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:28,608 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:28,608 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05686313294447048, 'best_improvement': -0.016666666666666666}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016483516483516508}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:28,609 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:28,609 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-05 10:28:28,609 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:28,609 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:28,609 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=732.000, 多样性=0.884
2025-08-05 10:28:28,610 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:28,610 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.884
2025-08-05 10:28:28,610 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:28,611 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.630
2025-08-05 10:28:28,613 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:28,613 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:28,613 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:28,614 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:28,628 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -31.000, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.568
2025-08-05 10:28:28,629 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:28,629 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:28,629 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 10:28:28,633 - visualization.landscape_visualizer - INFO - 插值约束: 149 个点被约束到最小值 680.00
2025-08-05 10:28:28,635 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=10.0%, 梯度: 22.62 → 20.36
2025-08-05 10:28:28,735 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250805_102828.html
2025-08-05 10:28:28,834 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250805_102828.html
2025-08-05 10:28:28,835 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-05 10:28:28,835 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:28,835 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2225秒
2025-08-05 10:28:28,836 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -31.000000000000007, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 34707.13142857143, 'cluster_count': 0}, 'population_state': {'diversity': 0.5682888540031398, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0034, 'fitness_entropy': 0.8982265179691364, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -31.000)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360908.629582, 'performance_metrics': {}}}
2025-08-05 10:28:28,836 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:28,836 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:28,836 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:28,837 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:28,837 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:28,837 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:28,837 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:28,838 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:28,838 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:28,838 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:28,838 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:28,838 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:28,839 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:28,839 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:28,839 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:28,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,841 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,842 - ExplorationExpert - INFO - 探索路径生成完成，成本: 910.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,842 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 4, 7, 8, 5, 3, 6, 0, 2], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 2, 6, 5, 3, 7, 8], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 2, 1, 3, 7, 0], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,842 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 910.00)
2025-08-05 10:28:28,842 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:28,842 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:28,842 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 852.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,843 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 5, 3, 7, 6, 4, 2, 0, 1], 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 2, 4, 1, 0, 6, 3], 'cur_cost': 732.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 0, 4, 2, 1, 6, 3], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 2, 4, 0, 1, 6, 3, 5], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,844 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 852.00)
2025-08-05 10:28:28,844 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:28,844 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:28,844 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,844 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,845 - ExplorationExpert - INFO - 探索路径生成完成，成本: 878.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,845 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 7, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 878.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 1, 6, 5, 4, 2, 0], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 0, 1, 6, 5, 8, 2, 4], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 1, 6, 5, 4, 8, 2], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,845 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 878.00)
2025-08-05 10:28:28,845 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:28,845 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:28,845 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:28,846 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1024.0
2025-08-05 10:28:28,851 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:28,851 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:28,851 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:28,853 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:28,854 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 8, 5, 3, 6, 0, 2], 'cur_cost': 910.0}, {'tour': [8, 5, 3, 7, 6, 4, 2, 0, 1], 'cur_cost': 852.0}, {'tour': [4, 7, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 878.0}, {'tour': array([6, 8, 5, 7, 1, 0, 2, 3, 4], dtype=int64), 'cur_cost': 1024.0}, {'tour': [8, 3, 6, 0, 7, 5, 4, 2, 1], 'cur_cost': 941.0}, {'tour': [3, 0, 6, 1, 4, 2, 8, 5, 7], 'cur_cost': 832.0}, {'tour': [3, 5, 1, 0, 6, 8, 2, 4, 7], 'cur_cost': 817.0}, {'tour': [4, 1, 8, 6, 7, 2, 3, 5, 0], 'cur_cost': 1058.0}, {'tour': [1, 8, 0, 7, 3, 2, 6, 4, 5], 'cur_cost': 1252.0}, {'tour': [2, 5, 1, 3, 4, 6, 7, 0, 8], 'cur_cost': 1176.0}]
2025-08-05 10:28:28,854 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:28,854 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-05 10:28:28,855 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 8, 5, 7, 1, 0, 2, 3, 4], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([3, 7, 8, 1, 4, 0, 5, 2, 6]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 7, 8, 4, 0, 5, 2, 6]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 3, 7, 8, 0, 5, 2, 6]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 3, 7, 4, 0, 5, 2, 6]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 1, 3, 7, 0, 5, 2, 6]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:28,855 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1024.00)
2025-08-05 10:28:28,855 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:28,855 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:28,856 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,856 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:28,856 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,857 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,857 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,857 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,857 - ExplorationExpert - INFO - 探索路径生成完成，成本: 930.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,858 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 3, 8, 4, 2, 5, 0, 6, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [8, 3, 6, 0, 2, 5, 4, 7, 1], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 6, 0, 7, 5, 1, 2, 4], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 0, 7, 5, 2, 4, 1], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,858 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 930.00)
2025-08-05 10:28:28,858 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:28,858 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:28,858 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 889.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,859 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 8, 4, 0, 7, 5, 6, 1, 2], 'cur_cost': 889.0, 'intermediate_solutions': [{'tour': [3, 0, 2, 1, 4, 6, 8, 5, 7], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 0, 1, 4, 2, 8, 5, 7], 'cur_cost': 732.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 6, 1, 4, 2, 8, 7, 5], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,860 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 889.00)
2025-08-05 10:28:28,860 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:28,860 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:28,860 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,861 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:28,861 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,861 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,861 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,862 - ExplorationExpert - INFO - 探索路径生成完成，成本: 842.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,862 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 0, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [3, 0, 1, 5, 6, 8, 2, 4, 7], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 1, 0, 8, 6, 2, 4, 7], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 1, 6, 8, 0, 2, 4, 7], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,862 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 842.00)
2025-08-05 10:28:28,862 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:28,862 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:28,863 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:28,863 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:28,863 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,863 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:28,864 - ExplorationExpert - INFO - 探索路径生成完成，成本: 980.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:28,864 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 7, 0, 3, 8, 4, 5, 6, 1], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [4, 1, 8, 0, 7, 2, 3, 5, 6], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 8, 7, 6, 2, 3, 5, 0], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 1, 8, 6, 7, 3, 5, 0], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:28,864 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 980.00)
2025-08-05 10:28:28,864 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:28,864 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:28,864 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:28,865 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1013.0
2025-08-05 10:28:28,871 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:28,872 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:28,872 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:28,873 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:28,873 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 8, 5, 3, 6, 0, 2], 'cur_cost': 910.0}, {'tour': [8, 5, 3, 7, 6, 4, 2, 0, 1], 'cur_cost': 852.0}, {'tour': [4, 7, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 878.0}, {'tour': array([6, 8, 5, 7, 1, 0, 2, 3, 4], dtype=int64), 'cur_cost': 1024.0}, {'tour': [7, 3, 8, 4, 2, 5, 0, 6, 1], 'cur_cost': 930.0}, {'tour': [3, 8, 4, 0, 7, 5, 6, 1, 2], 'cur_cost': 889.0}, {'tour': [1, 0, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 842.0}, {'tour': [2, 7, 0, 3, 8, 4, 5, 6, 1], 'cur_cost': 980.0}, {'tour': array([3, 8, 1, 2, 0, 5, 6, 7, 4], dtype=int64), 'cur_cost': 1013.0}, {'tour': [2, 5, 1, 3, 4, 6, 7, 0, 8], 'cur_cost': 1176.0}]
2025-08-05 10:28:28,874 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:28,874 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-05 10:28:28,875 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([3, 8, 1, 2, 0, 5, 6, 7, 4], dtype=int64), 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': array([0, 8, 1, 7, 3, 2, 6, 4, 5]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 8, 1, 3, 2, 6, 4, 5]), 'cur_cost': 1262.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 7, 0, 8, 1, 2, 6, 4, 5]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 7, 0, 8, 3, 2, 6, 4, 5]), 'cur_cost': 1219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 7, 0, 8, 2, 6, 4, 5]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:28,875 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1013.00)
2025-08-05 10:28:28,875 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:28,875 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:28,876 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:28,876 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1121.0
2025-08-05 10:28:28,883 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:28,883 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:28,883 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:28,884 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:28,884 - ExploitationExpert - INFO - populations: [{'tour': [1, 4, 7, 8, 5, 3, 6, 0, 2], 'cur_cost': 910.0}, {'tour': [8, 5, 3, 7, 6, 4, 2, 0, 1], 'cur_cost': 852.0}, {'tour': [4, 7, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 878.0}, {'tour': array([6, 8, 5, 7, 1, 0, 2, 3, 4], dtype=int64), 'cur_cost': 1024.0}, {'tour': [7, 3, 8, 4, 2, 5, 0, 6, 1], 'cur_cost': 930.0}, {'tour': [3, 8, 4, 0, 7, 5, 6, 1, 2], 'cur_cost': 889.0}, {'tour': [1, 0, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 842.0}, {'tour': [2, 7, 0, 3, 8, 4, 5, 6, 1], 'cur_cost': 980.0}, {'tour': array([3, 8, 1, 2, 0, 5, 6, 7, 4], dtype=int64), 'cur_cost': 1013.0}, {'tour': array([2, 3, 4, 6, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1121.0}]
2025-08-05 10:28:28,885 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:28,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-05 10:28:28,885 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 3, 4, 6, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([1, 5, 2, 3, 4, 6, 7, 0, 8]), 'cur_cost': 1236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 5, 2, 4, 6, 7, 0, 8]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 1, 5, 2, 6, 7, 0, 8]), 'cur_cost': 1211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 1, 5, 4, 6, 7, 0, 8]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 3, 1, 5, 6, 7, 0, 8]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:28,885 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1121.00)
2025-08-05 10:28:28,885 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:28,885 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:28,886 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 8, 5, 3, 6, 0, 2], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [4, 0, 1, 2, 6, 5, 3, 7, 8], 'cur_cost': 828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 2, 1, 3, 7, 0], 'cur_cost': 985.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 1, 2, 6, 5, 3, 7, 0], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 3, 7, 6, 4, 2, 0, 1], 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': [7, 5, 8, 2, 4, 1, 0, 6, 3], 'cur_cost': 732.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 8, 0, 4, 2, 1, 6, 3], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 2, 4, 0, 1, 6, 3, 5], 'cur_cost': 721.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 878.0, 'intermediate_solutions': [{'tour': [7, 3, 8, 1, 6, 5, 4, 2, 0], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 0, 1, 6, 5, 8, 2, 4], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 0, 1, 6, 5, 4, 8, 2], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 5, 7, 1, 0, 2, 3, 4], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([3, 7, 8, 1, 4, 0, 5, 2, 6]), 'cur_cost': 1104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 7, 8, 4, 0, 5, 2, 6]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 1, 3, 7, 8, 0, 5, 2, 6]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 3, 7, 4, 0, 5, 2, 6]), 'cur_cost': 1210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 1, 3, 7, 0, 5, 2, 6]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 8, 4, 2, 5, 0, 6, 1], 'cur_cost': 930.0, 'intermediate_solutions': [{'tour': [8, 3, 6, 0, 2, 5, 4, 7, 1], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 6, 0, 7, 5, 1, 2, 4], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 6, 0, 7, 5, 2, 4, 1], 'cur_cost': 941.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 4, 0, 7, 5, 6, 1, 2], 'cur_cost': 889.0, 'intermediate_solutions': [{'tour': [3, 0, 2, 1, 4, 6, 8, 5, 7], 'cur_cost': 1015.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 0, 1, 4, 2, 8, 5, 7], 'cur_cost': 732.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 0, 6, 1, 4, 2, 8, 7, 5], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [3, 0, 1, 5, 6, 8, 2, 4, 7], 'cur_cost': 852.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 1, 0, 8, 6, 2, 4, 7], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 1, 6, 8, 0, 2, 4, 7], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 3, 8, 4, 5, 6, 1], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [4, 1, 8, 0, 7, 2, 3, 5, 6], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 8, 7, 6, 2, 3, 5, 0], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 1, 8, 6, 7, 3, 5, 0], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 1, 2, 0, 5, 6, 7, 4], dtype=int64), 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': array([0, 8, 1, 7, 3, 2, 6, 4, 5]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 8, 1, 3, 2, 6, 4, 5]), 'cur_cost': 1262.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 7, 0, 8, 1, 2, 6, 4, 5]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 7, 0, 8, 3, 2, 6, 4, 5]), 'cur_cost': 1219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 7, 0, 8, 2, 6, 4, 5]), 'cur_cost': 1192.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 4, 6, 0, 5, 1, 7, 8], dtype=int64), 'cur_cost': 1121.0, 'intermediate_solutions': [{'tour': array([1, 5, 2, 3, 4, 6, 7, 0, 8]), 'cur_cost': 1236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 5, 2, 4, 6, 7, 0, 8]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 3, 1, 5, 2, 6, 7, 0, 8]), 'cur_cost': 1211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 1, 5, 4, 6, 7, 0, 8]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 3, 1, 5, 6, 7, 0, 8]), 'cur_cost': 979.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:28,887 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:28,887 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:28,888 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=842.000, 多样性=0.872
2025-08-05 10:28:28,888 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:28,888 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:28,888 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:28,888 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.006277916292107011, 'best_improvement': -0.15027322404371585}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.013966480446927313}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:28,888 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:28,888 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-05 10:28:28,889 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:28,889 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:28,889 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=842.000, 多样性=0.872
2025-08-05 10:28:28,889 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:28,890 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.872
2025-08-05 10:28:28,890 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:28,891 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.630
2025-08-05 10:28:28,892 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:28,892 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:28,893 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:28,893 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:28,901 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -3.814, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.557
2025-08-05 10:28:28,901 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:28,901 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:28,901 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 10:28:28,904 - visualization.landscape_visualizer - INFO - 插值约束: 151 个点被约束到最小值 680.00
2025-08-05 10:28:28,906 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.9%, 梯度: 14.98 → 14.24
2025-08-05 10:28:29,044 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250805_102828.html
2025-08-05 10:28:29,105 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250805_102828.html
2025-08-05 10:28:29,106 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-05 10:28:29,106 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:29,106 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2138秒
2025-08-05 10:28:29,107 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3.8142857142857136, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 12989.242653061223, 'cluster_count': 0}, 'population_state': {'diversity': 0.5565149136577708, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0044, 'fitness_entropy': 0.9654305096819759, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3.814)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360908.901192, 'performance_metrics': {}}}
2025-08-05 10:28:29,107 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:29,107 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:29,107 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:29,107 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:29,108 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:29,108 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:29,108 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:29,108 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,108 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:29,108 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:29,109 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,109 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:29,109 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:29,109 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:29,109 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:29,109 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,110 - ExplorationExpert - INFO - 探索路径生成完成，成本: 992.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,111 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 5, 7, 0, 6, 3, 1, 2, 8], 'cur_cost': 992.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 8, 5, 7, 6, 0, 2], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 5, 8, 7, 4, 6, 0, 2], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 4, 7, 8, 3, 6, 0, 2], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,111 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 992.00)
2025-08-05 10:28:29,111 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:29,111 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:29,111 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,111 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:29,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1022.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,112 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 0, 7, 4, 5, 3, 1, 2, 8], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [8, 5, 2, 7, 6, 4, 3, 0, 1], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 2, 4, 6, 7, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 7, 6, 4, 0, 1, 2], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,112 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1022.00)
2025-08-05 10:28:29,112 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:29,112 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,113 - ExplorationExpert - INFO - 探索路径生成完成，成本: 855.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,113 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 0, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 855.0, 'intermediate_solutions': [{'tour': [4, 7, 8, 6, 1, 3, 2, 0, 5], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 8, 0, 2, 3, 5, 6, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,114 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 855.00)
2025-08-05 10:28:29,114 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:29,114 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,114 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,114 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1066.0
2025-08-05 10:28:29,120 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,120 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,120 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,121 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,121 - ExploitationExpert - INFO - populations: [{'tour': [4, 5, 7, 0, 6, 3, 1, 2, 8], 'cur_cost': 992.0}, {'tour': [6, 0, 7, 4, 5, 3, 1, 2, 8], 'cur_cost': 1022.0}, {'tour': [7, 0, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 855.0}, {'tour': array([7, 0, 5, 8, 6, 2, 1, 4, 3], dtype=int64), 'cur_cost': 1066.0}, {'tour': [7, 3, 8, 4, 2, 5, 0, 6, 1], 'cur_cost': 930.0}, {'tour': [3, 8, 4, 0, 7, 5, 6, 1, 2], 'cur_cost': 889.0}, {'tour': [1, 0, 4, 8, 3, 5, 7, 6, 2], 'cur_cost': 842.0}, {'tour': [2, 7, 0, 3, 8, 4, 5, 6, 1], 'cur_cost': 980.0}, {'tour': [3, 8, 1, 2, 0, 5, 6, 7, 4], 'cur_cost': 1013.0}, {'tour': [2, 3, 4, 6, 0, 5, 1, 7, 8], 'cur_cost': 1121.0}]
2025-08-05 10:28:29,121 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,121 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-05 10:28:29,122 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 0, 5, 8, 6, 2, 1, 4, 3], dtype=int64), 'cur_cost': 1066.0, 'intermediate_solutions': [{'tour': array([5, 8, 6, 7, 1, 0, 2, 3, 4]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 5, 8, 6, 1, 0, 2, 3, 4]), 'cur_cost': 963.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 5, 8, 6, 0, 2, 3, 4]), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 7, 5, 8, 1, 0, 2, 3, 4]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 1, 7, 5, 8, 0, 2, 3, 4]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,122 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1066.00)
2025-08-05 10:28:29,122 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:29,122 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:29,122 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,122 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:29,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,123 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1013.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,123 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 2, 5, 0, 6, 1], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 4, 2, 5, 0, 1, 6], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 8, 4, 2, 6, 5, 0, 1], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,124 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1013.00)
2025-08-05 10:28:29,124 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:29,124 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:29,124 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,124 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1000.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,125 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 8, 3, 4, 7, 0, 1, 6, 2], 'cur_cost': 1000.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 0, 7, 6, 5, 1, 2], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 4, 0, 7, 5, 6, 2, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 4, 7, 0, 5, 6, 1, 2], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,125 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1000.00)
2025-08-05 10:28:29,125 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:29,125 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,126 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1063.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,127 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 7, 0, 3, 1, 4, 8, 5, 6], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 3, 5, 6, 7, 2], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,127 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1063.00)
2025-08-05 10:28:29,127 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:29,127 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:29,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,127 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,127 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,128 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 779.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,128 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0, 'intermediate_solutions': [{'tour': [2, 0, 7, 3, 8, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 4, 8, 3, 0, 7, 1], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 0, 3, 8, 5, 4, 6, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,128 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 779.00)
2025-08-05 10:28:29,128 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:29,128 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,128 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,129 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1068.0
2025-08-05 10:28:29,135 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,136 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,136 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,136 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,137 - ExploitationExpert - INFO - populations: [{'tour': [4, 5, 7, 0, 6, 3, 1, 2, 8], 'cur_cost': 992.0}, {'tour': [6, 0, 7, 4, 5, 3, 1, 2, 8], 'cur_cost': 1022.0}, {'tour': [7, 0, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 855.0}, {'tour': array([7, 0, 5, 8, 6, 2, 1, 4, 3], dtype=int64), 'cur_cost': 1066.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': [5, 8, 3, 4, 7, 0, 1, 6, 2], 'cur_cost': 1000.0}, {'tour': [2, 7, 0, 3, 1, 4, 8, 5, 6], 'cur_cost': 1063.0}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0}, {'tour': array([2, 5, 3, 4, 7, 0, 8, 6, 1], dtype=int64), 'cur_cost': 1068.0}, {'tour': [2, 3, 4, 6, 0, 5, 1, 7, 8], 'cur_cost': 1121.0}]
2025-08-05 10:28:29,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-05 10:28:29,138 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([2, 5, 3, 4, 7, 0, 8, 6, 1], dtype=int64), 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 2, 0, 5, 6, 7, 4]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 3, 0, 5, 6, 7, 4]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 1, 8, 3, 5, 6, 7, 4]), 'cur_cost': 911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 8, 0, 5, 6, 7, 4]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 2, 1, 8, 5, 6, 7, 4]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,138 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1068.00)
2025-08-05 10:28:29,138 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:29,138 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,139 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,139 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1026.0
2025-08-05 10:28:29,146 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,146 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,146 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,147 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,147 - ExploitationExpert - INFO - populations: [{'tour': [4, 5, 7, 0, 6, 3, 1, 2, 8], 'cur_cost': 992.0}, {'tour': [6, 0, 7, 4, 5, 3, 1, 2, 8], 'cur_cost': 1022.0}, {'tour': [7, 0, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 855.0}, {'tour': array([7, 0, 5, 8, 6, 2, 1, 4, 3], dtype=int64), 'cur_cost': 1066.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': [5, 8, 3, 4, 7, 0, 1, 6, 2], 'cur_cost': 1000.0}, {'tour': [2, 7, 0, 3, 1, 4, 8, 5, 6], 'cur_cost': 1063.0}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0}, {'tour': array([2, 5, 3, 4, 7, 0, 8, 6, 1], dtype=int64), 'cur_cost': 1068.0}, {'tour': array([1, 5, 6, 0, 2, 8, 7, 4, 3], dtype=int64), 'cur_cost': 1026.0}]
2025-08-05 10:28:29,148 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,148 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-05 10:28:29,149 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([1, 5, 6, 0, 2, 8, 7, 4, 3], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([4, 3, 2, 6, 0, 5, 1, 7, 8]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 3, 2, 0, 5, 1, 7, 8]), 'cur_cost': 1191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 3, 2, 5, 1, 7, 8]), 'cur_cost': 1203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 3, 0, 5, 1, 7, 8]), 'cur_cost': 1188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 6, 4, 3, 5, 1, 7, 8]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,149 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1026.00)
2025-08-05 10:28:29,149 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:29,149 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:29,151 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 7, 0, 6, 3, 1, 2, 8], 'cur_cost': 992.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 8, 5, 7, 6, 0, 2], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 5, 8, 7, 4, 6, 0, 2], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 4, 7, 8, 3, 6, 0, 2], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 7, 4, 5, 3, 1, 2, 8], 'cur_cost': 1022.0, 'intermediate_solutions': [{'tour': [8, 5, 2, 7, 6, 4, 3, 0, 1], 'cur_cost': 1096.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 3, 2, 4, 6, 7, 0, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 7, 6, 4, 0, 1, 2], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 855.0, 'intermediate_solutions': [{'tour': [4, 7, 8, 6, 1, 3, 2, 0, 5], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 8, 0, 2, 3, 5, 6, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 8, 6, 5, 3, 2, 0, 1], 'cur_cost': 905.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 0, 5, 8, 6, 2, 1, 4, 3], dtype=int64), 'cur_cost': 1066.0, 'intermediate_solutions': [{'tour': array([5, 8, 6, 7, 1, 0, 2, 3, 4]), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 5, 8, 6, 1, 0, 2, 3, 4]), 'cur_cost': 963.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 5, 8, 6, 0, 2, 3, 4]), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 7, 5, 8, 1, 0, 2, 3, 4]), 'cur_cost': 1016.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 1, 7, 5, 8, 0, 2, 3, 4]), 'cur_cost': 1097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [7, 3, 4, 8, 2, 5, 0, 6, 1], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 8, 4, 2, 5, 0, 1, 6], 'cur_cost': 832.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 8, 4, 2, 6, 5, 0, 1], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 4, 7, 0, 1, 6, 2], 'cur_cost': 1000.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 0, 7, 6, 5, 1, 2], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 4, 0, 7, 5, 6, 2, 1], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 4, 7, 0, 5, 6, 1, 2], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 0, 3, 1, 4, 8, 5, 6], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 3, 5, 6, 7, 2], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 8, 3, 5, 7, 6, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0, 'intermediate_solutions': [{'tour': [2, 0, 7, 3, 8, 4, 5, 6, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 4, 8, 3, 0, 7, 1], 'cur_cost': 1045.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 0, 3, 8, 5, 4, 6, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 5, 3, 4, 7, 0, 8, 6, 1], dtype=int64), 'cur_cost': 1068.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 2, 0, 5, 6, 7, 4]), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 8, 3, 0, 5, 6, 7, 4]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 1, 8, 3, 5, 6, 7, 4]), 'cur_cost': 911.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 8, 0, 5, 6, 7, 4]), 'cur_cost': 1094.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 0, 2, 1, 8, 5, 6, 7, 4]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 6, 0, 2, 8, 7, 4, 3], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([4, 3, 2, 6, 0, 5, 1, 7, 8]), 'cur_cost': 1156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 3, 2, 0, 5, 1, 7, 8]), 'cur_cost': 1191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 4, 3, 2, 5, 1, 7, 8]), 'cur_cost': 1203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 3, 0, 5, 1, 7, 8]), 'cur_cost': 1188.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 6, 4, 3, 5, 1, 7, 8]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:29,151 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:29,151 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,152 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=779.000, 多样性=0.881
2025-08-05 10:28:29,152 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:29,152 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:29,152 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:29,153 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.02498541435152697, 'best_improvement': 0.07482185273159145}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011331444759206773}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.02253259593023459, 'recent_improvements': [0.03878727556836217, -0.05686313294447048, -0.006277916292107011], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:29,153 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:29,153 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-05 10:28:29,153 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:29,153 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,154 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=779.000, 多样性=0.881
2025-08-05 10:28:29,154 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:29,154 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.881
2025-08-05 10:28:29,155 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:29,155 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.630
2025-08-05 10:28:29,158 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:29,158 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:29,159 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:29,159 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:29,167 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 6.600, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.565
2025-08-05 10:28:29,167 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:29,168 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:29,168 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-05 10:28:29,171 - visualization.landscape_visualizer - INFO - 插值约束: 73 个点被约束到最小值 680.00
2025-08-05 10:28:29,172 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 18.55 → 16.94
2025-08-05 10:28:29,272 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250805_102829.html
2025-08-05 10:28:29,321 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250805_102829.html
2025-08-05 10:28:29,321 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-05 10:28:29,322 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:29,322 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1642秒
2025-08-05 10:28:29,322 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 6.599999999999995, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 21211.68571428571, 'cluster_count': 0}, 'population_state': {'diversity': 0.565149136577708, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0054, 'fitness_entropy': 0.9285249215482253, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 6.600)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360909.167029, 'performance_metrics': {}}}
2025-08-05 10:28:29,322 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:29,322 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:29,323 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:29,323 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:29,323 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:29,323 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:29,323 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:29,324 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,324 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:29,324 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:29,324 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,324 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:29,324 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:29,325 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:29,325 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:29,325 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,325 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:29,325 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,325 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,326 - ExplorationExpert - INFO - 探索路径生成完成，成本: 984.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,326 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 5, 0, 3, 8, 4, 2, 6], 'cur_cost': 984.0, 'intermediate_solutions': [{'tour': [4, 3, 7, 0, 6, 5, 1, 2, 8], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 7, 8, 2, 1, 3, 6, 0], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 7, 6, 3, 1, 2, 8], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,326 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 984.00)
2025-08-05 10:28:29,326 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:29,326 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:29,326 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,327 - ExplorationExpert - INFO - 探索路径生成完成，成本: 952.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,327 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 7, 6, 3, 8, 5, 0, 2], 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': [5, 0, 7, 4, 6, 3, 1, 2, 8], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 7, 5, 4, 3, 1, 2, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 5, 7, 4, 3, 1, 2, 8], 'cur_cost': 1054.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,328 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 952.00)
2025-08-05 10:28:29,328 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,329 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 4, 7, 6, 2, 0, 1, 3, 8], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [7, 6, 0, 3, 5, 8, 4, 2, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 8, 5, 3, 6, 0, 7], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 757.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,329 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1018.00)
2025-08-05 10:28:29,329 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:29,329 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,329 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,329 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1126.0
2025-08-05 10:28:29,334 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,335 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,335 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,336 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,336 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 5, 0, 3, 8, 4, 2, 6], 'cur_cost': 984.0}, {'tour': [1, 4, 7, 6, 3, 8, 5, 0, 2], 'cur_cost': 952.0}, {'tour': [5, 4, 7, 6, 2, 0, 1, 3, 8], 'cur_cost': 1018.0}, {'tour': array([7, 5, 0, 2, 8, 6, 4, 3, 1], dtype=int64), 'cur_cost': 1126.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': [5, 8, 3, 4, 7, 0, 1, 6, 2], 'cur_cost': 1000.0}, {'tour': [2, 7, 0, 3, 1, 4, 8, 5, 6], 'cur_cost': 1063.0}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0}, {'tour': [2, 5, 3, 4, 7, 0, 8, 6, 1], 'cur_cost': 1068.0}, {'tour': [1, 5, 6, 0, 2, 8, 7, 4, 3], 'cur_cost': 1026.0}]
2025-08-05 10:28:29,336 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,336 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-05 10:28:29,337 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([7, 5, 0, 2, 8, 6, 4, 3, 1], dtype=int64), 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': array([5, 0, 7, 8, 6, 2, 1, 4, 3]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 0, 7, 6, 2, 1, 4, 3]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 5, 0, 7, 2, 1, 4, 3]), 'cur_cost': 1047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 5, 0, 6, 2, 1, 4, 3]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 8, 5, 0, 2, 1, 4, 3]), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,337 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1126.00)
2025-08-05 10:28:29,337 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:29,337 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:29,337 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,337 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 906.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,338 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 5, 1, 7, 6, 4, 2], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 1, 8, 0, 7, 6, 4, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 1, 5, 3, 6, 4, 2], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,338 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 906.00)
2025-08-05 10:28:29,338 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:29,338 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:29,339 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,339 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 849.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,340 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [8, 7, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [5, 8, 3, 4, 7, 0, 1, 2, 6], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 4, 7, 0, 1, 2, 6], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 3, 7, 0, 1, 6, 2], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,342 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 849.00)
2025-08-05 10:28:29,343 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:29,343 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,343 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,346 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 988.0
2025-08-05 10:28:29,353 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,353 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,354 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,355 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,355 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 5, 0, 3, 8, 4, 2, 6], 'cur_cost': 984.0}, {'tour': [1, 4, 7, 6, 3, 8, 5, 0, 2], 'cur_cost': 952.0}, {'tour': [5, 4, 7, 6, 2, 0, 1, 3, 8], 'cur_cost': 1018.0}, {'tour': array([7, 5, 0, 2, 8, 6, 4, 3, 1], dtype=int64), 'cur_cost': 1126.0}, {'tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0}, {'tour': [8, 7, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 849.0}, {'tour': array([5, 0, 7, 6, 8, 3, 2, 4, 1], dtype=int64), 'cur_cost': 988.0}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0}, {'tour': [2, 5, 3, 4, 7, 0, 8, 6, 1], 'cur_cost': 1068.0}, {'tour': [1, 5, 6, 0, 2, 8, 7, 4, 3], 'cur_cost': 1026.0}]
2025-08-05 10:28:29,356 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,356 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-05 10:28:29,357 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([5, 0, 7, 6, 8, 3, 2, 4, 1], dtype=int64), 'cur_cost': 988.0, 'intermediate_solutions': [{'tour': array([0, 7, 2, 3, 1, 4, 8, 5, 6]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 7, 2, 1, 4, 8, 5, 6]), 'cur_cost': 947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 0, 7, 2, 4, 8, 5, 6]), 'cur_cost': 941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 0, 7, 1, 4, 8, 5, 6]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 3, 0, 7, 4, 8, 5, 6]), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,358 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 988.00)
2025-08-05 10:28:29,358 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:29,358 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:29,358 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,359 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-05 10:28:29,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,360 - ExplorationExpert - INFO - 探索路径生成完成，成本: 956.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,360 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 5, 3, 7, 0, 4, 8, 6, 1], 'cur_cost': 956.0, 'intermediate_solutions': [{'tour': [8, 7, 6, 5, 1, 4, 2, 3, 0], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 6, 0, 1, 2, 4, 3, 5], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,360 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 956.00)
2025-08-05 10:28:29,360 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:29,361 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,361 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,361 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1024.0
2025-08-05 10:28:29,368 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,369 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680, 796.0]
2025-08-05 10:28:29,369 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 2, 4, 7, 3, 6, 5, 8], dtype=int64)]
2025-08-05 10:28:29,370 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,370 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 5, 0, 3, 8, 4, 2, 6], 'cur_cost': 984.0}, {'tour': [1, 4, 7, 6, 3, 8, 5, 0, 2], 'cur_cost': 952.0}, {'tour': [5, 4, 7, 6, 2, 0, 1, 3, 8], 'cur_cost': 1018.0}, {'tour': array([7, 5, 0, 2, 8, 6, 4, 3, 1], dtype=int64), 'cur_cost': 1126.0}, {'tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0}, {'tour': [8, 7, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 849.0}, {'tour': array([5, 0, 7, 6, 8, 3, 2, 4, 1], dtype=int64), 'cur_cost': 988.0}, {'tour': [2, 5, 3, 7, 0, 4, 8, 6, 1], 'cur_cost': 956.0}, {'tour': array([5, 4, 2, 1, 8, 0, 3, 6, 7], dtype=int64), 'cur_cost': 1024.0}, {'tour': [1, 5, 6, 0, 2, 8, 7, 4, 3], 'cur_cost': 1026.0}]
2025-08-05 10:28:29,373 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,373 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-05 10:28:29,374 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([5, 4, 2, 1, 8, 0, 3, 6, 7], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([3, 5, 2, 4, 7, 0, 8, 6, 1]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 5, 2, 7, 0, 8, 6, 1]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 3, 5, 2, 0, 8, 6, 1]), 'cur_cost': 1098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 5, 7, 0, 8, 6, 1]), 'cur_cost': 927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 4, 3, 5, 0, 8, 6, 1]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,374 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1024.00)
2025-08-05 10:28:29,374 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:29,374 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:29,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,375 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-05 10:28:29,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,377 - ExplorationExpert - INFO - 探索路径生成完成，成本: 764.0, 路径长度: 9, 收集中间解: 3
2025-08-05 10:28:29,377 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 8, 4, 2, 0, 1, 7, 5, 6], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 0, 2, 8, 4, 7, 3], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 6, 8, 2, 0, 7, 4, 3], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 5, 6, 0, 2, 8, 7, 4], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,377 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 764.00)
2025-08-05 10:28:29,377 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:29,377 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:29,379 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 0, 3, 8, 4, 2, 6], 'cur_cost': 984.0, 'intermediate_solutions': [{'tour': [4, 3, 7, 0, 6, 5, 1, 2, 8], 'cur_cost': 931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 7, 8, 2, 1, 3, 6, 0], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 7, 6, 3, 1, 2, 8], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 6, 3, 8, 5, 0, 2], 'cur_cost': 952.0, 'intermediate_solutions': [{'tour': [5, 0, 7, 4, 6, 3, 1, 2, 8], 'cur_cost': 1057.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 7, 5, 4, 3, 1, 2, 8], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 5, 7, 4, 3, 1, 2, 8], 'cur_cost': 1054.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 7, 6, 2, 0, 1, 3, 8], 'cur_cost': 1018.0, 'intermediate_solutions': [{'tour': [7, 6, 0, 3, 5, 8, 4, 2, 1], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 8, 5, 3, 6, 0, 7], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 6, 3, 5, 8, 4, 2, 1], 'cur_cost': 757.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 5, 0, 2, 8, 6, 4, 3, 1], dtype=int64), 'cur_cost': 1126.0, 'intermediate_solutions': [{'tour': array([5, 0, 7, 8, 6, 2, 1, 4, 3]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 5, 0, 7, 6, 2, 1, 4, 3]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 8, 5, 0, 7, 2, 1, 4, 3]), 'cur_cost': 1047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 8, 5, 0, 6, 2, 1, 4, 3]), 'cur_cost': 1025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 8, 5, 0, 2, 1, 4, 3]), 'cur_cost': 1001.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 8, 2, 0, 4, 5, 6, 1], 'cur_cost': 906.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 5, 1, 7, 6, 4, 2], 'cur_cost': 980.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 1, 8, 0, 7, 6, 4, 2], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 1, 5, 3, 6, 4, 2], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 5, 6, 3, 4, 2, 0, 1], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [5, 8, 3, 4, 7, 0, 1, 2, 6], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 4, 7, 0, 1, 2, 6], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 3, 7, 0, 1, 6, 2], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 0, 7, 6, 8, 3, 2, 4, 1], dtype=int64), 'cur_cost': 988.0, 'intermediate_solutions': [{'tour': array([0, 7, 2, 3, 1, 4, 8, 5, 6]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 7, 2, 1, 4, 8, 5, 6]), 'cur_cost': 947.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 0, 7, 2, 4, 8, 5, 6]), 'cur_cost': 941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 0, 7, 1, 4, 8, 5, 6]), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 3, 0, 7, 4, 8, 5, 6]), 'cur_cost': 1058.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 7, 0, 4, 8, 6, 1], 'cur_cost': 956.0, 'intermediate_solutions': [{'tour': [8, 7, 6, 5, 1, 4, 2, 3, 0], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 6, 0, 1, 2, 4, 3, 5], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 6, 0, 1, 4, 2, 3, 5], 'cur_cost': 779.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 4, 2, 1, 8, 0, 3, 6, 7], dtype=int64), 'cur_cost': 1024.0, 'intermediate_solutions': [{'tour': array([3, 5, 2, 4, 7, 0, 8, 6, 1]), 'cur_cost': 1029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 5, 2, 7, 0, 8, 6, 1]), 'cur_cost': 1073.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 3, 5, 2, 0, 8, 6, 1]), 'cur_cost': 1098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 5, 7, 0, 8, 6, 1]), 'cur_cost': 927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 4, 3, 5, 0, 8, 6, 1]), 'cur_cost': 1069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 4, 2, 0, 1, 7, 5, 6], 'cur_cost': 764.0, 'intermediate_solutions': [{'tour': [1, 5, 6, 0, 2, 8, 4, 7, 3], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 6, 8, 2, 0, 7, 4, 3], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 5, 6, 0, 2, 8, 7, 4], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:29,379 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:29,380 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,381 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=764.000, 多样性=0.862
2025-08-05 10:28:29,382 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:29,382 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:29,382 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:29,382 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.00099320825976834, 'best_improvement': 0.019255455712451863}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02240896358543425}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04092427364799873, 'recent_improvements': [-0.05686313294447048, -0.006277916292107011, 0.02498541435152697], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:29,383 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:29,385 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-05 10:28:29,386 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250805_102829.solution
2025-08-05 10:28:29,386 - __main__ - INFO - 实例执行完成 - 运行时间: 14.20s, 最佳成本: 680.0
2025-08-05 10:28:29,386 - __main__ - INFO - 实例 simple1_9 处理完成
