2025-08-05 09:52:23,518 - __main__ - INFO - lin105 开始进化第 1 代
2025-08-05 09:52:23,518 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:23,521 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,528 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17316.000, 多样性=0.991
2025-08-05 09:52:23,535 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:23,544 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.991
2025-08-05 09:52:23,547 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:23,550 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:23,550 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:23,550 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:23,551 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:23,578 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -12493.280, 聚类评分: 0.000, 覆盖率: 0.170, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:23,578 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:23,578 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:23,578 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 09:52:23,692 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_151_20250805_095223.html
2025-08-05 09:52:23,750 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_151_20250805_095223.html
2025-08-05 09:52:23,751 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 151
2025-08-05 09:52:23,751 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:23,751 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2004秒
2025-08-05 09:52:23,751 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 302, 'max_size': 500, 'hits': 0, 'misses': 302, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 1008, 'misses': 526, 'hit_rate': 0.6571056062581486, 'evictions': 426, 'ttl': 7200}}
2025-08-05 09:52:23,751 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -12493.280000000002, 'local_optima_density': 0.2, 'gradient_variance': 4638500942.873601, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1701, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12493.280)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.170)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358743.5786443, 'performance_metrics': {}}}
2025-08-05 09:52:23,751 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:23,751 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:23,752 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:23,752 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:23,754 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:23,754 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:23,754 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:23,754 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:23,754 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:23,755 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:23,755 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:23,755 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:23,755 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:23,755 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:23,755 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:23,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,760 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:23,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21195.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,761 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21195.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,761 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 21195.00)
2025-08-05 09:52:23,761 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:23,761 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:23,761 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,779 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:23,779 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 74047.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,780 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74047.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,780 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 74047.00)
2025-08-05 09:52:23,780 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:23,780 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:23,780 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,784 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:23,784 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,785 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20952.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,785 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20952.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,785 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 20952.00)
2025-08-05 09:52:23,785 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:23,785 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:23,785 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:23,786 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 128194.0
2025-08-05 09:52:23,798 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:23,798 - ExploitationExpert - INFO - res_population_costs: [14697.0, 14683, 14658]
2025-08-05 09:52:23,799 - ExploitationExpert - INFO - res_populations: [array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:23,800 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,800 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21195.0}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74047.0}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20952.0}, {'tour': array([ 72,  19,   1,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57], dtype=int64), 'cur_cost': 128194.0}, {'tour': array([ 59,  55,  53,  91,  99,  77,  35,  97,  21,  47,  73, 103,  75,
        49,  31,  27,  93,  71,  57,  79,  63,   6,   8,  69,  13,  67,
        62,  92,  37,  17,   1,   7, 104,  25,  78,   4,  34,  85,  36,
        12,  23,  43,  22,  51,  95,  29,  16,  26,  94,  70,  65,  58,
        30,  45,  82,  20,  14,  54,  28,   9,  84,   5,  11, 100,  83,
        74,  89,  90,  86,  64,  60,  40, 101,  32,  61,  81,  50,  98,
        72,  80,  56,  44,  15,   0,  18,  24,  68,  46,  96,  87,  42,
         3,  33,  41,  76,  48,   2, 102,  38,  10,  39,  66,  52,  19,
        88], dtype=int64), 'cur_cost': 114309.0}, {'tour': array([ 49,  76,  32,  18,  93,  27,  28,  25, 102,  14,  23,   2,  51,
        89,  10,   7,  22,  19,  43,  17,  64,  60,  82,  97,  42,  45,
        21,  31,  26,  11,  36,  20,  46,  90,  35,   6,  73, 101,  80,
        13,  48,  74,  88,  79,  30,  65, 100,  33,  58,   5,  16,  54,
        34,  67,  61,   0,  44,  87,   3,  40,  63,  39,  99,  77,  75,
         8,  98,  50,  55,  92,  91,  86,  96,  41,   1,  24,  37,  47,
         9,  15,  68,  95,   4,  69, 104,  66,  85,  83, 103,  72,  57,
        52,  84,  81,  53,  78,  62,  56,  12,  59,  29,  70,  38,  71,
        94], dtype=int64), 'cur_cost': 113652.0}, {'tour': array([ 14,  53,  85,  42,  96,  58,  19,   8,  33,   6,  24,  39,  76,
        13,  12,  95,  90,  86,  22,  50,  29,  92,  28,  21,  30,  79,
        68,   5,  56,   4,  34,  11,  47,  74, 101,  51,  37,  62,  27,
        80,  63,  45, 103,  61,  82,  15,  81,  91,  70,   1,  20,  55,
        17,  44,  73,   2,  48,  75,  65,  66,  57,  36,  16,  32,  46,
        93,  78,  84,  69,  97,   0,  77,  49,  43,  23,  99,  38, 104,
        40,  41,  88,   7,  54,  94,   9,  71,  18,  35,  25,  89,  98,
         3,  10,  83,  67,  87,  59, 100,  26,  64,  31, 102,  60,  72,
        52], dtype=int64), 'cur_cost': 120523.0}, {'tour': array([ 74,  77,  26,  13, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132100.0}, {'tour': array([ 78,   3,  51,  83,  86,  64,  31,  37,  81,  38,  74,  62,  60,
        55,  82,  50, 101,  27,   7,  12,  32,  10,  89,  11,  72,  41,
        18,  34, 103,  43,  33,  42,  91,  63,  68,   6, 104,  58,  44,
        21,  94,  39,  67,   1,  57,  49,  13,  36,  45,  24,  15,  17,
        46,  71,  48, 102,  25,  77,  87,  30,  53,  47,  56,  35,  66,
        61,  85,   5,   4,   2,  98,  26,  99,  65,   9,  70,  59,  29,
       100,  23,  76,  95,  16,  73,  92,  84,  19,  28,  54,  75,  20,
        88,  97,  96,  40,  22,  79,  14,  69,  93,   8,   0,  52,  80,
        90], dtype=int64), 'cur_cost': 124154.0}, {'tour': array([ 34,  50,  90,  12,  86,   3,  45, 100,  95, 102,  32,  62,  18,
        49, 101,  77,  29,  81,  39,  64,   5,  51,  80,  37,  20,  61,
        76, 104,  66,  21,  13,  55,  27,  65,  41,  73,  15,   4,  24,
        28,  43,  54,  11,  16,  48,   1,  75,  40,   2,   0,  52,  69,
         8,  25,  59,   7,  19,  33,  67,   9,  99,   6,  79,  63,  60,
        71,  72,  83,  82,  91,  44,  53,  22,  35,  70,  85,  96,  58,
        78, 103,  10,  88,  14,  68,  98,  89,  31,  30,  94,  84,  36,
        23,  46,  42,  26,  74,  38,  92,  57,  87,  93,  56,  47,  97,
        17], dtype=int64), 'cur_cost': 120766.0}]
2025-08-05 09:52:23,805 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:23,805 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 391, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 391, 'cache_hits': 0, 'similarity_calculations': 2058, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,806 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 72,  19,   1,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57], dtype=int64), 'cur_cost': 128194.0, 'intermediate_solutions': [{'tour': array([ 80,  13,  18,  45,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 128744.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 45,  80,  13,  18,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 129928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 98,  45,  80,  13,  18,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 130315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 18,  45,  80,  13,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 130906.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 18,  98,  45,  80,  13,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 132954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,807 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 128194.00)
2025-08-05 09:52:23,807 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:23,807 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:23,807 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,810 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:23,810 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,811 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106290.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,811 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106290.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,811 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 106290.00)
2025-08-05 09:52:23,811 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:23,812 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:23,812 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,816 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:23,816 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,816 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19963.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,817 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 19963.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,817 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 19963.00)
2025-08-05 09:52:23,817 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:23,817 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:23,817 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,820 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:23,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 81626.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,820 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 81626.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,821 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 81626.00)
2025-08-05 09:52:23,821 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:23,821 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:23,821 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:23,821 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 128588.0
2025-08-05 09:52:23,831 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:23,831 - ExploitationExpert - INFO - res_population_costs: [14697.0, 14683, 14658]
2025-08-05 09:52:23,831 - ExploitationExpert - INFO - res_populations: [array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:23,832 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:23,833 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21195.0}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74047.0}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20952.0}, {'tour': array([ 72,  19,   1,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57], dtype=int64), 'cur_cost': 128194.0}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106290.0}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 19963.0}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 81626.0}, {'tour': array([103,  51,  86,  12,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16], dtype=int64), 'cur_cost': 128588.0}, {'tour': array([ 78,   3,  51,  83,  86,  64,  31,  37,  81,  38,  74,  62,  60,
        55,  82,  50, 101,  27,   7,  12,  32,  10,  89,  11,  72,  41,
        18,  34, 103,  43,  33,  42,  91,  63,  68,   6, 104,  58,  44,
        21,  94,  39,  67,   1,  57,  49,  13,  36,  45,  24,  15,  17,
        46,  71,  48, 102,  25,  77,  87,  30,  53,  47,  56,  35,  66,
        61,  85,   5,   4,   2,  98,  26,  99,  65,   9,  70,  59,  29,
       100,  23,  76,  95,  16,  73,  92,  84,  19,  28,  54,  75,  20,
        88,  97,  96,  40,  22,  79,  14,  69,  93,   8,   0,  52,  80,
        90], dtype=int64), 'cur_cost': 124154.0}, {'tour': array([ 34,  50,  90,  12,  86,   3,  45, 100,  95, 102,  32,  62,  18,
        49, 101,  77,  29,  81,  39,  64,   5,  51,  80,  37,  20,  61,
        76, 104,  66,  21,  13,  55,  27,  65,  41,  73,  15,   4,  24,
        28,  43,  54,  11,  16,  48,   1,  75,  40,   2,   0,  52,  69,
         8,  25,  59,   7,  19,  33,  67,   9,  99,   6,  79,  63,  60,
        71,  72,  83,  82,  91,  44,  53,  22,  35,  70,  85,  96,  58,
        78, 103,  10,  88,  14,  68,  98,  89,  31,  30,  94,  84,  36,
        23,  46,  42,  26,  74,  38,  92,  57,  87,  93,  56,  47,  97,
        17], dtype=int64), 'cur_cost': 120766.0}]
2025-08-05 09:52:23,835 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:23,835 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 392, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 392, 'cache_hits': 0, 'similarity_calculations': 2059, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:23,836 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([103,  51,  86,  12,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16], dtype=int64), 'cur_cost': 128588.0, 'intermediate_solutions': [{'tour': array([ 26,  77,  74,  13, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 13,  26,  77,  74, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 131064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([104,  13,  26,  77,  74,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 74,  13,  26,  77, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 74, 104,  13,  26,  77,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:23,836 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 128588.00)
2025-08-05 09:52:23,836 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:23,836 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:23,836 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,839 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:23,839 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,840 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100066.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,840 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 100066.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,840 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 100066.00)
2025-08-05 09:52:23,840 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:23,840 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:23,840 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:23,844 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:23,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:23,845 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21284.0, 路径长度: 105, 收集中间解: 0
2025-08-05 09:52:23,845 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21284.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:23,845 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 21284.00)
2025-08-05 09:52:23,845 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:23,845 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:23,848 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21195.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74047.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20952.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 72,  19,   1,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57], dtype=int64), 'cur_cost': 128194.0, 'intermediate_solutions': [{'tour': array([ 80,  13,  18,  45,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 128744.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 45,  80,  13,  18,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 129928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 98,  45,  80,  13,  18,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 130315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 18,  45,  80,  13,  98,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 130906.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 18,  98,  45,  80,  13,  84,  26,  62,   0,  30,  47,  54,  60,
        15, 100,  24,  76,  44,  21,  95,  43,  85,  37,  66,  16,  31,
        69,   2,   9,  91,  12,  17,  35,  10,  70,  34,  77,   7,  42,
       102,  96,   3,  81,  74,  56,  50,  28,  38,  87,  49,  33,  41,
         4,  27,  39,  61,  93,  40,  36,  48,  67,  75,  29,  99,  65,
        68,  59,  92,  63,   8,  14,  11,  79,  20, 104,  97,  72,  90,
        73, 101,  89,  58,  46,  88,  51,  82,  53,   1, 103,  83,  23,
        94,   6,   5,  57,  52,  25,  78,  22,  55,  86,  32,  19,  71,
        64], dtype=int64), 'cur_cost': 132954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106290.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 19963.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 81626.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([103,  51,  86,  12,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16], dtype=int64), 'cur_cost': 128588.0, 'intermediate_solutions': [{'tour': array([ 26,  77,  74,  13, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 13,  26,  77,  74, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 131064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([104,  13,  26,  77,  74,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 74,  13,  26,  77, 104,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 74, 104,  13,  26,  77,  31,  58,  27,  16,  52,  29,  89,  46,
        47,  82,  38,  40,  24,  18,  70,  20,  62,  67,  44,  61,  35,
        51,  17,  12,  86,   6,  83,  14,  85,  90,  41,   2,  54,  60,
        81,  45,  78,   7,  22,  88,   1,  36,  72, 101,  73,  87,  28,
        39,  97,  71,  80,  65,  48,  21,  98,  53,  84,  75, 102,  34,
        37,  11,  96,   0,  33,  23,  56,  92,  55,  50,  66,  68,  25,
         8,  76,  19,  63,  69,  32,  10,   4,  64,  42,  94,  30,  15,
         5,  93,   3,  49, 100,  43,  99,  57,  95,  59,  91,   9,  79,
       103], dtype=int64), 'cur_cost': 132725.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 100066.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21284.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:23,849 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:23,849 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,855 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=19963.000, 多样性=0.971
2025-08-05 09:52:23,855 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:23,855 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:23,855 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:23,855 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03390972726419383, 'best_improvement': -0.15286440286440287}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02008117923520611}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06261162045888004, 'recent_improvements': [-0.05255189068448968, 8.704175307173757e-05, 0.0726713502332704], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 14658, 'new_best_cost': 14658, 'quality_improvement': 0.0, 'old_diversity': 0.7142857142857143, 'new_diversity': 0.7142857142857143, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:23,857 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:23,857 - __main__ - INFO - lin105 开始进化第 2 代
2025-08-05 09:52:23,857 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:23,857 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:23,857 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=19963.000, 多样性=0.971
2025-08-05 09:52:23,857 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:23,865 - PathExpert - INFO - 路径结构分析完成: 公共边数量=6, 路径相似性=0.971
2025-08-05 09:52:23,865 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:23,867 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.714
2025-08-05 09:52:23,869 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:23,869 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:23,869 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 09:52:23,869 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 09:52:23,917 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.077, 适应度梯度: -8638.600, 聚类评分: 0.000, 覆盖率: 0.171, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:23,917 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:23,918 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:23,918 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 09:52:23,922 - visualization.landscape_visualizer - INFO - 插值约束: 33 个点被约束到最小值 14658.00
2025-08-05 09:52:24,011 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_152_20250805_095223.html
2025-08-05 09:52:24,063 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_152_20250805_095223.html
2025-08-05 09:52:24,064 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 152
2025-08-05 09:52:24,064 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:24,064 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1943秒
2025-08-05 09:52:24,064 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.07692307692307693, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -8638.599999999997, 'local_optima_density': 0.07692307692307693, 'gradient_variance': 1073046276.4923077, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1713, 'fitness_entropy': 0.8101434166941021, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -8638.600)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.171)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358743.9178736, 'performance_metrics': {}}}
2025-08-05 09:52:24,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:24,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:24,064 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:24,065 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:24,066 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:24,066 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:24,066 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:24,066 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,066 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:24,066 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 09:52:24,066 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,067 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:24,067 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:24,067 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:24,067 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:24,067 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,071 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,072 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,073 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21113.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,073 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21113.0, 'intermediate_solutions': [{'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 40, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 41, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 87, 86, 65, 64, 59, 60, 36, 35, 32, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 93, 94, 99], 'cur_cost': 23207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 21, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25733.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,073 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 21113.00)
2025-08-05 09:52:24,073 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:24,073 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:24,073 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,078 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,079 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22823.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,079 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22823.0, 'intermediate_solutions': [{'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 95, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 104], 'cur_cost': 71743.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 36, 48, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74441.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 65, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 25, 29, 1, 16, 69, 95], 'cur_cost': 76268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,079 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 22823.00)
2025-08-05 09:52:24,079 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:24,079 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:24,080 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,082 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:24,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68114.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,084 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68114.0, 'intermediate_solutions': [{'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 60, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 11, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25810.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 53, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23217.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,084 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 68114.00)
2025-08-05 09:52:24,084 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:24,084 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,084 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,084 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 127226.0
2025-08-05 09:52:24,092 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:24,092 - ExploitationExpert - INFO - res_population_costs: [14658, 14683, 14697.0]
2025-08-05 09:52:24,092 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,094 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,094 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21113.0}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22823.0}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68114.0}, {'tour': array([ 26,  82,  22,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60], dtype=int64), 'cur_cost': 127226.0}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106290.0}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 19963.0}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 81626.0}, {'tour': [103, 51, 86, 12, 69, 27, 79, 33, 62, 100, 47, 1, 20, 23, 93, 2, 14, 37, 55, 29, 22, 36, 81, 32, 24, 48, 25, 96, 6, 94, 26, 43, 9, 90, 75, 101, 78, 13, 56, 65, 97, 68, 98, 21, 52, 35, 5, 19, 91, 74, 7, 88, 38, 0, 67, 85, 63, 54, 17, 46, 59, 10, 64, 99, 82, 18, 83, 77, 80, 45, 28, 41, 44, 84, 61, 42, 95, 73, 4, 87, 60, 11, 70, 34, 102, 53, 57, 92, 50, 58, 72, 71, 30, 66, 3, 15, 76, 31, 89, 8, 39, 40, 104, 49, 16], 'cur_cost': 128588.0}, {'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 100066.0}, {'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21284.0}]
2025-08-05 09:52:24,095 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:24,096 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 393, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 393, 'cache_hits': 0, 'similarity_calculations': 2061, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,097 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 26,  82,  22,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60], dtype=int64), 'cur_cost': 127226.0, 'intermediate_solutions': [{'tour': array([  1,  19,  72,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 51,   1,  19,  72,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 28,  51,   1,  19,  72,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 129000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 72,  51,   1,  19,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 126594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 72,  28,  51,   1,  19,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,097 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 127226.00)
2025-08-05 09:52:24,097 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:24,097 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:24,097 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,110 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,112 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,112 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72840.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,112 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 72840.0, 'intermediate_solutions': [{'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 98, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 99, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 105910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 76, 87, 93, 21, 22, 26, 80, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106381.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 66, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 109545.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,112 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 72840.00)
2025-08-05 09:52:24,113 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:24,113 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:24,113 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,133 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78565.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,136 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 78565.0, 'intermediate_solutions': [{'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 34, 4, 3, 12, 13, 33, 8, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22485.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 65, 64, 60, 59, 38, 37, 34, 33, 13, 12, 3, 4, 8, 7, 2, 1, 5, 6, 9, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 86, 87, 93, 94, 99], 'cur_cost': 21666.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 64, 12, 13, 33, 34, 37, 38, 59, 60, 65, 86, 87, 93, 94, 99], 'cur_cost': 23193.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,136 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 78565.00)
2025-08-05 09:52:24,136 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:24,136 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:24,137 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,159 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 73424.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,162 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 73424.0, 'intermediate_solutions': [{'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 45, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 30, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 83249.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 77, 70, 83, 67, 71, 76, 87, 88, 89, 44, 18, 103, 74, 69, 62, 61, 104, 43, 47, 49, 22, 39, 37, 40, 12, 46, 57, 3, 8, 58, 64, 45, 50, 84, 79, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 82005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 26, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 84126.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,163 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 73424.00)
2025-08-05 09:52:24,163 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:24,164 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,164 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 124398.0
2025-08-05 09:52:24,177 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:24,177 - ExploitationExpert - INFO - res_population_costs: [14658, 14683, 14697.0, 14579]
2025-08-05 09:52:24,177 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64)]
2025-08-05 09:52:24,180 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,180 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21113.0}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22823.0}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68114.0}, {'tour': array([ 26,  82,  22,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60], dtype=int64), 'cur_cost': 127226.0}, {'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 72840.0}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 78565.0}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 73424.0}, {'tour': array([ 93, 100,  50,  24,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21], dtype=int64), 'cur_cost': 124398.0}, {'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 100066.0}, {'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21284.0}]
2025-08-05 09:52:24,181 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,181 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 394, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 394, 'cache_hits': 0, 'similarity_calculations': 2064, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,183 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 93, 100,  50,  24,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21], dtype=int64), 'cur_cost': 124398.0, 'intermediate_solutions': [{'tour': array([ 86,  51, 103,  12,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 129009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 12,  86,  51, 103,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 127756.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 69,  12,  86,  51, 103,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([103,  12,  86,  51,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([103,  69,  12,  86,  51,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,183 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 124398.00)
2025-08-05 09:52:24,183 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:24,183 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:24,183 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,186 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:24,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,188 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103959.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,188 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [72, 12, 26, 8, 30, 27, 31, 103, 18, 1, 25, 4, 65, 36, 35, 42, 15, 57, 23, 24, 50, 98, 39, 64, 49, 54, 28, 58, 104, 62, 73, 46, 55, 68, 83, 52, 71, 85, 97, 63, 74, 80, 75, 38, 78, 91, 0, 70, 81, 96, 100, 2, 92, 33, 34, 11, 9, 95, 60, 44, 41, 90, 77, 94, 93, 99, 29, 45, 40, 19, 17, 56, 51, 87, 13, 76, 86, 59, 21, 89, 37, 48, 82, 20, 102, 5, 101, 32, 66, 3, 16, 7, 53, 79, 61, 69, 6, 10, 47, 84, 67, 43, 22, 88, 14], 'cur_cost': 103959.0, 'intermediate_solutions': [{'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 37, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 102, 68, 72, 92], 'cur_cost': 98262.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 35, 97, 74, 70, 77, 85, 83, 67, 58, 60, 87, 79, 89, 39, 80, 40, 59, 46, 62, 53, 104, 43, 52, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 99833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 4, 20, 24, 9, 10, 27, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 18, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 103301.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,188 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 103959.00)
2025-08-05 09:52:24,188 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:24,189 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:24,189 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,193 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22293.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,194 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22293.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 48, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 35, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 95, 90, 91, 92, 97, 98, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22324.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 5, 16, 15, 17, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22728.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,195 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 22293.00)
2025-08-05 09:52:24,195 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:24,195 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:24,198 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21113.0, 'intermediate_solutions': [{'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 40, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 41, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 87, 86, 65, 64, 59, 60, 36, 35, 32, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 93, 94, 99], 'cur_cost': 23207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 16, 5, 1, 9, 10, 14, 102, 20, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 21, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25733.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22823.0, 'intermediate_solutions': [{'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 95, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 104], 'cur_cost': 71743.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 36, 48, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 65, 25, 29, 1, 16, 69, 95], 'cur_cost': 74441.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 4, 20, 0, 103, 24, 27, 13, 34, 102, 51, 72, 68, 54, 73, 52, 56, 48, 36, 42, 49, 79, 84, 50, 75, 64, 58, 81, 77, 45, 57, 26, 12, 8, 23, 32, 65, 44, 22, 33, 39, 47, 43, 55, 31, 30, 28, 10, 3, 40, 18, 62, 89, 88, 87, 59, 37, 86, 83, 85, 61, 70, 46, 63, 74, 60, 41, 38, 53, 76, 104, 90, 66, 100, 78, 80, 94, 82, 97, 98, 35, 9, 19, 14, 2, 21, 15, 17, 11, 6, 71, 96, 101, 67, 92, 91, 93, 99, 25, 29, 1, 16, 69, 95], 'cur_cost': 76268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68114.0, 'intermediate_solutions': [{'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 60, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 11, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25810.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 14, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 53, 91, 90, 95, 96, 100, 101, 63, 48, 11, 10, 9, 6, 5, 1, 2, 7, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23217.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 26,  82,  22,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60], dtype=int64), 'cur_cost': 127226.0, 'intermediate_solutions': [{'tour': array([  1,  19,  72,  51,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 51,   1,  19,  72,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 28,  51,   1,  19,  72,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 129000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 72,  51,   1,  19,  28,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 126594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 72,  28,  51,   1,  19,  33,  83,  65,  23,  62,  74,  64,  54,
        76,  43,   2,  27,  40,  53,   4,  20,  12,  17,  71,  97,  24,
        75,  25,  79,  41,  15,  93,  98,  99,  31,  94,  16,  73,  30,
        45,  80,  89,  48, 101,  50,  18,  78,  11,  39, 102,  49,  14,
        22,  44,  60,  55,   5,  70,  29,  63,  21,  32,  13,   8,  68,
        85,  86,  92,   0, 104,  96,   7,  36,  66,   6,  52,  26,  34,
        67,  46,   9,  61,  59,  77,  38,  90,  81,   3,  87,  10,  88,
        42, 100,  95,  58,  69,  84,  91, 103,  37,  82,  56,  47,  35,
        57]), 'cur_cost': 128004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 72840.0, 'intermediate_solutions': [{'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 98, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 99, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 105910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 66, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 76, 87, 93, 21, 22, 26, 80, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 106381.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 16, 20, 0, 103, 10, 13, 34, 27, 51, 19, 68, 54, 73, 28, 56, 48, 36, 42, 25, 79, 17, 15, 11, 64, 58, 8, 66, 3, 12, 33, 37, 38, 47, 49, 52, 43, 55, 61, 30, 69, 74, 40, 39, 44, 89, 88, 78, 59, 71, 104, 83, 62, 70, 46, 84, 60, 75, 53, 67, 90, 95, 82, 63, 97, 35, 14, 1, 91, 57, 86, 101, 32, 18, 94, 45, 29, 9, 99, 77, 50, 102, 65, 92, 7, 31, 96, 85, 41, 98, 2, 80, 26, 22, 21, 93, 87, 76, 100, 24, 23, 6, 81, 4, 72], 'cur_cost': 109545.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 78565.0, 'intermediate_solutions': [{'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 34, 4, 3, 12, 13, 33, 8, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22485.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 65, 64, 60, 59, 38, 37, 34, 33, 13, 12, 3, 4, 8, 7, 2, 1, 5, 6, 9, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 86, 87, 93, 94, 99], 'cur_cost': 21666.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 11, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 9, 6, 5, 1, 2, 7, 8, 4, 3, 64, 12, 13, 33, 34, 37, 38, 59, 60, 65, 86, 87, 93, 94, 99], 'cur_cost': 23193.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 73424.0, 'intermediate_solutions': [{'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 45, 54, 21, 52, 56, 31, 26, 25, 79, 84, 50, 30, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 83249.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 26, 25, 77, 70, 83, 67, 71, 76, 87, 88, 89, 44, 18, 103, 74, 69, 62, 61, 104, 43, 47, 49, 22, 39, 37, 40, 12, 46, 57, 3, 8, 58, 64, 45, 50, 84, 79, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 82005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 14, 0, 9, 10, 13, 20, 102, 28, 19, 30, 54, 21, 52, 56, 31, 25, 79, 84, 50, 45, 64, 58, 8, 3, 57, 46, 12, 40, 37, 39, 22, 49, 47, 43, 104, 61, 62, 69, 74, 103, 18, 44, 89, 88, 87, 76, 71, 67, 83, 70, 77, 81, 82, 97, 35, 72, 75, 38, 78, 86, 92, 90, 100, 96, 94, 26, 63, 48, 85, 93, 98, 2, 91, 15, 17, 4, 6, 101, 33, 34, 1, 27, 60, 59, 23, 24, 29, 55, 16, 68, 66, 80, 99, 73, 95, 11, 32, 65, 53, 42, 36, 41, 51], 'cur_cost': 84126.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 93, 100,  50,  24,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21], dtype=int64), 'cur_cost': 124398.0, 'intermediate_solutions': [{'tour': array([ 86,  51, 103,  12,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 129009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 12,  86,  51, 103,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 127756.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 69,  12,  86,  51, 103,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128430.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([103,  12,  86,  51,  69,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([103,  69,  12,  86,  51,  27,  79,  33,  62, 100,  47,   1,  20,
        23,  93,   2,  14,  37,  55,  29,  22,  36,  81,  32,  24,  48,
        25,  96,   6,  94,  26,  43,   9,  90,  75, 101,  78,  13,  56,
        65,  97,  68,  98,  21,  52,  35,   5,  19,  91,  74,   7,  88,
        38,   0,  67,  85,  63,  54,  17,  46,  59,  10,  64,  99,  82,
        18,  83,  77,  80,  45,  28,  41,  44,  84,  61,  42,  95,  73,
         4,  87,  60,  11,  70,  34, 102,  53,  57,  92,  50,  58,  72,
        71,  30,  66,   3,  15,  76,  31,  89,   8,  39,  40, 104,  49,
        16]), 'cur_cost': 128556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [72, 12, 26, 8, 30, 27, 31, 103, 18, 1, 25, 4, 65, 36, 35, 42, 15, 57, 23, 24, 50, 98, 39, 64, 49, 54, 28, 58, 104, 62, 73, 46, 55, 68, 83, 52, 71, 85, 97, 63, 74, 80, 75, 38, 78, 91, 0, 70, 81, 96, 100, 2, 92, 33, 34, 11, 9, 95, 60, 44, 41, 90, 77, 94, 93, 99, 29, 45, 40, 19, 17, 56, 51, 87, 13, 76, 86, 59, 21, 89, 37, 48, 82, 20, 102, 5, 101, 32, 66, 3, 16, 7, 53, 79, 61, 69, 6, 10, 47, 84, 67, 43, 22, 88, 14], 'cur_cost': 103959.0, 'intermediate_solutions': [{'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 37, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 102, 68, 72, 92], 'cur_cost': 98262.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 4, 20, 24, 9, 10, 27, 18, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 35, 97, 74, 70, 77, 85, 83, 67, 58, 60, 87, 79, 89, 39, 80, 40, 59, 46, 62, 53, 104, 43, 52, 100, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 99833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 4, 20, 24, 9, 10, 27, 34, 102, 51, 29, 31, 73, 22, 19, 30, 23, 26, 25, 36, 84, 42, 11, 64, 7, 81, 3, 50, 13, 33, 8, 103, 32, 61, 47, 49, 54, 52, 43, 104, 53, 62, 46, 59, 40, 80, 39, 89, 79, 87, 60, 58, 67, 83, 85, 77, 70, 74, 97, 35, 100, 18, 75, 98, 86, 71, 90, 91, 95, 78, 45, 82, 63, 48, 101, 88, 21, 65, 2, 12, 96, 17, 6, 16, 5, 66, 0, 99, 93, 44, 1, 55, 38, 28, 69, 57, 41, 94, 56, 76, 14, 37, 68, 72, 92], 'cur_cost': 103301.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22293.0, 'intermediate_solutions': [{'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 48, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 35, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 16, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 95, 90, 91, 92, 97, 98, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22324.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 5, 16, 15, 17, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 8, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22728.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:24,199 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:24,199 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:24,206 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21113.000, 多样性=0.961
2025-08-05 09:52:24,206 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:24,207 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:24,207 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:24,208 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.02162895925945776, 'best_improvement': -0.05760657215849321}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009810333551340939}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.016911342755561044, 'recent_improvements': [8.704175307173757e-05, 0.0726713502332704, 0.03390972726419383], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 14579, 'new_best_cost': 14579, 'quality_improvement': 0.0, 'old_diversity': 0.8507936507936508, 'new_diversity': 0.8507936507936508, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:24,208 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:24,208 - __main__ - INFO - lin105 开始进化第 3 代
2025-08-05 09:52:24,208 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:24,209 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:24,211 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=21113.000, 多样性=0.961
2025-08-05 09:52:24,212 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:24,219 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.961
2025-08-05 09:52:24,220 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:24,223 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.851
2025-08-05 09:52:24,227 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:24,227 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:24,227 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:24,228 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:24,293 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -4078.571, 聚类评分: 0.000, 覆盖率: 0.172, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:24,293 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:24,293 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:24,293 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 09:52:24,298 - visualization.landscape_visualizer - INFO - 插值约束: 73 个点被约束到最小值 14579.00
2025-08-05 09:52:24,432 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_153_20250805_095224.html
2025-08-05 09:52:24,495 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_153_20250805_095224.html
2025-08-05 09:52:24,495 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 153
2025-08-05 09:52:24,495 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:24,496 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2678秒
2025-08-05 09:52:24,496 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4078.571428571428, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 1467320438.0906124, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1723, 'fitness_entropy': 0.827409557091281, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4078.571)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.172)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358744.2933571, 'performance_metrics': {}}}
2025-08-05 09:52:24,496 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:24,496 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:24,496 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:24,496 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:24,497 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,497 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:24,497 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,497 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,497 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:24,497 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,497 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,498 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:24,498 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:24,498 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:24,498 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:24,498 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,511 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,512 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,512 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,512 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,512 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72645.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,513 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72645.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 13, 12, 73, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 4, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 28371.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 48, 63, 101, 100, 96, 95, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23467.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 20, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23265.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,513 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 72645.00)
2025-08-05 09:52:24,513 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:24,513 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:24,513 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,517 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20669.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,519 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20669.0, 'intermediate_solutions': [{'tour': [0, 3, 6, 21, 20, 28, 17, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 29, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24532.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 93, 87, 86, 65, 94, 99], 'cur_cost': 24115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 60, 34, 37, 38, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,519 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 20669.00)
2025-08-05 09:52:24,519 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:24,519 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:24,519 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,523 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,525 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,525 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21463.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,525 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21463.0, 'intermediate_solutions': [{'tour': [72, 1, 3, 69, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 13, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 73359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 65, 30, 41, 12, 37, 101, 99, 87, 59, 90, 91, 92, 34, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 70535.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 52, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,526 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21463.00)
2025-08-05 09:52:24,526 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:24,526 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,526 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,527 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 128864.0
2025-08-05 09:52:24,540 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:24,540 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0]
2025-08-05 09:52:24,540 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,543 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,543 - ExploitationExpert - INFO - populations: [{'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72645.0}, {'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20669.0}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21463.0}, {'tour': array([ 95, 103,  69,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71], dtype=int64), 'cur_cost': 128864.0}, {'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 72840.0}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 78565.0}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 73424.0}, {'tour': [93, 100, 50, 24, 77, 57, 66, 65, 28, 2, 41, 20, 40, 54, 7, 80, 11, 104, 1, 76, 29, 5, 12, 62, 37, 70, 101, 75, 47, 44, 33, 90, 102, 43, 73, 79, 49, 38, 74, 68, 97, 55, 15, 69, 61, 78, 58, 14, 59, 95, 92, 45, 94, 25, 16, 39, 32, 56, 34, 0, 19, 72, 88, 83, 10, 91, 103, 89, 4, 87, 81, 18, 22, 52, 86, 23, 3, 53, 27, 85, 96, 84, 42, 99, 30, 82, 6, 36, 9, 26, 13, 64, 60, 67, 35, 48, 98, 51, 17, 63, 71, 31, 8, 46, 21], 'cur_cost': 124398.0}, {'tour': [72, 12, 26, 8, 30, 27, 31, 103, 18, 1, 25, 4, 65, 36, 35, 42, 15, 57, 23, 24, 50, 98, 39, 64, 49, 54, 28, 58, 104, 62, 73, 46, 55, 68, 83, 52, 71, 85, 97, 63, 74, 80, 75, 38, 78, 91, 0, 70, 81, 96, 100, 2, 92, 33, 34, 11, 9, 95, 60, 44, 41, 90, 77, 94, 93, 99, 29, 45, 40, 19, 17, 56, 51, 87, 13, 76, 86, 59, 21, 89, 37, 48, 82, 20, 102, 5, 101, 32, 66, 3, 16, 7, 53, 79, 61, 69, 6, 10, 47, 84, 67, 43, 22, 88, 14], 'cur_cost': 103959.0}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22293.0}]
2025-08-05 09:52:24,544 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,544 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 395, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 395, 'cache_hits': 0, 'similarity_calculations': 2068, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,546 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 95, 103,  69,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71], dtype=int64), 'cur_cost': 128864.0, 'intermediate_solutions': [{'tour': array([ 22,  82,  26,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127350.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 67,  22,  82,  26,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127559.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 65,  67,  22,  82,  26,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 26,  67,  22,  82,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 26,  65,  67,  22,  82,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 126844.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,546 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 128864.00)
2025-08-05 09:52:24,546 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:24,546 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:24,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,561 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,561 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,561 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68902.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,562 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68902.0, 'intermediate_solutions': [{'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 95, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 9, 94, 99], 'cur_cost': 81273.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [96, 53, 45, 62, 84, 97, 85, 69, 60, 40, 22, 20, 47, 21, 24, 56, 49, 19, 41, 15, 17, 14, 28, 50, 70, 92, 71, 64, 61, 68, 39, 54, 104, 79, 74, 82, 66, 57, 7, 11, 35, 55, 67, 65, 51, 25, 38, 1, 18, 27, 36, 103, 102, 31, 30, 44, 5, 12, 26, 48, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 73371.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 100, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 75055.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,563 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 68902.00)
2025-08-05 09:52:24,563 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:24,563 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:24,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,569 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,571 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21969.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,571 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21969.0, 'intermediate_solutions': [{'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 76, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 18, 26, 15, 7, 98], 'cur_cost': 79502.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 8, 23, 54, 83, 99, 96, 84, 92, 90, 91, 82, 97, 77, 104, 86, 80, 68, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 82210.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 2, 98], 'cur_cost': 78503.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,572 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 21969.00)
2025-08-05 09:52:24,572 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:24,572 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:24,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,588 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72074.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,590 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72074.0, 'intermediate_solutions': [{'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 12, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 7, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 72951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 72, 43, 84, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 74008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 23, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 75998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,591 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 72074.00)
2025-08-05 09:52:24,592 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:24,592 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,592 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,592 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 129603.0
2025-08-05 09:52:24,604 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:24,604 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0]
2025-08-05 09:52:24,604 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,606 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,606 - ExploitationExpert - INFO - populations: [{'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72645.0}, {'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20669.0}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21463.0}, {'tour': array([ 95, 103,  69,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71], dtype=int64), 'cur_cost': 128864.0}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68902.0}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21969.0}, {'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72074.0}, {'tour': array([ 92,  12,  16,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22], dtype=int64), 'cur_cost': 129603.0}, {'tour': [72, 12, 26, 8, 30, 27, 31, 103, 18, 1, 25, 4, 65, 36, 35, 42, 15, 57, 23, 24, 50, 98, 39, 64, 49, 54, 28, 58, 104, 62, 73, 46, 55, 68, 83, 52, 71, 85, 97, 63, 74, 80, 75, 38, 78, 91, 0, 70, 81, 96, 100, 2, 92, 33, 34, 11, 9, 95, 60, 44, 41, 90, 77, 94, 93, 99, 29, 45, 40, 19, 17, 56, 51, 87, 13, 76, 86, 59, 21, 89, 37, 48, 82, 20, 102, 5, 101, 32, 66, 3, 16, 7, 53, 79, 61, 69, 6, 10, 47, 84, 67, 43, 22, 88, 14], 'cur_cost': 103959.0}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22293.0}]
2025-08-05 09:52:24,608 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,608 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 396, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 396, 'cache_hits': 0, 'similarity_calculations': 2073, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,609 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 92,  12,  16,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22], dtype=int64), 'cur_cost': 129603.0, 'intermediate_solutions': [{'tour': array([ 50, 100,  93,  24,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 24,  50, 100,  93,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 121975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 77,  24,  50, 100,  93,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 93,  24,  50, 100,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 93,  77,  24,  50, 100,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 125086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,609 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 129603.00)
2025-08-05 09:52:24,610 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:24,610 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,610 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,610 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114389.0
2025-08-05 09:52:24,621 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:24,621 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0]
2025-08-05 09:52:24,621 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,624 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,624 - ExploitationExpert - INFO - populations: [{'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72645.0}, {'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20669.0}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21463.0}, {'tour': array([ 95, 103,  69,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71], dtype=int64), 'cur_cost': 128864.0}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68902.0}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21969.0}, {'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72074.0}, {'tour': array([ 92,  12,  16,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22], dtype=int64), 'cur_cost': 129603.0}, {'tour': array([ 82,  63,  73,   8,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14], dtype=int64), 'cur_cost': 114389.0}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22293.0}]
2025-08-05 09:52:24,626 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,626 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 397, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 397, 'cache_hits': 0, 'similarity_calculations': 2079, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,628 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 82,  63,  73,   8,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14], dtype=int64), 'cur_cost': 114389.0, 'intermediate_solutions': [{'tour': array([ 26,  12,  72,   8,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  8,  26,  12,  72,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 103543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 30,   8,  26,  12,  72,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 103878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 72,   8,  26,  12,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 72,  30,   8,  26,  12,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,628 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 114389.00)
2025-08-05 09:52:24,628 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:24,628 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:24,628 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,647 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 70307.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,649 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70307.0, 'intermediate_solutions': [{'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 76, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 61, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23957.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 12, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26131.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 68, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,649 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 70307.00)
2025-08-05 09:52:24,649 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:24,650 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:24,654 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72645.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 13, 12, 73, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 4, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 28371.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 48, 63, 101, 100, 96, 95, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23467.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 13, 12, 4, 8, 7, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 102, 14, 10, 9, 6, 5, 18, 23, 26, 25, 24, 17, 15, 16, 36, 35, 32, 103, 39, 44, 47, 49, 54, 55, 58, 104, 56, 53, 50, 46, 43, 40, 42, 45, 51, 52, 57, 20, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 41, 38, 37, 34, 33, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23265.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20669.0, 'intermediate_solutions': [{'tour': [0, 3, 6, 21, 20, 28, 17, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 29, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24532.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 34, 37, 38, 59, 60, 64, 93, 87, 86, 65, 94, 99], 'cur_cost': 24115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 21, 20, 28, 29, 30, 31, 27, 22, 19, 102, 14, 10, 9, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 11, 13, 12, 4, 33, 60, 34, 37, 38, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21463.0, 'intermediate_solutions': [{'tour': [72, 1, 3, 69, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 13, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 73359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 52, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 65, 30, 41, 12, 37, 101, 99, 87, 59, 90, 91, 92, 34, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 70535.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [72, 1, 3, 13, 20, 28, 29, 7, 31, 26, 22, 19, 102, 14, 10, 9, 5, 40, 2, 4, 8, 16, 36, 24, 42, 18, 23, 57, 56, 17, 50, 98, 43, 35, 32, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 53, 73, 74, 80, 68, 83, 45, 51, 78, 76, 71, 67, 69, 63, 21, 81, 82, 97, 75, 79, 88, 89, 0, 86, 52, 66, 70, 77, 95, 96, 100, 84, 85, 48, 46, 33, 34, 92, 91, 90, 59, 87, 99, 101, 37, 12, 41, 30, 65, 94, 93, 27, 11, 103, 15, 60, 6, 38, 25, 64], 'cur_cost': 68928.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 95, 103,  69,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71], dtype=int64), 'cur_cost': 128864.0, 'intermediate_solutions': [{'tour': array([ 22,  82,  26,  67,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127350.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 67,  22,  82,  26,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127559.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 65,  67,  22,  82,  26,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 26,  67,  22,  82,  65,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 127101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 26,  65,  67,  22,  82,  97,  38,  91,  16, 100,  96,  61,  31,
        86,  72,  34,  43,   5,   7,  83,  55, 103,  71,  45,  29,  77,
        12,   0,  48,  90,  88,  58,  50,  30,  59,  76,  87,  89,  14,
        54,  28,  63,  73,  32,  23,  49, 101,  92,  11,  80,  94,  17,
        10,   3,  70,  37,  40,  19,  85,  42, 104,   8,  21,  84,  75,
        35,  53,  78,  39,  36,  57,  56, 102,  41,  44,  81,   6,   4,
        62,   1,  51,  98,  24,  52,  47,  46,  15,  20,  13,   9,  33,
        18,  79,   2,  74,  25,  66,  64,  95,  27,  93,  69,  68,  99,
        60]), 'cur_cost': 126844.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68902.0, 'intermediate_solutions': [{'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 95, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 9, 94, 99], 'cur_cost': 81273.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [96, 53, 45, 62, 84, 97, 85, 69, 60, 40, 22, 20, 47, 21, 24, 56, 49, 19, 41, 15, 17, 14, 28, 50, 70, 92, 71, 64, 61, 68, 39, 54, 104, 79, 74, 82, 66, 57, 7, 11, 35, 55, 67, 65, 51, 25, 38, 1, 18, 27, 36, 103, 102, 31, 30, 44, 5, 12, 26, 48, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 100, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 73371.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [96, 53, 45, 62, 48, 26, 12, 5, 44, 30, 31, 102, 103, 36, 27, 18, 1, 100, 38, 25, 51, 65, 67, 55, 35, 11, 7, 57, 66, 82, 74, 79, 104, 54, 39, 68, 61, 64, 71, 92, 70, 50, 28, 14, 17, 15, 41, 19, 49, 56, 24, 21, 47, 20, 22, 40, 60, 69, 85, 97, 84, 87, 72, 91, 101, 80, 86, 93, 75, 81, 42, 58, 73, 43, 63, 78, 76, 98, 88, 37, 8, 9, 0, 16, 32, 33, 46, 10, 6, 3, 13, 2, 59, 89, 77, 83, 52, 34, 4, 23, 29, 90, 95, 94, 99], 'cur_cost': 75055.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21969.0, 'intermediate_solutions': [{'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 2, 43, 76, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 18, 26, 15, 7, 98], 'cur_cost': 79502.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 8, 23, 54, 83, 99, 96, 84, 92, 90, 91, 82, 97, 77, 104, 86, 80, 68, 2, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 98], 'cur_cost': 82210.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [36, 103, 44, 16, 9, 4, 39, 102, 6, 19, 12, 40, 17, 49, 51, 45, 74, 81, 50, 75, 69, 71, 57, 73, 58, 27, 46, 55, 31, 13, 53, 60, 88, 68, 80, 86, 104, 77, 97, 82, 91, 90, 92, 84, 96, 99, 83, 54, 23, 8, 43, 18, 1, 0, 42, 30, 21, 10, 48, 63, 95, 65, 56, 35, 38, 47, 61, 78, 67, 70, 72, 101, 85, 93, 79, 52, 59, 25, 28, 5, 22, 11, 24, 29, 37, 3, 41, 34, 14, 33, 20, 32, 62, 87, 94, 89, 66, 100, 64, 76, 26, 15, 7, 2, 98], 'cur_cost': 78503.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72074.0, 'intermediate_solutions': [{'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 12, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 7, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 72951.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 23, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 72, 43, 84, 74, 63, 59, 98, 86, 89, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 74008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 46, 102, 47, 18, 33, 62, 66, 83, 70, 52, 65, 94, 88, 51, 37, 7, 39, 50, 76, 71, 73, 81, 100, 49, 53, 36, 54, 28, 5, 15, 35, 10, 30, 103, 38, 12, 0, 24, 57, 104, 75, 69, 58, 68, 90, 79, 78, 55, 22, 42, 25, 21, 34, 2, 44, 67, 77, 84, 43, 72, 74, 63, 59, 98, 86, 89, 23, 93, 80, 97, 92, 48, 17, 40, 56, 26, 8, 1, 6, 11, 19, 27, 9, 32, 45, 60, 13, 4, 3, 41, 20, 16, 14, 85, 101, 95, 87, 99, 64, 82, 91, 96, 31, 29], 'cur_cost': 75998.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 92,  12,  16,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22], dtype=int64), 'cur_cost': 129603.0, 'intermediate_solutions': [{'tour': array([ 50, 100,  93,  24,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 24,  50, 100,  93,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 121975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 77,  24,  50, 100,  93,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124104.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 93,  24,  50, 100,  77,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 124414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 93,  77,  24,  50, 100,  57,  66,  65,  28,   2,  41,  20,  40,
        54,   7,  80,  11, 104,   1,  76,  29,   5,  12,  62,  37,  70,
       101,  75,  47,  44,  33,  90, 102,  43,  73,  79,  49,  38,  74,
        68,  97,  55,  15,  69,  61,  78,  58,  14,  59,  95,  92,  45,
        94,  25,  16,  39,  32,  56,  34,   0,  19,  72,  88,  83,  10,
        91, 103,  89,   4,  87,  81,  18,  22,  52,  86,  23,   3,  53,
        27,  85,  96,  84,  42,  99,  30,  82,   6,  36,   9,  26,  13,
        64,  60,  67,  35,  48,  98,  51,  17,  63,  71,  31,   8,  46,
        21]), 'cur_cost': 125086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 82,  63,  73,   8,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14], dtype=int64), 'cur_cost': 114389.0, 'intermediate_solutions': [{'tour': array([ 26,  12,  72,   8,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  8,  26,  12,  72,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 103543.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 30,   8,  26,  12,  72,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 103878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 72,   8,  26,  12,  30,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104387.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 72,  30,   8,  26,  12,  27,  31, 103,  18,   1,  25,   4,  65,
        36,  35,  42,  15,  57,  23,  24,  50,  98,  39,  64,  49,  54,
        28,  58, 104,  62,  73,  46,  55,  68,  83,  52,  71,  85,  97,
        63,  74,  80,  75,  38,  78,  91,   0,  70,  81,  96, 100,   2,
        92,  33,  34,  11,   9,  95,  60,  44,  41,  90,  77,  94,  93,
        99,  29,  45,  40,  19,  17,  56,  51,  87,  13,  76,  86,  59,
        21,  89,  37,  48,  82,  20, 102,   5, 101,  32,  66,   3,  16,
         7,  53,  79,  61,  69,   6,  10,  47,  84,  67,  43,  22,  88,
        14]), 'cur_cost': 104324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70307.0, 'intermediate_solutions': [{'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 76, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 61, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23957.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 12, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26131.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 22, 5, 6, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 19, 18, 23, 26, 25, 24, 17, 15, 16, 8, 2, 1, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 68, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:24,655 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:24,655 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:24,661 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20669.000, 多样性=0.984
2025-08-05 09:52:24,661 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:24,661 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:24,661 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:24,661 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.00207665199742618, 'best_improvement': 0.021029697342869323}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.023998238661382714}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04715015474636408, 'recent_improvements': [0.0726713502332704, 0.03390972726419383, -0.02162895925945776], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 14579, 'new_best_cost': 14579, 'quality_improvement': 0.0, 'old_diversity': 0.8507936507936508, 'new_diversity': 0.8507936507936508, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:24,662 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:24,662 - __main__ - INFO - lin105 开始进化第 4 代
2025-08-05 09:52:24,662 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:24,662 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:24,664 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20669.000, 多样性=0.984
2025-08-05 09:52:24,665 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:24,671 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.984
2025-08-05 09:52:24,671 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:24,673 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.851
2025-08-05 09:52:24,676 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:24,676 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:24,676 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:24,677 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:24,731 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -13865.429, 聚类评分: 0.000, 覆盖率: 0.173, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:24,731 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:24,731 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:24,732 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 09:52:24,736 - visualization.landscape_visualizer - INFO - 插值约束: 43 个点被约束到最小值 14579.00
2025-08-05 09:52:24,841 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_154_20250805_095224.html
2025-08-05 09:52:24,899 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_154_20250805_095224.html
2025-08-05 09:52:24,899 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 154
2025-08-05 09:52:24,899 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:24,899 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2229秒
2025-08-05 09:52:24,900 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13865.428571428574, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 1967244351.4334695, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1733, 'fitness_entropy': 0.827409557091281, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13865.429)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.173)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358744.731251, 'performance_metrics': {}}}
2025-08-05 09:52:24,900 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:24,900 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:24,900 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:24,900 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:24,901 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,901 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:24,902 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,902 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,902 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:24,902 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:24,902 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:24,903 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:24,903 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:24,903 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:24,903 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:24,903 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,914 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,915 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72465.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,916 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72465.0, 'intermediate_solutions': [{'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 48, 49, 102, 36, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72867.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 4, 16, 2, 7, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 73732.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 1, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17], 'cur_cost': 72719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,917 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 72465.00)
2025-08-05 09:52:24,917 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:24,917 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:24,917 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,921 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:24,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22366.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,922 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22366.0, 'intermediate_solutions': [{'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 89, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 45, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25292.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 7, 16, 17, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 24, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,923 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 22366.00)
2025-08-05 09:52:24,923 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:24,923 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:24,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,925 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:24,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,926 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,927 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107573.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,927 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 107573.0, 'intermediate_solutions': [{'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 29, 20, 21, 28, 102, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 60, 59, 38, 37, 34, 33, 3, 4, 12, 13, 11, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 64, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 65, 86, 87, 93, 94, 99], 'cur_cost': 22244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,927 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 107573.00)
2025-08-05 09:52:24,927 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:24,927 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,928 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,928 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 121663.0
2025-08-05 09:52:24,939 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:24,939 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0, 14522, 14468, 14379]
2025-08-05 09:52:24,939 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,943 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,943 - ExploitationExpert - INFO - populations: [{'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72465.0}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22366.0}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 107573.0}, {'tour': array([ 35,  77,  61,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56], dtype=int64), 'cur_cost': 121663.0}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68902.0}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21969.0}, {'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72074.0}, {'tour': [92, 12, 16, 73, 65, 102, 5, 94, 38, 21, 3, 8, 74, 25, 24, 28, 87, 19, 67, 104, 37, 101, 13, 80, 26, 58, 91, 69, 93, 55, 98, 77, 53, 41, 51, 68, 33, 15, 39, 95, 85, 20, 1, 90, 52, 32, 88, 29, 46, 18, 0, 54, 42, 59, 71, 43, 9, 76, 50, 64, 7, 27, 89, 79, 36, 17, 30, 48, 56, 75, 45, 63, 86, 62, 100, 81, 4, 14, 78, 84, 47, 83, 44, 57, 10, 49, 70, 40, 34, 61, 6, 11, 60, 2, 97, 31, 72, 35, 99, 103, 96, 23, 66, 82, 22], 'cur_cost': 129603.0}, {'tour': [82, 63, 73, 8, 20, 59, 46, 12, 70, 101, 25, 21, 85, 31, 16, 103, 62, 53, 89, 72, 15, 10, 24, 87, 80, 28, 97, 86, 7, 45, 6, 33, 95, 92, 54, 42, 64, 79, 50, 60, 94, 37, 4, 84, 17, 65, 98, 35, 26, 100, 96, 71, 77, 55, 23, 34, 43, 47, 32, 90, 78, 66, 57, 0, 52, 5, 13, 3, 30, 49, 18, 104, 22, 41, 38, 69, 67, 48, 102, 11, 9, 39, 93, 51, 76, 1, 58, 29, 75, 74, 88, 56, 2, 61, 91, 44, 27, 36, 81, 83, 68, 40, 99, 19, 14], 'cur_cost': 114389.0}, {'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70307.0}]
2025-08-05 09:52:24,944 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,944 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 398, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 398, 'cache_hits': 0, 'similarity_calculations': 2086, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,946 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 35,  77,  61,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56], dtype=int64), 'cur_cost': 121663.0, 'intermediate_solutions': [{'tour': array([ 69, 103,  95,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128454.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 97,  69, 103,  95,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128868.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 47,  97,  69, 103,  95,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 130313.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 95,  97,  69, 103,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 126381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 95,  47,  97,  69, 103,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,946 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 121663.00)
2025-08-05 09:52:24,947 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:24,947 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:24,947 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,960 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:24,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 71220.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,963 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71220.0, 'intermediate_solutions': [{'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 57, 68, 79, 88, 87, 58, 51, 17, 19, 53, 45, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 39, 61, 93, 98, 85, 62, 46, 49, 50, 29, 21, 40, 65, 52, 100, 75, 86, 72, 84, 66, 74, 63, 44, 1, 28, 12, 15, 9, 30, 23, 6, 5, 31, 20, 48, 55, 57, 53, 19, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68772.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 32, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 71106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,963 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 71220.00)
2025-08-05 09:52:24,963 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:24,963 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:24,964 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,966 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:24,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,968 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106030.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,968 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106030.0, 'intermediate_solutions': [{'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 85, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 102, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 29056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 60, 59, 3, 4, 12, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 11, 14, 102, 20, 21, 28, 31, 30, 29, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 7, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25245.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 49, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,969 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 106030.00)
2025-08-05 09:52:24,969 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:24,969 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:24,969 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:24,972 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:24,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:24,973 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111179.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:24,973 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 111179.0, 'intermediate_solutions': [{'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 64, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 20, 86, 16, 10], 'cur_cost': 77847.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 61, 53, 63, 91, 83, 43, 9, 11, 48, 41, 28, 18, 35, 60, 78, 81, 89, 71, 67, 95, 75, 55, 54, 80, 79, 96, 97, 72, 90, 77, 101, 88, 62, 7, 3, 0, 27, 40, 56, 34, 1, 19, 38, 52, 30, 36, 22, 32, 20, 25, 57, 69, 58, 70, 47, 37, 24, 8, 103, 13, 23, 26, 49, 84, 82, 92, 73, 46, 45, 50, 76, 65, 104, 98, 66, 51, 102, 39, 31, 21, 44, 59, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 70, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 73833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:24,974 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 111179.00)
2025-08-05 09:52:24,974 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:24,974 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,974 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,974 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 126135.0
2025-08-05 09:52:24,988 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:24,988 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0, 14522, 14468, 14379]
2025-08-05 09:52:24,988 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:24,992 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:24,993 - ExploitationExpert - INFO - populations: [{'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72465.0}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22366.0}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 107573.0}, {'tour': array([ 35,  77,  61,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56], dtype=int64), 'cur_cost': 121663.0}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71220.0}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106030.0}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 111179.0}, {'tour': array([ 85,  95,  84,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91], dtype=int64), 'cur_cost': 126135.0}, {'tour': [82, 63, 73, 8, 20, 59, 46, 12, 70, 101, 25, 21, 85, 31, 16, 103, 62, 53, 89, 72, 15, 10, 24, 87, 80, 28, 97, 86, 7, 45, 6, 33, 95, 92, 54, 42, 64, 79, 50, 60, 94, 37, 4, 84, 17, 65, 98, 35, 26, 100, 96, 71, 77, 55, 23, 34, 43, 47, 32, 90, 78, 66, 57, 0, 52, 5, 13, 3, 30, 49, 18, 104, 22, 41, 38, 69, 67, 48, 102, 11, 9, 39, 93, 51, 76, 1, 58, 29, 75, 74, 88, 56, 2, 61, 91, 44, 27, 36, 81, 83, 68, 40, 99, 19, 14], 'cur_cost': 114389.0}, {'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70307.0}]
2025-08-05 09:52:24,994 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:24,995 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 399, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 399, 'cache_hits': 0, 'similarity_calculations': 2094, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:24,996 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 85,  95,  84,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91], dtype=int64), 'cur_cost': 126135.0, 'intermediate_solutions': [{'tour': array([ 16,  12,  92,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 126814.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 73,  16,  12,  92,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 129604.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 65,  73,  16,  12,  92, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 129567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 92,  73,  16,  12,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 128807.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 92,  65,  73,  16,  12, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 127403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:24,997 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 126135.00)
2025-08-05 09:52:24,997 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:24,997 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:24,997 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:24,997 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 126399.0
2025-08-05 09:52:25,025 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:25,026 - ExploitationExpert - INFO - res_population_costs: [14579, 14658, 14683, 14697.0, 14522, 14468, 14379]
2025-08-05 09:52:25,026 - ExploitationExpert - INFO - res_populations: [array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:25,032 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:25,033 - ExploitationExpert - INFO - populations: [{'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72465.0}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22366.0}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 107573.0}, {'tour': array([ 35,  77,  61,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56], dtype=int64), 'cur_cost': 121663.0}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71220.0}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106030.0}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 111179.0}, {'tour': array([ 85,  95,  84,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91], dtype=int64), 'cur_cost': 126135.0}, {'tour': array([101,  15,  10,  30,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39], dtype=int64), 'cur_cost': 126399.0}, {'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70307.0}]
2025-08-05 09:52:25,039 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒，最大迭代次数: 10
2025-08-05 09:52:25,039 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 400, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 400, 'cache_hits': 0, 'similarity_calculations': 2103, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:25,042 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([101,  15,  10,  30,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39], dtype=int64), 'cur_cost': 126399.0, 'intermediate_solutions': [{'tour': array([ 73,  63,  82,   8,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 114533.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  8,  73,  63,  82,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 114215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 20,   8,  73,  63,  82,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 112259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 82,   8,  73,  63,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 116983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 82,  20,   8,  73,  63,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 115088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:25,042 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 126399.00)
2025-08-05 09:52:25,042 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:25,042 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:25,043 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,051 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:25,052 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,054 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,054 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20988.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,054 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20988.0, 'intermediate_solutions': [{'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 96, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 32, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 77151.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [94, 73, 71, 77, 97, 96, 88, 78, 45, 48, 36, 30, 42, 64, 74, 65, 60, 87, 85, 68, 75, 104, 61, 15, 47, 0, 5, 24, 10, 8, 11, 22, 19, 32, 26, 21, 43, 67, 55, 56, 20, 35, 16, 1, 6, 12, 25, 7, 9, 3, 2, 102, 49, 29, 18, 31, 58, 82, 98, 62, 33, 41, 69, 46, 92, 100, 80, 72, 50, 39, 103, 27, 38, 59, 89, 84, 81, 70, 91, 66, 51, 17, 44, 54, 79, 53, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 7, 25, 12, 6, 1, 16, 35, 9, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 69697.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,055 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 20988.00)
2025-08-05 09:52:25,055 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:25,055 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:25,062 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72465.0, 'intermediate_solutions': [{'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 48, 49, 102, 36, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 72867.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 4, 16, 2, 7, 3, 75, 86, 94, 92, 100, 101, 66, 17, 1], 'cur_cost': 73732.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [84, 78, 58, 70, 67, 81, 79, 47, 14, 15, 31, 41, 63, 83, 89, 104, 68, 90, 56, 52, 72, 55, 53, 103, 44, 26, 28, 30, 11, 8, 42, 23, 39, 6, 0, 32, 1, 25, 19, 27, 34, 36, 49, 102, 48, 69, 61, 80, 59, 50, 45, 54, 71, 97, 57, 74, 85, 82, 77, 95, 65, 98, 76, 60, 62, 51, 43, 20, 21, 46, 10, 13, 37, 29, 12, 24, 18, 33, 9, 40, 5, 35, 73, 64, 88, 93, 87, 91, 96, 99, 38, 22, 7, 2, 16, 4, 3, 75, 86, 94, 92, 100, 101, 66, 17], 'cur_cost': 72719.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22366.0, 'intermediate_solutions': [{'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 89, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 45, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25292.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 7, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 23531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 7, 16, 17, 25, 15, 18, 23, 26, 27, 22, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 24, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 2, 11, 8, 4, 3, 12, 13, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 107573.0, 'intermediate_solutions': [{'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 29, 20, 21, 28, 102, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21759.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 60, 59, 38, 37, 34, 33, 3, 4, 12, 13, 11, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 24540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 1, 10, 9, 6, 5, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 64, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 13, 12, 4, 3, 33, 34, 37, 38, 59, 60, 65, 86, 87, 93, 94, 99], 'cur_cost': 22244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 35,  77,  61,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56], dtype=int64), 'cur_cost': 121663.0, 'intermediate_solutions': [{'tour': array([ 69, 103,  95,  97,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128454.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 97,  69, 103,  95,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128868.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 47,  97,  69, 103,  95,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 130313.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 95,  97,  69, 103,  47,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 126381.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 95,  47,  97,  69, 103,  41,  48, 104,  73,  59,  65,  40, 100,
        22,  64, 101,  28,  36,  70,  12,  61,  23,   2,  42,  18,  67,
        86,  57,  34,  44,  27,  15,  60,  46,  35,  92,  30,  16,  93,
        54,  10,  74,  84,  81,  51,  11,  68,  56,  25,  88,  91,   7,
         0,  38,  75,  78,  87,  80,  76,  82,  49,   4,  58,  72,  31,
        24,  33,  79,   8,  77,   5,  39,   6,  63,  50,  66,  17,  37,
         3,  96,  99,  45,  55,  14,  83,  98,  43,  94,   9,  89,  19,
         1, 102,  85,  20,  13,  26,  90,  21,  53,  52,  29,  62,  32,
        71]), 'cur_cost': 128527.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71220.0, 'intermediate_solutions': [{'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 57, 68, 79, 88, 87, 58, 51, 17, 19, 53, 45, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68602.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 39, 61, 93, 98, 85, 62, 46, 49, 50, 29, 21, 40, 65, 52, 100, 75, 86, 72, 84, 66, 74, 63, 44, 1, 28, 12, 15, 9, 30, 23, 6, 5, 31, 20, 48, 55, 57, 53, 19, 22, 33, 36, 32, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 68772.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [97, 67, 69, 56, 41, 24, 25, 13, 18, 14, 54, 73, 45, 68, 79, 88, 87, 58, 51, 17, 19, 53, 57, 55, 48, 20, 31, 5, 6, 23, 30, 9, 15, 12, 28, 1, 44, 63, 74, 66, 32, 84, 72, 86, 75, 100, 52, 65, 40, 21, 29, 50, 49, 46, 62, 85, 98, 93, 61, 39, 22, 33, 36, 43, 27, 26, 38, 37, 11, 4, 47, 103, 35, 2, 0, 7, 3, 76, 104, 77, 82, 71, 91, 80, 94, 92, 90, 99, 101, 83, 96, 70, 64, 59, 89, 78, 60, 34, 16, 8, 102, 10, 42, 81, 95], 'cur_cost': 71106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106030.0, 'intermediate_solutions': [{'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 85, 14, 11, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 102, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 29056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 60, 59, 3, 4, 12, 13, 33, 34, 37, 38, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 98, 89, 88, 85, 84, 83, 82, 81, 77, 70, 66, 67, 71, 76, 78, 79, 75, 72, 68, 80, 74, 73, 69, 62, 61, 104, 58, 55, 54, 49, 47, 44, 39, 103, 40, 43, 46, 50, 53, 56, 57, 52, 51, 45, 42, 41, 36, 35, 32, 11, 14, 102, 20, 21, 28, 31, 30, 29, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 7, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25245.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 8, 10, 9, 6, 5, 1, 2, 7, 15, 17, 24, 25, 26, 23, 18, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 11, 32, 35, 36, 49, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 111179.0, 'intermediate_solutions': [{'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 70, 58, 69, 57, 25, 64, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 20, 86, 16, 10], 'cur_cost': 77847.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 61, 53, 63, 91, 83, 43, 9, 11, 48, 41, 28, 18, 35, 60, 78, 81, 89, 71, 67, 95, 75, 55, 54, 80, 79, 96, 97, 72, 90, 77, 101, 88, 62, 7, 3, 0, 27, 40, 56, 34, 1, 19, 38, 52, 30, 36, 22, 32, 20, 25, 57, 69, 58, 70, 47, 37, 24, 8, 103, 13, 23, 26, 49, 84, 82, 92, 73, 46, 45, 50, 76, 65, 104, 98, 66, 51, 102, 39, 31, 21, 44, 59, 12, 4, 5, 14, 33, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 72379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [42, 61, 53, 63, 91, 83, 43, 59, 44, 21, 31, 39, 102, 51, 66, 98, 104, 65, 76, 50, 45, 46, 73, 92, 82, 84, 49, 26, 23, 13, 103, 8, 24, 37, 47, 58, 69, 57, 25, 20, 32, 22, 36, 30, 52, 38, 19, 1, 34, 56, 40, 27, 0, 3, 7, 62, 88, 101, 77, 90, 72, 97, 96, 79, 80, 54, 55, 75, 95, 67, 71, 89, 81, 78, 60, 35, 18, 28, 41, 48, 11, 9, 12, 4, 5, 14, 33, 70, 17, 29, 6, 2, 15, 68, 74, 85, 100, 93, 99, 87, 94, 64, 86, 16, 10], 'cur_cost': 73833.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 85,  95,  84,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91], dtype=int64), 'cur_cost': 126135.0, 'intermediate_solutions': [{'tour': array([ 16,  12,  92,  73,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 126814.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 73,  16,  12,  92,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 129604.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 65,  73,  16,  12,  92, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 129567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 92,  73,  16,  12,  65, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 128807.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 92,  65,  73,  16,  12, 102,   5,  94,  38,  21,   3,   8,  74,
        25,  24,  28,  87,  19,  67, 104,  37, 101,  13,  80,  26,  58,
        91,  69,  93,  55,  98,  77,  53,  41,  51,  68,  33,  15,  39,
        95,  85,  20,   1,  90,  52,  32,  88,  29,  46,  18,   0,  54,
        42,  59,  71,  43,   9,  76,  50,  64,   7,  27,  89,  79,  36,
        17,  30,  48,  56,  75,  45,  63,  86,  62, 100,  81,   4,  14,
        78,  84,  47,  83,  44,  57,  10,  49,  70,  40,  34,  61,   6,
        11,  60,   2,  97,  31,  72,  35,  99, 103,  96,  23,  66,  82,
        22]), 'cur_cost': 127403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([101,  15,  10,  30,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39], dtype=int64), 'cur_cost': 126399.0, 'intermediate_solutions': [{'tour': array([ 73,  63,  82,   8,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 114533.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  8,  73,  63,  82,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 114215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 20,   8,  73,  63,  82,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 112259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 82,   8,  73,  63,  20,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 116983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 82,  20,   8,  73,  63,  59,  46,  12,  70, 101,  25,  21,  85,
        31,  16, 103,  62,  53,  89,  72,  15,  10,  24,  87,  80,  28,
        97,  86,   7,  45,   6,  33,  95,  92,  54,  42,  64,  79,  50,
        60,  94,  37,   4,  84,  17,  65,  98,  35,  26, 100,  96,  71,
        77,  55,  23,  34,  43,  47,  32,  90,  78,  66,  57,   0,  52,
         5,  13,   3,  30,  49,  18, 104,  22,  41,  38,  69,  67,  48,
       102,  11,   9,  39,  93,  51,  76,   1,  58,  29,  75,  74,  88,
        56,   2,  61,  91,  44,  27,  36,  81,  83,  68,  40,  99,  19,
        14]), 'cur_cost': 115088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20988.0, 'intermediate_solutions': [{'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 9, 7, 25, 12, 6, 1, 16, 35, 20, 56, 55, 67, 43, 21, 26, 96, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 32, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 77151.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [94, 73, 71, 77, 97, 96, 88, 78, 45, 48, 36, 30, 42, 64, 74, 65, 60, 87, 85, 68, 75, 104, 61, 15, 47, 0, 5, 24, 10, 8, 11, 22, 19, 32, 26, 21, 43, 67, 55, 56, 20, 35, 16, 1, 6, 12, 25, 7, 9, 3, 2, 102, 49, 29, 18, 31, 58, 82, 98, 62, 33, 41, 69, 46, 92, 100, 80, 72, 50, 39, 103, 27, 38, 59, 89, 84, 81, 70, 91, 66, 51, 17, 44, 54, 79, 53, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 70065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [94, 53, 79, 54, 44, 17, 51, 66, 91, 70, 81, 84, 89, 59, 38, 27, 103, 39, 50, 72, 80, 100, 92, 46, 69, 41, 33, 62, 98, 82, 58, 31, 18, 29, 49, 102, 2, 3, 7, 25, 12, 6, 1, 16, 35, 9, 20, 56, 55, 67, 43, 21, 26, 32, 19, 22, 11, 8, 10, 24, 5, 0, 47, 15, 61, 104, 75, 68, 85, 87, 60, 65, 74, 64, 42, 30, 36, 48, 45, 78, 88, 96, 97, 77, 71, 73, 83, 76, 86, 93, 99, 37, 52, 57, 63, 95, 40, 28, 23, 14, 13, 34, 4, 90, 101], 'cur_cost': 69697.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:25,063 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:25,063 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:25,071 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20988.000, 多样性=0.976
2025-08-05 09:52:25,071 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:25,071 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:25,071 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:25,073 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07326063842899731, 'best_improvement': -0.015433741351782864}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00838529348527191}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.017993189630810005, 'recent_improvements': [0.03390972726419383, -0.02162895925945776, -0.00207665199742618], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 14379, 'new_best_cost': 14379, 'quality_improvement': 0.0, 'old_diversity': 0.7492063492063492, 'new_diversity': 0.7492063492063492, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:25,074 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:25,074 - __main__ - INFO - lin105 开始进化第 5 代
2025-08-05 09:52:25,074 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:25,074 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:25,076 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20988.000, 多样性=0.976
2025-08-05 09:52:25,076 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:25,081 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-05 09:52:25,081 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:25,086 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.749
2025-08-05 09:52:25,088 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:25,088 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:25,088 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:25,088 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:25,178 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -15886.482, 聚类评分: 0.000, 覆盖率: 0.174, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:25,178 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:25,178 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:25,178 - visualization.landscape_visualizer - INFO - 设置当前实例名: lin105
2025-08-05 09:52:25,185 - visualization.landscape_visualizer - INFO - 插值约束: 232 个点被约束到最小值 14379.00
2025-08-05 09:52:25,316 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\landscape_lin105_iter_155_20250805_095225.html
2025-08-05 09:52:25,373 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_lin105\dashboard_lin105_iter_155_20250805_095225.html
2025-08-05 09:52:25,373 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 155
2025-08-05 09:52:25,373 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:25,373 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2847秒
2025-08-05 09:52:25,373 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -15886.482352941177, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 1738146645.4496887, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1743, 'fitness_entropy': 0.8661108318808242, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -15886.482)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.174)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358745.1788976, 'performance_metrics': {}}}
2025-08-05 09:52:25,374 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:25,374 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:25,374 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:25,374 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:25,375 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:25,375 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:25,376 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:25,376 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:25,376 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:25,376 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 09:52:25,376 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:25,376 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:25,377 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:25,377 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:25,377 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:25,377 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,603 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:25,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,605 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21316.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,605 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 8, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 15, 17, 24, 25, 16, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21316.0, 'intermediate_solutions': [{'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 13, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 56, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 73326.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 72, 62, 75, 64, 60, 11, 2, 35, 13, 27, 20, 18, 15, 3, 9, 6, 31, 48, 21, 42, 52, 80, 69, 51, 79, 73, 85, 61, 16, 5, 8, 4, 23, 104, 28, 41, 44, 71, 49, 82, 78, 55, 58, 59, 38, 57, 87, 65, 92, 63, 89, 81, 74, 70, 47, 46, 7, 34, 36, 103, 0, 26, 45, 17, 22, 40, 37, 56, 88, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72490.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 54, 29, 102, 39, 30, 10, 59, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 74553.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,605 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 21316.00)
2025-08-05 09:52:25,606 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:25,606 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:25,606 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,610 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:25,610 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,610 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,611 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20661.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,611 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 7, 10, 9, 6, 5, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20661.0, 'intermediate_solutions': [{'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 90, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 52, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27581.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 11, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26702.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 92, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,612 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 20661.00)
2025-08-05 09:52:25,612 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:25,612 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:25,612 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,615 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:25,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21267.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,617 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 16, 7, 6, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21267.0, 'intermediate_solutions': [{'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 93, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 72, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 108052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 91, 5, 25, 4, 49, 11, 28, 41, 84, 71, 43, 82, 78, 55, 44, 1, 52, 57, 60, 65, 92, 63, 31, 32, 74, 67, 18, 20, 7, 19, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 106982.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 33, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 108031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,617 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21267.00)
2025-08-05 09:52:25,617 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:25,617 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:25,618 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:25,619 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 130291.0
2025-08-05 09:52:25,629 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:25,630 - ExploitationExpert - INFO - res_population_costs: [14379, 14468, 14522, 14579, 14658, 14683, 14697.0]
2025-08-05 09:52:25,630 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:25,634 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:25,634 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 15, 17, 24, 25, 16, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21316.0}, {'tour': [0, 1, 7, 10, 9, 6, 5, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20661.0}, {'tour': [0, 16, 7, 6, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21267.0}, {'tour': array([ 86,  27,  11,  74,  32, 103,   4,  66,  36,  76,  84,  73,  57,
        20,  21,  26,  13,  94,  91,  82,  43,  70,  95,  53, 101,  52,
        16,  71,  48,  34,  88,  56,  59,   5,  65,  61,  58,  47,  45,
        30,  23,  75,  51,  64,  80,   7, 100,  15,  85,  14,  40,  67,
        54,  92,  33,  81,  93,  46,  83,  17,  25,  37, 104,  78,  72,
         0,  29,  77,  35,  31,  49,   9,  12,  98,  50,   2,  39,  10,
        24,  60,   3,  18,  87, 102,  55,  28,  63,  62,  68,   8,  38,
        97,  96,  22,  19,  89,  44,  69,  79,  41,  90,   1,  99,   6,
        42], dtype=int64), 'cur_cost': 130291.0}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71220.0}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106030.0}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 111179.0}, {'tour': [85, 95, 84, 7, 46, 22, 77, 81, 35, 66, 72, 70, 4, 92, 8, 54, 78, 87, 17, 43, 73, 64, 12, 101, 31, 97, 51, 20, 56, 75, 15, 6, 61, 24, 100, 71, 11, 29, 42, 40, 89, 62, 9, 3, 99, 104, 48, 76, 59, 55, 23, 28, 82, 16, 2, 27, 30, 88, 0, 65, 67, 52, 63, 26, 49, 102, 50, 79, 47, 83, 18, 60, 36, 53, 80, 1, 103, 98, 45, 34, 32, 25, 14, 5, 68, 69, 37, 44, 90, 13, 39, 33, 74, 10, 41, 93, 94, 19, 21, 57, 86, 38, 58, 96, 91], 'cur_cost': 126135.0}, {'tour': [101, 15, 10, 30, 34, 41, 71, 77, 79, 85, 14, 13, 3, 26, 75, 48, 73, 46, 72, 4, 90, 38, 63, 96, 12, 62, 45, 16, 23, 99, 19, 95, 53, 44, 82, 0, 7, 47, 18, 33, 9, 83, 54, 20, 103, 49, 6, 104, 17, 8, 92, 81, 29, 84, 86, 11, 100, 5, 52, 74, 43, 37, 80, 93, 68, 59, 55, 1, 42, 22, 2, 91, 64, 28, 88, 21, 87, 65, 89, 67, 97, 78, 70, 69, 58, 57, 76, 40, 98, 36, 60, 31, 35, 56, 102, 61, 32, 50, 51, 66, 25, 94, 27, 24, 39], 'cur_cost': 126399.0}, {'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20988.0}]
2025-08-05 09:52:25,635 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:25,635 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 401, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 401, 'cache_hits': 0, 'similarity_calculations': 2113, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:25,637 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 86,  27,  11,  74,  32, 103,   4,  66,  36,  76,  84,  73,  57,
        20,  21,  26,  13,  94,  91,  82,  43,  70,  95,  53, 101,  52,
        16,  71,  48,  34,  88,  56,  59,   5,  65,  61,  58,  47,  45,
        30,  23,  75,  51,  64,  80,   7, 100,  15,  85,  14,  40,  67,
        54,  92,  33,  81,  93,  46,  83,  17,  25,  37, 104,  78,  72,
         0,  29,  77,  35,  31,  49,   9,  12,  98,  50,   2,  39,  10,
        24,  60,   3,  18,  87, 102,  55,  28,  63,  62,  68,   8,  38,
        97,  96,  22,  19,  89,  44,  69,  79,  41,  90,   1,  99,   6,
        42], dtype=int64), 'cur_cost': 130291.0, 'intermediate_solutions': [{'tour': array([ 61,  77,  35,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 44,  61,  77,  35,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 78,  44,  61,  77,  35,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 35,  44,  61,  77,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 119930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 35,  78,  44,  61,  77,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:25,637 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 130291.00)
2025-08-05 09:52:25,637 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:25,637 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:25,637 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,640 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 105
2025-08-05 09:52:25,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,641 - ExplorationExpert - INFO - 探索路径生成完成，成本: 77983.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,641 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 29, 6, 9, 2, 30, 19, 20, 40, 102, 14, 31, 22, 34, 18, 7, 26, 17, 25, 24, 16, 15, 23, 32, 42, 45, 51, 52, 8, 71, 53, 50, 46, 43, 55, 44, 47, 49, 54, 69, 58, 104, 61, 62, 73, 74, 68, 78, 75, 79, 101, 76, 67, 66, 70, 77, 93, 82, 84, 85, 21, 89, 97, 92, 91, 90, 95, 27, 100, 37, 63, 38, 5, 39, 65, 72, 41, 3, 94, 98, 36, 99, 11, 87, 60, 33, 35, 0, 86, 57, 10, 28, 12, 13, 103, 83, 88, 81, 4, 59, 64, 80, 96, 56, 48], 'cur_cost': 77983.0, 'intermediate_solutions': [{'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 44, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 42, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 70814.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 68, 39, 42, 103, 28, 3, 4, 25, 41, 13, 36, 35, 22, 23, 34, 51, 52, 70, 64, 57, 49, 40, 61, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 70999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 66, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71407.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,642 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 77983.00)
2025-08-05 09:52:25,642 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:25,642 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:25,642 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,656 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:25,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 74572.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,657 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [44, 32, 31, 25, 10, 34, 17, 40, 49, 73, 89, 86, 92, 78, 84, 98, 61, 72, 50, 69, 62, 39, 14, 51, 67, 68, 64, 88, 59, 66, 46, 63, 45, 41, 18, 28, 5, 12, 33, 103, 43, 15, 8, 52, 80, 85, 71, 48, 30, 47, 104, 76, 55, 56, 22, 2, 21, 27, 26, 23, 3, 38, 24, 102, 11, 37, 75, 70, 83, 57, 16, 20, 54, 9, 53, 74, 95, 100, 96, 79, 81, 82, 87, 93, 91, 77, 65, 58, 60, 42, 29, 35, 6, 7, 19, 13, 36, 0, 90, 101, 97, 99, 94, 4, 1], 'cur_cost': 74572.0, 'intermediate_solutions': [{'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 74, 23, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 62, 67, 64, 60, 11, 2, 6, 20, 55, 15, 3, 76, 66, 21, 75, 72, 80, 69, 85, 91, 34, 8, 7, 104, 103, 36, 49, 71, 33, 59, 38, 63, 87, 77, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 22, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 107448.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,658 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 74572.00)
2025-08-05 09:52:25,658 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:25,658 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:25,658 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,674 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 105
2025-08-05 09:52:25,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,675 - ExplorationExpert - INFO - 探索路径生成完成，成本: 75861.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,675 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 6, 27, 42, 44, 18, 39, 40, 14, 4, 10, 5, 102, 35, 31, 19, 16, 21, 15, 9, 17, 33, 37, 30, 41, 65, 63, 75, 72, 92, 78, 101, 58, 32, 8, 0, 57, 47, 70, 71, 90, 82, 69, 55, 76, 89, 83, 62, 52, 54, 50, 67, 73, 84, 66, 88, 100, 87, 74, 95, 68, 53, 59, 48, 22, 36, 49, 24, 12, 1, 103, 28, 45, 79, 98, 94, 97, 56, 104, 46, 26, 11, 38, 64, 51, 61, 80, 86, 81, 77, 60, 85, 91, 43, 25, 7, 20, 29, 2, 34, 23, 13, 93, 99, 96], 'cur_cost': 75861.0, 'intermediate_solutions': [{'tour': [0, 32, 11, 97, 47, 62, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 6, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 115691.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 22, 50, 71, 2, 46, 59, 29, 37, 86, 5, 96, 84, 90, 99, 67, 98, 1, 42, 60, 88, 83, 82, 93, 27, 20, 77, 15, 3, 76, 78, 79, 48, 33, 101, 68, 80, 62, 69, 85, 61, 54, 7, 104, 28, 72, 43, 49, 53, 56, 44, 51, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 112108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 9, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 112544.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,675 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 75861.00)
2025-08-05 09:52:25,675 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 09:52:25,676 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:25,676 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:25,676 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 125470.0
2025-08-05 09:52:25,690 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:25,690 - ExploitationExpert - INFO - res_population_costs: [14379, 14468, 14522, 14579, 14658, 14683, 14697.0]
2025-08-05 09:52:25,691 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:25,695 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:25,695 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 15, 17, 24, 25, 16, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21316.0}, {'tour': [0, 1, 7, 10, 9, 6, 5, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20661.0}, {'tour': [0, 16, 7, 6, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21267.0}, {'tour': array([ 86,  27,  11,  74,  32, 103,   4,  66,  36,  76,  84,  73,  57,
        20,  21,  26,  13,  94,  91,  82,  43,  70,  95,  53, 101,  52,
        16,  71,  48,  34,  88,  56,  59,   5,  65,  61,  58,  47,  45,
        30,  23,  75,  51,  64,  80,   7, 100,  15,  85,  14,  40,  67,
        54,  92,  33,  81,  93,  46,  83,  17,  25,  37, 104,  78,  72,
         0,  29,  77,  35,  31,  49,   9,  12,  98,  50,   2,  39,  10,
        24,  60,   3,  18,  87, 102,  55,  28,  63,  62,  68,   8,  38,
        97,  96,  22,  19,  89,  44,  69,  79,  41,  90,   1,  99,   6,
        42], dtype=int64), 'cur_cost': 130291.0}, {'tour': [1, 29, 6, 9, 2, 30, 19, 20, 40, 102, 14, 31, 22, 34, 18, 7, 26, 17, 25, 24, 16, 15, 23, 32, 42, 45, 51, 52, 8, 71, 53, 50, 46, 43, 55, 44, 47, 49, 54, 69, 58, 104, 61, 62, 73, 74, 68, 78, 75, 79, 101, 76, 67, 66, 70, 77, 93, 82, 84, 85, 21, 89, 97, 92, 91, 90, 95, 27, 100, 37, 63, 38, 5, 39, 65, 72, 41, 3, 94, 98, 36, 99, 11, 87, 60, 33, 35, 0, 86, 57, 10, 28, 12, 13, 103, 83, 88, 81, 4, 59, 64, 80, 96, 56, 48], 'cur_cost': 77983.0}, {'tour': [44, 32, 31, 25, 10, 34, 17, 40, 49, 73, 89, 86, 92, 78, 84, 98, 61, 72, 50, 69, 62, 39, 14, 51, 67, 68, 64, 88, 59, 66, 46, 63, 45, 41, 18, 28, 5, 12, 33, 103, 43, 15, 8, 52, 80, 85, 71, 48, 30, 47, 104, 76, 55, 56, 22, 2, 21, 27, 26, 23, 3, 38, 24, 102, 11, 37, 75, 70, 83, 57, 16, 20, 54, 9, 53, 74, 95, 100, 96, 79, 81, 82, 87, 93, 91, 77, 65, 58, 60, 42, 29, 35, 6, 7, 19, 13, 36, 0, 90, 101, 97, 99, 94, 4, 1], 'cur_cost': 74572.0}, {'tour': [3, 6, 27, 42, 44, 18, 39, 40, 14, 4, 10, 5, 102, 35, 31, 19, 16, 21, 15, 9, 17, 33, 37, 30, 41, 65, 63, 75, 72, 92, 78, 101, 58, 32, 8, 0, 57, 47, 70, 71, 90, 82, 69, 55, 76, 89, 83, 62, 52, 54, 50, 67, 73, 84, 66, 88, 100, 87, 74, 95, 68, 53, 59, 48, 22, 36, 49, 24, 12, 1, 103, 28, 45, 79, 98, 94, 97, 56, 104, 46, 26, 11, 38, 64, 51, 61, 80, 86, 81, 77, 60, 85, 91, 43, 25, 7, 20, 29, 2, 34, 23, 13, 93, 99, 96], 'cur_cost': 75861.0}, {'tour': array([ 43,  64,  46,  63,  41,  36,  61,  39,  91,  42,  51,  86,   2,
        12,   7,  20,  96,  55,   3,  76,  35,   5,  15,  70,  85,  30,
         6,  88,  60,  34,  84,  83,  25,  58,  28,  14,  33,   0,  59,
        66,  19,   8,  50,  94,  54,  44,  90,  40,  10,  48,  92,  99,
        18,  81,  57,  11,  53,  78,  93,  26,   4,  82,  79,  75,  32,
        27,  87,   9,  98,  80,  52,  77,  69,  56,  24, 101,  97, 100,
        67,  38,  49, 104,  13,  65,  37,  21,  31,  89,  23,  95,  17,
        22,  45,  72, 102,  74,  73,  16,  29,  62,  71,  47,   1,  68,
       103], dtype=int64), 'cur_cost': 125470.0}, {'tour': [101, 15, 10, 30, 34, 41, 71, 77, 79, 85, 14, 13, 3, 26, 75, 48, 73, 46, 72, 4, 90, 38, 63, 96, 12, 62, 45, 16, 23, 99, 19, 95, 53, 44, 82, 0, 7, 47, 18, 33, 9, 83, 54, 20, 103, 49, 6, 104, 17, 8, 92, 81, 29, 84, 86, 11, 100, 5, 52, 74, 43, 37, 80, 93, 68, 59, 55, 1, 42, 22, 2, 91, 64, 28, 88, 21, 87, 65, 89, 67, 97, 78, 70, 69, 58, 57, 76, 40, 98, 36, 60, 31, 35, 56, 102, 61, 32, 50, 51, 66, 25, 94, 27, 24, 39], 'cur_cost': 126399.0}, {'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20988.0}]
2025-08-05 09:52:25,697 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:25,698 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 402, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 402, 'cache_hits': 0, 'similarity_calculations': 2124, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:25,699 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 43,  64,  46,  63,  41,  36,  61,  39,  91,  42,  51,  86,   2,
        12,   7,  20,  96,  55,   3,  76,  35,   5,  15,  70,  85,  30,
         6,  88,  60,  34,  84,  83,  25,  58,  28,  14,  33,   0,  59,
        66,  19,   8,  50,  94,  54,  44,  90,  40,  10,  48,  92,  99,
        18,  81,  57,  11,  53,  78,  93,  26,   4,  82,  79,  75,  32,
        27,  87,   9,  98,  80,  52,  77,  69,  56,  24, 101,  97, 100,
        67,  38,  49, 104,  13,  65,  37,  21,  31,  89,  23,  95,  17,
        22,  45,  72, 102,  74,  73,  16,  29,  62,  71,  47,   1,  68,
       103], dtype=int64), 'cur_cost': 125470.0, 'intermediate_solutions': [{'tour': array([ 84,  95,  85,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 126010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  7,  84,  95,  85,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 46,   7,  84,  95,  85,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 85,   7,  84,  95,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 85,  46,   7,  84,  95,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:25,699 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 125470.00)
2025-08-05 09:52:25,700 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:25,700 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:25,700 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:25,700 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 116521.0
2025-08-05 09:52:25,712 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:25,712 - ExploitationExpert - INFO - res_population_costs: [14379, 14468, 14522, 14579, 14658, 14683, 14697.0]
2025-08-05 09:52:25,712 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  99,  94,  89,  88,  98,  97,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  20,  21,  28, 102,  11,  19,
        22,  27,  29,  30,  31,  32,  26,  23,  18,  15,  16,  17,  24,
        25,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  79,  75,  72,  63,  66,  67,  70,
        71,  76,  78,  85,  83,  77,  81,  82,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14,  11,  15,  16,  17,  24,  25,
        26,  23,  18,  27,  22,  19, 102,  20,  21,  28,  29,  30,  31,
        32,  35,  36,  41,  40,  42,  45,  51,  52,  57,  56,  53,  50,
        46,  43, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  97,  98,  88,  89,  94,  99,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  68,  72,  75,  79,  71,  76,  78,  85,  92, 101,
       100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,
        63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,  43,  46,
        50,  53,  56,  61,  62,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  80,
        74,  73,  69,  62,  61,  68,  72,  75,  79,  71,  76,  78,  85,
        92, 101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,
        67,  66,  63, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,
        59,  60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,
        79,  75,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,  91,
        90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  68,  73,  74,
        80,  69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39,
       103,  43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,
        36,  35,  25,  24,  17,  16,  15,  18,  23,  26,  32,  27,  22,
        19,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-08-05 09:52:25,716 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:25,716 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 15, 17, 24, 25, 16, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21316.0}, {'tour': [0, 1, 7, 10, 9, 6, 5, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20661.0}, {'tour': [0, 16, 7, 6, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21267.0}, {'tour': array([ 86,  27,  11,  74,  32, 103,   4,  66,  36,  76,  84,  73,  57,
        20,  21,  26,  13,  94,  91,  82,  43,  70,  95,  53, 101,  52,
        16,  71,  48,  34,  88,  56,  59,   5,  65,  61,  58,  47,  45,
        30,  23,  75,  51,  64,  80,   7, 100,  15,  85,  14,  40,  67,
        54,  92,  33,  81,  93,  46,  83,  17,  25,  37, 104,  78,  72,
         0,  29,  77,  35,  31,  49,   9,  12,  98,  50,   2,  39,  10,
        24,  60,   3,  18,  87, 102,  55,  28,  63,  62,  68,   8,  38,
        97,  96,  22,  19,  89,  44,  69,  79,  41,  90,   1,  99,   6,
        42], dtype=int64), 'cur_cost': 130291.0}, {'tour': [1, 29, 6, 9, 2, 30, 19, 20, 40, 102, 14, 31, 22, 34, 18, 7, 26, 17, 25, 24, 16, 15, 23, 32, 42, 45, 51, 52, 8, 71, 53, 50, 46, 43, 55, 44, 47, 49, 54, 69, 58, 104, 61, 62, 73, 74, 68, 78, 75, 79, 101, 76, 67, 66, 70, 77, 93, 82, 84, 85, 21, 89, 97, 92, 91, 90, 95, 27, 100, 37, 63, 38, 5, 39, 65, 72, 41, 3, 94, 98, 36, 99, 11, 87, 60, 33, 35, 0, 86, 57, 10, 28, 12, 13, 103, 83, 88, 81, 4, 59, 64, 80, 96, 56, 48], 'cur_cost': 77983.0}, {'tour': [44, 32, 31, 25, 10, 34, 17, 40, 49, 73, 89, 86, 92, 78, 84, 98, 61, 72, 50, 69, 62, 39, 14, 51, 67, 68, 64, 88, 59, 66, 46, 63, 45, 41, 18, 28, 5, 12, 33, 103, 43, 15, 8, 52, 80, 85, 71, 48, 30, 47, 104, 76, 55, 56, 22, 2, 21, 27, 26, 23, 3, 38, 24, 102, 11, 37, 75, 70, 83, 57, 16, 20, 54, 9, 53, 74, 95, 100, 96, 79, 81, 82, 87, 93, 91, 77, 65, 58, 60, 42, 29, 35, 6, 7, 19, 13, 36, 0, 90, 101, 97, 99, 94, 4, 1], 'cur_cost': 74572.0}, {'tour': [3, 6, 27, 42, 44, 18, 39, 40, 14, 4, 10, 5, 102, 35, 31, 19, 16, 21, 15, 9, 17, 33, 37, 30, 41, 65, 63, 75, 72, 92, 78, 101, 58, 32, 8, 0, 57, 47, 70, 71, 90, 82, 69, 55, 76, 89, 83, 62, 52, 54, 50, 67, 73, 84, 66, 88, 100, 87, 74, 95, 68, 53, 59, 48, 22, 36, 49, 24, 12, 1, 103, 28, 45, 79, 98, 94, 97, 56, 104, 46, 26, 11, 38, 64, 51, 61, 80, 86, 81, 77, 60, 85, 91, 43, 25, 7, 20, 29, 2, 34, 23, 13, 93, 99, 96], 'cur_cost': 75861.0}, {'tour': array([ 43,  64,  46,  63,  41,  36,  61,  39,  91,  42,  51,  86,   2,
        12,   7,  20,  96,  55,   3,  76,  35,   5,  15,  70,  85,  30,
         6,  88,  60,  34,  84,  83,  25,  58,  28,  14,  33,   0,  59,
        66,  19,   8,  50,  94,  54,  44,  90,  40,  10,  48,  92,  99,
        18,  81,  57,  11,  53,  78,  93,  26,   4,  82,  79,  75,  32,
        27,  87,   9,  98,  80,  52,  77,  69,  56,  24, 101,  97, 100,
        67,  38,  49, 104,  13,  65,  37,  21,  31,  89,  23,  95,  17,
        22,  45,  72, 102,  74,  73,  16,  29,  62,  71,  47,   1,  68,
       103], dtype=int64), 'cur_cost': 125470.0}, {'tour': array([ 89,  42,  86,  33,   3,  75,   8,  41,  21,  27,   5,  14,  25,
        55,  24,  96,  36,  62, 100,  30,  64,  67,  69,  32,  48,  81,
        72,   7,   9,   6,  59,  97,  76,  22,  98,  40,  26,  45,  20,
        79,  47,   2,  46,  77,  93,  66,  58,  84,  99,  78,  74,  16,
        54,  39, 102,  82,  70,  50,  31,  63,  15,  71,  43,  37,  18,
        11,  65,  19,  28,  10,  57,  29,  13,   0,  61,  60,  92,  90,
        17, 104,  95,  87,   1,  85,  91,  80,  83, 101,  53,  56,  51,
       103,  68,  73,  44,  38,  49,  35,  52,  94,  23,  12,   4,  88,
        34], dtype=int64), 'cur_cost': 116521.0}, {'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20988.0}]
2025-08-05 09:52:25,718 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:25,718 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 403, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 403, 'cache_hits': 0, 'similarity_calculations': 2136, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:25,719 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 89,  42,  86,  33,   3,  75,   8,  41,  21,  27,   5,  14,  25,
        55,  24,  96,  36,  62, 100,  30,  64,  67,  69,  32,  48,  81,
        72,   7,   9,   6,  59,  97,  76,  22,  98,  40,  26,  45,  20,
        79,  47,   2,  46,  77,  93,  66,  58,  84,  99,  78,  74,  16,
        54,  39, 102,  82,  70,  50,  31,  63,  15,  71,  43,  37,  18,
        11,  65,  19,  28,  10,  57,  29,  13,   0,  61,  60,  92,  90,
        17, 104,  95,  87,   1,  85,  91,  80,  83, 101,  53,  56,  51,
       103,  68,  73,  44,  38,  49,  35,  52,  94,  23,  12,   4,  88,
        34], dtype=int64), 'cur_cost': 116521.0, 'intermediate_solutions': [{'tour': array([ 10,  15, 101,  30,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 127320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 30,  10,  15, 101,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 34,  30,  10,  15, 101,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([101,  30,  10,  15,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 125756.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([101,  34,  30,  10,  15,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:25,720 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 116521.00)
2025-08-05 09:52:25,720 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:25,720 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:25,720 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:25,724 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 105
2025-08-05 09:52:25,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:25,725 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21848.0, 路径长度: 105, 收集中间解: 3
2025-08-05 09:52:25,726 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 8, 2, 13, 12, 4, 3, 7, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 34, 33, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21848.0, 'intermediate_solutions': [{'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 89, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 72, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22313.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 38, 37, 34, 33, 12, 3, 4, 8, 7, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 13, 17, 24, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 25, 93, 94, 99], 'cur_cost': 24790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:25,727 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 21848.00)
2025-08-05 09:52:25,727 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:25,727 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:25,730 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 10, 9, 6, 1, 2, 7, 15, 17, 24, 25, 16, 18, 23, 26, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 38, 37, 34, 33, 13, 12, 4, 3, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21316.0, 'intermediate_solutions': [{'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 88, 13, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 59, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 56, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 73326.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 54, 29, 102, 39, 30, 10, 32, 53, 68, 72, 62, 75, 64, 60, 11, 2, 35, 13, 27, 20, 18, 15, 3, 9, 6, 31, 48, 21, 42, 52, 80, 69, 51, 79, 73, 85, 61, 16, 5, 8, 4, 23, 104, 28, 41, 44, 71, 49, 82, 78, 55, 58, 59, 38, 57, 87, 65, 92, 63, 89, 81, 74, 70, 47, 46, 7, 34, 36, 103, 0, 26, 45, 17, 22, 40, 37, 56, 88, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 72490.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 54, 29, 102, 39, 30, 10, 59, 32, 53, 68, 88, 56, 37, 40, 22, 17, 45, 26, 0, 103, 36, 34, 7, 46, 47, 70, 74, 81, 89, 63, 92, 65, 87, 57, 38, 58, 55, 78, 82, 49, 71, 44, 41, 28, 104, 23, 4, 8, 5, 16, 61, 85, 73, 79, 51, 69, 80, 52, 42, 21, 48, 31, 6, 9, 3, 15, 18, 20, 27, 13, 35, 2, 11, 60, 64, 75, 62, 72, 67, 91, 66, 84, 100, 96, 99, 98, 101, 94, 77, 86, 97, 93, 43, 19, 50, 33, 14, 1, 12, 24, 25, 76, 90, 95], 'cur_cost': 74553.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 10, 9, 6, 5, 2, 11, 19, 22, 27, 29, 30, 31, 28, 21, 20, 102, 14, 18, 23, 26, 25, 24, 17, 15, 16, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 20661.0, 'intermediate_solutions': [{'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 90, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 52, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 27581.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 11, 14, 48, 63, 101, 100, 96, 95, 90, 91, 92, 97, 4, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 26702.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 3, 10, 9, 6, 5, 1, 2, 7, 8, 16, 17, 24, 25, 15, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 91, 90, 95, 96, 100, 101, 63, 48, 14, 11, 4, 12, 33, 34, 92, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 25977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 7, 6, 5, 1, 9, 10, 14, 102, 20, 21, 28, 29, 30, 31, 27, 22, 19, 18, 23, 26, 25, 24, 17, 15, 11, 2, 8, 4, 3, 12, 13, 33, 34, 37, 38, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 32, 35, 36, 60, 59, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21267.0, 'intermediate_solutions': [{'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 93, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 72, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 108052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 91, 5, 25, 4, 49, 11, 28, 41, 84, 71, 43, 82, 78, 55, 44, 1, 52, 57, 60, 65, 92, 63, 31, 32, 74, 67, 18, 20, 7, 19, 21, 62, 69, 79, 51, 80, 68, 72, 75, 33, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 106982.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [83, 54, 3, 97, 47, 30, 48, 104, 73, 8, 16, 56, 100, 40, 22, 15, 45, 23, 26, 103, 36, 19, 7, 33, 20, 18, 67, 74, 32, 31, 63, 92, 65, 60, 57, 52, 1, 44, 55, 78, 82, 43, 71, 84, 41, 28, 11, 49, 4, 25, 5, 91, 21, 62, 69, 79, 51, 80, 68, 72, 75, 27, 76, 6, 9, 66, 70, 77, 81, 95, 13, 35, 2, 88, 50, 64, 17, 37, 102, 90, 99, 39, 93, 101, 96, 98, 58, 94, 14, 12, 86, 59, 10, 85, 89, 38, 46, 34, 24, 53, 61, 29, 42, 0, 87], 'cur_cost': 108031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 86,  27,  11,  74,  32, 103,   4,  66,  36,  76,  84,  73,  57,
        20,  21,  26,  13,  94,  91,  82,  43,  70,  95,  53, 101,  52,
        16,  71,  48,  34,  88,  56,  59,   5,  65,  61,  58,  47,  45,
        30,  23,  75,  51,  64,  80,   7, 100,  15,  85,  14,  40,  67,
        54,  92,  33,  81,  93,  46,  83,  17,  25,  37, 104,  78,  72,
         0,  29,  77,  35,  31,  49,   9,  12,  98,  50,   2,  39,  10,
        24,  60,   3,  18,  87, 102,  55,  28,  63,  62,  68,   8,  38,
        97,  96,  22,  19,  89,  44,  69,  79,  41,  90,   1,  99,   6,
        42], dtype=int64), 'cur_cost': 130291.0, 'intermediate_solutions': [{'tour': array([ 61,  77,  35,  44,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 44,  61,  77,  35,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 78,  44,  61,  77,  35,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 35,  44,  61,  77,  78,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 119930.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 35,  78,  44,  61,  77,  50,  28,  57, 100,  46,  55, 101,   0,
        23,  54,  98,   6, 102,  31,  11,  17,  97,  20,  34,  75,  60,
        66,  64,  24,  89,   9,  52,  96,  91,  92,  37,  45,  30,  32,
        41,  67,  94,  25,  81,  53,  48,  74,  47,  95,   1,  12,  65,
        15,  71,  43,  14,  36,  18,  73,  21,  70,  69,   2,  86,  27,
        29,  39,  26,  51,  22,  13,  90,  87,  84,  83,   4,  88,  58,
        63,  79,   3,  80,  99,  93,  38,  10,  19,  72,  76,  82,  40,
        33,   5,  62,  68,   8,   7, 103,  16,  42, 104,  85,  49,  59,
        56]), 'cur_cost': 121665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 29, 6, 9, 2, 30, 19, 20, 40, 102, 14, 31, 22, 34, 18, 7, 26, 17, 25, 24, 16, 15, 23, 32, 42, 45, 51, 52, 8, 71, 53, 50, 46, 43, 55, 44, 47, 49, 54, 69, 58, 104, 61, 62, 73, 74, 68, 78, 75, 79, 101, 76, 67, 66, 70, 77, 93, 82, 84, 85, 21, 89, 97, 92, 91, 90, 95, 27, 100, 37, 63, 38, 5, 39, 65, 72, 41, 3, 94, 98, 36, 99, 11, 87, 60, 33, 35, 0, 86, 57, 10, 28, 12, 13, 103, 83, 88, 81, 4, 59, 64, 80, 96, 56, 48], 'cur_cost': 77983.0, 'intermediate_solutions': [{'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 44, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 42, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 70814.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 72, 68, 39, 42, 103, 28, 3, 4, 25, 41, 13, 36, 35, 22, 23, 34, 51, 52, 70, 64, 57, 49, 40, 61, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 66, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 70999.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 32, 11, 2, 45, 53, 67, 104, 83, 66, 72, 61, 40, 49, 57, 64, 70, 52, 51, 34, 23, 22, 35, 36, 13, 41, 25, 4, 3, 28, 103, 42, 39, 68, 63, 56, 31, 48, 75, 71, 85, 89, 84, 97, 94, 87, 55, 19, 7, 47, 50, 69, 93, 58, 76, 65, 38, 73, 95, 60, 78, 54, 77, 62, 43, 17, 24, 33, 26, 20, 6, 1, 21, 18, 102, 8, 16, 27, 14, 9, 59, 81, 80, 96, 90, 100, 79, 86, 37, 5, 0, 44, 46, 15, 10, 82, 92, 98, 74, 99, 101, 88, 91, 30, 29], 'cur_cost': 71407.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [44, 32, 31, 25, 10, 34, 17, 40, 49, 73, 89, 86, 92, 78, 84, 98, 61, 72, 50, 69, 62, 39, 14, 51, 67, 68, 64, 88, 59, 66, 46, 63, 45, 41, 18, 28, 5, 12, 33, 103, 43, 15, 8, 52, 80, 85, 71, 48, 30, 47, 104, 76, 55, 56, 22, 2, 21, 27, 26, 23, 3, 38, 24, 102, 11, 37, 75, 70, 83, 57, 16, 20, 54, 9, 53, 74, 95, 100, 96, 79, 81, 82, 87, 93, 91, 77, 65, 58, 60, 42, 29, 35, 6, 7, 19, 13, 36, 0, 90, 101, 97, 99, 94, 4, 1], 'cur_cost': 74572.0, 'intermediate_solutions': [{'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 74, 23, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106149.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 45, 35, 102, 28, 47, 70, 4, 81, 62, 67, 64, 60, 11, 2, 6, 20, 55, 15, 3, 76, 66, 21, 75, 72, 80, 69, 85, 91, 34, 8, 7, 104, 103, 36, 49, 71, 33, 59, 38, 63, 87, 77, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 22, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 106315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 13, 29, 39, 53, 48, 32, 73, 68, 88, 16, 37, 17, 52, 26, 0, 50, 22, 45, 35, 102, 28, 47, 70, 4, 81, 77, 87, 63, 38, 59, 33, 71, 49, 36, 103, 104, 7, 8, 34, 91, 85, 69, 80, 72, 75, 21, 66, 76, 3, 15, 55, 20, 6, 2, 11, 60, 64, 67, 62, 78, 99, 83, 84, 101, 10, 98, 40, 96, 97, 56, 93, 61, 31, 95, 46, 42, 1, 92, 100, 57, 5, 51, 58, 27, 90, 23, 74, 79, 44, 14, 65, 25, 19, 30, 94, 9, 18, 41, 89, 82, 86, 54, 24, 43], 'cur_cost': 107448.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 27, 42, 44, 18, 39, 40, 14, 4, 10, 5, 102, 35, 31, 19, 16, 21, 15, 9, 17, 33, 37, 30, 41, 65, 63, 75, 72, 92, 78, 101, 58, 32, 8, 0, 57, 47, 70, 71, 90, 82, 69, 55, 76, 89, 83, 62, 52, 54, 50, 67, 73, 84, 66, 88, 100, 87, 74, 95, 68, 53, 59, 48, 22, 36, 49, 24, 12, 1, 103, 28, 45, 79, 98, 94, 97, 56, 104, 46, 26, 11, 38, 64, 51, 61, 80, 86, 81, 77, 60, 85, 91, 43, 25, 7, 20, 29, 2, 34, 23, 13, 93, 99, 96], 'cur_cost': 75861.0, 'intermediate_solutions': [{'tour': [0, 32, 11, 97, 47, 62, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 6, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 115691.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 22, 50, 71, 2, 46, 59, 29, 37, 86, 5, 96, 84, 90, 99, 67, 98, 1, 42, 60, 88, 83, 82, 93, 27, 20, 77, 15, 3, 76, 78, 79, 48, 33, 101, 68, 80, 62, 69, 85, 61, 54, 7, 104, 28, 72, 43, 49, 53, 56, 44, 51, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 9, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 112108.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 32, 11, 97, 47, 6, 10, 73, 8, 16, 100, 24, 25, 17, 18, 23, 45, 34, 36, 4, 70, 81, 89, 103, 87, 51, 44, 56, 53, 49, 43, 72, 28, 104, 7, 54, 61, 85, 69, 62, 80, 68, 101, 33, 48, 79, 78, 76, 3, 15, 77, 20, 27, 93, 9, 82, 83, 88, 60, 42, 1, 98, 67, 99, 90, 84, 96, 5, 86, 37, 29, 59, 46, 2, 71, 50, 22, 35, 64, 65, 13, 41, 58, 39, 26, 102, 12, 19, 63, 31, 21, 57, 38, 95, 52, 55, 91, 94, 14, 74, 66, 75, 40, 92, 30], 'cur_cost': 112544.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 43,  64,  46,  63,  41,  36,  61,  39,  91,  42,  51,  86,   2,
        12,   7,  20,  96,  55,   3,  76,  35,   5,  15,  70,  85,  30,
         6,  88,  60,  34,  84,  83,  25,  58,  28,  14,  33,   0,  59,
        66,  19,   8,  50,  94,  54,  44,  90,  40,  10,  48,  92,  99,
        18,  81,  57,  11,  53,  78,  93,  26,   4,  82,  79,  75,  32,
        27,  87,   9,  98,  80,  52,  77,  69,  56,  24, 101,  97, 100,
        67,  38,  49, 104,  13,  65,  37,  21,  31,  89,  23,  95,  17,
        22,  45,  72, 102,  74,  73,  16,  29,  62,  71,  47,   1,  68,
       103], dtype=int64), 'cur_cost': 125470.0, 'intermediate_solutions': [{'tour': array([ 84,  95,  85,   7,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 126010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([  7,  84,  95,  85,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 46,   7,  84,  95,  85,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 85,   7,  84,  95,  46,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 85,  46,   7,  84,  95,  22,  77,  81,  35,  66,  72,  70,   4,
        92,   8,  54,  78,  87,  17,  43,  73,  64,  12, 101,  31,  97,
        51,  20,  56,  75,  15,   6,  61,  24, 100,  71,  11,  29,  42,
        40,  89,  62,   9,   3,  99, 104,  48,  76,  59,  55,  23,  28,
        82,  16,   2,  27,  30,  88,   0,  65,  67,  52,  63,  26,  49,
       102,  50,  79,  47,  83,  18,  60,  36,  53,  80,   1, 103,  98,
        45,  34,  32,  25,  14,   5,  68,  69,  37,  44,  90,  13,  39,
        33,  74,  10,  41,  93,  94,  19,  21,  57,  86,  38,  58,  96,
        91]), 'cur_cost': 128177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 89,  42,  86,  33,   3,  75,   8,  41,  21,  27,   5,  14,  25,
        55,  24,  96,  36,  62, 100,  30,  64,  67,  69,  32,  48,  81,
        72,   7,   9,   6,  59,  97,  76,  22,  98,  40,  26,  45,  20,
        79,  47,   2,  46,  77,  93,  66,  58,  84,  99,  78,  74,  16,
        54,  39, 102,  82,  70,  50,  31,  63,  15,  71,  43,  37,  18,
        11,  65,  19,  28,  10,  57,  29,  13,   0,  61,  60,  92,  90,
        17, 104,  95,  87,   1,  85,  91,  80,  83, 101,  53,  56,  51,
       103,  68,  73,  44,  38,  49,  35,  52,  94,  23,  12,   4,  88,
        34], dtype=int64), 'cur_cost': 116521.0, 'intermediate_solutions': [{'tour': array([ 10,  15, 101,  30,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 127320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 30,  10,  15, 101,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 34,  30,  10,  15, 101,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([101,  30,  10,  15,  34,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 125756.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([101,  34,  30,  10,  15,  41,  71,  77,  79,  85,  14,  13,   3,
        26,  75,  48,  73,  46,  72,   4,  90,  38,  63,  96,  12,  62,
        45,  16,  23,  99,  19,  95,  53,  44,  82,   0,   7,  47,  18,
        33,   9,  83,  54,  20, 103,  49,   6, 104,  17,   8,  92,  81,
        29,  84,  86,  11, 100,   5,  52,  74,  43,  37,  80,  93,  68,
        59,  55,   1,  42,  22,   2,  91,  64,  28,  88,  21,  87,  65,
        89,  67,  97,  78,  70,  69,  58,  57,  76,  40,  98,  36,  60,
        31,  35,  56, 102,  61,  32,  50,  51,  66,  25,  94,  27,  24,
        39]), 'cur_cost': 126397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 2, 13, 12, 4, 3, 7, 15, 17, 24, 25, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 34, 33, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 21848.0, 'intermediate_solutions': [{'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 89, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 72, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22313.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 13, 17, 24, 25, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 38, 37, 34, 33, 12, 3, 4, 8, 7, 59, 60, 64, 65, 86, 87, 93, 94, 99], 'cur_cost': 22903.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 13, 17, 24, 15, 16, 18, 23, 26, 27, 22, 19, 102, 20, 21, 28, 29, 30, 31, 32, 35, 36, 41, 42, 45, 51, 52, 57, 56, 53, 50, 46, 43, 40, 103, 39, 44, 47, 49, 54, 55, 58, 104, 61, 62, 69, 73, 74, 80, 68, 72, 75, 79, 78, 76, 71, 67, 66, 70, 77, 81, 82, 83, 84, 85, 88, 89, 98, 97, 92, 91, 90, 95, 96, 100, 101, 63, 48, 14, 10, 9, 6, 5, 1, 11, 7, 8, 4, 3, 12, 33, 34, 37, 38, 59, 60, 64, 65, 86, 87, 25, 93, 94, 99], 'cur_cost': 24790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:25,731 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:25,731 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:25,738 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=20661.000, 多样性=0.966
2025-08-05 09:52:25,738 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:25,738 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:25,738 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:25,739 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07025844276135623, 'best_improvement': 0.015580331618067466}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.010841283607979297}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.025815839584769778, 'recent_improvements': [-0.02162895925945776, -0.00207665199742618, -0.07326063842899731], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 14379, 'new_best_cost': 14379, 'quality_improvement': 0.0, 'old_diversity': 0.7492063492063492, 'new_diversity': 0.7492063492063492, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:25,740 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:25,745 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\lin105_solution.json
2025-08-05 09:52:25,745 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\lin105_20250805_095225.solution
2025-08-05 09:52:25,746 - __main__ - INFO - 实例执行完成 - 运行时间: 2.23s, 最佳成本: 14379
2025-08-05 09:52:25,746 - __main__ - INFO - 实例 lin105 处理完成
