# -*- coding: utf-8 -*-
"""
景观分析专家模块

包含LandscapeExpert类，负责整合其他专家的分析结果，生成综合景观分析。
现已完全替换为算法化实现，去除LLM依赖。
"""

import time
import logging
import numpy as np
from typing import Dict, List, Any, Optional

from experts.base.expert_base import ExpertBase
from core.algorithms.fitness_landscape_analyzer import FitnessLandscapeAnalyzer
from core.algorithms.incremental_updater import IncrementalUpdater


class LandscapeExpert(ExpertBase):
    """
    景观分析专家 - 算法化实现版本

    完全替换原有的LLM依赖实现，使用纯算法进行适应度景观分析。
    保持与原有接口的完全兼容性。
    """

    def __init__(self, interface_llm=None, config: Optional[Dict[str, Any]] = None):
        """
        初始化景观分析专家

        参数:
            interface_llm: LLM接口（已废弃，保留用于兼容性）
            config: 配置参数
        """
        super().__init__()

        # 忽略LLM接口参数，保持兼容性
        if interface_llm is not None:
            self.logger.info("注意：LandscapeExpert已升级为算法化实现，不再使用LLM接口")

        # 初始化配置
        self.config = config or {}

        # 初始化核心分析器
        analyzer_config = {
            'window_size': self.config.get('window_size', 50),
            'grid_size': self.config.get('grid_size', 20),
            'k_neighbors': self.config.get('k_neighbors', 5),
            'clustering_eps': self.config.get('clustering_eps', 0.5),
            'clustering_min_samples': self.config.get('clustering_min_samples', 3),
            'cache_size': self.config.get('cache_size', 1000)
        }
        self.analyzer = FitnessLandscapeAnalyzer(config=analyzer_config)

        # 增量更新器已经在FitnessLandscapeAnalyzer中初始化了
        # 这里保留引用以便兼容
        self.updater = self.analyzer.incremental_updater

        # 历史数据存储
        self.fitness_history = []
        self.analysis_cache = {}

        # 初始化可视化器（如果启用）
        self.visualizer = None
        viz_config = self.config.get('visualization_config', {})
        if viz_config.get('enabled', False):
            try:
                from visualization.landscape_visualizer import LandscapeVisualizer
                self.visualizer = LandscapeVisualizer(viz_config)
                self.logger.info("可视化器初始化成功")
            except ImportError as e:
                self.logger.warning(f"可视化器初始化失败，将跳过可视化功能: {e}")
            except Exception as e:
                self.logger.error(f"可视化器初始化错误: {e}")

        self.logger.info("算法化景观分析专家初始化完成")

    def analyze(self, stats_report, path_report, elite_report, iteration=0, total_iterations=10, history_data=None):
        """
        整合分析结果，生成综合景观分析

        参数:
            stats_report: 统计分析报告
            path_report: 路径分析报告
            elite_report: 精英解分析报告
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            history_data: 历史数据

        返回:
            景观分析报告（格式与原LandscapeExpert一致）
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始算法化景观分析 (迭代 {iteration}/{total_iterations})")

            # 提取种群和适应度数据，传入真实的种群数据
            populations, fitness_values = self._extract_population_data(
                stats_report, path_report, elite_report,
                populations=getattr(self, '_current_populations', None),
                res_populations=getattr(self, '_current_res_populations', None)
            )

            if not populations or not fitness_values:
                return self._get_default_landscape_report(iteration, total_iterations)

            # 更新历史数据
            self._update_history(fitness_values, iteration)

            # 执行景观分析
            analysis_results = self.analyzer.analyze_landscape(
                populations=populations,
                fitness_values=fitness_values,
                fitness_history=self.fitness_history,
                current_iteration=iteration
            )

            # 转换为兼容格式
            landscape_report = self._convert_to_landscape_report(
                analysis_results, iteration, total_iterations,
                stats_report, path_report, elite_report
            )

            # 更新可视化（如果启用）
            if self.visualizer:
                try:
                    self.logger.info(f"开始更新可视化 (迭代 {iteration})")

                    # 提取精英解数据
                    elite_solutions = self._extract_elite_solutions(elite_report)

                    # 提取实例名（如果可用）
                    instance_name = self._extract_instance_name(stats_report, path_report, elite_report)

                    # 设置可视化器的当前实例名
                    if instance_name and instance_name != "unknown_instance":
                        self.visualizer.set_current_instance(instance_name)

                    landscape_fig, dashboard_fig = self.visualizer.update_visualization(
                        populations, fitness_values, analysis_results,
                        elite_solutions, instance_name
                    )
                    self.logger.info(f"可视化已更新 (迭代 {iteration})")
                except Exception as e:
                    self.logger.warning(f"可视化更新失败: {e}")
                    import traceback
                    self.logger.warning(f"可视化更新错误详情: {traceback.format_exc()}")
            else:
                self.logger.warning(f"可视化器未初始化，跳过可视化更新")

            # 记录性能
            execution_time = time.time() - start_time
            self.logger.info(f"算法化景观分析完成，用时: {execution_time:.4f}秒")

            return landscape_report

        except Exception as e:
            self.logger.error(f"景观分析失败: {e}")
            return self._handle_analysis_failure(
                stats_report, path_report, elite_report,
                iteration, total_iterations, e
            )

    def generate_report(self, analysis_result):
        """直接返回分析结果（保持兼容性）"""
        return analysis_result

    def update_visualization(self, populations=None, fitness_values=None, analysis_results=None):
        """
        更新可视化显示

        参数:
            populations: 种群数据
            fitness_values: 适应度值
            analysis_results: 分析结果
        """
        if self.visualizer:
            try:
                if populations is None or fitness_values is None or analysis_results is None:
                    self.logger.warning("可视化更新需要完整的数据")
                    return False

                self.visualizer.update_visualization(populations, fitness_values, analysis_results)
                self.logger.info("可视化更新成功")
                return True
            except Exception as e:
                self.logger.error(f"可视化更新失败: {e}")
                return False
        else:
            self.logger.warning("可视化器未初始化")
            return False

    def start_visualization_server(self, analysis_results=None, port=None):
        """
        启动可视化Web服务器

        参数:
            analysis_results: 分析结果
            port: 端口号
        """
        if self.visualizer:
            try:
                if analysis_results is None:
                    analysis_results = {}

                if port is None:
                    port = self.config.get('visualization_config', {}).get('port', 8050)

                self.logger.info(f"启动可视化服务器，端口: {port}")
                self.visualizer.start_dash_app(analysis_results, port)
                return True
            except Exception as e:
                self.logger.error(f"启动可视化服务器失败: {e}")
                return False
        else:
            self.logger.warning("可视化器未初始化")
            return False

    def _extract_population_data(self, stats_report, path_report, elite_report, populations=None, res_populations=None):
        """从报告中提取种群和适应度数据

        参数:
            stats_report: 统计报告
            path_report: 路径报告
            elite_report: 精英报告
            populations: 当前种群数据（直接传入）
            res_populations: 精英解种群数据（直接传入）
        """
        try:
            extracted_populations = []
            fitness_values = []

            self.logger.debug(f"开始提取种群数据...")
            self.logger.debug(f"统计报告类型: {type(stats_report)}")
            self.logger.debug(f"路径报告类型: {type(path_report)}")
            self.logger.debug(f"精英报告类型: {type(elite_report)}")

            # 优先使用直接传入的真实种群数据
            if populations is not None:
                self.logger.info(f"使用直接传入的种群数据: {len(populations)}个个体")
                for individual in populations:
                    if isinstance(individual, dict) and 'tour' in individual and 'cur_cost' in individual:
                        # 统一转换为Python列表格式
                        tour = individual['tour']
                        if hasattr(tour, 'tolist'):  # numpy数组
                            tour = tour.tolist()
                        extracted_populations.append(tour)
                        fitness_values.append(float(individual['cur_cost']))

            # 添加精英解数据
            if res_populations is not None:
                self.logger.info(f"添加精英解数据: {len(res_populations)}个精英解")
                for elite in res_populations:
                    if isinstance(elite, dict) and 'tour' in elite and 'cur_cost' in elite:
                        # 统一转换为Python列表格式
                        tour = elite['tour']
                        if hasattr(tour, 'tolist'):  # numpy数组
                            tour = tour.tolist()
                        extracted_populations.append(tour)
                        fitness_values.append(float(elite['cur_cost']))

            # 如果直接数据不可用，回退到报告数据提取
            if not extracted_populations:
                self.logger.warning("直接种群数据不可用，使用报告数据生成模拟数据")

                # 尝试从统计报告中提取成本信息
                if stats_report and isinstance(stats_report, dict):
                    cost_stats = stats_report.get('cost_stats', {})
                    if cost_stats:
                        # 如果有成本统计，我们可以生成模拟的种群数据用于可视化
                        min_cost = cost_stats.get('min', 100)
                        max_cost = cost_stats.get('max', 1000)
                        mean_cost = cost_stats.get('mean', 500)
                        std_cost = cost_stats.get('std', 100)
                        pop_size = stats_report.get('population_size', 5)

                        # 生成模拟的适应度值
                        import numpy as np
                        fitness_values = np.random.normal(mean_cost, std_cost, pop_size).tolist()
                        # 确保最小值和最大值
                        if len(fitness_values) > 0:
                            fitness_values[0] = min_cost
                            if len(fitness_values) > 1:
                                fitness_values[1] = max_cost

                        # 生成对应的模拟路径数据
                        for i in range(pop_size):
                            # 生成一个简单的路径（城市数量基于问题规模）
                            city_count = 9  # 默认城市数量，可以从distance_matrix推断
                            path = list(range(city_count))
                            np.random.shuffle(path)
                            extracted_populations.append(path)

                        self.logger.info(f"从统计报告生成模拟数据: {len(extracted_populations)}个路径, {len(fitness_values)}个适应度值")

                # 从路径报告中提取边信息（用于增强可视化）
                if path_report and isinstance(path_report, dict):
                    common_edges = path_report.get('common_edges', [])
                    if common_edges and not extracted_populations:
                        # 如果有公共边信息但没有种群数据，生成基于边的模拟路径
                        self.logger.info(f"基于公共边信息生成模拟路径数据")
                        # 这里可以基于公共边生成更真实的路径数据
                        pass

                # 从精英报告中提取数据
                if elite_report and isinstance(elite_report, dict):
                    elite_features = elite_report.get('elite_features', {})
                    if elite_features and not extracted_populations:
                        # 如果有精英特征但没有种群数据，生成基于特征的模拟数据
                        self.logger.info(f"基于精英特征生成模拟路径数据")
                        pass

            # 如果仍然没有数据，生成默认的模拟数据
            if not extracted_populations or not fitness_values:
                self.logger.warning(f"无法从报告中提取有效数据，生成默认模拟数据")
                # 生成默认的模拟数据用于可视化测试
                import numpy as np
                pop_size = 5
                city_count = 9

                for i in range(pop_size):
                    path = list(range(city_count))
                    np.random.shuffle(path)
                    extracted_populations.append(path)
                    fitness_values.append(float(np.random.uniform(500, 1500)))

            # 确保数据长度一致
            min_length = min(len(extracted_populations), len(fitness_values))
            if min_length > 0:
                extracted_populations = extracted_populations[:min_length]
                fitness_values = fitness_values[:min_length]
                self.logger.info(f"数据提取成功: {len(extracted_populations)}个路径, {len(fitness_values)}个适应度值")
            else:
                self.logger.warning(f"数据提取失败，返回空数据")

            return extracted_populations, fitness_values

        except Exception as e:
            self.logger.error(f"提取种群数据失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return [], []

    def _extract_elite_solutions(self, elite_report: Dict) -> List[Dict]:
        """
        从精英报告中提取精英解数据

        参数:
            elite_report: 精英报告

        返回:
            精英解列表
        """
        try:
            elite_solutions = []

            if elite_report and isinstance(elite_report, dict):
                # 尝试从不同的键中提取精英解
                if 'elite_solutions' in elite_report:
                    elite_solutions = elite_report['elite_solutions']
                elif 'elite_features' in elite_report:
                    # 从精英特征中构造精英解
                    elite_features = elite_report['elite_features']
                    if 'high_quality_edges' in elite_features:
                        # 基于高质量边构造模拟精英解
                        edges = elite_features['high_quality_edges']
                        for i, edge in enumerate(edges[:5]):  # 最多5个精英解
                            # 构造一个基于边的简单路径
                            path = list(range(9))  # 假设9个城市
                            import random
                            random.shuffle(path)
                            elite_solutions.append({
                                'tour': path,
                                'cur_cost': 500 + i * 50  # 模拟成本
                            })

                # 如果仍然没有精英解，从精英质量信息中生成
                if not elite_solutions and 'elite_quality' in elite_report:
                    elite_quality = elite_report['elite_quality']
                    best_cost = elite_quality.get('best_cost', 500)
                    avg_cost = elite_quality.get('avg_cost', 600)

                    # 生成几个精英解
                    import numpy as np
                    for i in range(3):
                        path = list(range(9))
                        np.random.shuffle(path)
                        cost = best_cost + i * (avg_cost - best_cost) / 3
                        elite_solutions.append({
                            'tour': path,
                            'cur_cost': cost
                        })

            self.logger.info(f"提取到 {len(elite_solutions)} 个精英解")
            return elite_solutions

        except Exception as e:
            self.logger.error(f"提取精英解失败: {e}")
            return []

    def _extract_instance_name(self, stats_report: Dict, path_report: Dict, elite_report: Dict) -> str:
        """
        从报告中提取实例名称

        参数:
            stats_report: 统计报告
            path_report: 路径报告
            elite_report: 精英报告

        返回:
            实例名称
        """
        try:
            # 尝试从各种报告中提取实例名
            for report in [stats_report, path_report, elite_report]:
                if report and isinstance(report, dict):
                    if 'instance_name' in report:
                        return report['instance_name']
                    elif 'problem_name' in report:
                        return report['problem_name']

            # 如果没有找到，返回默认名称
            return "unknown_instance"

        except Exception as e:
            self.logger.error(f"提取实例名失败: {e}")
            return "unknown_instance"

    def _update_history(self, fitness_values: List[float], iteration: int):
        """更新历史数据"""
        try:
            if fitness_values:
                # 计算当前迭代的统计信息
                current_stats = {
                    'iteration': iteration,
                    'best_fitness': min(fitness_values),
                    'worst_fitness': max(fitness_values),
                    'mean_fitness': np.mean(fitness_values),
                    'std_fitness': np.std(fitness_values),
                    'population_size': len(fitness_values)
                }

                self.fitness_history.append(current_stats)

                # 限制历史数据大小
                max_history = self.config.get('max_history_size', 1000)
                if len(self.fitness_history) > max_history:
                    self.fitness_history = self.fitness_history[-max_history:]

        except Exception as e:
            self.logger.error(f"更新历史数据失败: {e}")

    def _convert_to_landscape_report(self, analysis_results: Dict[str, Any],
                                   iteration: int, total_iterations: int,
                                   stats_report: Dict[str, Any],
                                   path_report: Dict[str, Any],
                                   elite_report: Any) -> Dict[str, Any]:
        """将分析结果转换为兼容的景观报告格式"""
        try:
            # 正确提取嵌套结构中的核心指标
            local_optima_data = analysis_results.get('local_optima', {})
            local_optima_density = local_optima_data.get('local_optima_density', 0.5)

            gradient_data = analysis_results.get('gradient', {})
            fitness_gradient = gradient_data.get('gradient_mean', 0.5)
            gradient_variance = gradient_data.get('gradient_variance', 0.0)

            clustering_data = analysis_results.get('clustering', {})
            solution_clustering = clustering_data.get('silhouette_score', 0.5)
            n_clusters = clustering_data.get('n_clusters', 1)

            coverage_data = analysis_results.get('coverage', {})
            search_space_coverage = coverage_data.get('coverage_ratio', 0.5)
            coverage_uniformity = coverage_data.get('coverage_uniformity', 0.5)

            convergence_data = analysis_results.get('convergence', {})
            convergence_trend = convergence_data.get('convergence_trend', 0.5)
            convergence_rate = convergence_data.get('convergence_rate', 0.0)

            diversity_data = analysis_results.get('diversity', {})
            diversity_preservation = diversity_data.get('diversity_index', 0.5)
            fitness_entropy = diversity_data.get('fitness_entropy', 0.5)

            # 记录提取的实际值用于调试
            self.logger.info(f"景观特征提取 - 局部最优密度: {local_optima_density:.3f}, "
                           f"适应度梯度: {fitness_gradient:.3f}, 聚类评分: {solution_clustering:.3f}, "
                           f"覆盖率: {search_space_coverage:.3f}, 收敛趋势: {convergence_trend:.3f}, "
                           f"多样性: {diversity_preservation:.3f}")

            # 计算进化阶段
            progress = iteration / max(total_iterations, 1)
            if progress < 0.3:
                evolution_phase = "exploration"
            elif progress < 0.7:
                evolution_phase = "exploitation"
            else:
                evolution_phase = "convergence"

            # 构建兼容的报告格式，使用动态计算的特征
            landscape_report = {
                "search_space_features": {
                    "ruggedness": local_optima_density,
                    "modality": self._determine_modality(local_optima_density, n_clusters),
                    "deceptiveness": self._determine_deceptiveness(fitness_gradient, gradient_variance),
                    "gradient_strength": fitness_gradient,
                    "local_optima_density": local_optima_density,
                    "gradient_variance": gradient_variance,
                    "cluster_count": n_clusters
                },
                "population_state": {
                    "diversity": diversity_preservation,
                    "convergence": convergence_trend,
                    "clustering": solution_clustering,
                    "coverage": search_space_coverage,
                    "fitness_entropy": fitness_entropy,
                    "coverage_uniformity": coverage_uniformity,
                    "convergence_rate": convergence_rate
                },
                "difficult_regions": self._identify_difficult_regions(analysis_results),
                "opportunity_regions": self._identify_opportunity_regions(analysis_results),
                "evolution_phase": evolution_phase,
                "evolution_direction": self._generate_evolution_direction(
                    analysis_results, evolution_phase
                ),
                "iteration_info": {
                    "current": iteration,
                    "total": total_iterations,
                    "progress": progress
                },
                "analysis_metadata": {
                    "algorithm": "algorithmic_landscape_analysis",
                    "version": "2.0",
                    "timestamp": time.time(),
                    "performance_metrics": analysis_results.get('performance_metrics', {})
                }
            }

            return landscape_report

        except Exception as e:
            self.logger.error(f"转换景观报告格式失败: {e}")
            return self._get_default_landscape_report(iteration, total_iterations)

    def _determine_modality(self, local_optima_density: float, n_clusters: int) -> str:
        """根据局部最优密度和聚类数量确定景观模态"""
        if local_optima_density > 0.7 or n_clusters > 5:
            return "multi-modal"
        elif local_optima_density > 0.3 or n_clusters > 2:
            return "bi-modal"
        else:
            return "uni-modal"

    def _determine_deceptiveness(self, gradient_strength: float, gradient_variance: float) -> str:
        """根据梯度强度和方差确定景观欺骗性"""
        if gradient_strength < 0.3 and gradient_variance > 0.5:
            return "high"
        elif gradient_strength < 0.5 and gradient_variance > 0.3:
            return "medium"
        else:
            return "low"

    def _identify_difficult_regions(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别困难区域"""
        try:
            difficult_regions = []

            # 基于局部最优密度识别困难区域
            local_optima_data = analysis_results.get('local_optima', {})
            local_optima_density = local_optima_data.get('local_optima_density', 0.5)
            if local_optima_density > 0.7:
                difficult_regions.append({
                    "type": "high_local_optima",
                    "severity": "high",
                    "description": f"高局部最优密度区域 (密度: {local_optima_density:.3f})",
                    "recommendation": "增加扰动强度"
                })

            # 基于适应度梯度识别困难区域
            gradient_data = analysis_results.get('gradient', {})
            fitness_gradient = gradient_data.get('gradient_mean', 0.5)
            if fitness_gradient < 0.3:
                difficult_regions.append({
                    "type": "flat_landscape",
                    "severity": "medium",
                    "description": f"平坦景观区域 (梯度强度: {fitness_gradient:.3f})",
                    "recommendation": "使用大步长搜索"
                })

            # 基于聚类分析识别困难区域
            clustering_data = analysis_results.get('clustering', {})
            silhouette_score = clustering_data.get('silhouette_score', 0.5)
            if silhouette_score < 0.2:
                difficult_regions.append({
                    "type": "poor_clustering",
                    "severity": "medium",
                    "description": f"聚类质量差 (轮廓系数: {silhouette_score:.3f})",
                    "recommendation": "增加种群多样性"
                })

            return difficult_regions

        except Exception as e:
            self.logger.error(f"识别困难区域失败: {e}")
            return []

    def _identify_opportunity_regions(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别机会区域"""
        try:
            opportunity_regions = []

            # 基于搜索空间覆盖率识别机会区域
            coverage_data = analysis_results.get('coverage', {})
            coverage = coverage_data.get('coverage_ratio', 0.5)
            if coverage < 0.6:
                opportunity_regions.append({
                    "type": "unexplored_space",
                    "potential": "high",
                    "description": f"未充分探索的搜索空间 (覆盖率: {coverage:.3f})",
                    "recommendation": "增加探索性操作"
                })

            # 基于多样性保持度识别机会区域
            diversity_data = analysis_results.get('diversity', {})
            diversity = diversity_data.get('diversity_index', 0.5)
            if diversity > 0.7:
                opportunity_regions.append({
                    "type": "diverse_population",
                    "potential": "medium",
                    "description": f"高多样性种群区域 (多样性指数: {diversity:.3f})",
                    "recommendation": "平衡探索与利用"
                })

            # 基于梯度强度识别机会区域
            gradient_data = analysis_results.get('gradient', {})
            gradient_strength = gradient_data.get('gradient_mean', 0.5)
            if gradient_strength > 0.7:
                opportunity_regions.append({
                    "type": "strong_gradient",
                    "potential": "high",
                    "description": f"强梯度区域 (梯度强度: {gradient_strength:.3f})",
                    "recommendation": "增加利用性操作"
                })

            return opportunity_regions

        except Exception as e:
            self.logger.error(f"识别机会区域失败: {e}")
            return []

    def _generate_evolution_direction(self, analysis_results: Dict[str, Any],
                                    evolution_phase: str) -> Dict[str, Any]:
        """生成进化方向建议"""
        try:
            direction = {
                "recommended_focus": "balance",
                "operators": [],
                "parameters": {}
            }

            # 基于进化阶段调整建议
            if evolution_phase == "exploration":
                direction["recommended_focus"] = "exploration"
                direction["operators"] = ["mutation", "crossover", "perturbation"]
                direction["parameters"] = {
                    "mutation_rate": 0.3,
                    "crossover_rate": 0.7,
                    "perturbation_strength": 0.8
                }
            elif evolution_phase == "exploitation":
                direction["recommended_focus"] = "exploitation"
                direction["operators"] = ["local_search", "hill_climbing"]
                direction["parameters"] = {
                    "local_search_intensity": 0.8,
                    "hill_climbing_steps": 100
                }
            else:  # convergence
                direction["recommended_focus"] = "intensification"
                direction["operators"] = ["fine_tuning", "local_optimization"]
                direction["parameters"] = {
                    "fine_tuning_precision": 0.9,
                    "local_optimization_depth": 50
                }

            # 基于分析结果微调建议
            diversity_data = analysis_results.get('diversity', {})
            diversity = diversity_data.get('diversity_index', 0.5)
            if diversity < 0.3:
                direction["operators"].append("diversification")
                direction["parameters"]["diversification_strength"] = 0.6

            convergence_data = analysis_results.get('convergence', {})
            convergence = convergence_data.get('convergence_trend', 0.5)
            if convergence > 0.8:
                direction["recommended_focus"] = "restart"
                direction["operators"] = ["population_restart", "elite_preservation"]

            return direction

        except Exception as e:
            self.logger.error(f"生成进化方向失败: {e}")
            return {
                "recommended_focus": "balance",
                "operators": ["mutation", "crossover"],
                "parameters": {}
            }

    def _get_default_landscape_report(self, iteration: int, total_iterations: int) -> Dict[str, Any]:
        """获取默认的景观报告（用于错误情况）"""
        progress = iteration / max(total_iterations, 1)

        return {
            "search_space_features": {
                "ruggedness": 0.5,
                "modality": "unknown",
                "deceptiveness": "unknown",
                "gradient_strength": 0.5,
                "local_optima_density": 0.5
            },
            "population_state": {
                "diversity": 0.5,
                "convergence": 0.5,
                "clustering": 0.5,
                "coverage": 0.5
            },
            "difficult_regions": [],
            "opportunity_regions": [],
            "evolution_phase": "exploration" if progress < 0.5 else "exploitation",
            "evolution_direction": {
                "recommended_focus": "balance",
                "operators": ["mutation", "crossover"],
                "parameters": {}
            },
            "iteration_info": {
                "current": iteration,
                "total": total_iterations,
                "progress": progress
            },
            "analysis_metadata": {
                "algorithm": "algorithmic_landscape_analysis",
                "version": "2.0",
                "timestamp": time.time(),
                "status": "default_fallback"
            }
        }

    def _handle_analysis_failure(self, stats_report: Dict[str, Any],
                               path_report: Dict[str, Any],
                               elite_report: Any,
                               iteration: int, total_iterations: int,
                               error: Exception) -> Dict[str, Any]:
        """处理分析失败的情况"""
        self.logger.error(f"景观分析失败，使用默认报告: {error}")

        default_report = self._get_default_landscape_report(iteration, total_iterations)
        default_report["analysis_metadata"]["error"] = str(error)
        default_report["analysis_metadata"]["status"] = "error_fallback"

        return default_report
