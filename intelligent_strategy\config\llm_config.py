"""
LLM configuration classes for the intelligent strategy selection system.

This module defines configuration classes for LLM providers,
API settings, and prompt engineering parameters.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum
import os


class LLMProvider(Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"
    MOCK = "mock"


@dataclass
class LLMConfig:
    """Configuration for LLM integration."""
    
    # Provider settings
    provider: LLMProvider = LLMProvider.OPENAI
    model: str = "gpt-4"
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    
    # Request parameters
    temperature: float = 0.1
    max_tokens: int = 2000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    
    # Timeout and retry settings
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    exponential_backoff: bool = True
    
    # Fallback settings
    fallback_enabled: bool = True
    fallback_strategy: str = "rule_based"
    fallback_provider: Optional[LLMProvider] = None
    
    # Caching settings
    enable_response_caching: bool = True
    cache_ttl: int = 3600  # seconds
    cache_size: int = 1000
    
    # Rate limiting
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.api_key is None:
            if self.provider == LLMProvider.OPENAI:
                self.api_key = os.getenv('OPENAI_API_KEY')
            elif self.provider == LLMProvider.ANTHROPIC:
                self.api_key = os.getenv('ANTHROPIC_API_KEY')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'provider': self.provider.value,
            'model': self.model,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'top_p': self.top_p,
            'frequency_penalty': self.frequency_penalty,
            'presence_penalty': self.presence_penalty,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'exponential_backoff': self.exponential_backoff,
            'fallback_enabled': self.fallback_enabled,
            'fallback_strategy': self.fallback_strategy,
            'fallback_provider': self.fallback_provider.value if self.fallback_provider else None,
            'enable_response_caching': self.enable_response_caching,
            'cache_ttl': self.cache_ttl,
            'cache_size': self.cache_size,
            'requests_per_minute': self.requests_per_minute,
            'requests_per_hour': self.requests_per_hour
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMConfig':
        """Create from dictionary."""
        config = cls()
        config.provider = LLMProvider(data.get('provider', config.provider.value))
        config.model = data.get('model', config.model)
        config.api_key = data.get('api_key', config.api_key)
        config.api_base = data.get('api_base', config.api_base)
        config.temperature = data.get('temperature', config.temperature)
        config.max_tokens = data.get('max_tokens', config.max_tokens)
        config.top_p = data.get('top_p', config.top_p)
        config.frequency_penalty = data.get('frequency_penalty', config.frequency_penalty)
        config.presence_penalty = data.get('presence_penalty', config.presence_penalty)
        config.timeout = data.get('timeout', config.timeout)
        config.max_retries = data.get('max_retries', config.max_retries)
        config.retry_delay = data.get('retry_delay', config.retry_delay)
        config.exponential_backoff = data.get('exponential_backoff', config.exponential_backoff)
        config.fallback_enabled = data.get('fallback_enabled', config.fallback_enabled)
        config.fallback_strategy = data.get('fallback_strategy', config.fallback_strategy)
        fallback_provider = data.get('fallback_provider')
        if fallback_provider:
            config.fallback_provider = LLMProvider(fallback_provider)
        config.enable_response_caching = data.get('enable_response_caching', config.enable_response_caching)
        config.cache_ttl = data.get('cache_ttl', config.cache_ttl)
        config.cache_size = data.get('cache_size', config.cache_size)
        config.requests_per_minute = data.get('requests_per_minute', config.requests_per_minute)
        config.requests_per_hour = data.get('requests_per_hour', config.requests_per_hour)
        return config


@dataclass
class PromptConfig:
    """Configuration for prompt engineering."""
    
    # Template settings
    use_structured_prompts: bool = True
    include_examples: bool = True
    include_reasoning_chain: bool = True
    
    # Context settings
    max_context_length: int = 4000
    include_landscape_analysis: bool = True
    include_individual_states: bool = True
    include_population_context: bool = True
    include_historical_performance: bool = True
    
    # Response format settings
    require_json_response: bool = True
    include_confidence_scores: bool = True
    include_reasoning_explanation: bool = True
    
    # Validation settings
    validate_response_format: bool = True
    require_all_individuals: bool = True
    allow_partial_responses: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'use_structured_prompts': self.use_structured_prompts,
            'include_examples': self.include_examples,
            'include_reasoning_chain': self.include_reasoning_chain,
            'max_context_length': self.max_context_length,
            'include_landscape_analysis': self.include_landscape_analysis,
            'include_individual_states': self.include_individual_states,
            'include_population_context': self.include_population_context,
            'include_historical_performance': self.include_historical_performance,
            'require_json_response': self.require_json_response,
            'include_confidence_scores': self.include_confidence_scores,
            'include_reasoning_explanation': self.include_reasoning_explanation,
            'validate_response_format': self.validate_response_format,
            'require_all_individuals': self.require_all_individuals,
            'allow_partial_responses': self.allow_partial_responses
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PromptConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


# Default configurations
DEFAULT_LLM_CONFIG = LLMConfig()
DEFAULT_PROMPT_CONFIG = PromptConfig()

# Provider-specific default configurations
OPENAI_CONFIG = LLMConfig(
    provider=LLMProvider.OPENAI,
    model="gpt-4",
    temperature=0.1,
    max_tokens=2000
)

ANTHROPIC_CONFIG = LLMConfig(
    provider=LLMProvider.ANTHROPIC,
    model="claude-3-sonnet-20240229",
    temperature=0.1,
    max_tokens=2000
)

MOCK_CONFIG = LLMConfig(
    provider=LLMProvider.MOCK,
    model="mock-model",
    temperature=0.0,
    max_tokens=1000,
    fallback_enabled=False
)
