2025-08-03 15:44:28,179 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 15:44:28,181 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 15:44:28,186 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:28,199 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9890.000, 多样性=0.970
2025-08-03 15:44:28,204 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:44:28,212 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-03 15:44:28,231 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:44:28,233 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-03 15:44:28,233 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:44:28,233 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 15:44:28,234 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 15:44:28,492 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: 92.290, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:44:28,492 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 15:44:28,493 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 15:44:28,557 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 15:44:28,837 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_154428.html
2025-08-03 15:44:28,879 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_154428.html
2025-08-03 15:44:28,879 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 15:44:28,881 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 15:44:28,882 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.6494秒
2025-08-03 15:44:28,884 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 15:44:28,885 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 92.28999999999833, 'local_optima_density': 0.15, 'gradient_variance': 1832480927.8579, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8599865470109875, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 92.290)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754207068.4920099, 'performance_metrics': {}}}
2025-08-03 15:44:28,887 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:44:28,887 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:44:28,888 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-03 15:44:28,888 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-03 15:44:28,896 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:28,896 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-03 15:44:28,897 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:28,897 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 17
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:28,898 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:44:28,898 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:28,899 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 17
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:28,900 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:44:28,900 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 15:44:28,901 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:44:28,901 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:44:28,901 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:28,907 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:28,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,072 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12875.0, 路径长度: 66
2025-08-03 15:44:29,072 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 20, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}
2025-08-03 15:44:29,073 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12875.00)
2025-08-03 15:44:29,073 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-03 15:44:29,073 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-03 15:44:29,074 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,088 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,089 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,090 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63109.0, 路径长度: 66
2025-08-03 15:44:29,090 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [45, 43, 13, 14, 2, 58, 20, 29, 35, 36, 25, 5, 63, 11, 60, 17, 21, 19, 28, 8, 10, 7, 56, 6, 64, 22, 9, 54, 57, 16, 40, 49, 26, 12, 15, 30, 31, 34, 46, 18, 1, 55, 53, 52, 39, 23, 32, 27, 48, 42, 47, 50, 38, 33, 24, 3, 59, 61, 0, 65, 4, 62, 41, 44, 51, 37], 'cur_cost': 63109.0}
2025-08-03 15:44:29,091 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 63109.00)
2025-08-03 15:44:29,091 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:44:29,092 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:44:29,092 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,108 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,108 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,108 - ExplorationExpert - INFO - 探索路径生成完成，成本: 47719.0, 路径长度: 66
2025-08-03 15:44:29,109 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [60, 10, 58, 4, 52, 53, 11, 16, 24, 30, 23, 31, 28, 14, 0, 22, 27, 29, 25, 26, 36, 37, 13, 15, 8, 2, 63, 3, 7, 57, 56, 5, 19, 34, 32, 18, 20, 48, 38, 50, 51, 46, 47, 21, 35, 17, 33, 42, 40, 12, 45, 6, 1, 55, 59, 65, 54, 61, 64, 62, 49, 39, 44, 41, 43, 9], 'cur_cost': 47719.0}
2025-08-03 15:44:29,109 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 47719.00)
2025-08-03 15:44:29,109 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:44:29,110 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:44:29,110 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,117 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:29,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86713.0, 路径长度: 66
2025-08-03 15:44:29,119 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [13, 14, 4, 36, 31, 63, 60, 17, 53, 62, 25, 7, 56, 6, 64, 52, 57, 22, 5, 16, 18, 32, 21, 34, 27, 51, 39, 33, 42, 48, 24, 45, 40, 49, 3, 0, 11, 9, 2, 37, 12, 47, 61, 23, 29, 50, 35, 30, 38, 15, 44, 43, 8, 26, 28, 59, 19, 54, 20, 41, 55, 10, 46, 1, 65, 58], 'cur_cost': 86713.0}
2025-08-03 15:44:29,120 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 86713.00)
2025-08-03 15:44:29,120 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:44:29,120 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:44:29,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,135 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57132.0, 路径长度: 66
2025-08-03 15:44:29,137 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 18, 20, 11, 55, 64, 8, 12, 37, 19, 29, 33, 26, 13, 16, 9, 14, 17, 28, 32, 25, 30, 10, 57, 54, 56, 5, 7, 0, 60, 61, 23, 24, 15, 48, 46, 38, 49, 51, 44, 27, 35, 1, 58, 65, 21, 6, 59, 40, 45, 43, 34, 3, 4, 63, 22, 36, 42, 50, 39, 31, 47, 41, 62, 53, 52], 'cur_cost': 57132.0}
2025-08-03 15:44:29,137 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 57132.00)
2025-08-03 15:44:29,138 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:44:29,138 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:44:29,138 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,156 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,157 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55840.0, 路径长度: 66
2025-08-03 15:44:29,157 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [31, 37, 15, 33, 12, 22, 7, 5, 62, 6, 17, 14, 11, 10, 63, 1, 55, 0, 9, 8, 20, 23, 28, 3, 57, 58, 64, 56, 49, 45, 43, 13, 32, 40, 47, 42, 51, 16, 35, 25, 2, 27, 21, 48, 34, 26, 36, 4, 19, 18, 50, 30, 39, 44, 38, 46, 59, 60, 65, 61, 54, 52, 53, 41, 24, 29], 'cur_cost': 55840.0}
2025-08-03 15:44:29,158 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 55840.00)
2025-08-03 15:44:29,158 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 15:44:29,158 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 15:44:29,158 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,162 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:29,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,163 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97140.0, 路径长度: 66
2025-08-03 15:44:29,163 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [45, 10, 58, 14, 52, 7, 29, 24, 19, 33, 64, 17, 27, 62, 16, 37, 28, 15, 57, 22, 9, 54, 49, 48, 40, 5, 32, 18, 47, 13, 38, 46, 44, 35, 36, 34, 30, 6, 42, 43, 26, 61, 20, 4, 31, 51, 21, 2, 65, 3, 0, 39, 12, 1, 8, 23, 63, 41, 60, 25, 59, 53, 55, 56, 50, 11], 'cur_cost': 97140.0}
2025-08-03 15:44:29,164 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 97140.00)
2025-08-03 15:44:29,164 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-03 15:44:29,164 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-03 15:44:29,165 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,178 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,179 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59301.0, 路径长度: 66
2025-08-03 15:44:29,180 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}
2025-08-03 15:44:29,181 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 59301.00)
2025-08-03 15:44:29,184 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 15:44:29,186 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 15:44:29,186 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,202 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,203 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,204 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66105.0, 路径长度: 66
2025-08-03 15:44:29,204 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}
2025-08-03 15:44:29,206 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 66105.00)
2025-08-03 15:44:29,206 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:44:29,206 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:44:29,206 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:29,225 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:29,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:29,226 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65307.0, 路径长度: 66
2025-08-03 15:44:29,226 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}
2025-08-03 15:44:29,227 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 65307.00)
2025-08-03 15:44:29,227 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 15:44:29,227 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:29,229 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:29,230 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 121920.0
2025-08-03 15:44:31,307 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 15:44:31,308 - ExploitationExpert - INFO - res_population_costs: [99530.0]
2025-08-03 15:44:31,308 - ExploitationExpert - INFO - res_populations: [array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:31,309 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:31,309 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 20, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}, {'tour': [45, 43, 13, 14, 2, 58, 20, 29, 35, 36, 25, 5, 63, 11, 60, 17, 21, 19, 28, 8, 10, 7, 56, 6, 64, 22, 9, 54, 57, 16, 40, 49, 26, 12, 15, 30, 31, 34, 46, 18, 1, 55, 53, 52, 39, 23, 32, 27, 48, 42, 47, 50, 38, 33, 24, 3, 59, 61, 0, 65, 4, 62, 41, 44, 51, 37], 'cur_cost': 63109.0}, {'tour': [60, 10, 58, 4, 52, 53, 11, 16, 24, 30, 23, 31, 28, 14, 0, 22, 27, 29, 25, 26, 36, 37, 13, 15, 8, 2, 63, 3, 7, 57, 56, 5, 19, 34, 32, 18, 20, 48, 38, 50, 51, 46, 47, 21, 35, 17, 33, 42, 40, 12, 45, 6, 1, 55, 59, 65, 54, 61, 64, 62, 49, 39, 44, 41, 43, 9], 'cur_cost': 47719.0}, {'tour': [13, 14, 4, 36, 31, 63, 60, 17, 53, 62, 25, 7, 56, 6, 64, 52, 57, 22, 5, 16, 18, 32, 21, 34, 27, 51, 39, 33, 42, 48, 24, 45, 40, 49, 3, 0, 11, 9, 2, 37, 12, 47, 61, 23, 29, 50, 35, 30, 38, 15, 44, 43, 8, 26, 28, 59, 19, 54, 20, 41, 55, 10, 46, 1, 65, 58], 'cur_cost': 86713.0}, {'tour': [2, 18, 20, 11, 55, 64, 8, 12, 37, 19, 29, 33, 26, 13, 16, 9, 14, 17, 28, 32, 25, 30, 10, 57, 54, 56, 5, 7, 0, 60, 61, 23, 24, 15, 48, 46, 38, 49, 51, 44, 27, 35, 1, 58, 65, 21, 6, 59, 40, 45, 43, 34, 3, 4, 63, 22, 36, 42, 50, 39, 31, 47, 41, 62, 53, 52], 'cur_cost': 57132.0}, {'tour': [31, 37, 15, 33, 12, 22, 7, 5, 62, 6, 17, 14, 11, 10, 63, 1, 55, 0, 9, 8, 20, 23, 28, 3, 57, 58, 64, 56, 49, 45, 43, 13, 32, 40, 47, 42, 51, 16, 35, 25, 2, 27, 21, 48, 34, 26, 36, 4, 19, 18, 50, 30, 39, 44, 38, 46, 59, 60, 65, 61, 54, 52, 53, 41, 24, 29], 'cur_cost': 55840.0}, {'tour': [45, 10, 58, 14, 52, 7, 29, 24, 19, 33, 64, 17, 27, 62, 16, 37, 28, 15, 57, 22, 9, 54, 49, 48, 40, 5, 32, 18, 47, 13, 38, 46, 44, 35, 36, 34, 30, 6, 42, 43, 26, 61, 20, 4, 31, 51, 21, 2, 65, 3, 0, 39, 12, 1, 8, 23, 63, 41, 60, 25, 59, 53, 55, 56, 50, 11], 'cur_cost': 97140.0}, {'tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}, {'tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}, {'tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}, {'tour': array([42, 25, 18, 12, 51,  5, 20, 60, 27,  4, 21, 56, 45, 36, 65, 35, 52,
       10, 54,  0,  1, 48, 11, 46, 29, 13, 40,  8, 28,  6, 50,  7, 26, 15,
       24, 59, 58,  9, 16, 61, 34, 53, 41, 37,  2, 57, 14, 39, 43, 17, 33,
       32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22,  3],
      dtype=int64), 'cur_cost': 121920.0}, {'tour': array([42, 44, 50, 38, 41, 22, 64, 21, 43, 59,  7, 28, 61, 24, 37, 19, 31,
       27, 15, 17, 34,  1, 40, 25, 53, 62, 58, 63, 20, 65, 47, 49, 35,  4,
       11, 52, 56, 32, 54,  8, 12,  0,  6, 48, 29,  2,  9, 36, 13, 30,  5,
       33, 57, 46, 39, 26, 16, 10, 14, 51, 55, 45, 23, 18,  3, 60],
      dtype=int64), 'cur_cost': 95163.0}, {'tour': array([ 8, 11, 36, 26, 51, 18,  3, 47, 25, 64, 52, 12,  4, 57, 63, 21, 55,
       58, 15, 30, 24, 41, 14, 23,  0, 49, 38, 50, 28, 42, 32, 61, 33, 17,
       62, 46, 19, 65, 43,  1, 44, 34, 27, 40,  9, 48, 20, 35, 31, 59, 60,
        7,  2, 13, 22, 53, 29, 37, 39, 10,  5, 56,  6, 16, 45, 54],
      dtype=int64), 'cur_cost': 98403.0}, {'tour': array([24,  2, 53, 22, 47,  5, 43, 37, 23, 16, 56, 65, 18, 20, 10, 60, 35,
       38, 46, 28, 40, 25, 41, 59, 58, 32,  8, 45, 19, 31, 29,  3, 34,  6,
       36,  1, 54,  0, 57, 27, 44, 33,  7, 55, 21, 13,  9, 51, 17,  4, 64,
       39, 14, 15, 61, 49, 12, 62, 52, 48, 63, 50, 42, 11, 26, 30],
      dtype=int64), 'cur_cost': 105913.0}, {'tour': array([47,  8, 19, 53, 20, 54, 50, 65, 21, 45, 28, 49, 57, 51, 37, 10, 58,
       63, 35, 52,  7, 11, 48, 26,  1, 34, 55, 56, 23, 14, 13, 18, 17,  6,
       27, 31, 62, 36, 42, 15, 30, 29,  3, 25, 59, 41, 44, 46, 12, 22, 24,
       33,  5, 32,  0,  2, 61, 40, 64,  9, 60, 38, 16,  4, 43, 39],
      dtype=int64), 'cur_cost': 103766.0}, {'tour': array([35, 64,  7,  4,  0, 51,  5, 10, 38, 29, 19, 54, 37,  8, 17, 34, 39,
       12, 20,  2, 26, 15,  6, 63, 24, 21, 42, 33, 55, 50, 27, 18, 57, 65,
       62, 56, 31, 16, 60, 47, 45, 58,  3, 28, 32, 53, 13, 48, 14,  9, 41,
       36,  1, 52, 44, 46, 49, 30, 11, 23, 59, 61, 43, 25, 22, 40],
      dtype=int64), 'cur_cost': 104773.0}, {'tour': array([ 0, 53, 18, 27, 29, 64, 32, 33, 61, 28, 16,  6, 52, 15, 46, 49, 54,
       63,  5,  9, 25, 14, 26, 48, 36, 42,  8,  3, 41, 11, 65, 24, 39, 31,
       17, 12, 50, 40, 56, 60,  4, 13, 22, 37, 55, 38, 23, 59, 57,  2, 20,
       47, 35, 45, 62, 10,  1, 19, 34,  7, 58, 43, 30, 21, 51, 44],
      dtype=int64), 'cur_cost': 98996.0}, {'tour': array([12, 30, 50, 40, 45, 48, 41, 16, 15, 11, 62,  6,  4, 37,  9,  7, 21,
       39, 27, 13, 20, 51, 14, 25, 10, 28, 56,  5, 55, 19, 63, 33, 58, 22,
       23, 17, 64, 61, 36, 47, 53, 43, 26, 60,  3, 52, 44, 59, 31, 35, 34,
        1, 42, 65, 24, 46,  2, 32, 18,  8, 49, 38,  0, 29, 57, 54],
      dtype=int64), 'cur_cost': 103259.0}, {'tour': array([ 2, 37, 45, 63, 48, 19,  5, 46,  6, 30,  0, 57,  3, 15, 11, 24, 62,
       58, 12, 56, 14, 43, 53, 16, 32, 61, 20, 39, 29,  1, 10, 13,  8, 21,
       31, 25, 36, 27, 44, 49, 47, 23, 55, 26,  9, 42, 35, 64, 65, 60, 50,
       38, 51, 41, 22,  4, 54, 52, 18, 33, 34, 59, 40, 17, 28,  7],
      dtype=int64), 'cur_cost': 98059.0}, {'tour': array([29, 59, 61, 60, 39,  0, 63, 17, 33, 11, 40, 45, 23, 16, 62, 21,  1,
       15,  8, 20, 19, 26, 14,  3,  9, 46, 56, 31,  2, 44,  5, 58, 27, 43,
       37,  4, 18, 53, 32, 50, 54, 10, 38,  6, 64, 35, 48, 55, 47, 22, 13,
       24, 25,  7, 57, 36, 41, 12, 30, 65, 28, 42, 49, 51, 34, 52],
      dtype=int64), 'cur_cost': 115084.0}]
2025-08-03 15:44:31,318 - ExploitationExpert - INFO - 局部搜索耗时: 2.09秒
2025-08-03 15:44:31,321 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 15:44:31,323 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([42, 25, 18, 12, 51,  5, 20, 60, 27,  4, 21, 56, 45, 36, 65, 35, 52,
       10, 54,  0,  1, 48, 11, 46, 29, 13, 40,  8, 28,  6, 50,  7, 26, 15,
       24, 59, 58,  9, 16, 61, 34, 53, 41, 37,  2, 57, 14, 39, 43, 17, 33,
       32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22,  3],
      dtype=int64), 'cur_cost': 121920.0}
2025-08-03 15:44:31,324 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 121920.00)
2025-08-03 15:44:31,324 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:44:31,324 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:44:31,324 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:31,344 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:31,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:31,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60514.0, 路径长度: 66
2025-08-03 15:44:31,345 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}
2025-08-03 15:44:31,347 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 60514.00)
2025-08-03 15:44:31,347 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:44:31,348 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:44:31,349 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:31,357 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:31,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:31,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12406.0, 路径长度: 66
2025-08-03 15:44:31,359 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}
2025-08-03 15:44:31,360 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12406.00)
2025-08-03 15:44:31,360 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 15:44:31,361 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:31,361 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:31,361 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 101150.0
2025-08-03 15:44:33,605 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 15:44:33,605 - ExploitationExpert - INFO - res_population_costs: [99530.0, 9541.0]
2025-08-03 15:44:33,606 - ExploitationExpert - INFO - res_populations: [array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:33,607 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:33,607 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 20, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}, {'tour': [45, 43, 13, 14, 2, 58, 20, 29, 35, 36, 25, 5, 63, 11, 60, 17, 21, 19, 28, 8, 10, 7, 56, 6, 64, 22, 9, 54, 57, 16, 40, 49, 26, 12, 15, 30, 31, 34, 46, 18, 1, 55, 53, 52, 39, 23, 32, 27, 48, 42, 47, 50, 38, 33, 24, 3, 59, 61, 0, 65, 4, 62, 41, 44, 51, 37], 'cur_cost': 63109.0}, {'tour': [60, 10, 58, 4, 52, 53, 11, 16, 24, 30, 23, 31, 28, 14, 0, 22, 27, 29, 25, 26, 36, 37, 13, 15, 8, 2, 63, 3, 7, 57, 56, 5, 19, 34, 32, 18, 20, 48, 38, 50, 51, 46, 47, 21, 35, 17, 33, 42, 40, 12, 45, 6, 1, 55, 59, 65, 54, 61, 64, 62, 49, 39, 44, 41, 43, 9], 'cur_cost': 47719.0}, {'tour': [13, 14, 4, 36, 31, 63, 60, 17, 53, 62, 25, 7, 56, 6, 64, 52, 57, 22, 5, 16, 18, 32, 21, 34, 27, 51, 39, 33, 42, 48, 24, 45, 40, 49, 3, 0, 11, 9, 2, 37, 12, 47, 61, 23, 29, 50, 35, 30, 38, 15, 44, 43, 8, 26, 28, 59, 19, 54, 20, 41, 55, 10, 46, 1, 65, 58], 'cur_cost': 86713.0}, {'tour': [2, 18, 20, 11, 55, 64, 8, 12, 37, 19, 29, 33, 26, 13, 16, 9, 14, 17, 28, 32, 25, 30, 10, 57, 54, 56, 5, 7, 0, 60, 61, 23, 24, 15, 48, 46, 38, 49, 51, 44, 27, 35, 1, 58, 65, 21, 6, 59, 40, 45, 43, 34, 3, 4, 63, 22, 36, 42, 50, 39, 31, 47, 41, 62, 53, 52], 'cur_cost': 57132.0}, {'tour': [31, 37, 15, 33, 12, 22, 7, 5, 62, 6, 17, 14, 11, 10, 63, 1, 55, 0, 9, 8, 20, 23, 28, 3, 57, 58, 64, 56, 49, 45, 43, 13, 32, 40, 47, 42, 51, 16, 35, 25, 2, 27, 21, 48, 34, 26, 36, 4, 19, 18, 50, 30, 39, 44, 38, 46, 59, 60, 65, 61, 54, 52, 53, 41, 24, 29], 'cur_cost': 55840.0}, {'tour': [45, 10, 58, 14, 52, 7, 29, 24, 19, 33, 64, 17, 27, 62, 16, 37, 28, 15, 57, 22, 9, 54, 49, 48, 40, 5, 32, 18, 47, 13, 38, 46, 44, 35, 36, 34, 30, 6, 42, 43, 26, 61, 20, 4, 31, 51, 21, 2, 65, 3, 0, 39, 12, 1, 8, 23, 63, 41, 60, 25, 59, 53, 55, 56, 50, 11], 'cur_cost': 97140.0}, {'tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}, {'tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}, {'tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}, {'tour': array([42, 25, 18, 12, 51,  5, 20, 60, 27,  4, 21, 56, 45, 36, 65, 35, 52,
       10, 54,  0,  1, 48, 11, 46, 29, 13, 40,  8, 28,  6, 50,  7, 26, 15,
       24, 59, 58,  9, 16, 61, 34, 53, 41, 37,  2, 57, 14, 39, 43, 17, 33,
       32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22,  3],
      dtype=int64), 'cur_cost': 121920.0}, {'tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}, {'tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}, {'tour': array([10, 49, 29, 36, 33, 52, 16, 48, 23,  1, 27, 51, 58, 43,  0, 60,  7,
       31,  5, 42, 19, 32, 11,  4,  2, 37, 41, 56,  9, 15, 50, 21, 39,  8,
       30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20,  6, 12,  3, 40,
       38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62],
      dtype=int64), 'cur_cost': 101150.0}, {'tour': array([47,  8, 19, 53, 20, 54, 50, 65, 21, 45, 28, 49, 57, 51, 37, 10, 58,
       63, 35, 52,  7, 11, 48, 26,  1, 34, 55, 56, 23, 14, 13, 18, 17,  6,
       27, 31, 62, 36, 42, 15, 30, 29,  3, 25, 59, 41, 44, 46, 12, 22, 24,
       33,  5, 32,  0,  2, 61, 40, 64,  9, 60, 38, 16,  4, 43, 39],
      dtype=int64), 'cur_cost': 103766.0}, {'tour': array([35, 64,  7,  4,  0, 51,  5, 10, 38, 29, 19, 54, 37,  8, 17, 34, 39,
       12, 20,  2, 26, 15,  6, 63, 24, 21, 42, 33, 55, 50, 27, 18, 57, 65,
       62, 56, 31, 16, 60, 47, 45, 58,  3, 28, 32, 53, 13, 48, 14,  9, 41,
       36,  1, 52, 44, 46, 49, 30, 11, 23, 59, 61, 43, 25, 22, 40],
      dtype=int64), 'cur_cost': 104773.0}, {'tour': array([ 0, 53, 18, 27, 29, 64, 32, 33, 61, 28, 16,  6, 52, 15, 46, 49, 54,
       63,  5,  9, 25, 14, 26, 48, 36, 42,  8,  3, 41, 11, 65, 24, 39, 31,
       17, 12, 50, 40, 56, 60,  4, 13, 22, 37, 55, 38, 23, 59, 57,  2, 20,
       47, 35, 45, 62, 10,  1, 19, 34,  7, 58, 43, 30, 21, 51, 44],
      dtype=int64), 'cur_cost': 98996.0}, {'tour': array([12, 30, 50, 40, 45, 48, 41, 16, 15, 11, 62,  6,  4, 37,  9,  7, 21,
       39, 27, 13, 20, 51, 14, 25, 10, 28, 56,  5, 55, 19, 63, 33, 58, 22,
       23, 17, 64, 61, 36, 47, 53, 43, 26, 60,  3, 52, 44, 59, 31, 35, 34,
        1, 42, 65, 24, 46,  2, 32, 18,  8, 49, 38,  0, 29, 57, 54],
      dtype=int64), 'cur_cost': 103259.0}, {'tour': array([ 2, 37, 45, 63, 48, 19,  5, 46,  6, 30,  0, 57,  3, 15, 11, 24, 62,
       58, 12, 56, 14, 43, 53, 16, 32, 61, 20, 39, 29,  1, 10, 13,  8, 21,
       31, 25, 36, 27, 44, 49, 47, 23, 55, 26,  9, 42, 35, 64, 65, 60, 50,
       38, 51, 41, 22,  4, 54, 52, 18, 33, 34, 59, 40, 17, 28,  7],
      dtype=int64), 'cur_cost': 98059.0}, {'tour': array([29, 59, 61, 60, 39,  0, 63, 17, 33, 11, 40, 45, 23, 16, 62, 21,  1,
       15,  8, 20, 19, 26, 14,  3,  9, 46, 56, 31,  2, 44,  5, 58, 27, 43,
       37,  4, 18, 53, 32, 50, 54, 10, 38,  6, 64, 35, 48, 55, 47, 22, 13,
       24, 25,  7, 57, 36, 41, 12, 30, 65, 28, 42, 49, 51, 34, 52],
      dtype=int64), 'cur_cost': 115084.0}]
2025-08-03 15:44:33,615 - ExploitationExpert - INFO - 局部搜索耗时: 2.25秒
2025-08-03 15:44:33,616 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 15:44:33,616 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([10, 49, 29, 36, 33, 52, 16, 48, 23,  1, 27, 51, 58, 43,  0, 60,  7,
       31,  5, 42, 19, 32, 11,  4,  2, 37, 41, 56,  9, 15, 50, 21, 39,  8,
       30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20,  6, 12,  3, 40,
       38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62],
      dtype=int64), 'cur_cost': 101150.0}
2025-08-03 15:44:33,617 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 101150.00)
2025-08-03 15:44:33,618 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 15:44:33,618 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 15:44:33,618 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:33,632 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:33,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:33,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53137.0, 路径长度: 66
2025-08-03 15:44:33,633 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}
2025-08-03 15:44:33,634 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 53137.00)
2025-08-03 15:44:33,634 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:44:33,635 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:44:33,635 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:33,643 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:33,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:33,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95443.0, 路径长度: 66
2025-08-03 15:44:33,644 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}
2025-08-03 15:44:33,645 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 95443.00)
2025-08-03 15:44:33,645 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:44:33,645 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:44:33,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:33,654 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:33,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:33,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14753.0, 路径长度: 66
2025-08-03 15:44:33,656 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}
2025-08-03 15:44:33,657 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 14753.00)
2025-08-03 15:44:33,658 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 15:44:33,658 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 15:44:33,658 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:33,671 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:33,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:33,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62872.0, 路径长度: 66
2025-08-03 15:44:33,672 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}
2025-08-03 15:44:33,672 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 62872.00)
2025-08-03 15:44:33,673 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:44:33,673 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:44:33,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:33,677 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:33,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:33,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12872.0, 路径长度: 66
2025-08-03 15:44:33,678 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}
2025-08-03 15:44:33,678 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12872.00)
2025-08-03 15:44:33,678 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 15:44:33,679 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:33,679 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:33,681 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 110536.0
2025-08-03 15:44:34,305 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 15:44:34,306 - ExploitationExpert - INFO - res_population_costs: [99530.0, 9541.0]
2025-08-03 15:44:34,306 - ExploitationExpert - INFO - res_populations: [array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:34,307 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:34,307 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 20, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}, {'tour': [45, 43, 13, 14, 2, 58, 20, 29, 35, 36, 25, 5, 63, 11, 60, 17, 21, 19, 28, 8, 10, 7, 56, 6, 64, 22, 9, 54, 57, 16, 40, 49, 26, 12, 15, 30, 31, 34, 46, 18, 1, 55, 53, 52, 39, 23, 32, 27, 48, 42, 47, 50, 38, 33, 24, 3, 59, 61, 0, 65, 4, 62, 41, 44, 51, 37], 'cur_cost': 63109.0}, {'tour': [60, 10, 58, 4, 52, 53, 11, 16, 24, 30, 23, 31, 28, 14, 0, 22, 27, 29, 25, 26, 36, 37, 13, 15, 8, 2, 63, 3, 7, 57, 56, 5, 19, 34, 32, 18, 20, 48, 38, 50, 51, 46, 47, 21, 35, 17, 33, 42, 40, 12, 45, 6, 1, 55, 59, 65, 54, 61, 64, 62, 49, 39, 44, 41, 43, 9], 'cur_cost': 47719.0}, {'tour': [13, 14, 4, 36, 31, 63, 60, 17, 53, 62, 25, 7, 56, 6, 64, 52, 57, 22, 5, 16, 18, 32, 21, 34, 27, 51, 39, 33, 42, 48, 24, 45, 40, 49, 3, 0, 11, 9, 2, 37, 12, 47, 61, 23, 29, 50, 35, 30, 38, 15, 44, 43, 8, 26, 28, 59, 19, 54, 20, 41, 55, 10, 46, 1, 65, 58], 'cur_cost': 86713.0}, {'tour': [2, 18, 20, 11, 55, 64, 8, 12, 37, 19, 29, 33, 26, 13, 16, 9, 14, 17, 28, 32, 25, 30, 10, 57, 54, 56, 5, 7, 0, 60, 61, 23, 24, 15, 48, 46, 38, 49, 51, 44, 27, 35, 1, 58, 65, 21, 6, 59, 40, 45, 43, 34, 3, 4, 63, 22, 36, 42, 50, 39, 31, 47, 41, 62, 53, 52], 'cur_cost': 57132.0}, {'tour': [31, 37, 15, 33, 12, 22, 7, 5, 62, 6, 17, 14, 11, 10, 63, 1, 55, 0, 9, 8, 20, 23, 28, 3, 57, 58, 64, 56, 49, 45, 43, 13, 32, 40, 47, 42, 51, 16, 35, 25, 2, 27, 21, 48, 34, 26, 36, 4, 19, 18, 50, 30, 39, 44, 38, 46, 59, 60, 65, 61, 54, 52, 53, 41, 24, 29], 'cur_cost': 55840.0}, {'tour': [45, 10, 58, 14, 52, 7, 29, 24, 19, 33, 64, 17, 27, 62, 16, 37, 28, 15, 57, 22, 9, 54, 49, 48, 40, 5, 32, 18, 47, 13, 38, 46, 44, 35, 36, 34, 30, 6, 42, 43, 26, 61, 20, 4, 31, 51, 21, 2, 65, 3, 0, 39, 12, 1, 8, 23, 63, 41, 60, 25, 59, 53, 55, 56, 50, 11], 'cur_cost': 97140.0}, {'tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}, {'tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}, {'tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}, {'tour': array([42, 25, 18, 12, 51,  5, 20, 60, 27,  4, 21, 56, 45, 36, 65, 35, 52,
       10, 54,  0,  1, 48, 11, 46, 29, 13, 40,  8, 28,  6, 50,  7, 26, 15,
       24, 59, 58,  9, 16, 61, 34, 53, 41, 37,  2, 57, 14, 39, 43, 17, 33,
       32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22,  3],
      dtype=int64), 'cur_cost': 121920.0}, {'tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}, {'tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}, {'tour': array([10, 49, 29, 36, 33, 52, 16, 48, 23,  1, 27, 51, 58, 43,  0, 60,  7,
       31,  5, 42, 19, 32, 11,  4,  2, 37, 41, 56,  9, 15, 50, 21, 39,  8,
       30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20,  6, 12,  3, 40,
       38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62],
      dtype=int64), 'cur_cost': 101150.0}, {'tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}, {'tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}, {'tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}, {'tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}, {'tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': array([22, 60, 64, 56, 18, 36, 48, 13,  8, 52, 49,  5, 43, 35, 21, 54,  9,
       17, 62, 58, 39, 15, 41,  2, 33, 10, 31, 42, 26,  4, 25,  6, 45,  1,
       65, 46,  0, 38, 20,  7,  3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29,
       61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19],
      dtype=int64), 'cur_cost': 110536.0}]
2025-08-03 15:44:34,311 - ExploitationExpert - INFO - 局部搜索耗时: 0.63秒
2025-08-03 15:44:34,312 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 15:44:34,312 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([22, 60, 64, 56, 18, 36, 48, 13,  8, 52, 49,  5, 43, 35, 21, 54,  9,
       17, 62, 58, 39, 15, 41,  2, 33, 10, 31, 42, 26,  4, 25,  6, 45,  1,
       65, 46,  0, 38, 20,  7,  3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29,
       61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19],
      dtype=int64), 'cur_cost': 110536.0}
2025-08-03 15:44:34,313 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 110536.00)
2025-08-03 15:44:34,314 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 15:44:34,316 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 15:44:34,318 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 20, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12875.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [45, 43, 13, 14, 2, 58, 20, 29, 35, 36, 25, 5, 63, 11, 60, 17, 21, 19, 28, 8, 10, 7, 56, 6, 64, 22, 9, 54, 57, 16, 40, 49, 26, 12, 15, 30, 31, 34, 46, 18, 1, 55, 53, 52, 39, 23, 32, 27, 48, 42, 47, 50, 38, 33, 24, 3, 59, 61, 0, 65, 4, 62, 41, 44, 51, 37], 'cur_cost': 63109.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [60, 10, 58, 4, 52, 53, 11, 16, 24, 30, 23, 31, 28, 14, 0, 22, 27, 29, 25, 26, 36, 37, 13, 15, 8, 2, 63, 3, 7, 57, 56, 5, 19, 34, 32, 18, 20, 48, 38, 50, 51, 46, 47, 21, 35, 17, 33, 42, 40, 12, 45, 6, 1, 55, 59, 65, 54, 61, 64, 62, 49, 39, 44, 41, 43, 9], 'cur_cost': 47719.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [13, 14, 4, 36, 31, 63, 60, 17, 53, 62, 25, 7, 56, 6, 64, 52, 57, 22, 5, 16, 18, 32, 21, 34, 27, 51, 39, 33, 42, 48, 24, 45, 40, 49, 3, 0, 11, 9, 2, 37, 12, 47, 61, 23, 29, 50, 35, 30, 38, 15, 44, 43, 8, 26, 28, 59, 19, 54, 20, 41, 55, 10, 46, 1, 65, 58], 'cur_cost': 86713.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 18, 20, 11, 55, 64, 8, 12, 37, 19, 29, 33, 26, 13, 16, 9, 14, 17, 28, 32, 25, 30, 10, 57, 54, 56, 5, 7, 0, 60, 61, 23, 24, 15, 48, 46, 38, 49, 51, 44, 27, 35, 1, 58, 65, 21, 6, 59, 40, 45, 43, 34, 3, 4, 63, 22, 36, 42, 50, 39, 31, 47, 41, 62, 53, 52], 'cur_cost': 57132.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [31, 37, 15, 33, 12, 22, 7, 5, 62, 6, 17, 14, 11, 10, 63, 1, 55, 0, 9, 8, 20, 23, 28, 3, 57, 58, 64, 56, 49, 45, 43, 13, 32, 40, 47, 42, 51, 16, 35, 25, 2, 27, 21, 48, 34, 26, 36, 4, 19, 18, 50, 30, 39, 44, 38, 46, 59, 60, 65, 61, 54, 52, 53, 41, 24, 29], 'cur_cost': 55840.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [45, 10, 58, 14, 52, 7, 29, 24, 19, 33, 64, 17, 27, 62, 16, 37, 28, 15, 57, 22, 9, 54, 49, 48, 40, 5, 32, 18, 47, 13, 38, 46, 44, 35, 36, 34, 30, 6, 42, 43, 26, 61, 20, 4, 31, 51, 21, 2, 65, 3, 0, 39, 12, 1, 8, 23, 63, 41, 60, 25, 59, 53, 55, 56, 50, 11], 'cur_cost': 97140.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([42, 25, 18, 12, 51,  5, 20, 60, 27,  4, 21, 56, 45, 36, 65, 35, 52,
       10, 54,  0,  1, 48, 11, 46, 29, 13, 40,  8, 28,  6, 50,  7, 26, 15,
       24, 59, 58,  9, 16, 61, 34, 53, 41, 37,  2, 57, 14, 39, 43, 17, 33,
       32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22,  3],
      dtype=int64), 'cur_cost': 121920.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 49, 29, 36, 33, 52, 16, 48, 23,  1, 27, 51, 58, 43,  0, 60,  7,
       31,  5, 42, 19, 32, 11,  4,  2, 37, 41, 56,  9, 15, 50, 21, 39,  8,
       30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20,  6, 12,  3, 40,
       38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62],
      dtype=int64), 'cur_cost': 101150.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 60, 64, 56, 18, 36, 48, 13,  8, 52, 49,  5, 43, 35, 21, 54,  9,
       17, 62, 58, 39, 15, 41,  2, 33, 10, 31, 42, 26,  4, 25,  6, 45,  1,
       65, 46,  0, 38, 20,  7,  3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29,
       61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19],
      dtype=int64), 'cur_cost': 110536.0}}]
2025-08-03 15:44:34,322 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:44:34,322 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:34,337 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12406.000, 多样性=0.974
2025-08-03 15:44:34,338 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 15:44:34,338 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 15:44:34,338 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:44:34,339 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05422116884732248, 'best_improvement': -0.2543983822042467}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.004770521467346555}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9541.0, 'new_best_cost': 9541.0, 'quality_improvement': 0.0, 'old_diversity': 0.9696969696969697, 'new_diversity': 0.9696969696969697, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 15:44:34,340 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 15:44:34,340 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-03 15:44:34,340 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-03 15:44:34,341 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:34,343 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12406.000, 多样性=0.974
2025-08-03 15:44:34,343 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:44:34,354 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.974
2025-08-03 15:44:34,357 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:44:34,359 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.970
2025-08-03 15:44:34,361 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-03 15:44:34,361 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:44:34,361 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-03 15:44:34,362 - LandscapeExpert - INFO - 数据提取成功: 22个路径, 22个适应度值
2025-08-03 15:44:34,455 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.091, 适应度梯度: -4294.409, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:44:34,455 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-03 15:44:34,455 - LandscapeExpert - INFO - 提取到 2 个精英解
2025-08-03 15:44:34,465 - visualization.landscape_visualizer - INFO - 已添加 2 个精英解标记
2025-08-03 15:44:34,538 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250803_154434.html
2025-08-03 15:44:34,578 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250803_154434.html
2025-08-03 15:44:34,578 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-03 15:44:34,579 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-03 15:44:34,579 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2182秒
2025-08-03 15:44:34,580 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.09090909090909091, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4294.409090909091, 'local_optima_density': 0.09090909090909091, 'gradient_variance': 1048657044.06719, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0042, 'fitness_entropy': 0.8801739875608642, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4294.409)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754207074.455079, 'performance_metrics': {}}}
2025-08-03 15:44:34,583 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:44:34,584 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:44:34,585 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-03 15:44:34,585 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-03 15:44:34,587 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:34,587 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-03 15:44:34,588 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:34,588 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 16
- 利用个体数量: 4
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:34,588 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:44:34,589 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:34,589 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 16
- 利用个体数量: 4
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:34,590 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:44:34,590 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 16, 18, 12} (总数: 4, 保护比例: 0.20)
2025-08-03 15:44:34,591 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:44:34,591 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:44:34,591 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,594 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:34,595 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,595 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10476.0, 路径长度: 66
2025-08-03 15:44:34,595 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}
2025-08-03 15:44:34,596 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10476.00)
2025-08-03 15:44:34,596 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-03 15:44:34,596 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-03 15:44:34,596 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,600 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:34,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,600 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97352.0, 路径长度: 66
2025-08-03 15:44:34,601 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}
2025-08-03 15:44:34,601 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 97352.00)
2025-08-03 15:44:34,602 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:44:34,602 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:44:34,602 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,606 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:34,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 91007.0, 路径长度: 66
2025-08-03 15:44:34,607 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}
2025-08-03 15:44:34,607 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 91007.00)
2025-08-03 15:44:34,608 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:44:34,608 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:44:34,608 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,617 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,618 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61834.0, 路径长度: 66
2025-08-03 15:44:34,618 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}
2025-08-03 15:44:34,619 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 61834.00)
2025-08-03 15:44:34,620 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:44:34,620 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:44:34,621 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,626 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:34,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85497.0, 路径长度: 66
2025-08-03 15:44:34,627 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}
2025-08-03 15:44:34,627 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 85497.00)
2025-08-03 15:44:34,627 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:44:34,627 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:44:34,627 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,630 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:34,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,631 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12803.0, 路径长度: 66
2025-08-03 15:44:34,631 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}
2025-08-03 15:44:34,632 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12803.00)
2025-08-03 15:44:34,632 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-03 15:44:34,632 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:34,633 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:34,633 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 113366.0
2025-08-03 15:44:34,716 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 15:44:34,718 - ExploitationExpert - INFO - res_population_costs: [9541.0, 99530.0, 9538, 9532]
2025-08-03 15:44:34,719 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 15:44:34,723 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:34,723 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}, {'tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}, {'tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}, {'tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}, {'tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}, {'tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}, {'tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}, {'tour': [34, 9, 61, 59, 3, 11, 60, 53, 5, 58, 55, 54, 14, 1, 52, 13, 27, 22, 19, 29, 26, 33, 25, 4, 63, 12, 30, 36, 0, 16, 6, 15, 35, 23, 2, 56, 62, 57, 21, 7, 28, 43, 42, 48, 38, 46, 39, 45, 51, 20, 18, 24, 17, 37, 32, 40, 44, 41, 47, 50, 31, 10, 8, 65, 64, 49], 'cur_cost': 59301.0}, {'tour': [58, 56, 60, 54, 1, 14, 6, 64, 62, 20, 11, 65, 4, 0, 63, 21, 3, 16, 9, 53, 17, 34, 22, 23, 24, 7, 26, 32, 5, 37, 18, 13, 8, 2, 29, 40, 38, 45, 48, 49, 19, 35, 12, 36, 47, 42, 41, 50, 15, 27, 30, 31, 46, 39, 44, 25, 43, 28, 33, 55, 59, 61, 57, 52, 10, 51], 'cur_cost': 66105.0}, {'tour': [11, 22, 24, 25, 20, 14, 35, 19, 32, 15, 36, 37, 10, 3, 7, 53, 6, 18, 30, 23, 47, 34, 1, 60, 55, 9, 4, 8, 0, 64, 16, 27, 26, 13, 39, 17, 44, 41, 33, 5, 65, 21, 31, 12, 45, 51, 40, 43, 29, 2, 58, 59, 61, 63, 57, 49, 38, 46, 48, 28, 56, 52, 62, 54, 50, 42], 'cur_cost': 65307.0}, {'tour': [42, 25, 18, 12, 51, 5, 20, 60, 27, 4, 21, 56, 45, 36, 65, 35, 52, 10, 54, 0, 1, 48, 11, 46, 29, 13, 40, 8, 28, 6, 50, 7, 26, 15, 24, 59, 58, 9, 16, 61, 34, 53, 41, 37, 2, 57, 14, 39, 43, 17, 33, 32, 19, 49, 55, 47, 64, 23, 63, 30, 62, 31, 44, 38, 22, 3], 'cur_cost': 121920.0}, {'tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}, {'tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}, {'tour': [10, 49, 29, 36, 33, 52, 16, 48, 23, 1, 27, 51, 58, 43, 0, 60, 7, 31, 5, 42, 19, 32, 11, 4, 2, 37, 41, 56, 9, 15, 50, 21, 39, 8, 30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20, 6, 12, 3, 40, 38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62], 'cur_cost': 101150.0}, {'tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}, {'tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}, {'tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}, {'tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}, {'tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [22, 60, 64, 56, 18, 36, 48, 13, 8, 52, 49, 5, 43, 35, 21, 54, 9, 17, 62, 58, 39, 15, 41, 2, 33, 10, 31, 42, 26, 4, 25, 6, 45, 1, 65, 46, 0, 38, 20, 7, 3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29, 61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19], 'cur_cost': 110536.0}]
2025-08-03 15:44:34,727 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:44:34,727 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 15:44:34,728 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}
2025-08-03 15:44:34,728 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 113366.00)
2025-08-03 15:44:34,728 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-03 15:44:34,729 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-03 15:44:34,729 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,738 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,739 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,739 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60449.0, 路径长度: 66
2025-08-03 15:44:34,739 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}
2025-08-03 15:44:34,740 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 60449.00)
2025-08-03 15:44:34,740 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 15:44:34,740 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 15:44:34,741 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,744 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:34,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,745 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12368.0, 路径长度: 66
2025-08-03 15:44:34,745 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}
2025-08-03 15:44:34,745 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12368.00)
2025-08-03 15:44:34,746 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:44:34,746 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:44:34,746 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,753 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:34,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,755 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12766.0, 路径长度: 66
2025-08-03 15:44:34,755 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}
2025-08-03 15:44:34,756 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12766.00)
2025-08-03 15:44:34,756 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 15:44:34,757 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:34,757 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:34,758 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 116429.0
2025-08-03 15:44:34,831 - ExploitationExpert - INFO - res_population_num: 7
2025-08-03 15:44:34,831 - ExploitationExpert - INFO - res_population_costs: [9541.0, 99530.0, 9538, 9532, 9521, 9521, 9521]
2025-08-03 15:44:34,832 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 15:44:34,836 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:34,836 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}, {'tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}, {'tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}, {'tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}, {'tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}, {'tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}, {'tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}, {'tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}, {'tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}, {'tour': array([54, 27, 64,  4, 26, 47, 10,  3, 53, 55, 62, 42, 25, 21, 46, 14, 61,
       49, 24, 43,  7, 20, 45, 17, 44, 23, 15, 57, 60, 36,  1, 32, 18, 52,
       13, 12,  9, 35,  2, 40,  0,  5, 31,  8,  6, 19, 56, 29, 58, 51, 30,
       11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34],
      dtype=int64), 'cur_cost': 116429.0}, {'tour': [53, 9, 22, 26, 12, 30, 27, 19, 34, 33, 29, 0, 65, 2, 1, 58, 5, 63, 14, 32, 11, 18, 15, 37, 16, 23, 17, 36, 8, 55, 52, 57, 10, 62, 56, 60, 49, 43, 42, 47, 39, 46, 35, 20, 21, 25, 28, 4, 59, 6, 24, 48, 44, 40, 50, 41, 51, 38, 31, 3, 61, 64, 7, 54, 13, 45], 'cur_cost': 60514.0}, {'tour': [0, 17, 21, 19, 16, 18, 12, 22, 23, 13, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12406.0}, {'tour': [10, 49, 29, 36, 33, 52, 16, 48, 23, 1, 27, 51, 58, 43, 0, 60, 7, 31, 5, 42, 19, 32, 11, 4, 2, 37, 41, 56, 9, 15, 50, 21, 39, 8, 30, 61, 55, 47, 54, 44, 18, 14, 53, 28, 46, 65, 20, 6, 12, 3, 40, 38, 26, 35, 34, 25, 24, 57, 64, 63, 59, 17, 45, 13, 22, 62], 'cur_cost': 101150.0}, {'tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}, {'tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}, {'tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}, {'tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}, {'tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [22, 60, 64, 56, 18, 36, 48, 13, 8, 52, 49, 5, 43, 35, 21, 54, 9, 17, 62, 58, 39, 15, 41, 2, 33, 10, 31, 42, 26, 4, 25, 6, 45, 1, 65, 46, 0, 38, 20, 7, 3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29, 61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19], 'cur_cost': 110536.0}]
2025-08-03 15:44:34,839 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 15:44:34,839 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 15:44:34,840 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([54, 27, 64,  4, 26, 47, 10,  3, 53, 55, 62, 42, 25, 21, 46, 14, 61,
       49, 24, 43,  7, 20, 45, 17, 44, 23, 15, 57, 60, 36,  1, 32, 18, 52,
       13, 12,  9, 35,  2, 40,  0,  5, 31,  8,  6, 19, 56, 29, 58, 51, 30,
       11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34],
      dtype=int64), 'cur_cost': 116429.0}
2025-08-03 15:44:34,840 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 116429.00)
2025-08-03 15:44:34,841 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:44:34,841 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:44:34,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,854 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,856 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,857 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61332.0, 路径长度: 66
2025-08-03 15:44:34,857 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}
2025-08-03 15:44:34,858 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 61332.00)
2025-08-03 15:44:34,858 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:44:34,859 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:44:34,859 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,870 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,871 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58376.0, 路径长度: 66
2025-08-03 15:44:34,871 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}
2025-08-03 15:44:34,872 - experts.management.collaboration_manager - INFO - 个体 12 保留原路径 (成本: 58376.00)
2025-08-03 15:44:34,872 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 15:44:34,872 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:34,872 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:34,873 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 103682.0
2025-08-03 15:44:34,954 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:34,955 - ExploitationExpert - INFO - res_population_costs: [9541.0, 99530.0, 9538, 9532, 9521, 9521, 9521, 9521, 9521]
2025-08-03 15:44:34,955 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:34,960 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:34,960 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}, {'tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}, {'tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}, {'tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}, {'tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}, {'tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}, {'tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}, {'tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}, {'tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}, {'tour': array([54, 27, 64,  4, 26, 47, 10,  3, 53, 55, 62, 42, 25, 21, 46, 14, 61,
       49, 24, 43,  7, 20, 45, 17, 44, 23, 15, 57, 60, 36,  1, 32, 18, 52,
       13, 12,  9, 35,  2, 40,  0,  5, 31,  8,  6, 19, 56, 29, 58, 51, 30,
       11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34],
      dtype=int64), 'cur_cost': 116429.0}, {'tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}, {'tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}, {'tour': array([46, 36, 16, 42, 64,  2, 39, 13, 48,  7,  6, 23, 45, 22, 10, 57, 53,
       49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43,
       32, 31,  8,  4, 59,  5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40,  9,
       52,  3, 28, 26, 41, 17,  0, 50, 34, 56, 11, 51, 12, 62,  1],
      dtype=int64), 'cur_cost': 103682.0}, {'tour': [15, 34, 35, 25, 7, 60, 52, 54, 57, 2, 12, 26, 20, 23, 16, 8, 0, 10, 56, 22, 6, 64, 14, 5, 4, 53, 1, 61, 59, 58, 47, 42, 13, 19, 32, 24, 11, 21, 37, 27, 28, 43, 38, 39, 40, 41, 49, 45, 44, 51, 50, 18, 9, 55, 62, 63, 3, 31, 36, 29, 17, 30, 48, 46, 33, 65], 'cur_cost': 53137.0}, {'tour': [34, 18, 24, 33, 55, 53, 8, 12, 35, 36, 64, 31, 11, 14, 7, 22, 28, 19, 59, 26, 9, 37, 56, 60, 27, 2, 39, 63, 57, 48, 61, 5, 16, 23, 15, 13, 51, 6, 46, 43, 1, 47, 20, 52, 65, 30, 32, 42, 38, 45, 44, 50, 40, 4, 62, 3, 54, 0, 41, 17, 10, 21, 58, 29, 49, 25], 'cur_cost': 95443.0}, {'tour': [0, 15, 11, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14753.0}, {'tour': [23, 13, 16, 28, 30, 9, 60, 1, 2, 57, 15, 5, 18, 12, 17, 37, 14, 36, 22, 43, 26, 33, 29, 40, 47, 42, 39, 51, 35, 8, 11, 53, 52, 58, 63, 7, 0, 20, 32, 19, 31, 49, 48, 27, 10, 56, 54, 3, 64, 4, 24, 34, 44, 21, 50, 41, 25, 45, 46, 55, 62, 61, 65, 59, 6, 38], 'cur_cost': 62872.0}, {'tour': [0, 17, 10, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [22, 60, 64, 56, 18, 36, 48, 13, 8, 52, 49, 5, 43, 35, 21, 54, 9, 17, 62, 58, 39, 15, 41, 2, 33, 10, 31, 42, 26, 4, 25, 6, 45, 1, 65, 46, 0, 38, 20, 7, 3, 40, 30, 23, 59, 37, 24, 53, 44, 51, 29, 61, 12, 32, 50, 55, 57, 16, 63, 11, 47, 14, 28, 34, 27, 19], 'cur_cost': 110536.0}]
2025-08-03 15:44:34,965 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:44:34,965 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 15:44:34,966 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([46, 36, 16, 42, 64,  2, 39, 13, 48,  7,  6, 23, 45, 22, 10, 57, 53,
       49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43,
       32, 31,  8,  4, 59,  5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40,  9,
       52,  3, 28, 26, 41, 17,  0, 50, 34, 56, 11, 51, 12, 62,  1],
      dtype=int64), 'cur_cost': 103682.0}
2025-08-03 15:44:34,966 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 103682.00)
2025-08-03 15:44:34,966 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 15:44:34,967 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 15:44:34,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,978 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,978 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59175.0, 路径长度: 66
2025-08-03 15:44:34,980 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}
2025-08-03 15:44:34,983 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 59175.00)
2025-08-03 15:44:34,985 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:44:34,985 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:44:34,985 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:34,996 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:34,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:34,997 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67499.0, 路径长度: 66
2025-08-03 15:44:34,997 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}
2025-08-03 15:44:34,998 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 67499.00)
2025-08-03 15:44:34,998 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:44:34,998 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:44:34,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,004 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12796.0, 路径长度: 66
2025-08-03 15:44:35,006 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}
2025-08-03 15:44:35,006 - experts.management.collaboration_manager - INFO - 个体 16 保留原路径 (成本: 12796.00)
2025-08-03 15:44:35,007 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 15:44:35,007 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 15:44:35,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,011 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14301.0, 路径长度: 66
2025-08-03 15:44:35,012 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}
2025-08-03 15:44:35,012 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 14301.00)
2025-08-03 15:44:35,013 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:44:35,014 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:44:35,016 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,029 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:35,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,030 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57141.0, 路径长度: 66
2025-08-03 15:44:35,030 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}
2025-08-03 15:44:35,030 - experts.management.collaboration_manager - INFO - 个体 18 保留原路径 (成本: 57141.00)
2025-08-03 15:44:35,031 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 15:44:35,031 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:35,031 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:35,032 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 97809.0
2025-08-03 15:44:35,107 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:35,107 - ExploitationExpert - INFO - res_population_costs: [9541.0, 99530.0, 9538, 9532, 9521, 9521, 9521, 9521, 9521]
2025-08-03 15:44:35,107 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:35,113 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:35,114 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}, {'tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}, {'tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}, {'tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}, {'tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}, {'tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}, {'tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}, {'tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}, {'tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}, {'tour': array([54, 27, 64,  4, 26, 47, 10,  3, 53, 55, 62, 42, 25, 21, 46, 14, 61,
       49, 24, 43,  7, 20, 45, 17, 44, 23, 15, 57, 60, 36,  1, 32, 18, 52,
       13, 12,  9, 35,  2, 40,  0,  5, 31,  8,  6, 19, 56, 29, 58, 51, 30,
       11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34],
      dtype=int64), 'cur_cost': 116429.0}, {'tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}, {'tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}, {'tour': array([46, 36, 16, 42, 64,  2, 39, 13, 48,  7,  6, 23, 45, 22, 10, 57, 53,
       49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43,
       32, 31,  8,  4, 59,  5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40,  9,
       52,  3, 28, 26, 41, 17,  0, 50, 34, 56, 11, 51, 12, 62,  1],
      dtype=int64), 'cur_cost': 103682.0}, {'tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}, {'tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}, {'tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}, {'tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}, {'tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}, {'tour': array([41, 49, 47,  1, 45, 52, 59, 63,  5, 13,  8,  2, 36,  6,  0, 18, 27,
       10,  3, 57,  4, 11, 54,  9, 29, 23, 33,  7, 34, 51, 19, 65, 46, 53,
       56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39,
       42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35],
      dtype=int64), 'cur_cost': 97809.0}]
2025-08-03 15:44:35,122 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:44:35,123 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 15:44:35,124 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([41, 49, 47,  1, 45, 52, 59, 63,  5, 13,  8,  2, 36,  6,  0, 18, 27,
       10,  3, 57,  4, 11, 54,  9, 29, 23, 33,  7, 34, 51, 19, 65, 46, 53,
       56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39,
       42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35],
      dtype=int64), 'cur_cost': 97809.0}
2025-08-03 15:44:35,124 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 97809.00)
2025-08-03 15:44:35,124 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 15:44:35,125 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 15:44:35,126 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 9, 5, 4, 8, 2, 10, 1, 7, 3, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10476.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 14, 4, 54, 1, 64, 7, 16, 53, 19, 55, 65, 28, 10, 52, 17, 21, 0, 57, 23, 20, 48, 49, 34, 63, 56, 9, 18, 22, 60, 24, 46, 40, 12, 3, 30, 31, 45, 38, 5, 51, 27, 47, 58, 39, 50, 35, 59, 15, 42, 43, 6, 8, 33, 32, 25, 36, 61, 41, 62, 26, 29, 44, 13, 11, 37], 'cur_cost': 97352.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51,
        4, 37,  5, 48,  9, 18, 55, 16, 20, 59,  7, 61, 49, 58, 45, 10, 54,
       36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28,  6, 57,  1, 39, 29,
        3,  8, 22, 44, 50, 53, 62,  2, 35, 65, 34,  0, 42, 47, 11],
      dtype=int64), 'cur_cost': 113366.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 27, 64,  4, 26, 47, 10,  3, 53, 55, 62, 42, 25, 21, 46, 14, 61,
       49, 24, 43,  7, 20, 45, 17, 44, 23, 15, 57, 60, 36,  1, 32, 18, 52,
       13, 12,  9, 35,  2, 40,  0,  5, 31,  8,  6, 19, 56, 29, 58, 51, 30,
       11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34],
      dtype=int64), 'cur_cost': 116429.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 36, 16, 42, 64,  2, 39, 13, 48,  7,  6, 23, 45, 22, 10, 57, 53,
       49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43,
       32, 31,  8,  4, 59,  5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40,  9,
       52,  3, 28, 26, 41, 17,  0, 50, 34, 56, 11, 51, 12, 62,  1],
      dtype=int64), 'cur_cost': 103682.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 49, 47,  1, 45, 52, 59, 63,  5, 13,  8,  2, 36,  6,  0, 18, 27,
       10,  3, 57,  4, 11, 54,  9, 29, 23, 33,  7, 34, 51, 19, 65, 46, 53,
       56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39,
       42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35],
      dtype=int64), 'cur_cost': 97809.0}}]
2025-08-03 15:44:35,128 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:44:35,129 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:35,141 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10476.000, 多样性=0.956
2025-08-03 15:44:35,141 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-03 15:44:35,142 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-03 15:44:35,142 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:44:35,143 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08023230608199987, 'best_improvement': 0.1555698855392552}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.018172888015717106}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7504208754208754, 'new_diversity': 0.7504208754208754, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 15:44:35,145 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-03 15:44:35,145 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-03 15:44:35,145 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-03 15:44:35,146 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:35,151 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10476.000, 多样性=0.956
2025-08-03 15:44:35,153 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:44:35,159 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.956
2025-08-03 15:44:35,159 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:44:35,164 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.750
2025-08-03 15:44:35,167 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-03 15:44:35,169 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:44:35,169 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-03 15:44:35,169 - LandscapeExpert - INFO - 数据提取成功: 29个路径, 29个适应度值
2025-08-03 15:44:35,323 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.207, 适应度梯度: -12218.814, 聚类评分: 0.000, 覆盖率: 0.007, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:44:35,323 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-03 15:44:35,323 - LandscapeExpert - INFO - 提取到 9 个精英解
2025-08-03 15:44:35,332 - visualization.landscape_visualizer - INFO - 已添加 9 个精英解标记
2025-08-03 15:44:35,421 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250803_154435.html
2025-08-03 15:44:35,478 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250803_154435.html
2025-08-03 15:44:35,479 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-03 15:44:35,479 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-03 15:44:35,479 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3113秒
2025-08-03 15:44:35,480 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.20689655172413793, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -12218.813793103447, 'local_optima_density': 0.20689655172413793, 'gradient_variance': 1441203104.5487754, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0069, 'fitness_entropy': 0.801301625349506, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12218.814)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.007)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754207075.3239393, 'performance_metrics': {}}}
2025-08-03 15:44:35,483 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:44:35,483 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:44:35,484 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-03 15:44:35,484 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-03 15:44:35,486 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:35,487 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-03 15:44:35,487 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:35,488 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:35,488 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:44:35,488 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:35,489 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:35,489 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:44:35,490 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8, 9, 16} (总数: 4, 保护比例: 0.20)
2025-08-03 15:44:35,490 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:44:35,490 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:44:35,490 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,493 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,494 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,494 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12718.0, 路径长度: 66
2025-08-03 15:44:35,494 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}
2025-08-03 15:44:35,495 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12718.00)
2025-08-03 15:44:35,495 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 15:44:35,495 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:35,495 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:35,496 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 116548.0
2025-08-03 15:44:35,587 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:35,588 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0]
2025-08-03 15:44:35,588 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:35,595 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:35,596 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}, {'tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}, {'tour': [31, 25, 61, 11, 51, 5, 35, 64, 19, 58, 3, 7, 4, 1, 53, 62, 21, 15, 28, 16, 17, 54, 22, 65, 57, 13, 30, 14, 42, 12, 56, 45, 24, 34, 36, 20, 44, 41, 48, 49, 27, 40, 38, 37, 39, 26, 33, 2, 29, 18, 0, 43, 46, 63, 8, 59, 10, 60, 50, 55, 47, 52, 23, 9, 6, 32], 'cur_cost': 91007.0}, {'tour': [19, 18, 28, 20, 24, 26, 1, 3, 17, 22, 7, 14, 16, 2, 63, 59, 4, 11, 13, 12, 27, 43, 38, 46, 23, 6, 0, 37, 15, 35, 40, 50, 51, 34, 21, 48, 30, 8, 9, 56, 55, 54, 65, 61, 52, 62, 58, 47, 44, 25, 49, 45, 32, 29, 10, 60, 57, 39, 33, 36, 31, 5, 64, 53, 41, 42], 'cur_cost': 61834.0}, {'tour': [11, 6, 20, 26, 52, 60, 29, 5, 53, 15, 36, 54, 27, 0, 37, 1, 55, 16, 30, 28, 25, 23, 33, 3, 2, 58, 64, 63, 47, 45, 17, 7, 40, 35, 13, 48, 21, 19, 9, 56, 65, 46, 12, 61, 31, 50, 41, 39, 51, 42, 49, 43, 8, 32, 10, 59, 44, 38, 57, 62, 14, 4, 18, 34, 24, 22], 'cur_cost': 85497.0}, {'tour': [0, 16, 1, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12803.0}, {'tour': [19, 21, 41, 56, 31, 60, 17, 46, 14, 33, 38, 52, 15, 40, 63, 12, 51, 4, 37, 5, 48, 9, 18, 55, 16, 20, 59, 7, 61, 49, 58, 45, 10, 54, 36, 32, 13, 24, 26, 27, 30, 23, 43, 25, 64, 28, 6, 57, 1, 39, 29, 3, 8, 22, 44, 50, 53, 62, 2, 35, 65, 34, 0, 42, 47, 11], 'cur_cost': 113366.0}, {'tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}, {'tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}, {'tour': [54, 27, 64, 4, 26, 47, 10, 3, 53, 55, 62, 42, 25, 21, 46, 14, 61, 49, 24, 43, 7, 20, 45, 17, 44, 23, 15, 57, 60, 36, 1, 32, 18, 52, 13, 12, 9, 35, 2, 40, 0, 5, 31, 8, 6, 19, 56, 29, 58, 51, 30, 11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34], 'cur_cost': 116429.0}, {'tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}, {'tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}, {'tour': [46, 36, 16, 42, 64, 2, 39, 13, 48, 7, 6, 23, 45, 22, 10, 57, 53, 49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43, 32, 31, 8, 4, 59, 5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40, 9, 52, 3, 28, 26, 41, 17, 0, 50, 34, 56, 11, 51, 12, 62, 1], 'cur_cost': 103682.0}, {'tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}, {'tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}, {'tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}, {'tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}, {'tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}, {'tour': [41, 49, 47, 1, 45, 52, 59, 63, 5, 13, 8, 2, 36, 6, 0, 18, 27, 10, 3, 57, 4, 11, 54, 9, 29, 23, 33, 7, 34, 51, 19, 65, 46, 53, 56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39, 42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35], 'cur_cost': 97809.0}]
2025-08-03 15:44:35,599 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:35,600 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-03 15:44:35,600 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}
2025-08-03 15:44:35,601 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 116548.00)
2025-08-03 15:44:35,601 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:44:35,601 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:44:35,602 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,606 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10438.0, 路径长度: 66
2025-08-03 15:44:35,607 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}
2025-08-03 15:44:35,608 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10438.00)
2025-08-03 15:44:35,608 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:44:35,608 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:44:35,608 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,612 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:35,612 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,613 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113368.0, 路径长度: 66
2025-08-03 15:44:35,615 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}
2025-08-03 15:44:35,618 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 113368.00)
2025-08-03 15:44:35,619 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:44:35,619 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:44:35,619 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,631 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:35,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55362.0, 路径长度: 66
2025-08-03 15:44:35,632 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}
2025-08-03 15:44:35,633 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 55362.00)
2025-08-03 15:44:35,633 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:44:35,633 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:44:35,634 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,638 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,639 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12862.0, 路径长度: 66
2025-08-03 15:44:35,640 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}
2025-08-03 15:44:35,640 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12862.00)
2025-08-03 15:44:35,640 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-03 15:44:35,640 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:35,641 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:35,641 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 99813.0
2025-08-03 15:44:35,728 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:35,729 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0]
2025-08-03 15:44:35,729 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:35,734 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:35,734 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}, {'tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}, {'tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}, {'tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}, {'tour': [25, 14, 18, 28, 1, 9, 8, 59, 4, 65, 10, 52, 12, 17, 29, 6, 61, 5, 58, 11, 15, 32, 30, 34, 40, 47, 38, 20, 26, 36, 27, 23, 0, 64, 54, 2, 13, 37, 22, 43, 44, 45, 48, 49, 19, 39, 50, 41, 46, 33, 7, 55, 60, 3, 56, 63, 62, 21, 24, 35, 16, 42, 51, 31, 53, 57], 'cur_cost': 60449.0}, {'tour': [0, 13, 22, 18, 16, 23, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12368.0}, {'tour': [0, 5, 12, 9, 11, 7, 3, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12766.0}, {'tour': [54, 27, 64, 4, 26, 47, 10, 3, 53, 55, 62, 42, 25, 21, 46, 14, 61, 49, 24, 43, 7, 20, 45, 17, 44, 23, 15, 57, 60, 36, 1, 32, 18, 52, 13, 12, 9, 35, 2, 40, 0, 5, 31, 8, 6, 19, 56, 29, 58, 51, 30, 11, 41, 59, 16, 37, 33, 48, 63, 39, 22, 65, 28, 50, 38, 34], 'cur_cost': 116429.0}, {'tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}, {'tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}, {'tour': [46, 36, 16, 42, 64, 2, 39, 13, 48, 7, 6, 23, 45, 22, 10, 57, 53, 49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43, 32, 31, 8, 4, 59, 5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40, 9, 52, 3, 28, 26, 41, 17, 0, 50, 34, 56, 11, 51, 12, 62, 1], 'cur_cost': 103682.0}, {'tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}, {'tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}, {'tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}, {'tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}, {'tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}, {'tour': [41, 49, 47, 1, 45, 52, 59, 63, 5, 13, 8, 2, 36, 6, 0, 18, 27, 10, 3, 57, 4, 11, 54, 9, 29, 23, 33, 7, 34, 51, 19, 65, 46, 53, 56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39, 42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35], 'cur_cost': 97809.0}]
2025-08-03 15:44:35,738 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:35,739 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-03 15:44:35,739 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}
2025-08-03 15:44:35,740 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 99813.00)
2025-08-03 15:44:35,740 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-03 15:44:35,740 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-03 15:44:35,740 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,743 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,744 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,744 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12456.0, 路径长度: 66
2025-08-03 15:44:35,744 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}
2025-08-03 15:44:35,745 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12456.00)
2025-08-03 15:44:35,745 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 15:44:35,745 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 15:44:35,745 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,752 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12873.0, 路径长度: 66
2025-08-03 15:44:35,755 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}
2025-08-03 15:44:35,755 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 12873.00)
2025-08-03 15:44:35,756 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:44:35,756 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:44:35,756 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,766 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:35,766 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,767 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65865.0, 路径长度: 66
2025-08-03 15:44:35,768 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}
2025-08-03 15:44:35,768 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 65865.00)
2025-08-03 15:44:35,769 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 15:44:35,769 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:35,769 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:35,770 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 94272.0
2025-08-03 15:44:35,861 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:35,861 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0]
2025-08-03 15:44:35,862 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:35,868 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:35,869 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}, {'tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}, {'tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}, {'tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}, {'tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}, {'tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}, {'tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}, {'tour': array([32, 46, 65, 64, 63, 29,  5, 18, 53, 28,  7, 19, 59,  0, 16, 43, 14,
       26, 48, 38,  4, 30, 39, 27, 47, 24, 10, 55,  1, 58, 35, 37, 25, 52,
       60, 13,  2, 45, 42, 44, 21,  3, 34, 22, 56, 54, 61,  9, 41, 51, 20,
       12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15,  8, 50, 49,  6],
      dtype=int64), 'cur_cost': 94272.0}, {'tour': [28, 21, 32, 16, 22, 17, 14, 3, 2, 15, 4, 0, 64, 52, 1, 57, 55, 39, 19, 11, 8, 18, 27, 36, 25, 34, 6, 65, 54, 40, 41, 46, 37, 13, 49, 26, 35, 29, 9, 58, 61, 62, 23, 12, 47, 20, 5, 60, 56, 63, 45, 42, 50, 44, 48, 43, 31, 24, 7, 53, 10, 33, 30, 38, 51, 59], 'cur_cost': 61332.0}, {'tour': [59, 14, 19, 32, 30, 33, 1, 5, 57, 6, 55, 0, 52, 13, 35, 37, 7, 16, 26, 31, 29, 20, 27, 28, 25, 9, 22, 2, 4, 12, 49, 48, 23, 21, 10, 65, 64, 56, 53, 11, 17, 24, 47, 43, 39, 46, 44, 18, 34, 3, 15, 50, 38, 40, 36, 42, 8, 58, 62, 61, 54, 60, 63, 41, 45, 51], 'cur_cost': 58376.0}, {'tour': [46, 36, 16, 42, 64, 2, 39, 13, 48, 7, 6, 23, 45, 22, 10, 57, 53, 49, 30, 60, 25, 20, 38, 47, 27, 14, 18, 29, 19, 44, 24, 63, 15, 43, 32, 31, 8, 4, 59, 5, 61, 54, 58, 21, 55, 35, 65, 37, 33, 40, 9, 52, 3, 28, 26, 41, 17, 0, 50, 34, 56, 11, 51, 12, 62, 1], 'cur_cost': 103682.0}, {'tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}, {'tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}, {'tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}, {'tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}, {'tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}, {'tour': [41, 49, 47, 1, 45, 52, 59, 63, 5, 13, 8, 2, 36, 6, 0, 18, 27, 10, 3, 57, 4, 11, 54, 9, 29, 23, 33, 7, 34, 51, 19, 65, 46, 53, 56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39, 42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35], 'cur_cost': 97809.0}]
2025-08-03 15:44:35,873 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:35,873 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-03 15:44:35,873 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([32, 46, 65, 64, 63, 29,  5, 18, 53, 28,  7, 19, 59,  0, 16, 43, 14,
       26, 48, 38,  4, 30, 39, 27, 47, 24, 10, 55,  1, 58, 35, 37, 25, 52,
       60, 13,  2, 45, 42, 44, 21,  3, 34, 22, 56, 54, 61,  9, 41, 51, 20,
       12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15,  8, 50, 49,  6],
      dtype=int64), 'cur_cost': 94272.0}
2025-08-03 15:44:35,874 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 94272.00)
2025-08-03 15:44:35,874 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:44:35,874 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:44:35,874 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,878 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,878 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,878 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12862.0, 路径长度: 66
2025-08-03 15:44:35,878 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}
2025-08-03 15:44:35,879 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12862.00)
2025-08-03 15:44:35,879 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:44:35,880 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:44:35,880 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:35,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,888 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12739.0, 路径长度: 66
2025-08-03 15:44:35,888 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}
2025-08-03 15:44:35,889 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12739.00)
2025-08-03 15:44:35,889 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 15:44:35,889 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:35,890 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:35,890 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 116955.0
2025-08-03 15:44:35,966 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 15:44:35,967 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0]
2025-08-03 15:44:35,967 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:35,972 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:35,972 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}, {'tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}, {'tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}, {'tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}, {'tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}, {'tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}, {'tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}, {'tour': array([32, 46, 65, 64, 63, 29,  5, 18, 53, 28,  7, 19, 59,  0, 16, 43, 14,
       26, 48, 38,  4, 30, 39, 27, 47, 24, 10, 55,  1, 58, 35, 37, 25, 52,
       60, 13,  2, 45, 42, 44, 21,  3, 34, 22, 56, 54, 61,  9, 41, 51, 20,
       12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15,  8, 50, 49,  6],
      dtype=int64), 'cur_cost': 94272.0}, {'tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}, {'tour': array([63,  0, 25, 23, 39, 20,  9, 11, 41, 61, 12, 54,  5, 18, 15, 59, 31,
        2, 49, 27, 64, 48, 38, 10,  8, 47, 53, 44, 62, 57, 16, 50,  7, 56,
       46, 32, 22,  1,  4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34,
       55, 40, 33, 21, 28, 65, 30, 42,  6, 26, 52, 36,  3, 14, 29],
      dtype=int64), 'cur_cost': 116955.0}, {'tour': [26, 19, 21, 34, 4, 12, 37, 25, 22, 35, 0, 62, 17, 10, 3, 7, 59, 6, 9, 15, 2, 56, 64, 57, 20, 31, 23, 18, 33, 32, 40, 51, 45, 50, 39, 36, 13, 16, 48, 14, 27, 5, 8, 63, 54, 60, 49, 42, 46, 30, 28, 43, 29, 11, 65, 58, 1, 55, 61, 52, 53, 47, 44, 41, 38, 24], 'cur_cost': 59175.0}, {'tour': [21, 36, 17, 20, 7, 57, 5, 56, 18, 33, 23, 22, 0, 53, 13, 16, 37, 28, 24, 11, 3, 2, 64, 65, 52, 54, 6, 15, 29, 40, 42, 38, 39, 34, 4, 10, 60, 19, 9, 8, 63, 12, 43, 49, 26, 32, 14, 30, 31, 48, 27, 45, 51, 41, 25, 46, 35, 44, 58, 55, 62, 61, 1, 59, 47, 50], 'cur_cost': 67499.0}, {'tour': [0, 23, 4, 8, 2, 6, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12796.0}, {'tour': [0, 2, 24, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14301.0}, {'tour': [34, 24, 37, 17, 11, 53, 6, 65, 64, 10, 18, 23, 25, 15, 12, 30, 36, 29, 26, 5, 62, 1, 52, 61, 63, 21, 27, 7, 2, 16, 14, 28, 3, 13, 19, 22, 40, 47, 43, 45, 38, 48, 31, 42, 35, 4, 9, 57, 0, 60, 58, 8, 32, 39, 44, 51, 41, 46, 33, 20, 55, 59, 56, 54, 49, 50], 'cur_cost': 57141.0}, {'tour': [41, 49, 47, 1, 45, 52, 59, 63, 5, 13, 8, 2, 36, 6, 0, 18, 27, 10, 3, 57, 4, 11, 54, 9, 29, 23, 33, 7, 34, 51, 19, 65, 46, 53, 56, 43, 30, 25, 16, 28, 60, 20, 48, 26, 62, 37, 17, 64, 21, 61, 39, 42, 24, 44, 38, 32, 22, 40, 14, 31, 15, 12, 55, 58, 50, 35], 'cur_cost': 97809.0}]
2025-08-03 15:44:35,977 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:44:35,977 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-03 15:44:35,977 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([63,  0, 25, 23, 39, 20,  9, 11, 41, 61, 12, 54,  5, 18, 15, 59, 31,
        2, 49, 27, 64, 48, 38, 10,  8, 47, 53, 44, 62, 57, 16, 50,  7, 56,
       46, 32, 22,  1,  4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34,
       55, 40, 33, 21, 28, 65, 30, 42,  6, 26, 52, 36,  3, 14, 29],
      dtype=int64), 'cur_cost': 116955.0}
2025-08-03 15:44:35,978 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 116955.00)
2025-08-03 15:44:35,978 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 15:44:35,978 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 15:44:35,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:35,986 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:35,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:35,988 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112473.0, 路径长度: 66
2025-08-03 15:44:35,988 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}
2025-08-03 15:44:35,989 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 112473.00)
2025-08-03 15:44:35,989 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:44:35,989 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:44:35,990 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,001 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,001 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72209.0, 路径长度: 66
2025-08-03 15:44:36,003 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}
2025-08-03 15:44:36,003 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 72209.00)
2025-08-03 15:44:36,003 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:44:36,004 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:44:36,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,018 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,019 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67879.0, 路径长度: 66
2025-08-03 15:44:36,020 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}
2025-08-03 15:44:36,021 - experts.management.collaboration_manager - INFO - 个体 16 保留原路径 (成本: 67879.00)
2025-08-03 15:44:36,021 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 15:44:36,022 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 15:44:36,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,027 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:36,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,028 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101771.0, 路径长度: 66
2025-08-03 15:44:36,028 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}
2025-08-03 15:44:36,029 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 101771.00)
2025-08-03 15:44:36,029 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:44:36,029 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:44:36,030 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,043 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,043 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59556.0, 路径长度: 66
2025-08-03 15:44:36,044 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}
2025-08-03 15:44:36,045 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 59556.00)
2025-08-03 15:44:36,045 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 15:44:36,045 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:36,045 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:36,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 105079.0
2025-08-03 15:44:36,139 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 15:44:36,139 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521]
2025-08-03 15:44:36,139 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 15:44:36,145 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:36,145 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}, {'tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}, {'tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}, {'tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}, {'tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}, {'tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}, {'tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}, {'tour': array([32, 46, 65, 64, 63, 29,  5, 18, 53, 28,  7, 19, 59,  0, 16, 43, 14,
       26, 48, 38,  4, 30, 39, 27, 47, 24, 10, 55,  1, 58, 35, 37, 25, 52,
       60, 13,  2, 45, 42, 44, 21,  3, 34, 22, 56, 54, 61,  9, 41, 51, 20,
       12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15,  8, 50, 49,  6],
      dtype=int64), 'cur_cost': 94272.0}, {'tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}, {'tour': array([63,  0, 25, 23, 39, 20,  9, 11, 41, 61, 12, 54,  5, 18, 15, 59, 31,
        2, 49, 27, 64, 48, 38, 10,  8, 47, 53, 44, 62, 57, 16, 50,  7, 56,
       46, 32, 22,  1,  4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34,
       55, 40, 33, 21, 28, 65, 30, 42,  6, 26, 52, 36,  3, 14, 29],
      dtype=int64), 'cur_cost': 116955.0}, {'tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}, {'tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}, {'tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}, {'tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}, {'tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}, {'tour': array([44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10,
        3,  9, 35, 50, 11,  1, 24, 61, 58, 21,  2, 29, 41, 39, 48, 12, 43,
       55, 46, 18, 49,  0, 22, 57, 56, 42, 45,  5,  6, 32, 36, 17, 65, 27,
       52, 20, 14, 54, 26,  4, 31,  7,  8, 19, 53, 34, 28, 25, 40],
      dtype=int64), 'cur_cost': 105079.0}]
2025-08-03 15:44:36,154 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 15:44:36,155 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-03 15:44:36,156 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10,
        3,  9, 35, 50, 11,  1, 24, 61, 58, 21,  2, 29, 41, 39, 48, 12, 43,
       55, 46, 18, 49,  0, 22, 57, 56, 42, 45,  5,  6, 32, 36, 17, 65, 27,
       52, 20, 14, 54, 26,  4, 31,  7,  8, 19, 53, 34, 28, 25, 40],
      dtype=int64), 'cur_cost': 105079.0}
2025-08-03 15:44:36,156 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 105079.00)
2025-08-03 15:44:36,157 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 15:44:36,157 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 15:44:36,158 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 8, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12718.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 45, 53, 50, 56,  2, 22, 46, 51, 60, 37, 30,  9,  5, 29, 59,  8,
       54,  6, 12, 34, 11, 55, 28, 47, 21, 31, 27,  0, 25, 16, 33, 48, 39,
       20, 44, 52, 38, 26, 64, 40, 65, 43, 63, 49,  3, 62, 42,  7, 36, 23,
        1, 24, 58, 13, 32, 10, 61, 35, 17, 57, 19, 14, 41, 15, 18],
      dtype=int64), 'cur_cost': 116548.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 11, 60, 27, 21, 33, 10, 62, 12, 35,  3, 13, 53, 32, 15,  9, 63,
       16, 40, 18, 26,  4, 28, 44, 43, 38,  6, 22, 34, 17, 50,  8, 56, 42,
        1, 23, 39, 48, 58,  2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52,
       55, 64, 36, 29, 37,  0, 59,  5, 49, 41, 65,  7, 57, 25, 51],
      dtype=int64), 'cur_cost': 99813.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 46, 65, 64, 63, 29,  5, 18, 53, 28,  7, 19, 59,  0, 16, 43, 14,
       26, 48, 38,  4, 30, 39, 27, 47, 24, 10, 55,  1, 58, 35, 37, 25, 52,
       60, 13,  2, 45, 42, 44, 21,  3, 34, 22, 56, 54, 61,  9, 41, 51, 20,
       12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15,  8, 50, 49,  6],
      dtype=int64), 'cur_cost': 94272.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([63,  0, 25, 23, 39, 20,  9, 11, 41, 61, 12, 54,  5, 18, 15, 59, 31,
        2, 49, 27, 64, 48, 38, 10,  8, 47, 53, 44, 62, 57, 16, 50,  7, 56,
       46, 32, 22,  1,  4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34,
       55, 40, 33, 21, 28, 65, 30, 42,  6, 26, 52, 36,  3, 14, 29],
      dtype=int64), 'cur_cost': 116955.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10,
        3,  9, 35, 50, 11,  1, 24, 61, 58, 21,  2, 29, 41, 39, 48, 12, 43,
       55, 46, 18, 49,  0, 22, 57, 56, 42, 45,  5,  6, 32, 36, 17, 65, 27,
       52, 20, 14, 54, 26,  4, 31,  7,  8, 19, 53, 34, 28, 25, 40],
      dtype=int64), 'cur_cost': 105079.0}}]
2025-08-03 15:44:36,161 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:44:36,162 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:36,174 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10438.000, 多样性=0.941
2025-08-03 15:44:36,174 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-03 15:44:36,175 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-03 15:44:36,175 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:44:36,176 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.017327381408808042, 'best_improvement': 0.0036273386788850705}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016008004002001062}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7915824915824916, 'new_diversity': 0.7915824915824916, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 15:44:36,178 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-03 15:44:36,178 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-03 15:44:36,178 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-03 15:44:36,179 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:36,180 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10438.000, 多样性=0.941
2025-08-03 15:44:36,183 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:44:36,190 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.941
2025-08-03 15:44:36,191 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:44:36,196 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.792
2025-08-03 15:44:36,197 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-03 15:44:36,198 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:44:36,198 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-03 15:44:36,199 - LandscapeExpert - INFO - 数据提取成功: 30个路径, 30个适应度值
2025-08-03 15:44:36,350 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.233, 适应度梯度: -21591.473, 聚类评分: 0.000, 覆盖率: 0.009, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:44:36,350 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-03 15:44:36,350 - LandscapeExpert - INFO - 提取到 10 个精英解
2025-08-03 15:44:36,366 - visualization.landscape_visualizer - INFO - 已添加 10 个精英解标记
2025-08-03 15:44:36,465 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_4_20250803_154436.html
2025-08-03 15:44:36,508 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_4_20250803_154436.html
2025-08-03 15:44:36,508 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-03 15:44:36,508 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-03 15:44:36,508 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3111秒
2025-08-03 15:44:36,509 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23333333333333334, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -21591.47333333333, 'local_optima_density': 0.23333333333333334, 'gradient_variance': 932641082.681289, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.009, 'fitness_entropy': 0.7795320093196345, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -21591.473)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.009)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754207076.3507457, 'performance_metrics': {}}}
2025-08-03 15:44:36,509 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:44:36,510 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:44:36,510 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-03 15:44:36,510 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-03 15:44:36,511 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:36,512 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-03 15:44:36,512 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:36,512 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:36,513 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:44:36,513 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:36,514 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:36,515 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:44:36,515 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 12, 7} (总数: 4, 保护比例: 0.20)
2025-08-03 15:44:36,515 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:44:36,515 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:44:36,516 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,528 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,528 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,529 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65171.0, 路径长度: 66
2025-08-03 15:44:36,529 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}
2025-08-03 15:44:36,530 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 65171.00)
2025-08-03 15:44:36,530 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 15:44:36,530 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:36,531 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:36,531 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108504.0
2025-08-03 15:44:36,619 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 15:44:36,619 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521]
2025-08-03 15:44:36,620 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 15:44:36,627 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:36,628 - ExploitationExpert - INFO - populations: [{'tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}, {'tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}, {'tour': [0, 2, 7, 11, 9, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10438.0}, {'tour': [11, 18, 1, 26, 52, 47, 17, 59, 65, 38, 10, 27, 35, 12, 7, 37, 43, 23, 46, 40, 63, 14, 3, 64, 9, 8, 22, 58, 30, 45, 31, 21, 19, 62, 41, 39, 56, 15, 50, 60, 29, 42, 57, 2, 53, 28, 4, 24, 6, 49, 32, 0, 36, 25, 16, 34, 61, 54, 20, 33, 5, 44, 48, 55, 13, 51], 'cur_cost': 113368.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [47, 11, 60, 27, 21, 33, 10, 62, 12, 35, 3, 13, 53, 32, 15, 9, 63, 16, 40, 18, 26, 4, 28, 44, 43, 38, 6, 22, 34, 17, 50, 8, 56, 42, 1, 23, 39, 48, 58, 2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52, 55, 64, 36, 29, 37, 0, 59, 5, 49, 41, 65, 7, 57, 25, 51], 'cur_cost': 99813.0}, {'tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}, {'tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}, {'tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}, {'tour': [32, 46, 65, 64, 63, 29, 5, 18, 53, 28, 7, 19, 59, 0, 16, 43, 14, 26, 48, 38, 4, 30, 39, 27, 47, 24, 10, 55, 1, 58, 35, 37, 25, 52, 60, 13, 2, 45, 42, 44, 21, 3, 34, 22, 56, 54, 61, 9, 41, 51, 20, 12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15, 8, 50, 49, 6], 'cur_cost': 94272.0}, {'tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}, {'tour': [63, 0, 25, 23, 39, 20, 9, 11, 41, 61, 12, 54, 5, 18, 15, 59, 31, 2, 49, 27, 64, 48, 38, 10, 8, 47, 53, 44, 62, 57, 16, 50, 7, 56, 46, 32, 22, 1, 4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34, 55, 40, 33, 21, 28, 65, 30, 42, 6, 26, 52, 36, 3, 14, 29], 'cur_cost': 116955.0}, {'tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}, {'tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}, {'tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}, {'tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}, {'tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}, {'tour': [44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10, 3, 9, 35, 50, 11, 1, 24, 61, 58, 21, 2, 29, 41, 39, 48, 12, 43, 55, 46, 18, 49, 0, 22, 57, 56, 42, 45, 5, 6, 32, 36, 17, 65, 27, 52, 20, 14, 54, 26, 4, 31, 7, 8, 19, 53, 34, 28, 25, 40], 'cur_cost': 105079.0}]
2025-08-03 15:44:36,635 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:36,636 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-03 15:44:36,637 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}
2025-08-03 15:44:36,637 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 108504.00)
2025-08-03 15:44:36,638 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:44:36,638 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:44:36,639 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,642 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:36,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,643 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101753.0, 路径长度: 66
2025-08-03 15:44:36,644 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}
2025-08-03 15:44:36,645 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 101753.00)
2025-08-03 15:44:36,645 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-03 15:44:36,645 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:36,645 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:36,647 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111071.0
2025-08-03 15:44:36,747 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 15:44:36,747 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521]
2025-08-03 15:44:36,747 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 15:44:36,755 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:36,755 - ExploitationExpert - INFO - populations: [{'tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}, {'tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}, {'tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}, {'tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}, {'tour': [17, 5, 58, 4, 9, 64, 61, 59, 15, 16, 36, 35, 37, 8, 62, 7, 65, 19, 14, 2, 55, 21, 40, 12, 18, 33, 6, 53, 52, 54, 11, 22, 25, 31, 30, 26, 24, 3, 63, 13, 1, 34, 29, 27, 10, 23, 20, 28, 39, 45, 41, 47, 44, 49, 46, 48, 38, 43, 51, 50, 0, 56, 57, 60, 42, 32], 'cur_cost': 55362.0}, {'tour': [0, 14, 19, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [47, 11, 60, 27, 21, 33, 10, 62, 12, 35, 3, 13, 53, 32, 15, 9, 63, 16, 40, 18, 26, 4, 28, 44, 43, 38, 6, 22, 34, 17, 50, 8, 56, 42, 1, 23, 39, 48, 58, 2, 30, 46, 54, 61, 24, 20, 19, 31, 45, 14, 52, 55, 64, 36, 29, 37, 0, 59, 5, 49, 41, 65, 7, 57, 25, 51], 'cur_cost': 99813.0}, {'tour': [0, 20, 12, 18, 16, 23, 22, 15, 14, 13, 21, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12456.0}, {'tour': [0, 4, 19, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12873.0}, {'tour': [47, 36, 13, 15, 12, 9, 54, 8, 23, 7, 14, 2, 58, 61, 55, 63, 56, 18, 11, 53, 10, 4, 52, 22, 25, 21, 1, 65, 17, 28, 3, 29, 30, 40, 34, 43, 44, 16, 26, 27, 49, 48, 46, 50, 39, 42, 35, 33, 6, 60, 19, 20, 32, 31, 38, 51, 41, 24, 37, 45, 0, 5, 59, 62, 64, 57], 'cur_cost': 65865.0}, {'tour': [32, 46, 65, 64, 63, 29, 5, 18, 53, 28, 7, 19, 59, 0, 16, 43, 14, 26, 48, 38, 4, 30, 39, 27, 47, 24, 10, 55, 1, 58, 35, 37, 25, 52, 60, 13, 2, 45, 42, 44, 21, 3, 34, 22, 56, 54, 61, 9, 41, 51, 20, 12, 11, 23, 40, 62, 57, 33, 36, 17, 31, 15, 8, 50, 49, 6], 'cur_cost': 94272.0}, {'tour': [0, 12, 7, 5, 4, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12862.0}, {'tour': [0, 15, 14, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12739.0}, {'tour': [63, 0, 25, 23, 39, 20, 9, 11, 41, 61, 12, 54, 5, 18, 15, 59, 31, 2, 49, 27, 64, 48, 38, 10, 8, 47, 53, 44, 62, 57, 16, 50, 7, 56, 46, 32, 22, 1, 4, 43, 13, 17, 37, 51, 45, 19, 35, 58, 60, 24, 34, 55, 40, 33, 21, 28, 65, 30, 42, 6, 26, 52, 36, 3, 14, 29], 'cur_cost': 116955.0}, {'tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}, {'tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}, {'tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}, {'tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}, {'tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}, {'tour': [44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10, 3, 9, 35, 50, 11, 1, 24, 61, 58, 21, 2, 29, 41, 39, 48, 12, 43, 55, 46, 18, 49, 0, 22, 57, 56, 42, 45, 5, 6, 32, 36, 17, 65, 27, 52, 20, 14, 54, 26, 4, 31, 7, 8, 19, 53, 34, 28, 25, 40], 'cur_cost': 105079.0}]
2025-08-03 15:44:36,760 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 15:44:36,760 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-03 15:44:36,760 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}
2025-08-03 15:44:36,761 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 111071.00)
2025-08-03 15:44:36,761 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:44:36,761 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:44:36,761 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,765 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:36,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,765 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12387.0, 路径长度: 66
2025-08-03 15:44:36,766 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}
2025-08-03 15:44:36,766 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 12387.00)
2025-08-03 15:44:36,766 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:44:36,766 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:44:36,767 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,778 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,778 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,779 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49903.0, 路径长度: 66
2025-08-03 15:44:36,779 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}
2025-08-03 15:44:36,781 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 49903.00)
2025-08-03 15:44:36,783 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 15:44:36,784 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 15:44:36,784 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,791 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:36,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12772.0, 路径长度: 66
2025-08-03 15:44:36,793 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}
2025-08-03 15:44:36,793 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12772.00)
2025-08-03 15:44:36,793 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-03 15:44:36,794 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-03 15:44:36,794 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,805 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,805 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,806 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54058.0, 路径长度: 66
2025-08-03 15:44:36,806 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}
2025-08-03 15:44:36,807 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 54058.00)
2025-08-03 15:44:36,807 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 15:44:36,807 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 15:44:36,807 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,810 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:36,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,811 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14713.0, 路径长度: 66
2025-08-03 15:44:36,812 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}
2025-08-03 15:44:36,813 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14713.00)
2025-08-03 15:44:36,813 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:44:36,813 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:44:36,814 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,822 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:36,823 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12938.0, 路径长度: 66
2025-08-03 15:44:36,823 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}
2025-08-03 15:44:36,824 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12938.00)
2025-08-03 15:44:36,824 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-03 15:44:36,824 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-03 15:44:36,825 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,834 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62694.0, 路径长度: 66
2025-08-03 15:44:36,836 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}
2025-08-03 15:44:36,838 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 62694.00)
2025-08-03 15:44:36,838 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:44:36,839 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:44:36,839 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,842 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:36,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107474.0, 路径长度: 66
2025-08-03 15:44:36,843 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}
2025-08-03 15:44:36,843 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 107474.00)
2025-08-03 15:44:36,844 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:44:36,844 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:44:36,844 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:36,857 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:36,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:36,858 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51991.0, 路径长度: 66
2025-08-03 15:44:36,859 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}
2025-08-03 15:44:36,859 - experts.management.collaboration_manager - INFO - 个体 12 保留原路径 (成本: 51991.00)
2025-08-03 15:44:36,859 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 15:44:36,859 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:36,860 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:36,860 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 105528.0
2025-08-03 15:44:36,961 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 15:44:36,962 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521, 9521]
2025-08-03 15:44:36,962 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:36,970 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:36,970 - ExploitationExpert - INFO - populations: [{'tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}, {'tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}, {'tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}, {'tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}, {'tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}, {'tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}, {'tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}, {'tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}, {'tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}, {'tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}, {'tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}, {'tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': array([ 9, 62, 34,  8, 57,  0, 41, 56, 14,  3, 48, 17, 31, 49, 30, 54, 18,
       16, 27,  7, 53,  6, 11, 39, 59, 63,  5, 40, 37, 65, 47, 15, 33, 19,
       32, 60,  1, 23, 42, 45, 24, 25, 61,  4, 44, 13, 29, 12,  2, 64, 52,
       46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10],
      dtype=int64), 'cur_cost': 105528.0}, {'tour': [5, 18, 64, 65, 38, 2, 35, 37, 12, 53, 25, 23, 46, 40, 55, 14, 3, 24, 8, 41, 26, 15, 13, 29, 34, 57, 32, 44, 11, 16, 48, 22, 54, 51, 45, 47, 59, 43, 6, 30, 42, 20, 33, 52, 17, 60, 10, 0, 50, 36, 21, 27, 62, 4, 49, 19, 1, 7, 61, 39, 63, 56, 28, 58, 9, 31], 'cur_cost': 112473.0}, {'tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}, {'tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}, {'tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}, {'tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}, {'tour': [44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10, 3, 9, 35, 50, 11, 1, 24, 61, 58, 21, 2, 29, 41, 39, 48, 12, 43, 55, 46, 18, 49, 0, 22, 57, 56, 42, 45, 5, 6, 32, 36, 17, 65, 27, 52, 20, 14, 54, 26, 4, 31, 7, 8, 19, 53, 34, 28, 25, 40], 'cur_cost': 105079.0}]
2025-08-03 15:44:36,974 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 15:44:36,975 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-03 15:44:36,975 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([ 9, 62, 34,  8, 57,  0, 41, 56, 14,  3, 48, 17, 31, 49, 30, 54, 18,
       16, 27,  7, 53,  6, 11, 39, 59, 63,  5, 40, 37, 65, 47, 15, 33, 19,
       32, 60,  1, 23, 42, 45, 24, 25, 61,  4, 44, 13, 29, 12,  2, 64, 52,
       46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10],
      dtype=int64), 'cur_cost': 105528.0}
2025-08-03 15:44:36,975 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 105528.00)
2025-08-03 15:44:36,976 - experts.management.collaboration_manager - INFO - 为个体 14 生成利用路径
2025-08-03 15:44:36,976 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:36,976 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:36,976 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 14 处的路径，新成本: 108739.0
2025-08-03 15:44:37,062 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 15:44:37,063 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521, 9521]
2025-08-03 15:44:37,063 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:37,070 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:37,071 - ExploitationExpert - INFO - populations: [{'tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}, {'tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}, {'tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}, {'tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}, {'tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}, {'tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}, {'tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}, {'tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}, {'tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}, {'tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}, {'tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}, {'tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': array([ 9, 62, 34,  8, 57,  0, 41, 56, 14,  3, 48, 17, 31, 49, 30, 54, 18,
       16, 27,  7, 53,  6, 11, 39, 59, 63,  5, 40, 37, 65, 47, 15, 33, 19,
       32, 60,  1, 23, 42, 45, 24, 25, 61,  4, 44, 13, 29, 12,  2, 64, 52,
       46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10],
      dtype=int64), 'cur_cost': 105528.0}, {'tour': array([ 8, 36, 56, 49, 29, 13, 41, 50,  1, 44, 57, 63, 15, 60, 12, 14,  2,
        0, 34, 40, 23, 35,  6,  9, 55, 48, 10, 53, 52, 31, 58,  4,  5, 27,
       20, 32, 62, 37, 39, 19, 51, 54, 25, 43,  7, 33, 38, 47,  3, 16, 61,
       64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59],
      dtype=int64), 'cur_cost': 108739.0}, {'tour': [13, 17, 2, 22, 29, 30, 19, 28, 27, 8, 7, 54, 64, 6, 23, 4, 12, 18, 36, 14, 1, 57, 58, 62, 9, 37, 26, 40, 38, 15, 11, 24, 20, 0, 63, 60, 39, 42, 16, 31, 3, 55, 5, 56, 53, 47, 51, 21, 35, 10, 61, 49, 48, 46, 34, 44, 45, 25, 50, 41, 32, 33, 59, 65, 52, 43], 'cur_cost': 72209.0}, {'tour': [42, 47, 15, 26, 36, 14, 35, 9, 60, 56, 22, 25, 13, 33, 32, 3, 54, 58, 6, 0, 65, 53, 57, 7, 62, 20, 4, 63, 23, 8, 24, 29, 34, 21, 16, 37, 19, 5, 17, 43, 18, 48, 46, 27, 12, 10, 52, 40, 39, 38, 41, 45, 30, 28, 11, 55, 61, 2, 59, 49, 44, 51, 31, 1, 64, 50], 'cur_cost': 67879.0}, {'tour': [17, 20, 7, 47, 15, 16, 63, 59, 23, 58, 1, 34, 52, 62, 41, 39, 50, 49, 57, 42, 4, 0, 21, 32, 36, 22, 54, 51, 40, 11, 53, 45, 38, 35, 24, 30, 8, 31, 60, 48, 13, 2, 18, 33, 14, 5, 37, 56, 44, 19, 9, 43, 25, 3, 65, 46, 10, 26, 12, 28, 29, 55, 61, 27, 6, 64], 'cur_cost': 101771.0}, {'tour': [63, 52, 55, 62, 8, 6, 23, 24, 25, 34, 1, 10, 22, 3, 9, 20, 13, 2, 59, 21, 31, 16, 19, 15, 40, 44, 18, 7, 27, 17, 30, 12, 26, 28, 33, 32, 46, 38, 49, 37, 0, 53, 61, 60, 5, 14, 36, 48, 35, 11, 65, 54, 47, 41, 45, 42, 51, 29, 4, 56, 58, 57, 39, 43, 50, 64], 'cur_cost': 59556.0}, {'tour': [44, 23, 37, 62, 15, 59, 64, 38, 16, 47, 30, 13, 60, 51, 63, 33, 10, 3, 9, 35, 50, 11, 1, 24, 61, 58, 21, 2, 29, 41, 39, 48, 12, 43, 55, 46, 18, 49, 0, 22, 57, 56, 42, 45, 5, 6, 32, 36, 17, 65, 27, 52, 20, 14, 54, 26, 4, 31, 7, 8, 19, 53, 34, 28, 25, 40], 'cur_cost': 105079.0}]
2025-08-03 15:44:37,075 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:37,075 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-03 15:44:37,076 - experts.management.collaboration_manager - INFO - 个体 14 利用路径生成报告: {'new_tour': array([ 8, 36, 56, 49, 29, 13, 41, 50,  1, 44, 57, 63, 15, 60, 12, 14,  2,
        0, 34, 40, 23, 35,  6,  9, 55, 48, 10, 53, 52, 31, 58,  4,  5, 27,
       20, 32, 62, 37, 39, 19, 51, 54, 25, 43,  7, 33, 38, 47,  3, 16, 61,
       64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59],
      dtype=int64), 'cur_cost': 108739.0}
2025-08-03 15:44:37,076 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 108739.00)
2025-08-03 15:44:37,076 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:44:37,077 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:44:37,077 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,082 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,083 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,084 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12462.0, 路径长度: 66
2025-08-03 15:44:37,084 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}
2025-08-03 15:44:37,085 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12462.00)
2025-08-03 15:44:37,085 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:44:37,085 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:44:37,086 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,091 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14730.0, 路径长度: 66
2025-08-03 15:44:37,093 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}
2025-08-03 15:44:37,093 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 14730.00)
2025-08-03 15:44:37,093 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 15:44:37,093 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 15:44:37,094 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,104 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:37,105 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,105 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59078.0, 路径长度: 66
2025-08-03 15:44:37,106 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}
2025-08-03 15:44:37,106 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 59078.00)
2025-08-03 15:44:37,106 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:44:37,107 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:44:37,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,110 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,111 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,111 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12431.0, 路径长度: 66
2025-08-03 15:44:37,111 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}
2025-08-03 15:44:37,112 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12431.00)
2025-08-03 15:44:37,113 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 15:44:37,113 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:37,114 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:37,115 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 107702.0
2025-08-03 15:44:37,198 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 15:44:37,198 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521, 9521]
2025-08-03 15:44:37,198 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:37,206 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:37,206 - ExploitationExpert - INFO - populations: [{'tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}, {'tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}, {'tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}, {'tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}, {'tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}, {'tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}, {'tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}, {'tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}, {'tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}, {'tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}, {'tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}, {'tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': array([ 9, 62, 34,  8, 57,  0, 41, 56, 14,  3, 48, 17, 31, 49, 30, 54, 18,
       16, 27,  7, 53,  6, 11, 39, 59, 63,  5, 40, 37, 65, 47, 15, 33, 19,
       32, 60,  1, 23, 42, 45, 24, 25, 61,  4, 44, 13, 29, 12,  2, 64, 52,
       46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10],
      dtype=int64), 'cur_cost': 105528.0}, {'tour': array([ 8, 36, 56, 49, 29, 13, 41, 50,  1, 44, 57, 63, 15, 60, 12, 14,  2,
        0, 34, 40, 23, 35,  6,  9, 55, 48, 10, 53, 52, 31, 58,  4,  5, 27,
       20, 32, 62, 37, 39, 19, 51, 54, 25, 43,  7, 33, 38, 47,  3, 16, 61,
       64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59],
      dtype=int64), 'cur_cost': 108739.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': array([11, 43, 63,  1, 48, 61, 27,  3, 14, 41, 16, 57, 54,  9, 56, 21, 45,
       53, 60, 37, 34, 50, 40,  7, 65,  8, 28, 13,  0, 22, 64, 24,  2, 39,
       58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20,  6,  4, 23, 32, 29, 36,
       12, 19, 35, 10, 51,  5, 33, 15, 52, 62, 47, 18, 42, 25, 49],
      dtype=int64), 'cur_cost': 107702.0}]
2025-08-03 15:44:37,211 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:37,211 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-03 15:44:37,211 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([11, 43, 63,  1, 48, 61, 27,  3, 14, 41, 16, 57, 54,  9, 56, 21, 45,
       53, 60, 37, 34, 50, 40,  7, 65,  8, 28, 13,  0, 22, 64, 24,  2, 39,
       58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20,  6,  4, 23, 32, 29, 36,
       12, 19, 35, 10, 51,  5, 33, 15, 52, 62, 47, 18, 42, 25, 49],
      dtype=int64), 'cur_cost': 107702.0}
2025-08-03 15:44:37,212 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 107702.00)
2025-08-03 15:44:37,212 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 15:44:37,212 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 15:44:37,213 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [28, 32, 22, 23, 16, 34, 33, 26, 15, 0, 17, 27, 9, 58, 5, 65, 63, 64, 11, 1, 55, 18, 20, 36, 2, 60, 8, 13, 30, 4, 3, 14, 29, 6, 10, 61, 7, 52, 57, 49, 47, 41, 21, 31, 19, 12, 37, 48, 40, 43, 38, 42, 45, 51, 35, 46, 25, 50, 24, 44, 56, 59, 53, 62, 54, 39], 'cur_cost': 65171.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 46,  5, 44,  9, 14, 18, 62, 47,  2, 60, 33, 61, 48, 39, 13, 52,
       45, 43, 29,  0, 64, 31, 27, 40, 59, 54, 16, 15, 21, 38, 58, 30, 51,
       53, 17, 42,  1,  6, 28, 20, 22, 35, 50, 11, 24, 32, 34, 41, 10, 23,
        7, 57, 55, 36, 25,  3, 56,  8, 19, 12,  4, 63, 37, 65, 49],
      dtype=int64), 'cur_cost': 108504.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 20, 31, 12, 63, 55, 59,  2, 26,  7, 36, 61, 15, 50, 48, 19, 34,
        8, 13,  4, 17, 32, 29, 21, 23,  3, 51, 27, 65,  9,  0, 45, 60, 16,
       64, 56, 37, 22, 43,  1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62,
       38, 47, 58, 57, 49, 35, 54, 14,  6, 44, 52, 28,  5, 10, 42],
      dtype=int64), 'cur_cost': 111071.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}}, {'individual': 10, 'strategy': 'explore', 'path_data': {'new_tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 62, 34,  8, 57,  0, 41, 56, 14,  3, 48, 17, 31, 49, 30, 54, 18,
       16, 27,  7, 53,  6, 11, 39, 59, 63,  5, 40, 37, 65, 47, 15, 33, 19,
       32, 60,  1, 23, 42, 45, 24, 25, 61,  4, 44, 13, 29, 12,  2, 64, 52,
       46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10],
      dtype=int64), 'cur_cost': 105528.0}}, {'individual': 14, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 36, 56, 49, 29, 13, 41, 50,  1, 44, 57, 63, 15, 60, 12, 14,  2,
        0, 34, 40, 23, 35,  6,  9, 55, 48, 10, 53, 52, 31, 58,  4,  5, 27,
       20, 32, 62, 37, 39, 19, 51, 54, 25, 43,  7, 33, 38, 47,  3, 16, 61,
       64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59],
      dtype=int64), 'cur_cost': 108739.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 43, 63,  1, 48, 61, 27,  3, 14, 41, 16, 57, 54,  9, 56, 21, 45,
       53, 60, 37, 34, 50, 40,  7, 65,  8, 28, 13,  0, 22, 64, 24,  2, 39,
       58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20,  6,  4, 23, 32, 29, 36,
       12, 19, 35, 10, 51,  5, 33, 15, 52, 62, 47, 18, 42, 25, 49],
      dtype=int64), 'cur_cost': 107702.0}}]
2025-08-03 15:44:37,216 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:44:37,216 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:37,232 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12387.000, 多样性=0.950
2025-08-03 15:44:37,232 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-03 15:44:37,232 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-03 15:44:37,233 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:44:37,235 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.023916220814719346, 'best_improvement': -0.18672159417512935}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.009744111167598754}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01844689371925722, 'recent_improvements': [-0.05422116884732248, 0.08023230608199987, -0.017327381408808042], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8378010878010879, 'new_diversity': 0.8378010878010879, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 15:44:37,240 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-03 15:44:37,240 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-03 15:44:37,241 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-03 15:44:37,241 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:37,242 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12387.000, 多样性=0.950
2025-08-03 15:44:37,242 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:44:37,249 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.950
2025-08-03 15:44:37,251 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:44:37,259 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.838
2025-08-03 15:44:37,260 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-03 15:44:37,261 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:44:37,261 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-03 15:44:37,261 - LandscapeExpert - INFO - 数据提取成功: 33个路径, 33个适应度值
2025-08-03 15:44:37,463 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.273, 适应度梯度: -15506.085, 聚类评分: 0.000, 覆盖率: 0.011, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:44:37,464 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-03 15:44:37,464 - LandscapeExpert - INFO - 提取到 13 个精英解
2025-08-03 15:44:37,473 - visualization.landscape_visualizer - INFO - 已添加 13 个精英解标记
2025-08-03 15:44:37,562 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_5_20250803_154437.html
2025-08-03 15:44:37,604 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_5_20250803_154437.html
2025-08-03 15:44:37,605 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-03 15:44:37,605 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-03 15:44:37,605 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3446秒
2025-08-03 15:44:37,606 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2727272727272727, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -15506.084848484848, 'local_optima_density': 0.2727272727272727, 'gradient_variance': 1340056432.7321944, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0113, 'fitness_entropy': 0.6957255983905418, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -15506.085)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.011)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754207077.4648929, 'performance_metrics': {}}}
2025-08-03 15:44:37,606 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:44:37,606 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:44:37,607 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-03 15:44:37,607 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-03 15:44:37,608 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:37,608 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-03 15:44:37,608 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:37,609 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 14
- 利用个体数量: 6
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:37,609 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:44:37,609 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-03 15:44:37,610 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 14
- 利用个体数量: 6
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-03 15:44:37,610 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:44:37,611 - experts.management.collaboration_manager - INFO - 识别精英个体: {18, 4, 6, 15} (总数: 4, 保护比例: 0.20)
2025-08-03 15:44:37,611 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:44:37,611 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:44:37,611 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,625 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:37,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48630.0, 路径长度: 66
2025-08-03 15:44:37,626 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}
2025-08-03 15:44:37,626 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 48630.00)
2025-08-03 15:44:37,627 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 15:44:37,627 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:37,627 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:37,627 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 119723.0
2025-08-03 15:44:37,707 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 15:44:37,707 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0]
2025-08-03 15:44:37,708 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64)]
2025-08-03 15:44:37,715 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:37,715 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [28, 5, 1, 18, 21, 3, 61, 8, 6, 35, 37, 32, 7, 26, 64, 53, 30, 20, 44, 24, 63, 22, 15, 34, 45, 17, 29, 31, 41, 39, 48, 57, 49, 25, 46, 54, 51, 23, 4, 9, 40, 14, 38, 12, 36, 16, 62, 65, 50, 27, 59, 52, 60, 10, 33, 58, 43, 55, 47, 56, 19, 11, 13, 42, 0, 2], 'cur_cost': 101753.0}, {'tour': [18, 20, 31, 12, 63, 55, 59, 2, 26, 7, 36, 61, 15, 50, 48, 19, 34, 8, 13, 4, 17, 32, 29, 21, 23, 3, 51, 27, 65, 9, 0, 45, 60, 16, 64, 56, 37, 22, 43, 1, 46, 30, 11, 41, 53, 24, 39, 33, 25, 40, 62, 38, 47, 58, 57, 49, 35, 54, 14, 6, 44, 52, 28, 5, 10, 42], 'cur_cost': 111071.0}, {'tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}, {'tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}, {'tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}, {'tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}, {'tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}, {'tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}, {'tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}, {'tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': [9, 62, 34, 8, 57, 0, 41, 56, 14, 3, 48, 17, 31, 49, 30, 54, 18, 16, 27, 7, 53, 6, 11, 39, 59, 63, 5, 40, 37, 65, 47, 15, 33, 19, 32, 60, 1, 23, 42, 45, 24, 25, 61, 4, 44, 13, 29, 12, 2, 64, 52, 46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10], 'cur_cost': 105528.0}, {'tour': [8, 36, 56, 49, 29, 13, 41, 50, 1, 44, 57, 63, 15, 60, 12, 14, 2, 0, 34, 40, 23, 35, 6, 9, 55, 48, 10, 53, 52, 31, 58, 4, 5, 27, 20, 32, 62, 37, 39, 19, 51, 54, 25, 43, 7, 33, 38, 47, 3, 16, 61, 64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59], 'cur_cost': 108739.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [11, 43, 63, 1, 48, 61, 27, 3, 14, 41, 16, 57, 54, 9, 56, 21, 45, 53, 60, 37, 34, 50, 40, 7, 65, 8, 28, 13, 0, 22, 64, 24, 2, 39, 58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20, 6, 4, 23, 32, 29, 36, 12, 19, 35, 10, 51, 5, 33, 15, 52, 62, 47, 18, 42, 25, 49], 'cur_cost': 107702.0}]
2025-08-03 15:44:37,721 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:44:37,723 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-03 15:44:37,724 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}
2025-08-03 15:44:37,725 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 119723.00)
2025-08-03 15:44:37,726 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:44:37,726 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:44:37,726 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,731 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12819.0, 路径长度: 66
2025-08-03 15:44:37,732 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}
2025-08-03 15:44:37,732 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12819.00)
2025-08-03 15:44:37,732 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-03 15:44:37,732 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:37,733 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:37,733 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 126063.0
2025-08-03 15:44:37,826 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:44:37,826 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521]
2025-08-03 15:44:37,826 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:37,835 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:37,836 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}, {'tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}, {'tour': [0, 22, 19, 13, 20, 21, 23, 16, 18, 12, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12387.0}, {'tour': [14, 31, 26, 37, 32, 18, 17, 8, 60, 12, 34, 30, 5, 57, 64, 1, 62, 55, 53, 11, 63, 52, 59, 0, 58, 54, 10, 15, 7, 9, 61, 40, 20, 22, 13, 4, 19, 6, 35, 28, 29, 33, 16, 25, 43, 49, 38, 45, 51, 48, 44, 41, 42, 46, 47, 23, 27, 36, 21, 2, 3, 56, 65, 39, 50, 24], 'cur_cost': 49903.0}, {'tour': [0, 18, 17, 8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12772.0}, {'tour': [37, 11, 18, 9, 5, 15, 30, 12, 6, 2, 56, 21, 17, 22, 0, 10, 53, 39, 34, 25, 26, 33, 20, 14, 23, 3, 13, 36, 1, 19, 44, 45, 50, 27, 31, 28, 24, 32, 35, 46, 48, 51, 4, 52, 57, 54, 65, 64, 8, 60, 55, 16, 29, 40, 49, 47, 38, 42, 7, 61, 59, 62, 58, 63, 43, 41], 'cur_cost': 54058.0}, {'tour': [0, 23, 11, 17, 12, 22, 15, 14, 20, 21, 13, 16, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14713.0}, {'tour': [0, 19, 2, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12938.0}, {'tour': [51, 44, 40, 18, 12, 30, 16, 35, 22, 29, 36, 24, 23, 4, 8, 3, 56, 58, 55, 6, 11, 9, 5, 60, 52, 21, 49, 14, 33, 25, 10, 15, 26, 31, 1, 57, 13, 48, 17, 32, 34, 2, 20, 47, 27, 0, 54, 53, 64, 19, 7, 59, 62, 39, 41, 38, 45, 43, 42, 37, 46, 50, 28, 61, 65, 63], 'cur_cost': 62694.0}, {'tour': [32, 20, 15, 22, 21, 34, 30, 58, 0, 44, 11, 33, 38, 36, 45, 54, 10, 14, 1, 23, 40, 49, 27, 51, 6, 57, 9, 47, 41, 31, 19, 65, 43, 55, 42, 60, 4, 18, 28, 50, 2, 46, 7, 64, 48, 8, 62, 16, 29, 53, 3, 5, 52, 61, 39, 17, 63, 35, 26, 12, 59, 37, 25, 56, 13, 24], 'cur_cost': 107474.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': [9, 62, 34, 8, 57, 0, 41, 56, 14, 3, 48, 17, 31, 49, 30, 54, 18, 16, 27, 7, 53, 6, 11, 39, 59, 63, 5, 40, 37, 65, 47, 15, 33, 19, 32, 60, 1, 23, 42, 45, 24, 25, 61, 4, 44, 13, 29, 12, 2, 64, 52, 46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10], 'cur_cost': 105528.0}, {'tour': [8, 36, 56, 49, 29, 13, 41, 50, 1, 44, 57, 63, 15, 60, 12, 14, 2, 0, 34, 40, 23, 35, 6, 9, 55, 48, 10, 53, 52, 31, 58, 4, 5, 27, 20, 32, 62, 37, 39, 19, 51, 54, 25, 43, 7, 33, 38, 47, 3, 16, 61, 64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59], 'cur_cost': 108739.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [11, 43, 63, 1, 48, 61, 27, 3, 14, 41, 16, 57, 54, 9, 56, 21, 45, 53, 60, 37, 34, 50, 40, 7, 65, 8, 28, 13, 0, 22, 64, 24, 2, 39, 58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20, 6, 4, 23, 32, 29, 36, 12, 19, 35, 10, 51, 5, 33, 15, 52, 62, 47, 18, 42, 25, 49], 'cur_cost': 107702.0}]
2025-08-03 15:44:37,840 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 15:44:37,841 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-03 15:44:37,841 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}
2025-08-03 15:44:37,842 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 126063.00)
2025-08-03 15:44:37,842 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:44:37,842 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:44:37,842 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,854 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:37,856 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,856 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60768.0, 路径长度: 66
2025-08-03 15:44:37,857 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}
2025-08-03 15:44:37,858 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 60768.00)
2025-08-03 15:44:37,858 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:44:37,858 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:44:37,858 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,862 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,863 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12850.0, 路径长度: 66
2025-08-03 15:44:37,863 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}
2025-08-03 15:44:37,864 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12850.00)
2025-08-03 15:44:37,864 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 15:44:37,864 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 15:44:37,865 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,868 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:37,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,869 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109236.0, 路径长度: 66
2025-08-03 15:44:37,869 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}
2025-08-03 15:44:37,870 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 109236.00)
2025-08-03 15:44:37,870 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-03 15:44:37,870 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-03 15:44:37,871 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,874 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,875 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12308.0, 路径长度: 66
2025-08-03 15:44:37,875 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}
2025-08-03 15:44:37,875 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12308.00)
2025-08-03 15:44:37,876 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 15:44:37,876 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 15:44:37,876 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,879 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14789.0, 路径长度: 66
2025-08-03 15:44:37,885 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}
2025-08-03 15:44:37,886 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14789.00)
2025-08-03 15:44:37,887 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:44:37,887 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:44:37,887 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,891 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 15:44:37,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,892 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116131.0, 路径长度: 66
2025-08-03 15:44:37,893 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}
2025-08-03 15:44:37,893 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 116131.00)
2025-08-03 15:44:37,893 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-03 15:44:37,894 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-03 15:44:37,894 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:37,897 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:37,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:37,898 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12742.0, 路径长度: 66
2025-08-03 15:44:37,898 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}
2025-08-03 15:44:37,898 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 12742.00)
2025-08-03 15:44:37,899 - experts.management.collaboration_manager - INFO - 为个体 11 生成利用路径
2025-08-03 15:44:37,899 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:37,899 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:37,899 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 11 处的路径，新成本: 114599.0
2025-08-03 15:44:37,984 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:44:37,985 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521]
2025-08-03 15:44:37,987 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:37,997 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:37,997 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}, {'tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}, {'tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}, {'tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}, {'tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}, {'tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}, {'tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}, {'tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}, {'tour': [49, 45, 26, 32, 19, 37, 9, 5, 8, 57, 61, 3, 55, 15, 28, 25, 29, 6, 14, 17, 23, 31, 36, 24, 16, 18, 27, 13, 40, 42, 51, 41, 4, 64, 2, 53, 22, 12, 33, 20, 39, 47, 7, 10, 1, 65, 63, 62, 21, 48, 43, 34, 30, 35, 50, 38, 11, 0, 56, 58, 59, 52, 54, 60, 44, 46], 'cur_cost': 51991.0}, {'tour': [9, 62, 34, 8, 57, 0, 41, 56, 14, 3, 48, 17, 31, 49, 30, 54, 18, 16, 27, 7, 53, 6, 11, 39, 59, 63, 5, 40, 37, 65, 47, 15, 33, 19, 32, 60, 1, 23, 42, 45, 24, 25, 61, 4, 44, 13, 29, 12, 2, 64, 52, 46, 28, 26, 35, 55, 50, 20, 38, 36, 51, 58, 43, 21, 22, 10], 'cur_cost': 105528.0}, {'tour': [8, 36, 56, 49, 29, 13, 41, 50, 1, 44, 57, 63, 15, 60, 12, 14, 2, 0, 34, 40, 23, 35, 6, 9, 55, 48, 10, 53, 52, 31, 58, 4, 5, 27, 20, 32, 62, 37, 39, 19, 51, 54, 25, 43, 7, 33, 38, 47, 3, 16, 61, 64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59], 'cur_cost': 108739.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [11, 43, 63, 1, 48, 61, 27, 3, 14, 41, 16, 57, 54, 9, 56, 21, 45, 53, 60, 37, 34, 50, 40, 7, 65, 8, 28, 13, 0, 22, 64, 24, 2, 39, 58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20, 6, 4, 23, 32, 29, 36, 12, 19, 35, 10, 51, 5, 33, 15, 52, 62, 47, 18, 42, 25, 49], 'cur_cost': 107702.0}]
2025-08-03 15:44:38,002 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:38,002 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-03 15:44:38,003 - experts.management.collaboration_manager - INFO - 个体 11 利用路径生成报告: {'new_tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}
2025-08-03 15:44:38,003 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 114599.00)
2025-08-03 15:44:38,004 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:44:38,004 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:44:38,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:38,008 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:38,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:38,008 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12431.0, 路径长度: 66
2025-08-03 15:44:38,008 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 11, 4, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}
2025-08-03 15:44:38,009 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12431.00)
2025-08-03 15:44:38,009 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 15:44:38,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:38,009 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:38,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 113861.0
2025-08-03 15:44:38,100 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:44:38,101 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521]
2025-08-03 15:44:38,101 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:38,109 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:38,109 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}, {'tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}, {'tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}, {'tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}, {'tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}, {'tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}, {'tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}, {'tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}, {'tour': [0, 11, 4, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': array([42, 44, 22, 27, 15, 26,  4, 21, 17,  0, 23, 35, 25, 56, 51, 28, 41,
       59, 62, 13, 64, 43, 33, 65, 63,  3, 30, 58, 19, 32, 40, 39, 61, 18,
        6,  8, 46, 24, 29, 11, 31, 55, 12, 48, 14, 49,  1,  5, 16,  2, 47,
       53, 38, 20, 10, 37, 52, 50, 57, 36,  9, 45, 34,  7, 54, 60],
      dtype=int64), 'cur_cost': 113861.0}, {'tour': [8, 36, 56, 49, 29, 13, 41, 50, 1, 44, 57, 63, 15, 60, 12, 14, 2, 0, 34, 40, 23, 35, 6, 9, 55, 48, 10, 53, 52, 31, 58, 4, 5, 27, 20, 32, 62, 37, 39, 19, 51, 54, 25, 43, 7, 33, 38, 47, 3, 16, 61, 64, 28, 30, 21, 65, 18, 46, 26, 17, 42, 45, 24, 11, 22, 59], 'cur_cost': 108739.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [11, 43, 63, 1, 48, 61, 27, 3, 14, 41, 16, 57, 54, 9, 56, 21, 45, 53, 60, 37, 34, 50, 40, 7, 65, 8, 28, 13, 0, 22, 64, 24, 2, 39, 58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20, 6, 4, 23, 32, 29, 36, 12, 19, 35, 10, 51, 5, 33, 15, 52, 62, 47, 18, 42, 25, 49], 'cur_cost': 107702.0}]
2025-08-03 15:44:38,113 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:38,113 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 15:44:38,114 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([42, 44, 22, 27, 15, 26,  4, 21, 17,  0, 23, 35, 25, 56, 51, 28, 41,
       59, 62, 13, 64, 43, 33, 65, 63,  3, 30, 58, 19, 32, 40, 39, 61, 18,
        6,  8, 46, 24, 29, 11, 31, 55, 12, 48, 14, 49,  1,  5, 16,  2, 47,
       53, 38, 20, 10, 37, 52, 50, 57, 36,  9, 45, 34,  7, 54, 60],
      dtype=int64), 'cur_cost': 113861.0}
2025-08-03 15:44:38,114 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 113861.00)
2025-08-03 15:44:38,114 - experts.management.collaboration_manager - INFO - 为个体 14 生成利用路径
2025-08-03 15:44:38,115 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:38,115 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:38,115 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 14 处的路径，新成本: 105536.0
2025-08-03 15:44:39,157 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:44:39,157 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521]
2025-08-03 15:44:39,157 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:39,165 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:39,166 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}, {'tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}, {'tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}, {'tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}, {'tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}, {'tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}, {'tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}, {'tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}, {'tour': [0, 11, 4, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': array([42, 44, 22, 27, 15, 26,  4, 21, 17,  0, 23, 35, 25, 56, 51, 28, 41,
       59, 62, 13, 64, 43, 33, 65, 63,  3, 30, 58, 19, 32, 40, 39, 61, 18,
        6,  8, 46, 24, 29, 11, 31, 55, 12, 48, 14, 49,  1,  5, 16,  2, 47,
       53, 38, 20, 10, 37, 52, 50, 57, 36,  9, 45, 34,  7, 54, 60],
      dtype=int64), 'cur_cost': 113861.0}, {'tour': array([13, 16,  5, 43, 40, 42, 48,  0, 11,  3, 53, 18, 58, 27, 52,  4, 45,
       62, 46,  9,  6,  1, 39, 63, 35, 23, 14, 55, 28, 54, 25,  7, 51, 10,
       47, 19, 22,  2, 37, 31, 21, 44, 50, 61, 59, 41, 56, 29, 65, 38, 64,
       12, 32, 36, 33, 57, 17, 60,  8, 24, 30, 34, 15, 26, 49, 20],
      dtype=int64), 'cur_cost': 105536.0}, {'tour': [0, 13, 21, 17, 12, 22, 23, 16, 18, 19, 20, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12462.0}, {'tour': [0, 12, 8, 17, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14730.0}, {'tour': [21, 22, 31, 34, 20, 6, 12, 33, 4, 17, 14, 24, 29, 18, 26, 9, 61, 5, 60, 63, 52, 23, 1, 10, 54, 3, 2, 0, 56, 55, 47, 51, 45, 38, 41, 27, 11, 64, 59, 58, 62, 7, 65, 44, 39, 42, 43, 40, 46, 13, 25, 19, 28, 30, 16, 15, 8, 37, 49, 36, 35, 32, 48, 50, 53, 57], 'cur_cost': 59078.0}, {'tour': [0, 10, 19, 17, 12, 22, 23, 16, 18, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [11, 43, 63, 1, 48, 61, 27, 3, 14, 41, 16, 57, 54, 9, 56, 21, 45, 53, 60, 37, 34, 50, 40, 7, 65, 8, 28, 13, 0, 22, 64, 24, 2, 39, 58, 17, 55, 30, 31, 59, 46, 44, 26, 38, 20, 6, 4, 23, 32, 29, 36, 12, 19, 35, 10, 51, 5, 33, 15, 52, 62, 47, 18, 42, 25, 49], 'cur_cost': 107702.0}]
2025-08-03 15:44:39,171 - ExploitationExpert - INFO - 局部搜索耗时: 1.06秒
2025-08-03 15:44:39,171 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 15:44:39,172 - experts.management.collaboration_manager - INFO - 个体 14 利用路径生成报告: {'new_tour': array([13, 16,  5, 43, 40, 42, 48,  0, 11,  3, 53, 18, 58, 27, 52,  4, 45,
       62, 46,  9,  6,  1, 39, 63, 35, 23, 14, 55, 28, 54, 25,  7, 51, 10,
       47, 19, 22,  2, 37, 31, 21, 44, 50, 61, 59, 41, 56, 29, 65, 38, 64,
       12, 32, 36, 33, 57, 17, 60,  8, 24, 30, 34, 15, 26, 49, 20],
      dtype=int64), 'cur_cost': 105536.0}
2025-08-03 15:44:39,173 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 105536.00)
2025-08-03 15:44:39,173 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:44:39,173 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:44:39,173 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:39,177 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:39,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:39,178 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14734.0, 路径长度: 66
2025-08-03 15:44:39,178 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 14, 1, 23, 16, 18, 12, 22, 15, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14734.0}
2025-08-03 15:44:39,179 - experts.management.collaboration_manager - INFO - 个体 15 保留原路径 (成本: 14734.00)
2025-08-03 15:44:39,179 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:44:39,179 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:44:39,180 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:39,193 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:39,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:39,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53297.0, 路径长度: 66
2025-08-03 15:44:39,194 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [54, 63, 56, 62, 60, 13, 18, 26, 17, 9, 23, 12, 30, 11, 64, 6, 8, 22, 28, 20, 34, 27, 36, 16, 10, 4, 59, 57, 19, 15, 32, 29, 21, 33, 2, 61, 14, 7, 25, 31, 24, 0, 65, 40, 44, 51, 47, 48, 38, 45, 43, 49, 39, 46, 41, 37, 35, 3, 1, 55, 52, 58, 53, 5, 50, 42], 'cur_cost': 53297.0}
2025-08-03 15:44:39,195 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 53297.00)
2025-08-03 15:44:39,195 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 15:44:39,195 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 15:44:39,195 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:39,199 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 15:44:39,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:39,200 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14207.0, 路径长度: 66
2025-08-03 15:44:39,200 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 8, 25, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14207.0}
2025-08-03 15:44:39,200 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 14207.00)
2025-08-03 15:44:39,201 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:44:39,201 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:44:39,201 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:44:39,212 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 15:44:39,213 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:44:39,216 - ExplorationExpert - INFO - 探索路径生成完成，成本: 47630.0, 路径长度: 66
2025-08-03 15:44:39,217 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [47, 17, 11, 5, 12, 27, 35, 4, 65, 61, 56, 52, 6, 10, 59, 23, 16, 25, 34, 28, 7, 9, 15, 1, 63, 57, 58, 64, 3, 8, 22, 32, 2, 0, 60, 49, 46, 39, 20, 19, 24, 37, 26, 31, 33, 18, 14, 13, 43, 38, 44, 48, 51, 45, 40, 41, 50, 36, 30, 21, 42, 55, 62, 53, 54, 29], 'cur_cost': 47630.0}
2025-08-03 15:44:39,218 - experts.management.collaboration_manager - INFO - 个体 18 保留原路径 (成本: 47630.00)
2025-08-03 15:44:39,219 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 15:44:39,219 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:44:39,219 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:44:39,220 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 84994.0
2025-08-03 15:44:39,304 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:44:39,304 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9532, 9538, 9541.0, 99530.0, 9521, 9521]
2025-08-03 15:44:39,304 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 18, 16, 19, 27, 37, 36, 26,
       25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 60,  3, 34, 32,  9, 38, 28, 15, 25, 65,  1, 44, 26, 40, 14,  7,
       16, 53, 61,  5, 19,  2, 46, 55, 11, 21, 58, 20, 33, 48, 37,  6, 42,
       59,  8, 62, 57, 36, 17, 27, 39, 52, 22,  4, 10, 64, 54, 45, 50, 31,
       12, 49, 24, 56, 47, 43, 41, 13, 51, 18, 30, 35, 29, 23, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 15:44:39,313 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:44:39,313 - ExploitationExpert - INFO - populations: [{'tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}, {'tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}, {'tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}, {'tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}, {'tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}, {'tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}, {'tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}, {'tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}, {'tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}, {'tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}, {'tour': [0, 11, 4, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': array([42, 44, 22, 27, 15, 26,  4, 21, 17,  0, 23, 35, 25, 56, 51, 28, 41,
       59, 62, 13, 64, 43, 33, 65, 63,  3, 30, 58, 19, 32, 40, 39, 61, 18,
        6,  8, 46, 24, 29, 11, 31, 55, 12, 48, 14, 49,  1,  5, 16,  2, 47,
       53, 38, 20, 10, 37, 52, 50, 57, 36,  9, 45, 34,  7, 54, 60],
      dtype=int64), 'cur_cost': 113861.0}, {'tour': array([13, 16,  5, 43, 40, 42, 48,  0, 11,  3, 53, 18, 58, 27, 52,  4, 45,
       62, 46,  9,  6,  1, 39, 63, 35, 23, 14, 55, 28, 54, 25,  7, 51, 10,
       47, 19, 22,  2, 37, 31, 21, 44, 50, 61, 59, 41, 56, 29, 65, 38, 64,
       12, 32, 36, 33, 57, 17, 60,  8, 24, 30, 34, 15, 26, 49, 20],
      dtype=int64), 'cur_cost': 105536.0}, {'tour': [0, 14, 1, 23, 16, 18, 12, 22, 15, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14734.0}, {'tour': [54, 63, 56, 62, 60, 13, 18, 26, 17, 9, 23, 12, 30, 11, 64, 6, 8, 22, 28, 20, 34, 27, 36, 16, 10, 4, 59, 57, 19, 15, 32, 29, 21, 33, 2, 61, 14, 7, 25, 31, 24, 0, 65, 40, 44, 51, 47, 48, 38, 45, 43, 49, 39, 46, 41, 37, 35, 3, 1, 55, 52, 58, 53, 5, 50, 42], 'cur_cost': 53297.0}, {'tour': [0, 8, 25, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14207.0}, {'tour': [47, 17, 11, 5, 12, 27, 35, 4, 65, 61, 56, 52, 6, 10, 59, 23, 16, 25, 34, 28, 7, 9, 15, 1, 63, 57, 58, 64, 3, 8, 22, 32, 2, 0, 60, 49, 46, 39, 20, 19, 24, 37, 26, 31, 33, 18, 14, 13, 43, 38, 44, 48, 51, 45, 40, 41, 50, 36, 30, 21, 42, 55, 62, 53, 54, 29], 'cur_cost': 47630.0}, {'tour': array([10, 63, 59, 56, 50, 47, 62, 21, 41, 42, 17,  0,  9, 19, 20, 15,  8,
       14, 53, 13, 54, 35, 22, 46, 45,  5, 32, 12, 26, 30, 49, 33, 24, 38,
       44, 29, 25, 55, 31, 34, 52, 37, 28,  6,  1,  4, 64, 57, 65, 27,  3,
       16, 58, 60, 40, 51, 61, 39, 48, 18,  7, 23, 43, 36, 11,  2],
      dtype=int64), 'cur_cost': 84994.0}]
2025-08-03 15:44:39,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:44:39,321 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 15:44:39,324 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([10, 63, 59, 56, 50, 47, 62, 21, 41, 42, 17,  0,  9, 19, 20, 15,  8,
       14, 53, 13, 54, 35, 22, 46, 45,  5, 32, 12, 26, 30, 49, 33, 24, 38,
       44, 29, 25, 55, 31, 34, 52, 37, 28,  6,  1,  4, 64, 57, 65, 27,  3,
       16, 58, 60, 40, 51, 61, 39, 48, 18,  7, 23, 43, 36, 11,  2],
      dtype=int64), 'cur_cost': 84994.0}
2025-08-03 15:44:39,325 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 84994.00)
2025-08-03 15:44:39,325 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 15:44:39,325 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 15:44:39,326 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [55, 62, 7, 65, 23, 29, 37, 31, 36, 28, 12, 26, 20, 24, 3, 54, 17, 9, 63, 5, 1, 59, 16, 13, 19, 15, 18, 32, 0, 52, 57, 64, 2, 6, 8, 4, 10, 21, 30, 14, 25, 40, 49, 44, 41, 48, 50, 51, 42, 45, 39, 38, 46, 43, 33, 34, 11, 53, 60, 56, 58, 22, 35, 27, 47, 61], 'cur_cost': 48630.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 16,  7, 19, 18,  8, 32, 58, 34, 22, 63, 62, 24,  2, 55, 37, 54,
       50, 27, 10, 41, 29, 35, 13,  0, 28, 36, 43,  9, 51, 61, 14, 44, 60,
       49, 20, 26, 65, 57, 12,  4, 17, 52, 45, 39, 30, 23, 25, 59, 33, 42,
        3, 21,  6, 38, 31, 15, 46, 56,  5, 48,  1, 64, 47, 40, 11],
      dtype=int64), 'cur_cost': 119723.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12819.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 41, 30, 64, 44, 20, 46, 22,  6, 14, 33, 45, 52, 32, 16,  2, 58,
       42, 15,  3, 61, 28, 47, 49,  7, 65, 25,  4, 13, 62, 31,  9, 54, 19,
       35,  8, 29, 43, 37, 50, 56, 12,  1, 60, 21, 27, 63, 18, 23,  5, 36,
       11, 51,  0, 40, 48, 59, 26, 55, 24, 39, 38, 53, 17, 57, 34],
      dtype=int64), 'cur_cost': 126063.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 57, 1, 15, 33, 21, 12, 17, 25, 20, 26, 13, 10, 56, 23, 2, 5, 62, 6, 27, 9, 64, 63, 65, 58, 18, 40, 34, 3, 61, 19, 28, 30, 8, 7, 55, 43, 46, 44, 41, 47, 45, 49, 51, 38, 16, 11, 14, 31, 32, 35, 37, 22, 48, 42, 39, 0, 52, 54, 53, 60, 59, 36, 24, 29, 50], 'cur_cost': 60768.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12850.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [55, 18, 23, 7, 1, 36, 56, 0, 10, 9, 33, 60, 19, 21, 40, 28, 6, 57, 41, 48, 20, 38, 50, 8, 32, 39, 46, 30, 49, 34, 53, 31, 44, 12, 24, 65, 29, 2, 17, 25, 61, 47, 26, 54, 64, 11, 52, 16, 22, 51, 58, 43, 35, 3, 42, 5, 62, 4, 45, 27, 59, 37, 14, 13, 15, 63], 'cur_cost': 109236.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 9, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12308.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 8, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23, 22, 12, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 15, 17, 8, 12, 21, 3, 28, 5, 57, 6, 48, 58, 38, 60, 39, 40, 14, 22, 64, 11, 31, 55, 19, 25, 46, 51, 27, 0, 50, 53, 42, 16, 35, 43, 30, 61, 33, 65, 54, 24, 36, 44, 63, 23, 13, 29, 9, 49, 26, 7, 45, 41, 52, 34, 59, 20, 10, 4, 37, 62, 47, 18, 32, 56, 1], 'cur_cost': 116131.0}}, {'individual': 10, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 11, 8, 2, 6, 4, 5, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}}, {'individual': 11, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 21, 47, 46, 37,  6, 43, 58, 12, 39,  1, 36, 29, 62, 10, 18, 61,
       38, 20, 57, 27, 56, 51, 34, 16,  7, 31, 59, 13, 40, 35, 45, 52, 49,
       14,  9, 15, 11, 65, 23, 17, 48, 24, 54, 25, 33, 53,  5, 63,  2, 19,
        4, 30, 41, 44, 64, 55, 32, 50,  8, 42, 22,  0, 60,  3, 26],
      dtype=int64), 'cur_cost': 114599.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 4, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([42, 44, 22, 27, 15, 26,  4, 21, 17,  0, 23, 35, 25, 56, 51, 28, 41,
       59, 62, 13, 64, 43, 33, 65, 63,  3, 30, 58, 19, 32, 40, 39, 61, 18,
        6,  8, 46, 24, 29, 11, 31, 55, 12, 48, 14, 49,  1,  5, 16,  2, 47,
       53, 38, 20, 10, 37, 52, 50, 57, 36,  9, 45, 34,  7, 54, 60],
      dtype=int64), 'cur_cost': 113861.0}}, {'individual': 14, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 16,  5, 43, 40, 42, 48,  0, 11,  3, 53, 18, 58, 27, 52,  4, 45,
       62, 46,  9,  6,  1, 39, 63, 35, 23, 14, 55, 28, 54, 25,  7, 51, 10,
       47, 19, 22,  2, 37, 31, 21, 44, 50, 61, 59, 41, 56, 29, 65, 38, 64,
       12, 32, 36, 33, 57, 17, 60,  8, 24, 30, 34, 15, 26, 49, 20],
      dtype=int64), 'cur_cost': 105536.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 23, 16, 18, 12, 22, 15, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14734.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [54, 63, 56, 62, 60, 13, 18, 26, 17, 9, 23, 12, 30, 11, 64, 6, 8, 22, 28, 20, 34, 27, 36, 16, 10, 4, 59, 57, 19, 15, 32, 29, 21, 33, 2, 61, 14, 7, 25, 31, 24, 0, 65, 40, 44, 51, 47, 48, 38, 45, 43, 49, 39, 46, 41, 37, 35, 3, 1, 55, 52, 58, 53, 5, 50, 42], 'cur_cost': 53297.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 25, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14207.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [47, 17, 11, 5, 12, 27, 35, 4, 65, 61, 56, 52, 6, 10, 59, 23, 16, 25, 34, 28, 7, 9, 15, 1, 63, 57, 58, 64, 3, 8, 22, 32, 2, 0, 60, 49, 46, 39, 20, 19, 24, 37, 26, 31, 33, 18, 14, 13, 43, 38, 44, 48, 51, 45, 40, 41, 50, 36, 30, 21, 42, 55, 62, 53, 54, 29], 'cur_cost': 47630.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 63, 59, 56, 50, 47, 62, 21, 41, 42, 17,  0,  9, 19, 20, 15,  8,
       14, 53, 13, 54, 35, 22, 46, 45,  5, 32, 12, 26, 30, 49, 33, 24, 38,
       44, 29, 25, 55, 31, 34, 52, 37, 28,  6,  1,  4, 64, 57, 65, 27,  3,
       16, 58, 60, 40, 51, 61, 39, 48, 18,  7, 23, 43, 36, 11,  2],
      dtype=int64), 'cur_cost': 84994.0}}]
2025-08-03 15:44:39,329 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:44:39,330 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:44:39,342 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12308.000, 多样性=0.947
2025-08-03 15:44:39,342 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-03 15:44:39,343 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-03 15:44:39,343 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:44:39,346 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04883803443589934, 'best_improvement': 0.0063776539920884796}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0038600318872200243}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0520742634483596, 'recent_improvements': [0.08023230608199987, -0.017327381408808042, -0.023916220814719346], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 15, 'new_count': 15, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8139971139971139, 'new_diversity': 0.8139971139971139, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 15:44:39,352 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-03 15:44:39,364 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 15:44:39,365 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_154439.solution
2025-08-03 15:44:39,365 - __main__ - INFO - 实例 composite13_66 处理完成
