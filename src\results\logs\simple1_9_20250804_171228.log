2025-08-04 17:12:28,002 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:12:28,003 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:12:28,024 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:28,027 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=817.000, 多样性=0.877
2025-08-04 17:12:28,028 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:12:28,029 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.877
2025-08-04 17:12:28,030 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:12:28,059 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:12:28,060 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:12:28,060 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:12:28,061 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:12:28,404 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -13.380, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.789
2025-08-04 17:12:28,405 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:12:28,405 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:12:28,406 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:12:28,440 - visualization.landscape_visualizer - INFO - 插值约束: 22 个点被约束到最小值 817.00
2025-08-04 17:12:28,820 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:12:32,557 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_171232.html
2025-08-04 17:12:32,601 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_171232.html
2025-08-04 17:12:32,602 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:12:32,603 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:12:32,603 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.5446秒
2025-08-04 17:12:32,603 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:12:32,604 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13.379999999999999, 'local_optima_density': 0.1, 'gradient_variance': 8825.8356, 'cluster_count': 0}, 'population_state': {'diversity': 0.788888888888889, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9138646883853215, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13.380)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.789)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298748.4047341, 'performance_metrics': {}}}
2025-08-04 17:12:32,606 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:12:32,606 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:12:32,606 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:12:32,606 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:12:32,607 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:32,607 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:12:32,607 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:32,607 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:32,608 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:12:32,608 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:32,608 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:32,609 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:12:32,609 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:12:32,609 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:12:32,609 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:12:32,610 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:32,617 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:32,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:32,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 990.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:32,754 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 4, 5, 1, 7, 3, 8, 6], 'cur_cost': 990.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:32,755 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 990.00)
2025-08-04 17:12:32,755 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:12:32,755 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:12:32,756 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:32,756 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:32,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:32,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 794.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:32,757 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 6, 7, 5, 8, 3, 4, 2, 0], 'cur_cost': 794.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:32,757 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 794.00)
2025-08-04 17:12:32,757 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:12:32,757 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:12:32,758 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:32,758 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:32,758 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:32,758 - ExplorationExpert - INFO - 探索路径生成完成，成本: 957.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:32,759 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:32,759 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 957.00)
2025-08-04 17:12:32,759 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:12:32,759 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:12:32,759 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:32,760 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:32,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:32,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 865.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:32,761 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 7, 3, 5, 8, 2, 0, 6], 'cur_cost': 865.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:32,761 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 865.00)
2025-08-04 17:12:32,761 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:12:32,761 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:32,763 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:32,765 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1176.0
2025-08-04 17:12:33,945 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:12:33,945 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:12:33,945 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:12:33,946 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:33,946 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 5, 1, 7, 3, 8, 6], 'cur_cost': 990.0}, {'tour': [1, 6, 7, 5, 8, 3, 4, 2, 0], 'cur_cost': 794.0}, {'tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0}, {'tour': [1, 4, 7, 3, 5, 8, 2, 0, 6], 'cur_cost': 865.0}, {'tour': array([4, 5, 7, 2, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1176.0}, {'tour': array([3, 4, 0, 1, 6, 7, 8, 5, 2], dtype=int64), 'cur_cost': 956.0}, {'tour': array([4, 1, 6, 5, 7, 3, 8, 2, 0], dtype=int64), 'cur_cost': 817.0}, {'tour': array([2, 7, 1, 4, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 1034.0}, {'tour': array([1, 0, 5, 8, 3, 6, 7, 2, 4], dtype=int64), 'cur_cost': 821.0}, {'tour': array([1, 4, 2, 3, 0, 5, 8, 7, 6], dtype=int64), 'cur_cost': 948.0}]
2025-08-04 17:12:33,948 - ExploitationExpert - INFO - 局部搜索耗时: 1.18秒，最大迭代次数: 10
2025-08-04 17:12:33,948 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:12:33,949 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 5, 7, 2, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1176.0, 'intermediate_solutions': [{'tour': array([4, 1, 3, 2, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 1, 3, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 4, 1, 3, 7, 5, 8, 0], dtype=int64), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 4, 1, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 6, 2, 4, 1, 7, 5, 8, 0], dtype=int64), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:33,950 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1176.00)
2025-08-04 17:12:33,950 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:12:33,950 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:12:33,950 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:33,951 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:33,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:33,951 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1019.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:33,952 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 2, 6, 8, 4, 0, 7, 3, 5], 'cur_cost': 1019.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:33,952 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1019.00)
2025-08-04 17:12:33,952 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:12:33,952 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:12:33,953 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:33,953 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:33,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:33,954 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1027.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:33,954 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 5, 4, 6, 3, 7, 0, 1, 8], 'cur_cost': 1027.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:33,954 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1027.00)
2025-08-04 17:12:33,954 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:12:33,954 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:33,955 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:33,955 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 853.0
2025-08-04 17:12:35,689 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:12:35,689 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-04 17:12:35,690 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:12:35,691 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:35,691 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 5, 1, 7, 3, 8, 6], 'cur_cost': 990.0}, {'tour': [1, 6, 7, 5, 8, 3, 4, 2, 0], 'cur_cost': 794.0}, {'tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0}, {'tour': [1, 4, 7, 3, 5, 8, 2, 0, 6], 'cur_cost': 865.0}, {'tour': array([4, 5, 7, 2, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1176.0}, {'tour': [1, 2, 6, 8, 4, 0, 7, 3, 5], 'cur_cost': 1019.0}, {'tour': [2, 5, 4, 6, 3, 7, 0, 1, 8], 'cur_cost': 1027.0}, {'tour': array([0, 3, 7, 6, 5, 2, 8, 4, 1], dtype=int64), 'cur_cost': 853.0}, {'tour': array([1, 0, 5, 8, 3, 6, 7, 2, 4], dtype=int64), 'cur_cost': 821.0}, {'tour': array([1, 4, 2, 3, 0, 5, 8, 7, 6], dtype=int64), 'cur_cost': 948.0}]
2025-08-04 17:12:35,693 - ExploitationExpert - INFO - 局部搜索耗时: 1.74秒，最大迭代次数: 10
2025-08-04 17:12:35,693 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:12:35,694 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 3, 7, 6, 5, 2, 8, 4, 1], dtype=int64), 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': array([1, 7, 2, 4, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 7, 2, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 1, 7, 2, 8, 5, 3, 6], dtype=int64), 'cur_cost': 905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 1, 7, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 1, 7, 8, 5, 3, 6], dtype=int64), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:35,695 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 853.00)
2025-08-04 17:12:35,695 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:12:35,695 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:12:35,695 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,696 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:35,696 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,696 - ExplorationExpert - INFO - 探索路径生成完成，成本: 802.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:35,697 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,697 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 802.00)
2025-08-04 17:12:35,697 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:12:35,697 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:12:35,697 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,698 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:35,698 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,699 - ExplorationExpert - INFO - 探索路径生成完成，成本: 879.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:12:35,699 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,699 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 879.00)
2025-08-04 17:12:35,700 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:12:35,700 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:12:35,701 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 5, 1, 7, 3, 8, 6], 'cur_cost': 990.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 5, 8, 3, 4, 2, 0], 'cur_cost': 794.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 3, 5, 8, 2, 0, 6], 'cur_cost': 865.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 5, 7, 2, 6, 3, 0, 8, 1], dtype=int64), 'cur_cost': 1176.0, 'intermediate_solutions': [{'tour': array([4, 1, 3, 2, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 4, 1, 3, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 4, 1, 3, 7, 5, 8, 0], dtype=int64), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 4, 1, 6, 7, 5, 8, 0], dtype=int64), 'cur_cost': 942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 6, 2, 4, 1, 7, 5, 8, 0], dtype=int64), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 6, 8, 4, 0, 7, 3, 5], 'cur_cost': 1019.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 4, 6, 3, 7, 0, 1, 8], 'cur_cost': 1027.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 7, 6, 5, 2, 8, 4, 1], dtype=int64), 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': array([1, 7, 2, 4, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 7, 2, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 1036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 1, 7, 2, 8, 5, 3, 6], dtype=int64), 'cur_cost': 905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 1, 7, 0, 8, 5, 3, 6], dtype=int64), 'cur_cost': 958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 1, 7, 8, 5, 3, 6], dtype=int64), 'cur_cost': 980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:12:35,703 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:12:35,703 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:35,704 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=794.000, 多样性=0.889
2025-08-04 17:12:35,705 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:12:35,705 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:12:35,705 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:12:35,706 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04550086106979217, 'best_improvement': 0.028151774785801713}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014084507042253587}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:12:35,714 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:12:35,714 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:12:35,714 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:12:35,715 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:35,715 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=794.000, 多样性=0.889
2025-08-04 17:12:35,716 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:12:35,717 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.889
2025-08-04 17:12:35,717 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:12:35,718 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:12:35,721 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:12:35,721 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:12:35,722 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:12:35,722 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:12:35,727 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 4.117, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.654
2025-08-04 17:12:35,727 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:12:35,728 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:12:35,728 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:12:35,732 - visualization.landscape_visualizer - INFO - 插值约束: 95 个点被约束到最小值 680.00
2025-08-04 17:12:35,737 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:12:35,815 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_171235.html
2025-08-04 17:12:35,891 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_171235.html
2025-08-04 17:12:35,892 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:12:35,892 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:12:35,892 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1712秒
2025-08-04 17:12:35,893 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 4.11666666666666, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 24372.789722222224, 'cluster_count': 0}, 'population_state': {'diversity': 0.6540404040404041, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.957742056202218, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 4.117)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298755.7277002, 'performance_metrics': {}}}
2025-08-04 17:12:35,894 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:12:35,894 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:12:35,895 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:12:35,895 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:12:35,895 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:12:35,895 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:12:35,895 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:12:35,896 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:35,896 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:12:35,896 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:12:35,896 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:35,897 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:12:35,897 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:12:35,897 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:12:35,898 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:12:35,898 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,898 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:35,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,900 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1036.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:35,900 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 0, 6, 2, 1, 8, 3, 7, 5], 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 6, 1, 7, 3, 8, 5], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 5, 1, 7, 6, 8, 3], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 4, 5, 7, 3, 8, 6], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,901 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1036.00)
2025-08-04 17:12:35,901 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:12:35,901 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:12:35,901 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,901 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:35,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1062.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:35,903 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 4, 3, 7, 1, 6, 8, 2, 0], 'cur_cost': 1062.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 8, 5, 4, 2, 0], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 5, 8, 3, 2, 4, 0], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 8, 3, 4, 2, 0, 6], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,903 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1062.00)
2025-08-04 17:12:35,903 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:12:35,903 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:12:35,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,904 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:35,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,904 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 979.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:35,905 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 6, 0, 8, 5, 3, 7, 2, 1], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [4, 0, 2, 3, 7, 6, 5, 8, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 8, 5, 6, 7, 3, 4, 0], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,905 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 979.00)
2025-08-04 17:12:35,906 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:12:35,906 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:12:35,906 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,906 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:35,906 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,907 - ExplorationExpert - INFO - 探索路径生成完成，成本: 731.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:35,907 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 6, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 731.0, 'intermediate_solutions': [{'tour': [1, 2, 7, 3, 5, 8, 4, 0, 6], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 5, 3, 7, 4, 1, 0, 6], 'cur_cost': 851.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 3, 5, 8, 2, 0, 6, 1], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,908 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 731.00)
2025-08-04 17:12:35,908 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 17:12:35,908 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:35,908 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:35,909 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 979.0
2025-08-04 17:12:35,974 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:35,975 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:35,975 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:35,976 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:35,976 - ExploitationExpert - INFO - populations: [{'tour': [4, 0, 6, 2, 1, 8, 3, 7, 5], 'cur_cost': 1036.0}, {'tour': [5, 4, 3, 7, 1, 6, 8, 2, 0], 'cur_cost': 1062.0}, {'tour': [4, 6, 0, 8, 5, 3, 7, 2, 1], 'cur_cost': 979.0}, {'tour': [3, 6, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 731.0}, {'tour': array([1, 5, 3, 7, 4, 8, 6, 0, 2], dtype=int64), 'cur_cost': 979.0}, {'tour': [1, 2, 6, 8, 4, 0, 7, 3, 5], 'cur_cost': 1019.0}, {'tour': [2, 5, 4, 6, 3, 7, 0, 1, 8], 'cur_cost': 1027.0}, {'tour': [0, 3, 7, 6, 5, 2, 8, 4, 1], 'cur_cost': 853.0}, {'tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0}, {'tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0}]
2025-08-04 17:12:35,976 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:12:35,977 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:12:35,977 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([1, 5, 3, 7, 4, 8, 6, 0, 2], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([7, 5, 4, 2, 6, 3, 0, 8, 1]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 5, 4, 6, 3, 0, 8, 1]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 7, 5, 4, 3, 0, 8, 1]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 7, 5, 6, 3, 0, 8, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 6, 2, 7, 5, 3, 0, 8, 1]), 'cur_cost': 1145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:35,978 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 979.00)
2025-08-04 17:12:35,978 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:12:35,978 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:12:35,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:35,979 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:35,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:35,980 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1103.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:35,980 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 0, 4, 6, 8, 3, 7, 1, 5], 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 8, 4, 0, 7, 5, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 2, 1, 0, 7, 3, 5], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 6, 8, 4, 0, 7, 5, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:35,981 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1103.00)
2025-08-04 17:12:35,981 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:12:35,981 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:35,981 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:35,982 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1129.0
2025-08-04 17:12:36,042 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,043 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,043 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,044 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,044 - ExploitationExpert - INFO - populations: [{'tour': [4, 0, 6, 2, 1, 8, 3, 7, 5], 'cur_cost': 1036.0}, {'tour': [5, 4, 3, 7, 1, 6, 8, 2, 0], 'cur_cost': 1062.0}, {'tour': [4, 6, 0, 8, 5, 3, 7, 2, 1], 'cur_cost': 979.0}, {'tour': [3, 6, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 731.0}, {'tour': array([1, 5, 3, 7, 4, 8, 6, 0, 2], dtype=int64), 'cur_cost': 979.0}, {'tour': [2, 0, 4, 6, 8, 3, 7, 1, 5], 'cur_cost': 1103.0}, {'tour': array([3, 6, 2, 1, 8, 5, 0, 7, 4], dtype=int64), 'cur_cost': 1129.0}, {'tour': [0, 3, 7, 6, 5, 2, 8, 4, 1], 'cur_cost': 853.0}, {'tour': [5, 6, 3, 7, 8, 4, 2, 0, 1], 'cur_cost': 802.0}, {'tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0}]
2025-08-04 17:12:36,045 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:12:36,045 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:12:36,045 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 6, 2, 1, 8, 5, 0, 7, 4], dtype=int64), 'cur_cost': 1129.0, 'intermediate_solutions': [{'tour': array([4, 5, 2, 6, 3, 7, 0, 1, 8]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 5, 2, 3, 7, 0, 1, 8]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 4, 5, 2, 7, 0, 1, 8]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 5, 3, 7, 0, 1, 8]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 6, 4, 5, 7, 0, 1, 8]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,046 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1129.00)
2025-08-04 17:12:36,046 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:12:36,046 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:12:36,047 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,047 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:36,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 853.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,049 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 7, 3, 8, 4, 0, 1, 6, 5], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 1, 5, 2, 8, 4, 6], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 6, 5, 2, 8, 1, 4], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 6, 5, 0, 2, 8, 4, 1], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,049 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 853.00)
2025-08-04 17:12:36,050 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:12:36,050 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:12:36,050 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,050 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,051 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1159.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,052 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 6, 2, 5, 0, 3, 8, 7, 1], 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': [5, 6, 1, 7, 8, 4, 2, 0, 3], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 6, 3, 7, 8, 4, 2, 0], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,052 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1159.00)
2025-08-04 17:12:36,052 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:12:36,053 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:12:36,053 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,053 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,054 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,054 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,054 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,054 - ExplorationExpert - INFO - 探索路径生成完成，成本: 882.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,054 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 7, 5, 0, 4, 2, 8, 3, 1], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 8, 3, 2, 7, 4, 5], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 8, 3, 5, 4, 7, 2], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,055 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 882.00)
2025-08-04 17:12:36,055 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:12:36,055 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:12:36,056 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 6, 2, 1, 8, 3, 7, 5], 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': [0, 2, 4, 6, 1, 7, 3, 8, 5], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 5, 1, 7, 6, 8, 3], 'cur_cost': 1038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 2, 4, 5, 7, 3, 8, 6], 'cur_cost': 835.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 3, 7, 1, 6, 8, 2, 0], 'cur_cost': 1062.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 8, 5, 4, 2, 0], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 5, 8, 3, 2, 4, 0], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 8, 3, 4, 2, 0, 6], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 8, 5, 3, 7, 2, 1], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [4, 0, 2, 3, 7, 6, 5, 8, 1], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 8, 5, 6, 7, 3, 4, 0], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 4, 3, 7, 6, 5, 8, 1], 'cur_cost': 957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 7, 0, 1, 4, 2, 8, 5], 'cur_cost': 731.0, 'intermediate_solutions': [{'tour': [1, 2, 7, 3, 5, 8, 4, 0, 6], 'cur_cost': 885.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 5, 3, 7, 4, 1, 0, 6], 'cur_cost': 851.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 3, 5, 8, 2, 0, 6, 1], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 5, 3, 7, 4, 8, 6, 0, 2], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([7, 5, 4, 2, 6, 3, 0, 8, 1]), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 7, 5, 4, 6, 3, 0, 8, 1]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 2, 7, 5, 4, 3, 0, 8, 1]), 'cur_cost': 1209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 7, 5, 6, 3, 0, 8, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 6, 2, 7, 5, 3, 0, 8, 1]), 'cur_cost': 1145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 4, 6, 8, 3, 7, 1, 5], 'cur_cost': 1103.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 8, 4, 0, 7, 5, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 2, 1, 0, 7, 3, 5], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 6, 8, 4, 0, 7, 5, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 2, 1, 8, 5, 0, 7, 4], dtype=int64), 'cur_cost': 1129.0, 'intermediate_solutions': [{'tour': array([4, 5, 2, 6, 3, 7, 0, 1, 8]), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 5, 2, 3, 7, 0, 1, 8]), 'cur_cost': 1116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 4, 5, 2, 7, 0, 1, 8]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 5, 3, 7, 0, 1, 8]), 'cur_cost': 1005.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 6, 4, 5, 7, 0, 1, 8]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 3, 8, 4, 0, 1, 6, 5], 'cur_cost': 853.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 1, 5, 2, 8, 4, 6], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 6, 5, 2, 8, 1, 4], 'cur_cost': 976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 7, 6, 5, 0, 2, 8, 4, 1], 'cur_cost': 933.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 5, 0, 3, 8, 7, 1], 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': [5, 6, 1, 7, 8, 4, 2, 0, 3], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 7, 3, 8, 4, 2, 0, 1], 'cur_cost': 775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 6, 3, 7, 8, 4, 2, 0], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 0, 4, 2, 8, 3, 1], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 8, 3, 2, 7, 4, 5], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 8, 3, 5, 4, 7, 2], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 1, 8, 3, 5, 7, 4, 2], 'cur_cost': 879.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:12:36,059 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:12:36,059 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,060 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=731.000, 多样性=0.881
2025-08-04 17:12:36,060 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:12:36,060 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:12:36,060 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:12:36,061 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.011648641649891251, 'best_improvement': 0.07934508816120907}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.008333333333333347}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:12:36,061 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:12:36,061 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:12:36,062 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:12:36,062 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,063 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=731.000, 多样性=0.881
2025-08-04 17:12:36,063 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:12:36,063 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.881
2025-08-04 17:12:36,064 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:12:36,064 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:12:36,066 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:12:36,066 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:12:36,066 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:12:36,066 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:12:36,073 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 38.938, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.614
2025-08-04 17:12:36,074 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:12:36,074 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:12:36,074 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:12:36,077 - visualization.landscape_visualizer - INFO - 插值约束: 46 个点被约束到最小值 680.00
2025-08-04 17:12:36,081 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:12:36,157 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_171236.html
2025-08-04 17:12:36,193 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_171236.html
2025-08-04 17:12:36,194 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:12:36,194 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:12:36,194 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1279秒
2025-08-04 17:12:36,194 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 38.93846153846153, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 38284.80852071005, 'cluster_count': 0}, 'population_state': {'diversity': 0.6143984220907298, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.957713559837711, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 38.938)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298756.07417, 'performance_metrics': {}}}
2025-08-04 17:12:36,195 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:12:36,196 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:12:36,196 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:12:36,196 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:12:36,197 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:12:36,197 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:12:36,197 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:12:36,197 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:36,197 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:12:36,198 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore']
2025-08-04 17:12:36,198 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:36,199 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:12:36,199 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:12:36,199 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:12:36,199 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:12:36,199 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,200 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1183.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,201 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 6, 2, 0, 8, 3, 1, 7], 'cur_cost': 1183.0, 'intermediate_solutions': [{'tour': [8, 0, 6, 2, 1, 4, 3, 7, 5], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 0, 1, 8, 3, 7, 5], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 2, 1, 6, 8, 3, 7, 5], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,202 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1183.00)
2025-08-04 17:12:36,202 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:12:36,202 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:12:36,202 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,202 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:36,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,203 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,203 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,203 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,203 - ExplorationExpert - INFO - 探索路径生成完成，成本: 978.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,204 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 7, 1, 6, 8, 2, 4], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 6, 1, 7, 3, 8, 2, 0], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 3, 7, 6, 8, 1, 2, 0], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,204 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 978.00)
2025-08-04 17:12:36,204 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:12:36,204 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:12:36,205 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,205 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,205 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,205 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,206 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,206 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1138.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,206 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0, 'intermediate_solutions': [{'tour': [4, 6, 7, 8, 5, 3, 0, 2, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 5, 8, 0, 3, 7, 2, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 0, 1, 8, 5, 3, 7, 2], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,207 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1138.00)
2025-08-04 17:12:36,207 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:12:36,207 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:12:36,207 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,207 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,208 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,208 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1150.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,209 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0, 'intermediate_solutions': [{'tour': [2, 6, 7, 0, 1, 4, 3, 8, 5], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 5, 8, 2, 4, 1, 0], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 0, 1, 4, 2, 5, 8], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,209 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1150.00)
2025-08-04 17:12:36,209 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:12:36,209 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:12:36,210 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,210 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,210 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,210 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,211 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,211 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,211 - ExplorationExpert - INFO - 探索路径生成完成，成本: 861.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,211 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 7, 4, 1, 6, 0, 2], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 0, 6, 8, 4, 7], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 4, 8, 7, 6, 0, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,212 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 861.00)
2025-08-04 17:12:36,212 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:12:36,212 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,212 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,212 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 795.0
2025-08-04 17:12:36,268 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,268 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,268 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,269 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,269 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 6, 2, 0, 8, 3, 1, 7], 'cur_cost': 1183.0}, {'tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0}, {'tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0}, {'tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': array([3, 7, 5, 6, 0, 1, 2, 8, 4], dtype=int64), 'cur_cost': 795.0}, {'tour': [3, 6, 2, 1, 8, 5, 0, 7, 4], 'cur_cost': 1129.0}, {'tour': [2, 7, 3, 8, 4, 0, 1, 6, 5], 'cur_cost': 853.0}, {'tour': [4, 6, 2, 5, 0, 3, 8, 7, 1], 'cur_cost': 1159.0}, {'tour': [6, 7, 5, 0, 4, 2, 8, 3, 1], 'cur_cost': 882.0}]
2025-08-04 17:12:36,270 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:12:36,270 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:12:36,271 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 7, 5, 6, 0, 1, 2, 8, 4], dtype=int64), 'cur_cost': 795.0, 'intermediate_solutions': [{'tour': array([4, 0, 2, 6, 8, 3, 7, 1, 5]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 0, 2, 8, 3, 7, 1, 5]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 4, 0, 2, 3, 7, 1, 5]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 0, 8, 3, 7, 1, 5]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 6, 4, 0, 3, 7, 1, 5]), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,271 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 795.00)
2025-08-04 17:12:36,271 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:12:36,271 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,272 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,272 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1036.0
2025-08-04 17:12:36,339 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,339 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,339 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,340 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,340 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 6, 2, 0, 8, 3, 1, 7], 'cur_cost': 1183.0}, {'tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0}, {'tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0}, {'tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': array([3, 7, 5, 6, 0, 1, 2, 8, 4], dtype=int64), 'cur_cost': 795.0}, {'tour': array([1, 7, 3, 5, 4, 0, 6, 2, 8], dtype=int64), 'cur_cost': 1036.0}, {'tour': [2, 7, 3, 8, 4, 0, 1, 6, 5], 'cur_cost': 853.0}, {'tour': [4, 6, 2, 5, 0, 3, 8, 7, 1], 'cur_cost': 1159.0}, {'tour': [6, 7, 5, 0, 4, 2, 8, 3, 1], 'cur_cost': 882.0}]
2025-08-04 17:12:36,341 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:12:36,341 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:12:36,342 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 7, 3, 5, 4, 0, 6, 2, 8], dtype=int64), 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': array([2, 6, 3, 1, 8, 5, 0, 7, 4]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 6, 3, 8, 5, 0, 7, 4]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 2, 6, 3, 5, 0, 7, 4]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 2, 6, 8, 5, 0, 7, 4]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 2, 6, 5, 0, 7, 4]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,342 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1036.00)
2025-08-04 17:12:36,343 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:12:36,343 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:12:36,343 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,343 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,343 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,344 - ExplorationExpert - INFO - 探索路径生成完成，成本: 852.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,344 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': [2, 7, 3, 4, 8, 0, 1, 6, 5], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 3, 8, 6, 1, 0, 4, 5], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 8, 4, 0, 6, 1, 5], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,345 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 852.00)
2025-08-04 17:12:36,345 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:12:36,345 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,345 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,346 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 866.0
2025-08-04 17:12:36,415 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,416 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,416 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,417 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,417 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 6, 2, 0, 8, 3, 1, 7], 'cur_cost': 1183.0}, {'tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0}, {'tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0}, {'tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': array([3, 7, 5, 6, 0, 1, 2, 8, 4], dtype=int64), 'cur_cost': 795.0}, {'tour': array([1, 7, 3, 5, 4, 0, 6, 2, 8], dtype=int64), 'cur_cost': 1036.0}, {'tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0}, {'tour': array([4, 1, 6, 5, 8, 3, 0, 7, 2], dtype=int64), 'cur_cost': 866.0}, {'tour': [6, 7, 5, 0, 4, 2, 8, 3, 1], 'cur_cost': 882.0}]
2025-08-04 17:12:36,418 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:12:36,418 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:12:36,419 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([4, 1, 6, 5, 8, 3, 0, 7, 2], dtype=int64), 'cur_cost': 866.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 0, 3, 8, 7, 1]), 'cur_cost': 1159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 0, 3, 8, 7, 1]), 'cur_cost': 1175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 2, 6, 4, 3, 8, 7, 1]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 0, 3, 8, 7, 1]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 5, 2, 6, 3, 8, 7, 1]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,420 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 866.00)
2025-08-04 17:12:36,420 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:12:36,420 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:12:36,421 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,421 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,421 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,422 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,422 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,422 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,422 - ExplorationExpert - INFO - 探索路径生成完成，成本: 775.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,423 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 3, 4, 2, 8, 0, 1], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 3, 8, 2, 4, 0, 5, 7], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 7, 5, 0, 2, 8, 3, 1], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,423 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 775.00)
2025-08-04 17:12:36,423 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:12:36,423 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:12:36,425 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 6, 2, 0, 8, 3, 1, 7], 'cur_cost': 1183.0, 'intermediate_solutions': [{'tour': [8, 0, 6, 2, 1, 4, 3, 7, 5], 'cur_cost': 1019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 0, 1, 8, 3, 7, 5], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 2, 1, 6, 8, 3, 7, 5], 'cur_cost': 960.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 7, 1, 6, 8, 2, 4], 'cur_cost': 993.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 4, 6, 1, 7, 3, 8, 2, 0], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 4, 3, 7, 6, 8, 1, 2, 0], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0, 'intermediate_solutions': [{'tour': [4, 6, 7, 8, 5, 3, 0, 2, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 5, 8, 0, 3, 7, 2, 1], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 0, 1, 8, 5, 3, 7, 2], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0, 'intermediate_solutions': [{'tour': [2, 6, 7, 0, 1, 4, 3, 8, 5], 'cur_cost': 975.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 7, 5, 8, 2, 4, 1, 0], 'cur_cost': 780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 7, 0, 1, 4, 2, 5, 8], 'cur_cost': 820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 7, 4, 1, 6, 0, 2], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 0, 6, 8, 4, 7], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 4, 8, 7, 6, 0, 2], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 5, 6, 0, 1, 2, 8, 4], dtype=int64), 'cur_cost': 795.0, 'intermediate_solutions': [{'tour': array([4, 0, 2, 6, 8, 3, 7, 1, 5]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 0, 2, 8, 3, 7, 1, 5]), 'cur_cost': 960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 6, 4, 0, 2, 3, 7, 1, 5]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 4, 0, 8, 3, 7, 1, 5]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 6, 4, 0, 3, 7, 1, 5]), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 7, 3, 5, 4, 0, 6, 2, 8], dtype=int64), 'cur_cost': 1036.0, 'intermediate_solutions': [{'tour': array([2, 6, 3, 1, 8, 5, 0, 7, 4]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 2, 6, 3, 8, 5, 0, 7, 4]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 2, 6, 3, 5, 0, 7, 4]), 'cur_cost': 1066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 1, 2, 6, 8, 5, 0, 7, 4]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 8, 1, 2, 6, 5, 0, 7, 4]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': [2, 7, 3, 4, 8, 0, 1, 6, 5], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 3, 8, 6, 1, 0, 4, 5], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 7, 3, 8, 4, 0, 6, 1, 5], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 6, 5, 8, 3, 0, 7, 2], dtype=int64), 'cur_cost': 866.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 5, 0, 3, 8, 7, 1]), 'cur_cost': 1159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 2, 6, 4, 0, 3, 8, 7, 1]), 'cur_cost': 1175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 5, 2, 6, 4, 3, 8, 7, 1]), 'cur_cost': 1092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 5, 2, 6, 0, 3, 8, 7, 1]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 5, 2, 6, 3, 8, 7, 1]), 'cur_cost': 1071.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 3, 4, 2, 8, 0, 1], 'cur_cost': 789.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 3, 8, 2, 4, 0, 5, 7], 'cur_cost': 882.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 7, 5, 0, 2, 8, 3, 1], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:12:36,427 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:12:36,427 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,428 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=775.000, 多样性=0.894
2025-08-04 17:12:36,428 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:12:36,429 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:12:36,429 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:12:36,429 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.01758472155149104, 'best_improvement': -0.060191518467852256}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014005602240896675}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:12:36,430 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:12:36,430 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:12:36,430 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:12:36,430 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,431 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=775.000, 多样性=0.894
2025-08-04 17:12:36,431 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:12:36,432 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.894
2025-08-04 17:12:36,432 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:12:36,433 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:12:36,435 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:12:36,435 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:12:36,435 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:12:36,435 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:12:36,442 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 25.123, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.613
2025-08-04 17:12:36,442 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:12:36,443 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:12:36,443 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:12:36,446 - visualization.landscape_visualizer - INFO - 插值约束: 187 个点被约束到最小值 680.00
2025-08-04 17:12:36,449 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:12:36,531 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_171236.html
2025-08-04 17:12:36,575 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_171236.html
2025-08-04 17:12:36,575 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:12:36,576 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:12:36,576 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1410秒
2025-08-04 17:12:36,576 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 25.123076923076926, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 34684.11408284024, 'cluster_count': 0}, 'population_state': {'diversity': 0.6134122287968442, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9272635841016705, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 25.123)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298756.4423122, 'performance_metrics': {}}}
2025-08-04 17:12:36,577 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:12:36,577 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:12:36,578 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:12:36,578 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:12:36,578 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:12:36,579 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:12:36,579 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:12:36,579 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:36,579 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:12:36,579 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:12:36,580 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:36,580 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:12:36,580 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:12:36,580 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:12:36,581 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,581 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,581 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1010.0
2025-08-04 17:12:36,642 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,643 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,643 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,644 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,644 - ExploitationExpert - INFO - populations: [{'tour': array([6, 7, 3, 8, 0, 1, 4, 5, 2], dtype=int64), 'cur_cost': 1010.0}, {'tour': [2, 3, 0, 5, 6, 7, 8, 4, 1], 'cur_cost': 978.0}, {'tour': [1, 3, 6, 2, 5, 7, 0, 4, 8], 'cur_cost': 1138.0}, {'tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': [3, 7, 5, 6, 0, 1, 2, 8, 4], 'cur_cost': 795.0}, {'tour': [1, 7, 3, 5, 4, 0, 6, 2, 8], 'cur_cost': 1036.0}, {'tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0}, {'tour': [4, 1, 6, 5, 8, 3, 0, 7, 2], 'cur_cost': 866.0}, {'tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0}]
2025-08-04 17:12:36,644 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:12:36,645 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:12:36,645 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([6, 7, 3, 8, 0, 1, 4, 5, 2], dtype=int64), 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': array([6, 4, 5, 2, 0, 8, 3, 1, 7]), 'cur_cost': 1177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 4, 5, 0, 8, 3, 1, 7]), 'cur_cost': 1243.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 4, 5, 8, 3, 1, 7]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 4, 0, 8, 3, 1, 7]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 4, 8, 3, 1, 7]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,646 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1010.00)
2025-08-04 17:12:36,646 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:12:36,646 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:12:36,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,647 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 876.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,648 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 3, 6, 0, 4, 2, 8, 7, 1], 'cur_cost': 876.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 5, 6, 7, 8, 4, 0], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 0, 5, 6, 4, 8, 7, 1], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 5, 0, 6, 7, 8, 4, 1], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,649 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 876.00)
2025-08-04 17:12:36,649 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:12:36,649 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,650 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1090.0
2025-08-04 17:12:36,713 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,713 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,713 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,714 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,714 - ExploitationExpert - INFO - populations: [{'tour': array([6, 7, 3, 8, 0, 1, 4, 5, 2], dtype=int64), 'cur_cost': 1010.0}, {'tour': [5, 3, 6, 0, 4, 2, 8, 7, 1], 'cur_cost': 876.0}, {'tour': array([3, 1, 8, 6, 5, 2, 0, 4, 7], dtype=int64), 'cur_cost': 1090.0}, {'tour': [5, 4, 0, 7, 6, 2, 3, 8, 1], 'cur_cost': 1150.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': [3, 7, 5, 6, 0, 1, 2, 8, 4], 'cur_cost': 795.0}, {'tour': [1, 7, 3, 5, 4, 0, 6, 2, 8], 'cur_cost': 1036.0}, {'tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0}, {'tour': [4, 1, 6, 5, 8, 3, 0, 7, 2], 'cur_cost': 866.0}, {'tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0}]
2025-08-04 17:12:36,715 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:12:36,715 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:12:36,716 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([3, 1, 8, 6, 5, 2, 0, 4, 7], dtype=int64), 'cur_cost': 1090.0, 'intermediate_solutions': [{'tour': array([6, 3, 1, 2, 5, 7, 0, 4, 8]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 3, 1, 5, 7, 0, 4, 8]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 6, 3, 1, 7, 0, 4, 8]), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 3, 5, 7, 0, 4, 8]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 6, 3, 7, 0, 4, 8]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,717 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1090.00)
2025-08-04 17:12:36,717 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:12:36,717 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:36,717 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:36,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1019.0
2025-08-04 17:12:36,780 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:36,780 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:36,780 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:36,781 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:36,782 - ExploitationExpert - INFO - populations: [{'tour': array([6, 7, 3, 8, 0, 1, 4, 5, 2], dtype=int64), 'cur_cost': 1010.0}, {'tour': [5, 3, 6, 0, 4, 2, 8, 7, 1], 'cur_cost': 876.0}, {'tour': array([3, 1, 8, 6, 5, 2, 0, 4, 7], dtype=int64), 'cur_cost': 1090.0}, {'tour': array([3, 8, 0, 2, 1, 5, 6, 4, 7], dtype=int64), 'cur_cost': 1019.0}, {'tour': [4, 5, 6, 1, 0, 8, 7, 3, 2], 'cur_cost': 861.0}, {'tour': [3, 7, 5, 6, 0, 1, 2, 8, 4], 'cur_cost': 795.0}, {'tour': [1, 7, 3, 5, 4, 0, 6, 2, 8], 'cur_cost': 1036.0}, {'tour': [6, 5, 3, 4, 7, 0, 1, 2, 8], 'cur_cost': 852.0}, {'tour': [4, 1, 6, 5, 8, 3, 0, 7, 2], 'cur_cost': 866.0}, {'tour': [3, 6, 5, 7, 8, 2, 4, 0, 1], 'cur_cost': 775.0}]
2025-08-04 17:12:36,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:12:36,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:12:36,784 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 8, 0, 2, 1, 5, 6, 4, 7], dtype=int64), 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': array([0, 4, 5, 7, 6, 2, 3, 8, 1]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 4, 5, 6, 2, 3, 8, 1]), 'cur_cost': 1098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 7, 0, 4, 5, 2, 3, 8, 1]), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 0, 4, 6, 2, 3, 8, 1]), 'cur_cost': 1147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 6, 7, 0, 4, 2, 3, 8, 1]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:36,784 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1019.00)
2025-08-04 17:12:36,785 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:12:36,785 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:12:36,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,786 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:36,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1056.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,787 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 3, 0, 2, 7, 6, 1, 8, 4], 'cur_cost': 1056.0, 'intermediate_solutions': [{'tour': [4, 0, 6, 1, 5, 8, 7, 3, 2], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 6, 0, 8, 7, 3, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 6, 1, 8, 7, 3, 2], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,788 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1056.00)
2025-08-04 17:12:36,788 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:12:36,788 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:12:36,789 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,789 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:36,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1173.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,790 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 3, 0, 5, 4, 6, 7, 8, 1], 'cur_cost': 1173.0, 'intermediate_solutions': [{'tour': [3, 7, 5, 6, 4, 1, 2, 8, 0], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 8, 2, 1, 0, 6, 5, 4], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 6, 0, 1, 2, 8, 4], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,791 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1173.00)
2025-08-04 17:12:36,791 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:12:36,791 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:12:36,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,792 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,793 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,794 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 0, 4, 2, 8, 7, 3, 5, 6], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [1, 6, 3, 5, 4, 0, 7, 2, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 5, 4, 0, 6, 2, 8], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 7, 3, 5, 4, 0, 6, 2], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,794 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 680.00)
2025-08-04 17:12:36,794 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:12:36,795 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:12:36,795 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,795 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:36,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1076.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,796 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 6, 3, 4, 1, 7, 5, 8, 0], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [6, 4, 3, 5, 7, 0, 1, 2, 8], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 5, 6, 7, 0, 1, 2, 8], 'cur_cost': 794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 7, 4, 0, 1, 2, 8], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,797 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1076.00)
2025-08-04 17:12:36,797 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:12:36,797 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:12:36,797 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,798 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:36,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,799 - ExplorationExpert - INFO - 探索路径生成完成，成本: 794.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,799 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0, 'intermediate_solutions': [{'tour': [4, 1, 6, 5, 3, 8, 0, 7, 2], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 5, 6, 1, 4, 7, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 5, 0, 8, 3, 7, 2], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,800 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 794.00)
2025-08-04 17:12:36,800 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:12:36,800 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:12:36,801 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:36,801 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:36,801 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:36,802 - ExplorationExpert - INFO - 探索路径生成完成，成本: 896.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:36,803 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 8, 0, 6, 5, 3, 7, 2, 1], 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': [3, 2, 5, 7, 8, 6, 4, 0, 1], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 4, 2, 8, 7, 5, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 5, 8, 2, 4, 7, 0, 1], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:36,803 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 896.00)
2025-08-04 17:12:36,803 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:12:36,804 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:12:36,805 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 3, 8, 0, 1, 4, 5, 2], dtype=int64), 'cur_cost': 1010.0, 'intermediate_solutions': [{'tour': array([6, 4, 5, 2, 0, 8, 3, 1, 7]), 'cur_cost': 1177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 4, 5, 0, 8, 3, 1, 7]), 'cur_cost': 1243.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 2, 6, 4, 5, 8, 3, 1, 7]), 'cur_cost': 1158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 2, 6, 4, 0, 8, 3, 1, 7]), 'cur_cost': 1172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 2, 6, 4, 8, 3, 1, 7]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 6, 0, 4, 2, 8, 7, 1], 'cur_cost': 876.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 5, 6, 7, 8, 4, 0], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 0, 5, 6, 4, 8, 7, 1], 'cur_cost': 1064.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 3, 5, 0, 6, 7, 8, 4, 1], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 8, 6, 5, 2, 0, 4, 7], dtype=int64), 'cur_cost': 1090.0, 'intermediate_solutions': [{'tour': array([6, 3, 1, 2, 5, 7, 0, 4, 8]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 3, 1, 5, 7, 0, 4, 8]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 6, 3, 1, 7, 0, 4, 8]), 'cur_cost': 1115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 3, 5, 7, 0, 4, 8]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 5, 2, 6, 3, 7, 0, 4, 8]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 0, 2, 1, 5, 6, 4, 7], dtype=int64), 'cur_cost': 1019.0, 'intermediate_solutions': [{'tour': array([0, 4, 5, 7, 6, 2, 3, 8, 1]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 4, 5, 6, 2, 3, 8, 1]), 'cur_cost': 1098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 7, 0, 4, 5, 2, 3, 8, 1]), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 0, 4, 6, 2, 3, 8, 1]), 'cur_cost': 1147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 6, 7, 0, 4, 2, 3, 8, 1]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 0, 2, 7, 6, 1, 8, 4], 'cur_cost': 1056.0, 'intermediate_solutions': [{'tour': [4, 0, 6, 1, 5, 8, 7, 3, 2], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 1, 6, 0, 8, 7, 3, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 5, 6, 1, 8, 7, 3, 2], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 5, 4, 6, 7, 8, 1], 'cur_cost': 1173.0, 'intermediate_solutions': [{'tour': [3, 7, 5, 6, 4, 1, 2, 8, 0], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 8, 2, 1, 0, 6, 5, 4], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 6, 0, 1, 2, 8, 4], 'cur_cost': 759.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 4, 2, 8, 7, 3, 5, 6], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [1, 6, 3, 5, 4, 0, 7, 2, 8], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 5, 4, 0, 6, 2, 8], 'cur_cost': 1085.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 7, 3, 5, 4, 0, 6, 2], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 3, 4, 1, 7, 5, 8, 0], 'cur_cost': 1076.0, 'intermediate_solutions': [{'tour': [6, 4, 3, 5, 7, 0, 1, 2, 8], 'cur_cost': 908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 5, 6, 7, 0, 1, 2, 8], 'cur_cost': 794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 7, 4, 0, 1, 2, 8], 'cur_cost': 792.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0, 'intermediate_solutions': [{'tour': [4, 1, 6, 5, 3, 8, 0, 7, 2], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 8, 5, 6, 1, 4, 7, 2], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 5, 0, 8, 3, 7, 2], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 6, 5, 3, 7, 2, 1], 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': [3, 2, 5, 7, 8, 6, 4, 0, 1], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 0, 4, 2, 8, 7, 5, 6, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 5, 8, 2, 4, 7, 0, 1], 'cur_cost': 810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:12:36,807 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:12:36,807 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,808 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.901
2025-08-04 17:12:36,808 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:12:36,809 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:12:36,809 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:12:36,809 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04243232163856544, 'best_improvement': 0.12258064516129032}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008287292817679445}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.013958069759150565, 'recent_improvements': [-0.04550086106979217, 0.011648641649891251, -0.01758472155149104], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:12:36,810 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:12:36,810 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:12:36,810 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:12:36,810 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:36,811 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.901
2025-08-04 17:12:36,811 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:12:36,812 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.901
2025-08-04 17:12:36,812 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:12:36,813 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.852
2025-08-04 17:12:36,814 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:12:36,815 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:12:36,815 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:12:36,815 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:12:36,823 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 52.538, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.620
2025-08-04 17:12:36,823 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:12:36,823 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:12:36,824 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:12:36,861 - visualization.landscape_visualizer - INFO - 插值约束: 50 个点被约束到最小值 680.00
2025-08-04 17:12:36,864 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:12:37,118 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_171237.html
2025-08-04 17:12:37,161 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_171237.html
2025-08-04 17:12:37,162 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:12:37,162 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:12:37,163 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3480秒
2025-08-04 17:12:37,163 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 52.53846153846154, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 34260.40852071006, 'cluster_count': 0}, 'population_state': {'diversity': 0.6203155818540433, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9257875983547231, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 52.538)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754298756.8237288, 'performance_metrics': {}}}
2025-08-04 17:12:37,164 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:12:37,164 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:12:37,165 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:12:37,165 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:12:37,166 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:37,166 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:12:37,167 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:37,167 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:37,167 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:12:37,168 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:12:37,168 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:12:37,169 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:12:37,169 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:12:37,169 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:12:37,169 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:12:37,170 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,170 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:37,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 789.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,171 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 6, 7, 5, 3, 4, 2, 8, 0], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [6, 7, 3, 4, 0, 1, 8, 5, 2], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 8, 4, 1, 0, 5, 2], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 8, 0, 1, 4, 6, 5, 2], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,172 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 789.00)
2025-08-04 17:12:37,172 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:12:37,172 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:12:37,172 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,173 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:37,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 914.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,174 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 6, 5, 0, 4, 2, 8, 7, 1], 'cur_cost': 914.0, 'intermediate_solutions': [{'tour': [5, 3, 6, 0, 2, 4, 8, 7, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 6, 0, 4, 7, 8, 2, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 0, 6, 4, 2, 8, 7, 1], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,175 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 914.00)
2025-08-04 17:12:37,175 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:12:37,175 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:37,175 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:37,176 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1061.0
2025-08-04 17:12:37,244 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:37,244 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:37,245 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:37,245 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:37,246 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 5, 3, 4, 2, 8, 0], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 4, 2, 8, 7, 1], 'cur_cost': 914.0}, {'tour': array([2, 7, 8, 1, 5, 6, 3, 4, 0], dtype=int64), 'cur_cost': 1061.0}, {'tour': [3, 8, 0, 2, 1, 5, 6, 4, 7], 'cur_cost': 1019.0}, {'tour': [5, 3, 0, 2, 7, 6, 1, 8, 4], 'cur_cost': 1056.0}, {'tour': [2, 3, 0, 5, 4, 6, 7, 8, 1], 'cur_cost': 1173.0}, {'tour': [1, 0, 4, 2, 8, 7, 3, 5, 6], 'cur_cost': 680.0}, {'tour': [2, 6, 3, 4, 1, 7, 5, 8, 0], 'cur_cost': 1076.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': [4, 8, 0, 6, 5, 3, 7, 2, 1], 'cur_cost': 896.0}]
2025-08-04 17:12:37,246 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:12:37,247 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:12:37,248 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([2, 7, 8, 1, 5, 6, 3, 4, 0], dtype=int64), 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 6, 5, 2, 0, 4, 7]), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 3, 5, 2, 0, 4, 7]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 8, 1, 3, 2, 0, 4, 7]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 1, 5, 2, 0, 4, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 6, 8, 1, 2, 0, 4, 7]), 'cur_cost': 952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:37,249 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1061.00)
2025-08-04 17:12:37,249 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:12:37,249 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:12:37,250 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,250 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:37,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1101.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,252 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 0, 7, 8, 2, 6, 3, 1, 4], 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 2, 1, 5, 6, 0, 7], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 2, 4, 6, 5, 1, 7], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 8, 0, 1, 5, 6, 4, 7], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,253 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1101.00)
2025-08-04 17:12:37,253 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:12:37,253 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:12:37,253 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,254 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:37,254 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,254 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,255 - ExplorationExpert - INFO - 探索路径生成完成，成本: 823.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,255 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 7, 6, 8, 3, 5, 4, 2, 1], 'cur_cost': 823.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 2, 7, 6, 1, 8, 4], 'cur_cost': 1144.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 2, 7, 6, 1, 4, 8], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 0, 7, 6, 1, 8, 2, 4], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,256 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 823.00)
2025-08-04 17:12:37,256 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:12:37,256 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:37,257 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:37,257 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 896.0
2025-08-04 17:12:37,334 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:37,335 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:37,335 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:37,336 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:37,336 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 5, 3, 4, 2, 8, 0], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 4, 2, 8, 7, 1], 'cur_cost': 914.0}, {'tour': array([2, 7, 8, 1, 5, 6, 3, 4, 0], dtype=int64), 'cur_cost': 1061.0}, {'tour': [5, 0, 7, 8, 2, 6, 3, 1, 4], 'cur_cost': 1101.0}, {'tour': [0, 7, 6, 8, 3, 5, 4, 2, 1], 'cur_cost': 823.0}, {'tour': array([3, 8, 5, 4, 0, 1, 2, 7, 6], dtype=int64), 'cur_cost': 896.0}, {'tour': [1, 0, 4, 2, 8, 7, 3, 5, 6], 'cur_cost': 680.0}, {'tour': [2, 6, 3, 4, 1, 7, 5, 8, 0], 'cur_cost': 1076.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': [4, 8, 0, 6, 5, 3, 7, 2, 1], 'cur_cost': 896.0}]
2025-08-04 17:12:37,338 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 17:12:37,338 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:12:37,338 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 8, 5, 4, 0, 1, 2, 7, 6], dtype=int64), 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': array([0, 3, 2, 5, 4, 6, 7, 8, 1]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 3, 2, 4, 6, 7, 8, 1]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 0, 3, 2, 6, 7, 8, 1]), 'cur_cost': 1182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 0, 3, 4, 6, 7, 8, 1]), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 5, 0, 3, 6, 7, 8, 1]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:37,339 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 896.00)
2025-08-04 17:12:37,339 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:12:37,339 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:12:37,339 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,340 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:12:37,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,341 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 999.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,341 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 5, 4, 2, 7, 1, 3, 8], 'cur_cost': 999.0, 'intermediate_solutions': [{'tour': [1, 0, 4, 2, 8, 5, 3, 7, 6], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 2, 8, 7, 3, 6, 5], 'cur_cost': 765.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 4, 2, 8, 3, 7, 5, 6], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,341 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 999.00)
2025-08-04 17:12:37,342 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:12:37,342 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:12:37,342 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:12:37,342 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 939.0
2025-08-04 17:12:37,403 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:12:37,403 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-04 17:12:37,404 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 6, 5, 3, 7, 8, 2, 4], dtype=int64)]
2025-08-04 17:12:37,404 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:12:37,405 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 5, 3, 4, 2, 8, 0], 'cur_cost': 789.0}, {'tour': [3, 6, 5, 0, 4, 2, 8, 7, 1], 'cur_cost': 914.0}, {'tour': array([2, 7, 8, 1, 5, 6, 3, 4, 0], dtype=int64), 'cur_cost': 1061.0}, {'tour': [5, 0, 7, 8, 2, 6, 3, 1, 4], 'cur_cost': 1101.0}, {'tour': [0, 7, 6, 8, 3, 5, 4, 2, 1], 'cur_cost': 823.0}, {'tour': array([3, 8, 5, 4, 0, 1, 2, 7, 6], dtype=int64), 'cur_cost': 896.0}, {'tour': [0, 6, 5, 4, 2, 7, 1, 3, 8], 'cur_cost': 999.0}, {'tour': array([4, 0, 6, 8, 2, 1, 7, 5, 3], dtype=int64), 'cur_cost': 939.0}, {'tour': [6, 7, 5, 8, 3, 4, 2, 0, 1], 'cur_cost': 794.0}, {'tour': [4, 8, 0, 6, 5, 3, 7, 2, 1], 'cur_cost': 896.0}]
2025-08-04 17:12:37,405 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:12:37,406 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:12:37,406 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([4, 0, 6, 8, 2, 1, 7, 5, 3], dtype=int64), 'cur_cost': 939.0, 'intermediate_solutions': [{'tour': array([3, 6, 2, 4, 1, 7, 5, 8, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 6, 2, 1, 7, 5, 8, 0]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 3, 6, 2, 7, 5, 8, 0]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 6, 1, 7, 5, 8, 0]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 4, 3, 6, 7, 5, 8, 0]), 'cur_cost': 981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:12:37,407 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 939.00)
2025-08-04 17:12:37,407 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:12:37,407 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:12:37,407 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,408 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:12:37,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,409 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1033.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,409 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 8, 5, 4, 7, 3, 6, 0, 2], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 3, 8, 4, 2, 0, 1], 'cur_cost': 731.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 4, 3, 8, 5, 0, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 3, 4, 2, 0, 8, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,409 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1033.00)
2025-08-04 17:12:37,409 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:12:37,410 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:12:37,410 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:12:37,410 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:12:37,410 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,410 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:12:37,411 - ExplorationExpert - INFO - 探索路径生成完成，成本: 818.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:12:37,411 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 7, 0, 1, 6, 3, 8, 4, 2], 'cur_cost': 818.0, 'intermediate_solutions': [{'tour': [4, 1, 0, 6, 5, 3, 7, 2, 8], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 6, 0, 8, 3, 7, 2, 1], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 0, 6, 5, 7, 2, 1], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:12:37,412 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 818.00)
2025-08-04 17:12:37,412 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:12:37,412 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:12:37,413 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 5, 3, 4, 2, 8, 0], 'cur_cost': 789.0, 'intermediate_solutions': [{'tour': [6, 7, 3, 4, 0, 1, 8, 5, 2], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 3, 8, 4, 1, 0, 5, 2], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 8, 0, 1, 4, 6, 5, 2], 'cur_cost': 947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 5, 0, 4, 2, 8, 7, 1], 'cur_cost': 914.0, 'intermediate_solutions': [{'tour': [5, 3, 6, 0, 2, 4, 8, 7, 1], 'cur_cost': 913.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 6, 0, 4, 7, 8, 2, 1], 'cur_cost': 928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 0, 6, 4, 2, 8, 7, 1], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 7, 8, 1, 5, 6, 3, 4, 0], dtype=int64), 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': array([8, 1, 3, 6, 5, 2, 0, 4, 7]), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 8, 1, 3, 5, 2, 0, 4, 7]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 8, 1, 3, 2, 0, 4, 7]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 8, 1, 5, 2, 0, 4, 7]), 'cur_cost': 1121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 5, 6, 8, 1, 2, 0, 4, 7]), 'cur_cost': 952.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 0, 7, 8, 2, 6, 3, 1, 4], 'cur_cost': 1101.0, 'intermediate_solutions': [{'tour': [3, 8, 4, 2, 1, 5, 6, 0, 7], 'cur_cost': 843.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 2, 4, 6, 5, 1, 7], 'cur_cost': 967.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 8, 0, 1, 5, 6, 4, 7], 'cur_cost': 968.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 8, 3, 5, 4, 2, 1], 'cur_cost': 823.0, 'intermediate_solutions': [{'tour': [5, 0, 3, 2, 7, 6, 1, 8, 4], 'cur_cost': 1144.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 2, 7, 6, 1, 4, 8], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 0, 7, 6, 1, 8, 2, 4], 'cur_cost': 943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 5, 4, 0, 1, 2, 7, 6], dtype=int64), 'cur_cost': 896.0, 'intermediate_solutions': [{'tour': array([0, 3, 2, 5, 4, 6, 7, 8, 1]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 3, 2, 4, 6, 7, 8, 1]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 5, 0, 3, 2, 6, 7, 8, 1]), 'cur_cost': 1182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 0, 3, 4, 6, 7, 8, 1]), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 5, 0, 3, 6, 7, 8, 1]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 4, 2, 7, 1, 3, 8], 'cur_cost': 999.0, 'intermediate_solutions': [{'tour': [1, 0, 4, 2, 8, 5, 3, 7, 6], 'cur_cost': 704.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 2, 8, 7, 3, 6, 5], 'cur_cost': 765.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 4, 2, 8, 3, 7, 5, 6], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 0, 6, 8, 2, 1, 7, 5, 3], dtype=int64), 'cur_cost': 939.0, 'intermediate_solutions': [{'tour': array([3, 6, 2, 4, 1, 7, 5, 8, 0]), 'cur_cost': 1007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 6, 2, 1, 7, 5, 8, 0]), 'cur_cost': 1065.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 3, 6, 2, 7, 5, 8, 0]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 6, 1, 7, 5, 8, 0]), 'cur_cost': 954.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 4, 3, 6, 7, 5, 8, 0]), 'cur_cost': 981.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 5, 4, 7, 3, 6, 0, 2], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [6, 7, 5, 3, 8, 4, 2, 0, 1], 'cur_cost': 731.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 4, 3, 8, 5, 0, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 5, 3, 4, 2, 0, 8, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 0, 1, 6, 3, 8, 4, 2], 'cur_cost': 818.0, 'intermediate_solutions': [{'tour': [4, 1, 0, 6, 5, 3, 7, 2, 8], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 6, 0, 8, 3, 7, 2, 1], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 8, 0, 6, 5, 7, 2, 1], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:12:37,416 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:12:37,416 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:12:37,418 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=789.000, 多样性=0.896
2025-08-04 17:12:37,418 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:12:37,418 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:12:37,418 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:12:37,419 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.022733165475173466, 'best_improvement': -0.16029411764705884}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005479452054794692}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01539183999433709, 'recent_improvements': [0.011648641649891251, -0.01758472155149104, 0.04243232163856544], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8518518518518519, 'new_diversity': 0.8518518518518519, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:12:37,419 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:12:37,421 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:12:37,421 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_171237.solution
2025-08-04 17:12:37,432 - __main__ - INFO - 评估统计 - 总次数: 222019.6666664859, 运行时间: 9.65s, 最佳成本: 680.0
2025-08-04 17:12:37,433 - __main__ - INFO - 实例 simple1_9 处理完成
