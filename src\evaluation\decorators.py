"""
评估装饰器

提供函数级别的评估计数装饰器。
"""

import time
from functools import wraps
from typing import Callable, Optional, Any

from .types import EvaluationType, EvaluationComplexity
from .counter import EvaluationCounter


def evaluation_tracker(evaluation_type: EvaluationType,
                      count_per_call: int = 1,
                      complexity: EvaluationComplexity = EvaluationComplexity.O_N,
                      extract_cost: bool = False,
                      extract_improvement: bool = False,
                      counter: Optional[EvaluationCounter] = None):
    """
    评估追踪装饰器
    
    Args:
        evaluation_type: 评估类型
        count_per_call: 每次调用的评估次数
        complexity: 评估复杂度
        extract_cost: 是否提取成本信息
        extract_improvement: 是否提取改进信息
        counter: 评估计数器（如果为None，使用全局计数器）
    
    Usage:
        @evaluation_tracker(EvaluationType.FULL_EVALUATION, extract_cost=True)
        def tour_cost(distance_matrix, tour):
            return calculate_cost(distance_matrix, tour)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取计数器
            eval_counter = counter
            if eval_counter is None:
                from . import get_global_counter
                eval_counter = get_global_counter()
            
            start_time = time.time()
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            execution_time = time.time() - start_time
            
            # 提取成本和改进信息
            cost_value = None
            improvement = None
            
            if extract_cost and result is not None:
                if isinstance(result, (int, float)):
                    cost_value = float(result)
                elif isinstance(result, tuple) and len(result) > 0:
                    if isinstance(result[0], (int, float)):
                        cost_value = float(result[0])
            
            if extract_improvement and isinstance(result, tuple) and len(result) > 1:
                if isinstance(result[1], (int, float)):
                    improvement = float(result[1])
            
            # 记录评估
            eval_counter.increment(
                evaluation_type=evaluation_type,
                count=count_per_call,
                cost_value=cost_value,
                improvement=improvement,
                complexity=complexity,
                metadata={
                    'function_name': func.__name__,
                    'execution_time': execution_time,
                    'args_count': len(args),
                    'kwargs_count': len(kwargs)
                }
            )
            
            return result
        
        # 添加元数据
        wrapper._evaluation_tracked = True
        wrapper._evaluation_type = evaluation_type
        wrapper._count_per_call = count_per_call
        wrapper._original_function = func
        
        return wrapper
    return decorator


def count_evaluations(evaluation_type: EvaluationType,
                     complexity: EvaluationComplexity = EvaluationComplexity.O_N,
                     counter: Optional[EvaluationCounter] = None):
    """
    简单的评估计数装饰器
    
    Args:
        evaluation_type: 评估类型
        complexity: 评估复杂度
        counter: 评估计数器
    
    Usage:
        @count_evaluations(EvaluationType.DELTA_EVALUATION)
        def two_opt_cost(tour, distance_matrix, i, j):
            return calculate_delta_cost(tour, distance_matrix, i, j)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取计数器
            eval_counter = counter
            if eval_counter is None:
                from . import get_global_counter
                eval_counter = get_global_counter()
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 记录评估
            eval_counter.increment(
                evaluation_type=evaluation_type,
                count=1,
                complexity=complexity,
                metadata={'function_name': func.__name__}
            )
            
            return result
        
        wrapper._evaluation_tracked = True
        wrapper._evaluation_type = evaluation_type
        wrapper._original_function = func
        
        return wrapper
    return decorator


class EvaluationProxy:
    """评估代理类 - 非侵入式集成"""
    
    def __init__(self, target_module: Any, counter: Optional[EvaluationCounter] = None):
        """
        初始化评估代理
        
        Args:
            target_module: 目标模块
            counter: 评估计数器
        """
        self.target_module = target_module
        self.counter = counter
        if self.counter is None:
            from . import get_global_counter
            self.counter = get_global_counter()
        
        self.original_functions = {}
        self.wrapped_functions = {}
    
    def wrap_function(self, 
                     func_name: str,
                     evaluation_type: EvaluationType,
                     count_per_call: int = 1,
                     complexity: EvaluationComplexity = EvaluationComplexity.O_N,
                     extract_cost: bool = False) -> bool:
        """
        包装目标函数
        
        Args:
            func_name: 函数名
            evaluation_type: 评估类型
            count_per_call: 每次调用的评估次数
            complexity: 评估复杂度
            extract_cost: 是否提取成本信息
        
        Returns:
            bool: 是否成功包装
        """
        if not hasattr(self.target_module, func_name):
            return False
        
        original_func = getattr(self.target_module, func_name)
        self.original_functions[func_name] = original_func
        
        @wraps(original_func)
        def wrapped_func(*args, **kwargs):
            start_time = time.time()
            result = original_func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 提取成本信息
            cost_value = None
            if extract_cost and result is not None:
                if isinstance(result, (int, float)):
                    cost_value = float(result)
                elif isinstance(result, tuple) and len(result) > 0:
                    if isinstance(result[0], (int, float)):
                        cost_value = float(result[0])
            
            # 记录评估
            self.counter.increment(
                evaluation_type=evaluation_type,
                count=count_per_call,
                complexity=complexity,
                cost_value=cost_value,
                metadata={
                    'function_name': func_name,
                    'module_name': getattr(self.target_module, '__name__', 'unknown'),
                    'execution_time': execution_time
                }
            )
            
            return result
        
        # 保存包装后的函数
        self.wrapped_functions[func_name] = wrapped_func
        setattr(self.target_module, func_name, wrapped_func)
        return True
    
    def restore_function(self, func_name: str) -> bool:
        """恢复单个函数"""
        if func_name in self.original_functions:
            setattr(self.target_module, func_name, self.original_functions[func_name])
            del self.original_functions[func_name]
            if func_name in self.wrapped_functions:
                del self.wrapped_functions[func_name]
            return True
        return False
    
    def restore_all_functions(self) -> None:
        """恢复所有函数"""
        for func_name in list(self.original_functions.keys()):
            self.restore_function(func_name)
    
    def get_wrapped_functions(self) -> list:
        """获取已包装的函数列表"""
        return list(self.original_functions.keys())


def create_evaluation_proxy(target_module: Any, 
                           counter: Optional[EvaluationCounter] = None) -> EvaluationProxy:
    """
    创建评估代理的便捷函数
    
    Args:
        target_module: 目标模块
        counter: 评估计数器
    
    Returns:
        EvaluationProxy: 评估代理实例
    """
    return EvaluationProxy(target_module, counter)
