2025-08-05 10:28:54,416 - __main__ - INFO - composite6_39 开始进化第 1 代
2025-08-05 10:28:54,416 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:54,417 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,419 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24012.000, 多样性=0.978
2025-08-05 10:28:54,421 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:54,423 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.978
2025-08-05 10:28:54,424 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:54,426 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:54,426 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:54,426 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:54,426 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:54,440 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: 950.400, 聚类评分: 0.000, 覆盖率: 0.102, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:54,441 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:54,441 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:54,441 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 10:28:54,450 - visualization.landscape_visualizer - INFO - 插值约束: 61 个点被约束到最小值 24012.00
2025-08-05 10:28:54,452 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.9%, 梯度: 5473.54 → 5095.78
2025-08-05 10:28:54,560 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_86_20250805_102854.html
2025-08-05 10:28:54,632 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_86_20250805_102854.html
2025-08-05 10:28:54,633 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 86
2025-08-05 10:28:54,633 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:54,633 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2076秒
2025-08-05 10:28:54,633 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 172, 'max_size': 500, 'hits': 0, 'misses': 172, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 575, 'misses': 296, 'hit_rate': 0.6601607347876005, 'evictions': 196, 'ttl': 7200}}
2025-08-05 10:28:54,634 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 950.4000000000015, 'local_optima_density': 0.1, 'gradient_variance': 2647364150.384, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1025, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.102)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 950.400)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360934.4410625, 'performance_metrics': {}}}
2025-08-05 10:28:54,634 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:54,634 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:54,634 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:54,634 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:54,635 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:54,635 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:54,636 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:54,636 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:54,636 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:54,637 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:54,637 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,638 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,640 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34431.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,640 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34431.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,640 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 34431.00)
2025-08-05 10:28:54,640 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:54,641 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:54,641 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,643 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:54,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107185.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,645 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107185.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,645 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 107185.00)
2025-08-05 10:28:54,645 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:54,645 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:54,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,650 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:54,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,651 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106992.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,651 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 106992.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,652 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 106992.00)
2025-08-05 10:28:54,652 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:54,652 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:54,652 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,656 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:54,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88851.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,659 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 88851.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,659 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 88851.00)
2025-08-05 10:28:54,659 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:54,660 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:54,660 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,663 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,663 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,664 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33927.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,664 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33927.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,664 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 33927.00)
2025-08-05 10:28:54,664 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:54,665 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:54,665 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,667 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:54,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,668 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115897.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,668 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 115897.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,668 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 115897.00)
2025-08-05 10:28:54,668 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:54,668 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,669 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,669 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 132546.0
2025-08-05 10:28:54,684 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:54,685 - ExploitationExpert - INFO - res_population_costs: [23771.0, 23763]
2025-08-05 10:28:54,685 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:54,686 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,686 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34431.0}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107185.0}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 106992.0}, {'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 88851.0}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33927.0}, {'tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 115897.0}, {'tour': array([27, 28,  5, 14, 23, 34, 12, 37, 33, 16, 13,  9, 38, 22, 21,  3, 19,
       20, 18, 31, 24,  7, 17, 30,  1, 11,  6,  4,  2,  0, 36, 25, 29, 26,
       35, 15, 10, 32,  8], dtype=int64), 'cur_cost': 132546.0}, {'tour': array([29, 34, 13,  2,  8, 28, 21, 38, 25, 22,  5, 10,  9, 16, 32,  7, 27,
       20,  6,  4, 36,  1, 30, 12, 23, 35, 14, 31, 33,  0, 17, 37,  3, 18,
       15, 19, 24, 26, 11], dtype=int64), 'cur_cost': 154030.0}, {'tour': array([13, 22,  4, 29,  9, 33, 35, 10, 27,  8, 15, 17,  2, 32,  3, 12,  5,
       18,  7, 37, 16, 21, 24, 20,  1, 34, 19, 31, 11, 25, 23,  6, 14, 36,
       28, 38, 26, 30,  0], dtype=int64), 'cur_cost': 143399.0}, {'tour': array([10, 21, 17,  4,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 161268.0}]
2025-08-05 10:28:54,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:54,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 222, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 222, 'cache_hits': 0, 'similarity_calculations': 1044, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,690 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([27, 28,  5, 14, 23, 34, 12, 37, 33, 16, 13,  9, 38, 22, 21,  3, 19,
       20, 18, 31, 24,  7, 17, 30,  1, 11,  6,  4,  2,  0, 36, 25, 29, 26,
       35, 15, 10, 32,  8], dtype=int64), 'cur_cost': 132546.0, 'intermediate_solutions': [{'tour': array([ 4, 16, 31, 15, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 154267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15,  4, 16, 31, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 153994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 15,  4, 16, 31, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 153612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 15,  4, 16, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 155521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 20, 15,  4, 16, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 157200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,691 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 132546.00)
2025-08-05 10:28:54,691 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:54,691 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:54,691 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,694 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:54,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,695 - ExplorationExpert - INFO - 探索路径生成完成，成本: 141115.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,695 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [31, 20, 21, 36, 5, 14, 9, 22, 32, 30, 28, 19, 24, 1, 7, 11, 34, 6, 38, 23, 18, 29, 0, 16, 4, 8, 37, 27, 13, 35, 33, 10, 26, 17, 12, 15, 2, 25, 3], 'cur_cost': 141115.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,695 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 141115.00)
2025-08-05 10:28:54,696 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:54,696 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:54,696 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,698 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:54,698 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,698 - ExplorationExpert - INFO - 探索路径生成完成，成本: 131317.0, 路径长度: 39, 收集中间解: 0
2025-08-05 10:28:54,699 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131317.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,699 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 131317.00)
2025-08-05 10:28:54,699 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:54,699 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,700 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,700 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 136811.0
2025-08-05 10:28:54,716 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:54,716 - ExploitationExpert - INFO - res_population_costs: [23771.0, 23763, 23763]
2025-08-05 10:28:54,716 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:54,718 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,718 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34431.0}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107185.0}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 106992.0}, {'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 88851.0}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33927.0}, {'tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 115897.0}, {'tour': array([27, 28,  5, 14, 23, 34, 12, 37, 33, 16, 13,  9, 38, 22, 21,  3, 19,
       20, 18, 31, 24,  7, 17, 30,  1, 11,  6,  4,  2,  0, 36, 25, 29, 26,
       35, 15, 10, 32,  8], dtype=int64), 'cur_cost': 132546.0}, {'tour': [31, 20, 21, 36, 5, 14, 9, 22, 32, 30, 28, 19, 24, 1, 7, 11, 34, 6, 38, 23, 18, 29, 0, 16, 4, 8, 37, 27, 13, 35, 33, 10, 26, 17, 12, 15, 2, 25, 3], 'cur_cost': 141115.0}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131317.0}, {'tour': array([32, 20, 19,  4, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38], dtype=int64), 'cur_cost': 136811.0}]
2025-08-05 10:28:54,720 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:54,721 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 223, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 223, 'cache_hits': 0, 'similarity_calculations': 1045, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,722 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([32, 20, 19,  4, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38], dtype=int64), 'cur_cost': 136811.0, 'intermediate_solutions': [{'tour': array([17, 21, 10,  4,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 161185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 17, 21, 10,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 156369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  4, 17, 21, 10, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 161348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  4, 17, 21,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 160365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  9,  4, 17, 21, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 159510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,722 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 136811.00)
2025-08-05 10:28:54,722 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:54,723 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:54,724 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34431.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107185.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 106992.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 88851.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 33927.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 115897.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 28,  5, 14, 23, 34, 12, 37, 33, 16, 13,  9, 38, 22, 21,  3, 19,
       20, 18, 31, 24,  7, 17, 30,  1, 11,  6,  4,  2,  0, 36, 25, 29, 26,
       35, 15, 10, 32,  8], dtype=int64), 'cur_cost': 132546.0, 'intermediate_solutions': [{'tour': array([ 4, 16, 31, 15, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 154267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15,  4, 16, 31, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 153994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 15,  4, 16, 31, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 153612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 15,  4, 16, 20, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 155521.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 20, 15,  4, 16, 14, 17, 25, 30, 38,  1, 22, 37, 13, 28, 18,  2,
        7,  5, 29, 34,  8, 12, 36, 26, 21, 10,  6, 27, 19, 23,  0, 11,  9,
        3, 24, 35, 33, 32], dtype=int64), 'cur_cost': 157200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [31, 20, 21, 36, 5, 14, 9, 22, 32, 30, 28, 19, 24, 1, 7, 11, 34, 6, 38, 23, 18, 29, 0, 16, 4, 8, 37, 27, 13, 35, 33, 10, 26, 17, 12, 15, 2, 25, 3], 'cur_cost': 141115.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131317.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 20, 19,  4, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38], dtype=int64), 'cur_cost': 136811.0, 'intermediate_solutions': [{'tour': array([17, 21, 10,  4,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 161185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 17, 21, 10,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 156369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  4, 17, 21, 10, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 161348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  4, 17, 21,  9, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 160365.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  9,  4, 17, 21, 33, 19, 13, 20, 28, 38, 18, 34, 29, 24,  6, 23,
       14, 36,  5, 37, 11, 22, 25, 12,  2,  0,  1, 16, 30,  7, 31, 32, 27,
       35, 15,  8, 26,  3], dtype=int64), 'cur_cost': 159510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:54,724 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:54,725 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,727 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33927.000, 多样性=0.942
2025-08-05 10:28:54,727 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:54,727 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:54,728 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:54,728 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07202209949946665, 'best_improvement': -0.4129185407296352}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03613053613053597}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0481471144905632, 'recent_improvements': [0.07172268424880758, -0.13489896229426326, -0.024571544732318814], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.8803418803418804, 'new_diversity': 0.8803418803418804, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:54,728 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:54,728 - __main__ - INFO - composite6_39 开始进化第 2 代
2025-08-05 10:28:54,729 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:54,729 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,729 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=33927.000, 多样性=0.942
2025-08-05 10:28:54,730 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:54,732 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.942
2025-08-05 10:28:54,732 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:54,733 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.880
2025-08-05 10:28:54,736 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:54,736 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:54,737 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:28:54,737 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:28:54,762 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 2156.923, 聚类评分: 0.000, 覆盖率: 0.104, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:54,763 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:54,763 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:54,763 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 10:28:54,767 - visualization.landscape_visualizer - INFO - 插值约束: 107 个点被约束到最小值 23763.00
2025-08-05 10:28:54,768 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.5%, 梯度: 3954.43 → 3777.35
2025-08-05 10:28:54,884 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_87_20250805_102854.html
2025-08-05 10:28:54,952 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_87_20250805_102854.html
2025-08-05 10:28:54,952 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 87
2025-08-05 10:28:54,952 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:54,952 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2160秒
2025-08-05 10:28:54,953 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 2156.923076923076, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 1864014441.0248523, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1036, 'fitness_entropy': 0.9131226292013046, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.104)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 2156.923)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360934.7636855, 'performance_metrics': {}}}
2025-08-05 10:28:54,953 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:54,953 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:54,953 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:54,953 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:54,954 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:54,954 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:54,954 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:54,955 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,955 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:54,955 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:54,955 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,956 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:54,956 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:54,957 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:54,957 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:54,957 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,959 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,961 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32822.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,961 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32822.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 29, 28, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34432.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 13, 14, 10, 11, 12, 37, 32, 35], 'cur_cost': 39348.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 7, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 39696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,961 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 32822.00)
2025-08-05 10:28:54,962 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:54,962 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:54,962 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,963 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,964 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36470.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,965 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36470.0, 'intermediate_solutions': [{'tour': [36, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 5, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 110426.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 7, 37, 25, 31, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 110802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 34, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,965 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 36470.00)
2025-08-05 10:28:54,965 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:54,965 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:54,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,966 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39675.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,967 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39675.0, 'intermediate_solutions': [{'tour': [4, 3, 31, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 25, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 110647.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 11, 17, 30, 27, 26, 10, 35, 21, 31, 29, 2, 16, 37], 'cur_cost': 109096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 2, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 110163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,967 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 39675.00)
2025-08-05 10:28:54,968 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:54,968 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:54,968 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,969 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30807.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,970 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 30807.0, 'intermediate_solutions': [{'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 29, 18, 22, 16, 17, 35, 10, 14], 'cur_cost': 88967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 9, 8, 26, 27, 30, 28, 1, 23, 6, 3, 2, 24, 25, 34, 15, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 98492.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 31, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 92797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,970 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 30807.00)
2025-08-05 10:28:54,970 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:54,970 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:54,970 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,971 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,971 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,972 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32731.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,973 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 32731.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 35, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 30, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 61488.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 19, 15, 17, 16, 18, 37, 34, 33, 36, 38, 35, 32], 'cur_cost': 37191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 17, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 15, 19], 'cur_cost': 39791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,973 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 32731.00)
2025-08-05 10:28:54,973 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:54,973 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:54,973 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,974 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:54,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,975 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29394.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,975 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 29394.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 26, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 24, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 122179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 0, 36, 28, 30, 27, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 120071.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 5, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 119747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,975 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 29394.00)
2025-08-05 10:28:54,976 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:54,976 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:54,976 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,978 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:54,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,980 - ExplorationExpert - INFO - 探索路径生成完成，成本: 74584.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:54,980 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 74584.0, 'intermediate_solutions': [{'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 9, 38, 22, 21, 3, 19, 20, 18, 31, 24, 7, 17, 30, 1, 11, 6, 4, 2, 0, 36, 32, 29, 26, 35, 15, 10, 25, 8], 'cur_cost': 132120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 4, 6, 11, 1, 30, 17, 7, 24, 31, 18, 20, 19, 3, 21, 22, 38, 9, 2, 0, 36, 25, 29, 26, 35, 15, 10, 32, 8], 'cur_cost': 137312.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 9, 38, 22, 21, 3, 19, 20, 18, 31, 24, 7, 17, 30, 1, 11, 6, 4, 2, 0, 36, 25, 29, 26, 35, 15, 10, 32, 8], 'cur_cost': 132546.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,980 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 74584.00)
2025-08-05 10:28:54,980 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:54,980 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,980 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,981 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 154129.0
2025-08-05 10:28:54,992 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:54,992 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23771.0, 23763.0, 23763, 23763]
2025-08-05 10:28:54,992 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:54,994 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,994 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32822.0}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36470.0}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39675.0}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 30807.0}, {'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 32731.0}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 29394.0}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 74584.0}, {'tour': array([32,  5, 25, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4], dtype=int64), 'cur_cost': 154129.0}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131317.0}, {'tour': [32, 20, 19, 4, 16, 37, 34, 28, 18, 6, 33, 11, 36, 25, 17, 0, 35, 5, 1, 23, 29, 26, 22, 24, 9, 12, 21, 7, 10, 8, 15, 2, 13, 31, 30, 14, 3, 27, 38], 'cur_cost': 136811.0}]
2025-08-05 10:28:54,995 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:54,995 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 224, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 224, 'cache_hits': 0, 'similarity_calculations': 1047, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,996 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([32,  5, 25, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4], dtype=int64), 'cur_cost': 154129.0, 'intermediate_solutions': [{'tour': array([21, 20, 31, 36,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 145612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 21, 20, 31,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 146478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 36, 21, 20, 31, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 146015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 36, 21, 20,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 143218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31,  5, 36, 21, 20, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 143904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,996 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 154129.00)
2025-08-05 10:28:54,996 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:54,996 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:54,996 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,998 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:54,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86774.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,000 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [22, 9, 13, 20, 8, 14, 32, 36, 25, 21, 38, 11, 7, 35, 16, 15, 19, 1, 2, 0, 4, 27, 29, 28, 6, 10, 5, 24, 3, 31, 26, 17, 18, 23, 34, 33, 37, 12, 30], 'cur_cost': 86774.0, 'intermediate_solutions': [{'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 26, 4, 17, 37, 0, 21, 29, 27, 34, 10, 16, 6, 20], 'cur_cost': 126083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 9, 2, 23, 8, 33, 5, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 5, 22, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 129512.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,000 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 86774.00)
2025-08-05 10:28:55,000 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:55,000 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,001 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,001 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 146693.0
2025-08-05 10:28:55,018 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:28:55,018 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23771.0, 23763.0, 23763, 23763, 23763, 23763]
2025-08-05 10:28:55,018 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64)]
2025-08-05 10:28:55,023 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,023 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32822.0}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36470.0}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39675.0}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 30807.0}, {'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 32731.0}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 29394.0}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 74584.0}, {'tour': array([32,  5, 25, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4], dtype=int64), 'cur_cost': 154129.0}, {'tour': [22, 9, 13, 20, 8, 14, 32, 36, 25, 21, 38, 11, 7, 35, 16, 15, 19, 1, 2, 0, 4, 27, 29, 28, 6, 10, 5, 24, 3, 31, 26, 17, 18, 23, 34, 33, 37, 12, 30], 'cur_cost': 86774.0}, {'tour': array([ 4,  5, 34, 19, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9], dtype=int64), 'cur_cost': 146693.0}]
2025-08-05 10:28:55,025 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:55,025 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 225, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 225, 'cache_hits': 0, 'similarity_calculations': 1050, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,026 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 4,  5, 34, 19, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9], dtype=int64), 'cur_cost': 146693.0, 'intermediate_solutions': [{'tour': array([19, 20, 32,  4, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 141261.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 19, 20, 32, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 141362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  4, 19, 20, 32, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 136835.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  4, 19, 20, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 137278.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 16,  4, 19, 20, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 136869.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,026 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 146693.00)
2025-08-05 10:28:55,027 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:55,027 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:55,029 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32822.0, 'intermediate_solutions': [{'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 29, 28, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 34432.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 3, 7, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 13, 14, 10, 11, 12, 37, 32, 35], 'cur_cost': 39348.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 5, 8, 9, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 7, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 39696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36470.0, 'intermediate_solutions': [{'tour': [36, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 5, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 110426.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 7, 37, 25, 31, 11, 32, 3, 9, 22, 23, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 34, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 110802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 20, 21, 24, 1, 2, 26, 27, 16, 18, 15, 31, 25, 37, 7, 11, 32, 3, 9, 22, 23, 34, 36, 17, 14, 4, 19, 0, 30, 10, 8, 35, 38, 13, 12, 28, 29, 33, 6], 'cur_cost': 107095.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39675.0, 'intermediate_solutions': [{'tour': [4, 3, 31, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 2, 29, 25, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 110647.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 11, 17, 30, 27, 26, 10, 35, 21, 31, 29, 2, 16, 37], 'cur_cost': 109096.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 25, 36, 9, 14, 24, 22, 32, 12, 13, 2, 5, 33, 8, 23, 7, 34, 6, 38, 19, 18, 15, 0, 28, 1, 20, 37, 16, 29, 31, 21, 35, 10, 26, 27, 30, 17, 11], 'cur_cost': 110163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 30807.0, 'intermediate_solutions': [{'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 29, 18, 22, 16, 17, 35, 10, 14], 'cur_cost': 88967.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 9, 8, 26, 27, 30, 28, 1, 23, 6, 3, 2, 24, 25, 34, 15, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 98492.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 20, 5, 12, 32, 33, 37, 36, 21, 38, 19, 15, 31, 34, 25, 24, 2, 3, 6, 23, 1, 28, 30, 27, 26, 8, 9, 11, 13, 0, 7, 35, 18, 22, 16, 17, 29, 10, 14], 'cur_cost': 92797.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 32731.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 35, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 30, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19], 'cur_cost': 61488.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 19, 15, 17, 16, 18, 37, 34, 33, 36, 38, 35, 32], 'cur_cost': 37191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 20, 25, 21, 22, 17, 23, 24, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 15, 19], 'cur_cost': 39791.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 29394.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 26, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 24, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 122179.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 25, 37, 2, 4, 26, 31, 0, 36, 28, 30, 27, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 120071.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 24, 12, 8, 33, 20, 16, 18, 22, 23, 5, 25, 37, 2, 4, 26, 31, 27, 30, 28, 36, 0, 14, 11, 15, 9, 34, 10, 29, 38, 21, 35, 13, 19, 17, 7, 1, 32], 'cur_cost': 119747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 74584.0, 'intermediate_solutions': [{'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 9, 38, 22, 21, 3, 19, 20, 18, 31, 24, 7, 17, 30, 1, 11, 6, 4, 2, 0, 36, 32, 29, 26, 35, 15, 10, 25, 8], 'cur_cost': 132120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 4, 6, 11, 1, 30, 17, 7, 24, 31, 18, 20, 19, 3, 21, 22, 38, 9, 2, 0, 36, 25, 29, 26, 35, 15, 10, 32, 8], 'cur_cost': 137312.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 28, 5, 14, 23, 34, 12, 37, 33, 16, 13, 9, 38, 22, 21, 3, 19, 20, 18, 31, 24, 7, 17, 30, 1, 11, 6, 4, 2, 0, 36, 25, 29, 26, 35, 15, 10, 32, 8], 'cur_cost': 132546.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([32,  5, 25, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4], dtype=int64), 'cur_cost': 154129.0, 'intermediate_solutions': [{'tour': array([21, 20, 31, 36,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 145612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 21, 20, 31,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 146478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 36, 21, 20, 31, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 146015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31, 36, 21, 20,  5, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 143218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31,  5, 36, 21, 20, 14,  9, 22, 32, 30, 28, 19, 24,  1,  7, 11, 34,
        6, 38, 23, 18, 29,  0, 16,  4,  8, 37, 27, 13, 35, 33, 10, 26, 17,
       12, 15,  2, 25,  3]), 'cur_cost': 143904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [22, 9, 13, 20, 8, 14, 32, 36, 25, 21, 38, 11, 7, 35, 16, 15, 19, 1, 2, 0, 4, 27, 29, 28, 6, 10, 5, 24, 3, 31, 26, 17, 18, 23, 34, 33, 37, 12, 30], 'cur_cost': 86774.0, 'intermediate_solutions': [{'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 5, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 26, 4, 17, 37, 0, 21, 29, 27, 34, 10, 16, 6, 20], 'cur_cost': 126083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 22, 9, 2, 23, 8, 33, 5, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 131315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 25, 7, 1, 32, 24, 36, 12, 5, 22, 33, 8, 23, 2, 9, 19, 15, 30, 28, 18, 14, 13, 11, 35, 38, 31, 34, 4, 17, 37, 0, 21, 29, 27, 26, 10, 16, 6, 20], 'cur_cost': 129512.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  5, 34, 19, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9], dtype=int64), 'cur_cost': 146693.0, 'intermediate_solutions': [{'tour': array([19, 20, 32,  4, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 141261.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 19, 20, 32, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 141362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  4, 19, 20, 32, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 136835.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32,  4, 19, 20, 16, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 137278.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 16,  4, 19, 20, 37, 34, 28, 18,  6, 33, 11, 36, 25, 17,  0, 35,
        5,  1, 23, 29, 26, 22, 24,  9, 12, 21,  7, 10,  8, 15,  2, 13, 31,
       30, 14,  3, 27, 38]), 'cur_cost': 136869.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:55,029 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:55,030 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:55,034 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29394.000, 多样性=0.938
2025-08-05 10:28:55,034 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:55,034 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:55,034 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:55,035 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.13202314577139052, 'best_improvement': 0.13361039879741798}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00483675937122146}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03143843139739829, 'recent_improvements': [-0.13489896229426326, -0.024571544732318814, -0.07202209949946665], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.8021978021978021, 'new_diversity': 0.8021978021978021, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:55,036 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:55,037 - __main__ - INFO - composite6_39 开始进化第 3 代
2025-08-05 10:28:55,037 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:55,037 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:55,038 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=29394.000, 多样性=0.938
2025-08-05 10:28:55,038 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:55,041 - PathExpert - INFO - 路径结构分析完成: 公共边数量=17, 路径相似性=0.938
2025-08-05 10:28:55,042 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:55,050 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.802
2025-08-05 10:28:55,054 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:55,054 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:55,054 - LandscapeExpert - INFO - 添加精英解数据: 8个精英解
2025-08-05 10:28:55,054 - LandscapeExpert - INFO - 数据提取成功: 18个路径, 18个适应度值
2025-08-05 10:28:55,104 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.389, 适应度梯度: -12462.344, 聚类评分: 0.000, 覆盖率: 0.105, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:55,104 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:55,105 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:55,105 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 10:28:55,112 - visualization.landscape_visualizer - INFO - 插值约束: 303 个点被约束到最小值 23763.00
2025-08-05 10:28:55,115 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.5%, 梯度: 2291.55 → 2120.62
2025-08-05 10:28:55,235 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_88_20250805_102855.html
2025-08-05 10:28:55,283 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_88_20250805_102855.html
2025-08-05 10:28:55,284 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 88
2025-08-05 10:28:55,284 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:55,284 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2299秒
2025-08-05 10:28:55,284 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3888888888888889, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -12462.344444444443, 'local_optima_density': 0.3888888888888889, 'gradient_variance': 1537450386.1724691, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.105, 'fitness_entropy': 0.5970357553054272, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12462.344)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.105)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360935.1048698, 'performance_metrics': {}}}
2025-08-05 10:28:55,284 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:55,284 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:55,284 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:55,285 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:55,285 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,285 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:55,285 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,285 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:55,285 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:55,285 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,285 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:55,286 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:55,286 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:55,286 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:55,286 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:55,286 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,287 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:55,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,288 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113694.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,288 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113694.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 20, 25, 36, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 21, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 44000.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 10, 11, 12, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 30, 26, 31, 27, 28, 29], 'cur_cost': 32943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,288 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 113694.00)
2025-08-05 10:28:55,288 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:55,288 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:55,288 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,289 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,289 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,289 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,290 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,291 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37769.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,291 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37769.0, 'intermediate_solutions': [{'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 13, 11, 10, 14, 12, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36521.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 2, 1, 37, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 46510.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 30, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 28, 29], 'cur_cost': 50261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,291 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 37769.00)
2025-08-05 10:28:55,291 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:55,291 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:55,291 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,292 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,292 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,293 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41397.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,293 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 41397.0, 'intermediate_solutions': [{'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 13, 10, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39693.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 27, 31, 26, 2, 1, 30, 28, 29], 'cur_cost': 43814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 7, 5, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39725.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,293 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 41397.00)
2025-08-05 10:28:55,294 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:55,294 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:55,294 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,296 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:55,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93426.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,297 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 93426.0, 'intermediate_solutions': [{'tour': [24, 18, 17, 21, 22, 23, 0, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 35938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 28, 30, 27, 31, 26, 2, 29], 'cur_cost': 35129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 10, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39617.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,297 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 93426.00)
2025-08-05 10:28:55,297 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:55,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,298 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 39
2025-08-05 10:28:55,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113824.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,299 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 113824.0, 'intermediate_solutions': [{'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 14, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 18, 13], 'cur_cost': 61824.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 32, 35, 38, 36, 33, 37, 12, 11, 10, 14, 13], 'cur_cost': 32811.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 21, 8, 9, 5, 20, 25, 22, 23, 6, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 36511.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,299 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 113824.00)
2025-08-05 10:28:55,299 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:55,299 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:55,300 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,302 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:55,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,303 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102140.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,303 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 102140.0, 'intermediate_solutions': [{'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 24, 9, 6, 20, 25, 21, 22, 23, 8, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 35497.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 37, 32, 35, 38], 'cur_cost': 29366.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 35, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 32, 37], 'cur_cost': 35062.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,303 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 102140.00)
2025-08-05 10:28:55,303 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:55,303 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:55,303 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,306 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:55,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103995.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,308 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 103995.0, 'intermediate_solutions': [{'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 1, 16, 18, 4, 3, 2, 0, 17, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 88030.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 19, 36, 26], 'cur_cost': 76248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 21, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 73566.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,308 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 103995.00)
2025-08-05 10:28:55,308 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:55,308 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,309 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 146774.0
2025-08-05 10:28:55,319 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:55,319 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763.0, 23763, 23763, 23763, 23763, 23771.0, 23763]
2025-08-05 10:28:55,319 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64)]
2025-08-05 10:28:55,322 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,323 - ExploitationExpert - INFO - populations: [{'tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113694.0}, {'tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37769.0}, {'tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 41397.0}, {'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 93426.0}, {'tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 113824.0}, {'tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 102140.0}, {'tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 103995.0}, {'tour': array([16, 34, 18, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2], dtype=int64), 'cur_cost': 146774.0}, {'tour': [22, 9, 13, 20, 8, 14, 32, 36, 25, 21, 38, 11, 7, 35, 16, 15, 19, 1, 2, 0, 4, 27, 29, 28, 6, 10, 5, 24, 3, 31, 26, 17, 18, 23, 34, 33, 37, 12, 30], 'cur_cost': 86774.0}, {'tour': [4, 5, 34, 19, 28, 10, 8, 2, 15, 1, 27, 6, 25, 23, 17, 24, 16, 31, 7, 33, 36, 26, 3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32, 21, 0, 11, 29, 9], 'cur_cost': 146693.0}]
2025-08-05 10:28:55,324 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:55,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 226, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 226, 'cache_hits': 0, 'similarity_calculations': 1054, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,325 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([16, 34, 18, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2], dtype=int64), 'cur_cost': 146774.0, 'intermediate_solutions': [{'tour': array([25,  5, 32, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 152892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 25,  5, 32, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 150745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 15, 25,  5, 32, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 153907.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 15, 25,  5, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 151006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 11, 15, 25,  5, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 154172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,325 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 146774.00)
2025-08-05 10:28:55,325 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:55,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,325 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 152095.0
2025-08-05 10:28:55,334 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:55,334 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763.0, 23763, 23763, 23763, 23763, 23771.0, 23763]
2025-08-05 10:28:55,334 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64)]
2025-08-05 10:28:55,337 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,337 - ExploitationExpert - INFO - populations: [{'tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113694.0}, {'tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37769.0}, {'tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 41397.0}, {'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 93426.0}, {'tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 113824.0}, {'tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 102140.0}, {'tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 103995.0}, {'tour': array([16, 34, 18, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2], dtype=int64), 'cur_cost': 146774.0}, {'tour': array([ 4, 11, 24, 12,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7], dtype=int64), 'cur_cost': 152095.0}, {'tour': [4, 5, 34, 19, 28, 10, 8, 2, 15, 1, 27, 6, 25, 23, 17, 24, 16, 31, 7, 33, 36, 26, 3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32, 21, 0, 11, 29, 9], 'cur_cost': 146693.0}]
2025-08-05 10:28:55,338 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:55,338 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 227, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 227, 'cache_hits': 0, 'similarity_calculations': 1059, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,338 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 4, 11, 24, 12,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7], dtype=int64), 'cur_cost': 152095.0, 'intermediate_solutions': [{'tour': array([13,  9, 22, 20,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 84890.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 13,  9, 22,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 86812.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 20, 13,  9, 22, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 89587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 20, 13,  9,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 83017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  8, 20, 13,  9, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 86787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,339 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 152095.00)
2025-08-05 10:28:55,339 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:55,339 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,339 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,339 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 139946.0
2025-08-05 10:28:55,352 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:55,352 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763.0, 23763, 23763, 23763, 23763, 23771.0, 23763, 23763]
2025-08-05 10:28:55,353 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:55,359 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,359 - ExploitationExpert - INFO - populations: [{'tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113694.0}, {'tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37769.0}, {'tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 41397.0}, {'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 93426.0}, {'tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 113824.0}, {'tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 102140.0}, {'tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 103995.0}, {'tour': array([16, 34, 18, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2], dtype=int64), 'cur_cost': 146774.0}, {'tour': array([ 4, 11, 24, 12,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7], dtype=int64), 'cur_cost': 152095.0}, {'tour': array([19, 27, 17,  6, 13, 12, 15,  3, 16, 29, 20, 22, 21,  0, 25, 24, 36,
       18, 35,  4, 30, 38, 33, 31, 11, 34, 14, 37, 32, 28,  9, 10, 26, 23,
        1,  8,  7,  5,  2], dtype=int64), 'cur_cost': 139946.0}]
2025-08-05 10:28:55,361 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:55,361 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 228, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 228, 'cache_hits': 0, 'similarity_calculations': 1065, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,362 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([19, 27, 17,  6, 13, 12, 15,  3, 16, 29, 20, 22, 21,  0, 25, 24, 36,
       18, 35,  4, 30, 38, 33, 31, 11, 34, 14, 37, 32, 28,  9, 10, 26, 23,
        1,  8,  7,  5,  2], dtype=int64), 'cur_cost': 139946.0, 'intermediate_solutions': [{'tour': array([34,  5,  4, 19, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 147044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 34,  5,  4, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 145296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 19, 34,  5,  4, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 146716.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 19, 34,  5, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 148206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 28, 19, 34,  5, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 141777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,363 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 139946.00)
2025-08-05 10:28:55,363 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:55,363 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:55,365 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113694.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 20, 25, 36, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 21, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 44000.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 10, 11, 12, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 32905.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 20, 25, 21, 22, 23, 24, 6, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 19, 3, 4, 1, 2, 30, 26, 31, 27, 28, 29], 'cur_cost': 32943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37769.0, 'intermediate_solutions': [{'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 13, 11, 10, 14, 12, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36521.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 2, 1, 37, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 46510.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 5, 17, 15, 19, 18, 22, 23, 24, 21, 25, 20, 6, 8, 9, 7, 12, 11, 10, 14, 13, 32, 35, 38, 30, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 28, 29], 'cur_cost': 50261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 41397.0, 'intermediate_solutions': [{'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 13, 10, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39693.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 27, 31, 26, 2, 1, 30, 28, 29], 'cur_cost': 43814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 14, 21, 22, 23, 24, 25, 20, 6, 8, 9, 7, 5, 12, 11, 10, 13, 32, 35, 38, 36, 33, 34, 37, 18, 16, 17, 15, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39725.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 93426.0, 'intermediate_solutions': [{'tour': [24, 18, 17, 21, 22, 23, 0, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 35938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 28, 30, 27, 31, 26, 2, 29], 'cur_cost': 35129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 21, 22, 23, 24, 25, 20, 6, 8, 9, 5, 7, 12, 11, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 3, 4, 1, 10, 2, 26, 31, 27, 30, 28, 29], 'cur_cost': 39617.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 113824.0, 'intermediate_solutions': [{'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 14, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 18, 13], 'cur_cost': 61824.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 21, 8, 9, 5, 6, 20, 25, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 32, 35, 38, 36, 33, 37, 12, 11, 10, 14, 13], 'cur_cost': 32811.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 21, 8, 9, 5, 20, 25, 22, 23, 6, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 36511.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 102140.0, 'intermediate_solutions': [{'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 24, 9, 6, 20, 25, 21, 22, 23, 8, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 35497.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 37, 32, 35, 38], 'cur_cost': 29366.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 13, 11, 12, 10, 14, 7, 5, 8, 9, 6, 20, 25, 35, 21, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 32, 37], 'cur_cost': 35062.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 103995.0, 'intermediate_solutions': [{'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 1, 16, 18, 4, 3, 2, 0, 17, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 88030.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 21, 9, 10, 5, 24, 19, 36, 26], 'cur_cost': 76248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 11, 25, 23, 7, 8, 6, 13, 14, 20, 21, 33, 32, 37, 22, 34, 35, 15, 38, 17, 16, 18, 4, 3, 2, 0, 1, 28, 31, 30, 29, 27, 9, 10, 5, 24, 36, 19, 26], 'cur_cost': 73566.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 34, 18, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2], dtype=int64), 'cur_cost': 146774.0, 'intermediate_solutions': [{'tour': array([25,  5, 32, 15, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 152892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 25,  5, 32, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 150745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 15, 25,  5, 32, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 153907.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 15, 25,  5, 11, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 151006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 11, 15, 25,  5, 28,  1, 21, 16,  6, 36, 37, 22, 38,  0, 29, 14,
       20, 26, 18,  3, 13, 30,  9, 31, 24, 33, 19,  2, 35, 12, 34,  8, 10,
        7, 23, 27, 17,  4]), 'cur_cost': 154172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 11, 24, 12,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7], dtype=int64), 'cur_cost': 152095.0, 'intermediate_solutions': [{'tour': array([13,  9, 22, 20,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 84890.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 13,  9, 22,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 86812.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 20, 13,  9, 22, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 89587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 20, 13,  9,  8, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 83017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  8, 20, 13,  9, 14, 32, 36, 25, 21, 38, 11,  7, 35, 16, 15, 19,
        1,  2,  0,  4, 27, 29, 28,  6, 10,  5, 24,  3, 31, 26, 17, 18, 23,
       34, 33, 37, 12, 30]), 'cur_cost': 86787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 27, 17,  6, 13, 12, 15,  3, 16, 29, 20, 22, 21,  0, 25, 24, 36,
       18, 35,  4, 30, 38, 33, 31, 11, 34, 14, 37, 32, 28,  9, 10, 26, 23,
        1,  8,  7,  5,  2], dtype=int64), 'cur_cost': 139946.0, 'intermediate_solutions': [{'tour': array([34,  5,  4, 19, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 147044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 34,  5,  4, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 145296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 19, 34,  5,  4, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 146716.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 19, 34,  5, 28, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 148206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 28, 19, 34,  5, 10,  8,  2, 15,  1, 27,  6, 25, 23, 17, 24, 16,
       31,  7, 33, 36, 26,  3, 18, 13, 35, 38, 20, 30, 12, 37, 22, 14, 32,
       21,  0, 11, 29,  9]), 'cur_cost': 141777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:55,366 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:55,366 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:55,370 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=37769.000, 多样性=0.958
2025-08-05 10:28:55,371 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:55,371 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:55,371 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:55,372 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.28180788837893544, 'best_improvement': -0.2849220929441383}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.021263669501822603}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07829734525185468, 'recent_improvements': [-0.024571544732318814, -0.07202209949946665, 0.13202314577139052], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 23763, 'new_best_cost': 23763, 'quality_improvement': 0.0, 'old_diversity': 0.7857549857549858, 'new_diversity': 0.7857549857549858, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:55,374 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:55,374 - __main__ - INFO - composite6_39 开始进化第 4 代
2025-08-05 10:28:55,374 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:55,374 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:55,375 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=37769.000, 多样性=0.958
2025-08-05 10:28:55,376 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:55,378 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.958
2025-08-05 10:28:55,379 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:55,384 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.786
2025-08-05 10:28:55,387 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:55,388 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:55,388 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:55,388 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:55,451 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.450, 适应度梯度: -15909.490, 聚类评分: 0.000, 覆盖率: 0.106, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:55,451 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:55,451 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:55,452 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite6_39
2025-08-05 10:28:55,459 - visualization.landscape_visualizer - INFO - 插值约束: 15 个点被约束到最小值 23763.00
2025-08-05 10:28:55,461 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.9%, 梯度: 5050.66 → 4548.94
2025-08-05 10:28:55,578 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\landscape_composite6_39_iter_89_20250805_102855.html
2025-08-05 10:28:55,625 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite6_39\dashboard_composite6_39_iter_89_20250805_102855.html
2025-08-05 10:28:55,625 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 89
2025-08-05 10:28:55,625 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:55,625 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2372秒
2025-08-05 10:28:55,626 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -15909.49, 'local_optima_density': 0.45, 'gradient_variance': 1511347924.0939002, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1061, 'fitness_entropy': 0.8213734906185948, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -15909.490)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.106)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360935.4519026, 'performance_metrics': {}}}
2025-08-05 10:28:55,626 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:55,626 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:55,626 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:55,626 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:55,627 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,627 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:55,627 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,627 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:55,627 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:55,627 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-05 10:28:55,628 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:55,628 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:55,628 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:55,628 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:55,628 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:55,628 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,629 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,630 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36847.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,630 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 17, 8, 12, 11, 10, 14, 13, 7, 5, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 18, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36847.0, 'intermediate_solutions': [{'tour': [12, 8, 17, 11, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 36, 38, 14, 35, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 113724.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 8, 17, 36, 14, 38, 35, 32, 13, 10, 4, 2, 1, 5, 33, 24, 23, 20, 22, 21, 15, 11, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 0, 9, 28, 25], 'cur_cost': 114902.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 8, 17, 15, 21, 22, 20, 23, 24, 33, 5, 1, 2, 4, 10, 13, 32, 35, 38, 14, 36, 34, 37, 30, 16, 29, 31, 19, 18, 27, 6, 3, 26, 7, 11, 0, 9, 28, 25], 'cur_cost': 103133.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,630 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 36847.00)
2025-08-05 10:28:55,630 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:55,630 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:55,630 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,631 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34912.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,632 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 3, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 17, 1, 2, 4, 26, 31, 27, 30, 28, 29], 'cur_cost': 34912.0, 'intermediate_solutions': [{'tour': [0, 12, 1, 5, 8, 9, 6, 7, 20, 22, 21, 25, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 37838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 1, 5, 8, 9, 6, 30, 27, 31, 26, 2, 4, 3, 24, 23, 22, 21, 25, 20, 7, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 47627.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 1, 5, 8, 9, 18, 6, 7, 20, 25, 21, 22, 23, 24, 3, 4, 2, 26, 31, 27, 30, 28, 29, 17, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37, 11, 10, 14, 13], 'cur_cost': 47227.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,632 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 34912.00)
2025-08-05 10:28:55,632 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:55,632 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:55,632 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,636 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:55,636 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,636 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,636 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106959.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,637 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 24, 8, 14, 10, 38, 36, 20, 34, 6, 7, 22, 4, 31, 3, 29, 25, 1, 23, 16, 18, 21, 9, 32, 37, 17, 19, 2, 28, 27, 15, 30, 13, 35, 12, 33, 11, 0, 26], 'cur_cost': 106959.0, 'intermediate_solutions': [{'tour': [20, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 0, 6, 8, 9, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 44997.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 11, 15, 9, 8, 6, 20, 25, 21, 24, 23, 22, 19, 18, 16, 5, 7, 12, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 51044.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 11, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 7, 12, 10, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 14, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 50369.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,637 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 106959.00)
2025-08-05 10:28:55,637 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:55,637 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:55,637 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,639 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,640 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36571.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,640 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 17, 7, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36571.0, 'intermediate_solutions': [{'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 29, 1, 3, 12, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 38, 15, 5, 26, 31, 30], 'cur_cost': 98354.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 14, 13, 35, 17, 38, 15, 5, 26, 31, 30], 'cur_cost': 93455.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 34, 32, 10, 11, 36, 23, 24, 0, 20, 7, 21, 4, 28, 8, 6, 9, 12, 1, 3, 29, 2, 27, 18, 19, 16, 25, 22, 37, 17, 35, 13, 14, 15, 5, 26, 38, 31, 30], 'cur_cost': 105963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,640 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 36571.00)
2025-08-05 10:28:55,641 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:55,641 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:55,641 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,642 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,643 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33817.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,643 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 21, 3, 9, 8, 5, 7, 6, 20, 25, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33817.0, 'intermediate_solutions': [{'tour': [33, 18, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 12, 15, 17, 30, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 128564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 0, 14, 11, 27, 7, 28, 8, 6, 26, 31, 30, 17, 15, 18, 19, 37, 34, 36, 29, 38, 35, 32, 2, 10, 3, 24, 4, 21, 23, 25, 20, 13, 22, 9, 16, 5, 1, 12], 'cur_cost': 113824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 12, 1, 5, 16, 9, 22, 13, 20, 25, 23, 21, 4, 24, 30, 3, 10, 2, 32, 35, 38, 29, 36, 34, 37, 19, 18, 15, 17, 31, 26, 6, 8, 28, 7, 27, 11, 14, 0], 'cur_cost': 117525.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,643 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 33817.00)
2025-08-05 10:28:55,643 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:55,643 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:55,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 39
2025-08-05 10:28:55,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39683.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,645 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 18, 11, 12, 10, 14, 13, 7, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 39683.0, 'intermediate_solutions': [{'tour': [30, 1, 20, 13, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 8, 7, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 103089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 1, 20, 8, 22, 33, 32, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 11, 10, 35, 14, 5, 23, 31, 15], 'cur_cost': 101208.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 1, 20, 8, 22, 33, 37, 6, 12, 34, 21, 24, 38, 36, 25, 16, 2, 26, 9, 0, 29, 28, 17, 18, 19, 4, 27, 3, 13, 7, 32, 11, 10, 35, 23, 5, 14, 31, 15], 'cur_cost': 106423.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,645 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 39683.00)
2025-08-05 10:28:55,645 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:55,645 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:55,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:55,647 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 39
2025-08-05 10:28:55,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:55,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85925.0, 路径长度: 39, 收集中间解: 3
2025-08-05 10:28:55,648 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [14, 25, 2, 1, 22, 20, 8, 36, 11, 12, 33, 21, 6, 24, 23, 0, 30, 3, 9, 35, 34, 38, 37, 7, 4, 28, 26, 18, 16, 19, 17, 15, 27, 29, 5, 13, 10, 32, 31], 'cur_cost': 85925.0, 'intermediate_solutions': [{'tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 10, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 23, 13, 14, 5, 16], 'cur_cost': 119015.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [36, 20, 8, 22, 34, 35, 9, 31, 0, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 15, 24, 11, 10, 13, 14, 5, 16], 'cur_cost': 109187.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [36, 20, 8, 22, 0, 31, 9, 35, 34, 32, 7, 33, 12, 38, 25, 6, 2, 23, 21, 1, 29, 17, 37, 19, 4, 3, 26, 18, 27, 28, 30, 16, 15, 24, 11, 10, 13, 14, 5], 'cur_cost': 97507.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:55,649 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 85925.00)
2025-08-05 10:28:55,649 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:55,649 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,649 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,649 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 133433.0
2025-08-05 10:28:55,660 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:55,660 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763.0, 23763, 23763, 23763, 23763, 23763, 23763, 23771.0, 23763, 23763, 23763]
2025-08-05 10:28:55,660 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:55,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,664 - ExploitationExpert - INFO - populations: [{'tour': [0, 17, 8, 12, 11, 10, 14, 13, 7, 5, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 18, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36847.0}, {'tour': [0, 18, 3, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 17, 1, 2, 4, 26, 31, 27, 30, 28, 29], 'cur_cost': 34912.0}, {'tour': [5, 24, 8, 14, 10, 38, 36, 20, 34, 6, 7, 22, 4, 31, 3, 29, 25, 1, 23, 16, 18, 21, 9, 32, 37, 17, 19, 2, 28, 27, 15, 30, 13, 35, 12, 33, 11, 0, 26], 'cur_cost': 106959.0}, {'tour': [0, 17, 7, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36571.0}, {'tour': [0, 21, 3, 9, 8, 5, 7, 6, 20, 25, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33817.0}, {'tour': [0, 5, 18, 11, 12, 10, 14, 13, 7, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 39683.0}, {'tour': [14, 25, 2, 1, 22, 20, 8, 36, 11, 12, 33, 21, 6, 24, 23, 0, 30, 3, 9, 35, 34, 38, 37, 7, 4, 28, 26, 18, 16, 19, 17, 15, 27, 29, 5, 13, 10, 32, 31], 'cur_cost': 85925.0}, {'tour': array([ 3,  7, 32,  5,  6, 35,  2, 33, 16, 13, 38, 17,  8, 12, 19,  4, 36,
       18, 26, 29, 31, 15, 37, 23, 11,  1, 34, 24, 21, 25, 10, 14,  0, 22,
        9, 27, 30, 20, 28], dtype=int64), 'cur_cost': 133433.0}, {'tour': [4, 11, 24, 12, 2, 22, 30, 5, 23, 21, 18, 29, 8, 9, 16, 25, 13, 36, 27, 32, 35, 1, 33, 6, 31, 26, 3, 20, 38, 28, 37, 19, 34, 17, 14, 10, 15, 0, 7], 'cur_cost': 152095.0}, {'tour': [19, 27, 17, 6, 13, 12, 15, 3, 16, 29, 20, 22, 21, 0, 25, 24, 36, 18, 35, 4, 30, 38, 33, 31, 11, 34, 14, 37, 32, 28, 9, 10, 26, 23, 1, 8, 7, 5, 2], 'cur_cost': 139946.0}]
2025-08-05 10:28:55,665 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:55,665 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 229, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 229, 'cache_hits': 0, 'similarity_calculations': 1072, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,666 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 3,  7, 32,  5,  6, 35,  2, 33, 16, 13, 38, 17,  8, 12, 19,  4, 36,
       18, 26, 29, 31, 15, 37, 23, 11,  1, 34, 24, 21, 25, 10, 14,  0, 22,
        9, 27, 30, 20, 28], dtype=int64), 'cur_cost': 133433.0, 'intermediate_solutions': [{'tour': array([18, 34, 16, 33, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2]), 'cur_cost': 146783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 18, 34, 16, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2]), 'cur_cost': 147160.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 33, 18, 34, 16, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2]), 'cur_cost': 147380.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 33, 18, 34, 23, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2]), 'cur_cost': 146788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 23, 33, 18, 34, 10, 30,  0, 13,  6, 35, 12, 17, 20, 28, 19, 24,
       15,  9,  7,  8, 26, 29,  3, 21, 14, 31, 38, 36, 27, 32, 22, 11, 37,
        4,  5,  1, 25,  2]), 'cur_cost': 145386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,666 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 133433.00)
2025-08-05 10:28:55,666 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:55,666 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,666 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,667 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 162135.0
2025-08-05 10:28:55,679 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:55,679 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763.0, 23763, 23763, 23763, 23763, 23763, 23763, 23771.0, 23763, 23763, 23763]
2025-08-05 10:28:55,679 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 24, 23, 22,
       21, 25, 20, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        8,  9,  6,  1,  2], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  1,  2, 13, 10, 14, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-08-05 10:28:55,683 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:55,683 - ExploitationExpert - INFO - populations: [{'tour': [0, 17, 8, 12, 11, 10, 14, 13, 7, 5, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 18, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 36847.0}, {'tour': [0, 18, 3, 20, 25, 21, 22, 23, 24, 6, 8, 9, 5, 7, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 19, 15, 16, 17, 1, 2, 4, 26, 31, 27, 30, 28, 29], 'cur_cost': 34912.0}, {'tour': [5, 24, 8, 14, 10, 38, 36, 20, 34, 6, 7, 22, 4, 31, 3, 29, 25, 1, 23, 16, 18, 21, 9, 32, 37, 17, 19, 2, 28, 27, 15, 30, 13, 35, 12, 33, 11, 0, 26], 'cur_cost': 106959.0}, {'tour': [0, 17, 7, 15, 16, 18, 19, 22, 23, 24, 21, 25, 20, 6, 8, 9, 5, 12, 11, 10, 14, 13, 32, 35, 38, 36, 33, 34, 37, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29], 'cur_cost': 36571.0}, {'tour': [0, 21, 3, 9, 8, 5, 7, 6, 20, 25, 22, 23, 24, 1, 2, 4, 26, 31, 27, 30, 28, 29, 17, 16, 18, 15, 19, 34, 33, 36, 38, 35, 32, 37, 12, 11, 10, 14, 13], 'cur_cost': 33817.0}, {'tour': [0, 5, 18, 11, 12, 10, 14, 13, 7, 8, 9, 6, 20, 25, 21, 22, 23, 24, 1, 2, 4, 3, 26, 31, 27, 30, 28, 29, 17, 16, 15, 19, 34, 33, 36, 38, 35, 32, 37], 'cur_cost': 39683.0}, {'tour': [14, 25, 2, 1, 22, 20, 8, 36, 11, 12, 33, 21, 6, 24, 23, 0, 30, 3, 9, 35, 34, 38, 37, 7, 4, 28, 26, 18, 16, 19, 17, 15, 27, 29, 5, 13, 10, 32, 31], 'cur_cost': 85925.0}, {'tour': array([ 3,  7, 32,  5,  6, 35,  2, 33, 16, 13, 38, 17,  8, 12, 19,  4, 36,
       18, 26, 29, 31, 15, 37, 23, 11,  1, 34, 24, 21, 25, 10, 14,  0, 22,
        9, 27, 30, 20, 28], dtype=int64), 'cur_cost': 133433.0}, {'tour': array([ 6,  3,  5, 30,  1, 32, 13, 36, 16, 14, 22, 27, 11, 19, 34, 12, 24,
       15,  0,  4, 35, 21,  2, 25, 26, 38, 17, 10, 33, 20, 37,  7, 28, 23,
       18, 29,  9, 31,  8], dtype=int64), 'cur_cost': 162135.0}, {'tour': [19, 27, 17, 6, 13, 12, 15, 3, 16, 29, 20, 22, 21, 0, 25, 24, 36, 18, 35, 4, 30, 38, 33, 31, 11, 34, 14, 37, 32, 28, 9, 10, 26, 23, 1, 8, 7, 5, 2], 'cur_cost': 139946.0}]
2025-08-05 10:28:55,684 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:55,684 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 230, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 230, 'cache_hits': 0, 'similarity_calculations': 1080, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:55,685 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 6,  3,  5, 30,  1, 32, 13, 36, 16, 14, 22, 27, 11, 19, 34, 12, 24,
       15,  0,  4, 35, 21,  2, 25, 26, 38, 17, 10, 33, 20, 37,  7, 28, 23,
       18, 29,  9, 31,  8], dtype=int64), 'cur_cost': 162135.0, 'intermediate_solutions': [{'tour': array([24, 11,  4, 12,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7]), 'cur_cost': 151264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 24, 11,  4,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7]), 'cur_cost': 147251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 12, 24, 11,  4, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7]), 'cur_cost': 152055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 12, 24, 11,  2, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7]), 'cur_cost': 152095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  2, 12, 24, 11, 22, 30,  5, 23, 21, 18, 29,  8,  9, 16, 25, 13,
       36, 27, 32, 35,  1, 33,  6, 31, 26,  3, 20, 38, 28, 37, 19, 34, 17,
       14, 10, 15,  0,  7]), 'cur_cost': 149540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:55,685 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 162135.00)
2025-08-05 10:28:55,685 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:55,686 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:55,686 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:55,686 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 146820.0
