2025-08-05 09:52:14,271 - __main__ - INFO - eil51 开始进化第 1 代
2025-08-05 09:52:14,271 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:14,272 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,275 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=507.000, 多样性=0.986
2025-08-05 09:52:14,277 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:14,280 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.986
2025-08-05 09:52:14,281 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:14,291 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:14,291 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:14,291 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:14,291 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:14,308 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -91.000, 聚类评分: 0.000, 覆盖率: 0.146, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:14,308 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:14,308 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:14,308 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 09:52:14,314 - visualization.landscape_visualizer - INFO - 插值约束: 209 个点被约束到最小值 507.00
2025-08-05 09:52:14,410 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_126_20250805_095214.html
2025-08-05 09:52:14,480 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_126_20250805_095214.html
2025-08-05 09:52:14,480 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 126
2025-08-05 09:52:14,480 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:14,481 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1902秒
2025-08-05 09:52:14,481 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 252, 'max_size': 500, 'hits': 0, 'misses': 252, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 829, 'misses': 450, 'hit_rate': 0.6481626270523847, 'evictions': 350, 'ttl': 7200}}
2025-08-05 09:52:14,481 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -91.00000000000003, 'local_optima_density': 0.2, 'gradient_variance': 380261.792, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1463, 'fitness_entropy': 0.8173454221465103, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -91.000)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.146)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358734.3081374, 'performance_metrics': {}}}
2025-08-05 09:52:14,481 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:14,482 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:14,482 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:14,482 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:14,484 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,484 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:14,484 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,484 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,484 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:14,485 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,485 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,485 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:14,485 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:14,485 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:14,485 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:14,486 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,487 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,488 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,488 - ExplorationExpert - INFO - 探索路径生成完成，成本: 596.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,488 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 596.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,488 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 596.00)
2025-08-05 09:52:14,488 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:14,489 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:14,489 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,491 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,491 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,491 - ExplorationExpert - INFO - 探索路径生成完成，成本: 594.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,491 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,492 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 594.00)
2025-08-05 09:52:14,492 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:14,492 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:14,492 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,494 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,495 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,495 - ExplorationExpert - INFO - 探索路径生成完成，成本: 645.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,496 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 645.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,496 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 645.00)
2025-08-05 09:52:14,496 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:14,496 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,496 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,496 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1518.0
2025-08-05 09:52:14,503 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:14,503 - ExploitationExpert - INFO - res_population_costs: [438.0, 435, 435]
2025-08-05 09:52:14,503 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64)]
2025-08-05 09:52:14,504 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,504 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 596.0}, {'tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0}, {'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 645.0}, {'tour': array([ 5, 24, 20, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14],
      dtype=int64), 'cur_cost': 1518.0}, {'tour': array([33, 40, 27, 22,  8, 25, 21, 17, 50, 20,  0,  9,  5, 35, 42, 44, 13,
       29,  4, 32, 34, 49, 43, 48,  7, 12, 39, 38, 26, 46, 45, 18, 41, 16,
       14, 31,  2, 23, 24, 11, 37,  3, 19,  1, 36, 15, 47, 30, 10, 28,  6],
      dtype=int64), 'cur_cost': 1650.0}, {'tour': array([48, 27,  3, 23,  0, 35, 38,  9, 36, 13,  2,  5,  7, 40, 29, 15, 25,
       21, 22, 34, 32, 31,  1, 43, 44, 16, 28, 42, 39, 46, 41, 26, 49, 14,
       45, 30,  4, 37,  8, 47, 11, 20, 24, 17, 18, 12, 19, 50, 33,  6, 10],
      dtype=int64), 'cur_cost': 1625.0}, {'tour': array([40, 28, 33, 11, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1743.0}, {'tour': array([11, 44, 14,  6, 27,  9, 49, 35,  4, 46, 38, 26, 48, 21, 36, 24, 17,
       41, 33, 13, 31, 47,  2, 20, 40,  8, 34, 43, 32,  1, 12, 15, 50,  0,
       16,  3, 30, 28, 10, 37,  5,  7, 42, 18, 19, 45, 23, 22, 25, 29, 39],
      dtype=int64), 'cur_cost': 1631.0}, {'tour': array([ 5, 50, 20, 48, 46, 19,  4, 45, 32, 22, 49, 30, 31, 23, 18, 28, 37,
        6, 42, 13, 40, 33, 25, 35, 34, 16, 41,  9, 17, 43,  2,  7, 21, 12,
       47,  8, 14, 29, 36, 38,  1, 15,  3, 26, 10, 44, 39,  0, 27, 11, 24],
      dtype=int64), 'cur_cost': 1553.0}, {'tour': array([50, 26, 28, 20, 22, 16, 21, 47, 38, 32, 23, 41, 27, 11, 45, 37, 48,
       10, 39, 46, 42, 33,  6, 18, 24, 12,  9, 49,  8, 43,  1,  2,  0, 29,
       40, 19, 30, 44, 14,  7, 25, 35,  4, 31,  5,  3, 15, 13, 36, 34, 17],
      dtype=int64), 'cur_cost': 1631.0}]
2025-08-05 09:52:14,507 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,507 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 326, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 326, 'cache_hits': 0, 'similarity_calculations': 1668, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,508 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 5, 24, 20, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14],
      dtype=int64), 'cur_cost': 1518.0, 'intermediate_solutions': [{'tour': array([32, 25,  1, 45, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 32, 25,  1, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 45, 32, 25,  1, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 45, 32, 25, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 15, 45, 32, 25, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,508 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1518.00)
2025-08-05 09:52:14,508 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:14,508 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:14,508 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,510 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:14,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1489.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,512 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1489.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,512 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1489.00)
2025-08-05 09:52:14,512 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:14,512 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:14,512 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,515 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,516 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,516 - ExplorationExpert - INFO - 探索路径生成完成，成本: 607.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,516 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 607.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,516 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 607.00)
2025-08-05 09:52:14,517 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:14,517 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,517 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,517 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1563.0
2025-08-05 09:52:14,526 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:14,526 - ExploitationExpert - INFO - res_population_costs: [438.0, 435, 435, 434]
2025-08-05 09:52:14,526 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64)]
2025-08-05 09:52:14,528 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,528 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 596.0}, {'tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0}, {'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 645.0}, {'tour': array([ 5, 24, 20, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14],
      dtype=int64), 'cur_cost': 1518.0}, {'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1489.0}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 607.0}, {'tour': array([40, 32,  4, 31, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23],
      dtype=int64), 'cur_cost': 1563.0}, {'tour': array([11, 44, 14,  6, 27,  9, 49, 35,  4, 46, 38, 26, 48, 21, 36, 24, 17,
       41, 33, 13, 31, 47,  2, 20, 40,  8, 34, 43, 32,  1, 12, 15, 50,  0,
       16,  3, 30, 28, 10, 37,  5,  7, 42, 18, 19, 45, 23, 22, 25, 29, 39],
      dtype=int64), 'cur_cost': 1631.0}, {'tour': array([ 5, 50, 20, 48, 46, 19,  4, 45, 32, 22, 49, 30, 31, 23, 18, 28, 37,
        6, 42, 13, 40, 33, 25, 35, 34, 16, 41,  9, 17, 43,  2,  7, 21, 12,
       47,  8, 14, 29, 36, 38,  1, 15,  3, 26, 10, 44, 39,  0, 27, 11, 24],
      dtype=int64), 'cur_cost': 1553.0}, {'tour': array([50, 26, 28, 20, 22, 16, 21, 47, 38, 32, 23, 41, 27, 11, 45, 37, 48,
       10, 39, 46, 42, 33,  6, 18, 24, 12,  9, 49,  8, 43,  1,  2,  0, 29,
       40, 19, 30, 44, 14,  7, 25, 35,  4, 31,  5,  3, 15, 13, 36, 34, 17],
      dtype=int64), 'cur_cost': 1631.0}]
2025-08-05 09:52:14,530 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,530 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 327, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 327, 'cache_hits': 0, 'similarity_calculations': 1669, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,531 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([40, 32,  4, 31, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23],
      dtype=int64), 'cur_cost': 1563.0, 'intermediate_solutions': [{'tour': array([33, 28, 40, 11, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 33, 28, 40, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 11, 33, 28, 40,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1735.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 11, 33, 28, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1738.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 43, 11, 33, 28,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,531 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1563.00)
2025-08-05 09:52:14,531 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:14,531 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:14,532 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 538.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,534 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 538.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,534 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 538.00)
2025-08-05 09:52:14,534 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:14,534 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:14,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,536 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,536 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 567.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,536 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 567.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,536 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 567.00)
2025-08-05 09:52:14,537 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:14,537 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:14,537 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,539 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:14,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1413.0, 路径长度: 51, 收集中间解: 0
2025-08-05 09:52:14,539 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1413.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,539 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1413.00)
2025-08-05 09:52:14,539 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:14,540 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:14,541 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 596.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 645.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 24, 20, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14],
      dtype=int64), 'cur_cost': 1518.0, 'intermediate_solutions': [{'tour': array([32, 25,  1, 45, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1654.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 32, 25,  1, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 45, 32, 25,  1, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 45, 32, 25, 15, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 15, 45, 32, 25, 36, 20, 37, 16, 11,  7, 41, 24, 33, 43, 27, 13,
       35, 23, 19, 21, 12, 26, 17,  5, 30, 14, 38,  8, 42, 31, 46, 44,  6,
       29, 22, 10,  2, 34, 49,  9, 50, 48, 28,  0, 39, 18,  3, 40, 47,  4],
      dtype=int64), 'cur_cost': 1664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1489.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 607.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 32,  4, 31, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23],
      dtype=int64), 'cur_cost': 1563.0, 'intermediate_solutions': [{'tour': array([33, 28, 40, 11, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 33, 28, 40, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 11, 33, 28, 40,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1735.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 11, 33, 28, 43,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1738.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 43, 11, 33, 28,  7, 32, 30, 37, 41, 34,  6, 24,  4, 12, 45, 22,
       48, 21, 15,  1, 35, 20, 47, 16,  5, 42, 18, 13, 29,  8, 27, 50,  9,
        3, 10, 49, 39, 17, 25, 14,  0, 44, 23, 36, 26, 31, 46, 38,  2, 19],
      dtype=int64), 'cur_cost': 1689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 538.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 567.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1413.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:14,541 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:14,541 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,544 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=538.000, 多样性=0.963
2025-08-05 09:52:14,544 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:14,545 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:14,545 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:14,545 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10415769120581832, 'best_improvement': -0.0611439842209073}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02298850574712654}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06471142235752358, 'recent_improvements': [-0.05051827029262141, -0.004810984206318699, 0.07890457442242578], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 434, 'new_best_cost': 434, 'quality_improvement': 0.0, 'old_diversity': 0.8823529411764706, 'new_diversity': 0.8823529411764706, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:14,545 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:14,545 - __main__ - INFO - eil51 开始进化第 2 代
2025-08-05 09:52:14,545 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:14,546 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,546 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=538.000, 多样性=0.963
2025-08-05 09:52:14,546 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:14,548 - PathExpert - INFO - 路径结构分析完成: 公共边数量=25, 路径相似性=0.963
2025-08-05 09:52:14,549 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:14,550 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.882
2025-08-05 09:52:14,552 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:14,552 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:14,552 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:14,552 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:14,581 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: -124.786, 聚类评分: 0.000, 覆盖率: 0.148, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:14,581 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:14,581 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:14,581 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 09:52:14,587 - visualization.landscape_visualizer - INFO - 插值约束: 166 个点被约束到最小值 434.00
2025-08-05 09:52:14,680 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_127_20250805_095214.html
2025-08-05 09:52:14,726 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_127_20250805_095214.html
2025-08-05 09:52:14,726 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 127
2025-08-05 09:52:14,726 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:14,726 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1736秒
2025-08-05 09:52:14,727 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -124.78571428571426, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 132872.11693877555, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1476, 'fitness_entropy': 0.904600016348906, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -124.786)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.148)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358734.5813527, 'performance_metrics': {}}}
2025-08-05 09:52:14,727 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:14,727 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:14,727 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:14,727 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:14,728 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,728 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:14,728 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,728 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,728 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:14,728 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:14,728 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:14,728 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:14,729 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:14,729 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:14,729 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:14,729 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,732 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:14,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,733 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,733 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1218.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,733 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 25, 22, 6, 47, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 624.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 19, 21, 27, 30, 34, 35, 23, 42], 'cur_cost': 637.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,733 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1218.00)
2025-08-05 09:52:14,733 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:14,734 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:14,734 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,735 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,736 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,736 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,736 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 603.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,736 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 34, 35], 'cur_cost': 603.0, 'intermediate_solutions': [{'tour': [0, 14, 1, 19, 3, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 34, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 1, 19, 31, 26, 50, 45, 11, 46, 39, 12, 40, 18, 41, 43, 36, 16, 3, 17, 24, 13, 23, 22, 6, 25, 7, 30, 27, 2, 35, 34, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 633.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,736 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 603.00)
2025-08-05 09:52:14,737 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:14,737 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:14,737 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,740 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:14,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,741 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1206.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,741 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1206.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 48, 20, 49, 8, 28, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 15, 21, 47, 5, 13, 24, 12, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 729.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 22, 6, 25, 50, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,741 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1206.00)
2025-08-05 09:52:14,741 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:14,741 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,741 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,741 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1617.0
2025-08-05 09:52:14,750 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:14,750 - ExploitationExpert - INFO - res_population_costs: [434, 435, 435, 438.0]
2025-08-05 09:52:14,751 - ExploitationExpert - INFO - res_populations: [array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:14,752 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,752 - ExploitationExpert - INFO - populations: [{'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1218.0}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 34, 35], 'cur_cost': 603.0}, {'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1206.0}, {'tour': array([19, 45, 30, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43],
      dtype=int64), 'cur_cost': 1617.0}, {'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1489.0}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 607.0}, {'tour': [40, 32, 4, 31, 22, 42, 39, 1, 30, 10, 2, 21, 47, 41, 29, 45, 16, 28, 6, 27, 36, 43, 5, 13, 37, 50, 15, 18, 35, 24, 8, 11, 46, 49, 38, 14, 3, 44, 9, 19, 0, 48, 20, 34, 12, 33, 17, 7, 26, 25, 23], 'cur_cost': 1563.0}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 538.0}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 567.0}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1413.0}]
2025-08-05 09:52:14,753 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,753 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 328, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 328, 'cache_hits': 0, 'similarity_calculations': 1671, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,754 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([19, 45, 30, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43],
      dtype=int64), 'cur_cost': 1617.0, 'intermediate_solutions': [{'tour': array([20, 24,  5, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 20, 24,  5, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([39, 18, 20, 24,  5, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 20, 24, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 39, 18, 20, 24, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,754 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1617.00)
2025-08-05 09:52:14,754 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:14,754 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:14,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,756 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 618.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,757 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 618.0, 'intermediate_solutions': [{'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 18, 30, 31, 21, 42, 23, 44, 40, 6, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1554.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 13, 24, 17, 2, 14, 18, 40, 44, 23, 42, 21, 31, 30, 6, 5, 49, 32, 38, 29, 33, 26, 50, 15, 11, 12, 37, 10, 43, 36, 16, 3, 8, 39, 20, 22, 34, 25, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1413.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 37, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1535.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,757 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 618.00)
2025-08-05 09:52:14,757 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:14,757 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:14,757 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,759 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 567.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,760 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 567.0, 'intermediate_solutions': [{'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 9, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 12, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 29, 33, 20, 28, 35, 34, 19, 2, 27, 30, 7, 25, 6, 22, 23, 24, 12, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 684.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 35, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 670.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,760 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 567.00)
2025-08-05 09:52:14,760 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:14,760 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:14,761 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:14,761 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1565.0
2025-08-05 09:52:14,766 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:14,766 - ExploitationExpert - INFO - res_population_costs: [434, 435, 435, 438.0]
2025-08-05 09:52:14,766 - ExploitationExpert - INFO - res_populations: [array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:14,768 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:14,768 - ExploitationExpert - INFO - populations: [{'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1218.0}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 34, 35], 'cur_cost': 603.0}, {'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1206.0}, {'tour': array([19, 45, 30, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43],
      dtype=int64), 'cur_cost': 1617.0}, {'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 618.0}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 567.0}, {'tour': array([ 2,  0, 13, 43, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34],
      dtype=int64), 'cur_cost': 1565.0}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 538.0}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 567.0}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1413.0}]
2025-08-05 09:52:14,769 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:14,769 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 329, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 329, 'cache_hits': 0, 'similarity_calculations': 1674, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:14,770 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 2,  0, 13, 43, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34],
      dtype=int64), 'cur_cost': 1565.0, 'intermediate_solutions': [{'tour': array([ 4, 32, 40, 31, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  4, 32, 40, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 31,  4, 32, 40, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 31,  4, 32, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 22, 31,  4, 32, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1621.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:14,770 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1565.00)
2025-08-05 09:52:14,770 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:14,770 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:14,770 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,772 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:14,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,772 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,773 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,773 - ExplorationExpert - INFO - 探索路径生成完成，成本: 660.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,773 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 660.0, 'intermediate_solutions': [{'tour': [0, 3, 12, 13, 24, 17, 39, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 46, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 572.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 27, 30, 25, 7, 47, 5, 39, 40, 18, 41, 16, 36, 43, 14, 44, 32, 38, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 19, 2, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 549.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,773 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 660.00)
2025-08-05 09:52:14,774 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:14,774 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:14,774 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,775 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:14,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,775 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,776 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1546.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,776 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [31, 5, 47, 6, 22, 28, 41, 4, 37, 29, 23, 48, 3, 27, 46, 49, 20, 33, 19, 45, 35, 30, 1, 10, 25, 17, 34, 8, 44, 15, 18, 7, 42, 9, 38, 32, 13, 21, 39, 43, 26, 11, 36, 0, 16, 50, 14, 2, 12, 40, 24], 'cur_cost': 1546.0, 'intermediate_solutions': [{'tour': [0, 13, 11, 3, 16, 36, 14, 43, 23, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 41, 42], 'cur_cost': 712.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 42, 23, 22, 6, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 5, 39, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 45, 50, 46, 17, 24, 12, 40, 18, 41], 'cur_cost': 623.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 47, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 610.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,776 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1546.00)
2025-08-05 09:52:14,776 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:14,776 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:14,776 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:14,780 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:14,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,780 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,781 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:14,781 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:14,781 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 40, 12, 13, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 4, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 28, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 17, 0, 41], 'cur_cost': 1429.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 19, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1424.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:14,781 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1102.00)
2025-08-05 09:52:14,782 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:14,782 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:14,783 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1218.0, 'intermediate_solutions': [{'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 25, 22, 6, 47, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 624.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 22, 6, 25, 19, 21, 27, 30, 34, 35, 23, 42], 'cur_cost': 637.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 45, 5, 47, 22, 6, 25, 30, 27, 21, 19, 34, 35, 23, 42], 'cur_cost': 598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 34, 35], 'cur_cost': 603.0, 'intermediate_solutions': [{'tour': [0, 14, 1, 19, 3, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 34, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 782.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 1, 19, 31, 26, 50, 45, 11, 46, 39, 12, 40, 18, 41, 43, 36, 16, 3, 17, 24, 13, 23, 22, 6, 25, 7, 30, 27, 2, 35, 34, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 633.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 1, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 28, 20, 33, 29, 9, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 594.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1206.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 48, 20, 49, 8, 28, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 15, 21, 47, 5, 13, 24, 12, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 729.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 22, 6, 25, 50, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 21, 15, 33, 29, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 45, 30, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43],
      dtype=int64), 'cur_cost': 1617.0, 'intermediate_solutions': [{'tour': array([20, 24,  5, 18, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 20, 24,  5, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([39, 18, 20, 24,  5, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 20, 24, 39, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 39, 18, 20, 24, 44, 10, 46, 35, 29, 22,  4, 32, 11, 27, 40, 26,
        7,  1, 33, 49,  0, 48,  9,  3, 12, 36, 41,  8, 38, 30, 31, 23, 13,
       47,  6, 34, 16, 50, 25, 21,  2, 42, 43, 28, 37, 19, 45, 17, 15, 14]), 'cur_cost': 1554.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 618.0, 'intermediate_solutions': [{'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 37, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 18, 30, 31, 21, 42, 23, 44, 40, 6, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1554.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 13, 24, 17, 2, 14, 18, 40, 44, 23, 42, 21, 31, 30, 6, 5, 49, 32, 38, 29, 33, 26, 50, 15, 11, 12, 37, 10, 43, 36, 16, 3, 8, 39, 20, 22, 34, 25, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1413.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 13, 24, 17, 2, 14, 25, 34, 22, 20, 39, 8, 3, 16, 36, 43, 10, 12, 11, 15, 50, 26, 33, 29, 38, 32, 49, 5, 6, 37, 30, 31, 21, 42, 23, 44, 40, 18, 27, 19, 4, 0, 47, 41, 1, 48, 45, 28, 46, 35, 7], 'cur_cost': 1535.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 567.0, 'intermediate_solutions': [{'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 9, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 12, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 29, 33, 20, 28, 35, 34, 19, 2, 27, 30, 7, 25, 6, 22, 23, 24, 12, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 684.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 35, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 21, 1, 42], 'cur_cost': 670.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  0, 13, 43, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34],
      dtype=int64), 'cur_cost': 1565.0, 'intermediate_solutions': [{'tour': array([ 4, 32, 40, 31, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  4, 32, 40, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1574.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 31,  4, 32, 40, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1571.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40, 31,  4, 32, 22, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 22, 31,  4, 32, 42, 39,  1, 30, 10,  2, 21, 47, 41, 29, 45, 16,
       28,  6, 27, 36, 43,  5, 13, 37, 50, 15, 18, 35, 24,  8, 11, 46, 49,
       38, 14,  3, 44,  9, 19,  0, 48, 20, 34, 12, 33, 17,  7, 26, 25, 23]), 'cur_cost': 1621.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 660.0, 'intermediate_solutions': [{'tour': [0, 3, 12, 13, 24, 17, 39, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 46, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 572.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 27, 30, 25, 7, 47, 5, 39, 40, 18, 41, 16, 36, 43, 14, 44, 32, 38, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 12, 13, 24, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 41, 18, 40, 39, 5, 47, 7, 25, 30, 27, 19, 2, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 549.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [31, 5, 47, 6, 22, 28, 41, 4, 37, 29, 23, 48, 3, 27, 46, 49, 20, 33, 19, 45, 35, 30, 1, 10, 25, 17, 34, 8, 44, 15, 18, 7, 42, 9, 38, 32, 13, 21, 39, 43, 26, 11, 36, 0, 16, 50, 14, 2, 12, 40, 24], 'cur_cost': 1546.0, 'intermediate_solutions': [{'tour': [0, 13, 11, 3, 16, 36, 14, 43, 23, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 41, 42], 'cur_cost': 712.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 42, 23, 22, 6, 21, 35, 34, 19, 2, 27, 30, 25, 7, 47, 5, 39, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 45, 50, 46, 17, 24, 12, 40, 18, 41], 'cur_cost': 623.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 11, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 17, 46, 50, 45, 26, 31, 10, 47, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 39, 5, 7, 25, 30, 27, 2, 19, 34, 35, 21, 6, 22, 23, 42], 'cur_cost': 610.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 40, 12, 13, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 4, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 28, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1396.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 19, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 17, 0, 41], 'cur_cost': 1429.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 22, 34, 35, 3, 16, 36, 7, 25, 23, 13, 24, 46, 48, 45, 50, 26, 10, 19, 29, 39, 8, 15, 1, 28, 43, 33, 37, 38, 40, 49, 27, 21, 6, 30, 44, 31, 12, 11, 18, 2, 9, 4, 20, 5, 42, 47, 32, 0, 17, 41], 'cur_cost': 1424.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:14,784 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:14,784 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,786 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=567.000, 多样性=0.973
2025-08-05 09:52:14,787 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:14,787 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:14,787 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:14,787 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0704774510072652, 'best_improvement': -0.05390334572490706}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.010407239819004526}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05448433770606851, 'recent_improvements': [-0.004810984206318699, 0.07890457442242578, 0.10415769120581832], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 434, 'new_best_cost': 434, 'quality_improvement': 0.0, 'old_diversity': 0.8823529411764706, 'new_diversity': 0.8823529411764706, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:14,787 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:14,787 - __main__ - INFO - eil51 开始进化第 3 代
2025-08-05 09:52:14,788 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:14,788 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:14,788 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=567.000, 多样性=0.973
2025-08-05 09:52:14,789 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:14,791 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.973
2025-08-05 09:52:14,791 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:14,792 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.882
2025-08-05 09:52:14,794 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:14,794 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:14,795 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:14,795 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:14,825 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -31.686, 聚类评分: 0.000, 覆盖率: 0.149, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:14,825 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:14,825 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:14,825 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 09:52:14,830 - visualization.landscape_visualizer - INFO - 插值约束: 205 个点被约束到最小值 434.00
2025-08-05 09:52:14,936 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_128_20250805_095214.html
2025-08-05 09:52:15,016 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_128_20250805_095214.html
2025-08-05 09:52:15,016 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 128
2025-08-05 09:52:15,016 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:15,016 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2216秒
2025-08-05 09:52:15,017 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -31.685714285714294, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 169316.47836734695, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1486, 'fitness_entropy': 0.9397387539970584, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -31.686)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.149)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358734.825789, 'performance_metrics': {}}}
2025-08-05 09:52:15,017 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:15,017 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:15,017 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:15,017 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:15,017 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,017 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:15,018 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,018 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,018 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:15,018 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,018 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,018 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:15,018 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:15,019 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:15,019 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:15,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,021 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,022 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,022 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,022 - ExplorationExpert - INFO - 探索路径生成完成，成本: 571.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,023 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 8, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 571.0, 'intermediate_solutions': [{'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 7, 32, 50, 24, 42, 46, 23, 18, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 32, 18, 43, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9, 29], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,023 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 571.00)
2025-08-05 09:52:15,023 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:15,023 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:15,024 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,026 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:15,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,028 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1381.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,028 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1381.0, 'intermediate_solutions': [{'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 2, 32, 44, 21, 27, 30, 7, 25, 47, 42, 38, 34, 35], 'cur_cost': 740.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 42, 47, 25, 7, 30, 27, 2, 34, 35], 'cur_cost': 593.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 11, 34, 35], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,029 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1381.00)
2025-08-05 09:52:15,029 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:15,029 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:15,029 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,031 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,033 - ExplorationExpert - INFO - 探索路径生成完成，成本: 613.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,033 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 7, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 613.0, 'intermediate_solutions': [{'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 19, 34, 25, 6, 14, 44, 32], 'cur_cost': 1195.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 5, 47, 27, 37, 4, 50, 26, 46, 29, 36, 43, 48, 45, 23, 30, 31, 38, 9, 28, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 24, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,033 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 613.00)
2025-08-05 09:52:15,033 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:15,033 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,033 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,034 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1521.0
2025-08-05 09:52:15,040 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:15,040 - ExploitationExpert - INFO - res_population_costs: [434, 435, 435, 438.0]
2025-08-05 09:52:15,040 - ExploitationExpert - INFO - res_populations: [array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:15,042 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,043 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 8, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 571.0}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1381.0}, {'tour': [0, 8, 7, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 613.0}, {'tour': array([50, 49, 31, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14],
      dtype=int64), 'cur_cost': 1521.0}, {'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 618.0}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 567.0}, {'tour': [2, 0, 13, 43, 15, 40, 5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42, 9, 23, 6, 30, 4, 1, 19, 10, 21, 36, 37, 3, 28, 16, 11, 48, 32, 12, 50, 49, 41, 8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20, 7, 34], 'cur_cost': 1565.0}, {'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 660.0}, {'tour': [31, 5, 47, 6, 22, 28, 41, 4, 37, 29, 23, 48, 3, 27, 46, 49, 20, 33, 19, 45, 35, 30, 1, 10, 25, 17, 34, 8, 44, 15, 18, 7, 42, 9, 38, 32, 13, 21, 39, 43, 26, 11, 36, 0, 16, 50, 14, 2, 12, 40, 24], 'cur_cost': 1546.0}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 40, 12, 13, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1102.0}]
2025-08-05 09:52:15,043 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,043 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 330, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 330, 'cache_hits': 0, 'similarity_calculations': 1678, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,044 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([50, 49, 31, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14],
      dtype=int64), 'cur_cost': 1521.0, 'intermediate_solutions': [{'tour': array([30, 45, 19, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([44, 30, 45, 19, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 44, 30, 45, 19, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 44, 30, 45, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 48, 44, 30, 45, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,044 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1521.00)
2025-08-05 09:52:15,044 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:15,044 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:15,045 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,046 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,046 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,047 - ExplorationExpert - INFO - 探索路径生成完成，成本: 646.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,047 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 646.0, 'intermediate_solutions': [{'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 36, 24, 12, 40, 18, 41, 43, 14, 13, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 2, 35, 34, 19, 20, 28, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 28, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,047 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 646.00)
2025-08-05 09:52:15,047 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:15,047 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:15,048 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,049 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 593.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,050 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 593.0, 'intermediate_solutions': [{'tour': [0, 13, 33, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 10, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 616.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 44, 39, 32, 38, 21], 'cur_cost': 603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 26, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 615.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,050 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 593.00)
2025-08-05 09:52:15,050 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:15,050 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,050 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,051 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1610.0
2025-08-05 09:52:15,055 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:15,055 - ExploitationExpert - INFO - res_population_costs: [434, 435, 435, 438.0]
2025-08-05 09:52:15,056 - ExploitationExpert - INFO - res_populations: [array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:15,057 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,057 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 8, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 571.0}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1381.0}, {'tour': [0, 8, 7, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 613.0}, {'tour': array([50, 49, 31, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14],
      dtype=int64), 'cur_cost': 1521.0}, {'tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 646.0}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 593.0}, {'tour': array([12, 30,  9, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18],
      dtype=int64), 'cur_cost': 1610.0}, {'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 25, 30, 27, 2, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 660.0}, {'tour': [31, 5, 47, 6, 22, 28, 41, 4, 37, 29, 23, 48, 3, 27, 46, 49, 20, 33, 19, 45, 35, 30, 1, 10, 25, 17, 34, 8, 44, 15, 18, 7, 42, 9, 38, 32, 13, 21, 39, 43, 26, 11, 36, 0, 16, 50, 14, 2, 12, 40, 24], 'cur_cost': 1546.0}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 40, 12, 13, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1102.0}]
2025-08-05 09:52:15,058 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,058 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 331, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 331, 'cache_hits': 0, 'similarity_calculations': 1683, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,059 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([12, 30,  9, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18],
      dtype=int64), 'cur_cost': 1610.0, 'intermediate_solutions': [{'tour': array([13,  0,  2, 43, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1631.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 13,  0,  2, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 43, 13,  0,  2, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 43, 13,  0, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 15, 43, 13,  0, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,059 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1610.00)
2025-08-05 09:52:15,059 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:15,059 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:15,060 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,063 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1154.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,064 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1154.0, 'intermediate_solutions': [{'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 19, 22, 47, 7, 25, 30, 27, 2, 23, 34, 35, 21, 42, 41, 39], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 19, 2, 27, 30, 25, 7, 47, 22, 23, 17, 50, 45, 11, 46, 16, 36, 43, 14, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 6, 3, 0, 35, 21, 42, 41, 39], 'cur_cost': 710.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 30, 27, 2, 25, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 701.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,065 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1154.00)
2025-08-05 09:52:15,065 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:15,065 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,065 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,065 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1698.0
2025-08-05 09:52:15,071 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:15,071 - ExploitationExpert - INFO - res_population_costs: [434, 435, 435, 438.0, 429]
2025-08-05 09:52:15,071 - ExploitationExpert - INFO - res_populations: [array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64)]
2025-08-05 09:52:15,073 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,073 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 8, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 571.0}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1381.0}, {'tour': [0, 8, 7, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 613.0}, {'tour': array([50, 49, 31, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14],
      dtype=int64), 'cur_cost': 1521.0}, {'tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 646.0}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 593.0}, {'tour': array([12, 30,  9, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18],
      dtype=int64), 'cur_cost': 1610.0}, {'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1154.0}, {'tour': array([11, 46, 34,  8, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33],
      dtype=int64), 'cur_cost': 1698.0}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 40, 12, 13, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1102.0}]
2025-08-05 09:52:15,075 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,075 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 332, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 332, 'cache_hits': 0, 'similarity_calculations': 1689, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,076 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([11, 46, 34,  8, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33],
      dtype=int64), 'cur_cost': 1698.0, 'intermediate_solutions': [{'tour': array([47,  5, 31,  6, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6, 47,  5, 31, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22,  6, 47,  5, 31, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  6, 47,  5, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 22,  6, 47,  5, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,076 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1698.00)
2025-08-05 09:52:15,076 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:15,076 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:15,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,078 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,078 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,079 - ExplorationExpert - INFO - 探索路径生成完成，成本: 599.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,079 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 599.0, 'intermediate_solutions': [{'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 13, 36, 18, 8, 16, 40, 12, 32, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 6, 3, 13, 12, 40, 16, 8, 18, 36, 32, 41, 43, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1149.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 19, 40, 12, 13, 3, 6, 7, 28, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,080 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 599.00)
2025-08-05 09:52:15,080 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:15,080 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:15,082 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 8, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 571.0, 'intermediate_solutions': [{'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 7, 32, 50, 24, 42, 46, 23, 18, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 29, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 32, 18, 43, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 8, 21, 26, 45, 14, 41, 4, 37, 11, 36, 40, 47, 27, 1, 48, 10, 20, 33, 19, 49, 35, 30, 28, 0, 25, 17, 5, 3, 44, 43, 18, 32, 50, 24, 42, 46, 23, 7, 15, 38, 34, 2, 6, 12, 39, 13, 22, 16, 9, 29], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1381.0, 'intermediate_solutions': [{'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 2, 32, 44, 21, 27, 30, 7, 25, 47, 42, 38, 34, 35], 'cur_cost': 740.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 42, 47, 25, 7, 30, 27, 2, 34, 35], 'cur_cost': 593.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 5, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 12, 39, 46, 45, 50, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 21, 27, 30, 7, 25, 47, 42, 2, 11, 34, 35], 'cur_cost': 672.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 7, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 613.0, 'intermediate_solutions': [{'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 19, 34, 25, 6, 14, 44, 32], 'cur_cost': 1195.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 5, 47, 27, 37, 4, 50, 26, 46, 29, 36, 43, 48, 45, 23, 30, 31, 38, 9, 28, 21, 8, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 24, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 5, 47, 27, 37, 28, 9, 38, 31, 30, 23, 45, 48, 43, 36, 29, 46, 26, 50, 4, 21, 8, 24, 0, 2, 1, 35, 33, 16, 10, 3, 18, 12, 17, 22, 15, 49, 20, 7, 42, 40, 13, 41, 39, 25, 34, 19, 6, 14, 44, 32], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 49, 31, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14],
      dtype=int64), 'cur_cost': 1521.0, 'intermediate_solutions': [{'tour': array([30, 45, 19, 44, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([44, 30, 45, 19, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 44, 30, 45, 19, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1575.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 44, 30, 45, 48, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 48, 44, 30, 45, 25, 28, 35, 50, 23, 11, 40, 15,  7, 41, 24, 31,
       37, 17, 18, 21, 34,  0, 36, 13, 38, 39, 20, 33, 27, 22, 42,  5, 10,
       47,  2,  8, 16,  4,  1,  9,  6, 26,  3, 14, 49, 29, 12, 32, 46, 43]), 'cur_cost': 1601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 646.0, 'intermediate_solutions': [{'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 36, 24, 12, 40, 18, 41, 43, 14, 13, 44, 32, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 2, 35, 34, 19, 20, 28, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 28, 16, 8, 48, 4, 37, 10, 31, 26, 50, 45, 46, 3, 17, 13, 24, 12, 40, 18, 41, 43, 14, 36, 44, 32, 9, 29, 33, 49, 15, 1, 20, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 21, 38, 39], 'cur_cost': 678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 593.0, 'intermediate_solutions': [{'tour': [0, 13, 33, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 10, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 616.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 26, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 44, 39, 32, 38, 21], 'cur_cost': 603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 14, 43, 36, 16, 3, 17, 46, 11, 45, 50, 31, 1, 15, 49, 8, 48, 4, 37, 9, 29, 33, 20, 28, 19, 34, 35, 26, 2, 27, 30, 7, 25, 6, 22, 23, 42, 47, 5, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21], 'cur_cost': 615.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 30,  9, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18],
      dtype=int64), 'cur_cost': 1610.0, 'intermediate_solutions': [{'tour': array([13,  0,  2, 43, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1631.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 13,  0,  2, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1602.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 43, 13,  0,  2, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 43, 13,  0, 15, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1585.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 15, 43, 13,  0, 40,  5, 22, 44, 39, 35, 17, 26, 46, 24, 45, 42,
        9, 23,  6, 30,  4,  1, 19, 10, 21, 36, 37,  3, 28, 16, 11, 48, 32,
       12, 50, 49, 41,  8, 27, 47, 25, 33, 14, 18, 38, 29, 31, 20,  7, 34]), 'cur_cost': 1565.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1154.0, 'intermediate_solutions': [{'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 19, 22, 47, 7, 25, 30, 27, 2, 23, 34, 35, 21, 42, 41, 39], 'cur_cost': 804.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 19, 2, 27, 30, 25, 7, 47, 22, 23, 17, 50, 45, 11, 46, 16, 36, 43, 14, 44, 32, 38, 9, 29, 33, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 5, 13, 24, 12, 40, 18, 6, 3, 0, 35, 21, 42, 41, 39], 'cur_cost': 710.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 18, 40, 12, 24, 13, 5, 26, 31, 10, 37, 4, 48, 8, 49, 15, 1, 28, 20, 33, 29, 9, 38, 32, 44, 14, 43, 36, 16, 46, 11, 45, 50, 17, 23, 22, 47, 7, 30, 27, 2, 25, 19, 34, 35, 21, 42, 41, 39], 'cur_cost': 701.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 46, 34,  8, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33],
      dtype=int64), 'cur_cost': 1698.0, 'intermediate_solutions': [{'tour': array([47,  5, 31,  6, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6, 47,  5, 31, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22,  6, 47,  5, 31, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  6, 47,  5, 22, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 22,  6, 47,  5, 28, 41,  4, 37, 29, 23, 48,  3, 27, 46, 49, 20,
       33, 19, 45, 35, 30,  1, 10, 25, 17, 34,  8, 44, 15, 18,  7, 42,  9,
       38, 32, 13, 21, 39, 43, 26, 11, 36,  0, 16, 50, 14,  2, 12, 40, 24]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 599.0, 'intermediate_solutions': [{'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 13, 36, 18, 8, 16, 40, 12, 32, 3, 6, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 6, 3, 13, 12, 40, 16, 8, 18, 36, 32, 41, 43, 7, 28, 19, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1149.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 9, 4, 10, 37, 50, 31, 2, 0, 45, 47, 30, 49, 38, 15, 33, 26, 21, 22, 5, 23, 25, 11, 44, 14, 48, 29, 46, 43, 41, 32, 36, 18, 8, 16, 19, 40, 12, 13, 3, 6, 7, 28, 1, 35, 27, 42, 24, 17, 39, 34], 'cur_cost': 1179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:15,082 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:15,082 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,085 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=571.000, 多样性=0.959
2025-08-05 09:52:15,085 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:15,085 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:15,085 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:15,086 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.009206748973533362, 'best_improvement': -0.007054673721340388}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.013882669055082645}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07469101271484549, 'recent_improvements': [0.07890457442242578, 0.10415769120581832, -0.0704774510072652], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 429, 'new_best_cost': 429, 'quality_improvement': 0.0, 'old_diversity': 0.8803921568627451, 'new_diversity': 0.8803921568627451, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:15,086 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:15,086 - __main__ - INFO - eil51 开始进化第 4 代
2025-08-05 09:52:15,086 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:15,086 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,087 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=571.000, 多样性=0.959
2025-08-05 09:52:15,087 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:15,089 - PathExpert - INFO - 路径结构分析完成: 公共边数量=13, 路径相似性=0.959
2025-08-05 09:52:15,090 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:15,091 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.880
2025-08-05 09:52:15,093 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:15,093 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:15,093 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 09:52:15,093 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 09:52:15,126 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -93.667, 聚类评分: 0.000, 覆盖率: 0.150, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:15,127 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:15,127 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:15,127 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 09:52:15,133 - visualization.landscape_visualizer - INFO - 插值约束: 255 个点被约束到最小值 429.00
2025-08-05 09:52:15,267 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_129_20250805_095215.html
2025-08-05 09:52:15,329 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_129_20250805_095215.html
2025-08-05 09:52:15,329 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 129
2025-08-05 09:52:15,329 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:15,329 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2358秒
2025-08-05 09:52:15,329 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.06666666666666667, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -93.66666666666669, 'local_optima_density': 0.06666666666666667, 'gradient_variance': 190852.39822222225, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1497, 'fitness_entropy': 0.8609816190354955, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -93.667)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.150)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358735.1270635, 'performance_metrics': {}}}
2025-08-05 09:52:15,330 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:15,330 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:15,330 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:15,330 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:15,330 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,331 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:15,331 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,331 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,331 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:15,331 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:15,331 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,332 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:15,332 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:15,332 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:15,332 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:15,332 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,334 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,335 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,335 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,335 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,336 - ExplorationExpert - INFO - 探索路径生成完成，成本: 546.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,336 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 546.0, 'intermediate_solutions': [{'tour': [0, 19, 8, 9, 48, 4, 22, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 37, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 690.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 8, 9, 48, 4, 23, 22, 6, 21, 1, 15, 49, 20, 28, 35, 34, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 640.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 8, 9, 48, 4, 37, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 10, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 587.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,337 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 546.00)
2025-08-05 09:52:15,337 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:15,337 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:15,337 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,339 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:15,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1444.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,340 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1444.0, 'intermediate_solutions': [{'tour': [19, 11, 5, 9, 15, 23, 45, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 28, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1402.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 26, 12, 40, 18, 41, 43, 14, 46, 24, 17, 3, 48, 36, 16, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 0, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 32, 13, 37, 50], 'cur_cost': 1400.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,340 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1444.00)
2025-08-05 09:52:15,340 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:15,340 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:15,341 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,344 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,344 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,345 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,345 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,345 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1182.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,346 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1182.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 14, 43, 36, 16, 3, 9, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 17, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 711.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 14, 41, 18, 40, 12, 24, 13, 23, 22, 6, 25, 30, 27, 2, 35, 34, 19, 20, 28, 1, 15, 49, 33, 29, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 3, 16, 36, 43, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 634.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 45, 14, 43, 36, 16, 3, 17, 46, 11, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 612.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,346 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1182.00)
2025-08-05 09:52:15,346 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:15,346 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,346 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,347 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1641.0
2025-08-05 09:52:15,356 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,356 - ExploitationExpert - INFO - res_population_costs: [429, 434, 435, 435, 438.0, 429, 427]
2025-08-05 09:52:15,356 - ExploitationExpert - INFO - res_populations: [array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64)]
2025-08-05 09:52:15,359 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,359 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 546.0}, {'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1444.0}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1182.0}, {'tour': array([36,  2, 40, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50],
      dtype=int64), 'cur_cost': 1641.0}, {'tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 646.0}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 593.0}, {'tour': [12, 30, 9, 46, 40, 45, 2, 21, 1, 32, 22, 3, 11, 34, 15, 19, 38, 26, 25, 13, 49, 37, 48, 10, 8, 7, 42, 28, 20, 6, 0, 14, 17, 31, 43, 41, 33, 39, 50, 36, 23, 35, 4, 27, 47, 24, 44, 5, 16, 29, 18], 'cur_cost': 1610.0}, {'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1154.0}, {'tour': [11, 46, 34, 8, 24, 4, 2, 38, 28, 39, 42, 5, 35, 9, 50, 6, 7, 37, 1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47, 49, 30, 48, 10, 22, 40, 27, 3, 0, 26, 17, 45, 25, 19, 18, 32, 33], 'cur_cost': 1698.0}, {'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 599.0}]
2025-08-05 09:52:15,360 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,360 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 333, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 333, 'cache_hits': 0, 'similarity_calculations': 1696, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,361 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36,  2, 40, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50],
      dtype=int64), 'cur_cost': 1641.0, 'intermediate_solutions': [{'tour': array([31, 49, 50, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 31, 49, 50,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 12, 31, 49, 50, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 12, 31, 49,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50,  3, 12, 31, 49, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,361 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1641.00)
2025-08-05 09:52:15,362 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:15,362 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:15,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,366 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1113.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,368 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [0, 6, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 12, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 30, 27, 2, 35, 34, 19, 28, 1, 15, 49, 8, 48, 9, 20, 12, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 657.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 3, 17, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,369 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1113.00)
2025-08-05 09:52:15,369 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:15,369 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:15,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,373 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1065.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,374 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 45, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 36, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 635.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 39, 15, 33, 29, 9, 38, 32, 44, 42], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,374 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1065.00)
2025-08-05 09:52:15,374 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:15,374 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,374 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,375 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1539.0
2025-08-05 09:52:15,381 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,381 - ExploitationExpert - INFO - res_population_costs: [429, 434, 435, 435, 438.0, 429, 427]
2025-08-05 09:52:15,381 - ExploitationExpert - INFO - res_populations: [array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64)]
2025-08-05 09:52:15,383 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,383 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 546.0}, {'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1444.0}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1182.0}, {'tour': array([36,  2, 40, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50],
      dtype=int64), 'cur_cost': 1641.0}, {'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1113.0}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1065.0}, {'tour': array([46, 31,  0, 29, 41, 38, 18,  6, 47, 22, 24, 42,  3, 35, 16, 20, 43,
       44, 13, 21,  8, 28, 50, 19,  2,  7, 39, 26, 12, 27,  4, 33, 10, 40,
       32, 37, 30, 34,  1, 36, 25, 14, 11,  9, 48, 15, 45, 49,  5, 23, 17],
      dtype=int64), 'cur_cost': 1539.0}, {'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1154.0}, {'tour': [11, 46, 34, 8, 24, 4, 2, 38, 28, 39, 42, 5, 35, 9, 50, 6, 7, 37, 1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47, 49, 30, 48, 10, 22, 40, 27, 3, 0, 26, 17, 45, 25, 19, 18, 32, 33], 'cur_cost': 1698.0}, {'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 599.0}]
2025-08-05 09:52:15,384 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,384 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 334, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 334, 'cache_hits': 0, 'similarity_calculations': 1704, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,385 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([46, 31,  0, 29, 41, 38, 18,  6, 47, 22, 24, 42,  3, 35, 16, 20, 43,
       44, 13, 21,  8, 28, 50, 19,  2,  7, 39, 26, 12, 27,  4, 33, 10, 40,
       32, 37, 30, 34,  1, 36, 25, 14, 11,  9, 48, 15, 45, 49,  5, 23, 17],
      dtype=int64), 'cur_cost': 1539.0, 'intermediate_solutions': [{'tour': array([ 9, 30, 12, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46,  9, 30, 12, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 46,  9, 30, 12, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1600.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 46,  9, 30, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 40, 46,  9, 30, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,385 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1539.00)
2025-08-05 09:52:15,385 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:15,385 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:15,385 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,387 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,388 - ExplorationExpert - INFO - 探索路径生成完成，成本: 606.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,388 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 16, 36, 14, 43, 41, 39, 44, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 606.0, 'intermediate_solutions': [{'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 31, 19, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 36, 6, 34, 30, 31, 19, 27, 20, 37, 17, 18, 32, 4, 10, 22, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 21, 45, 50, 11, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 3, 41, 13, 24, 25, 2, 29], 'cur_cost': 1164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,388 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 606.00)
2025-08-05 09:52:15,388 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:15,388 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,388 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,388 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1590.0
2025-08-05 09:52:15,395 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,395 - ExploitationExpert - INFO - res_population_costs: [429, 434, 435, 435, 438.0, 429, 427]
2025-08-05 09:52:15,395 - ExploitationExpert - INFO - res_populations: [array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64)]
2025-08-05 09:52:15,398 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,398 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 546.0}, {'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1444.0}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1182.0}, {'tour': array([36,  2, 40, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50],
      dtype=int64), 'cur_cost': 1641.0}, {'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1113.0}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1065.0}, {'tour': array([46, 31,  0, 29, 41, 38, 18,  6, 47, 22, 24, 42,  3, 35, 16, 20, 43,
       44, 13, 21,  8, 28, 50, 19,  2,  7, 39, 26, 12, 27,  4, 33, 10, 40,
       32, 37, 30, 34,  1, 36, 25, 14, 11,  9, 48, 15, 45, 49,  5, 23, 17],
      dtype=int64), 'cur_cost': 1539.0}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 16, 36, 14, 43, 41, 39, 44, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 606.0}, {'tour': array([46, 16,  3, 47, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26],
      dtype=int64), 'cur_cost': 1590.0}, {'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 599.0}]
2025-08-05 09:52:15,399 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,399 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 335, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 335, 'cache_hits': 0, 'similarity_calculations': 1713, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,400 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([46, 16,  3, 47, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26],
      dtype=int64), 'cur_cost': 1590.0, 'intermediate_solutions': [{'tour': array([34, 46, 11,  8, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 34, 46, 11, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24,  8, 34, 46, 11,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1697.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  8, 34, 46, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 24,  8, 34, 46,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,400 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1590.00)
2025-08-05 09:52:15,400 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:15,400 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:15,401 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,402 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:15,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1557.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,405 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [34, 11, 5, 14, 27, 23, 16, 2, 36, 30, 28, 49, 10, 26, 37, 43, 18, 40, 17, 46, 3, 25, 22, 48, 41, 12, 24, 9, 21, 29, 47, 35, 20, 7, 42, 50, 39, 44, 19, 8, 13, 4, 31, 33, 0, 15, 32, 1, 38, 45, 6], 'cur_cost': 1557.0, 'intermediate_solutions': [{'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 1, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 21, 47, 5, 42], 'cur_cost': 679.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 21, 1, 44, 32, 38, 9, 47, 5, 42], 'cur_cost': 650.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 0, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,405 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1557.00)
2025-08-05 09:52:15,405 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:15,405 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:15,408 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 546.0, 'intermediate_solutions': [{'tour': [0, 19, 8, 9, 48, 4, 22, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 21, 6, 37, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 690.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 8, 9, 48, 4, 23, 22, 6, 21, 1, 15, 49, 20, 28, 35, 34, 2, 27, 30, 25, 7, 47, 5, 13, 24, 12, 40, 18, 41, 43, 14, 36, 16, 3, 46, 11, 45, 50, 26, 31, 10, 37, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 640.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 8, 9, 48, 4, 37, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 7, 25, 30, 27, 2, 34, 35, 28, 20, 49, 15, 1, 10, 21, 6, 22, 23, 42, 17, 39, 44, 32, 38, 29, 33], 'cur_cost': 587.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1444.0, 'intermediate_solutions': [{'tour': [19, 11, 5, 9, 15, 23, 45, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 28, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1402.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 26, 12, 40, 18, 41, 43, 14, 46, 24, 17, 3, 48, 36, 16, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 0, 32, 13, 37, 50], 'cur_cost': 1373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 11, 5, 9, 15, 23, 28, 38, 44, 30, 16, 36, 48, 3, 17, 24, 46, 14, 43, 41, 18, 40, 12, 26, 31, 10, 47, 4, 25, 8, 27, 2, 34, 22, 20, 33, 0, 29, 7, 1, 21, 6, 45, 39, 42, 35, 49, 32, 13, 37, 50], 'cur_cost': 1400.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1182.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 14, 43, 36, 16, 3, 9, 46, 11, 45, 50, 26, 31, 10, 37, 4, 48, 17, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 711.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 14, 41, 18, 40, 12, 24, 13, 23, 22, 6, 25, 30, 27, 2, 35, 34, 19, 20, 28, 1, 15, 49, 33, 29, 9, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 17, 3, 16, 36, 43, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 634.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 45, 14, 43, 36, 16, 3, 17, 46, 11, 50, 26, 31, 10, 37, 4, 48, 9, 29, 33, 49, 15, 1, 28, 20, 19, 34, 35, 2, 27, 30, 25, 6, 22, 23, 13, 24, 12, 40, 18, 41, 39, 44, 32, 38, 21, 47, 5, 42], 'cur_cost': 612.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36,  2, 40, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50],
      dtype=int64), 'cur_cost': 1641.0, 'intermediate_solutions': [{'tour': array([31, 49, 50, 12,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 31, 49, 50,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1530.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 12, 31, 49, 50, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 12, 31, 49,  3, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50,  3, 12, 31, 49, 40, 32, 38, 42, 23,  2,  9, 39, 10, 11, 22,  1,
       47,  5, 45, 16, 20, 43, 36, 46, 15, 33, 19, 26, 48, 18, 13,  8, 35,
       17, 44, 24,  4,  0, 30, 25, 21,  7, 37, 41, 34, 27, 28, 29,  6, 14]), 'cur_cost': 1549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [0, 6, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 12, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 30, 27, 2, 35, 34, 19, 28, 1, 15, 49, 8, 48, 9, 20, 12, 25, 6, 22, 23, 13, 24, 17, 3, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 657.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 20, 9, 48, 8, 49, 15, 1, 28, 19, 34, 35, 2, 27, 30, 7, 25, 6, 22, 23, 13, 24, 3, 17, 16, 36, 14, 43, 41, 18, 40, 39, 46, 11, 45, 50, 26, 31, 10, 37, 4, 29, 33, 38, 32, 44, 5, 47, 21, 42], 'cur_cost': 659.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1065.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 45, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 36, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 635.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 50, 26, 31, 10, 37, 4, 48, 8, 49, 20, 28, 35, 45, 5, 47, 21, 15, 33, 29, 9, 38, 32, 44, 39, 42], 'cur_cost': 667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 12, 40, 18, 41, 43, 14, 36, 11, 46, 3, 17, 13, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 5, 47, 21, 39, 15, 33, 29, 9, 38, 32, 44, 42], 'cur_cost': 668.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 31,  0, 29, 41, 38, 18,  6, 47, 22, 24, 42,  3, 35, 16, 20, 43,
       44, 13, 21,  8, 28, 50, 19,  2,  7, 39, 26, 12, 27,  4, 33, 10, 40,
       32, 37, 30, 34,  1, 36, 25, 14, 11,  9, 48, 15, 45, 49,  5, 23, 17],
      dtype=int64), 'cur_cost': 1539.0, 'intermediate_solutions': [{'tour': array([ 9, 30, 12, 46, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46,  9, 30, 12, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 46,  9, 30, 12, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1600.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 46,  9, 30, 40, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 40, 46,  9, 30, 45,  2, 21,  1, 32, 22,  3, 11, 34, 15, 19, 38,
       26, 25, 13, 49, 37, 48, 10,  8,  7, 42, 28, 20,  6,  0, 14, 17, 31,
       43, 41, 33, 39, 50, 36, 23, 35,  4, 27, 47, 24, 44,  5, 16, 29, 18]), 'cur_cost': 1564.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 16, 36, 14, 43, 41, 39, 44, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 606.0, 'intermediate_solutions': [{'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 31, 19, 30, 34, 6, 36, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 21, 45, 50, 11, 3, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 36, 6, 34, 30, 31, 19, 27, 20, 37, 17, 18, 32, 4, 10, 22, 49, 44, 39, 41, 13, 24, 25, 2, 29], 'cur_cost': 1180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 21, 45, 50, 11, 46, 12, 40, 14, 16, 0, 28, 9, 43, 38, 48, 33, 1, 35, 15, 47, 7, 23, 5, 42, 26, 22, 10, 4, 32, 18, 17, 37, 20, 27, 19, 31, 30, 34, 6, 36, 49, 44, 39, 3, 41, 13, 24, 25, 2, 29], 'cur_cost': 1164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 16,  3, 47, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26],
      dtype=int64), 'cur_cost': 1590.0, 'intermediate_solutions': [{'tour': array([34, 46, 11,  8, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 34, 46, 11, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24,  8, 34, 46, 11,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1697.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  8, 34, 46, 24,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 24,  8, 34, 46,  4,  2, 38, 28, 39, 42,  5, 35,  9, 50,  6,  7,
       37,  1, 12, 43, 23, 13, 15, 31, 41, 20, 29, 16, 36, 21, 14, 44, 47,
       49, 30, 48, 10, 22, 40, 27,  3,  0, 26, 17, 45, 25, 19, 18, 32, 33]), 'cur_cost': 1698.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [34, 11, 5, 14, 27, 23, 16, 2, 36, 30, 28, 49, 10, 26, 37, 43, 18, 40, 17, 46, 3, 25, 22, 48, 41, 12, 24, 9, 21, 29, 47, 35, 20, 7, 42, 50, 39, 44, 19, 8, 13, 4, 31, 33, 0, 15, 32, 1, 38, 45, 6], 'cur_cost': 1557.0, 'intermediate_solutions': [{'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 1, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 17, 21, 47, 5, 42], 'cur_cost': 679.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 21, 1, 44, 32, 38, 9, 47, 5, 42], 'cur_cost': 650.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 13, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 0, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 39, 17, 24, 23, 22, 6, 25, 7, 30, 27, 2, 19, 34, 35, 28, 20, 33, 29, 9, 38, 32, 44, 1, 21, 47, 5, 42], 'cur_cost': 588.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:15,408 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:15,408 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,412 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=546.000, 多样性=0.976
2025-08-05 09:52:15,412 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:15,412 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:15,412 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:15,413 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03509932635216165, 'best_improvement': 0.043782837127845885}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.017257039055404034}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.056682220089675835, 'recent_improvements': [0.10415769120581832, -0.0704774510072652, -0.009206748973533362], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 427, 'new_best_cost': 427, 'quality_improvement': 0.0, 'old_diversity': 0.873015873015873, 'new_diversity': 0.873015873015873, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:15,414 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:15,414 - __main__ - INFO - eil51 开始进化第 5 代
2025-08-05 09:52:15,414 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:15,415 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,416 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=546.000, 多样性=0.976
2025-08-05 09:52:15,416 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:15,419 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-05 09:52:15,420 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:15,422 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.873
2025-08-05 09:52:15,425 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:15,425 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:15,426 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:15,426 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:15,467 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -221.318, 聚类评分: 0.000, 覆盖率: 0.151, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:15,468 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:15,468 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:15,468 - visualization.landscape_visualizer - INFO - 设置当前实例名: eil51
2025-08-05 09:52:15,475 - visualization.landscape_visualizer - INFO - 插值约束: 86 个点被约束到最小值 427.00
2025-08-05 09:52:15,595 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\landscape_eil51_iter_130_20250805_095215.html
2025-08-05 09:52:15,648 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_eil51\dashboard_eil51_iter_130_20250805_095215.html
2025-08-05 09:52:15,648 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 130
2025-08-05 09:52:15,649 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:15,649 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2232秒
2025-08-05 09:52:15,649 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -221.3176470588235, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 184087.40733564013, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1508, 'fitness_entropy': 0.8075408601354863, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -221.318)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.151)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358735.4689255, 'performance_metrics': {}}}
2025-08-05 09:52:15,649 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:15,649 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:15,650 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:15,650 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:15,650 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:15,650 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:15,650 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:15,650 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,650 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:15,651 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 09:52:15,651 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:15,651 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:15,651 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:15,651 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:15,652 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:15,652 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,656 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1102.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,658 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [36, 5, 3, 46, 37, 20, 4, 49, 48, 32, 16, 41, 13, 12, 18, 24, 47, 50, 10, 15, 19, 34, 8, 38, 29, 44, 39, 9, 28, 0, 1, 31, 27, 2, 21, 7, 6, 25, 30, 42, 17, 26, 11, 14, 43, 33, 45, 22, 23, 40, 35], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 50, 4, 37, 10, 31, 26, 48, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 605.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 17, 23, 42, 6, 22, 47, 5, 13, 24, 12, 40, 18, 41, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 544.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 12, 38, 21], 'cur_cost': 639.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,658 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1102.00)
2025-08-05 09:52:15,659 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:15,659 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:15,659 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,662 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,663 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,663 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,663 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,664 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1212.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,664 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [41, 8, 33, 0, 7, 50, 14, 15, 4, 46, 16, 11, 45, 3, 18, 9, 48, 31, 26, 5, 30, 22, 23, 27, 20, 32, 49, 34, 25, 19, 21, 35, 38, 29, 1, 6, 17, 43, 36, 40, 13, 47, 2, 10, 28, 44, 37, 12, 24, 42, 39], 'cur_cost': 1212.0, 'intermediate_solutions': [{'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 37, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 35, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1482.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 0, 7, 29, 13, 24, 12, 39, 2, 27, 48, 34, 22, 33, 15, 1, 17, 45, 40, 18, 31, 43, 47, 4, 10, 8, 26, 20, 36, 35, 30, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 50, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,664 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1212.00)
2025-08-05 09:52:15,664 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:15,665 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:15,665 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,668 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,669 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1109.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,669 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [24, 36, 16, 26, 30, 34, 28, 27, 5, 11, 13, 3, 39, 41, 46, 23, 21, 50, 45, 1, 0, 4, 9, 44, 18, 40, 32, 37, 38, 31, 19, 10, 8, 2, 25, 47, 22, 6, 12, 43, 14, 29, 49, 33, 48, 20, 35, 7, 42, 17, 15], 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 19, 13, 26, 23, 22, 31, 20, 32, 10, 18, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 48, 39, 16, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1216.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 16, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1218.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,670 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1109.00)
2025-08-05 09:52:15,670 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:15,670 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,670 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,670 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1643.0
2025-08-05 09:52:15,678 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,678 - ExploitationExpert - INFO - res_population_costs: [427, 429, 429, 434, 435, 435, 438.0]
2025-08-05 09:52:15,679 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64), array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:15,681 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,681 - ExploitationExpert - INFO - populations: [{'tour': [36, 5, 3, 46, 37, 20, 4, 49, 48, 32, 16, 41, 13, 12, 18, 24, 47, 50, 10, 15, 19, 34, 8, 38, 29, 44, 39, 9, 28, 0, 1, 31, 27, 2, 21, 7, 6, 25, 30, 42, 17, 26, 11, 14, 43, 33, 45, 22, 23, 40, 35], 'cur_cost': 1102.0}, {'tour': [41, 8, 33, 0, 7, 50, 14, 15, 4, 46, 16, 11, 45, 3, 18, 9, 48, 31, 26, 5, 30, 22, 23, 27, 20, 32, 49, 34, 25, 19, 21, 35, 38, 29, 1, 6, 17, 43, 36, 40, 13, 47, 2, 10, 28, 44, 37, 12, 24, 42, 39], 'cur_cost': 1212.0}, {'tour': [24, 36, 16, 26, 30, 34, 28, 27, 5, 11, 13, 3, 39, 41, 46, 23, 21, 50, 45, 1, 0, 4, 9, 44, 18, 40, 32, 37, 38, 31, 19, 10, 8, 2, 25, 47, 22, 6, 12, 43, 14, 29, 49, 33, 48, 20, 35, 7, 42, 17, 15], 'cur_cost': 1109.0}, {'tour': array([24, 30, 47, 43, 21, 23, 12, 40, 13, 36, 20, 17, 42,  4, 46, 19,  0,
       25, 26, 39, 34,  8, 38,  7, 31, 32, 27, 15,  3,  6,  5, 18,  1, 45,
       22, 16, 10, 37, 41, 35, 11, 48,  2, 49, 44, 33, 28, 50, 29,  9, 14],
      dtype=int64), 'cur_cost': 1643.0}, {'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1113.0}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1065.0}, {'tour': [46, 31, 0, 29, 41, 38, 18, 6, 47, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 19, 2, 7, 39, 26, 12, 27, 4, 33, 10, 40, 32, 37, 30, 34, 1, 36, 25, 14, 11, 9, 48, 15, 45, 49, 5, 23, 17], 'cur_cost': 1539.0}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 16, 36, 14, 43, 41, 39, 44, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 606.0}, {'tour': [46, 16, 3, 47, 20, 12, 11, 34, 38, 35, 6, 41, 18, 32, 30, 37, 36, 1, 49, 5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22, 7, 44, 39, 14, 40, 23, 8, 50, 17, 48, 42, 9, 10, 27, 4, 29, 0, 2, 25, 21, 26], 'cur_cost': 1590.0}, {'tour': [34, 11, 5, 14, 27, 23, 16, 2, 36, 30, 28, 49, 10, 26, 37, 43, 18, 40, 17, 46, 3, 25, 22, 48, 41, 12, 24, 9, 21, 29, 47, 35, 20, 7, 42, 50, 39, 44, 19, 8, 13, 4, 31, 33, 0, 15, 32, 1, 38, 45, 6], 'cur_cost': 1557.0}]
2025-08-05 09:52:15,682 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,682 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 336, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 336, 'cache_hits': 0, 'similarity_calculations': 1723, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,683 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([24, 30, 47, 43, 21, 23, 12, 40, 13, 36, 20, 17, 42,  4, 46, 19,  0,
       25, 26, 39, 34,  8, 38,  7, 31, 32, 27, 15,  3,  6,  5, 18,  1, 45,
       22, 16, 10, 37, 41, 35, 11, 48,  2, 49, 44, 33, 28, 50, 29,  9, 14],
      dtype=int64), 'cur_cost': 1643.0, 'intermediate_solutions': [{'tour': array([40,  2, 36, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1656.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 40,  2, 36,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 23, 40,  2, 36, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([36, 23, 40,  2,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([36,  8, 23, 40,  2, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,683 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1643.00)
2025-08-05 09:52:15,683 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:15,684 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:15,684 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,688 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 51
2025-08-05 09:52:15,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1196.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,689 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [11, 36, 43, 39, 50, 3, 9, 41, 4, 5, 10, 37, 49, 29, 20, 48, 19, 47, 26, 6, 31, 13, 7, 23, 0, 45, 22, 16, 8, 1, 2, 35, 15, 27, 30, 24, 40, 44, 14, 32, 46, 12, 18, 42, 21, 34, 28, 33, 38, 17, 25], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 9, 37, 49, 33, 16, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 25, 24], 'cur_cost': 1126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [45, 42, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,689 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1196.00)
2025-08-05 09:52:15,689 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:15,689 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:15,689 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,690 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:15,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1480.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,692 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [41, 46, 7, 50, 14, 15, 48, 10, 11, 45, 3, 20, 9, 31, 13, 1, 19, 22, 8, 38, 29, 44, 32, 34, 28, 21, 27, 33, 25, 17, 43, 12, 42, 26, 4, 49, 35, 23, 39, 37, 2, 24, 40, 5, 0, 36, 30, 16, 18, 6, 47], 'cur_cost': 1480.0, 'intermediate_solutions': [{'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 39, 14, 36, 40, 48, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 33, 43, 3, 44, 18, 46, 39, 40, 36, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 25, 42, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,692 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1480.00)
2025-08-05 09:52:15,692 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:15,692 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:15,692 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,693 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 51
2025-08-05 09:52:15,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,694 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,694 - ExplorationExpert - INFO - 探索路径生成完成，成本: 680.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,694 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 7, 18, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 21, 33, 29, 9, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [46, 31, 0, 29, 41, 38, 18, 6, 10, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 19, 2, 7, 39, 26, 12, 27, 4, 33, 47, 40, 32, 37, 30, 34, 1, 36, 25, 14, 11, 9, 48, 15, 45, 49, 5, 23, 17], 'cur_cost': 1605.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 31, 0, 29, 41, 38, 18, 6, 47, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 45, 15, 48, 9, 11, 14, 25, 36, 1, 34, 30, 37, 32, 40, 10, 33, 4, 27, 12, 26, 39, 7, 2, 19, 49, 5, 23, 17], 'cur_cost': 1506.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 31, 0, 29, 41, 38, 18, 6, 47, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 19, 2, 7, 39, 26, 12, 4, 33, 10, 40, 32, 37, 30, 34, 1, 36, 25, 14, 11, 9, 48, 15, 45, 49, 27, 5, 23, 17], 'cur_cost': 1507.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,695 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 680.00)
2025-08-05 09:52:15,695 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:15,695 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:15,695 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:15,696 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 51
2025-08-05 09:52:15,696 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,696 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:15,697 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1410.0, 路径长度: 51, 收集中间解: 3
2025-08-05 09:52:15,697 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [36, 5, 16, 26, 30, 20, 28, 32, 10, 11, 39, 3, 37, 23, 47, 31, 8, 1, 19, 34, 27, 29, 44, 49, 33, 15, 0, 21, 42, 25, 7, 6, 43, 12, 48, 17, 24, 2, 14, 4, 35, 41, 22, 9, 46, 50, 38, 40, 13, 18, 45], 'cur_cost': 1410.0, 'intermediate_solutions': [{'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 8, 36, 14, 43, 41, 39, 44, 32, 9, 48, 16, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 35, 34, 19, 2, 47, 42, 23, 22, 6, 25, 7, 30, 27, 21, 37, 4, 38, 29, 33, 20, 28, 1, 15, 49, 8, 48, 9, 32, 44, 39, 41, 43, 14, 36, 16, 17, 46, 11, 50, 45, 31, 26], 'cur_cost': 621.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 17, 16, 36, 14, 43, 41, 39, 44, 46, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:15,697 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1410.00)
2025-08-05 09:52:15,697 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:15,697 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,698 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,698 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1669.0
2025-08-05 09:52:15,704 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,704 - ExploitationExpert - INFO - res_population_costs: [427, 429, 429, 434, 435, 435, 438.0]
2025-08-05 09:52:15,704 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64), array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:15,707 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,707 - ExploitationExpert - INFO - populations: [{'tour': [36, 5, 3, 46, 37, 20, 4, 49, 48, 32, 16, 41, 13, 12, 18, 24, 47, 50, 10, 15, 19, 34, 8, 38, 29, 44, 39, 9, 28, 0, 1, 31, 27, 2, 21, 7, 6, 25, 30, 42, 17, 26, 11, 14, 43, 33, 45, 22, 23, 40, 35], 'cur_cost': 1102.0}, {'tour': [41, 8, 33, 0, 7, 50, 14, 15, 4, 46, 16, 11, 45, 3, 18, 9, 48, 31, 26, 5, 30, 22, 23, 27, 20, 32, 49, 34, 25, 19, 21, 35, 38, 29, 1, 6, 17, 43, 36, 40, 13, 47, 2, 10, 28, 44, 37, 12, 24, 42, 39], 'cur_cost': 1212.0}, {'tour': [24, 36, 16, 26, 30, 34, 28, 27, 5, 11, 13, 3, 39, 41, 46, 23, 21, 50, 45, 1, 0, 4, 9, 44, 18, 40, 32, 37, 38, 31, 19, 10, 8, 2, 25, 47, 22, 6, 12, 43, 14, 29, 49, 33, 48, 20, 35, 7, 42, 17, 15], 'cur_cost': 1109.0}, {'tour': array([24, 30, 47, 43, 21, 23, 12, 40, 13, 36, 20, 17, 42,  4, 46, 19,  0,
       25, 26, 39, 34,  8, 38,  7, 31, 32, 27, 15,  3,  6,  5, 18,  1, 45,
       22, 16, 10, 37, 41, 35, 11, 48,  2, 49, 44, 33, 28, 50, 29,  9, 14],
      dtype=int64), 'cur_cost': 1643.0}, {'tour': [11, 36, 43, 39, 50, 3, 9, 41, 4, 5, 10, 37, 49, 29, 20, 48, 19, 47, 26, 6, 31, 13, 7, 23, 0, 45, 22, 16, 8, 1, 2, 35, 15, 27, 30, 24, 40, 44, 14, 32, 46, 12, 18, 42, 21, 34, 28, 33, 38, 17, 25], 'cur_cost': 1196.0}, {'tour': [41, 46, 7, 50, 14, 15, 48, 10, 11, 45, 3, 20, 9, 31, 13, 1, 19, 22, 8, 38, 29, 44, 32, 34, 28, 21, 27, 33, 25, 17, 43, 12, 42, 26, 4, 49, 35, 23, 39, 37, 2, 24, 40, 5, 0, 36, 30, 16, 18, 6, 47], 'cur_cost': 1480.0}, {'tour': [0, 7, 18, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 21, 33, 29, 9, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 680.0}, {'tour': [36, 5, 16, 26, 30, 20, 28, 32, 10, 11, 39, 3, 37, 23, 47, 31, 8, 1, 19, 34, 27, 29, 44, 49, 33, 15, 0, 21, 42, 25, 7, 6, 43, 12, 48, 17, 24, 2, 14, 4, 35, 41, 22, 9, 46, 50, 38, 40, 13, 18, 45], 'cur_cost': 1410.0}, {'tour': array([20, 41, 28,  8, 16, 19, 42,  9, 49, 18, 13, 26, 50, 24, 47, 21,  5,
        4, 38, 37,  3, 11,  1, 12, 17, 34,  6, 45, 10, 33,  0, 31, 14, 15,
       25, 29, 39, 46, 40, 32, 43,  7, 36, 48, 30, 35, 22, 44, 27,  2, 23],
      dtype=int64), 'cur_cost': 1669.0}, {'tour': [34, 11, 5, 14, 27, 23, 16, 2, 36, 30, 28, 49, 10, 26, 37, 43, 18, 40, 17, 46, 3, 25, 22, 48, 41, 12, 24, 9, 21, 29, 47, 35, 20, 7, 42, 50, 39, 44, 19, 8, 13, 4, 31, 33, 0, 15, 32, 1, 38, 45, 6], 'cur_cost': 1557.0}]
2025-08-05 09:52:15,708 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,708 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 337, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 337, 'cache_hits': 0, 'similarity_calculations': 1734, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,709 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([20, 41, 28,  8, 16, 19, 42,  9, 49, 18, 13, 26, 50, 24, 47, 21,  5,
        4, 38, 37,  3, 11,  1, 12, 17, 34,  6, 45, 10, 33,  0, 31, 14, 15,
       25, 29, 39, 46, 40, 32, 43,  7, 36, 48, 30, 35, 22, 44, 27,  2, 23],
      dtype=int64), 'cur_cost': 1669.0, 'intermediate_solutions': [{'tour': array([ 3, 16, 46, 47, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  3, 16, 46, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1581.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 47,  3, 16, 46, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 47,  3, 16, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 20, 47,  3, 16, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,710 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1669.00)
2025-08-05 09:52:15,710 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:15,710 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:15,710 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:15,710 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1676.0
2025-08-05 09:52:15,719 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:15,719 - ExploitationExpert - INFO - res_population_costs: [427, 429, 429, 434, 435, 435, 438.0]
2025-08-05 09:52:15,720 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 10, 37,  4, 48,  9, 38, 32, 44, 14, 36, 16, 43, 41, 18, 39,
       40, 12, 24, 13, 17,  3, 46, 11, 45, 50, 26,  5, 47, 22, 23, 42,  6,
       25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 29,  8, 49, 15,  1, 21],
      dtype=int64), array([ 0, 21,  1, 15, 49, 33, 20, 28, 19, 34, 35,  2, 27, 30,  7, 25,  6,
       42, 23, 22, 47,  5, 26, 50, 45, 11, 36, 16,  3, 46, 17, 13, 24, 12,
       40, 18, 39, 41, 43, 14, 44, 32, 38,  9, 29,  8, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  8, 29,  9, 38, 32, 44, 14, 43, 41, 18, 39,
       40, 12, 24, 13, 17, 46,  3, 16, 36, 11, 45, 50, 26,  5, 47, 22, 23,
       42,  6, 25,  7, 30, 27,  2, 35, 34, 19, 28, 20, 33, 49, 15,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 39, 18,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27,  2, 35, 34,
       19, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31,  1, 21],
      dtype=int64), array([ 0, 26, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44, 43, 41, 18, 39,
       40, 12, 24, 13, 23, 42,  6, 22,  5, 47,  7, 25, 30, 27, 21,  2, 35,
       34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,  9, 48,  4, 37, 10, 31],
      dtype=int64), array([ 0, 31, 10, 37,  4, 48,  9, 38, 29,  8, 15, 49, 33, 20, 28,  1, 19,
       34, 35,  2, 21, 27, 30, 25,  7, 47,  5, 22,  6, 42, 23, 13, 24, 12,
       40, 18, 39, 41, 43, 44, 32, 14, 36, 16,  3, 17, 46, 11, 45, 50, 26],
      dtype=int64), array([ 0, 21, 30, 27,  2, 35, 34, 19,  1, 28, 20, 33, 49, 15,  8, 29, 38,
        9, 48,  4, 37, 10, 31, 50, 45, 11, 46, 17,  3, 16, 36, 14, 32, 44,
       43, 41, 39, 18, 40, 12, 24, 13, 23, 42, 22,  6, 25,  7, 47,  5, 26],
      dtype=int64)]
2025-08-05 09:52:15,723 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:15,723 - ExploitationExpert - INFO - populations: [{'tour': [36, 5, 3, 46, 37, 20, 4, 49, 48, 32, 16, 41, 13, 12, 18, 24, 47, 50, 10, 15, 19, 34, 8, 38, 29, 44, 39, 9, 28, 0, 1, 31, 27, 2, 21, 7, 6, 25, 30, 42, 17, 26, 11, 14, 43, 33, 45, 22, 23, 40, 35], 'cur_cost': 1102.0}, {'tour': [41, 8, 33, 0, 7, 50, 14, 15, 4, 46, 16, 11, 45, 3, 18, 9, 48, 31, 26, 5, 30, 22, 23, 27, 20, 32, 49, 34, 25, 19, 21, 35, 38, 29, 1, 6, 17, 43, 36, 40, 13, 47, 2, 10, 28, 44, 37, 12, 24, 42, 39], 'cur_cost': 1212.0}, {'tour': [24, 36, 16, 26, 30, 34, 28, 27, 5, 11, 13, 3, 39, 41, 46, 23, 21, 50, 45, 1, 0, 4, 9, 44, 18, 40, 32, 37, 38, 31, 19, 10, 8, 2, 25, 47, 22, 6, 12, 43, 14, 29, 49, 33, 48, 20, 35, 7, 42, 17, 15], 'cur_cost': 1109.0}, {'tour': array([24, 30, 47, 43, 21, 23, 12, 40, 13, 36, 20, 17, 42,  4, 46, 19,  0,
       25, 26, 39, 34,  8, 38,  7, 31, 32, 27, 15,  3,  6,  5, 18,  1, 45,
       22, 16, 10, 37, 41, 35, 11, 48,  2, 49, 44, 33, 28, 50, 29,  9, 14],
      dtype=int64), 'cur_cost': 1643.0}, {'tour': [11, 36, 43, 39, 50, 3, 9, 41, 4, 5, 10, 37, 49, 29, 20, 48, 19, 47, 26, 6, 31, 13, 7, 23, 0, 45, 22, 16, 8, 1, 2, 35, 15, 27, 30, 24, 40, 44, 14, 32, 46, 12, 18, 42, 21, 34, 28, 33, 38, 17, 25], 'cur_cost': 1196.0}, {'tour': [41, 46, 7, 50, 14, 15, 48, 10, 11, 45, 3, 20, 9, 31, 13, 1, 19, 22, 8, 38, 29, 44, 32, 34, 28, 21, 27, 33, 25, 17, 43, 12, 42, 26, 4, 49, 35, 23, 39, 37, 2, 24, 40, 5, 0, 36, 30, 16, 18, 6, 47], 'cur_cost': 1480.0}, {'tour': [0, 7, 18, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 21, 33, 29, 9, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 680.0}, {'tour': [36, 5, 16, 26, 30, 20, 28, 32, 10, 11, 39, 3, 37, 23, 47, 31, 8, 1, 19, 34, 27, 29, 44, 49, 33, 15, 0, 21, 42, 25, 7, 6, 43, 12, 48, 17, 24, 2, 14, 4, 35, 41, 22, 9, 46, 50, 38, 40, 13, 18, 45], 'cur_cost': 1410.0}, {'tour': array([20, 41, 28,  8, 16, 19, 42,  9, 49, 18, 13, 26, 50, 24, 47, 21,  5,
        4, 38, 37,  3, 11,  1, 12, 17, 34,  6, 45, 10, 33,  0, 31, 14, 15,
       25, 29, 39, 46, 40, 32, 43,  7, 36, 48, 30, 35, 22, 44, 27,  2, 23],
      dtype=int64), 'cur_cost': 1669.0}, {'tour': array([29,  6, 41, 20, 18,  5, 26, 15, 36, 22, 50, 27, 35, 46, 19, 14, 30,
       21, 37, 48, 28, 33, 47, 31, 43, 10, 11,  4,  2, 17,  9,  0, 45, 34,
        7, 24, 12, 49, 23, 32,  8,  1, 38, 39, 16, 25, 13, 40, 42,  3, 44],
      dtype=int64), 'cur_cost': 1676.0}]
2025-08-05 09:52:15,724 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:15,725 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 338, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 338, 'cache_hits': 0, 'similarity_calculations': 1746, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:15,725 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([29,  6, 41, 20, 18,  5, 26, 15, 36, 22, 50, 27, 35, 46, 19, 14, 30,
       21, 37, 48, 28, 33, 47, 31, 43, 10, 11,  4,  2, 17,  9,  0, 45, 34,
        7, 24, 12, 49, 23, 32,  8,  1, 38, 39, 16, 25, 13, 40, 42,  3, 44],
      dtype=int64), 'cur_cost': 1676.0, 'intermediate_solutions': [{'tour': array([ 5, 11, 34, 14, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  5, 11, 34, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 14,  5, 11, 34, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 14,  5, 11, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 27, 14,  5, 11, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:15,726 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1676.00)
2025-08-05 09:52:15,726 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:15,726 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:15,729 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [36, 5, 3, 46, 37, 20, 4, 49, 48, 32, 16, 41, 13, 12, 18, 24, 47, 50, 10, 15, 19, 34, 8, 38, 29, 44, 39, 9, 28, 0, 1, 31, 27, 2, 21, 7, 6, 25, 30, 42, 17, 26, 11, 14, 43, 33, 45, 22, 23, 40, 35], 'cur_cost': 1102.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 50, 4, 37, 10, 31, 26, 48, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 12, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 605.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 17, 23, 42, 6, 22, 47, 5, 13, 24, 12, 40, 18, 41, 39, 44, 32, 9, 29, 33, 38, 21], 'cur_cost': 544.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 15, 7, 25, 30, 27, 2, 19, 34, 35, 28, 20, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 18, 40, 24, 13, 5, 47, 22, 6, 42, 23, 17, 39, 44, 32, 9, 29, 33, 12, 38, 21], 'cur_cost': 639.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [41, 8, 33, 0, 7, 50, 14, 15, 4, 46, 16, 11, 45, 3, 18, 9, 48, 31, 26, 5, 30, 22, 23, 27, 20, 32, 49, 34, 25, 19, 21, 35, 38, 29, 1, 6, 17, 43, 36, 40, 13, 47, 2, 10, 28, 44, 37, 12, 24, 42, 39], 'cur_cost': 1212.0, 'intermediate_solutions': [{'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 30, 37, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 21, 41, 35, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1482.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 11, 5, 14, 25, 23, 16, 3, 19, 0, 7, 29, 13, 24, 12, 39, 2, 27, 48, 34, 22, 33, 15, 1, 17, 45, 40, 18, 31, 43, 47, 4, 10, 8, 26, 20, 36, 35, 30, 6, 42, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1440.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 5, 14, 25, 23, 16, 3, 19, 30, 35, 36, 20, 26, 8, 10, 4, 47, 43, 31, 18, 40, 45, 17, 1, 15, 33, 22, 34, 48, 27, 2, 39, 12, 24, 13, 29, 7, 0, 6, 42, 50, 21, 41, 37, 38, 49, 9, 28, 44, 32, 46], 'cur_cost': 1451.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [24, 36, 16, 26, 30, 34, 28, 27, 5, 11, 13, 3, 39, 41, 46, 23, 21, 50, 45, 1, 0, 4, 9, 44, 18, 40, 32, 37, 38, 31, 19, 10, 8, 2, 25, 47, 22, 6, 12, 43, 14, 29, 49, 33, 48, 20, 35, 7, 42, 17, 15], 'cur_cost': 1109.0, 'intermediate_solutions': [{'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 19, 13, 26, 23, 22, 31, 20, 32, 10, 18, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 16, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 48, 39, 16, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1216.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 37, 21, 6, 27, 35, 8, 15, 36, 4, 14, 11, 46, 3, 18, 13, 26, 23, 22, 31, 20, 32, 10, 16, 19, 30, 5, 25, 45, 0, 47, 42, 12, 24, 41, 9, 43, 39, 48, 29, 1, 33, 28, 2, 50, 7, 49, 38, 44, 40, 17], 'cur_cost': 1218.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 30, 47, 43, 21, 23, 12, 40, 13, 36, 20, 17, 42,  4, 46, 19,  0,
       25, 26, 39, 34,  8, 38,  7, 31, 32, 27, 15,  3,  6,  5, 18,  1, 45,
       22, 16, 10, 37, 41, 35, 11, 48,  2, 49, 44, 33, 28, 50, 29,  9, 14],
      dtype=int64), 'cur_cost': 1643.0, 'intermediate_solutions': [{'tour': array([40,  2, 36, 23,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1656.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 40,  2, 36,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 23, 40,  2, 36, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1623.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([36, 23, 40,  2,  8, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1615.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([36,  8, 23, 40,  2, 41, 49, 22, 34, 21, 32,  9, 33, 25,  1,  3, 14,
       19, 13,  0,  7, 20, 15, 37, 16, 12, 17, 43,  6,  5, 27, 44,  4, 45,
       18, 24, 11, 35, 28, 31, 10, 29, 42, 46, 39, 47, 38, 30, 26, 48, 50]), 'cur_cost': 1640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [11, 36, 43, 39, 50, 3, 9, 41, 4, 5, 10, 37, 49, 29, 20, 48, 19, 47, 26, 6, 31, 13, 7, 23, 0, 45, 22, 16, 8, 1, 2, 35, 15, 27, 30, 24, 40, 44, 14, 32, 46, 12, 18, 42, 21, 34, 28, 33, 38, 17, 25], 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 9, 37, 49, 33, 16, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [45, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 42, 12, 43, 39, 44, 38, 15, 46, 25, 24], 'cur_cost': 1126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [45, 42, 14, 40, 17, 6, 13, 23, 50, 0, 8, 48, 31, 1, 20, 35, 4, 10, 21, 19, 27, 34, 5, 47, 7, 22, 36, 11, 26, 30, 3, 18, 41, 32, 16, 37, 49, 33, 9, 29, 28, 2, 12, 43, 39, 44, 38, 15, 46, 24, 25], 'cur_cost': 1159.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [41, 46, 7, 50, 14, 15, 48, 10, 11, 45, 3, 20, 9, 31, 13, 1, 19, 22, 8, 38, 29, 44, 32, 34, 28, 21, 27, 33, 25, 17, 43, 12, 42, 26, 4, 49, 35, 23, 39, 37, 2, 24, 40, 5, 0, 36, 30, 16, 18, 6, 47], 'cur_cost': 1480.0, 'intermediate_solutions': [{'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 39, 14, 36, 40, 48, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 42, 25, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 33, 43, 3, 44, 18, 46, 39, 40, 36, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [37, 9, 50, 13, 12, 4, 10, 49, 8, 32, 17, 22, 25, 42, 24, 6, 27, 30, 7, 5, 26, 0, 28, 48, 14, 36, 40, 39, 46, 18, 44, 3, 43, 33, 15, 21, 47, 2, 35, 31, 20, 1, 29, 38, 11, 41, 16, 45, 23, 19, 34], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 18, 20, 28, 1, 15, 49, 8, 48, 4, 37, 10, 31, 26, 50, 45, 11, 46, 3, 16, 36, 14, 43, 41, 40, 12, 24, 13, 5, 47, 22, 6, 25, 30, 27, 2, 19, 34, 35, 21, 33, 29, 9, 38, 32, 44, 17, 23, 42, 39], 'cur_cost': 680.0, 'intermediate_solutions': [{'tour': [46, 31, 0, 29, 41, 38, 18, 6, 10, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 19, 2, 7, 39, 26, 12, 27, 4, 33, 47, 40, 32, 37, 30, 34, 1, 36, 25, 14, 11, 9, 48, 15, 45, 49, 5, 23, 17], 'cur_cost': 1605.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 31, 0, 29, 41, 38, 18, 6, 47, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 45, 15, 48, 9, 11, 14, 25, 36, 1, 34, 30, 37, 32, 40, 10, 33, 4, 27, 12, 26, 39, 7, 2, 19, 49, 5, 23, 17], 'cur_cost': 1506.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 31, 0, 29, 41, 38, 18, 6, 47, 22, 24, 42, 3, 35, 16, 20, 43, 44, 13, 21, 8, 28, 50, 19, 2, 7, 39, 26, 12, 4, 33, 10, 40, 32, 37, 30, 34, 1, 36, 25, 14, 11, 9, 48, 15, 45, 49, 27, 5, 23, 17], 'cur_cost': 1507.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [36, 5, 16, 26, 30, 20, 28, 32, 10, 11, 39, 3, 37, 23, 47, 31, 8, 1, 19, 34, 27, 29, 44, 49, 33, 15, 0, 21, 42, 25, 7, 6, 43, 12, 48, 17, 24, 2, 14, 4, 35, 41, 22, 9, 46, 50, 38, 40, 13, 18, 45], 'cur_cost': 1410.0, 'intermediate_solutions': [{'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 46, 17, 8, 36, 14, 43, 41, 39, 44, 32, 9, 48, 16, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 687.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 35, 34, 19, 2, 47, 42, 23, 22, 6, 25, 7, 30, 27, 21, 37, 4, 38, 29, 33, 20, 28, 1, 15, 49, 8, 48, 9, 32, 44, 39, 41, 43, 14, 36, 16, 17, 46, 11, 50, 45, 31, 26], 'cur_cost': 621.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 10, 18, 40, 12, 24, 13, 5, 26, 31, 45, 50, 11, 17, 16, 36, 14, 43, 41, 39, 44, 46, 32, 9, 48, 8, 49, 15, 1, 28, 20, 33, 29, 38, 4, 37, 21, 27, 30, 7, 25, 6, 22, 23, 42, 47, 2, 19, 34, 35], 'cur_cost': 655.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 41, 28,  8, 16, 19, 42,  9, 49, 18, 13, 26, 50, 24, 47, 21,  5,
        4, 38, 37,  3, 11,  1, 12, 17, 34,  6, 45, 10, 33,  0, 31, 14, 15,
       25, 29, 39, 46, 40, 32, 43,  7, 36, 48, 30, 35, 22, 44, 27,  2, 23],
      dtype=int64), 'cur_cost': 1669.0, 'intermediate_solutions': [{'tour': array([ 3, 16, 46, 47, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([47,  3, 16, 46, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1581.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 47,  3, 16, 46, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 47,  3, 16, 20, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 20, 47,  3, 16, 12, 11, 34, 38, 35,  6, 41, 18, 32, 30, 37, 36,
        1, 49,  5, 28, 31, 45, 43, 13, 19, 33, 15, 24, 22,  7, 44, 39, 14,
       40, 23,  8, 50, 17, 48, 42,  9, 10, 27,  4, 29,  0,  2, 25, 21, 26]), 'cur_cost': 1582.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([29,  6, 41, 20, 18,  5, 26, 15, 36, 22, 50, 27, 35, 46, 19, 14, 30,
       21, 37, 48, 28, 33, 47, 31, 43, 10, 11,  4,  2, 17,  9,  0, 45, 34,
        7, 24, 12, 49, 23, 32,  8,  1, 38, 39, 16, 25, 13, 40, 42,  3, 44],
      dtype=int64), 'cur_cost': 1676.0, 'intermediate_solutions': [{'tour': array([ 5, 11, 34, 14, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1548.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  5, 11, 34, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 14,  5, 11, 34, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 14,  5, 11, 27, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 27, 14,  5, 11, 23, 16,  2, 36, 30, 28, 49, 10, 26, 37, 43, 18,
       40, 17, 46,  3, 25, 22, 48, 41, 12, 24,  9, 21, 29, 47, 35, 20,  7,
       42, 50, 39, 44, 19,  8, 13,  4, 31, 33,  0, 15, 32,  1, 38, 45,  6]), 'cur_cost': 1524.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:15,729 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:15,729 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,733 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=680.000, 多样性=0.976
2025-08-05 09:52:15,733 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:15,733 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:15,733 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:15,734 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11317774187309988, 'best_improvement': -0.2454212454212454}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0004464285714285319}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01768906232755178, 'recent_improvements': [-0.0704774510072652, -0.009206748973533362, -0.03509932635216165], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 427, 'new_best_cost': 427, 'quality_improvement': 0.0, 'old_diversity': 0.873015873015873, 'new_diversity': 0.873015873015873, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:15,734 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:15,749 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\eil51_solution.json
2025-08-05 09:52:15,749 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\eil51_20250805_095215.solution
2025-08-05 09:52:15,749 - __main__ - INFO - 实例执行完成 - 运行时间: 1.48s, 最佳成本: 427
2025-08-05 09:52:15,749 - __main__ - INFO - 实例 eil51 处理完成
