# -*- coding: utf-8 -*-

import time
import importlib
import numpy as np
import sys

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from utils import utils
from core.algorithms.gls_evol_enhanced import tour_cost_2End
from core.algorithms.guided_local_search_with_similarity import guided_local_search_with_similarity

# 在函数开始处添加类型检查
def solve_instance(dis_matrix, time_limit, ite_max, perturbation_moves, init_tour,
                  evo_individual, populations, res_populations):
    


   
    # time.sleep(1)
    t = time.time()

    try:
        # 转换数据类型前先进行深拷贝，避免修改原始数据
        import copy
        init_tour_copy = copy.deepcopy(init_tour)
        dis_matrix_copy = copy.deepcopy(dis_matrix)
        
        # 使用拷贝后的数据进行类型转换
        init_tour_array = np.array(init_tour_copy, dtype=np.int64)
        # distance matrix may stay float/int; keep original dtype to avoid precision issues
        dis_matrix_array = np.array(dis_matrix_copy)
        
       
        init_cost = tour_cost_2End(dis_matrix_array, init_tour_array)
        
      
        nb = len(dis_matrix_array)  # 邻域大小
        nearest_indices = np.argsort(dis_matrix_array, axis=1)[:, 1:nb+1].astype(np.int64)  # 最近邻矩阵索引

            
        # gls_evol_enhanced.guided_local_search(
        #     dis_matrix_array, nearest_indices, init_tour_array, init_cost,
        #     t + time_limit, ite_max, perturbation_moves, evo_individual, 
        #     evo_populations, res_populations,
        #     first_improvement=False
        # ) 
        #  

        guided_local_search_with_similarity(
            dis_matrix_array, nearest_indices, init_tour_array, init_cost,
            t + time_limit, ite_max, perturbation_moves, evo_individual, 
            populations, res_populations,
            first_improvement=False
        )       

    except Exception as e:
        print("Error:", str(e))  # 打印错误信息
        import traceback
        traceback.print_exc()  # 打印完整的错误堆栈

# 添加一个非JIT版本的tour_cost_2End函数，用于调试模式
def calculate_tour_cost_2End(dis_m, tour2End):
    c = 0
    s = 0
    e = tour2End[0, 1]
    for i in range(tour2End.shape[0]):
        c += dis_m[s, e]
        s = e
        e = tour2End[s, 1]
    return c


        
    
   
  