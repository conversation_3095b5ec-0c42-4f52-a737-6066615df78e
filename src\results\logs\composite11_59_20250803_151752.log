2025-08-03 15:17:52,872 - __main__ - INFO - composite11_59 开始进化第 1 代
2025-08-03 15:17:52,872 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 15:17:52,875 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:17:52,890 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=24805.000, 多样性=0.974
2025-08-03 15:17:52,895 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:17:52,900 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.974
2025-08-03 15:17:52,903 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:17:52,908 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/3)
2025-08-03 15:17:52,908 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:17:52,909 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 15:17:52,909 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 15:17:53,186 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -5431.380, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:17:53,186 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 15:17:53,187 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 15:17:53,258 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 15:17:53,585 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_151753.html
2025-08-03 15:17:53,633 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_151753.html
2025-08-03 15:17:53,634 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 15:17:53,634 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 15:17:53,634 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7257秒
2025-08-03 15:17:53,634 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 15:17:53,635 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -5431.379999999999, 'local_optima_density': 0.15, 'gradient_variance': 15530097405.399597, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9763620978123273, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5431.380)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 3, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754205473.1867952, 'performance_metrics': {}}}
2025-08-03 15:17:53,636 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:17:53,636 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:17:53,636 - StrategyExpert - INFO - 动态计算的探索比例: 0.800
2025-08-03 15:17:53,641 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:53,641 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:53,642 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.800
- 探索个体数量: 16
- 利用个体数量: 4
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:17:53,642 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:17:53,643 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:53,643 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.800
- 探索个体数量: 16
- 利用个体数量: 4
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:17:53,643 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:17:53,644 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 5} (总数: 4)
2025-08-03 15:17:53,644 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:17:53,644 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:17:53,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,661 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:53,661 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,839 - ExplorationExpert - INFO - 探索路径生成完成，成本: 131344.0, 路径长度: 59
2025-08-03 15:17:53,886 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}
2025-08-03 15:17:53,886 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 131344.00)
2025-08-03 15:17:53,887 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-03 15:17:53,887 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-03 15:17:53,888 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,893 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:53,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,894 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32624.0, 路径长度: 59
2025-08-03 15:17:53,894 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}
2025-08-03 15:17:53,895 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 32624.00)
2025-08-03 15:17:53,895 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:17:53,895 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:17:53,895 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,910 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:53,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 142754.0, 路径长度: 59
2025-08-03 15:17:53,912 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}
2025-08-03 15:17:53,912 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 142754.00)
2025-08-03 15:17:53,914 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:17:53,915 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:17:53,918 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,927 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:53,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32445.0, 路径长度: 59
2025-08-03 15:17:53,929 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}
2025-08-03 15:17:53,930 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 32445.00)
2025-08-03 15:17:53,930 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:17:53,931 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:17:53,931 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,939 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:53,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,941 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37240.0, 路径长度: 59
2025-08-03 15:17:53,942 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}
2025-08-03 15:17:53,943 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 37240.00)
2025-08-03 15:17:53,943 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:17:53,943 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:17:53,944 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:53,952 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:53,953 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:53,955 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37241.0, 路径长度: 59
2025-08-03 15:17:53,956 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}
2025-08-03 15:17:53,958 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 37241.00)
2025-08-03 15:17:53,959 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-03 15:17:53,960 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:17:53,963 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:17:53,965 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 241624.0
2025-08-03 15:17:56,125 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 15:17:56,125 - ExploitationExpert - INFO - res_population_costs: [252967.0]
2025-08-03 15:17:56,126 - ExploitationExpert - INFO - res_populations: [array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64)]
2025-08-03 15:17:56,126 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:17:56,127 - ExploitationExpert - INFO - populations: [{'tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}, {'tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}, {'tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}, {'tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}, {'tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}, {'tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}, {'tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}, {'tour': array([46, 55, 54, 48, 26, 20, 58, 15, 47, 13, 39,  6, 51,  1, 56, 10, 12,
       14, 35,  8,  4, 11, 44, 33, 21, 34, 41, 19, 37, 52, 28, 16, 30, 53,
       43,  9, 36, 38, 25, 49, 32,  5, 50, 31, 18,  0, 45, 23, 57, 27,  3,
       42, 17, 29, 40,  2,  7, 22, 24], dtype=int64), 'cur_cost': 284440.0}, {'tour': array([17, 24, 31, 57, 37, 19,  8,  5, 50, 34, 38, 13, 35, 30, 45, 54,  7,
        3, 46,  9, 28, 15, 32, 42, 23, 53, 25, 40,  0, 22, 51,  6, 56,  4,
       39, 10, 26, 44, 47, 48, 33, 18, 16,  1, 12, 11, 43, 41, 55, 20, 14,
       27, 52, 21, 36, 58, 29, 49,  2], dtype=int64), 'cur_cost': 271680.0}, {'tour': array([31, 22, 42, 34, 17, 18,  6, 19, 46, 13, 28, 32, 54, 53, 48, 45, 35,
       38, 41, 50, 49,  0, 15, 40, 21, 26, 24, 56, 11, 43, 25, 39,  1, 27,
       36, 12,  4,  9, 52, 14, 16, 51, 23, 44,  2, 55, 37, 58, 29, 30,  7,
       20, 57,  8, 10, 33,  5, 47,  3], dtype=int64), 'cur_cost': 241760.0}, {'tour': array([15, 17, 16, 54,  6, 25, 26, 50, 42, 46, 14, 36,  3, 20, 32, 48, 45,
       24, 38, 51, 31, 30,  4, 28,  0, 57, 33, 53, 22, 41, 49, 13,  5, 29,
       18, 35, 12, 21,  9, 37,  1,  8, 19, 10, 39, 58, 55,  7, 43, 34, 47,
       44, 23,  2, 27, 52, 40, 11, 56], dtype=int64), 'cur_cost': 270317.0}, {'tour': array([52, 29, 11, 12, 33, 31, 40,  8, 54, 51, 41, 47, 37, 39, 49, 42, 34,
       50,  6, 58, 14, 26, 44, 10, 27, 20, 23, 17, 13, 32,  7, 38,  5,  0,
       25,  9, 22, 18,  4,  3, 55, 48, 19, 57, 43, 30, 46, 16, 53, 28, 35,
       24, 56, 36, 21,  1,  2, 45, 15], dtype=int64), 'cur_cost': 251727.0}, {'tour': array([39, 36, 44, 41, 24, 23, 11, 37, 40, 10, 42,  5, 49, 25, 18,  2, 46,
       51,  1, 43, 22, 54, 47, 35, 50, 33, 30, 45, 57, 58, 27,  8, 14, 34,
       15, 38, 17, 32, 16, 31, 28,  0, 56, 13, 53, 55, 19, 20,  4,  9,  6,
       21, 12,  7, 29, 48, 52, 26,  3], dtype=int64), 'cur_cost': 227132.0}, {'tour': array([34, 51, 17, 43,  4, 58, 20,  3, 41, 42, 33,  7, 19, 10, 39, 22,  5,
       36, 55, 13,  1, 25, 24, 15,  8, 37, 40, 35, 48, 50, 57, 21, 46, 47,
       14, 53, 54, 52, 29, 45, 31, 18,  2,  0, 26, 30, 11, 56, 27, 32, 49,
        9, 16, 38,  6, 28, 12, 44, 23], dtype=int64), 'cur_cost': 240662.0}, {'tour': array([52, 32, 35, 49, 41, 24, 12, 37,  8, 29, 46,  3, 20, 36,  4, 47,  1,
       17, 33, 56, 38, 39, 30, 22,  2, 34,  5, 27, 53, 48, 55, 43, 45, 23,
       50,  6, 26, 51, 11, 13,  0, 58, 40, 57, 10, 42, 18, 31, 28, 54,  9,
       16, 19, 14, 15, 44, 25,  7, 21], dtype=int64), 'cur_cost': 253805.0}, {'tour': array([ 4, 29, 39, 10, 35, 52, 33, 41, 40, 47, 27, 51,  0, 21, 45, 58,  3,
       13, 20, 18, 12, 22, 44, 31, 28, 16, 19, 38, 46,  9, 49, 54, 23,  2,
       25,  8, 14, 57,  7,  1, 32, 15, 37, 30, 11,  5, 48, 17, 24, 43, 50,
       53, 34, 36, 42,  6, 26, 56, 55], dtype=int64), 'cur_cost': 234748.0}, {'tour': array([13, 18, 15, 33, 36, 49, 27, 42, 46, 30, 32,  4, 34,  2, 17, 12,  8,
       43,  3, 54, 38, 47, 29,  5, 28, 50, 37, 48, 44,  0, 16, 14, 53,  6,
       22, 39, 35, 19, 23, 25, 45, 41, 51, 24, 58, 21, 52,  7, 55, 57,  1,
       56, 26, 11,  9, 40, 20, 10, 31], dtype=int64), 'cur_cost': 246679.0}, {'tour': array([11, 56,  1, 35, 27,  5, 54, 58, 51, 31, 45, 48, 10, 34, 18, 37, 26,
       30, 36,  7, 25, 42, 43, 20, 52, 14, 44, 17, 28, 23, 29, 39, 19, 40,
       47,  0, 57, 33,  2, 46, 55,  3, 41,  8, 12,  9, 22, 50,  6, 13,  4,
       32, 21, 53, 24, 15, 49, 16, 38], dtype=int64), 'cur_cost': 273839.0}, {'tour': array([21,  0, 31, 12,  7, 50, 35, 51, 24, 15, 17, 20, 18,  5, 45, 36, 25,
       30,  3, 10, 34,  8, 44, 38, 43, 53, 52, 26, 19, 37, 23, 42, 32, 29,
       14, 58, 41, 40,  1, 27, 46, 33, 28,  9, 55, 22, 54, 13, 39,  4,  2,
       49, 11,  6, 48, 56, 16, 47, 57], dtype=int64), 'cur_cost': 215276.0}, {'tour': array([11, 26, 48, 27,  6, 35, 50, 51, 47, 55, 14, 16, 29, 58,  3, 49,  1,
       41, 43, 12, 13, 25, 21, 53, 32, 52, 33, 34, 37, 30, 10,  0,  8, 44,
       23,  9, 56, 17, 36, 57, 42, 39, 18, 28,  2,  5, 20, 15,  4, 31, 46,
       54, 38, 45, 40,  7, 19, 22, 24], dtype=int64), 'cur_cost': 228688.0}]
2025-08-03 15:17:56,135 - ExploitationExpert - INFO - 局部搜索耗时: 2.17秒
2025-08-03 15:17:56,135 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 15:17:56,136 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}
2025-08-03 15:17:56,137 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 241624.00)
2025-08-03 15:17:56,137 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 15:17:56,138 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:17:56,138 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:17:56,139 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 256853.0
2025-08-03 15:17:58,535 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 15:17:58,535 - ExploitationExpert - INFO - res_population_costs: [252967.0, 24499.0]
2025-08-03 15:17:58,536 - ExploitationExpert - INFO - res_populations: [array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64)]
2025-08-03 15:17:58,537 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:17:58,537 - ExploitationExpert - INFO - populations: [{'tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}, {'tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}, {'tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}, {'tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}, {'tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}, {'tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}, {'tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}, {'tour': array([14, 39, 13,  7, 43, 16,  1, 26, 51, 11, 50, 21,  4, 44, 19, 33,  2,
       22, 15,  9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10,  3, 40,
       37,  8, 24, 42, 38,  6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57,
       30, 28, 23, 54, 46, 58,  5,  0], dtype=int64), 'cur_cost': 256853.0}, {'tour': array([17, 24, 31, 57, 37, 19,  8,  5, 50, 34, 38, 13, 35, 30, 45, 54,  7,
        3, 46,  9, 28, 15, 32, 42, 23, 53, 25, 40,  0, 22, 51,  6, 56,  4,
       39, 10, 26, 44, 47, 48, 33, 18, 16,  1, 12, 11, 43, 41, 55, 20, 14,
       27, 52, 21, 36, 58, 29, 49,  2], dtype=int64), 'cur_cost': 271680.0}, {'tour': array([31, 22, 42, 34, 17, 18,  6, 19, 46, 13, 28, 32, 54, 53, 48, 45, 35,
       38, 41, 50, 49,  0, 15, 40, 21, 26, 24, 56, 11, 43, 25, 39,  1, 27,
       36, 12,  4,  9, 52, 14, 16, 51, 23, 44,  2, 55, 37, 58, 29, 30,  7,
       20, 57,  8, 10, 33,  5, 47,  3], dtype=int64), 'cur_cost': 241760.0}, {'tour': array([15, 17, 16, 54,  6, 25, 26, 50, 42, 46, 14, 36,  3, 20, 32, 48, 45,
       24, 38, 51, 31, 30,  4, 28,  0, 57, 33, 53, 22, 41, 49, 13,  5, 29,
       18, 35, 12, 21,  9, 37,  1,  8, 19, 10, 39, 58, 55,  7, 43, 34, 47,
       44, 23,  2, 27, 52, 40, 11, 56], dtype=int64), 'cur_cost': 270317.0}, {'tour': array([52, 29, 11, 12, 33, 31, 40,  8, 54, 51, 41, 47, 37, 39, 49, 42, 34,
       50,  6, 58, 14, 26, 44, 10, 27, 20, 23, 17, 13, 32,  7, 38,  5,  0,
       25,  9, 22, 18,  4,  3, 55, 48, 19, 57, 43, 30, 46, 16, 53, 28, 35,
       24, 56, 36, 21,  1,  2, 45, 15], dtype=int64), 'cur_cost': 251727.0}, {'tour': array([39, 36, 44, 41, 24, 23, 11, 37, 40, 10, 42,  5, 49, 25, 18,  2, 46,
       51,  1, 43, 22, 54, 47, 35, 50, 33, 30, 45, 57, 58, 27,  8, 14, 34,
       15, 38, 17, 32, 16, 31, 28,  0, 56, 13, 53, 55, 19, 20,  4,  9,  6,
       21, 12,  7, 29, 48, 52, 26,  3], dtype=int64), 'cur_cost': 227132.0}, {'tour': array([34, 51, 17, 43,  4, 58, 20,  3, 41, 42, 33,  7, 19, 10, 39, 22,  5,
       36, 55, 13,  1, 25, 24, 15,  8, 37, 40, 35, 48, 50, 57, 21, 46, 47,
       14, 53, 54, 52, 29, 45, 31, 18,  2,  0, 26, 30, 11, 56, 27, 32, 49,
        9, 16, 38,  6, 28, 12, 44, 23], dtype=int64), 'cur_cost': 240662.0}, {'tour': array([52, 32, 35, 49, 41, 24, 12, 37,  8, 29, 46,  3, 20, 36,  4, 47,  1,
       17, 33, 56, 38, 39, 30, 22,  2, 34,  5, 27, 53, 48, 55, 43, 45, 23,
       50,  6, 26, 51, 11, 13,  0, 58, 40, 57, 10, 42, 18, 31, 28, 54,  9,
       16, 19, 14, 15, 44, 25,  7, 21], dtype=int64), 'cur_cost': 253805.0}, {'tour': array([ 4, 29, 39, 10, 35, 52, 33, 41, 40, 47, 27, 51,  0, 21, 45, 58,  3,
       13, 20, 18, 12, 22, 44, 31, 28, 16, 19, 38, 46,  9, 49, 54, 23,  2,
       25,  8, 14, 57,  7,  1, 32, 15, 37, 30, 11,  5, 48, 17, 24, 43, 50,
       53, 34, 36, 42,  6, 26, 56, 55], dtype=int64), 'cur_cost': 234748.0}, {'tour': array([13, 18, 15, 33, 36, 49, 27, 42, 46, 30, 32,  4, 34,  2, 17, 12,  8,
       43,  3, 54, 38, 47, 29,  5, 28, 50, 37, 48, 44,  0, 16, 14, 53,  6,
       22, 39, 35, 19, 23, 25, 45, 41, 51, 24, 58, 21, 52,  7, 55, 57,  1,
       56, 26, 11,  9, 40, 20, 10, 31], dtype=int64), 'cur_cost': 246679.0}, {'tour': array([11, 56,  1, 35, 27,  5, 54, 58, 51, 31, 45, 48, 10, 34, 18, 37, 26,
       30, 36,  7, 25, 42, 43, 20, 52, 14, 44, 17, 28, 23, 29, 39, 19, 40,
       47,  0, 57, 33,  2, 46, 55,  3, 41,  8, 12,  9, 22, 50,  6, 13,  4,
       32, 21, 53, 24, 15, 49, 16, 38], dtype=int64), 'cur_cost': 273839.0}, {'tour': array([21,  0, 31, 12,  7, 50, 35, 51, 24, 15, 17, 20, 18,  5, 45, 36, 25,
       30,  3, 10, 34,  8, 44, 38, 43, 53, 52, 26, 19, 37, 23, 42, 32, 29,
       14, 58, 41, 40,  1, 27, 46, 33, 28,  9, 55, 22, 54, 13, 39,  4,  2,
       49, 11,  6, 48, 56, 16, 47, 57], dtype=int64), 'cur_cost': 215276.0}, {'tour': array([11, 26, 48, 27,  6, 35, 50, 51, 47, 55, 14, 16, 29, 58,  3, 49,  1,
       41, 43, 12, 13, 25, 21, 53, 32, 52, 33, 34, 37, 30, 10,  0,  8, 44,
       23,  9, 56, 17, 36, 57, 42, 39, 18, 28,  2,  5, 20, 15,  4, 31, 46,
       54, 38, 45, 40,  7, 19, 22, 24], dtype=int64), 'cur_cost': 228688.0}]
2025-08-03 15:17:58,545 - ExploitationExpert - INFO - 局部搜索耗时: 2.41秒
2025-08-03 15:17:58,546 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 15:17:58,546 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([14, 39, 13,  7, 43, 16,  1, 26, 51, 11, 50, 21,  4, 44, 19, 33,  2,
       22, 15,  9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10,  3, 40,
       37,  8, 24, 42, 38,  6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57,
       30, 28, 23, 54, 46, 58,  5,  0], dtype=int64), 'cur_cost': 256853.0}
2025-08-03 15:17:58,548 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 256853.00)
2025-08-03 15:17:58,551 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-03 15:17:58,552 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:17:58,552 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:17:58,553 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 240956.0
2025-08-03 15:17:59,335 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 15:17:59,336 - ExploitationExpert - INFO - res_population_costs: [252967.0, 24499.0]
2025-08-03 15:17:59,336 - ExploitationExpert - INFO - res_populations: [array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64)]
2025-08-03 15:17:59,337 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:17:59,337 - ExploitationExpert - INFO - populations: [{'tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}, {'tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}, {'tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}, {'tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}, {'tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}, {'tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}, {'tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}, {'tour': array([14, 39, 13,  7, 43, 16,  1, 26, 51, 11, 50, 21,  4, 44, 19, 33,  2,
       22, 15,  9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10,  3, 40,
       37,  8, 24, 42, 38,  6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57,
       30, 28, 23, 54, 46, 58,  5,  0], dtype=int64), 'cur_cost': 256853.0}, {'tour': array([27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29,  0,
       35,  8, 24, 34, 14,  6, 47,  7, 51, 43, 41, 39, 23, 55, 20, 57, 53,
       13,  9,  4, 44,  1, 18, 21, 10, 32, 33, 40, 30,  2, 22, 11, 31, 16,
        5, 25, 38, 52, 46,  3, 54, 45], dtype=int64), 'cur_cost': 240956.0}, {'tour': array([31, 22, 42, 34, 17, 18,  6, 19, 46, 13, 28, 32, 54, 53, 48, 45, 35,
       38, 41, 50, 49,  0, 15, 40, 21, 26, 24, 56, 11, 43, 25, 39,  1, 27,
       36, 12,  4,  9, 52, 14, 16, 51, 23, 44,  2, 55, 37, 58, 29, 30,  7,
       20, 57,  8, 10, 33,  5, 47,  3], dtype=int64), 'cur_cost': 241760.0}, {'tour': array([15, 17, 16, 54,  6, 25, 26, 50, 42, 46, 14, 36,  3, 20, 32, 48, 45,
       24, 38, 51, 31, 30,  4, 28,  0, 57, 33, 53, 22, 41, 49, 13,  5, 29,
       18, 35, 12, 21,  9, 37,  1,  8, 19, 10, 39, 58, 55,  7, 43, 34, 47,
       44, 23,  2, 27, 52, 40, 11, 56], dtype=int64), 'cur_cost': 270317.0}, {'tour': array([52, 29, 11, 12, 33, 31, 40,  8, 54, 51, 41, 47, 37, 39, 49, 42, 34,
       50,  6, 58, 14, 26, 44, 10, 27, 20, 23, 17, 13, 32,  7, 38,  5,  0,
       25,  9, 22, 18,  4,  3, 55, 48, 19, 57, 43, 30, 46, 16, 53, 28, 35,
       24, 56, 36, 21,  1,  2, 45, 15], dtype=int64), 'cur_cost': 251727.0}, {'tour': array([39, 36, 44, 41, 24, 23, 11, 37, 40, 10, 42,  5, 49, 25, 18,  2, 46,
       51,  1, 43, 22, 54, 47, 35, 50, 33, 30, 45, 57, 58, 27,  8, 14, 34,
       15, 38, 17, 32, 16, 31, 28,  0, 56, 13, 53, 55, 19, 20,  4,  9,  6,
       21, 12,  7, 29, 48, 52, 26,  3], dtype=int64), 'cur_cost': 227132.0}, {'tour': array([34, 51, 17, 43,  4, 58, 20,  3, 41, 42, 33,  7, 19, 10, 39, 22,  5,
       36, 55, 13,  1, 25, 24, 15,  8, 37, 40, 35, 48, 50, 57, 21, 46, 47,
       14, 53, 54, 52, 29, 45, 31, 18,  2,  0, 26, 30, 11, 56, 27, 32, 49,
        9, 16, 38,  6, 28, 12, 44, 23], dtype=int64), 'cur_cost': 240662.0}, {'tour': array([52, 32, 35, 49, 41, 24, 12, 37,  8, 29, 46,  3, 20, 36,  4, 47,  1,
       17, 33, 56, 38, 39, 30, 22,  2, 34,  5, 27, 53, 48, 55, 43, 45, 23,
       50,  6, 26, 51, 11, 13,  0, 58, 40, 57, 10, 42, 18, 31, 28, 54,  9,
       16, 19, 14, 15, 44, 25,  7, 21], dtype=int64), 'cur_cost': 253805.0}, {'tour': array([ 4, 29, 39, 10, 35, 52, 33, 41, 40, 47, 27, 51,  0, 21, 45, 58,  3,
       13, 20, 18, 12, 22, 44, 31, 28, 16, 19, 38, 46,  9, 49, 54, 23,  2,
       25,  8, 14, 57,  7,  1, 32, 15, 37, 30, 11,  5, 48, 17, 24, 43, 50,
       53, 34, 36, 42,  6, 26, 56, 55], dtype=int64), 'cur_cost': 234748.0}, {'tour': array([13, 18, 15, 33, 36, 49, 27, 42, 46, 30, 32,  4, 34,  2, 17, 12,  8,
       43,  3, 54, 38, 47, 29,  5, 28, 50, 37, 48, 44,  0, 16, 14, 53,  6,
       22, 39, 35, 19, 23, 25, 45, 41, 51, 24, 58, 21, 52,  7, 55, 57,  1,
       56, 26, 11,  9, 40, 20, 10, 31], dtype=int64), 'cur_cost': 246679.0}, {'tour': array([11, 56,  1, 35, 27,  5, 54, 58, 51, 31, 45, 48, 10, 34, 18, 37, 26,
       30, 36,  7, 25, 42, 43, 20, 52, 14, 44, 17, 28, 23, 29, 39, 19, 40,
       47,  0, 57, 33,  2, 46, 55,  3, 41,  8, 12,  9, 22, 50,  6, 13,  4,
       32, 21, 53, 24, 15, 49, 16, 38], dtype=int64), 'cur_cost': 273839.0}, {'tour': array([21,  0, 31, 12,  7, 50, 35, 51, 24, 15, 17, 20, 18,  5, 45, 36, 25,
       30,  3, 10, 34,  8, 44, 38, 43, 53, 52, 26, 19, 37, 23, 42, 32, 29,
       14, 58, 41, 40,  1, 27, 46, 33, 28,  9, 55, 22, 54, 13, 39,  4,  2,
       49, 11,  6, 48, 56, 16, 47, 57], dtype=int64), 'cur_cost': 215276.0}, {'tour': array([11, 26, 48, 27,  6, 35, 50, 51, 47, 55, 14, 16, 29, 58,  3, 49,  1,
       41, 43, 12, 13, 25, 21, 53, 32, 52, 33, 34, 37, 30, 10,  0,  8, 44,
       23,  9, 56, 17, 36, 57, 42, 39, 18, 28,  2,  5, 20, 15,  4, 31, 46,
       54, 38, 45, 40,  7, 19, 22, 24], dtype=int64), 'cur_cost': 228688.0}]
2025-08-03 15:17:59,347 - ExploitationExpert - INFO - 局部搜索耗时: 0.79秒
2025-08-03 15:17:59,349 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 15:17:59,351 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29,  0,
       35,  8, 24, 34, 14,  6, 47,  7, 51, 43, 41, 39, 23, 55, 20, 57, 53,
       13,  9,  4, 44,  1, 18, 21, 10, 32, 33, 40, 30,  2, 22, 11, 31, 16,
        5, 25, 38, 52, 46,  3, 54, 45], dtype=int64), 'cur_cost': 240956.0}
2025-08-03 15:17:59,352 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 240956.00)
2025-08-03 15:17:59,352 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:17:59,352 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:17:59,353 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,362 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24926.0, 路径长度: 59
2025-08-03 15:17:59,363 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}
2025-08-03 15:17:59,363 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 24926.00)
2025-08-03 15:17:59,363 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-03 15:17:59,364 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-03 15:17:59,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,368 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34909.0, 路径长度: 59
2025-08-03 15:17:59,369 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}
2025-08-03 15:17:59,369 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 34909.00)
2025-08-03 15:17:59,369 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:17:59,370 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:17:59,370 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,377 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,378 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37116.0, 路径长度: 59
2025-08-03 15:17:59,378 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}
2025-08-03 15:17:59,379 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 37116.00)
2025-08-03 15:17:59,379 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:17:59,380 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:17:59,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,400 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:59,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,401 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165485.0, 路径长度: 59
2025-08-03 15:17:59,401 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}
2025-08-03 15:17:59,402 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 165485.00)
2025-08-03 15:17:59,402 - experts.management.collaboration_manager - INFO - 为个体 13 生成探索路径
2025-08-03 15:17:59,402 - ExplorationExpert - INFO - 开始为个体 13 生成探索路径（算法实现）
2025-08-03 15:17:59,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,421 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:59,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155447.0, 路径长度: 59
2025-08-03 15:17:59,428 - experts.management.collaboration_manager - INFO - 个体 13 探索路径生成报告: {'new_tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}
2025-08-03 15:17:59,429 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 155447.00)
2025-08-03 15:17:59,430 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 15:17:59,430 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 15:17:59,431 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,435 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:17:59,435 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,436 - ExplorationExpert - INFO - 探索路径生成完成，成本: 223121.0, 路径长度: 59
2025-08-03 15:17:59,436 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}
2025-08-03 15:17:59,436 - experts.management.collaboration_manager - INFO - 个体 14 保留原路径 (成本: 223121.00)
2025-08-03 15:17:59,437 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:17:59,437 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:17:59,438 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,454 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:59,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 180968.0, 路径长度: 59
2025-08-03 15:17:59,458 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}
2025-08-03 15:17:59,459 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 180968.00)
2025-08-03 15:17:59,460 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:17:59,460 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:17:59,460 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,465 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,466 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,466 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27097.0, 路径长度: 59
2025-08-03 15:17:59,466 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}
2025-08-03 15:17:59,467 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 27097.00)
2025-08-03 15:17:59,467 - experts.management.collaboration_manager - INFO - 为个体 17 生成利用路径
2025-08-03 15:17:59,467 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:17:59,468 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:17:59,468 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 17 处的路径，新成本: 243790.0
2025-08-03 15:17:59,556 - ExploitationExpert - INFO - res_population_num: 6
2025-08-03 15:17:59,557 - ExploitationExpert - INFO - res_population_costs: [252967.0, 24499.0, 24475.0, 24451, 24451, 24451]
2025-08-03 15:17:59,557 - ExploitationExpert - INFO - res_populations: [array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-03 15:17:59,562 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:17:59,563 - ExploitationExpert - INFO - populations: [{'tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}, {'tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}, {'tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}, {'tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}, {'tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}, {'tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}, {'tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}, {'tour': array([14, 39, 13,  7, 43, 16,  1, 26, 51, 11, 50, 21,  4, 44, 19, 33,  2,
       22, 15,  9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10,  3, 40,
       37,  8, 24, 42, 38,  6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57,
       30, 28, 23, 54, 46, 58,  5,  0], dtype=int64), 'cur_cost': 256853.0}, {'tour': array([27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29,  0,
       35,  8, 24, 34, 14,  6, 47,  7, 51, 43, 41, 39, 23, 55, 20, 57, 53,
       13,  9,  4, 44,  1, 18, 21, 10, 32, 33, 40, 30,  2, 22, 11, 31, 16,
        5, 25, 38, 52, 46,  3, 54, 45], dtype=int64), 'cur_cost': 240956.0}, {'tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}, {'tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}, {'tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}, {'tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}, {'tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}, {'tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}, {'tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}, {'tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}, {'tour': array([14, 21, 56, 17, 50, 51, 28, 42, 53,  5,  4, 18, 22, 33, 26,  2, 48,
       27,  7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54,  8, 31, 49,
       52, 41,  1, 12, 38, 11,  3, 20, 37, 57, 39,  9,  0, 43, 58, 25, 13,
       47, 19, 32,  6, 45, 46, 55, 24], dtype=int64), 'cur_cost': 243790.0}, {'tour': array([21,  0, 31, 12,  7, 50, 35, 51, 24, 15, 17, 20, 18,  5, 45, 36, 25,
       30,  3, 10, 34,  8, 44, 38, 43, 53, 52, 26, 19, 37, 23, 42, 32, 29,
       14, 58, 41, 40,  1, 27, 46, 33, 28,  9, 55, 22, 54, 13, 39,  4,  2,
       49, 11,  6, 48, 56, 16, 47, 57], dtype=int64), 'cur_cost': 215276.0}, {'tour': array([11, 26, 48, 27,  6, 35, 50, 51, 47, 55, 14, 16, 29, 58,  3, 49,  1,
       41, 43, 12, 13, 25, 21, 53, 32, 52, 33, 34, 37, 30, 10,  0,  8, 44,
       23,  9, 56, 17, 36, 57, 42, 39, 18, 28,  2,  5, 20, 15,  4, 31, 46,
       54, 38, 45, 40,  7, 19, 22, 24], dtype=int64), 'cur_cost': 228688.0}]
2025-08-03 15:17:59,567 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:17:59,568 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 15:17:59,568 - experts.management.collaboration_manager - INFO - 个体 17 利用路径生成报告: {'new_tour': array([14, 21, 56, 17, 50, 51, 28, 42, 53,  5,  4, 18, 22, 33, 26,  2, 48,
       27,  7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54,  8, 31, 49,
       52, 41,  1, 12, 38, 11,  3, 20, 37, 57, 39,  9,  0, 43, 58, 25, 13,
       47, 19, 32,  6, 45, 46, 55, 24], dtype=int64), 'cur_cost': 243790.0}
2025-08-03 15:17:59,569 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 243790.00)
2025-08-03 15:17:59,569 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:17:59,569 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:17:59,569 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,583 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:17:59,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 161112.0, 路径长度: 59
2025-08-03 15:17:59,586 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}
2025-08-03 15:17:59,586 - experts.management.collaboration_manager - INFO - 个体 18 保留原路径 (成本: 161112.00)
2025-08-03 15:17:59,587 - experts.management.collaboration_manager - INFO - 为个体 19 生成探索路径
2025-08-03 15:17:59,587 - ExplorationExpert - INFO - 开始为个体 19 生成探索路径（算法实现）
2025-08-03 15:17:59,587 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,591 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27136.0, 路径长度: 59
2025-08-03 15:17:59,592 - experts.management.collaboration_manager - INFO - 个体 19 探索路径生成报告: {'new_tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}
2025-08-03 15:17:59,593 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 27136.00)
2025-08-03 15:17:59,593 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 13, 'rejected': 7, 'elite_protected': 4}
2025-08-03 15:17:59,593 - experts.management.collaboration_manager - INFO - 接受率: 13/20 (65.0%)
2025-08-03 15:17:59,594 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [24, 30, 23, 29, 9, 12, 13, 21, 11, 10, 34, 25, 26, 28, 46, 6, 20, 4, 18, 31, 43, 1, 19, 14, 8, 15, 3, 51, 7, 32, 45, 42, 39, 0, 44, 35, 41, 40, 5, 53, 54, 57, 49, 48, 56, 52, 22, 16, 17, 33, 36, 2, 50, 47, 55, 58, 37, 38, 27], 'cur_cost': 131344.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 22, 23, 34, 27, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 4, 3, 5, 2, 1, 7, 6, 19, 18, 13, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32624.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [48, 53, 58, 52, 54, 14, 22, 27, 8, 18, 33, 39, 38, 9, 3, 30, 10, 11, 20, 6, 29, 24, 46, 44, 7, 12, 5, 50, 0, 34, 4, 19, 2, 35, 41, 1, 28, 25, 23, 40, 43, 57, 51, 47, 49, 56, 13, 16, 17, 15, 26, 37, 45, 42, 36, 32, 31, 21, 55], 'cur_cost': 142754.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 18, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32445.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 25, 15, 7, 1, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 37240.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 23, 14, 13, 18, 22, 21, 12, 11, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37241.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 54,  4, 35, 25, 41, 22, 20, 36, 32, 53, 40, 28, 11, 30, 24,  9,
        2, 13, 39, 17, 21, 12,  5, 56, 14, 45, 26,  7, 57,  6,  8, 42, 52,
       19, 58, 16, 55, 50, 46,  1, 37, 44,  0, 48, 27, 23, 34, 43, 15, 51,
       10, 18, 38, 47,  3, 29, 31, 33], dtype=int64), 'cur_cost': 241624.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 39, 13,  7, 43, 16,  1, 26, 51, 11, 50, 21,  4, 44, 19, 33,  2,
       22, 15,  9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10,  3, 40,
       37,  8, 24, 42, 38,  6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57,
       30, 28, 23, 54, 46, 58,  5,  0], dtype=int64), 'cur_cost': 256853.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29,  0,
       35,  8, 24, 34, 14,  6, 47,  7, 51, 43, 41, 39, 23, 55, 20, 57, 53,
       13,  9,  4, 44,  1, 18, 21, 10, 32, 33, 40, 30,  2, 22, 11, 31, 16,
        5, 25, 38, 52, 46,  3, 54, 45], dtype=int64), 'cur_cost': 240956.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}}, {'individual': 10, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}}, {'individual': 13, 'strategy': 'explore', 'path_data': {'new_tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}}, {'individual': 17, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 21, 56, 17, 50, 51, 28, 42, 53,  5,  4, 18, 22, 33, 26,  2, 48,
       27,  7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54,  8, 31, 49,
       52, 41,  1, 12, 38, 11,  3, 20, 37, 57, 39,  9,  0, 43, 58, 25, 13,
       47, 19, 32,  6, 45, 46, 55, 24], dtype=int64), 'cur_cost': 243790.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}}, {'individual': 19, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}}]
2025-08-03 15:17:59,596 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:17:59,597 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:17:59,608 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=24926.000, 多样性=0.958
2025-08-03 15:17:59,609 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 15:17:59,609 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 15:17:59,609 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:17:59,610 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.14319025965911428, 'best_improvement': -0.004878048780487805}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016843647015745148}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 24451, 'new_best_cost': 24451, 'quality_improvement': 0.0, 'old_diversity': 0.7875706214689265, 'new_diversity': 0.7875706214689265, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 15:17:59,610 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 15:17:59,611 - __main__ - INFO - composite11_59 开始进化第 2 代
2025-08-03 15:17:59,611 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-03 15:17:59,611 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:17:59,613 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=24926.000, 多样性=0.958
2025-08-03 15:17:59,616 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:17:59,624 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.958
2025-08-03 15:17:59,626 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:17:59,628 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.788
2025-08-03 15:17:59,630 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/3)
2025-08-03 15:17:59,630 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:17:59,630 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-03 15:17:59,630 - LandscapeExpert - INFO - 数据提取成功: 26个路径, 26个适应度值
2025-08-03 15:17:59,756 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.192, 适应度梯度: -24639.446, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:17:59,757 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-03 15:17:59,758 - LandscapeExpert - INFO - 提取到 6 个精英解
2025-08-03 15:17:59,768 - visualization.landscape_visualizer - INFO - 已添加 6 个精英解标记
2025-08-03 15:17:59,877 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250803_151759.html
2025-08-03 15:17:59,933 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250803_151759.html
2025-08-03 15:17:59,934 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-03 15:17:59,934 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-03 15:17:59,934 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3040秒
2025-08-03 15:17:59,935 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.19230769230769232, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -24639.446153846155, 'local_optima_density': 0.19230769230769232, 'gradient_variance': 7565399768.99787, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0046, 'fitness_entropy': 0.7520409018306821, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -24639.446)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 3, 'progress': 0.3333333333333333}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754205479.757605, 'performance_metrics': {}}}
2025-08-03 15:17:59,935 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:17:59,936 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:17:59,936 - StrategyExpert - INFO - 动态计算的探索比例: 0.750
2025-08-03 15:17:59,937 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:59,938 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:59,939 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.750
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:17:59,939 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:17:59,940 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:17:59,940 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.750
- 探索个体数量: 15
- 利用个体数量: 5
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:17:59,941 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:17:59,941 - experts.management.collaboration_manager - INFO - 识别精英个体: {16, 9, 19, 3} (总数: 4)
2025-08-03 15:17:59,941 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:17:59,941 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:17:59,942 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,944 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,945 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34706.0, 路径长度: 59
2025-08-03 15:17:59,945 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}
2025-08-03 15:17:59,946 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 34706.00)
2025-08-03 15:17:59,946 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-03 15:17:59,946 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-03 15:17:59,946 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,951 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,951 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,952 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32620.0, 路径长度: 59
2025-08-03 15:17:59,952 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}
2025-08-03 15:17:59,952 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 32620.00)
2025-08-03 15:17:59,953 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:17:59,953 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:17:59,953 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,958 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27113.0, 路径长度: 59
2025-08-03 15:17:59,959 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}
2025-08-03 15:17:59,960 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 27113.00)
2025-08-03 15:17:59,960 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:17:59,960 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:17:59,960 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,963 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,963 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,963 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34671.0, 路径长度: 59
2025-08-03 15:17:59,964 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}
2025-08-03 15:17:59,964 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 34671.00)
2025-08-03 15:17:59,964 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-03 15:17:59,965 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-03 15:17:59,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,967 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:17:59,967 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,968 - ExplorationExpert - INFO - 探索路径生成完成，成本: 239523.0, 路径长度: 59
2025-08-03 15:17:59,968 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}
2025-08-03 15:17:59,968 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 239523.00)
2025-08-03 15:17:59,968 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:17:59,968 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:17:59,969 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:17:59,972 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:17:59,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:17:59,973 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34510.0, 路径长度: 59
2025-08-03 15:17:59,973 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}
2025-08-03 15:17:59,973 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 34510.00)
2025-08-03 15:17:59,974 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-03 15:17:59,974 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:17:59,974 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:17:59,975 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 269452.0
2025-08-03 15:18:00,064 - ExploitationExpert - INFO - res_population_num: 7
2025-08-03 15:18:00,064 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451]
2025-08-03 15:18:00,065 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-03 15:18:00,069 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:00,069 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}, {'tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}, {'tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}, {'tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}, {'tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}, {'tour': [14, 39, 13, 7, 43, 16, 1, 26, 51, 11, 50, 21, 4, 44, 19, 33, 2, 22, 15, 9, 55, 20, 47, 35, 32, 31, 12, 45, 53, 52, 41, 10, 3, 40, 37, 8, 24, 42, 38, 6, 56, 27, 34, 48, 29, 49, 17, 18, 25, 36, 57, 30, 28, 23, 54, 46, 58, 5, 0], 'cur_cost': 256853.0}, {'tour': [27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29, 0, 35, 8, 24, 34, 14, 6, 47, 7, 51, 43, 41, 39, 23, 55, 20, 57, 53, 13, 9, 4, 44, 1, 18, 21, 10, 32, 33, 40, 30, 2, 22, 11, 31, 16, 5, 25, 38, 52, 46, 3, 54, 45], 'cur_cost': 240956.0}, {'tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}, {'tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}, {'tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}, {'tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}, {'tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}, {'tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}, {'tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}, {'tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}, {'tour': [14, 21, 56, 17, 50, 51, 28, 42, 53, 5, 4, 18, 22, 33, 26, 2, 48, 27, 7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54, 8, 31, 49, 52, 41, 1, 12, 38, 11, 3, 20, 37, 57, 39, 9, 0, 43, 58, 25, 13, 47, 19, 32, 6, 45, 46, 55, 24], 'cur_cost': 243790.0}, {'tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}, {'tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}]
2025-08-03 15:18:00,073 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:18:00,073 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 15:18:00,074 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}
2025-08-03 15:18:00,074 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 269452.00)
2025-08-03 15:18:00,074 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 15:18:00,075 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:00,075 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:00,075 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 250874.0
2025-08-03 15:18:00,151 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 15:18:00,151 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451]
2025-08-03 15:18:00,152 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64)]
2025-08-03 15:18:00,159 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:00,159 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}, {'tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}, {'tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}, {'tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}, {'tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': [27, 37, 17, 15, 50, 12, 42, 48, 19, 36, 49, 26, 28, 56, 58, 29, 0, 35, 8, 24, 34, 14, 6, 47, 7, 51, 43, 41, 39, 23, 55, 20, 57, 53, 13, 9, 4, 44, 1, 18, 21, 10, 32, 33, 40, 30, 2, 22, 11, 31, 16, 5, 25, 38, 52, 46, 3, 54, 45], 'cur_cost': 240956.0}, {'tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}, {'tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}, {'tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}, {'tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}, {'tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}, {'tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}, {'tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}, {'tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}, {'tour': [14, 21, 56, 17, 50, 51, 28, 42, 53, 5, 4, 18, 22, 33, 26, 2, 48, 27, 7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54, 8, 31, 49, 52, 41, 1, 12, 38, 11, 3, 20, 37, 57, 39, 9, 0, 43, 58, 25, 13, 47, 19, 32, 6, 45, 46, 55, 24], 'cur_cost': 243790.0}, {'tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}, {'tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}]
2025-08-03 15:18:00,163 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 15:18:00,164 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 15:18:00,164 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}
2025-08-03 15:18:00,164 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 250874.00)
2025-08-03 15:18:00,165 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-03 15:18:00,165 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:00,165 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:00,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 264216.0
2025-08-03 15:18:00,231 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 15:18:00,231 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:00,232 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64)]
2025-08-03 15:18:00,237 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:00,238 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}, {'tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}, {'tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}, {'tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}, {'tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [0, 9, 6, 7, 1, 4, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24926.0}, {'tour': [0, 20, 6, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 5, 8, 7, 1, 2, 19, 18, 13, 22, 14, 21, 12, 11, 15, 17, 16, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34909.0}, {'tour': [0, 11, 23, 13, 18, 22, 14, 20, 16, 17, 15, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37116.0}, {'tour': [7, 21, 12, 8, 5, 29, 24, 26, 6, 32, 28, 25, 9, 0, 31, 45, 1, 23, 3, 54, 52, 18, 22, 30, 2, 10, 33, 38, 44, 27, 11, 34, 39, 35, 58, 49, 4, 14, 57, 47, 48, 56, 51, 17, 20, 16, 55, 19, 46, 40, 36, 43, 37, 50, 13, 53, 42, 41, 15], 'cur_cost': 165485.0}, {'tour': [56, 49, 19, 9, 34, 7, 8, 2, 0, 17, 28, 23, 44, 37, 4, 13, 10, 3, 50, 14, 32, 36, 35, 1, 29, 31, 39, 46, 24, 25, 38, 48, 58, 18, 26, 5, 11, 20, 6, 33, 27, 12, 16, 30, 15, 54, 52, 55, 51, 47, 53, 57, 21, 22, 42, 40, 45, 41, 43], 'cur_cost': 155447.0}, {'tour': [53, 18, 13, 27, 31, 30, 34, 38, 6, 11, 24, 23, 46, 33, 8, 12, 51, 20, 43, 4, 41, 0, 1, 48, 5, 58, 54, 55, 57, 47, 50, 22, 35, 26, 49, 21, 44, 9, 19, 45, 37, 29, 52, 42, 32, 16, 3, 7, 39, 56, 36, 2, 40, 10, 28, 17, 14, 15, 25], 'cur_cost': 223121.0}, {'tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}, {'tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}, {'tour': [14, 21, 56, 17, 50, 51, 28, 42, 53, 5, 4, 18, 22, 33, 26, 2, 48, 27, 7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54, 8, 31, 49, 52, 41, 1, 12, 38, 11, 3, 20, 37, 57, 39, 9, 0, 43, 58, 25, 13, 47, 19, 32, 6, 45, 46, 55, 24], 'cur_cost': 243790.0}, {'tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}, {'tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}]
2025-08-03 15:18:00,243 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 15:18:00,243 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 15:18:00,243 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}
2025-08-03 15:18:00,244 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 264216.00)
2025-08-03 15:18:00,244 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:18:00,244 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:18:00,244 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,250 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:00,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 171350.0, 路径长度: 59
2025-08-03 15:18:00,252 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}
2025-08-03 15:18:00,252 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 171350.00)
2025-08-03 15:18:00,253 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-03 15:18:00,253 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-03 15:18:00,253 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,264 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:00,264 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,265 - ExplorationExpert - INFO - 探索路径生成完成，成本: 138918.0, 路径长度: 59
2025-08-03 15:18:00,265 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}
2025-08-03 15:18:00,266 - experts.management.collaboration_manager - INFO - 个体 10 保留原路径 (成本: 138918.00)
2025-08-03 15:18:00,266 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:18:00,266 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:18:00,266 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,270 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:00,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,272 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31611.0, 路径长度: 59
2025-08-03 15:18:00,272 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}
2025-08-03 15:18:00,273 - experts.management.collaboration_manager - INFO - 个体 11 保留原路径 (成本: 31611.00)
2025-08-03 15:18:00,273 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:18:00,273 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:18:00,274 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,285 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:00,286 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154016.0, 路径长度: 59
2025-08-03 15:18:00,287 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}
2025-08-03 15:18:00,288 - experts.management.collaboration_manager - INFO - 个体 12 保留原路径 (成本: 154016.00)
2025-08-03 15:18:00,289 - experts.management.collaboration_manager - INFO - 为个体 13 生成探索路径
2025-08-03 15:18:00,289 - ExplorationExpert - INFO - 开始为个体 13 生成探索路径（算法实现）
2025-08-03 15:18:00,290 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,294 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:00,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32776.0, 路径长度: 59
2025-08-03 15:18:00,295 - experts.management.collaboration_manager - INFO - 个体 13 探索路径生成报告: {'new_tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}
2025-08-03 15:18:00,295 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 32776.00)
2025-08-03 15:18:00,295 - experts.management.collaboration_manager - INFO - 为个体 14 生成利用路径
2025-08-03 15:18:00,296 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:00,296 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:00,297 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 14 处的路径，新成本: 253341.0
2025-08-03 15:18:00,378 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 15:18:00,378 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:00,378 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:00,387 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:00,388 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}, {'tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}, {'tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}, {'tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}, {'tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': array([48, 52, 38, 47, 58, 33, 22,  4, 26, 56,  1, 39, 28, 21, 34, 24,  5,
       11,  8, 41, 15, 27, 17, 20, 55,  3, 32, 53, 16, 49, 18,  9, 25, 23,
        7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31,  0, 51,  6,
       43,  2, 42, 19, 12, 54, 50, 40], dtype=int64), 'cur_cost': 253341.0}, {'tour': [25, 0, 31, 44, 26, 10, 3, 13, 4, 21, 27, 6, 32, 29, 40, 38, 42, 35, 24, 46, 43, 28, 23, 36, 30, 39, 54, 47, 58, 48, 5, 49, 53, 8, 33, 12, 1, 18, 20, 14, 2, 17, 9, 11, 15, 57, 7, 50, 52, 19, 34, 16, 22, 55, 51, 37, 41, 45, 56], 'cur_cost': 180968.0}, {'tour': [0, 4, 1, 12, 11, 15, 17, 16, 21, 22, 13, 18, 19, 14, 20, 6, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27097.0}, {'tour': [14, 21, 56, 17, 50, 51, 28, 42, 53, 5, 4, 18, 22, 33, 26, 2, 48, 27, 7, 15, 16, 40, 10, 30, 44, 36, 29, 34, 23, 35, 54, 8, 31, 49, 52, 41, 1, 12, 38, 11, 3, 20, 37, 57, 39, 9, 0, 43, 58, 25, 13, 47, 19, 32, 6, 45, 46, 55, 24], 'cur_cost': 243790.0}, {'tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}, {'tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}]
2025-08-03 15:18:00,393 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:18:00,393 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-03 15:18:00,394 - experts.management.collaboration_manager - INFO - 个体 14 利用路径生成报告: {'new_tour': array([48, 52, 38, 47, 58, 33, 22,  4, 26, 56,  1, 39, 28, 21, 34, 24,  5,
       11,  8, 41, 15, 27, 17, 20, 55,  3, 32, 53, 16, 49, 18,  9, 25, 23,
        7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31,  0, 51,  6,
       43,  2, 42, 19, 12, 54, 50, 40], dtype=int64), 'cur_cost': 253341.0}
2025-08-03 15:18:00,394 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 253341.00)
2025-08-03 15:18:00,394 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:18:00,395 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:18:00,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,399 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:00,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,399 - ExplorationExpert - INFO - 探索路径生成完成，成本: 238742.0, 路径长度: 59
2025-08-03 15:18:00,399 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}
2025-08-03 15:18:00,400 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 238742.00)
2025-08-03 15:18:00,400 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:18:00,400 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:18:00,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,403 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:00,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34886.0, 路径长度: 59
2025-08-03 15:18:00,405 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}
2025-08-03 15:18:00,406 - experts.management.collaboration_manager - INFO - 个体 16 保留原路径 (成本: 34886.00)
2025-08-03 15:18:00,406 - experts.management.collaboration_manager - INFO - 为个体 17 生成利用路径
2025-08-03 15:18:00,406 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:00,406 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:00,407 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 17 处的路径，新成本: 266207.0
2025-08-03 15:18:00,508 - ExploitationExpert - INFO - res_population_num: 14
2025-08-03 15:18:00,508 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:00,509 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:00,524 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:00,525 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}, {'tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}, {'tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}, {'tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}, {'tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': array([48, 52, 38, 47, 58, 33, 22,  4, 26, 56,  1, 39, 28, 21, 34, 24,  5,
       11,  8, 41, 15, 27, 17, 20, 55,  3, 32, 53, 16, 49, 18,  9, 25, 23,
        7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31,  0, 51,  6,
       43,  2, 42, 19, 12, 54, 50, 40], dtype=int64), 'cur_cost': 253341.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': array([11, 38, 20, 12,  1, 21, 45, 43, 23, 58, 47, 56,  9, 13, 16, 37, 24,
       32, 55, 30, 52, 31, 57, 18, 40,  6, 49,  8, 50, 25, 34, 35, 27, 19,
       39,  3, 36, 48,  2, 26, 44, 51, 46,  7, 42,  4, 33, 22, 28, 10, 53,
       17, 15, 41, 29, 14, 54,  0,  5], dtype=int64), 'cur_cost': 266207.0}, {'tour': [53, 9, 29, 0, 7, 11, 18, 21, 4, 25, 39, 45, 38, 3, 26, 27, 31, 5, 54, 8, 14, 30, 32, 35, 23, 1, 6, 2, 46, 48, 50, 12, 17, 13, 24, 42, 10, 20, 22, 57, 56, 55, 49, 51, 19, 28, 34, 44, 40, 43, 37, 33, 41, 58, 47, 16, 15, 52, 36], 'cur_cost': 161112.0}, {'tour': [0, 16, 21, 17, 15, 11, 12, 19, 18, 13, 22, 14, 20, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27136.0}]
2025-08-03 15:18:00,533 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 15:18:00,534 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-03 15:18:00,535 - experts.management.collaboration_manager - INFO - 个体 17 利用路径生成报告: {'new_tour': array([11, 38, 20, 12,  1, 21, 45, 43, 23, 58, 47, 56,  9, 13, 16, 37, 24,
       32, 55, 30, 52, 31, 57, 18, 40,  6, 49,  8, 50, 25, 34, 35, 27, 19,
       39,  3, 36, 48,  2, 26, 44, 51, 46,  7, 42,  4, 33, 22, 28, 10, 53,
       17, 15, 41, 29, 14, 54,  0,  5], dtype=int64), 'cur_cost': 266207.0}
2025-08-03 15:18:00,536 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 266207.00)
2025-08-03 15:18:00,546 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:18:00,548 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:18:00,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,573 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:00,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,574 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162188.0, 路径长度: 59
2025-08-03 15:18:00,575 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}
2025-08-03 15:18:00,576 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 162188.00)
2025-08-03 15:18:00,576 - experts.management.collaboration_manager - INFO - 为个体 19 生成探索路径
2025-08-03 15:18:00,577 - ExplorationExpert - INFO - 开始为个体 19 生成探索路径（算法实现）
2025-08-03 15:18:00,577 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:00,588 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:00,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:00,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34046.0, 路径长度: 59
2025-08-03 15:18:00,593 - experts.management.collaboration_manager - INFO - 个体 19 探索路径生成报告: {'new_tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}
2025-08-03 15:18:00,594 - experts.management.collaboration_manager - INFO - 个体 19 保留原路径 (成本: 34046.00)
2025-08-03 15:18:00,595 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 9, 'rejected': 11, 'elite_protected': 4}
2025-08-03 15:18:00,595 - experts.management.collaboration_manager - INFO - 接受率: 9/20 (45.0%)
2025-08-03 15:18:00,597 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 2, 15, 17, 16, 11, 12, 21, 18, 13, 14, 20, 19, 6, 1, 7, 8, 5, 3, 4, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34706.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 10, 7, 1, 6, 4, 9, 3, 5, 8, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32620.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 5, 11, 12, 21, 17, 15, 16, 20, 14, 13, 18, 22, 19, 6, 4, 9, 3, 2, 8, 7, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27113.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 5, 18, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34671.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [27, 14, 17, 24, 32, 12, 42, 30, 51, 36, 53, 21, 4, 22, 23, 33, 29, 9, 8, 39, 55, 2, 47, 1, 7, 44, 43, 26, 57, 52, 6, 10, 38, 18, 19, 58, 16, 48, 50, 46, 37, 54, 34, 0, 40, 49, 3, 13, 25, 15, 20, 5, 28, 41, 11, 45, 31, 35, 56], 'cur_cost': 239523.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 56,  9, 36, 33,  5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48,
       55,  0, 44,  7, 32, 52, 30, 23, 45, 25, 13,  1, 14, 43, 21, 46, 29,
        4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41,  3, 38, 35, 47, 34, 17,
        8,  2, 10, 42, 57, 24,  6, 39], dtype=int64), 'cur_cost': 269452.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}}, {'individual': 10, 'strategy': 'explore', 'path_data': {'new_tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}}, {'individual': 13, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}}, {'individual': 14, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 52, 38, 47, 58, 33, 22,  4, 26, 56,  1, 39, 28, 21, 34, 24,  5,
       11,  8, 41, 15, 27, 17, 20, 55,  3, 32, 53, 16, 49, 18,  9, 25, 23,
        7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31,  0, 51,  6,
       43,  2, 42, 19, 12, 54, 50, 40], dtype=int64), 'cur_cost': 253341.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}}, {'individual': 17, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 38, 20, 12,  1, 21, 45, 43, 23, 58, 47, 56,  9, 13, 16, 37, 24,
       32, 55, 30, 52, 31, 57, 18, 40,  6, 49,  8, 50, 25, 34, 35, 27, 19,
       39,  3, 36, 48,  2, 26, 44, 51, 46,  7, 42,  4, 33, 22, 28, 10, 53,
       17, 15, 41, 29, 14, 54,  0,  5], dtype=int64), 'cur_cost': 266207.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}}, {'individual': 19, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}}]
2025-08-03 15:18:00,601 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:18:00,602 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:18:00,624 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=27113.000, 多样性=0.949
2025-08-03 15:18:00,625 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-03 15:18:00,626 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-03 15:18:00,627 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:18:00,629 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.08278644110647011, 'best_improvement': -0.0877397095402391}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009031657355679663}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 24451, 'new_best_cost': 24451, 'quality_improvement': 0.0, 'old_diversity': 0.8785621158502513, 'new_diversity': 0.8785621158502513, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 15:18:00,632 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-03 15:18:00,633 - __main__ - INFO - composite11_59 开始进化第 3 代
2025-08-03 15:18:00,633 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-03 15:18:00,634 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:18:00,635 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=27113.000, 多样性=0.949
2025-08-03 15:18:00,636 - PathExpert - INFO - 开始路径结构分析
2025-08-03 15:18:00,647 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.949
2025-08-03 15:18:00,653 - EliteExpert - INFO - 开始精英解分析
2025-08-03 15:18:00,667 - EliteExpert - INFO - 精英解分析完成: 精英解数量=14, 多样性=0.879
2025-08-03 15:18:00,669 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/3)
2025-08-03 15:18:00,670 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 15:18:00,670 - LandscapeExpert - INFO - 添加精英解数据: 14个精英解
2025-08-03 15:18:00,671 - LandscapeExpert - INFO - 数据提取成功: 34个路径, 34个适应度值
2025-08-03 15:18:00,860 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.324, 适应度梯度: -47702.641, 聚类评分: 0.000, 覆盖率: 0.007, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 15:18:00,861 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-03 15:18:00,861 - LandscapeExpert - INFO - 提取到 14 个精英解
2025-08-03 15:18:00,873 - visualization.landscape_visualizer - INFO - 已添加 14 个精英解标记
2025-08-03 15:18:00,960 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250803_151800.html
2025-08-03 15:18:01,018 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250803_151800.html
2025-08-03 15:18:01,019 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-03 15:18:01,020 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-03 15:18:01,020 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3502秒
2025-08-03 15:18:01,021 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3235294117647059, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -47702.64117647059, 'local_optima_density': 0.3235294117647059, 'gradient_variance': 8924937215.961836, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0074, 'fitness_entropy': 0.6663019638699464, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -47702.641)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.007)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 3, 'progress': 0.6666666666666666}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754205480.8619084, 'performance_metrics': {}}}
2025-08-03 15:18:01,024 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 15:18:01,024 - StrategyExpert - INFO - 开始策略分配分析
2025-08-03 15:18:01,025 - StrategyExpert - INFO - 动态计算的探索比例: 0.700
2025-08-03 15:18:01,028 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:18:01,029 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:18:01,029 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.700
- 探索个体数量: 14
- 利用个体数量: 6
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:18:01,030 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 15:18:01,030 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-03 15:18:01,031 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.700
- 探索个体数量: 14
- 利用个体数量: 6
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 0项特征
2025-08-03 15:18:01,032 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 15:18:01,032 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2, 11, 13} (总数: 4)
2025-08-03 15:18:01,033 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 15:18:01,033 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 15:18:01,033 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,037 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,037 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,038 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24939.0, 路径长度: 59
2025-08-03 15:18:01,039 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}
2025-08-03 15:18:01,040 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 24939.00)
2025-08-03 15:18:01,041 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-03 15:18:01,041 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-03 15:18:01,041 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,047 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,052 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24993.0, 路径长度: 59
2025-08-03 15:18:01,053 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}
2025-08-03 15:18:01,054 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 24993.00)
2025-08-03 15:18:01,055 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 15:18:01,056 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 15:18:01,056 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,062 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27282.0, 路径长度: 59
2025-08-03 15:18:01,064 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}
2025-08-03 15:18:01,065 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 27282.00)
2025-08-03 15:18:01,065 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 15:18:01,066 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 15:18:01,066 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,076 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:01,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,077 - ExplorationExpert - INFO - 探索路径生成完成，成本: 132792.0, 路径长度: 59
2025-08-03 15:18:01,077 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}
2025-08-03 15:18:01,078 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 132792.00)
2025-08-03 15:18:01,078 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 15:18:01,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,079 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,079 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 266337.0
2025-08-03 15:18:01,171 - ExploitationExpert - INFO - res_population_num: 15
2025-08-03 15:18:01,172 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451]
2025-08-03 15:18:01,173 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:01,183 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,184 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [0, 18, 10, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 34510.0}, {'tour': [15, 56, 9, 36, 33, 5, 51, 28, 11, 50, 49, 31, 37, 22, 26, 54, 48, 55, 0, 44, 7, 32, 52, 30, 23, 45, 25, 13, 1, 14, 43, 21, 46, 29, 4, 58, 19, 18, 27, 20, 40, 53, 16, 12, 41, 3, 38, 35, 47, 34, 17, 8, 2, 10, 42, 57, 24, 6, 39], 'cur_cost': 269452.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': [48, 52, 38, 47, 58, 33, 22, 4, 26, 56, 1, 39, 28, 21, 34, 24, 5, 11, 8, 41, 15, 27, 17, 20, 55, 3, 32, 53, 16, 49, 18, 9, 25, 23, 7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31, 0, 51, 6, 43, 2, 42, 19, 12, 54, 50, 40], 'cur_cost': 253341.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': [11, 38, 20, 12, 1, 21, 45, 43, 23, 58, 47, 56, 9, 13, 16, 37, 24, 32, 55, 30, 52, 31, 57, 18, 40, 6, 49, 8, 50, 25, 34, 35, 27, 19, 39, 3, 36, 48, 2, 26, 44, 51, 46, 7, 42, 4, 33, 22, 28, 10, 53, 17, 15, 41, 29, 14, 54, 0, 5], 'cur_cost': 266207.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,189 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 15:18:01,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-03 15:18:01,191 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}
2025-08-03 15:18:01,191 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 266337.00)
2025-08-03 15:18:01,192 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 15:18:01,192 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 15:18:01,192 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,202 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:01,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,203 - ExplorationExpert - INFO - 探索路径生成完成，成本: 195631.0, 路径长度: 59
2025-08-03 15:18:01,203 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}
2025-08-03 15:18:01,203 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 195631.00)
2025-08-03 15:18:01,204 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-03 15:18:01,204 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,204 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,205 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 290182.0
2025-08-03 15:18:01,293 - ExploitationExpert - INFO - res_population_num: 18
2025-08-03 15:18:01,293 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451]
2025-08-03 15:18:01,294 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-03 15:18:01,303 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,303 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}, {'tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}, {'tour': array([ 3, 34,  5, 43, 53,  1, 36, 35, 15, 56, 30, 20, 16, 27, 24, 23, 33,
       58, 14, 47, 44,  9, 49, 28, 29, 26, 19, 21, 41, 55, 52, 12, 40, 51,
       31,  7,  8, 10,  4, 50, 37, 54, 42, 17, 45, 25, 22, 38,  6, 18, 11,
        0, 48, 32, 13, 39,  2, 46, 57], dtype=int64), 'cur_cost': 250874.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': [48, 52, 38, 47, 58, 33, 22, 4, 26, 56, 1, 39, 28, 21, 34, 24, 5, 11, 8, 41, 15, 27, 17, 20, 55, 3, 32, 53, 16, 49, 18, 9, 25, 23, 7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31, 0, 51, 6, 43, 2, 42, 19, 12, 54, 50, 40], 'cur_cost': 253341.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': [11, 38, 20, 12, 1, 21, 45, 43, 23, 58, 47, 56, 9, 13, 16, 37, 24, 32, 55, 30, 52, 31, 57, 18, 40, 6, 49, 8, 50, 25, 34, 35, 27, 19, 39, 3, 36, 48, 2, 26, 44, 51, 46, 7, 42, 4, 33, 22, 28, 10, 53, 17, 15, 41, 29, 14, 54, 0, 5], 'cur_cost': 266207.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,308 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 15:18:01,308 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-03 15:18:01,309 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}
2025-08-03 15:18:01,309 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 290182.00)
2025-08-03 15:18:01,309 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 15:18:01,310 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,310 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,310 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 253846.0
2025-08-03 15:18:01,406 - ExploitationExpert - INFO - res_population_num: 18
2025-08-03 15:18:01,406 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451]
2025-08-03 15:18:01,407 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-08-03 15:18:01,421 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,422 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}, {'tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}, {'tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}, {'tour': array([36, 27, 46, 30, 35, 20, 44, 14,  5, 17,  1, 38, 49, 28, 13, 37, 45,
       53, 58, 21, 15,  8, 51, 54, 40, 26,  6, 43, 12, 22,  3, 50, 55, 16,
       25,  2, 10, 57, 23, 31,  4, 18, 34, 52, 29, 19,  9, 56, 41, 11, 48,
       32, 42, 47, 24,  7,  0, 39, 33], dtype=int64), 'cur_cost': 264216.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': [48, 52, 38, 47, 58, 33, 22, 4, 26, 56, 1, 39, 28, 21, 34, 24, 5, 11, 8, 41, 15, 27, 17, 20, 55, 3, 32, 53, 16, 49, 18, 9, 25, 23, 7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31, 0, 51, 6, 43, 2, 42, 19, 12, 54, 50, 40], 'cur_cost': 253341.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': [11, 38, 20, 12, 1, 21, 45, 43, 23, 58, 47, 56, 9, 13, 16, 37, 24, 32, 55, 30, 52, 31, 57, 18, 40, 6, 49, 8, 50, 25, 34, 35, 27, 19, 39, 3, 36, 48, 2, 26, 44, 51, 46, 7, 42, 4, 33, 22, 28, 10, 53, 17, 15, 41, 29, 14, 54, 0, 5], 'cur_cost': 266207.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,427 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-03 15:18:01,427 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-03 15:18:01,428 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}
2025-08-03 15:18:01,428 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 253846.00)
2025-08-03 15:18:01,429 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-03 15:18:01,429 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,429 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,429 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 231266.0
2025-08-03 15:18:01,557 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 15:18:01,559 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:01,560 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:01,572 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,572 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}, {'tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}, {'tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}, {'tour': array([55, 20, 16, 58, 29, 30,  1, 41, 53,  7, 15, 33, 44,  2, 42,  8, 35,
       50,  3, 27, 13, 12, 31, 38, 43,  6, 56, 57, 21, 48, 22, 52,  5, 18,
       17, 25,  9, 28, 23,  4, 14, 51, 36, 26, 32, 11, 46, 24, 10, 39, 54,
       47, 19, 40,  0, 45, 37, 34, 49], dtype=int64), 'cur_cost': 231266.0}, {'tour': [27, 11, 12, 21, 4, 18, 13, 30, 19, 32, 29, 8, 39, 3, 36, 35, 44, 25, 43, 42, 41, 40, 53, 58, 55, 57, 9, 0, 48, 28, 47, 5, 24, 1, 16, 31, 26, 6, 10, 2, 54, 49, 7, 52, 45, 38, 46, 15, 22, 20, 14, 34, 37, 33, 56, 17, 51, 50, 23], 'cur_cost': 171350.0}, {'tour': [23, 42, 30, 24, 32, 10, 28, 29, 35, 6, 1, 31, 9, 12, 13, 4, 49, 53, 54, 57, 0, 18, 27, 45, 43, 25, 26, 8, 2, 34, 11, 22, 15, 5, 3, 17, 50, 21, 20, 19, 55, 58, 44, 36, 7, 51, 52, 47, 56, 48, 38, 46, 40, 39, 37, 41, 33, 16, 14], 'cur_cost': 138918.0}, {'tour': [0, 24, 10, 2, 3, 5, 4, 9, 1, 7, 8, 6, 33, 31, 27, 34, 23, 25, 32, 29, 30, 26, 28, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 31611.0}, {'tour': [20, 15, 2, 49, 4, 3, 10, 24, 0, 11, 12, 30, 46, 25, 6, 31, 36, 45, 27, 40, 37, 35, 23, 34, 19, 9, 21, 32, 28, 5, 8, 33, 18, 14, 51, 55, 54, 53, 57, 52, 47, 22, 13, 56, 17, 7, 16, 58, 50, 38, 43, 41, 39, 44, 42, 26, 29, 1, 48], 'cur_cost': 154016.0}, {'tour': [0, 14, 21, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 20, 16, 17, 15, 11, 12, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32776.0}, {'tour': [48, 52, 38, 47, 58, 33, 22, 4, 26, 56, 1, 39, 28, 21, 34, 24, 5, 11, 8, 41, 15, 27, 17, 20, 55, 3, 32, 53, 16, 49, 18, 9, 25, 23, 7, 37, 14, 46, 29, 30, 45, 35, 10, 57, 44, 13, 36, 31, 0, 51, 6, 43, 2, 42, 19, 12, 54, 50, 40], 'cur_cost': 253341.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': [11, 38, 20, 12, 1, 21, 45, 43, 23, 58, 47, 56, 9, 13, 16, 37, 24, 32, 55, 30, 52, 31, 57, 18, 40, 6, 49, 8, 50, 25, 34, 35, 27, 19, 39, 3, 36, 48, 2, 26, 44, 51, 46, 7, 42, 4, 33, 22, 28, 10, 53, 17, 15, 41, 29, 14, 54, 0, 5], 'cur_cost': 266207.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,577 - ExploitationExpert - INFO - 局部搜索耗时: 0.15秒
2025-08-03 15:18:01,577 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-03 15:18:01,578 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([55, 20, 16, 58, 29, 30,  1, 41, 53,  7, 15, 33, 44,  2, 42,  8, 35,
       50,  3, 27, 13, 12, 31, 38, 43,  6, 56, 57, 21, 48, 22, 52,  5, 18,
       17, 25,  9, 28, 23,  4, 14, 51, 36, 26, 32, 11, 46, 24, 10, 39, 54,
       47, 19, 40,  0, 45, 37, 34, 49], dtype=int64), 'cur_cost': 231266.0}
2025-08-03 15:18:01,578 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 231266.00)
2025-08-03 15:18:01,578 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 15:18:01,578 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 15:18:01,579 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,586 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:01,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,588 - ExplorationExpert - INFO - 探索路径生成完成，成本: 247987.0, 路径长度: 59
2025-08-03 15:18:01,589 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [9, 25, 8, 35, 29, 3, 7, 2, 10, 30, 31, 33, 49, 12, 22, 54, 36, 38, 4, 5, 32, 19, 43, 34, 50, 51, 46, 55, 45, 1, 58, 15, 27, 18, 41, 44, 16, 37, 26, 11, 6, 53, 47, 52, 20, 56, 0, 48, 23, 42, 28, 21, 14, 40, 24, 57, 17, 39, 13], 'cur_cost': 247987.0}
2025-08-03 15:18:01,590 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 247987.00)
2025-08-03 15:18:01,591 - experts.management.collaboration_manager - INFO - 为个体 10 生成探索路径
2025-08-03 15:18:01,591 - ExplorationExpert - INFO - 开始为个体 10 生成探索路径（算法实现）
2025-08-03 15:18:01,592 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,603 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:01,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170384.0, 路径长度: 59
2025-08-03 15:18:01,604 - experts.management.collaboration_manager - INFO - 个体 10 探索路径生成报告: {'new_tour': [12, 5, 33, 7, 28, 2, 3, 0, 6, 34, 23, 35, 30, 1, 32, 9, 8, 29, 27, 10, 24, 44, 46, 26, 37, 53, 50, 51, 18, 19, 25, 41, 45, 40, 58, 4, 54, 14, 11, 13, 16, 31, 38, 49, 47, 21, 15, 55, 57, 52, 22, 56, 20, 17, 48, 36, 43, 42, 39], 'cur_cost': 170384.0}
2025-08-03 15:18:01,606 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 170384.00)
2025-08-03 15:18:01,606 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 15:18:01,606 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 15:18:01,607 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,612 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:01,612 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,613 - ExplorationExpert - INFO - 探索路径生成完成，成本: 230641.0, 路径长度: 59
2025-08-03 15:18:01,613 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 8, 37, 10, 5, 33, 6, 12, 22, 50, 17, 26, 24, 1, 32, 14, 16, 31, 43, 34, 41, 46, 52, 55, 56, 58, 45, 21, 51, 18, 30, 36, 2, 49, 4, 38, 20, 27, 35, 28, 47, 23, 57, 53, 13, 25, 39, 54, 40, 3, 42, 9, 11, 15, 19, 48, 44, 29, 7], 'cur_cost': 230641.0}
2025-08-03 15:18:01,615 - experts.management.collaboration_manager - INFO - 个体 11 保留原路径 (成本: 230641.00)
2025-08-03 15:18:01,618 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 15:18:01,619 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 15:18:01,620 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,626 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,628 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,628 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32617.0, 路径长度: 59
2025-08-03 15:18:01,629 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 5, 12, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 2, 8, 7, 1, 6, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32617.0}
2025-08-03 15:18:01,629 - experts.management.collaboration_manager - INFO - 个体 12 保留原路径 (成本: 32617.00)
2025-08-03 15:18:01,629 - experts.management.collaboration_manager - INFO - 为个体 13 生成探索路径
2025-08-03 15:18:01,630 - ExplorationExpert - INFO - 开始为个体 13 生成探索路径（算法实现）
2025-08-03 15:18:01,630 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,633 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:01,634 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,634 - ExplorationExpert - INFO - 探索路径生成完成，成本: 243911.0, 路径长度: 59
2025-08-03 15:18:01,634 - experts.management.collaboration_manager - INFO - 个体 13 探索路径生成报告: {'new_tour': [13, 7, 1, 9, 44, 33, 28, 26, 6, 49, 12, 54, 10, 38, 55, 34, 24, 11, 32, 14, 3, 16, 19, 40, 51, 56, 45, 20, 25, 58, 31, 22, 27, 18, 36, 30, 35, 4, 47, 23, 53, 43, 41, 42, 37, 48, 50, 0, 2, 8, 29, 21, 46, 17, 52, 57, 15, 5, 39], 'cur_cost': 243911.0}
2025-08-03 15:18:01,635 - experts.management.collaboration_manager - INFO - 个体 13 保留原路径 (成本: 243911.00)
2025-08-03 15:18:01,635 - experts.management.collaboration_manager - INFO - 为个体 14 生成利用路径
2025-08-03 15:18:01,635 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,636 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,636 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 14 处的路径，新成本: 255753.0
2025-08-03 15:18:01,754 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 15:18:01,754 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:01,755 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:01,767 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,767 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}, {'tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}, {'tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}, {'tour': array([55, 20, 16, 58, 29, 30,  1, 41, 53,  7, 15, 33, 44,  2, 42,  8, 35,
       50,  3, 27, 13, 12, 31, 38, 43,  6, 56, 57, 21, 48, 22, 52,  5, 18,
       17, 25,  9, 28, 23,  4, 14, 51, 36, 26, 32, 11, 46, 24, 10, 39, 54,
       47, 19, 40,  0, 45, 37, 34, 49], dtype=int64), 'cur_cost': 231266.0}, {'tour': [9, 25, 8, 35, 29, 3, 7, 2, 10, 30, 31, 33, 49, 12, 22, 54, 36, 38, 4, 5, 32, 19, 43, 34, 50, 51, 46, 55, 45, 1, 58, 15, 27, 18, 41, 44, 16, 37, 26, 11, 6, 53, 47, 52, 20, 56, 0, 48, 23, 42, 28, 21, 14, 40, 24, 57, 17, 39, 13], 'cur_cost': 247987.0}, {'tour': [12, 5, 33, 7, 28, 2, 3, 0, 6, 34, 23, 35, 30, 1, 32, 9, 8, 29, 27, 10, 24, 44, 46, 26, 37, 53, 50, 51, 18, 19, 25, 41, 45, 40, 58, 4, 54, 14, 11, 13, 16, 31, 38, 49, 47, 21, 15, 55, 57, 52, 22, 56, 20, 17, 48, 36, 43, 42, 39], 'cur_cost': 170384.0}, {'tour': [0, 8, 37, 10, 5, 33, 6, 12, 22, 50, 17, 26, 24, 1, 32, 14, 16, 31, 43, 34, 41, 46, 52, 55, 56, 58, 45, 21, 51, 18, 30, 36, 2, 49, 4, 38, 20, 27, 35, 28, 47, 23, 57, 53, 13, 25, 39, 54, 40, 3, 42, 9, 11, 15, 19, 48, 44, 29, 7], 'cur_cost': 230641.0}, {'tour': [0, 5, 12, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 2, 8, 7, 1, 6, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32617.0}, {'tour': [13, 7, 1, 9, 44, 33, 28, 26, 6, 49, 12, 54, 10, 38, 55, 34, 24, 11, 32, 14, 3, 16, 19, 40, 51, 56, 45, 20, 25, 58, 31, 22, 27, 18, 36, 30, 35, 4, 47, 23, 53, 43, 41, 42, 37, 48, 50, 0, 2, 8, 29, 21, 46, 17, 52, 57, 15, 5, 39], 'cur_cost': 243911.0}, {'tour': array([15, 14, 32, 45, 11, 42, 33, 34,  8,  7, 28,  9, 50, 57, 56, 13, 27,
        0, 17, 58, 53, 19, 29, 49,  6, 48, 23, 38, 52, 37, 43,  5, 12, 24,
       31, 39, 36, 16, 47, 35,  4, 26, 22, 51, 18, 40, 20,  3, 41, 55, 25,
       46, 21, 54, 10,  1, 30,  2, 44], dtype=int64), 'cur_cost': 255753.0}, {'tour': [22, 12, 17, 6, 42, 51, 32, 53, 13, 26, 30, 33, 4, 29, 39, 34, 35, 46, 44, 57, 11, 38, 19, 16, 58, 47, 9, 0, 49, 41, 25, 15, 5, 1, 24, 27, 45, 31, 23, 8, 54, 28, 10, 21, 3, 20, 48, 2, 50, 40, 18, 55, 52, 7, 37, 14, 36, 56, 43], 'cur_cost': 238742.0}, {'tour': [0, 17, 1, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 23, 10, 9, 4, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34886.0}, {'tour': [11, 38, 20, 12, 1, 21, 45, 43, 23, 58, 47, 56, 9, 13, 16, 37, 24, 32, 55, 30, 52, 31, 57, 18, 40, 6, 49, 8, 50, 25, 34, 35, 27, 19, 39, 3, 36, 48, 2, 26, 44, 51, 46, 7, 42, 4, 33, 22, 28, 10, 53, 17, 15, 41, 29, 14, 54, 0, 5], 'cur_cost': 266207.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,771 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-03 15:18:01,772 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-03 15:18:01,773 - experts.management.collaboration_manager - INFO - 个体 14 利用路径生成报告: {'new_tour': array([15, 14, 32, 45, 11, 42, 33, 34,  8,  7, 28,  9, 50, 57, 56, 13, 27,
        0, 17, 58, 53, 19, 29, 49,  6, 48, 23, 38, 52, 37, 43,  5, 12, 24,
       31, 39, 36, 16, 47, 35,  4, 26, 22, 51, 18, 40, 20,  3, 41, 55, 25,
       46, 21, 54, 10,  1, 30,  2, 44], dtype=int64), 'cur_cost': 255753.0}
2025-08-03 15:18:01,773 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 255753.00)
2025-08-03 15:18:01,774 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 15:18:01,774 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 15:18:01,774 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,787 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-03 15:18:01,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 164821.0, 路径长度: 59
2025-08-03 15:18:01,791 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [47, 57, 18, 23, 32, 1, 5, 29, 34, 0, 24, 46, 6, 33, 3, 54, 48, 58, 19, 22, 27, 9, 28, 36, 10, 2, 30, 38, 45, 43, 26, 37, 41, 44, 25, 12, 13, 16, 8, 14, 15, 49, 51, 53, 50, 20, 31, 35, 4, 7, 17, 11, 55, 52, 21, 56, 42, 40, 39], 'cur_cost': 164821.0}
2025-08-03 15:18:01,792 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 164821.00)
2025-08-03 15:18:01,792 - experts.management.collaboration_manager - INFO - 为个体 16 生成探索路径
2025-08-03 15:18:01,792 - ExplorationExpert - INFO - 开始为个体 16 生成探索路径（算法实现）
2025-08-03 15:18:01,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,796 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27338.0, 路径长度: 59
2025-08-03 15:18:01,797 - experts.management.collaboration_manager - INFO - 个体 16 探索路径生成报告: {'new_tour': [0, 1, 4, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27338.0}
2025-08-03 15:18:01,797 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 27338.00)
2025-08-03 15:18:01,798 - experts.management.collaboration_manager - INFO - 为个体 17 生成利用路径
2025-08-03 15:18:01,798 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 15:18:01,798 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 15:18:01,798 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 17 处的路径，新成本: 234873.0
2025-08-03 15:18:01,901 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 15:18:01,901 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24475.0, 24499.0, 252967.0, 24451, 24451, 24451, 24451, 24451]
2025-08-03 15:18:01,901 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 22, 13, 14, 20, 16, 17, 15, 11,
       12, 21, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  4,  9,  6,  1,  7, 19, 18, 22, 21, 12, 11, 15,
       17, 16, 20, 14, 13, 51, 55, 47, 56, 57, 49, 54, 50, 52, 58, 48, 53,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 10], dtype=int64), array([ 0, 49, 31,  3, 28, 41, 45, 56,  8, 33, 17, 46, 39, 52, 54, 36, 23,
       21,  2, 50, 24, 47, 37, 38,  9, 26, 44, 40, 19, 13, 57,  4, 34, 48,
       14, 22, 29,  1, 25,  7, 58, 12,  5, 15, 16, 42, 32, 53, 35, 10, 55,
       27, 51, 18, 30, 20, 11, 43,  6], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64)]
2025-08-03 15:18:01,911 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 15:18:01,911 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}, {'tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}, {'tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}, {'tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}, {'tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}, {'tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}, {'tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}, {'tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}, {'tour': array([55, 20, 16, 58, 29, 30,  1, 41, 53,  7, 15, 33, 44,  2, 42,  8, 35,
       50,  3, 27, 13, 12, 31, 38, 43,  6, 56, 57, 21, 48, 22, 52,  5, 18,
       17, 25,  9, 28, 23,  4, 14, 51, 36, 26, 32, 11, 46, 24, 10, 39, 54,
       47, 19, 40,  0, 45, 37, 34, 49], dtype=int64), 'cur_cost': 231266.0}, {'tour': [9, 25, 8, 35, 29, 3, 7, 2, 10, 30, 31, 33, 49, 12, 22, 54, 36, 38, 4, 5, 32, 19, 43, 34, 50, 51, 46, 55, 45, 1, 58, 15, 27, 18, 41, 44, 16, 37, 26, 11, 6, 53, 47, 52, 20, 56, 0, 48, 23, 42, 28, 21, 14, 40, 24, 57, 17, 39, 13], 'cur_cost': 247987.0}, {'tour': [12, 5, 33, 7, 28, 2, 3, 0, 6, 34, 23, 35, 30, 1, 32, 9, 8, 29, 27, 10, 24, 44, 46, 26, 37, 53, 50, 51, 18, 19, 25, 41, 45, 40, 58, 4, 54, 14, 11, 13, 16, 31, 38, 49, 47, 21, 15, 55, 57, 52, 22, 56, 20, 17, 48, 36, 43, 42, 39], 'cur_cost': 170384.0}, {'tour': [0, 8, 37, 10, 5, 33, 6, 12, 22, 50, 17, 26, 24, 1, 32, 14, 16, 31, 43, 34, 41, 46, 52, 55, 56, 58, 45, 21, 51, 18, 30, 36, 2, 49, 4, 38, 20, 27, 35, 28, 47, 23, 57, 53, 13, 25, 39, 54, 40, 3, 42, 9, 11, 15, 19, 48, 44, 29, 7], 'cur_cost': 230641.0}, {'tour': [0, 5, 12, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 2, 8, 7, 1, 6, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32617.0}, {'tour': [13, 7, 1, 9, 44, 33, 28, 26, 6, 49, 12, 54, 10, 38, 55, 34, 24, 11, 32, 14, 3, 16, 19, 40, 51, 56, 45, 20, 25, 58, 31, 22, 27, 18, 36, 30, 35, 4, 47, 23, 53, 43, 41, 42, 37, 48, 50, 0, 2, 8, 29, 21, 46, 17, 52, 57, 15, 5, 39], 'cur_cost': 243911.0}, {'tour': array([15, 14, 32, 45, 11, 42, 33, 34,  8,  7, 28,  9, 50, 57, 56, 13, 27,
        0, 17, 58, 53, 19, 29, 49,  6, 48, 23, 38, 52, 37, 43,  5, 12, 24,
       31, 39, 36, 16, 47, 35,  4, 26, 22, 51, 18, 40, 20,  3, 41, 55, 25,
       46, 21, 54, 10,  1, 30,  2, 44], dtype=int64), 'cur_cost': 255753.0}, {'tour': [47, 57, 18, 23, 32, 1, 5, 29, 34, 0, 24, 46, 6, 33, 3, 54, 48, 58, 19, 22, 27, 9, 28, 36, 10, 2, 30, 38, 45, 43, 26, 37, 41, 44, 25, 12, 13, 16, 8, 14, 15, 49, 51, 53, 50, 20, 31, 35, 4, 7, 17, 11, 55, 52, 21, 56, 42, 40, 39], 'cur_cost': 164821.0}, {'tour': [0, 1, 4, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27338.0}, {'tour': array([37,  6, 39, 53,  2,  1,  5, 10, 19, 44, 24, 50, 11, 54, 40, 49, 43,
       35, 41, 31, 29, 21, 34, 32,  9, 14, 55, 47, 58, 33,  8,  7,  3, 12,
       30, 38, 27,  0, 13, 26, 46, 15, 17, 42, 51, 56, 36, 28, 22, 25, 45,
       48, 20, 23, 18, 52, 57,  4, 16], dtype=int64), 'cur_cost': 234873.0}, {'tour': [5, 27, 3, 25, 31, 28, 9, 26, 10, 8, 4, 34, 46, 7, 2, 22, 21, 49, 13, 6, 50, 51, 17, 54, 48, 53, 56, 19, 33, 43, 24, 29, 1, 11, 14, 47, 20, 23, 0, 35, 42, 37, 39, 45, 38, 30, 41, 36, 52, 55, 57, 58, 18, 16, 12, 32, 40, 44, 15], 'cur_cost': 162188.0}, {'tour': [0, 23, 4, 27, 34, 31, 33, 26, 28, 30, 25, 32, 29, 24, 10, 9, 3, 5, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 34046.0}]
2025-08-03 15:18:01,920 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-03 15:18:01,920 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-03 15:18:01,921 - experts.management.collaboration_manager - INFO - 个体 17 利用路径生成报告: {'new_tour': array([37,  6, 39, 53,  2,  1,  5, 10, 19, 44, 24, 50, 11, 54, 40, 49, 43,
       35, 41, 31, 29, 21, 34, 32,  9, 14, 55, 47, 58, 33,  8,  7,  3, 12,
       30, 38, 27,  0, 13, 26, 46, 15, 17, 42, 51, 56, 36, 28, 22, 25, 45,
       48, 20, 23, 18, 52, 57,  4, 16], dtype=int64), 'cur_cost': 234873.0}
2025-08-03 15:18:01,922 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 234873.00)
2025-08-03 15:18:01,923 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 15:18:01,923 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 15:18:01,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,927 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-03 15:18:01,927 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,927 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27147.0, 路径长度: 59
2025-08-03 15:18:01,928 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 9, 10, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27147.0}
2025-08-03 15:18:01,928 - experts.management.collaboration_manager - INFO - 个体 18 保留原路径 (成本: 27147.00)
2025-08-03 15:18:01,928 - experts.management.collaboration_manager - INFO - 为个体 19 生成探索路径
2025-08-03 15:18:01,929 - ExplorationExpert - INFO - 开始为个体 19 生成探索路径（算法实现）
2025-08-03 15:18:01,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 15:18:01,932 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-03 15:18:01,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 15:18:01,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 224117.0, 路径长度: 59
2025-08-03 15:18:01,933 - experts.management.collaboration_manager - INFO - 个体 19 探索路径生成报告: {'new_tour': [3, 7, 29, 9, 44, 37, 31, 49, 57, 30, 40, 38, 4, 17, 58, 14, 16, 41, 11, 34, 26, 51, 47, 52, 56, 55, 20, 1, 15, 12, 21, 39, 42, 27, 23, 46, 50, 36, 18, 13, 24, 54, 28, 22, 5, 35, 0, 48, 25, 45, 53, 32, 2, 43, 8, 19, 6, 33, 10], 'cur_cost': 224117.0}
2025-08-03 15:18:01,933 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 224117.00)
2025-08-03 15:18:01,934 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 9, 'rejected': 11, 'elite_protected': 4}
2025-08-03 15:18:01,934 - experts.management.collaboration_manager - INFO - 接受率: 9/20 (45.0%)
2025-08-03 15:18:01,936 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 7, 1, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24939.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 10, 8, 5, 3, 2, 9, 4, 1, 7, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24993.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 4, 8, 7, 1, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27282.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [9, 6, 25, 35, 37, 30, 2, 7, 4, 23, 28, 32, 31, 33, 39, 50, 57, 49, 53, 54, 10, 1, 22, 18, 17, 26, 24, 5, 29, 42, 0, 14, 3, 16, 19, 11, 34, 41, 40, 43, 46, 52, 13, 55, 48, 47, 58, 56, 21, 15, 12, 51, 44, 38, 45, 36, 8, 27, 20], 'cur_cost': 132792.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 53,  4, 19, 31, 47, 18, 43, 12, 10, 30, 27,  3, 39, 24, 32,  1,
       37, 17, 11, 35, 57, 28,  6, 58, 56, 40, 22, 44, 41,  0, 49, 13, 42,
        7, 14, 16, 33,  9, 25, 50, 21, 23, 52, 55, 54, 20, 36,  8, 46, 29,
       48,  2, 15, 45, 51, 26, 34, 38], dtype=int64), 'cur_cost': 266337.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [13, 7, 0, 29, 35, 10, 18, 33, 44, 46, 37, 39, 6, 8, 49, 2, 12, 22, 30, 40, 36, 25, 38, 4, 24, 34, 3, 14, 28, 11, 32, 15, 20, 5, 31, 43, 54, 52, 50, 51, 53, 48, 56, 21, 19, 1, 23, 45, 27, 17, 57, 9, 58, 16, 55, 42, 41, 26, 47], 'cur_cost': 195631.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 14, 56,  7,  4, 18, 35, 33, 48, 11, 39,  9, 25, 32, 44, 29, 37,
       40, 15,  5, 57, 42, 21, 26, 13, 54,  6, 28, 20,  0, 30, 17, 47, 41,
       16, 46, 52,  1,  3, 24, 38, 34, 12, 50, 19, 45, 49, 22, 23,  8, 27,
       55, 31, 58, 10, 36, 51, 43,  2], dtype=int64), 'cur_cost': 290182.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([58, 39, 12, 36, 23, 25, 41, 11, 14, 37, 21, 29, 20, 10, 16, 48, 54,
       44,  4, 38,  8,  0,  7, 15, 31, 51, 56, 43, 24, 53, 57, 33, 19, 32,
       26, 52, 27, 17, 42, 49, 35,  2, 46, 50, 28, 34, 13,  1, 30,  3,  6,
       40,  9,  5, 47, 55, 22, 18, 45], dtype=int64), 'cur_cost': 253846.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 20, 16, 58, 29, 30,  1, 41, 53,  7, 15, 33, 44,  2, 42,  8, 35,
       50,  3, 27, 13, 12, 31, 38, 43,  6, 56, 57, 21, 48, 22, 52,  5, 18,
       17, 25,  9, 28, 23,  4, 14, 51, 36, 26, 32, 11, 46, 24, 10, 39, 54,
       47, 19, 40,  0, 45, 37, 34, 49], dtype=int64), 'cur_cost': 231266.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [9, 25, 8, 35, 29, 3, 7, 2, 10, 30, 31, 33, 49, 12, 22, 54, 36, 38, 4, 5, 32, 19, 43, 34, 50, 51, 46, 55, 45, 1, 58, 15, 27, 18, 41, 44, 16, 37, 26, 11, 6, 53, 47, 52, 20, 56, 0, 48, 23, 42, 28, 21, 14, 40, 24, 57, 17, 39, 13], 'cur_cost': 247987.0}}, {'individual': 10, 'strategy': 'explore', 'path_data': {'new_tour': [12, 5, 33, 7, 28, 2, 3, 0, 6, 34, 23, 35, 30, 1, 32, 9, 8, 29, 27, 10, 24, 44, 46, 26, 37, 53, 50, 51, 18, 19, 25, 41, 45, 40, 58, 4, 54, 14, 11, 13, 16, 31, 38, 49, 47, 21, 15, 55, 57, 52, 22, 56, 20, 17, 48, 36, 43, 42, 39], 'cur_cost': 170384.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 37, 10, 5, 33, 6, 12, 22, 50, 17, 26, 24, 1, 32, 14, 16, 31, 43, 34, 41, 46, 52, 55, 56, 58, 45, 21, 51, 18, 30, 36, 2, 49, 4, 38, 20, 27, 35, 28, 47, 23, 57, 53, 13, 25, 39, 54, 40, 3, 42, 9, 11, 15, 19, 48, 44, 29, 7], 'cur_cost': 230641.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 24, 32, 25, 29, 30, 26, 28, 33, 31, 27, 34, 23, 10, 9, 4, 3, 2, 8, 7, 1, 6, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 32617.0}}, {'individual': 13, 'strategy': 'explore', 'path_data': {'new_tour': [13, 7, 1, 9, 44, 33, 28, 26, 6, 49, 12, 54, 10, 38, 55, 34, 24, 11, 32, 14, 3, 16, 19, 40, 51, 56, 45, 20, 25, 58, 31, 22, 27, 18, 36, 30, 35, 4, 47, 23, 53, 43, 41, 42, 37, 48, 50, 0, 2, 8, 29, 21, 46, 17, 52, 57, 15, 5, 39], 'cur_cost': 243911.0}}, {'individual': 14, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 14, 32, 45, 11, 42, 33, 34,  8,  7, 28,  9, 50, 57, 56, 13, 27,
        0, 17, 58, 53, 19, 29, 49,  6, 48, 23, 38, 52, 37, 43,  5, 12, 24,
       31, 39, 36, 16, 47, 35,  4, 26, 22, 51, 18, 40, 20,  3, 41, 55, 25,
       46, 21, 54, 10,  1, 30,  2, 44], dtype=int64), 'cur_cost': 255753.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [47, 57, 18, 23, 32, 1, 5, 29, 34, 0, 24, 46, 6, 33, 3, 54, 48, 58, 19, 22, 27, 9, 28, 36, 10, 2, 30, 38, 45, 43, 26, 37, 41, 44, 25, 12, 13, 16, 8, 14, 15, 49, 51, 53, 50, 20, 31, 35, 4, 7, 17, 11, 55, 52, 21, 56, 42, 40, 39], 'cur_cost': 164821.0}}, {'individual': 16, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 4, 25, 32, 29, 30, 26, 28, 33, 31, 27, 34, 23, 24, 10, 9, 3, 5, 8, 7, 6, 2, 19, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 49, 57, 55, 56, 47, 51, 54, 50, 53, 48, 58, 52, 43, 37, 36, 42, 46, 35, 39, 40, 45, 41, 38, 44], 'cur_cost': 27338.0}}, {'individual': 17, 'strategy': 'exploit', 'path_data': {'new_tour': array([37,  6, 39, 53,  2,  1,  5, 10, 19, 44, 24, 50, 11, 54, 40, 49, 43,
       35, 41, 31, 29, 21, 34, 32,  9, 14, 55, 47, 58, 33,  8,  7,  3, 12,
       30, 38, 27,  0, 13, 26, 46, 15, 17, 42, 51, 56, 36, 28, 22, 25, 45,
       48, 20, 23, 18, 52, 57,  4, 16], dtype=int64), 'cur_cost': 234873.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 10, 16, 17, 15, 11, 12, 21, 22, 13, 18, 19, 14, 20, 6, 1, 7, 8, 5, 3, 2, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27147.0}}, {'individual': 19, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 29, 9, 44, 37, 31, 49, 57, 30, 40, 38, 4, 17, 58, 14, 16, 41, 11, 34, 26, 51, 47, 52, 56, 55, 20, 1, 15, 12, 21, 39, 42, 27, 23, 46, 50, 36, 18, 13, 24, 54, 28, 22, 5, 35, 0, 48, 25, 45, 53, 32, 2, 43, 8, 19, 6, 33, 10], 'cur_cost': 224117.0}}]
2025-08-03 15:18:01,939 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 15:18:01,939 - StatsExpert - INFO - 开始统计分析
2025-08-03 15:18:01,954 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=24939.000, 多样性=0.966
2025-08-03 15:18:01,956 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-03 15:18:01,957 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-03 15:18:01,957 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 15:18:01,961 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07296762189851337, 'best_improvement': 0.08018293807398665}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.017758150897303468}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 19, 'new_count': 19, 'count_change': 0, 'old_best_cost': 24451, 'new_best_cost': 24451, 'quality_improvement': 0.0, 'old_diversity': 0.8602438299137675, 'new_diversity': 0.8602438299137675, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 15:18:01,966 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-03 15:18:02,028 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite11_59_solution.json
2025-08-03 15:18:02,029 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite11_59_20250803_151801.solution
2025-08-03 15:18:02,029 - __main__ - INFO - 实例 composite11_59 处理完成
