{"analysis_mode": "algorithmic", "fallback_to_llm": true, "algorithmic_config": {"k_neighbors": 5, "clustering_eps": 0.5, "clustering_min_samples": 3, "grid_size": 100, "window_size": 10, "cache_size": 1000, "max_history_length": 50}, "visualization_config": {"enabled": true, "grid_size": 50, "update_interval": 1000, "auto_save": true, "save_dir": "visualization_output", "port": 8050, "host": "127.0.0.1", "enhanced_sampling": {"enabled": true, "historical_data_fusion": {"enabled": true, "max_historical_points": 500, "elite_solution_retention": 50, "diversity_threshold": 0.1, "quality_threshold_percentile": 0.2}, "intermediate_solution_collection": {"enabled": true, "max_intermediate_points": 200, "collection_interval": 5, "quality_filter_enabled": true}, "incremental_updates": {"enabled": true, "batch_size": 50, "update_frequency": 1, "memory_cleanup_interval": 10}, "layered_visualization": {"enabled": true, "show_current_population": true, "show_historical_elite": true, "show_intermediate_solutions": true, "show_local_optima": true}}}, "performance_config": {"enable_jit": true, "parallel_processing": true, "max_workers": 4, "memory_limit_mb": 1024, "sampling_optimization": {"max_total_points": 2000, "cleanup_threshold": 0.8, "diversity_sampling_ratio": 0.3}}, "logging_config": {"level": "INFO", "file": "logs/landscape_analysis.log", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}