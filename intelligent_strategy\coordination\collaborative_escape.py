"""
Collaborative escape coordination for the intelligent strategy selection system.

This module implements coordination mechanisms that allow multiple individuals
to collaborate in escaping from local optima and sharing exploration information.
"""

import time
import random
import numpy as np
from typing import List, Dict, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from threading import Lock

from ..core.individual_state import IndividualState, StagnationLevel
from ..core.data_structures import StrategyType, ExecutionResult


@dataclass
class EscapeGroup:
    """Group of individuals collaborating for escape."""
    group_id: str
    member_ids: Set[str] = field(default_factory=set)
    formation_time: float = field(default_factory=time.time)
    escape_attempts: int = 0
    successful_escapes: int = 0
    shared_information: Dict[str, Any] = field(default_factory=dict)
    coordination_strategy: str = "distributed"
    max_members: int = 5
    
    def add_member(self, individual_id: str) -> bool:
        """Add a member to the escape group."""
        if len(self.member_ids) < self.max_members:
            self.member_ids.add(individual_id)
            return True
        return False
    
    def remove_member(self, individual_id: str):
        """Remove a member from the escape group."""
        self.member_ids.discard(individual_id)
    
    def is_active(self) -> bool:
        """Check if the group is still active."""
        return len(self.member_ids) >= 2 and time.time() - self.formation_time < 300  # 5 minutes


@dataclass
class SharedExplorationInfo:
    """Information shared between individuals for collaborative exploration."""
    individual_id: str
    fitness_value: float
    solution_features: Dict[str, float]
    explored_regions: List[Tuple[float, float]]  # (center, radius) pairs
    successful_moves: List[Dict[str, Any]]
    failed_moves: List[Dict[str, Any]]
    timestamp: float = field(default_factory=time.time)
    
    def is_recent(self, max_age: float = 60.0) -> bool:
        """Check if the information is recent enough to be useful."""
        return time.time() - self.timestamp < max_age


class CollaborativeEscapeCoordinator:
    """
    Coordinator for collaborative escape mechanisms.
    
    This class manages groups of individuals that collaborate to escape
    from local optima by sharing information and coordinating strategies.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the collaborative escape coordinator."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.max_groups = self.config.get('max_groups', 10)
        self.min_stagnation_for_grouping = self.config.get('min_stagnation_for_grouping', 5)
        self.group_formation_threshold = self.config.get('group_formation_threshold', 0.8)
        self.information_sharing_radius = self.config.get('information_sharing_radius', 0.1)
        
        # State management
        self.escape_groups: Dict[str, EscapeGroup] = {}
        self.individual_to_group: Dict[str, str] = {}
        self.shared_information: Dict[str, SharedExplorationInfo] = {}
        self.coordination_history: deque = deque(maxlen=1000)
        
        # Thread safety
        self._lock = Lock()
        
        # Statistics
        self.groups_formed = 0
        self.successful_collaborations = 0
        self.total_escape_attempts = 0
    
    def request_collaborative_escape(self, 
                                   individual_id: str,
                                   individual_state: IndividualState,
                                   current_fitness: float,
                                   solution_features: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """
        Request collaborative escape assistance.
        
        Args:
            individual_id: ID of the requesting individual
            individual_state: Current state of the individual
            current_fitness: Current fitness value
            solution_features: Features of the current solution
            
        Returns:
            Collaboration instructions or None if no collaboration available
        """
        with self._lock:
            # Check if individual qualifies for collaboration
            if not self._qualifies_for_collaboration(individual_state):
                return None
            
            # Update shared information
            self._update_shared_information(
                individual_id, current_fitness, solution_features
            )
            
            # Try to join existing group or form new group
            group_id = self._find_or_create_group(individual_id, individual_state, solution_features)
            
            if group_id is None:
                return None
            
            # Generate collaboration instructions
            instructions = self._generate_collaboration_instructions(
                individual_id, group_id, individual_state
            )
            
            return instructions
    
    def report_escape_result(self, 
                           individual_id: str,
                           escape_successful: bool,
                           new_fitness: float,
                           strategy_used: StrategyType,
                           execution_result: ExecutionResult):
        """
        Report the result of an escape attempt.
        
        Args:
            individual_id: ID of the individual
            escape_successful: Whether the escape was successful
            new_fitness: New fitness after escape attempt
            strategy_used: Strategy that was used
            execution_result: Detailed execution result
        """
        with self._lock:
            # Update group statistics
            if individual_id in self.individual_to_group:
                group_id = self.individual_to_group[individual_id]
                if group_id in self.escape_groups:
                    group = self.escape_groups[group_id]
                    group.escape_attempts += 1
                    
                    if escape_successful:
                        group.successful_escapes += 1
                        self.successful_collaborations += 1
            
            # Update coordination history
            self.coordination_history.append({
                'individual_id': individual_id,
                'timestamp': time.time(),
                'escape_successful': escape_successful,
                'fitness_improvement': execution_result.improvement,
                'strategy_used': strategy_used.value,
                'execution_time': execution_result.execution_time
            })
            
            # Clean up if individual escaped successfully
            if escape_successful:
                self._remove_individual_from_group(individual_id)
    
    def _qualifies_for_collaboration(self, individual_state: IndividualState) -> bool:
        """Check if an individual qualifies for collaborative escape."""
        # Must be stagnated for minimum duration
        if individual_state.stagnation_duration < self.min_stagnation_for_grouping:
            return False
        
        # Must have moderate to critical stagnation level
        if individual_state.stagnation_level in [StagnationLevel.NONE, StagnationLevel.LOW]:
            return False
        
        return True
    
    def _update_shared_information(self, 
                                 individual_id: str,
                                 fitness: float,
                                 solution_features: Dict[str, float]):
        """Update shared exploration information."""
        # Create or update shared information
        if individual_id not in self.shared_information:
            self.shared_information[individual_id] = SharedExplorationInfo(
                individual_id=individual_id,
                fitness_value=fitness,
                solution_features=solution_features,
                explored_regions=[],
                successful_moves=[],
                failed_moves=[]
            )
        else:
            info = self.shared_information[individual_id]
            info.fitness_value = fitness
            info.solution_features = solution_features
            info.timestamp = time.time()
    
    def _find_or_create_group(self, 
                            individual_id: str,
                            individual_state: IndividualState,
                            solution_features: Dict[str, float]) -> Optional[str]:
        """Find an existing group or create a new one."""
        # Try to find compatible existing group
        compatible_group = self._find_compatible_group(individual_id, solution_features)
        
        if compatible_group is not None:
            group = self.escape_groups[compatible_group]
            if group.add_member(individual_id):
                self.individual_to_group[individual_id] = compatible_group
                return compatible_group
        
        # Create new group if possible
        if len(self.escape_groups) < self.max_groups:
            group_id = f"group_{self.groups_formed}_{int(time.time())}"
            new_group = EscapeGroup(
                group_id=group_id,
                member_ids={individual_id},
                coordination_strategy=self._select_coordination_strategy(individual_state)
            )
            
            self.escape_groups[group_id] = new_group
            self.individual_to_group[individual_id] = group_id
            self.groups_formed += 1
            
            return group_id
        
        return None
    
    def _find_compatible_group(self, 
                             individual_id: str,
                             solution_features: Dict[str, float]) -> Optional[str]:
        """Find a compatible existing group."""
        best_group = None
        best_compatibility = 0.0
        
        for group_id, group in self.escape_groups.items():
            if not group.is_active() or len(group.member_ids) >= group.max_members:
                continue
            
            # Calculate compatibility with group members
            compatibility = self._calculate_group_compatibility(
                individual_id, solution_features, group
            )
            
            if compatibility > self.group_formation_threshold and compatibility > best_compatibility:
                best_group = group_id
                best_compatibility = compatibility
        
        return best_group
    
    def _calculate_group_compatibility(self, 
                                     individual_id: str,
                                     solution_features: Dict[str, float],
                                     group: EscapeGroup) -> float:
        """Calculate compatibility with a group."""
        if not group.member_ids:
            return 0.0
        
        total_compatibility = 0.0
        valid_members = 0
        
        for member_id in group.member_ids:
            if member_id in self.shared_information:
                member_info = self.shared_information[member_id]
                if member_info.is_recent():
                    # Calculate feature similarity
                    similarity = self._calculate_feature_similarity(
                        solution_features, member_info.solution_features
                    )
                    total_compatibility += similarity
                    valid_members += 1
        
        if valid_members == 0:
            return 0.0
        
        return total_compatibility / valid_members
    
    def _calculate_feature_similarity(self, 
                                    features1: Dict[str, float],
                                    features2: Dict[str, float]) -> float:
        """Calculate similarity between solution features."""
        common_keys = set(features1.keys()) & set(features2.keys())
        if not common_keys:
            return 0.0
        
        similarities = []
        for key in common_keys:
            # Normalized difference
            diff = abs(features1[key] - features2[key])
            max_val = max(abs(features1[key]), abs(features2[key]), 1e-6)
            similarity = 1.0 - min(1.0, diff / max_val)
            similarities.append(similarity)
        
        return np.mean(similarities)
    
    def _select_coordination_strategy(self, individual_state: IndividualState) -> str:
        """Select coordination strategy based on individual state."""
        if individual_state.stagnation_level == StagnationLevel.CRITICAL:
            return "aggressive"
        elif individual_state.stagnation_level == StagnationLevel.HIGH:
            return "active"
        else:
            return "distributed"
    
    def _generate_collaboration_instructions(self, 
                                           individual_id: str,
                                           group_id: str,
                                           individual_state: IndividualState) -> Dict[str, Any]:
        """Generate collaboration instructions for an individual."""
        group = self.escape_groups[group_id]
        
        # Collect relevant shared information
        relevant_info = self._collect_relevant_information(individual_id, group)
        
        # Generate specific instructions based on coordination strategy
        if group.coordination_strategy == "aggressive":
            instructions = self._generate_aggressive_instructions(relevant_info)
        elif group.coordination_strategy == "active":
            instructions = self._generate_active_instructions(relevant_info)
        else:  # distributed
            instructions = self._generate_distributed_instructions(relevant_info)
        
        # Add group context
        instructions.update({
            'group_id': group_id,
            'group_size': len(group.member_ids),
            'coordination_strategy': group.coordination_strategy,
            'shared_information': relevant_info
        })
        
        return instructions
    
    def _collect_relevant_information(self, 
                                    individual_id: str,
                                    group: EscapeGroup) -> Dict[str, Any]:
        """Collect relevant shared information for collaboration."""
        relevant_info = {
            'group_members': [],
            'successful_strategies': [],
            'failed_strategies': [],
            'explored_regions': [],
            'best_solutions': []
        }
        
        for member_id in group.member_ids:
            if member_id == individual_id:
                continue
            
            if member_id in self.shared_information:
                member_info = self.shared_information[member_id]
                if member_info.is_recent():
                    relevant_info['group_members'].append({
                        'id': member_id,
                        'fitness': member_info.fitness_value,
                        'features': member_info.solution_features
                    })
                    
                    relevant_info['successful_strategies'].extend(member_info.successful_moves)
                    relevant_info['failed_strategies'].extend(member_info.failed_moves)
                    relevant_info['explored_regions'].extend(member_info.explored_regions)
        
        return relevant_info
    
    def _generate_aggressive_instructions(self, relevant_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate aggressive collaboration instructions."""
        return {
            'collaboration_type': 'aggressive',
            'recommended_strategy': StrategyType.STRONG_EXPLORATION,
            'coordination_actions': [
                'share_exploration_results',
                'coordinate_search_directions',
                'avoid_explored_regions'
            ],
            'exploration_bias': 0.9,
            'information_sharing_frequency': 'high',
            'avoid_regions': relevant_info.get('explored_regions', [])
        }
    
    def _generate_active_instructions(self, relevant_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate active collaboration instructions."""
        return {
            'collaboration_type': 'active',
            'recommended_strategy': StrategyType.INTELLIGENT_EXPLORATION,
            'coordination_actions': [
                'share_successful_moves',
                'coordinate_timing',
                'exchange_solutions'
            ],
            'exploration_bias': 0.7,
            'information_sharing_frequency': 'medium',
            'successful_patterns': relevant_info.get('successful_strategies', [])
        }
    
    def _generate_distributed_instructions(self, relevant_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate distributed collaboration instructions."""
        return {
            'collaboration_type': 'distributed',
            'recommended_strategy': StrategyType.BALANCED_EXPLORATION,
            'coordination_actions': [
                'share_basic_info',
                'avoid_redundancy'
            ],
            'exploration_bias': 0.6,
            'information_sharing_frequency': 'low',
            'group_diversity_target': 0.8
        }
    
    def _remove_individual_from_group(self, individual_id: str):
        """Remove an individual from their group."""
        if individual_id in self.individual_to_group:
            group_id = self.individual_to_group[individual_id]
            if group_id in self.escape_groups:
                group = self.escape_groups[group_id]
                group.remove_member(individual_id)
                
                # Remove group if too few members
                if len(group.member_ids) < 2:
                    del self.escape_groups[group_id]
                    # Remove all remaining members from mapping
                    for member_id in list(group.member_ids):
                        if member_id in self.individual_to_group:
                            del self.individual_to_group[member_id]
            
            del self.individual_to_group[individual_id]
    
    def cleanup_inactive_groups(self):
        """Clean up inactive groups and outdated information."""
        with self._lock:
            # Remove inactive groups
            inactive_groups = []
            for group_id, group in self.escape_groups.items():
                if not group.is_active():
                    inactive_groups.append(group_id)
            
            for group_id in inactive_groups:
                group = self.escape_groups[group_id]
                # Remove individual mappings
                for member_id in group.member_ids:
                    if member_id in self.individual_to_group:
                        del self.individual_to_group[member_id]
                # Remove group
                del self.escape_groups[group_id]
            
            # Remove outdated shared information
            outdated_info = []
            for individual_id, info in self.shared_information.items():
                if not info.is_recent(max_age=120.0):  # 2 minutes
                    outdated_info.append(individual_id)
            
            for individual_id in outdated_info:
                del self.shared_information[individual_id]
            
            if inactive_groups or outdated_info:
                self.logger.info(
                    f"Cleaned up {len(inactive_groups)} inactive groups and "
                    f"{len(outdated_info)} outdated information entries"
                )
    
    def get_coordination_statistics(self) -> Dict[str, Any]:
        """Get coordination statistics."""
        with self._lock:
            active_groups = sum(1 for group in self.escape_groups.values() if group.is_active())
            total_members = sum(len(group.member_ids) for group in self.escape_groups.values())
            
            return {
                'active_groups': active_groups,
                'total_groups_formed': self.groups_formed,
                'total_members': total_members,
                'successful_collaborations': self.successful_collaborations,
                'total_escape_attempts': self.total_escape_attempts,
                'success_rate': self.successful_collaborations / max(1, self.total_escape_attempts),
                'shared_information_entries': len(self.shared_information),
                'coordination_history_length': len(self.coordination_history)
            }


class StrategyCoordinator:
    """
    Coordinator for strategy execution coordination.
    
    This class manages coordination between individuals to avoid
    redundant strategy executions and optimize resource usage.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the strategy coordinator."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.coordination_window = self.config.get('coordination_window', 30.0)  # seconds
        self.max_concurrent_strategies = self.config.get('max_concurrent_strategies', 5)
        
        # State management
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        self.execution_queue: deque = deque()
        self.resource_usage: Dict[str, float] = defaultdict(float)
        
        # Thread safety
        self._lock = Lock()
        
        # Statistics
        self.coordinated_executions = 0
        self.resource_conflicts_avoided = 0
    
    def request_strategy_execution(self, 
                                 individual_id: str,
                                 strategy_type: StrategyType,
                                 priority: float = 0.5,
                                 resource_requirements: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        Request coordinated strategy execution.
        
        Args:
            individual_id: ID of the individual
            strategy_type: Type of strategy to execute
            priority: Execution priority (0.0 to 1.0)
            resource_requirements: Required resources
            
        Returns:
            Coordination decision
        """
        with self._lock:
            # Check resource availability
            resource_requirements = resource_requirements or {'cpu': 0.1, 'memory': 0.05}
            
            if self._check_resource_availability(resource_requirements):
                # Resources available - proceed immediately
                execution_id = f"{individual_id}_{strategy_type.value}_{int(time.time())}"
                
                self.active_executions[execution_id] = {
                    'individual_id': individual_id,
                    'strategy_type': strategy_type,
                    'start_time': time.time(),
                    'priority': priority,
                    'resource_requirements': resource_requirements
                }
                
                # Reserve resources
                for resource, amount in resource_requirements.items():
                    self.resource_usage[resource] += amount
                
                self.coordinated_executions += 1
                
                return {
                    'decision': 'execute_immediately',
                    'execution_id': execution_id,
                    'estimated_wait_time': 0.0
                }
            else:
                # Resources not available - queue or delay
                if priority > 0.7:
                    # High priority - add to queue
                    self.execution_queue.append({
                        'individual_id': individual_id,
                        'strategy_type': strategy_type,
                        'priority': priority,
                        'resource_requirements': resource_requirements,
                        'request_time': time.time()
                    })
                    
                    estimated_wait = self._estimate_queue_wait_time()
                    
                    return {
                        'decision': 'queued',
                        'queue_position': len(self.execution_queue),
                        'estimated_wait_time': estimated_wait
                    }
                else:
                    # Low priority - suggest delay
                    self.resource_conflicts_avoided += 1
                    
                    return {
                        'decision': 'delay',
                        'suggested_delay': 10.0,
                        'reason': 'resource_contention'
                    }
    
    def complete_strategy_execution(self, execution_id: str):
        """Mark a strategy execution as completed."""
        with self._lock:
            if execution_id in self.active_executions:
                execution_info = self.active_executions[execution_id]
                
                # Release resources
                for resource, amount in execution_info['resource_requirements'].items():
                    self.resource_usage[resource] = max(0.0, self.resource_usage[resource] - amount)
                
                # Remove from active executions
                del self.active_executions[execution_id]
                
                # Process queue
                self._process_execution_queue()
    
    def _check_resource_availability(self, requirements: Dict[str, float]) -> bool:
        """Check if required resources are available."""
        for resource, amount in requirements.items():
            if self.resource_usage[resource] + amount > 1.0:  # Assuming normalized resources
                return False
        return True
    
    def _estimate_queue_wait_time(self) -> float:
        """Estimate wait time for queued executions."""
        if not self.active_executions:
            return 0.0
        
        # Estimate based on average execution time and current load
        avg_execution_time = 5.0  # Default estimate
        current_load = len(self.active_executions)
        
        return avg_execution_time * current_load / max(1, self.max_concurrent_strategies)
    
    def _process_execution_queue(self):
        """Process queued executions when resources become available."""
        if not self.execution_queue:
            return
        
        # Sort queue by priority and wait time
        sorted_queue = sorted(
            self.execution_queue,
            key=lambda x: (-x['priority'], x['request_time'])
        )
        
        # Try to execute high-priority items
        executed_items = []
        for i, item in enumerate(sorted_queue):
            if self._check_resource_availability(item['resource_requirements']):
                # Execute this item
                execution_id = f"{item['individual_id']}_{item['strategy_type'].value}_{int(time.time())}"
                
                self.active_executions[execution_id] = {
                    'individual_id': item['individual_id'],
                    'strategy_type': item['strategy_type'],
                    'start_time': time.time(),
                    'priority': item['priority'],
                    'resource_requirements': item['resource_requirements']
                }
                
                # Reserve resources
                for resource, amount in item['resource_requirements'].items():
                    self.resource_usage[resource] += amount
                
                executed_items.append(i)
                self.coordinated_executions += 1
        
        # Remove executed items from queue
        for i in reversed(executed_items):
            del sorted_queue[i]
        
        # Update queue
        self.execution_queue = deque(sorted_queue)
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """Get current coordination status."""
        with self._lock:
            return {
                'active_executions': len(self.active_executions),
                'queued_executions': len(self.execution_queue),
                'resource_usage': dict(self.resource_usage),
                'coordinated_executions': self.coordinated_executions,
                'resource_conflicts_avoided': self.resource_conflicts_avoided
            }
