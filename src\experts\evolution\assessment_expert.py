# -*- coding: utf-8 -*-
"""
评估专家模块

包含EvolutionAssessmentExpert类，负责基于数学指标和统计分析评估策略效果，提供反馈。
使用纯算法实现，不依赖LLM。
"""

import copy
import numpy as np
from experts.base.expert_base import ExpertBase
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from utils import utils


class EvolutionAssessmentExpert(ExpertBase):
    """进化评估专家，基于数学指标和统计分析评估策略效果，提供反馈
    使用纯算法实现，不依赖LLM"""

    def __init__(self, interface_llm=None):
        super().__init__()
        # 保留接口参数以保持兼容性，但不使用
        self.interface_llm = interface_llm

        # 评估历史记录
        self.feedback_history = []

        # 评估参数配置
        self.improvement_threshold = 0.01  # 改进阈值（1%）
        self.diversity_weight = 0.3        # 多样性权重
        self.convergence_window = 3        # 收敛趋势分析窗口
    
    def evaluate(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """评估进化结果并生成报告，使用纯算法分析"""
        self.logger.info(f"--- Iteration {iteration} Assessment (Algorithm-based) ---")

        # 执行算法评估分析
        assessment_result = self.perform_algorithmic_assessment(
            old_stats_report=old_stats_report,
            new_stats_report=new_stats_report,
            strategies=strategies,
            iteration=iteration,
            total_iterations=total_iterations,
            old_res_populations=old_res_populations,
            new_res_populations=new_res_populations
        )

        # 生成评估报告
        assessment_response = self.generate_assessment_report(assessment_result)
        self.logger.info("算法评估报告: %s", assessment_response)
        
        # 保存反馈历史 - 添加精英解统计信息
        
        # --- 兼容: old_stats_report / new_stats_report 可能是 dict（stats_report）或 list（population） ---
        def _extract_costs(entity):
            if isinstance(entity, list):
                return [p.get("cur_cost", 0) for p in entity if isinstance(p, dict)]
            elif isinstance(entity, dict):
                cs = entity.get("cost_stats", {})
                return [cs.get("min", 0), cs.get("max", 0), cs.get("mean", 0)]
            else:
                return [0]

        def _calc_diversity(entity):
            if isinstance(entity, list):
                return utils.calculate_population_diversity(entity)
            return entity.get("diversity_level", 0) if isinstance(entity, dict) else 0

        old_costs = _extract_costs(old_stats_report)
        new_costs = _extract_costs(new_stats_report)

        old_population_stats = {
            "min_cost": min(old_costs) if old_costs else 0,
            "max_cost": max(old_costs) if old_costs else 0,
            "mean_cost": sum(old_costs) / len(old_costs) if old_costs else 0,
            "diversity": _calc_diversity(old_stats_report)
        }

        new_population_stats = {
            "min_cost": min(new_costs) if new_costs else 0,
            "max_cost": max(new_costs) if new_costs else 0,
            "mean_cost": sum(new_costs) / len(new_costs) if new_costs else 0,
            "diversity": _calc_diversity(new_stats_report)
        }

        # 精英解统计
        old_elite_stats = {}
        new_elite_stats = {}
        
        if old_res_populations:
            old_elite_costs = [p.get("cur_cost", 0) for p in old_res_populations]
            old_elite_stats = {
                "count": len(old_res_populations),
                "min_cost": min(old_elite_costs) if old_elite_costs else 0,
                "mean_cost": sum(old_elite_costs) / len(old_elite_costs) if old_elite_costs else 0,
                "diversity": self._calculate_population_diversity_from_list(old_res_populations)
            }
        
        if new_res_populations:
            new_elite_costs = [p.get("cur_cost", 0) for p in new_res_populations]
            new_elite_stats = {
                "count": len(new_res_populations),
                "min_cost": min(new_elite_costs) if new_elite_costs else 0,
                "mean_cost": sum(new_elite_costs) / len(new_elite_costs) if new_elite_costs else 0,
                "diversity": self._calculate_population_diversity_from_list(new_res_populations)
            }

        feedback_entry = {
            "iteration": iteration,
            "old_population": old_population_stats,
            "new_population": new_population_stats,
            "old_elite": old_elite_stats,
            "new_elite": new_elite_stats,
            "strategies": strategies,
            "assessment": assessment_result,
            "response": assessment_response
        }
        
        self.feedback_history.append(feedback_entry)
        
        # 限制历史记录长度
        if len(self.feedback_history) > 20:
            self.feedback_history = self.feedback_history[-20:]
        
        return assessment_response

    def perform_algorithmic_assessment(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """执行基于算法的评估分析
        
        参数:
            old_stats_report: 旧的统计报告
            new_stats_report: 新的统计报告
            strategies: 策略分配
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            old_res_populations: 旧的精英解种群
            new_res_populations: 新的精英解种群
            
        返回:
            评估结果字典
        """
        self.logger.info("开始执行算法评估分析")
        
        # 1. 成本改进分析
        cost_improvement = self.calculate_cost_improvement(old_stats_report, new_stats_report)
        
        # 2. 多样性变化分析
        diversity_change = self.analyze_diversity_change(old_stats_report, new_stats_report)
        
        # 3. 策略效果分析
        strategy_effectiveness = self.analyze_strategy_effectiveness(strategies, old_stats_report, new_stats_report)
        
        # 4. 收敛趋势分析
        convergence_trend = self.analyze_convergence_trend()
        
        # 5. 精英解分析
        elite_analysis = self.analyze_elite_populations(old_res_populations, new_res_populations)
        
        # 6. 生成改进建议
        improvement_suggestions = self.generate_improvement_suggestions(
            cost_improvement, diversity_change, strategy_effectiveness, 
            convergence_trend, elite_analysis, iteration, total_iterations
        )
        
        # 7. 计算总体评分
        overall_score = self.calculate_overall_score(
            cost_improvement, diversity_change, strategy_effectiveness, convergence_trend
        )
        
        return {
            "cost_improvement": cost_improvement,
            "diversity_change": diversity_change,
            "strategy_effectiveness": strategy_effectiveness,
            "convergence_trend": convergence_trend,
            "elite_analysis": elite_analysis,
            "improvement_suggestions": improvement_suggestions,
            "overall_score": overall_score,
            "iteration": iteration,
            "total_iterations": total_iterations
        }

    def calculate_cost_improvement(self, old_stats, new_stats):
        """计算成本改进情况
        
        参数:
            old_stats: 旧统计数据
            new_stats: 新统计数据
            
        返回:
            成本改进分析结果
        """
        # 提取成本数据
        old_costs = self._extract_costs(old_stats)
        new_costs = self._extract_costs(new_stats)
        
        if not old_costs or not new_costs:
            return {
                "improvement_rate": 0.0,
                "best_improvement": 0.0,
                "avg_improvement": 0.0,
                "status": "insufficient_data"
            }
        
        # 计算改进率
        old_min = min(old_costs)
        new_min = min(new_costs)
        old_avg = sum(old_costs) / len(old_costs)
        new_avg = sum(new_costs) / len(new_costs)
        
        best_improvement = (old_min - new_min) / old_min if old_min > 0 else 0
        avg_improvement = (old_avg - new_avg) / old_avg if old_avg > 0 else 0
        
        # 计算个体改进率
        individual_improvements = []
        min_len = min(len(old_costs), len(new_costs))
        for i in range(min_len):
            if old_costs[i] > 0:
                improvement = (old_costs[i] - new_costs[i]) / old_costs[i]
                individual_improvements.append(improvement)
        
        improvement_rate = sum(individual_improvements) / len(individual_improvements) if individual_improvements else 0
        
        # 评估改进状态
        if improvement_rate > self.improvement_threshold:
            status = "significant_improvement"
        elif improvement_rate > 0:
            status = "slight_improvement"
        else:
            status = "deterioration"
        
        return {
            "improvement_rate": improvement_rate,
            "best_improvement": best_improvement,
            "avg_improvement": avg_improvement,
            "individual_improvements": individual_improvements,
            "status": status
        }

    def analyze_diversity_change(self, old_stats, new_stats):
        """分析多样性变化
        
        参数:
            old_stats: 旧统计数据
            new_stats: 新统计数据
            
        返回:
            多样性变化分析结果
        """
        old_diversity = self._calc_diversity(old_stats)
        new_diversity = self._calc_diversity(new_stats)
        
        diversity_change = new_diversity - old_diversity
        diversity_change_rate = diversity_change / old_diversity if old_diversity > 0 else 0
        
        # 评估多样性状态
        if new_diversity > 0.7:
            diversity_status = "high_diversity"
        elif new_diversity > 0.4:
            diversity_status = "moderate_diversity"
        else:
            diversity_status = "low_diversity"
        
        return {
            "old_diversity": old_diversity,
            "new_diversity": new_diversity,
            "diversity_change": diversity_change,
            "diversity_change_rate": diversity_change_rate,
            "diversity_status": diversity_status
        }

    def analyze_strategy_effectiveness(self, strategies, old_stats, new_stats):
        """分析策略效果统计

        参数:
            strategies: 策略分配列表
            old_stats: 旧种群统计报告
            new_stats: 新种群统计报告

        返回:
            策略效果分析结果
        """
        old_costs = self._extract_costs(old_stats)
        new_costs = self._extract_costs(new_stats)

        if len(old_costs) != len(new_costs) or len(strategies) != len(old_costs):
            return {"status": "data_mismatch"}

        # 分别统计探索和利用策略的效果
        explore_improvements = []
        exploit_improvements = []

        for i, strategy in enumerate(strategies):
            if i < len(old_costs) and i < len(new_costs):
                improvement = (old_costs[i] - new_costs[i]) / old_costs[i] if old_costs[i] > 0 else 0

                if strategy == 'explore':
                    explore_improvements.append(improvement)
                elif strategy == 'exploit':
                    exploit_improvements.append(improvement)

        # 计算策略统计
        explore_stats = self._calculate_strategy_stats(explore_improvements, "explore")
        exploit_stats = self._calculate_strategy_stats(exploit_improvements, "exploit")

        # 比较策略效果
        if explore_stats["avg_improvement"] > exploit_stats["avg_improvement"]:
            better_strategy = "explore"
            strategy_recommendation = "increase_exploration"
        elif exploit_stats["avg_improvement"] > explore_stats["avg_improvement"]:
            better_strategy = "exploit"
            strategy_recommendation = "increase_exploitation"
        else:
            better_strategy = "balanced"
            strategy_recommendation = "maintain_balance"

        return {
            "explore_stats": explore_stats,
            "exploit_stats": exploit_stats,
            "better_strategy": better_strategy,
            "strategy_recommendation": strategy_recommendation,
            "status": "success"
        }

    def _calculate_strategy_stats(self, improvements, strategy_name):
        """计算策略统计信息

        参数:
            improvements: 改进率列表
            strategy_name: 策略名称

        返回:
            策略统计信息
        """
        if not improvements:
            return {
                "count": 0,
                "avg_improvement": 0.0,
                "success_rate": 0.0,
                "best_improvement": 0.0,
                "worst_improvement": 0.0
            }

        avg_improvement = sum(improvements) / len(improvements)
        success_count = sum(1 for imp in improvements if imp > 0)
        success_rate = success_count / len(improvements)

        return {
            "count": len(improvements),
            "avg_improvement": avg_improvement,
            "success_rate": success_rate,
            "best_improvement": max(improvements),
            "worst_improvement": min(improvements)
        }

    def analyze_convergence_trend(self):
        """分析收敛趋势

        返回:
            收敛趋势分析结果
        """
        if len(self.feedback_history) < self.convergence_window:
            return {
                "trend": "insufficient_data",
                "trend_strength": 0.0,
                "recent_improvements": [],
                "convergence_status": "unknown"
            }

        # 获取最近几次迭代的改进情况
        recent_history = self.feedback_history[-self.convergence_window:]
        recent_improvements = []

        for entry in recent_history:
            if "assessment" in entry and "cost_improvement" in entry["assessment"]:
                improvement = entry["assessment"]["cost_improvement"].get("improvement_rate", 0)
                recent_improvements.append(improvement)

        if not recent_improvements:
            return {
                "trend": "no_data",
                "trend_strength": 0.0,
                "recent_improvements": [],
                "convergence_status": "unknown"
            }

        # 分析趋势
        if len(recent_improvements) >= 2:
            # 计算趋势斜率
            x = list(range(len(recent_improvements)))
            y = recent_improvements

            # 简单线性回归计算趋势
            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(x[i] ** 2 for i in range(n))

            if n * sum_x2 - sum_x ** 2 != 0:
                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
            else:
                slope = 0

            # 判断趋势
            if slope > 0.01:
                trend = "improving"
                convergence_status = "accelerating"
            elif slope < -0.01:
                trend = "deteriorating"
                convergence_status = "decelerating"
            else:
                trend = "stable"
                convergence_status = "converged"

            trend_strength = abs(slope)
        else:
            trend = "stable"
            trend_strength = 0.0
            convergence_status = "stable"

        return {
            "trend": trend,
            "trend_strength": trend_strength,
            "recent_improvements": recent_improvements,
            "convergence_status": convergence_status
        }

    def analyze_elite_populations(self, old_res_populations, new_res_populations):
        """分析精英解种群变化

        参数:
            old_res_populations: 旧精英解种群
            new_res_populations: 新精英解种群

        返回:
            精英解分析结果
        """
        old_count = len(old_res_populations) if old_res_populations else 0
        new_count = len(new_res_populations) if new_res_populations else 0

        count_change = new_count - old_count

        # 分析精英解质量变化
        old_best_cost = float('inf')
        new_best_cost = float('inf')

        if old_res_populations:
            old_costs = [p.get("cur_cost", float('inf')) for p in old_res_populations]
            old_best_cost = min(old_costs) if old_costs else float('inf')

        if new_res_populations:
            new_costs = [p.get("cur_cost", float('inf')) for p in new_res_populations]
            new_best_cost = min(new_costs) if new_costs else float('inf')

        quality_improvement = 0.0
        if old_best_cost != float('inf') and new_best_cost != float('inf'):
            quality_improvement = (old_best_cost - new_best_cost) / old_best_cost

        # 分析精英解多样性
        old_diversity = self._calculate_population_diversity_from_list(old_res_populations) if old_res_populations else 0
        new_diversity = self._calculate_population_diversity_from_list(new_res_populations) if new_res_populations else 0

        return {
            "old_count": old_count,
            "new_count": new_count,
            "count_change": count_change,
            "old_best_cost": old_best_cost if old_best_cost != float('inf') else None,
            "new_best_cost": new_best_cost if new_best_cost != float('inf') else None,
            "quality_improvement": quality_improvement,
            "old_diversity": old_diversity,
            "new_diversity": new_diversity,
            "diversity_change": new_diversity - old_diversity
        }

    def generate_improvement_suggestions(self, cost_improvement, diversity_change, strategy_effectiveness, convergence_trend, elite_analysis, iteration, total_iterations):
        """生成改进建议

        参数:
            cost_improvement: 成本改进分析
            diversity_change: 多样性变化分析
            strategy_effectiveness: 策略效果分析
            convergence_trend: 收敛趋势分析
            elite_analysis: 精英解分析
            iteration: 当前迭代
            total_iterations: 总迭代数

        返回:
            改进建议列表
        """
        suggestions = []

        # 基于成本改进的建议
        if cost_improvement["status"] == "deterioration":
            suggestions.append("成本出现恶化，建议增加探索策略比例以跳出局部最优")
        elif cost_improvement["status"] == "slight_improvement":
            suggestions.append("改进缓慢，建议调整策略平衡或增加扰动强度")

        # 基于多样性的建议
        if diversity_change["diversity_status"] == "low_diversity":
            suggestions.append("种群多样性过低，建议增加探索策略和随机扰动")
        elif diversity_change["diversity_status"] == "high_diversity":
            suggestions.append("种群多样性较高，可适当增加利用策略进行局部优化")

        # 基于策略效果的建议
        if strategy_effectiveness.get("status") == "success":
            if strategy_effectiveness["strategy_recommendation"] == "increase_exploration":
                suggestions.append("探索策略效果更好，建议增加探索策略比例")
            elif strategy_effectiveness["strategy_recommendation"] == "increase_exploitation":
                suggestions.append("利用策略效果更好，建议增加利用策略比例")

        # 基于收敛趋势的建议
        if convergence_trend["convergence_status"] == "converged":
            suggestions.append("算法可能已收敛，建议考虑早停或增加扰动重启")
        elif convergence_trend["convergence_status"] == "decelerating":
            suggestions.append("收敛速度放缓，建议调整参数或改变策略分配")

        # 基于精英解的建议
        if elite_analysis["count_change"] == 0 and iteration > total_iterations * 0.3:
            suggestions.append("精英解数量未增加，建议调整精英解选择标准")

        # 基于迭代进度的建议
        progress = iteration / total_iterations if total_iterations > 0 else 0
        if progress > 0.8 and cost_improvement["improvement_rate"] < 0.001:
            suggestions.append("接近迭代结束且改进微小，建议考虑提前终止")

        return suggestions if suggestions else ["当前状态良好，继续保持现有策略"]

    def calculate_overall_score(self, cost_improvement, diversity_change, strategy_effectiveness, convergence_trend):
        """计算总体评分

        参数:
            cost_improvement: 成本改进分析
            diversity_change: 多样性变化分析
            strategy_effectiveness: 策略效果分析
            convergence_trend: 收敛趋势分析

        返回:
            总体评分 (0-100)
        """
        score = 50  # 基础分数

        # 成本改进评分 (40分)
        if cost_improvement["status"] == "significant_improvement":
            score += 40
        elif cost_improvement["status"] == "slight_improvement":
            score += 20
        elif cost_improvement["status"] == "deterioration":
            score -= 20

        # 多样性评分 (20分)
        if diversity_change["diversity_status"] == "moderate_diversity":
            score += 20
        elif diversity_change["diversity_status"] == "high_diversity":
            score += 10
        elif diversity_change["diversity_status"] == "low_diversity":
            score -= 10

        # 策略效果评分 (20分)
        if strategy_effectiveness.get("status") == "success":
            explore_success = strategy_effectiveness["explore_stats"]["success_rate"]
            exploit_success = strategy_effectiveness["exploit_stats"]["success_rate"]
            avg_success = (explore_success + exploit_success) / 2
            score += int(avg_success * 20)

        # 收敛趋势评分 (20分)
        if convergence_trend["convergence_status"] == "accelerating":
            score += 20
        elif convergence_trend["convergence_status"] == "stable":
            score += 10
        elif convergence_trend["convergence_status"] == "decelerating":
            score -= 10

        return max(0, min(100, score))  # 确保分数在0-100范围内

    def generate_assessment_report(self, assessment_result):
        """生成评估报告

        参数:
            assessment_result: 评估结果

        返回:
            格式化的评估报告
        """
        report = {
            "overall_score": assessment_result["overall_score"],
            "iteration": assessment_result["iteration"],
            "total_iterations": assessment_result["total_iterations"],
            "cost_improvement": {
                "status": assessment_result["cost_improvement"]["status"],
                "improvement_rate": assessment_result["cost_improvement"]["improvement_rate"],
                "best_improvement": assessment_result["cost_improvement"]["best_improvement"]
            },
            "diversity_analysis": {
                "status": assessment_result["diversity_change"]["diversity_status"],
                "change_rate": assessment_result["diversity_change"]["diversity_change_rate"]
            },
            "strategy_effectiveness": assessment_result["strategy_effectiveness"],
            "convergence_trend": assessment_result["convergence_trend"],
            "elite_analysis": assessment_result["elite_analysis"],
            "suggestions": assessment_result["improvement_suggestions"]
        }

        return report

    def _extract_costs(self, entity):
        """提取成本数据的辅助方法"""
        if isinstance(entity, list):
            return [p.get("cur_cost", 0) for p in entity if isinstance(p, dict)]
        elif isinstance(entity, dict):
            cs = entity.get("cost_stats", {})
            return [cs.get("min", 0), cs.get("max", 0), cs.get("mean", 0)]
        else:
            return [0]

    def _calc_diversity(self, entity):
        """计算多样性的辅助方法"""
        if isinstance(entity, list):
            return utils.calculate_population_diversity(entity)
        return entity.get("diversity_level", 0) if isinstance(entity, dict) else 0

    def _calculate_population_diversity_from_list(self, populations):
        """从种群列表计算多样性

        参数:
            populations: 种群列表

        返回:
            多样性值 (0-1)
        """
        if not populations or len(populations) < 2:
            return 0.0

        # 计算路径间的平均汉明距离
        total_distance = 0
        comparisons = 0

        for i in range(len(populations)):
            for j in range(i + 1, len(populations)):
                if "tour" in populations[i] and "tour" in populations[j]:
                    tour1 = populations[i]["tour"]
                    tour2 = populations[j]["tour"]

                    # 计算汉明距离（不同位置的数量）
                    distance = sum(1 for a, b in zip(tour1, tour2) if a != b)
                    total_distance += distance
                    comparisons += 1

        if comparisons == 0:
            return 0.0

        avg_distance = total_distance / comparisons
        max_possible_distance = len(populations[0]["tour"]) if populations and "tour" in populations[0] else 1

        return min(1.0, avg_distance / max_possible_distance)
