2025-08-05 10:29:15,673 - __main__ - INFO - kroA100 开始进化第 1 代
2025-08-05 10:29:15,673 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:15,675 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:15,680 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=26207.000, 多样性=0.968
2025-08-05 10:29:15,683 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:15,688 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.968
2025-08-05 10:29:15,690 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:15,693 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:15,693 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:15,693 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:15,693 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:15,718 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -23913.220, 聚类评分: 0.000, 覆盖率: 0.163, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:15,718 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:15,718 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:15,718 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 10:29:15,724 - visualization.landscape_visualizer - INFO - 插值约束: 13 个点被约束到最小值 26207.00
2025-08-05 10:29:15,725 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=10.4%, 梯度: 4556.67 → 4080.71
2025-08-05 10:29:15,825 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_146_20250805_102915.html
2025-08-05 10:29:15,892 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_146_20250805_102915.html
2025-08-05 10:29:15,892 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 146
2025-08-05 10:29:15,893 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:15,893 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1996秒
2025-08-05 10:29:15,893 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 292, 'max_size': 500, 'hits': 0, 'misses': 292, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 969, 'misses': 498, 'hit_rate': 0.6605316973415133, 'evictions': 398, 'ttl': 7200}}
2025-08-05 10:29:15,893 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -23913.219999999998, 'local_optima_density': 0.1, 'gradient_variance': 5682071124.0756, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1633, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -23913.220)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.163)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360955.7189639, 'performance_metrics': {}}}
2025-08-05 10:29:15,893 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:15,894 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:15,894 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:15,894 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:15,896 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:15,896 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:15,896 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:15,896 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,897 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:15,897 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:15,897 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,897 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:15,897 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:15,898 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:15,898 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:15,898 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,903 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:15,903 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33186.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,903 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33186.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,904 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 33186.00)
2025-08-05 10:29:15,904 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:15,904 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:15,904 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,933 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:15,934 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,935 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102301.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,935 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102301.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,935 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 102301.00)
2025-08-05 10:29:15,936 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:15,936 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:15,936 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,964 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:15,964 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98282.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,965 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98282.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,965 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 98282.00)
2025-08-05 10:29:15,965 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:15,965 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:15,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,970 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:15,970 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31772.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,971 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 31772.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,971 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 31772.00)
2025-08-05 10:29:15,971 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:15,971 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:15,971 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,974 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 100
2025-08-05 10:29:15,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 138384.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,975 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 138384.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,975 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 138384.00)
2025-08-05 10:29:15,975 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:15,975 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:15,975 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,991 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:15,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103654.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:15,993 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103654.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,994 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 103654.00)
2025-08-05 10:29:15,994 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:15,994 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,994 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,994 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 163549.0
2025-08-05 10:29:16,022 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:16,023 - ExploitationExpert - INFO - res_population_costs: [22246.0, 22022, 21782]
2025-08-05 10:29:16,023 - ExploitationExpert - INFO - res_populations: [array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64)]
2025-08-05 10:29:16,025 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,025 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33186.0}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102301.0}, {'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98282.0}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 31772.0}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 138384.0}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103654.0}, {'tour': array([71, 61, 80, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6],
      dtype=int64), 'cur_cost': 163549.0}, {'tour': array([67, 61, 60, 88, 83, 58, 56, 22, 89, 76, 37, 29, 97, 82, 87, 98, 39,
       92, 55, 77, 54, 18,  4, 68, 59, 20, 99, 66, 90,  2, 86, 84, 79, 13,
        5,  0, 41, 80, 45, 71, 48, 74,  8,  3, 49, 96, 23, 25, 63, 51, 12,
       30,  1, 73, 95, 53, 38, 93, 65, 11, 24, 40, 81, 57, 27, 33,  7, 69,
        6, 72,  9, 34, 35, 14, 17, 78, 62, 52, 46, 91, 43, 42, 64, 16, 75,
       94, 50, 85, 28, 21, 10, 32, 47, 70, 36, 26, 15, 31, 19, 44],
      dtype=int64), 'cur_cost': 164166.0}, {'tour': array([60, 62, 12, 46, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 182499.0}, {'tour': array([53, 66, 45, 41, 33, 83,  6, 73, 23, 39, 54, 29, 48, 11, 91, 12, 86,
       95, 80, 65, 24, 87, 51, 79, 92,  4, 68, 42,  2,  0,  7, 16, 30, 57,
       67, 61, 22, 31, 56, 63, 82, 49, 19, 72, 78, 17, 96, 40, 28, 59, 43,
       58, 93, 64, 84, 32, 15,  5, 60, 34, 99, 46, 94, 97, 13, 20, 27, 52,
       44,  9, 69, 74, 14, 36,  3, 47, 55, 89, 62, 50, 77,  8,  1, 85, 70,
       26, 35, 81, 25, 37, 18, 10, 98, 21, 76, 90, 75, 88, 38, 71],
      dtype=int64), 'cur_cost': 171741.0}]
2025-08-05 10:29:16,028 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:29:16,028 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 378, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 378, 'cache_hits': 0, 'similarity_calculations': 1980, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,029 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([71, 61, 80, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6],
      dtype=int64), 'cur_cost': 163549.0, 'intermediate_solutions': [{'tour': array([38, 55, 28,  7, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 176843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 38, 55, 28, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 179838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20,  7, 38, 55, 28, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 180935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28,  7, 38, 55, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 176096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 20,  7, 38, 55, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 175956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,029 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 163549.00)
2025-08-05 10:29:16,029 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:16,030 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:16,030 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,045 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:16,045 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,046 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101141.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:16,046 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101141.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,046 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 101141.00)
2025-08-05 10:29:16,046 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:16,046 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,047 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,047 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 179669.0
2025-08-05 10:29:16,068 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:16,069 - ExploitationExpert - INFO - res_population_costs: [22246.0, 22022, 21782, 21739.0]
2025-08-05 10:29:16,069 - ExploitationExpert - INFO - res_populations: [array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64)]
2025-08-05 10:29:16,071 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,072 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33186.0}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102301.0}, {'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98282.0}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 31772.0}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 138384.0}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103654.0}, {'tour': array([71, 61, 80, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6],
      dtype=int64), 'cur_cost': 163549.0}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101141.0}, {'tour': array([78, 13, 52, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14],
      dtype=int64), 'cur_cost': 179669.0}, {'tour': array([53, 66, 45, 41, 33, 83,  6, 73, 23, 39, 54, 29, 48, 11, 91, 12, 86,
       95, 80, 65, 24, 87, 51, 79, 92,  4, 68, 42,  2,  0,  7, 16, 30, 57,
       67, 61, 22, 31, 56, 63, 82, 49, 19, 72, 78, 17, 96, 40, 28, 59, 43,
       58, 93, 64, 84, 32, 15,  5, 60, 34, 99, 46, 94, 97, 13, 20, 27, 52,
       44,  9, 69, 74, 14, 36,  3, 47, 55, 89, 62, 50, 77,  8,  1, 85, 70,
       26, 35, 81, 25, 37, 18, 10, 98, 21, 76, 90, 75, 88, 38, 71],
      dtype=int64), 'cur_cost': 171741.0}]
2025-08-05 10:29:16,074 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:29:16,074 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 379, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 379, 'cache_hits': 0, 'similarity_calculations': 1981, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,075 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([78, 13, 52, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14],
      dtype=int64), 'cur_cost': 179669.0, 'intermediate_solutions': [{'tour': array([12, 62, 60, 46, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 181772.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 12, 62, 60, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 184134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 46, 12, 62, 60,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 185098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 46, 12, 62, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 181870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 48, 46, 12, 62,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 182720.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,076 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 179669.00)
2025-08-05 10:29:16,076 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:16,076 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:16,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,080 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 100
2025-08-05 10:29:16,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,080 - ExplorationExpert - INFO - 探索路径生成完成，成本: 148222.0, 路径长度: 100, 收集中间解: 0
2025-08-05 10:29:16,081 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 148222.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,081 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 148222.00)
2025-08-05 10:29:16,081 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:16,081 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:16,084 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 33186.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102301.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98282.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 31772.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 138384.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103654.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([71, 61, 80, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6],
      dtype=int64), 'cur_cost': 163549.0, 'intermediate_solutions': [{'tour': array([38, 55, 28,  7, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 176843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 38, 55, 28, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 179838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20,  7, 38, 55, 28, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 180935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28,  7, 38, 55, 20, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 176096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 20,  7, 38, 55, 87, 89, 94, 32, 73, 91, 52, 29, 50, 92, 45, 78,
       61, 96,  6, 71, 79, 39, 54, 47,  2, 40, 37, 18, 58,  4, 44, 27, 10,
       42,  9,  1, 67, 33, 17, 59, 80, 69, 24, 63, 19, 14, 26, 36, 43, 62,
       83, 82, 11, 66, 25,  8, 46, 21, 98, 68, 16, 57, 56, 77, 31, 99, 85,
       93, 84, 15, 53, 41, 65,  0, 88, 34, 75,  3, 95, 35, 72, 60, 49, 51,
       22, 74, 64, 70, 23, 86, 48, 90, 30, 81, 12, 76, 97,  5, 13],
      dtype=int64), 'cur_cost': 175956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101141.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([78, 13, 52, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14],
      dtype=int64), 'cur_cost': 179669.0, 'intermediate_solutions': [{'tour': array([12, 62, 60, 46, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 181772.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([46, 12, 62, 60, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 184134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 46, 12, 62, 60,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 185098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 46, 12, 62, 48,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 181870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 48, 46, 12, 62,  9, 77, 53, 88, 76, 32, 42, 91, 83, 22,  2, 15,
        0, 47, 80, 98, 27, 63, 58,  5, 93, 36, 35, 87, 49, 97, 69, 74, 57,
       25,  1, 23, 37, 94, 71, 51, 20, 61, 33, 75, 10, 72, 85, 17,  8, 52,
        3, 19, 41, 44,  6, 14, 11, 81, 79, 18, 67,  4, 50, 89, 34, 59, 43,
       68, 73, 65, 30, 13, 26, 95, 29, 84, 31, 99, 40, 55, 56, 21, 96, 28,
        7, 82, 16, 38, 66, 78, 70, 64, 86, 24, 90, 45, 39, 92, 54],
      dtype=int64), 'cur_cost': 182720.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 148222.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:16,084 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:16,084 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,090 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=31772.000, 多样性=0.974
2025-08-05 10:29:16,091 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:16,091 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:16,091 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:16,091 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.017365568279127514, 'best_improvement': -0.21234784599534476}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.005277650298301812}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.1504433940842588, 'recent_improvements': [-0.19742905988491924, -0.07257449797742667, 0.10345772828359835], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 21739.0, 'new_best_cost': 21739.0, 'quality_improvement': 0.0, 'old_diversity': 0.865, 'new_diversity': 0.865, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:16,092 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:16,092 - __main__ - INFO - kroA100 开始进化第 2 代
2025-08-05 10:29:16,092 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:16,093 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,093 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=31772.000, 多样性=0.974
2025-08-05 10:29:16,094 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:16,098 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.974
2025-08-05 10:29:16,098 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:16,100 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.865
2025-08-05 10:29:16,103 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:16,103 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:16,103 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:16,103 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:16,154 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -7980.643, 聚类评分: 0.000, 覆盖率: 0.165, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:16,154 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:16,155 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:16,155 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 10:29:16,161 - visualization.landscape_visualizer - INFO - 插值约束: 81 个点被约束到最小值 21739.00
2025-08-05 10:29:16,163 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.6%, 梯度: 6829.55 → 6378.97
2025-08-05 10:29:16,270 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_147_20250805_102916.html
2025-08-05 10:29:16,364 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_147_20250805_102916.html
2025-08-05 10:29:16,364 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 147
2025-08-05 10:29:16,364 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:16,364 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2610秒
2025-08-05 10:29:16,365 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7980.642857142855, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 3802318639.515306, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1646, 'fitness_entropy': 0.8576611402529865, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7980.643)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.165)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360956.1543546, 'performance_metrics': {}}}
2025-08-05 10:29:16,365 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:16,365 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:16,365 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:16,366 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:16,366 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:16,367 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:16,367 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:16,367 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:16,367 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:16,367 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:29:16,367 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:16,368 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:16,368 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:16,368 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:16,369 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:16,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,375 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,377 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,377 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31251.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,378 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31251.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 19, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 2, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 34311.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 42], 'cur_cost': 34193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 54, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 35665.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,378 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 31251.00)
2025-08-05 10:29:16,379 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:16,379 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:16,379 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,386 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,388 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32442.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,388 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 32442.0, 'intermediate_solutions': [{'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 33, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 92, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 78, 79, 91, 47, 99, 94, 4, 36, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 105603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 97, 15, 57, 44, 86, 68, 26, 80, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 101838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,388 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 32442.00)
2025-08-05 10:29:16,388 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:16,389 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:16,389 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,392 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,393 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35735.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,394 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35735.0, 'intermediate_solutions': [{'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 24, 8, 50, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 97879.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 37, 21, 41, 1, 53, 81, 84, 94, 70, 4, 85, 56, 89, 16, 83, 61, 59, 99, 82, 39, 11, 28, 67, 44, 76, 5, 58, 71, 52, 78, 46, 24, 8, 50, 80, 97, 73, 7, 64, 18, 10, 74, 66, 22, 0, 79, 91, 27, 90, 48, 62, 25, 69, 9, 30, 63, 54, 60, 31, 14, 20, 93, 87, 88, 92, 33, 2, 86, 95, 6, 34, 45, 32, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 102186.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [77, 12, 42, 26, 29, 43, 27, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98500.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,394 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 35735.00)
2025-08-05 10:29:16,394 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:16,394 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:16,394 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,398 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,399 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30553.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,399 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 30553.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 65, 17, 78, 52, 87, 21, 93, 69, 23, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 34996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 57, 66, 27, 92, 46, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 25, 64, 65, 69, 93, 21, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 42, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 50, 86, 98], 'cur_cost': 32716.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 85, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 35953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,400 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 30553.00)
2025-08-05 10:29:16,400 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:16,400 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:16,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,404 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,404 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,404 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,404 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,405 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36668.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,405 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 36668.0, 'intermediate_solutions': [{'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 49, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 75, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 139936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 39, 11, 28, 40, 67, 95, 6, 45, 46, 54, 12, 76, 5, 48, 41, 74, 18, 3, 1, 22, 33, 23, 0, 15], 'cur_cost': 138359.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 49, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 135896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,406 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 36668.00)
2025-08-05 10:29:16,406 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:16,406 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:16,407 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,412 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,412 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,412 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,413 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31359.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,413 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 31359.0, 'intermediate_solutions': [{'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 55, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 79, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103765.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 35, 87, 27, 5, 18, 55, 60, 94, 70, 95, 68, 92, 28, 49, 64, 98, 13, 51], 'cur_cost': 107387.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 96, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103303.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,413 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 31359.00)
2025-08-05 10:29:16,414 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:16,414 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,414 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,414 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 164601.0
2025-08-05 10:29:16,434 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:16,434 - ExploitationExpert - INFO - res_population_costs: [21739.0, 21782, 22022, 22246.0, 21511.0]
2025-08-05 10:29:16,434 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 80, 24, 50, 86, 56,  6,  8, 84, 67, 72, 68,
       63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38,
       29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54, 11, 19, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64)]
2025-08-05 10:29:16,436 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,437 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31251.0}, {'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 32442.0}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35735.0}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 30553.0}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 36668.0}, {'tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 31359.0}, {'tour': array([87, 88,  2, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73],
      dtype=int64), 'cur_cost': 164601.0}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101141.0}, {'tour': [78, 13, 52, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68, 87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88, 2, 39, 25, 10, 9, 35, 63, 28, 92, 41, 17, 95, 71, 0, 82, 33, 48, 55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97, 5, 19, 24, 74, 83, 7, 62, 42, 16, 91, 54, 1, 81, 89, 86, 44, 11, 61, 98, 36, 6, 38, 21, 29, 3, 77, 85, 37, 8, 58, 57, 4, 94, 53, 69, 14], 'cur_cost': 179669.0}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 148222.0}]
2025-08-05 10:29:16,437 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:16,438 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 380, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 380, 'cache_hits': 0, 'similarity_calculations': 1983, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,439 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([87, 88,  2, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73],
      dtype=int64), 'cur_cost': 164601.0, 'intermediate_solutions': [{'tour': array([80, 61, 71, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 161432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 80, 61, 71,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 163541.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 31, 80, 61, 71, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 162485.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([71, 31, 80, 61,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 161931.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([71,  8, 31, 80, 61, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 163608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,439 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 164601.00)
2025-08-05 10:29:16,439 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:16,439 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:16,439 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,443 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,443 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,443 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,444 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,444 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32858.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,444 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 32858.0, 'intermediate_solutions': [{'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 75, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 64, 13, 15], 'cur_cost': 112878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 3, 52, 17, 0, 27, 1, 4, 51, 8, 22, 14, 21, 48, 41, 65, 30, 93, 58, 62, 79, 78, 16, 97, 76, 11, 95, 94, 12, 68, 54, 38, 67, 86, 53, 49, 63, 28, 47, 26, 80, 99, 42, 77, 82, 72, 84, 19, 35, 46, 91, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101164.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 84, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 105139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,444 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 32858.00)
2025-08-05 10:29:16,445 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:16,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,445 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,445 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 182880.0
2025-08-05 10:29:16,467 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:16,468 - ExploitationExpert - INFO - res_population_costs: [21739.0, 21782, 22022, 22246.0, 21511.0]
2025-08-05 10:29:16,468 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 80, 24, 50, 86, 56,  6,  8, 84, 67, 72, 68,
       63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38,
       29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54, 11, 19, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64)]
2025-08-05 10:29:16,470 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,470 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31251.0}, {'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 32442.0}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35735.0}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 30553.0}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 36668.0}, {'tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 31359.0}, {'tour': array([87, 88,  2, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73],
      dtype=int64), 'cur_cost': 164601.0}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 32858.0}, {'tour': array([57, 59, 36, 39, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80],
      dtype=int64), 'cur_cost': 182880.0}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 148222.0}]
2025-08-05 10:29:16,472 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:29:16,472 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 381, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 381, 'cache_hits': 0, 'similarity_calculations': 1986, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,473 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([57, 59, 36, 39, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80],
      dtype=int64), 'cur_cost': 182880.0, 'intermediate_solutions': [{'tour': array([52, 13, 78, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 179811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 52, 13, 78, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 181734.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([75, 50, 52, 13, 78, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 180329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([78, 50, 52, 13, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 178106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([78, 75, 50, 52, 13, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 180171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,473 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 182880.00)
2025-08-05 10:29:16,473 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:16,473 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:16,473 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,486 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:16,487 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,487 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,487 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,487 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,487 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105225.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,488 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [55, 21, 78, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29, 45, 13, 38, 60, 7, 0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67, 24, 34, 82, 77, 54, 1, 28, 80, 33, 36, 84, 63, 19, 20, 17, 9, 69, 41, 39, 43, 72, 85, 46, 90, 5, 10, 92, 31, 59, 58, 14, 93, 83, 3, 52, 18, 71, 74, 35, 65, 61, 66, 23, 6, 62, 73, 42, 12, 75, 99, 70, 2, 32, 94, 8, 16, 91, 88, 30, 25, 49, 81, 4, 40, 79, 98], 'cur_cost': 105225.0, 'intermediate_solutions': [{'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 0, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 82, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 143936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 6, 27, 31, 34, 43, 50, 69, 8, 19, 22, 52, 60, 84, 67, 5, 93, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 145284.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 56, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 149067.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,488 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 105225.00)
2025-08-05 10:29:16,488 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:16,488 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:16,491 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31251.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 19, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 2, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 34311.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 42], 'cur_cost': 34193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 9, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 22, 97, 90, 54, 44, 31, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 42], 'cur_cost': 35665.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 32442.0, 'intermediate_solutions': [{'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 33, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 92, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 102236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 15, 57, 44, 86, 68, 26, 80, 97, 72, 1, 51, 53, 78, 79, 91, 47, 99, 94, 4, 36, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 105603.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 98, 18, 17, 9, 90, 85, 76, 34, 2, 75, 95, 40, 60, 61, 58, 83, 96, 71, 21, 20, 7, 10, 5, 55, 73, 64, 52, 22, 19, 8, 82, 50, 43, 92, 31, 6, 27, 48, 46, 16, 59, 67, 39, 12, 32, 70, 29, 13, 63, 81, 28, 38, 56, 11, 49, 33, 66, 30, 93, 35, 25, 3, 89, 88, 97, 15, 57, 44, 86, 68, 26, 80, 72, 1, 51, 53, 36, 4, 94, 99, 47, 91, 79, 78, 74, 69, 87, 14, 62, 23, 0, 24, 84, 54, 45, 77, 42, 41, 37], 'cur_cost': 101838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35735.0, 'intermediate_solutions': [{'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 27, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 24, 8, 50, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 97879.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [77, 12, 42, 26, 29, 43, 57, 19, 72, 36, 49, 37, 21, 41, 1, 53, 81, 84, 94, 70, 4, 85, 56, 89, 16, 83, 61, 59, 99, 82, 39, 11, 28, 67, 44, 76, 5, 58, 71, 52, 78, 46, 24, 8, 50, 80, 97, 73, 7, 64, 18, 10, 74, 66, 22, 0, 79, 91, 27, 90, 48, 62, 25, 69, 9, 30, 63, 54, 60, 31, 14, 20, 93, 87, 88, 92, 33, 2, 86, 95, 6, 34, 45, 32, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 102186.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [77, 12, 42, 26, 29, 43, 27, 57, 19, 72, 36, 49, 32, 45, 34, 6, 95, 86, 2, 33, 92, 88, 87, 93, 20, 14, 31, 60, 54, 63, 30, 9, 69, 25, 62, 48, 90, 91, 79, 0, 22, 66, 74, 10, 18, 64, 7, 73, 97, 80, 50, 8, 24, 46, 78, 52, 71, 58, 5, 76, 44, 67, 28, 11, 39, 82, 99, 59, 61, 83, 16, 89, 56, 85, 4, 70, 94, 84, 81, 53, 1, 41, 21, 37, 96, 65, 35, 55, 3, 15, 23, 98, 17, 13, 51, 47, 38, 75, 68, 40], 'cur_cost': 98500.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 30553.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 65, 17, 78, 52, 87, 21, 93, 69, 23, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 34996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 57, 66, 27, 92, 46, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 25, 64, 65, 69, 93, 21, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 42, 40, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 1, 43, 49, 72, 68, 80, 24, 60, 50, 86, 98], 'cur_cost': 32716.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 13, 2, 45, 28, 33, 82, 54, 11, 26, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 42, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 25, 96, 55, 85, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 92, 27, 66, 57, 98], 'cur_cost': 35953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 36668.0, 'intermediate_solutions': [{'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 49, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 75, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 139936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 49, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 39, 11, 28, 40, 67, 95, 6, 45, 46, 54, 12, 76, 5, 48, 41, 74, 18, 3, 1, 22, 33, 23, 0, 15], 'cur_cost': 138359.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [65, 98, 42, 17, 9, 43, 57, 19, 72, 2, 75, 51, 77, 61, 36, 32, 96, 71, 94, 20, 7, 10, 55, 84, 64, 52, 63, 30, 8, 82, 26, 62, 34, 31, 27, 91, 79, 86, 50, 60, 24, 80, 68, 47, 99, 53, 13, 66, 81, 92, 38, 56, 59, 44, 97, 90, 93, 35, 14, 16, 73, 88, 58, 83, 37, 69, 89, 78, 85, 87, 70, 21, 29, 4, 25, 1, 3, 18, 74, 41, 48, 5, 76, 12, 54, 46, 45, 6, 95, 67, 49, 40, 28, 11, 39, 22, 33, 23, 0, 15], 'cur_cost': 135896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 31359.0, 'intermediate_solutions': [{'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 55, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 79, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103765.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 96, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 35, 87, 27, 5, 18, 55, 60, 94, 70, 95, 68, 92, 28, 49, 64, 98, 13, 51], 'cur_cost': 107387.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [62, 69, 97, 7, 88, 91, 17, 37, 9, 96, 25, 73, 76, 45, 56, 33, 57, 26, 72, 86, 53, 63, 79, 78, 15, 48, 71, 22, 80, 67, 19, 54, 43, 42, 29, 40, 11, 2, 32, 39, 77, 85, 66, 82, 50, 34, 0, 20, 3, 23, 89, 30, 74, 90, 61, 84, 4, 59, 16, 58, 46, 44, 8, 24, 31, 41, 65, 52, 93, 21, 10, 83, 14, 6, 99, 38, 75, 47, 36, 12, 81, 1, 49, 28, 92, 68, 95, 70, 94, 60, 55, 18, 5, 27, 87, 35, 64, 98, 13, 51], 'cur_cost': 103303.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([87, 88,  2, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73],
      dtype=int64), 'cur_cost': 164601.0, 'intermediate_solutions': [{'tour': array([80, 61, 71, 31,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 161432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 80, 61, 71,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 163541.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 31, 80, 61, 71, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 162485.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([71, 31, 80, 61,  8, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 161931.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([71,  8, 31, 80, 61, 76,  9, 57, 83, 49, 33, 63,  3, 82, 13, 81, 65,
       26, 35, 16, 42,  1, 44, 68,  5, 52, 86, 64, 47, 32, 70, 22, 28, 39,
       56, 46, 97, 66, 29, 84, 91, 74, 14, 93, 48, 36, 30, 87, 53, 95, 17,
       24, 72, 98, 12, 92, 85, 88, 20, 11, 43, 38,  0, 10, 21, 15, 73, 50,
       90, 60, 59, 51, 78, 54, 94, 55, 79, 69, 89, 37, 19,  2, 77, 34, 67,
       25, 62, 96,  4, 75, 27, 99, 45, 40, 58, 18, 41, 23,  7,  6]), 'cur_cost': 163608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 32858.0, 'intermediate_solutions': [{'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 75, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 84, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 64, 13, 15], 'cur_cost': 112878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 3, 52, 17, 0, 27, 1, 4, 51, 8, 22, 14, 21, 48, 41, 65, 30, 93, 58, 62, 79, 78, 16, 97, 76, 11, 95, 94, 12, 68, 54, 38, 67, 86, 53, 49, 63, 28, 47, 26, 80, 99, 42, 77, 82, 72, 84, 19, 35, 46, 91, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 101164.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 6, 85, 2, 39, 7, 10, 34, 90, 44, 20, 5, 25, 71, 84, 69, 64, 23, 98, 73, 74, 87, 55, 31, 9, 89, 37, 66, 43, 57, 92, 91, 46, 35, 19, 72, 82, 77, 42, 99, 80, 26, 47, 28, 63, 49, 53, 86, 67, 38, 54, 68, 12, 94, 95, 11, 76, 97, 16, 78, 79, 62, 58, 93, 30, 65, 41, 48, 21, 14, 22, 8, 51, 4, 1, 27, 0, 17, 52, 3, 24, 29, 50, 81, 33, 56, 45, 60, 88, 96, 18, 83, 59, 61, 70, 36, 32, 75, 13, 15], 'cur_cost': 105139.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([57, 59, 36, 39, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80],
      dtype=int64), 'cur_cost': 182880.0, 'intermediate_solutions': [{'tour': array([52, 13, 78, 50, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 179811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 52, 13, 78, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 181734.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([75, 50, 52, 13, 78, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 180329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([78, 50, 52, 13, 75, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 178106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([78, 75, 50, 52, 13, 30, 99, 22, 32, 18, 67, 70, 60, 46, 51, 15, 68,
       87, 65, 59, 84, 23, 12, 45, 40, 47, 49, 64, 20, 76, 66, 80, 79, 88,
        2, 39, 25, 10,  9, 35, 63, 28, 92, 41, 17, 95, 71,  0, 82, 33, 48,
       55, 43, 93, 72, 96, 56, 26, 31, 73, 34, 90, 27, 97,  5, 19, 24, 74,
       83,  7, 62, 42, 16, 91, 54,  1, 81, 89, 86, 44, 11, 61, 98, 36,  6,
       38, 21, 29,  3, 77, 85, 37,  8, 58, 57,  4, 94, 53, 69, 14]), 'cur_cost': 180171.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [55, 21, 78, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29, 45, 13, 38, 60, 7, 0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67, 24, 34, 82, 77, 54, 1, 28, 80, 33, 36, 84, 63, 19, 20, 17, 9, 69, 41, 39, 43, 72, 85, 46, 90, 5, 10, 92, 31, 59, 58, 14, 93, 83, 3, 52, 18, 71, 74, 35, 65, 61, 66, 23, 6, 62, 73, 42, 12, 75, 99, 70, 2, 32, 94, 8, 16, 91, 88, 30, 25, 49, 81, 4, 40, 79, 98], 'cur_cost': 105225.0, 'intermediate_solutions': [{'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 0, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 82, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 143936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 56, 83, 96, 12, 92, 88, 24, 6, 27, 31, 34, 43, 50, 69, 8, 19, 22, 52, 60, 84, 67, 5, 93, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 145284.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [77, 15, 9, 26, 29, 90, 13, 70, 40, 36, 11, 32, 85, 95, 4, 83, 96, 12, 92, 88, 24, 93, 5, 67, 84, 60, 52, 22, 19, 8, 69, 50, 43, 34, 31, 27, 6, 48, 46, 16, 59, 74, 39, 18, 64, 99, 73, 97, 56, 66, 81, 28, 57, 76, 78, 61, 33, 58, 30, 44, 35, 14, 3, 89, 20, 82, 71, 38, 86, 37, 23, 17, 21, 72, 87, 51, 94, 65, 53, 1, 47, 91, 55, 79, 41, 7, 45, 54, 0, 68, 42, 80, 63, 49, 2, 10, 98, 62, 25, 75], 'cur_cost': 149067.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:16,491 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:16,491 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,496 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30553.000, 多样性=0.945
2025-08-05 10:29:16,496 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:16,496 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:16,496 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:16,497 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.13305221609117615, 'best_improvement': 0.038367115699357926}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02921707372745931}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.027604464849149576, 'recent_improvements': [-0.07257449797742667, 0.10345772828359835, -0.017365568279127514], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 21511.0, 'new_best_cost': 21511.0, 'quality_improvement': 0.0, 'old_diversity': 0.853, 'new_diversity': 0.853, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:16,498 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:16,498 - __main__ - INFO - kroA100 开始进化第 3 代
2025-08-05 10:29:16,498 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:16,499 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,499 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30553.000, 多样性=0.945
2025-08-05 10:29:16,500 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:16,503 - PathExpert - INFO - 路径结构分析完成: 公共边数量=69, 路径相似性=0.945
2025-08-05 10:29:16,504 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:16,507 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.853
2025-08-05 10:29:16,508 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:16,509 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:16,509 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:16,509 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:16,565 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -16603.640, 聚类评分: 0.000, 覆盖率: 0.166, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:16,565 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:16,565 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:16,566 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 10:29:16,569 - visualization.landscape_visualizer - INFO - 插值约束: 382 个点被约束到最小值 21511.00
2025-08-05 10:29:16,571 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 6330.42 → 5872.48
2025-08-05 10:29:16,691 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\landscape_kroA100_iter_148_20250805_102916.html
2025-08-05 10:29:16,785 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_kroA100\dashboard_kroA100_iter_148_20250805_102916.html
2025-08-05 10:29:16,785 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 148
2025-08-05 10:29:16,786 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:16,786 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2783秒
2025-08-05 10:29:16,786 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -16603.640000000003, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 2097089603.5477333, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1655, 'fitness_entropy': 0.5194602975157968, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -16603.640)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.166)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360956.5658317, 'performance_metrics': {}}}
2025-08-05 10:29:16,786 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:16,786 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:16,787 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:16,787 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:16,787 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:16,788 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:16,788 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:16,788 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:16,788 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:16,788 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:16,788 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:16,789 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:16,789 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:16,789 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:16,789 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:16,789 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,791 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 100
2025-08-05 10:29:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,793 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112891.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,793 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [87, 1, 19, 11, 45, 28, 34, 70, 40, 6, 8, 51, 50, 60, 4, 80, 68, 72, 67, 84, 38, 29, 95, 63, 23, 17, 36, 52, 33, 12, 21, 94, 81, 47, 99, 3, 96, 13, 2, 30, 88, 39, 53, 54, 66, 27, 82, 57, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 85, 56, 77, 69, 98, 37, 32, 75, 78, 61, 86, 15, 64, 93, 55, 65, 79, 25, 42, 92, 91, 5, 26, 48, 62, 89, 18, 74, 49, 46, 0, 24, 41, 43, 7], 'cur_cost': 112891.0, 'intermediate_solutions': [{'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 98, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 36, 46, 92, 27, 66, 57, 42], 'cur_cost': 41969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 30, 79, 55, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 23, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 36611.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,793 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 112891.00)
2025-08-05 10:29:16,793 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:16,793 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:16,793 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,797 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,798 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31332.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,798 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 4, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 38, 29, 84, 67, 72, 49, 43, 1, 53, 39, 63, 68, 80, 24, 60, 50, 86, 56, 6, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 42, 36, 32, 75, 12, 94, 81], 'cur_cost': 31332.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 97, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 86, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 35709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 27, 92, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 66, 57], 'cur_cost': 34479.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 11, 26, 85, 34, 96, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 36711.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,798 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 31332.00)
2025-08-05 10:29:16,799 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:16,799 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:16,799 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,803 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34406.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,805 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 21, 6, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34406.0, 'intermediate_solutions': [{'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 85, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 63, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 40697.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 33, 28, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 36188.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 15, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 37049.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,805 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 34406.00)
2025-08-05 10:29:16,805 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:16,805 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:16,805 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,809 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,809 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,809 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,809 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,810 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,810 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33207.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,810 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 8, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 66, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 86, 50, 76, 59, 61, 42], 'cur_cost': 33207.0, 'intermediate_solutions': [{'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 41, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 85, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 37968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 18, 89, 62, 48, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 16, 14, 10, 31, 44, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 32990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 53, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35560.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,810 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 33207.00)
2025-08-05 10:29:16,810 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:16,811 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:16,811 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,814 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,815 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30539.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,815 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 19, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 76, 59, 61, 85, 26, 34, 42], 'cur_cost': 30539.0, 'intermediate_solutions': [{'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 15, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 97, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 43490.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 27, 66, 1, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 38788.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 89, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 18, 74, 46, 98, 42], 'cur_cost': 37947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,816 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 30539.00)
2025-08-05 10:29:16,816 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:16,816 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:16,816 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,819 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 100
2025-08-05 10:29:16,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32314.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,821 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 4, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 32314.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 5, 16, 10, 31, 90, 97, 22, 44, 46, 62, 14, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 34437.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 35003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 2, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 10, 59, 61], 'cur_cost': 32979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,821 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 32314.00)
2025-08-05 10:29:16,821 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:16,821 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,821 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,822 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 162062.0
2025-08-05 10:29:16,838 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:16,838 - ExploitationExpert - INFO - res_population_costs: [21511.0, 21739.0, 21782, 22022, 22246.0]
2025-08-05 10:29:16,838 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 66, 57, 60, 80, 24, 50, 86, 56,  6,  8, 84, 67, 72, 68,
       63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38,
       29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54, 11, 19, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64)]
2025-08-05 10:29:16,842 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,842 - ExploitationExpert - INFO - populations: [{'tour': [87, 1, 19, 11, 45, 28, 34, 70, 40, 6, 8, 51, 50, 60, 4, 80, 68, 72, 67, 84, 38, 29, 95, 63, 23, 17, 36, 52, 33, 12, 21, 94, 81, 47, 99, 3, 96, 13, 2, 30, 88, 39, 53, 54, 66, 27, 82, 57, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 85, 56, 77, 69, 98, 37, 32, 75, 78, 61, 86, 15, 64, 93, 55, 65, 79, 25, 42, 92, 91, 5, 26, 48, 62, 89, 18, 74, 49, 46, 0, 24, 41, 43, 7], 'cur_cost': 112891.0}, {'tour': [0, 8, 4, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 38, 29, 84, 67, 72, 49, 43, 1, 53, 39, 63, 68, 80, 24, 60, 50, 86, 56, 6, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 42, 36, 32, 75, 12, 94, 81], 'cur_cost': 31332.0}, {'tour': [0, 21, 6, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34406.0}, {'tour': [0, 15, 8, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 66, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 86, 50, 76, 59, 61, 42], 'cur_cost': 33207.0}, {'tour': [0, 11, 19, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 76, 59, 61, 85, 26, 34, 42], 'cur_cost': 30539.0}, {'tour': [0, 2, 4, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 32314.0}, {'tour': array([20,  5, 48, 93, 45, 53, 40, 46, 22, 66, 65,  1, 57,  7, 12, 95, 68,
       90, 38, 84, 77, 94, 10, 31, 43, 60, 55,  2, 19, 80, 23, 64, 18, 85,
       98, 83, 59, 89, 35, 96, 56, 14, 51, 82, 73, 34, 58,  4, 67, 49, 17,
       50, 62, 26, 24, 78, 42, 76, 47, 13, 30, 71,  9, 21,  6, 99, 97, 87,
       16, 28, 37,  0, 41,  8, 79, 39, 92, 63, 72, 74, 61, 44, 86, 70, 81,
       33, 25, 32, 88, 54,  3, 29, 36, 11, 69, 52, 15, 91, 27, 75],
      dtype=int64), 'cur_cost': 162062.0}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 32858.0}, {'tour': [57, 59, 36, 39, 12, 75, 15, 28, 3, 83, 16, 18, 24, 20, 74, 67, 62, 32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38, 7, 66, 89, 95, 84, 49, 6, 9, 19, 23, 58, 51, 4, 61, 76, 93, 11, 26, 56, 73, 53, 54, 78, 46, 63, 88, 42, 40, 86, 64, 70, 5, 69, 14, 68, 31, 85, 44, 65, 55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71, 8, 79, 2, 87, 92, 35, 45, 98, 1, 72, 0, 25, 90, 52, 60, 17, 82, 10, 21, 80], 'cur_cost': 182880.0}, {'tour': [55, 21, 78, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29, 45, 13, 38, 60, 7, 0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67, 24, 34, 82, 77, 54, 1, 28, 80, 33, 36, 84, 63, 19, 20, 17, 9, 69, 41, 39, 43, 72, 85, 46, 90, 5, 10, 92, 31, 59, 58, 14, 93, 83, 3, 52, 18, 71, 74, 35, 65, 61, 66, 23, 6, 62, 73, 42, 12, 75, 99, 70, 2, 32, 94, 8, 16, 91, 88, 30, 25, 49, 81, 4, 40, 79, 98], 'cur_cost': 105225.0}]
2025-08-05 10:29:16,843 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:16,843 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 382, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 382, 'cache_hits': 0, 'similarity_calculations': 1990, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,844 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([20,  5, 48, 93, 45, 53, 40, 46, 22, 66, 65,  1, 57,  7, 12, 95, 68,
       90, 38, 84, 77, 94, 10, 31, 43, 60, 55,  2, 19, 80, 23, 64, 18, 85,
       98, 83, 59, 89, 35, 96, 56, 14, 51, 82, 73, 34, 58,  4, 67, 49, 17,
       50, 62, 26, 24, 78, 42, 76, 47, 13, 30, 71,  9, 21,  6, 99, 97, 87,
       16, 28, 37,  0, 41,  8, 79, 39, 92, 63, 72, 74, 61, 44, 86, 70, 81,
       33, 25, 32, 88, 54,  3, 29, 36, 11, 69, 52, 15, 91, 27, 75],
      dtype=int64), 'cur_cost': 162062.0, 'intermediate_solutions': [{'tour': array([ 2, 88, 87, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164644.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  2, 88, 87, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 163802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([93, 30,  2, 88, 87, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164536.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([87, 30,  2, 88, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([87, 93, 30,  2, 88, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 162693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,844 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 162062.00)
2025-08-05 10:29:16,844 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:16,844 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:16,845 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:16,857 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 100
2025-08-05 10:29:16,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:16,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103963.0, 路径长度: 100, 收集中间解: 3
2025-08-05 10:29:16,859 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 44, 57, 49, 72, 45, 33, 59, 56, 84, 8, 99, 60, 0, 35, 21, 14, 50, 61, 73, 46, 98, 15, 96, 58, 91, 30, 89, 97, 78, 7, 53, 81, 66, 62, 74, 3, 9, 83, 52, 48, 17, 20, 31, 90, 37, 24, 67, 6, 22, 54, 76, 10, 27, 80, 77, 95, 4, 29, 47, 75, 63, 43, 36, 1, 92, 11, 86, 40, 28, 26, 2, 94, 68, 85, 82, 38, 42, 19, 34, 12, 41, 88, 39, 51, 79, 25, 93, 65, 5, 87, 23, 71, 55, 64, 16, 69, 13, 70, 32], 'cur_cost': 103963.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 84, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 44, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 38762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 85, 26, 11, 54, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 33631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 35, 76, 59, 61, 42, 25], 'cur_cost': 35904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:16,859 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 103963.00)
2025-08-05 10:29:16,859 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:16,859 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,860 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,860 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 180768.0
2025-08-05 10:29:16,879 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:16,879 - ExploitationExpert - INFO - res_population_costs: [21511.0, 21739.0, 21782, 22022, 22246.0]
2025-08-05 10:29:16,879 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 66, 57, 60, 80, 24, 50, 86, 56,  6,  8, 84, 67, 72, 68,
       63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38,
       29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54, 11, 19, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64)]
2025-08-05 10:29:16,882 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,882 - ExploitationExpert - INFO - populations: [{'tour': [87, 1, 19, 11, 45, 28, 34, 70, 40, 6, 8, 51, 50, 60, 4, 80, 68, 72, 67, 84, 38, 29, 95, 63, 23, 17, 36, 52, 33, 12, 21, 94, 81, 47, 99, 3, 96, 13, 2, 30, 88, 39, 53, 54, 66, 27, 82, 57, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 85, 56, 77, 69, 98, 37, 32, 75, 78, 61, 86, 15, 64, 93, 55, 65, 79, 25, 42, 92, 91, 5, 26, 48, 62, 89, 18, 74, 49, 46, 0, 24, 41, 43, 7], 'cur_cost': 112891.0}, {'tour': [0, 8, 4, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 38, 29, 84, 67, 72, 49, 43, 1, 53, 39, 63, 68, 80, 24, 60, 50, 86, 56, 6, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 42, 36, 32, 75, 12, 94, 81], 'cur_cost': 31332.0}, {'tour': [0, 21, 6, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34406.0}, {'tour': [0, 15, 8, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 66, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 86, 50, 76, 59, 61, 42], 'cur_cost': 33207.0}, {'tour': [0, 11, 19, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 76, 59, 61, 85, 26, 34, 42], 'cur_cost': 30539.0}, {'tour': [0, 2, 4, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 32314.0}, {'tour': array([20,  5, 48, 93, 45, 53, 40, 46, 22, 66, 65,  1, 57,  7, 12, 95, 68,
       90, 38, 84, 77, 94, 10, 31, 43, 60, 55,  2, 19, 80, 23, 64, 18, 85,
       98, 83, 59, 89, 35, 96, 56, 14, 51, 82, 73, 34, 58,  4, 67, 49, 17,
       50, 62, 26, 24, 78, 42, 76, 47, 13, 30, 71,  9, 21,  6, 99, 97, 87,
       16, 28, 37,  0, 41,  8, 79, 39, 92, 63, 72, 74, 61, 44, 86, 70, 81,
       33, 25, 32, 88, 54,  3, 29, 36, 11, 69, 52, 15, 91, 27, 75],
      dtype=int64), 'cur_cost': 162062.0}, {'tour': [18, 44, 57, 49, 72, 45, 33, 59, 56, 84, 8, 99, 60, 0, 35, 21, 14, 50, 61, 73, 46, 98, 15, 96, 58, 91, 30, 89, 97, 78, 7, 53, 81, 66, 62, 74, 3, 9, 83, 52, 48, 17, 20, 31, 90, 37, 24, 67, 6, 22, 54, 76, 10, 27, 80, 77, 95, 4, 29, 47, 75, 63, 43, 36, 1, 92, 11, 86, 40, 28, 26, 2, 94, 68, 85, 82, 38, 42, 19, 34, 12, 41, 88, 39, 51, 79, 25, 93, 65, 5, 87, 23, 71, 55, 64, 16, 69, 13, 70, 32], 'cur_cost': 103963.0}, {'tour': array([53, 70, 24, 64, 57, 91, 56, 10, 84, 31,  2, 42,  7, 19, 89, 21, 72,
       90, 15, 51, 58, 26, 43, 78, 27, 12, 14,  5, 86, 66, 23, 34, 69, 46,
       63,  8, 52, 94, 73, 30, 54, 36, 11,  1, 92,  3, 45, 49, 98, 39, 76,
       68, 37, 55, 47, 50, 82, 61, 22, 41, 32, 75, 95, 67,  9, 33, 20,  6,
       97, 80, 44, 38, 83, 18, 48, 17, 77, 13, 79,  0, 16, 93, 99,  4, 96,
       29, 62, 65, 74, 71, 59, 28, 88, 25, 87, 60, 81, 85, 35, 40],
      dtype=int64), 'cur_cost': 180768.0}, {'tour': [55, 21, 78, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29, 45, 13, 38, 60, 7, 0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67, 24, 34, 82, 77, 54, 1, 28, 80, 33, 36, 84, 63, 19, 20, 17, 9, 69, 41, 39, 43, 72, 85, 46, 90, 5, 10, 92, 31, 59, 58, 14, 93, 83, 3, 52, 18, 71, 74, 35, 65, 61, 66, 23, 6, 62, 73, 42, 12, 75, 99, 70, 2, 32, 94, 8, 16, 91, 88, 30, 25, 49, 81, 4, 40, 79, 98], 'cur_cost': 105225.0}]
2025-08-05 10:29:16,884 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:16,884 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 383, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 383, 'cache_hits': 0, 'similarity_calculations': 1995, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,886 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([53, 70, 24, 64, 57, 91, 56, 10, 84, 31,  2, 42,  7, 19, 89, 21, 72,
       90, 15, 51, 58, 26, 43, 78, 27, 12, 14,  5, 86, 66, 23, 34, 69, 46,
       63,  8, 52, 94, 73, 30, 54, 36, 11,  1, 92,  3, 45, 49, 98, 39, 76,
       68, 37, 55, 47, 50, 82, 61, 22, 41, 32, 75, 95, 67,  9, 33, 20,  6,
       97, 80, 44, 38, 83, 18, 48, 17, 77, 13, 79,  0, 16, 93, 99,  4, 96,
       29, 62, 65, 74, 71, 59, 28, 88, 25, 87, 60, 81, 85, 35, 40],
      dtype=int64), 'cur_cost': 180768.0, 'intermediate_solutions': [{'tour': array([36, 59, 57, 39, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 36, 59, 57, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 39, 36, 59, 57, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 185688.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 39, 36, 59, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 12, 39, 36, 59, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 186296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,886 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 180768.00)
2025-08-05 10:29:16,886 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:16,886 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:16,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:16,887 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 172201.0
2025-08-05 10:29:16,909 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:16,910 - ExploitationExpert - INFO - res_population_costs: [21511.0, 21739.0, 21782, 22022, 22246.0]
2025-08-05 10:29:16,910 - ExploitationExpert - INFO - res_populations: [array([ 0, 92, 27, 66, 57, 60, 80, 24, 50, 86, 56,  6,  8, 84, 67, 72, 68,
       63, 39, 53,  1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38,
       29, 47, 99, 40, 70, 13,  2, 42, 45, 28, 33, 82, 54, 11, 19, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53,  1, 81,
       94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 67, 84, 29, 47, 99, 40, 70,
       13,  2, 42, 45, 28, 33, 82, 54, 11, 26, 34, 85, 19, 56,  6,  8, 86,
       50, 76, 61, 59, 22, 97, 90, 44, 14, 16, 10, 31, 46, 62,  5, 48, 89,
        9, 83, 71, 20, 73, 58, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62, 46, 92, 27, 66, 57, 68, 63, 39, 53,  1, 43, 72, 49, 81, 94,
       12, 75, 32, 36,  4, 95, 77, 51, 47, 99, 40, 70, 13,  2, 42, 45, 28,
       29, 38, 84, 67, 80, 24, 60, 50, 86, 56,  8,  6, 33, 82, 54, 11, 19,
       26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20,
       71,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21, 69, 65, 25,
       64,  3, 18, 89, 48,  5, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 15, 87,
       93, 17, 23, 37, 98, 35, 83, 78, 52, 18, 74,  5, 48, 89,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 38, 29, 28,
       45, 42,  2, 13, 70, 40, 99, 47, 51, 77, 95,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96, 25, 65, 64,  3, 18, 74,  5, 48,
       89, 78, 52, 87, 15, 69, 21, 93, 17, 23, 37, 98, 35, 83,  9, 71, 20,
       73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26, 19,
       11, 54, 82, 33,  6,  8, 56, 86, 50, 60, 24, 80, 67, 84, 28, 45, 42,
        2, 13, 70, 40, 99, 47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94,
       81, 49, 72, 43,  1, 53, 39, 63, 68, 57, 66, 27, 92, 46, 62],
      dtype=int64)]
2025-08-05 10:29:16,912 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:16,912 - ExploitationExpert - INFO - populations: [{'tour': [87, 1, 19, 11, 45, 28, 34, 70, 40, 6, 8, 51, 50, 60, 4, 80, 68, 72, 67, 84, 38, 29, 95, 63, 23, 17, 36, 52, 33, 12, 21, 94, 81, 47, 99, 3, 96, 13, 2, 30, 88, 39, 53, 54, 66, 27, 82, 57, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 85, 56, 77, 69, 98, 37, 32, 75, 78, 61, 86, 15, 64, 93, 55, 65, 79, 25, 42, 92, 91, 5, 26, 48, 62, 89, 18, 74, 49, 46, 0, 24, 41, 43, 7], 'cur_cost': 112891.0}, {'tour': [0, 8, 4, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 38, 29, 84, 67, 72, 49, 43, 1, 53, 39, 63, 68, 80, 24, 60, 50, 86, 56, 6, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 42, 36, 32, 75, 12, 94, 81], 'cur_cost': 31332.0}, {'tour': [0, 21, 6, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34406.0}, {'tour': [0, 15, 8, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 66, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 86, 50, 76, 59, 61, 42], 'cur_cost': 33207.0}, {'tour': [0, 11, 19, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 76, 59, 61, 85, 26, 34, 42], 'cur_cost': 30539.0}, {'tour': [0, 2, 4, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 32314.0}, {'tour': array([20,  5, 48, 93, 45, 53, 40, 46, 22, 66, 65,  1, 57,  7, 12, 95, 68,
       90, 38, 84, 77, 94, 10, 31, 43, 60, 55,  2, 19, 80, 23, 64, 18, 85,
       98, 83, 59, 89, 35, 96, 56, 14, 51, 82, 73, 34, 58,  4, 67, 49, 17,
       50, 62, 26, 24, 78, 42, 76, 47, 13, 30, 71,  9, 21,  6, 99, 97, 87,
       16, 28, 37,  0, 41,  8, 79, 39, 92, 63, 72, 74, 61, 44, 86, 70, 81,
       33, 25, 32, 88, 54,  3, 29, 36, 11, 69, 52, 15, 91, 27, 75],
      dtype=int64), 'cur_cost': 162062.0}, {'tour': [18, 44, 57, 49, 72, 45, 33, 59, 56, 84, 8, 99, 60, 0, 35, 21, 14, 50, 61, 73, 46, 98, 15, 96, 58, 91, 30, 89, 97, 78, 7, 53, 81, 66, 62, 74, 3, 9, 83, 52, 48, 17, 20, 31, 90, 37, 24, 67, 6, 22, 54, 76, 10, 27, 80, 77, 95, 4, 29, 47, 75, 63, 43, 36, 1, 92, 11, 86, 40, 28, 26, 2, 94, 68, 85, 82, 38, 42, 19, 34, 12, 41, 88, 39, 51, 79, 25, 93, 65, 5, 87, 23, 71, 55, 64, 16, 69, 13, 70, 32], 'cur_cost': 103963.0}, {'tour': array([53, 70, 24, 64, 57, 91, 56, 10, 84, 31,  2, 42,  7, 19, 89, 21, 72,
       90, 15, 51, 58, 26, 43, 78, 27, 12, 14,  5, 86, 66, 23, 34, 69, 46,
       63,  8, 52, 94, 73, 30, 54, 36, 11,  1, 92,  3, 45, 49, 98, 39, 76,
       68, 37, 55, 47, 50, 82, 61, 22, 41, 32, 75, 95, 67,  9, 33, 20,  6,
       97, 80, 44, 38, 83, 18, 48, 17, 77, 13, 79,  0, 16, 93, 99,  4, 96,
       29, 62, 65, 74, 71, 59, 28, 88, 25, 87, 60, 81, 85, 35, 40],
      dtype=int64), 'cur_cost': 180768.0}, {'tour': array([39, 24, 45, 50, 84, 59, 92, 81, 38, 66,  1, 49, 57, 65, 55, 17, 28,
       19,  0, 67, 41,  6, 44, 27, 89, 96,  3, 76, 31, 21, 97, 53, 70, 13,
       37, 29, 80, 47, 23, 61, 85, 36, 83, 18, 71, 93, 95, 25, 75, 22, 26,
       78, 99, 88, 54, 72,  5,  7,  8, 40, 68, 43, 90, 12, 63, 10, 82,  4,
       30, 94, 48, 58, 15, 87, 64, 56, 52, 77, 74, 14, 33, 91, 60, 46, 34,
        9, 79, 42, 20, 35, 11, 98, 73, 16,  2, 86, 69, 51, 62, 32],
      dtype=int64), 'cur_cost': 172201.0}]
2025-08-05 10:29:16,914 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:29:16,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 384, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 384, 'cache_hits': 0, 'similarity_calculations': 2001, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:16,915 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([39, 24, 45, 50, 84, 59, 92, 81, 38, 66,  1, 49, 57, 65, 55, 17, 28,
       19,  0, 67, 41,  6, 44, 27, 89, 96,  3, 76, 31, 21, 97, 53, 70, 13,
       37, 29, 80, 47, 23, 61, 85, 36, 83, 18, 71, 93, 95, 25, 75, 22, 26,
       78, 99, 88, 54, 72,  5,  7,  8, 40, 68, 43, 90, 12, 63, 10, 82,  4,
       30, 94, 48, 58, 15, 87, 64, 56, 52, 77, 74, 14, 33, 91, 60, 46, 34,
        9, 79, 42, 20, 35, 11, 98, 73, 16,  2, 86, 69, 51, 62, 32],
      dtype=int64), 'cur_cost': 172201.0, 'intermediate_solutions': [{'tour': array([78, 21, 55, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 103962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 78, 21, 55, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 105047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([89, 27, 78, 21, 55, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 27, 78, 21, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55, 89, 27, 78, 21, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:16,916 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 172201.00)
2025-08-05 10:29:16,916 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:16,916 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:16,919 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [87, 1, 19, 11, 45, 28, 34, 70, 40, 6, 8, 51, 50, 60, 4, 80, 68, 72, 67, 84, 38, 29, 95, 63, 23, 17, 36, 52, 33, 12, 21, 94, 81, 47, 99, 3, 96, 13, 2, 30, 88, 39, 53, 54, 66, 27, 82, 57, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 85, 56, 77, 69, 98, 37, 32, 75, 78, 61, 86, 15, 64, 93, 55, 65, 79, 25, 42, 92, 91, 5, 26, 48, 62, 89, 18, 74, 49, 46, 0, 24, 41, 43, 7], 'cur_cost': 112891.0, 'intermediate_solutions': [{'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 98, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 36, 46, 92, 27, 66, 57, 42], 'cur_cost': 41969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 30, 79, 55, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 31925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 19, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 63, 39, 53, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 23, 82, 54, 11, 26, 85, 34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 36611.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 38, 29, 84, 67, 72, 49, 43, 1, 53, 39, 63, 68, 80, 24, 60, 50, 86, 56, 6, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 54, 82, 33, 42, 36, 32, 75, 12, 94, 81], 'cur_cost': 31332.0, 'intermediate_solutions': [{'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 97, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 86, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 35709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 10, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 27, 92, 46, 98, 25, 74, 18, 89, 62, 48, 5, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 66, 57], 'cur_cost': 34479.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 10, 11, 26, 85, 34, 96, 19, 56, 6, 8, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 2, 45, 28, 33, 82, 54, 42, 61, 59, 76, 22, 97, 90, 44, 31, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57], 'cur_cost': 36711.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 6, 18, 74, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 9, 83, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 35, 37, 23, 17, 78, 52, 87, 15, 93, 69, 65, 64, 3, 25, 98], 'cur_cost': 34406.0, 'intermediate_solutions': [{'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 85, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 63, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 40697.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 33, 28, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 36188.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 9, 6, 8, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 83, 35, 15, 37, 23, 17, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 37049.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 8, 17, 23, 37, 35, 98, 83, 9, 71, 20, 73, 58, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 66, 27, 92, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 86, 50, 76, 59, 61, 42], 'cur_cost': 33207.0, 'intermediate_solutions': [{'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 41, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 85, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 37968.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 18, 89, 62, 48, 91, 7, 41, 88, 30, 79, 55, 96, 3, 64, 65, 69, 93, 21, 15, 87, 52, 78, 17, 23, 37, 35, 83, 9, 71, 58, 20, 73, 16, 14, 10, 31, 44, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 32990.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 11, 8, 6, 56, 86, 50, 60, 24, 80, 68, 72, 49, 43, 1, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 26, 85, 34, 19, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 53, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 48, 62, 89, 18, 74, 25, 98, 46, 92, 27, 66, 57, 42], 'cur_cost': 35560.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 19, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 6, 8, 56, 86, 50, 76, 59, 61, 85, 26, 34, 42], 'cur_cost': 30539.0, 'intermediate_solutions': [{'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 15, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 97, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 43490.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 27, 66, 1, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 46, 98, 42], 'cur_cost': 38788.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 3, 2, 45, 28, 13, 70, 40, 99, 47, 51, 77, 95, 4, 36, 32, 75, 12, 94, 81, 49, 43, 72, 67, 84, 38, 29, 33, 82, 54, 6, 8, 56, 86, 50, 60, 24, 80, 68, 63, 39, 53, 1, 66, 27, 92, 57, 76, 59, 61, 85, 26, 34, 19, 22, 97, 90, 44, 31, 10, 14, 16, 73, 89, 20, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 25, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 18, 74, 46, 98, 42], 'cur_cost': 37947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 20, 73, 58, 71, 9, 83, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 5, 48, 62, 89, 18, 74, 25, 98, 16, 14, 10, 31, 90, 97, 22, 44, 46, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 32314.0, 'intermediate_solutions': [{'tour': [0, 7, 2, 5, 16, 10, 31, 90, 97, 22, 44, 46, 62, 14, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 34437.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 2, 14, 16, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 70, 99, 47, 81, 94, 12, 75, 32, 36, 4, 51, 77, 95, 29, 38, 84, 67, 63, 39, 53, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 59, 61], 'cur_cost': 35003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 2, 14, 16, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 9, 83, 71, 20, 73, 58, 35, 37, 23, 17, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 91, 74, 18, 25, 98, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 28, 45, 42, 33, 82, 54, 11, 26, 85, 34, 19, 56, 6, 8, 86, 50, 76, 10, 59, 61], 'cur_cost': 32979.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([20,  5, 48, 93, 45, 53, 40, 46, 22, 66, 65,  1, 57,  7, 12, 95, 68,
       90, 38, 84, 77, 94, 10, 31, 43, 60, 55,  2, 19, 80, 23, 64, 18, 85,
       98, 83, 59, 89, 35, 96, 56, 14, 51, 82, 73, 34, 58,  4, 67, 49, 17,
       50, 62, 26, 24, 78, 42, 76, 47, 13, 30, 71,  9, 21,  6, 99, 97, 87,
       16, 28, 37,  0, 41,  8, 79, 39, 92, 63, 72, 74, 61, 44, 86, 70, 81,
       33, 25, 32, 88, 54,  3, 29, 36, 11, 69, 52, 15, 91, 27, 75],
      dtype=int64), 'cur_cost': 162062.0, 'intermediate_solutions': [{'tour': array([ 2, 88, 87, 30, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164644.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  2, 88, 87, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 163802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([93, 30,  2, 88, 87, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164536.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([87, 30,  2, 88, 93, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 164601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([87, 93, 30,  2, 88, 76, 63,  0, 92, 37, 48, 23, 14, 16, 28, 90, 59,
       32, 50, 26, 17, 62, 35, 94, 25, 31,  9, 44, 96,  8, 42, 89, 43, 33,
       97, 18,  6, 85, 77, 64,  7, 91,  4, 61, 39,  3, 70, 65, 38, 29,  5,
       68, 67, 27, 19, 71, 83, 99, 13, 55, 80, 53, 74, 34, 11, 54, 56, 41,
       69, 98, 21, 75, 40, 45, 58, 86, 49, 66, 95, 47, 79, 60, 15, 57, 36,
        1, 82, 78, 10, 20, 72, 12, 52, 22, 84, 46, 24, 81, 51, 73]), 'cur_cost': 162693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 44, 57, 49, 72, 45, 33, 59, 56, 84, 8, 99, 60, 0, 35, 21, 14, 50, 61, 73, 46, 98, 15, 96, 58, 91, 30, 89, 97, 78, 7, 53, 81, 66, 62, 74, 3, 9, 83, 52, 48, 17, 20, 31, 90, 37, 24, 67, 6, 22, 54, 76, 10, 27, 80, 77, 95, 4, 29, 47, 75, 63, 43, 36, 1, 92, 11, 86, 40, 28, 26, 2, 94, 68, 85, 82, 38, 42, 19, 34, 12, 41, 88, 39, 51, 79, 25, 93, 65, 5, 87, 23, 71, 55, 64, 16, 69, 13, 70, 32], 'cur_cost': 103963.0, 'intermediate_solutions': [{'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 84, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 44, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 38762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 35, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 85, 26, 11, 54, 34, 19, 56, 8, 86, 50, 76, 59, 61, 42, 25], 'cur_cost': 33631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 9, 16, 14, 10, 31, 90, 97, 22, 44, 46, 62, 5, 48, 89, 78, 52, 87, 15, 21, 93, 69, 65, 64, 3, 96, 55, 79, 30, 88, 41, 7, 91, 74, 18, 17, 23, 37, 98, 83, 71, 20, 73, 58, 92, 27, 66, 57, 60, 24, 80, 68, 72, 49, 43, 1, 53, 39, 63, 67, 84, 38, 29, 95, 77, 51, 4, 36, 32, 75, 12, 94, 81, 47, 99, 70, 40, 13, 2, 45, 28, 33, 82, 54, 11, 26, 85, 34, 19, 56, 8, 86, 50, 35, 76, 59, 61, 42, 25], 'cur_cost': 35904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 70, 24, 64, 57, 91, 56, 10, 84, 31,  2, 42,  7, 19, 89, 21, 72,
       90, 15, 51, 58, 26, 43, 78, 27, 12, 14,  5, 86, 66, 23, 34, 69, 46,
       63,  8, 52, 94, 73, 30, 54, 36, 11,  1, 92,  3, 45, 49, 98, 39, 76,
       68, 37, 55, 47, 50, 82, 61, 22, 41, 32, 75, 95, 67,  9, 33, 20,  6,
       97, 80, 44, 38, 83, 18, 48, 17, 77, 13, 79,  0, 16, 93, 99,  4, 96,
       29, 62, 65, 74, 71, 59, 28, 88, 25, 87, 60, 81, 85, 35, 40],
      dtype=int64), 'cur_cost': 180768.0, 'intermediate_solutions': [{'tour': array([36, 59, 57, 39, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 36, 59, 57, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12, 39, 36, 59, 57, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 185688.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 39, 36, 59, 12, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 183990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 12, 39, 36, 59, 75, 15, 28,  3, 83, 16, 18, 24, 20, 74, 67, 62,
       32, 91, 99, 77, 48, 94, 47, 37, 13, 22, 41, 38,  7, 66, 89, 95, 84,
       49,  6,  9, 19, 23, 58, 51,  4, 61, 76, 93, 11, 26, 56, 73, 53, 54,
       78, 46, 63, 88, 42, 40, 86, 64, 70,  5, 69, 14, 68, 31, 85, 44, 65,
       55, 33, 81, 29, 30, 27, 97, 43, 34, 96, 50, 71,  8, 79,  2, 87, 92,
       35, 45, 98,  1, 72,  0, 25, 90, 52, 60, 17, 82, 10, 21, 80]), 'cur_cost': 186296.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 24, 45, 50, 84, 59, 92, 81, 38, 66,  1, 49, 57, 65, 55, 17, 28,
       19,  0, 67, 41,  6, 44, 27, 89, 96,  3, 76, 31, 21, 97, 53, 70, 13,
       37, 29, 80, 47, 23, 61, 85, 36, 83, 18, 71, 93, 95, 25, 75, 22, 26,
       78, 99, 88, 54, 72,  5,  7,  8, 40, 68, 43, 90, 12, 63, 10, 82,  4,
       30, 94, 48, 58, 15, 87, 64, 56, 52, 77, 74, 14, 33, 91, 60, 46, 34,
        9, 79, 42, 20, 35, 11, 98, 73, 16,  2, 86, 69, 51, 62, 32],
      dtype=int64), 'cur_cost': 172201.0, 'intermediate_solutions': [{'tour': array([78, 21, 55, 27, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 103962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 78, 21, 55, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 105047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([89, 27, 78, 21, 55, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 27, 78, 21, 89, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104894.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55, 89, 27, 78, 21, 64, 37, 48, 96, 15, 76, 22, 44, 97, 86, 56, 29,
       45, 13, 38, 60,  7,  0, 87, 50, 26, 68, 57, 53, 51, 11, 95, 47, 67,
       24, 34, 82, 77, 54,  1, 28, 80, 33, 36, 84, 63, 19, 20, 17,  9, 69,
       41, 39, 43, 72, 85, 46, 90,  5, 10, 92, 31, 59, 58, 14, 93, 83,  3,
       52, 18, 71, 74, 35, 65, 61, 66, 23,  6, 62, 73, 42, 12, 75, 99, 70,
        2, 32, 94,  8, 16, 91, 88, 30, 25, 49, 81,  4, 40, 79, 98]), 'cur_cost': 104721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:16,919 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:16,919 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,924 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30539.000, 多样性=0.968
2025-08-05 10:29:16,924 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:16,924 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:16,924 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:16,926 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0984551267580625, 'best_improvement': 0.00045822014204824406}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.024218198918410372}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.014797243903788893, 'recent_improvements': [0.10345772828359835, -0.017365568279127514, 0.13305221609117615], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 21511.0, 'new_best_cost': 21511.0, 'quality_improvement': 0.0, 'old_diversity': 0.853, 'new_diversity': 0.853, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:16,926 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:16,926 - __main__ - INFO - kroA100 开始进化第 4 代
2025-08-05 10:29:16,926 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:16,927 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:16,927 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=30539.000, 多样性=0.968
2025-08-05 10:29:16,928 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:16,931 - PathExpert - INFO - 路径结构分析完成: 公共边数量=26, 路径相似性=0.968
2025-08-05 10:29:16,931 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:16,934 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.853
2025-08-05 10:29:16,936 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:16,937 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:16,937 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:16,937 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:16,993 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -1704.547, 聚类评分: 0.000, 覆盖率: 0.166, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:16,993 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:16,993 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:16,993 - visualization.landscape_visualizer - INFO - 设置当前实例名: kroA100
2025-08-05 10:29:16,999 - visualization.landscape_visualizer - INFO - 插值约束: 405 个点被约束到最小值 21511.00
2025-08-05 10:29:17,000 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.2%, 梯度: 7340.17 → 6734.62
