2025-08-04 17:21:07,602 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:21:07,602 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:21:07,603 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:07,605 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.859
2025-08-04 17:21:07,607 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:21:07,607 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.859
2025-08-04 17:21:07,608 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:21:07,633 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:21:07,633 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:21:07,634 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:21:07,634 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:21:07,902 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -6.940, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.773
2025-08-04 17:21:07,902 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:21:07,903 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:21:07,903 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:21:08,293 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:21:11,997 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_172111.html
2025-08-04 17:21:12,041 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_172111.html
2025-08-04 17:21:12,042 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:21:12,042 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:21:12,042 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.4096秒
2025-08-04 17:21:12,043 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:21:12,044 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6.939999999999995, 'local_optima_density': 0.2, 'gradient_variance': 17370.504399999998, 'cluster_count': 0}, 'population_state': {'diversity': 0.7733333333333333, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0009, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6.940)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.773)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299267.9025364, 'performance_metrics': {}}}
2025-08-04 17:21:12,045 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:21:12,045 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:21:12,045 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:21:12,045 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:21:12,046 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:12,046 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:21:12,047 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:12,047 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:12,048 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:21:12,048 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:12,048 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:12,049 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:21:12,049 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:21:12,049 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:21:12,050 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:21:12,050 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:12,056 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:12,056 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:12,213 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1181.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:12,214 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 4, 5, 1, 3, 7, 2, 6, 0], 'cur_cost': 1181.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:12,214 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1181.00)
2025-08-04 17:21:12,215 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:21:12,215 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:21:12,216 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:12,216 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:12,217 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:12,217 - ExplorationExpert - INFO - 探索路径生成完成，成本: 940.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:12,217 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 7, 5, 1, 6, 3, 8, 2, 0], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:12,218 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 940.00)
2025-08-04 17:21:12,218 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:21:12,218 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:21:12,218 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:12,219 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:12,219 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:12,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 739.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:12,219 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 2, 7, 3, 8, 5, 6, 0, 1], 'cur_cost': 739.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:12,220 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 739.00)
2025-08-04 17:21:12,220 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:21:12,221 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:12,225 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:12,226 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 927.0
2025-08-04 17:21:13,401 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:21:13,402 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:21:13,402 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:21:13,403 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:13,403 - ExploitationExpert - INFO - populations: [{'tour': [8, 4, 5, 1, 3, 7, 2, 6, 0], 'cur_cost': 1181.0}, {'tour': [4, 7, 5, 1, 6, 3, 8, 2, 0], 'cur_cost': 940.0}, {'tour': [4, 2, 7, 3, 8, 5, 6, 0, 1], 'cur_cost': 739.0}, {'tour': array([0, 3, 2, 4, 7, 8, 6, 5, 1], dtype=int64), 'cur_cost': 927.0}, {'tour': array([0, 5, 8, 7, 1, 4, 3, 2, 6], dtype=int64), 'cur_cost': 1101.0}, {'tour': array([4, 3, 1, 0, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1110.0}, {'tour': array([6, 5, 8, 3, 2, 0, 1, 4, 7], dtype=int64), 'cur_cost': 848.0}, {'tour': array([0, 7, 8, 4, 3, 6, 5, 1, 2], dtype=int64), 'cur_cost': 987.0}, {'tour': array([7, 5, 8, 2, 1, 4, 6, 0, 3], dtype=int64), 'cur_cost': 945.0}, {'tour': array([3, 5, 6, 7, 2, 4, 1, 8, 0], dtype=int64), 'cur_cost': 942.0}]
2025-08-04 17:21:13,405 - ExploitationExpert - INFO - 局部搜索耗时: 1.18秒，最大迭代次数: 10
2025-08-04 17:21:13,405 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:21:13,406 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([0, 3, 2, 4, 7, 8, 6, 5, 1], dtype=int64), 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': array([4, 3, 2, 8, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 3, 2, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 8, 4, 3, 2, 1, 7, 0, 6], dtype=int64), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 4, 3, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 8, 4, 3, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:13,406 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 927.00)
2025-08-04 17:21:13,407 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:21:13,407 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:21:13,407 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:13,408 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:13,408 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:13,408 - ExplorationExpert - INFO - 探索路径生成完成，成本: 834.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:13,408 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 7, 8, 4, 2, 3, 5, 1], 'cur_cost': 834.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:13,408 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 834.00)
2025-08-04 17:21:13,409 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:21:13,409 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:13,409 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:13,409 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 852.0
2025-08-04 17:21:15,285 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:21:15,286 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 17:21:15,286 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:21:15,286 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,287 - ExploitationExpert - INFO - populations: [{'tour': [8, 4, 5, 1, 3, 7, 2, 6, 0], 'cur_cost': 1181.0}, {'tour': [4, 7, 5, 1, 6, 3, 8, 2, 0], 'cur_cost': 940.0}, {'tour': [4, 2, 7, 3, 8, 5, 6, 0, 1], 'cur_cost': 739.0}, {'tour': array([0, 3, 2, 4, 7, 8, 6, 5, 1], dtype=int64), 'cur_cost': 927.0}, {'tour': [0, 6, 7, 8, 4, 2, 3, 5, 1], 'cur_cost': 834.0}, {'tour': array([6, 4, 2, 0, 1, 8, 5, 3, 7], dtype=int64), 'cur_cost': 852.0}, {'tour': array([6, 5, 8, 3, 2, 0, 1, 4, 7], dtype=int64), 'cur_cost': 848.0}, {'tour': array([0, 7, 8, 4, 3, 6, 5, 1, 2], dtype=int64), 'cur_cost': 987.0}, {'tour': array([7, 5, 8, 2, 1, 4, 6, 0, 3], dtype=int64), 'cur_cost': 945.0}, {'tour': array([3, 5, 6, 7, 2, 4, 1, 8, 0], dtype=int64), 'cur_cost': 942.0}]
2025-08-04 17:21:15,288 - ExploitationExpert - INFO - 局部搜索耗时: 1.88秒，最大迭代次数: 10
2025-08-04 17:21:15,288 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:21:15,289 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([6, 4, 2, 0, 1, 8, 5, 3, 7], dtype=int64), 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': array([1, 3, 4, 0, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 4, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 1, 3, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1008.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 0, 1, 3, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,289 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 852.00)
2025-08-04 17:21:15,290 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:21:15,290 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:21:15,290 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,291 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,291 - ExplorationExpert - INFO - 探索路径生成完成，成本: 964.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:15,291 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 5, 7, 8, 2, 0, 4, 6, 1], 'cur_cost': 964.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,292 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 964.00)
2025-08-04 17:21:15,292 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:21:15,292 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:21:15,292 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,292 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:15,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,293 - ExplorationExpert - INFO - 探索路径生成完成，成本: 932.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:15,293 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 5, 3, 8, 7, 4, 0, 6, 1], 'cur_cost': 932.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,293 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 932.00)
2025-08-04 17:21:15,293 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:21:15,294 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:21:15,294 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,294 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 892.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:15,295 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 5, 7, 6, 0, 4, 2, 8, 1], 'cur_cost': 892.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,295 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 892.00)
2025-08-04 17:21:15,295 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:21:15,295 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:21:15,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,296 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:15,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,296 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1097.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:21:15,297 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 5, 1, 2, 7, 3, 8, 0, 6], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,297 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1097.00)
2025-08-04 17:21:15,297 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:21:15,297 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:21:15,298 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 5, 1, 3, 7, 2, 6, 0], 'cur_cost': 1181.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 1, 6, 3, 8, 2, 0], 'cur_cost': 940.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 7, 3, 8, 5, 6, 0, 1], 'cur_cost': 739.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 2, 4, 7, 8, 6, 5, 1], dtype=int64), 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': array([4, 3, 2, 8, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 4, 3, 2, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1150.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 8, 4, 3, 2, 1, 7, 0, 6], dtype=int64), 'cur_cost': 978.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 4, 3, 5, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 8, 4, 3, 1, 7, 0, 6], dtype=int64), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 7, 8, 4, 2, 3, 5, 1], 'cur_cost': 834.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 2, 0, 1, 8, 5, 3, 7], dtype=int64), 'cur_cost': 852.0, 'intermediate_solutions': [{'tour': array([1, 3, 4, 0, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 1, 3, 4, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 0, 1, 3, 5, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1008.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 5, 0, 1, 3, 8, 7, 2, 6], dtype=int64), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 8, 2, 0, 4, 6, 1], 'cur_cost': 964.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 8, 7, 4, 0, 6, 1], 'cur_cost': 932.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 6, 0, 4, 2, 8, 1], 'cur_cost': 892.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 1, 2, 7, 3, 8, 0, 6], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-04 17:21:15,300 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:21:15,300 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:15,301 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=739.000, 多样性=0.867
2025-08-04 17:21:15,301 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:21:15,302 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:21:15,302 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:21:15,302 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0056726340342384525, 'best_improvement': 0.0107095046854083}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008620689655172037}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:21:15,313 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:21:15,313 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:21:15,314 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:21:15,314 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:15,315 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=739.000, 多样性=0.867
2025-08-04 17:21:15,315 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:21:15,316 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.867
2025-08-04 17:21:15,317 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:21:15,317 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-04 17:21:15,320 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:21:15,320 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:21:15,321 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-04 17:21:15,321 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-04 17:21:15,326 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.182, 适应度梯度: -36.127, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.689
2025-08-04 17:21:15,326 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:21:15,327 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:21:15,327 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:21:15,330 - visualization.landscape_visualizer - INFO - 插值约束: 48 个点被约束到最小值 680.00
2025-08-04 17:21:15,335 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:21:15,416 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_172115.html
2025-08-04 17:21:15,475 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_172115.html
2025-08-04 17:21:15,476 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:21:15,476 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:21:15,476 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1564秒
2025-08-04 17:21:15,477 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.18181818181818182, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -36.12727272727272, 'local_optima_density': 0.18181818181818182, 'gradient_variance': 25484.75107438017, 'cluster_count': 0}, 'population_state': {'diversity': 0.6892561983471075, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9487695103589049, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -36.127)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299275.3262076, 'performance_metrics': {}}}
2025-08-04 17:21:15,477 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:21:15,478 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:21:15,478 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:21:15,478 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:21:15,479 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:15,479 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:21:15,479 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:15,479 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:15,480 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:21:15,480 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:15,480 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:15,481 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:21:15,481 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-04 17:21:15,482 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:21:15,482 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:15,482 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:15,483 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 969.0
2025-08-04 17:21:15,544 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:21:15,545 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:21:15,545 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:21:15,545 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,546 - ExploitationExpert - INFO - populations: [{'tour': array([7, 6, 5, 2, 4, 0, 8, 3, 1], dtype=int64), 'cur_cost': 969.0}, {'tour': [4, 7, 5, 1, 6, 3, 8, 2, 0], 'cur_cost': 940.0}, {'tour': [4, 2, 7, 3, 8, 5, 6, 0, 1], 'cur_cost': 739.0}, {'tour': [0, 3, 2, 4, 7, 8, 6, 5, 1], 'cur_cost': 927.0}, {'tour': [0, 6, 7, 8, 4, 2, 3, 5, 1], 'cur_cost': 834.0}, {'tour': [6, 4, 2, 0, 1, 8, 5, 3, 7], 'cur_cost': 852.0}, {'tour': [3, 5, 7, 8, 2, 0, 4, 6, 1], 'cur_cost': 964.0}, {'tour': [2, 5, 3, 8, 7, 4, 0, 6, 1], 'cur_cost': 932.0}, {'tour': [3, 5, 7, 6, 0, 4, 2, 8, 1], 'cur_cost': 892.0}, {'tour': [4, 5, 1, 2, 7, 3, 8, 0, 6], 'cur_cost': 1097.0}]
2025-08-04 17:21:15,546 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:21:15,547 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:21:15,548 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([7, 6, 5, 2, 4, 0, 8, 3, 1], dtype=int64), 'cur_cost': 969.0, 'intermediate_solutions': [{'tour': array([5, 4, 8, 1, 3, 7, 2, 6, 0]), 'cur_cost': 1182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 8, 3, 7, 2, 6, 0]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 5, 4, 8, 7, 2, 6, 0]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 5, 4, 3, 7, 2, 6, 0]), 'cur_cost': 1218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 1, 5, 4, 7, 2, 6, 0]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,548 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 969.00)
2025-08-04 17:21:15,549 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:21:15,549 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:21:15,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,550 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:15,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1002.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,551 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 7, 3, 1, 8, 5, 4, 2, 0], 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': [4, 7, 5, 6, 1, 3, 8, 2, 0], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 2, 8, 3, 6, 1, 5, 0], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 1, 6, 8, 3, 2, 0], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,551 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1002.00)
2025-08-04 17:21:15,552 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:21:15,552 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:21:15,552 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,552 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:15,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,553 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1200.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,553 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 3, 1, 6, 4, 7, 8, 0, 5], 'cur_cost': 1200.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 3, 8, 5, 1, 0, 6], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 3, 8, 5, 6, 1, 0], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 3, 8, 5, 6, 0, 2, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,554 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1200.00)
2025-08-04 17:21:15,554 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:21:15,554 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:21:15,554 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,555 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:15,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,556 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1092.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,556 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 7, 1, 8, 2, 6, 0, 5, 3], 'cur_cost': 1092.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 8, 7, 4, 6, 5, 1], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 4, 7, 8, 6, 1, 5], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 7, 8, 6, 5, 1, 3], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,556 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1092.00)
2025-08-04 17:21:15,556 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:21:15,556 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:21:15,557 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,557 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,558 - ExplorationExpert - INFO - 探索路径生成完成，成本: 804.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,558 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0, 'intermediate_solutions': [{'tour': [0, 6, 7, 8, 2, 4, 3, 5, 1], 'cur_cost': 811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 4, 2, 3, 1, 5], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 7, 8, 4, 2, 5, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,559 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 804.00)
2025-08-04 17:21:15,559 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:21:15,559 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:21:15,559 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,559 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:15,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,560 - ExplorationExpert - INFO - 探索路径生成完成，成本: 994.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,561 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0, 'intermediate_solutions': [{'tour': [6, 8, 2, 0, 1, 4, 5, 3, 7], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 2, 7, 3, 5, 8, 1, 0], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 0, 1, 8, 6, 5, 3, 7], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,561 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 994.00)
2025-08-04 17:21:15,561 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:21:15,561 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:21:15,561 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,562 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 862.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,563 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 2, 0, 4, 5, 1], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 7, 6, 4, 0, 2, 8, 1], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 8, 2, 3, 0, 4, 6, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,564 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 862.00)
2025-08-04 17:21:15,564 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:21:15,564 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:21:15,565 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,566 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,567 - ExplorationExpert - INFO - 探索路径生成完成，成本: 784.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,568 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'intermediate_solutions': [{'tour': [2, 3, 5, 8, 7, 4, 0, 6, 1], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 3, 5, 4, 0, 6, 1], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 3, 8, 7, 0, 6, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,568 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 784.00)
2025-08-04 17:21:15,568 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:21:15,569 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:21:15,569 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,569 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:15,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 904.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,571 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 6, 1, 4, 2, 8, 0], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 4, 0, 6, 7, 5, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 4, 7, 6, 0, 2, 8, 1], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,571 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 904.00)
2025-08-04 17:21:15,571 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:21:15,571 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:15,572 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:15,572 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 954.0
2025-08-04 17:21:15,634 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:21:15,635 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:21:15,635 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:21:15,636 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,636 - ExploitationExpert - INFO - populations: [{'tour': array([7, 6, 5, 2, 4, 0, 8, 3, 1], dtype=int64), 'cur_cost': 969.0}, {'tour': [6, 7, 3, 1, 8, 5, 4, 2, 0], 'cur_cost': 1002.0}, {'tour': [2, 3, 1, 6, 4, 7, 8, 0, 5], 'cur_cost': 1200.0}, {'tour': [4, 7, 1, 8, 2, 6, 0, 5, 3], 'cur_cost': 1092.0}, {'tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0}, {'tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0}, {'tour': array([4, 1, 7, 2, 0, 6, 5, 8, 3], dtype=int64), 'cur_cost': 954.0}]
2025-08-04 17:21:15,637 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:21:15,637 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:21:15,638 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([4, 1, 7, 2, 0, 6, 5, 8, 3], dtype=int64), 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 2, 7, 3, 8, 0, 6]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 5, 4, 7, 3, 8, 0, 6]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 5, 4, 3, 8, 0, 6]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 1, 5, 7, 3, 8, 0, 6]), 'cur_cost': 951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 2, 1, 5, 3, 8, 0, 6]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,639 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 954.00)
2025-08-04 17:21:15,639 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:21:15,639 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:21:15,640 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 6, 5, 2, 4, 0, 8, 3, 1], dtype=int64), 'cur_cost': 969.0, 'intermediate_solutions': [{'tour': array([5, 4, 8, 1, 3, 7, 2, 6, 0]), 'cur_cost': 1182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 5, 4, 8, 3, 7, 2, 6, 0]), 'cur_cost': 1000.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 1, 5, 4, 8, 7, 2, 6, 0]), 'cur_cost': 1194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 1, 5, 4, 3, 7, 2, 6, 0]), 'cur_cost': 1218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 3, 1, 5, 4, 7, 2, 6, 0]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 1, 8, 5, 4, 2, 0], 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': [4, 7, 5, 6, 1, 3, 8, 2, 0], 'cur_cost': 909.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 2, 8, 3, 6, 1, 5, 0], 'cur_cost': 1000.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 1, 6, 8, 3, 2, 0], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 1, 6, 4, 7, 8, 0, 5], 'cur_cost': 1200.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 3, 8, 5, 1, 0, 6], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 3, 8, 5, 6, 1, 0], 'cur_cost': 739.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 3, 8, 5, 6, 0, 2, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 1, 8, 2, 6, 0, 5, 3], 'cur_cost': 1092.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 8, 7, 4, 6, 5, 1], 'cur_cost': 981.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 4, 7, 8, 6, 1, 5], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 7, 8, 6, 5, 1, 3], 'cur_cost': 1027.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0, 'intermediate_solutions': [{'tour': [0, 6, 7, 8, 2, 4, 3, 5, 1], 'cur_cost': 811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 7, 8, 4, 2, 3, 1, 5], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 7, 8, 4, 2, 5, 1], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0, 'intermediate_solutions': [{'tour': [6, 8, 2, 0, 1, 4, 5, 3, 7], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 4, 2, 7, 3, 5, 8, 1, 0], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 0, 1, 8, 6, 5, 3, 7], 'cur_cost': 827.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0, 'intermediate_solutions': [{'tour': [3, 6, 7, 8, 2, 0, 4, 5, 1], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 7, 6, 4, 0, 2, 8, 1], 'cur_cost': 1016.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 8, 2, 3, 0, 4, 6, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0, 'intermediate_solutions': [{'tour': [2, 3, 5, 8, 7, 4, 0, 6, 1], 'cur_cost': 929.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 8, 3, 5, 4, 0, 6, 1], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 5, 3, 8, 7, 0, 6, 1], 'cur_cost': 861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 6, 1, 4, 2, 8, 0], 'cur_cost': 856.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 4, 0, 6, 7, 5, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 4, 7, 6, 0, 2, 8, 1], 'cur_cost': 1044.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 7, 2, 0, 6, 5, 8, 3], dtype=int64), 'cur_cost': 954.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 2, 7, 3, 8, 0, 6]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 5, 4, 7, 3, 8, 0, 6]), 'cur_cost': 1101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 5, 4, 3, 8, 0, 6]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 1, 5, 7, 3, 8, 0, 6]), 'cur_cost': 951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 2, 1, 5, 3, 8, 0, 6]), 'cur_cost': 1051.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:21:15,642 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:21:15,643 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:15,644 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=784.000, 多样性=0.891
2025-08-04 17:21:15,644 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:21:15,644 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:21:15,644 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:21:15,645 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.033033756960759865, 'best_improvement': -0.06089309878213803}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02849002849002875}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:21:15,645 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:21:15,645 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:21:15,646 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:21:15,646 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:15,647 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=784.000, 多样性=0.891
2025-08-04 17:21:15,647 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:21:15,648 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.891
2025-08-04 17:21:15,649 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:21:15,649 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 17:21:15,652 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:21:15,652 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:21:15,652 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:21:15,652 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:21:15,659 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 23.133, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.668
2025-08-04 17:21:15,659 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:21:15,660 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:21:15,660 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:21:15,664 - visualization.landscape_visualizer - INFO - 插值约束: 23 个点被约束到最小值 680.00
2025-08-04 17:21:15,670 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:21:15,748 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_172115.html
2025-08-04 17:21:15,783 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_172115.html
2025-08-04 17:21:15,783 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:21:15,784 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:21:15,784 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1320秒
2025-08-04 17:21:15,784 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 23.133333333333336, 'local_optima_density': 0.25, 'gradient_variance': 29500.568888888887, 'cluster_count': 0}, 'population_state': {'diversity': 0.6679292929292929, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0031, 'fitness_entropy': 0.972765278018163, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 23.133)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299275.6596253, 'performance_metrics': {}}}
2025-08-04 17:21:15,785 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:21:15,785 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:21:15,786 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:21:15,786 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:21:15,786 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:15,786 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:21:15,787 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:15,787 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:15,787 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:21:15,787 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 17:21:15,788 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:15,788 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:21:15,788 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:21:15,788 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:21:15,789 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:21:15,789 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,789 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 723.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:15,791 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 7, 8, 3, 5, 6, 0, 1, 2], 'cur_cost': 723.0, 'intermediate_solutions': [{'tour': [7, 6, 5, 2, 3, 0, 8, 4, 1], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 1, 3, 8, 0, 4, 2, 5], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 2, 3, 4, 0, 8, 1], 'cur_cost': 1117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:15,791 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 723.00)
2025-08-04 17:21:15,791 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 17:21:15,792 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:15,792 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:15,792 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 824.0
2025-08-04 17:21:15,846 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:21:15,847 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 17:21:15,847 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 17:21:15,849 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,849 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 8, 3, 5, 6, 0, 1, 2], 'cur_cost': 723.0}, {'tour': array([7, 3, 5, 6, 1, 4, 8, 2, 0], dtype=int64), 'cur_cost': 824.0}, {'tour': [2, 3, 1, 6, 4, 7, 8, 0, 5], 'cur_cost': 1200.0}, {'tour': [4, 7, 1, 8, 2, 6, 0, 5, 3], 'cur_cost': 1092.0}, {'tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0}, {'tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0}, {'tour': [4, 1, 7, 2, 0, 6, 5, 8, 3], 'cur_cost': 954.0}]
2025-08-04 17:21:15,850 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:21:15,850 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:21:15,851 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([7, 3, 5, 6, 1, 4, 8, 2, 0], dtype=int64), 'cur_cost': 824.0, 'intermediate_solutions': [{'tour': array([3, 7, 6, 1, 8, 5, 4, 2, 0]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 7, 6, 8, 5, 4, 2, 0]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 3, 7, 6, 5, 4, 2, 0]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 1, 3, 7, 8, 5, 4, 2, 0]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 8, 1, 3, 7, 5, 4, 2, 0]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,852 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 824.00)
2025-08-04 17:21:15,852 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:21:15,852 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:15,853 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:15,853 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1026.0
2025-08-04 17:21:15,919 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:15,919 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:15,919 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:15,920 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,920 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 8, 3, 5, 6, 0, 1, 2], 'cur_cost': 723.0}, {'tour': array([7, 3, 5, 6, 1, 4, 8, 2, 0], dtype=int64), 'cur_cost': 824.0}, {'tour': array([6, 1, 8, 4, 3, 7, 2, 0, 5], dtype=int64), 'cur_cost': 1026.0}, {'tour': [4, 7, 1, 8, 2, 6, 0, 5, 3], 'cur_cost': 1092.0}, {'tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0}, {'tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0}, {'tour': [4, 1, 7, 2, 0, 6, 5, 8, 3], 'cur_cost': 954.0}]
2025-08-04 17:21:15,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:15,922 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:21:15,923 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 1, 8, 4, 3, 7, 2, 0, 5], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([1, 3, 2, 6, 4, 7, 8, 0, 5]), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 3, 2, 4, 7, 8, 0, 5]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 1, 3, 2, 7, 8, 0, 5]), 'cur_cost': 1205.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 1, 3, 4, 7, 8, 0, 5]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 6, 1, 3, 7, 8, 0, 5]), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,923 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1026.00)
2025-08-04 17:21:15,924 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:21:15,924 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:15,924 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:15,924 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1108.0
2025-08-04 17:21:15,993 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:15,993 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:15,994 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:15,994 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:15,995 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 8, 3, 5, 6, 0, 1, 2], 'cur_cost': 723.0}, {'tour': array([7, 3, 5, 6, 1, 4, 8, 2, 0], dtype=int64), 'cur_cost': 824.0}, {'tour': array([6, 1, 8, 4, 3, 7, 2, 0, 5], dtype=int64), 'cur_cost': 1026.0}, {'tour': array([0, 8, 1, 5, 7, 6, 4, 2, 3], dtype=int64), 'cur_cost': 1108.0}, {'tour': [7, 0, 6, 5, 3, 8, 4, 2, 1], 'cur_cost': 804.0}, {'tour': [2, 6, 1, 4, 7, 3, 5, 8, 0], 'cur_cost': 994.0}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0}, {'tour': [3, 5, 6, 7, 8, 4, 2, 0, 1], 'cur_cost': 784.0}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0}, {'tour': [4, 1, 7, 2, 0, 6, 5, 8, 3], 'cur_cost': 954.0}]
2025-08-04 17:21:15,996 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:15,996 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:21:15,996 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([0, 8, 1, 5, 7, 6, 4, 2, 3], dtype=int64), 'cur_cost': 1108.0, 'intermediate_solutions': [{'tour': array([1, 7, 4, 8, 2, 6, 0, 5, 3]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 7, 4, 2, 6, 0, 5, 3]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 1, 7, 4, 6, 0, 5, 3]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 1, 7, 2, 6, 0, 5, 3]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 8, 1, 7, 6, 0, 5, 3]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:15,997 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1108.00)
2025-08-04 17:21:15,997 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:21:15,998 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:21:15,998 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:15,999 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:15,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:15,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,000 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [5, 0, 6, 7, 3, 8, 4, 2, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 6, 2, 4, 8, 3, 5, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 3, 8, 4, 2, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,001 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 829.00)
2025-08-04 17:21:16,001 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:21:16,001 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:21:16,001 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,002 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,003 - ExplorationExpert - INFO - 探索路径生成完成，成本: 841.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,003 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 0, 1, 4, 7, 5, 3, 8, 2], 'cur_cost': 841.0, 'intermediate_solutions': [{'tour': [2, 6, 1, 8, 7, 3, 5, 4, 0], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 4, 7, 3, 0, 8, 5], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 1, 4, 7, 3, 5, 0], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,004 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 841.00)
2025-08-04 17:21:16,004 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:21:16,004 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:21:16,004 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,005 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 976.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,006 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 6, 1, 3, 8, 7, 5, 0, 4], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 8, 4, 0, 7, 2, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 3, 8, 7, 0, 4, 1, 2], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,006 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 976.00)
2025-08-04 17:21:16,006 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:21:16,006 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:21:16,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,007 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,008 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1014.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,008 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': [3, 5, 6, 7, 8, 2, 4, 0, 1], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 7, 1, 0, 2, 4, 8], 'cur_cost': 736.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 6, 8, 4, 2, 0, 1, 7], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,008 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1014.00)
2025-08-04 17:21:16,009 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:21:16,009 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:21:16,009 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,009 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:16,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 739.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,011 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 2, 4, 1, 0, 6, 5, 8, 3], 'cur_cost': 739.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 1, 7, 5, 2, 8, 6], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 0, 4, 3, 5, 6, 8, 2], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,011 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 739.00)
2025-08-04 17:21:16,011 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:21:16,011 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:21:16,012 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,012 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:16,012 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,012 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,013 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1113.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,013 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 2, 8, 1, 6, 3, 0, 5, 4], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [5, 1, 7, 2, 0, 6, 4, 8, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 7, 2, 0, 6, 8, 5, 3], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 0, 6, 1, 5, 8, 3], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,014 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1113.00)
2025-08-04 17:21:16,014 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:21:16,014 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:21:16,016 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 8, 3, 5, 6, 0, 1, 2], 'cur_cost': 723.0, 'intermediate_solutions': [{'tour': [7, 6, 5, 2, 3, 0, 8, 4, 1], 'cur_cost': 1061.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 1, 3, 8, 0, 4, 2, 5], 'cur_cost': 964.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 5, 2, 3, 4, 0, 8, 1], 'cur_cost': 1117.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 5, 6, 1, 4, 8, 2, 0], dtype=int64), 'cur_cost': 824.0, 'intermediate_solutions': [{'tour': array([3, 7, 6, 1, 8, 5, 4, 2, 0]), 'cur_cost': 983.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 7, 6, 8, 5, 4, 2, 0]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 3, 7, 6, 5, 4, 2, 0]), 'cur_cost': 1013.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 1, 3, 7, 8, 5, 4, 2, 0]), 'cur_cost': 950.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 8, 1, 3, 7, 5, 4, 2, 0]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 8, 4, 3, 7, 2, 0, 5], dtype=int64), 'cur_cost': 1026.0, 'intermediate_solutions': [{'tour': array([1, 3, 2, 6, 4, 7, 8, 0, 5]), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 3, 2, 4, 7, 8, 0, 5]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 6, 1, 3, 2, 7, 8, 0, 5]), 'cur_cost': 1205.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 6, 1, 3, 4, 7, 8, 0, 5]), 'cur_cost': 1212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 6, 1, 3, 7, 8, 0, 5]), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 8, 1, 5, 7, 6, 4, 2, 3], dtype=int64), 'cur_cost': 1108.0, 'intermediate_solutions': [{'tour': array([1, 7, 4, 8, 2, 6, 0, 5, 3]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 7, 4, 2, 6, 0, 5, 3]), 'cur_cost': 1004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 1, 7, 4, 6, 0, 5, 3]), 'cur_cost': 1080.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 1, 7, 2, 6, 0, 5, 3]), 'cur_cost': 1123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 8, 1, 7, 6, 0, 5, 3]), 'cur_cost': 945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [5, 0, 6, 7, 3, 8, 4, 2, 1], 'cur_cost': 898.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 6, 2, 4, 8, 3, 5, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 3, 8, 4, 2, 5, 1], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 1, 4, 7, 5, 3, 8, 2], 'cur_cost': 841.0, 'intermediate_solutions': [{'tour': [2, 6, 1, 8, 7, 3, 5, 4, 0], 'cur_cost': 1046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 4, 7, 3, 0, 8, 5], 'cur_cost': 1102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 8, 1, 4, 7, 3, 5, 0], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 1, 3, 8, 7, 5, 0, 4], 'cur_cost': 976.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 8, 4, 0, 7, 2, 1], 'cur_cost': 946.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 3, 8, 7, 0, 4, 1, 2], 'cur_cost': 921.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 3, 8, 7, 0, 4, 2, 1], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0, 'intermediate_solutions': [{'tour': [3, 5, 6, 7, 8, 2, 4, 0, 1], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 6, 7, 1, 0, 2, 4, 8], 'cur_cost': 736.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 6, 8, 4, 2, 0, 1, 7], 'cur_cost': 777.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 4, 1, 0, 6, 5, 8, 3], 'cur_cost': 739.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 1, 7, 5, 2, 8, 6], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 0, 4, 3, 5, 6, 8, 2], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 0, 1, 7, 5, 6, 8, 2], 'cur_cost': 904.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 8, 1, 6, 3, 0, 5, 4], 'cur_cost': 1113.0, 'intermediate_solutions': [{'tour': [5, 1, 7, 2, 0, 6, 4, 8, 3], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 7, 2, 0, 6, 8, 5, 3], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 0, 6, 1, 5, 8, 3], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:21:16,018 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:21:16,019 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:16,020 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=723.000, 多样性=0.891
2025-08-04 17:21:16,020 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:21:16,020 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:21:16,020 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:21:16,020 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06306597181475833, 'best_improvement': 0.0778061224489796}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:21:16,021 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:21:16,021 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:21:16,021 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:21:16,022 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:16,022 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=723.000, 多样性=0.891
2025-08-04 17:21:16,022 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:21:16,023 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.891
2025-08-04 17:21:16,023 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:21:16,024 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:21:16,025 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:21:16,025 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:21:16,026 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:21:16,026 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:21:16,033 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 7.662, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.607
2025-08-04 17:21:16,034 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:21:16,034 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:21:16,035 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:21:16,038 - visualization.landscape_visualizer - INFO - 插值约束: 234 个点被约束到最小值 680.00
2025-08-04 17:21:16,041 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:21:16,118 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_172116.html
2025-08-04 17:21:16,160 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_172116.html
2025-08-04 17:21:16,160 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:21:16,161 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:21:16,161 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1357秒
2025-08-04 17:21:16,161 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 7.661538461538457, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 38233.37467455622, 'cluster_count': 0}, 'population_state': {'diversity': 0.6074950690335306, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0042, 'fitness_entropy': 0.9329350879565486, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 7.662)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299276.03437, 'performance_metrics': {}}}
2025-08-04 17:21:16,162 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:21:16,162 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:21:16,162 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:21:16,162 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:21:16,163 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:16,163 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:21:16,163 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:16,163 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:16,164 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:21:16,164 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 17:21:16,164 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:16,165 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:21:16,165 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-04 17:21:16,166 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:21:16,166 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:21:16,166 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,167 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,167 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,167 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,168 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,168 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [8, 3, 7, 5, 6, 4, 2, 0, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [4, 1, 8, 3, 5, 6, 0, 7, 2], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 0, 6, 5, 3, 8, 7, 4], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 3, 5, 6, 0, 1, 2, 4], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,169 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 829.00)
2025-08-04 17:21:16,169 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:21:16,169 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:21:16,169 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,170 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,170 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 776.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,171 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 5, 7, 3, 4, 2, 8, 0, 1], 'cur_cost': 776.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 6, 1, 2, 8, 4, 0], 'cur_cost': 813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 6, 1, 4, 0, 2, 8], 'cur_cost': 816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 3, 6, 1, 4, 8, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,171 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 776.00)
2025-08-04 17:21:16,172 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:21:16,172 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,172 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,172 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1106.0
2025-08-04 17:21:16,243 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,243 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,244 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,244 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,245 - ExploitationExpert - INFO - populations: [{'tour': [8, 3, 7, 5, 6, 4, 2, 0, 1], 'cur_cost': 829.0}, {'tour': [6, 5, 7, 3, 4, 2, 8, 0, 1], 'cur_cost': 776.0}, {'tour': array([7, 1, 8, 5, 2, 3, 4, 0, 6], dtype=int64), 'cur_cost': 1106.0}, {'tour': [0, 8, 1, 5, 7, 6, 4, 2, 3], 'cur_cost': 1108.0}, {'tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0}, {'tour': [6, 0, 1, 4, 7, 5, 3, 8, 2], 'cur_cost': 841.0}, {'tour': [2, 6, 1, 3, 8, 7, 5, 0, 4], 'cur_cost': 976.0}, {'tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0}, {'tour': [7, 2, 4, 1, 0, 6, 5, 8, 3], 'cur_cost': 739.0}, {'tour': [7, 2, 8, 1, 6, 3, 0, 5, 4], 'cur_cost': 1113.0}]
2025-08-04 17:21:16,245 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:16,245 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:21:16,246 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([7, 1, 8, 5, 2, 3, 4, 0, 6], dtype=int64), 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': array([8, 1, 6, 4, 3, 7, 2, 0, 5]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 6, 3, 7, 2, 0, 5]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 1, 6, 7, 2, 0, 5]), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 8, 1, 3, 7, 2, 0, 5]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 3, 4, 8, 1, 7, 2, 0, 5]), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,247 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1106.00)
2025-08-04 17:21:16,247 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:21:16,247 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,247 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1002.0
2025-08-04 17:21:16,313 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,313 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,314 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,315 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,315 - ExploitationExpert - INFO - populations: [{'tour': [8, 3, 7, 5, 6, 4, 2, 0, 1], 'cur_cost': 829.0}, {'tour': [6, 5, 7, 3, 4, 2, 8, 0, 1], 'cur_cost': 776.0}, {'tour': array([7, 1, 8, 5, 2, 3, 4, 0, 6], dtype=int64), 'cur_cost': 1106.0}, {'tour': array([5, 3, 2, 0, 6, 1, 7, 8, 4], dtype=int64), 'cur_cost': 1002.0}, {'tour': [7, 0, 4, 2, 8, 5, 3, 6, 1], 'cur_cost': 829.0}, {'tour': [6, 0, 1, 4, 7, 5, 3, 8, 2], 'cur_cost': 841.0}, {'tour': [2, 6, 1, 3, 8, 7, 5, 0, 4], 'cur_cost': 976.0}, {'tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0}, {'tour': [7, 2, 4, 1, 0, 6, 5, 8, 3], 'cur_cost': 739.0}, {'tour': [7, 2, 8, 1, 6, 3, 0, 5, 4], 'cur_cost': 1113.0}]
2025-08-04 17:21:16,317 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:16,317 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:21:16,318 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([5, 3, 2, 0, 6, 1, 7, 8, 4], dtype=int64), 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 5, 7, 6, 4, 2, 3]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 8, 0, 7, 6, 4, 2, 3]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 1, 8, 0, 6, 4, 2, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 8, 7, 6, 4, 2, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 5, 1, 8, 6, 4, 2, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,318 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1002.00)
2025-08-04 17:21:16,318 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:21:16,319 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:21:16,319 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,319 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,321 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 4, 0, 6, 7, 3, 5, 8, 2], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 2, 8, 5, 3, 1, 6], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 4, 2, 8, 5, 1, 6, 3], 'cur_cost': 868.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 2, 8, 5, 4, 3, 6, 1], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,322 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 829.00)
2025-08-04 17:21:16,322 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:21:16,322 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:21:16,322 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,323 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,324 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 925.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,324 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 3, 6, 1, 4, 2, 7, 5], 'cur_cost': 925.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 4, 7, 5, 3, 8, 6], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 1, 4, 7, 5, 2, 8, 3], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 4, 7, 5, 2, 3, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,325 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 925.00)
2025-08-04 17:21:16,325 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:21:16,325 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:21:16,325 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,325 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,326 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,326 - ExplorationExpert - INFO - 探索路径生成完成，成本: 968.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,326 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 4, 7, 1, 2, 8, 5, 6], 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 6, 8, 7, 5, 0, 4], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 3, 8, 5, 7, 0, 4], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 3, 0, 8, 7, 5, 4], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,327 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 968.00)
2025-08-04 17:21:16,327 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:21:16,327 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:21:16,327 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,328 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:16,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 948.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,329 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 5, 2, 8, 7, 3, 0], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 5, 2, 8, 3, 0, 7], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,329 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 948.00)
2025-08-04 17:21:16,330 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:21:16,330 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:21:16,330 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,330 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:21:16,330 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1105.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,332 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [3, 4, 8, 0, 7, 1, 6, 2, 5], 'cur_cost': 1105.0, 'intermediate_solutions': [{'tour': [7, 2, 4, 1, 0, 5, 6, 8, 3], 'cur_cost': 811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 5, 6, 0, 1, 4, 8, 3], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 2, 1, 0, 6, 5, 8, 3], 'cur_cost': 734.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,332 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1105.00)
2025-08-04 17:21:16,332 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:21:16,333 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,334 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1091.0
2025-08-04 17:21:16,395 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,396 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,396 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,397 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,397 - ExploitationExpert - INFO - populations: [{'tour': [8, 3, 7, 5, 6, 4, 2, 0, 1], 'cur_cost': 829.0}, {'tour': [6, 5, 7, 3, 4, 2, 8, 0, 1], 'cur_cost': 776.0}, {'tour': array([7, 1, 8, 5, 2, 3, 4, 0, 6], dtype=int64), 'cur_cost': 1106.0}, {'tour': array([5, 3, 2, 0, 6, 1, 7, 8, 4], dtype=int64), 'cur_cost': 1002.0}, {'tour': [1, 4, 0, 6, 7, 3, 5, 8, 2], 'cur_cost': 829.0}, {'tour': [0, 8, 3, 6, 1, 4, 2, 7, 5], 'cur_cost': 925.0}, {'tour': [0, 3, 4, 7, 1, 2, 8, 5, 6], 'cur_cost': 968.0}, {'tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0}, {'tour': [3, 4, 8, 0, 7, 1, 6, 2, 5], 'cur_cost': 1105.0}, {'tour': array([6, 1, 5, 4, 7, 0, 8, 2, 3], dtype=int64), 'cur_cost': 1091.0}]
2025-08-04 17:21:16,398 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 17:21:16,398 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:21:16,399 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([6, 1, 5, 4, 7, 0, 8, 2, 3], dtype=int64), 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': array([8, 2, 7, 1, 6, 3, 0, 5, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 2, 7, 6, 3, 0, 5, 4]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 8, 2, 7, 3, 0, 5, 4]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 1, 8, 2, 6, 3, 0, 5, 4]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 1, 8, 2, 3, 0, 5, 4]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,400 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1091.00)
2025-08-04 17:21:16,400 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:21:16,400 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:21:16,401 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 7, 5, 6, 4, 2, 0, 1], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [4, 1, 8, 3, 5, 6, 0, 7, 2], 'cur_cost': 859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 0, 6, 5, 3, 8, 7, 4], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 3, 5, 6, 0, 1, 2, 4], 'cur_cost': 723.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 7, 3, 4, 2, 8, 0, 1], 'cur_cost': 776.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 6, 1, 2, 8, 4, 0], 'cur_cost': 813.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 6, 1, 4, 0, 2, 8], 'cur_cost': 816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 3, 6, 1, 4, 8, 2, 0], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 8, 5, 2, 3, 4, 0, 6], dtype=int64), 'cur_cost': 1106.0, 'intermediate_solutions': [{'tour': array([8, 1, 6, 4, 3, 7, 2, 0, 5]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 6, 3, 7, 2, 0, 5]), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 1, 6, 7, 2, 0, 5]), 'cur_cost': 1039.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 8, 1, 3, 7, 2, 0, 5]), 'cur_cost': 1100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 3, 4, 8, 1, 7, 2, 0, 5]), 'cur_cost': 1072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 3, 2, 0, 6, 1, 7, 8, 4], dtype=int64), 'cur_cost': 1002.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 5, 7, 6, 4, 2, 3]), 'cur_cost': 1111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 8, 0, 7, 6, 4, 2, 3]), 'cur_cost': 1059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 5, 1, 8, 0, 6, 4, 2, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 8, 7, 6, 4, 2, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 7, 5, 1, 8, 6, 4, 2, 3]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 6, 7, 3, 5, 8, 2], 'cur_cost': 829.0, 'intermediate_solutions': [{'tour': [7, 0, 4, 2, 8, 5, 3, 1, 6], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 0, 4, 2, 8, 5, 1, 6, 3], 'cur_cost': 868.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 2, 8, 5, 4, 3, 6, 1], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 3, 6, 1, 4, 2, 7, 5], 'cur_cost': 925.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 4, 7, 5, 3, 8, 6], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 1, 4, 7, 5, 2, 8, 3], 'cur_cost': 863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 4, 7, 5, 2, 3, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 7, 1, 2, 8, 5, 6], 'cur_cost': 968.0, 'intermediate_solutions': [{'tour': [2, 3, 1, 6, 8, 7, 5, 0, 4], 'cur_cost': 998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 3, 8, 5, 7, 0, 4], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 3, 0, 8, 7, 5, 4], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 5, 2, 8, 7, 3, 0], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 1, 5, 2, 8, 3, 0, 7], 'cur_cost': 1087.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 1, 5, 2, 8, 3, 7, 0], 'cur_cost': 1014.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 8, 0, 7, 1, 6, 2, 5], 'cur_cost': 1105.0, 'intermediate_solutions': [{'tour': [7, 2, 4, 1, 0, 5, 6, 8, 3], 'cur_cost': 811.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 2, 5, 6, 0, 1, 4, 8, 3], 'cur_cost': 853.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 2, 1, 0, 6, 5, 8, 3], 'cur_cost': 734.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 5, 4, 7, 0, 8, 2, 3], dtype=int64), 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': array([8, 2, 7, 1, 6, 3, 0, 5, 4]), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 2, 7, 6, 3, 0, 5, 4]), 'cur_cost': 1088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 8, 2, 7, 3, 0, 5, 4]), 'cur_cost': 1128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 1, 8, 2, 6, 3, 0, 5, 4]), 'cur_cost': 1178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 6, 1, 8, 2, 3, 0, 5, 4]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:21:16,403 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:21:16,404 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:16,405 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=776.000, 多样性=0.894
2025-08-04 17:21:16,405 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:21:16,405 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:21:16,405 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:21:16,405 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03633494404253244, 'best_improvement': -0.07330567081604426}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.002770083102493037}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03436930292449839, 'recent_improvements': [-0.0056726340342384525, -0.033033756960759865, 0.06306597181475833], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:21:16,406 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:21:16,406 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:21:16,406 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:21:16,407 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:16,407 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=776.000, 多样性=0.894
2025-08-04 17:21:16,407 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:21:16,408 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.894
2025-08-04 17:21:16,408 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:21:16,409 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 17:21:16,410 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:21:16,410 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:21:16,411 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:21:16,411 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:21:16,419 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 9.015, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.610
2025-08-04 17:21:16,420 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:21:16,420 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:21:16,420 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:21:16,423 - visualization.landscape_visualizer - INFO - 插值约束: 32 个点被约束到最小值 680.00
2025-08-04 17:21:16,427 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:21:16,656 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_172116.html
2025-08-04 17:21:16,703 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_172116.html
2025-08-04 17:21:16,703 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:21:16,704 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:21:16,704 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2949秒
2025-08-04 17:21:16,705 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 9.015384615384617, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 31273.2274556213, 'cluster_count': 0}, 'population_state': {'diversity': 0.6104536489151874, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0052, 'fitness_entropy': 0.9479479190038742, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 9.015)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754299276.4190886, 'performance_metrics': {}}}
2025-08-04 17:21:16,706 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:21:16,706 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:21:16,706 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:21:16,707 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:21:16,707 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:21:16,707 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:21:16,708 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:21:16,708 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:16,708 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:21:16,709 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-04 17:21:16,709 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:21:16,710 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:21:16,710 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-04 17:21:16,710 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:21:16,710 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:21:16,710 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,711 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,712 - ExplorationExpert - INFO - 探索路径生成完成，成本: 917.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,712 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 7, 6, 5, 8, 4, 0, 1, 3], 'cur_cost': 917.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 3, 6, 4, 2, 0, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 5, 0, 2, 4, 6, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 7, 5, 6, 4, 0, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,712 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 917.00)
2025-08-04 17:21:16,712 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:21:16,712 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:21:16,713 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,713 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,715 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1083.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,715 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 3, 0, 2, 5, 7, 6, 8, 4], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [6, 5, 7, 3, 8, 2, 4, 0, 1], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 3, 4, 2, 0, 8, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 7, 3, 2, 4, 8, 0, 1], 'cur_cost': 799.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,715 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1083.00)
2025-08-04 17:21:16,716 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:21:16,716 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,717 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 957.0
2025-08-04 17:21:16,781 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,781 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,782 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,783 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,783 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 8, 4, 0, 1, 3], 'cur_cost': 917.0}, {'tour': [1, 3, 0, 2, 5, 7, 6, 8, 4], 'cur_cost': 1083.0}, {'tour': array([3, 6, 8, 4, 7, 2, 0, 1, 5], dtype=int64), 'cur_cost': 957.0}, {'tour': [5, 3, 2, 0, 6, 1, 7, 8, 4], 'cur_cost': 1002.0}, {'tour': [1, 4, 0, 6, 7, 3, 5, 8, 2], 'cur_cost': 829.0}, {'tour': [0, 8, 3, 6, 1, 4, 2, 7, 5], 'cur_cost': 925.0}, {'tour': [0, 3, 4, 7, 1, 2, 8, 5, 6], 'cur_cost': 968.0}, {'tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0}, {'tour': [3, 4, 8, 0, 7, 1, 6, 2, 5], 'cur_cost': 1105.0}, {'tour': [6, 1, 5, 4, 7, 0, 8, 2, 3], 'cur_cost': 1091.0}]
2025-08-04 17:21:16,784 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:16,784 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:21:16,784 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([3, 6, 8, 4, 7, 2, 0, 1, 5], dtype=int64), 'cur_cost': 957.0, 'intermediate_solutions': [{'tour': array([8, 1, 7, 5, 2, 3, 4, 0, 6]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 7, 2, 3, 4, 0, 6]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 8, 1, 7, 3, 4, 0, 6]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 8, 1, 2, 3, 4, 0, 6]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 5, 8, 1, 3, 4, 0, 6]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,785 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 957.00)
2025-08-04 17:21:16,785 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:21:16,785 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:21:16,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,786 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,786 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 875.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,787 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [5, 3, 2, 8, 6, 1, 7, 0, 4], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 6, 0, 2, 3, 5, 4], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 6, 2, 0, 1, 7, 8, 4], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,787 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 875.00)
2025-08-04 17:21:16,788 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:21:16,788 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:21:16,788 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,788 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,790 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 5, 7, 8, 4, 2, 3, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 6, 3, 7, 5, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 3, 7, 5, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 0, 6, 7, 3, 5, 8], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,790 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 831.00)
2025-08-04 17:21:16,790 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:21:16,790 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:21:16,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,791 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,792 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 801.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,792 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 3, 7, 4, 2, 8, 6, 1], 'cur_cost': 801.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 6, 1, 4, 2, 5, 7], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 4, 1, 6, 3, 8, 0], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 5, 3, 6, 1, 4, 2, 7], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,792 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 801.00)
2025-08-04 17:21:16,793 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:21:16,793 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:21:16,793 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,793 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:21:16,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,794 - ExplorationExpert - INFO - 探索路径生成完成，成本: 967.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,795 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [8, 4, 0, 6, 7, 3, 5, 2, 1], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 7, 1, 8, 2, 5, 6], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 7, 4, 3, 8, 5, 6], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 7, 1, 2, 8, 5, 6, 4], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,795 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 967.00)
2025-08-04 17:21:16,795 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:21:16,795 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:21:16,795 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:21:16,796 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:21:16,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:21:16,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1087.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:21:16,798 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 8, 2, 6, 4, 0, 7, 5, 1], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [6, 5, 3, 4, 0, 1, 7, 2, 8], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 4, 0, 8, 2, 7, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:21:16,798 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1087.00)
2025-08-04 17:21:16,798 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-04 17:21:16,799 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,799 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,799 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1053.0
2025-08-04 17:21:16,863 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,863 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,864 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,865 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,865 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 8, 4, 0, 1, 3], 'cur_cost': 917.0}, {'tour': [1, 3, 0, 2, 5, 7, 6, 8, 4], 'cur_cost': 1083.0}, {'tour': array([3, 6, 8, 4, 7, 2, 0, 1, 5], dtype=int64), 'cur_cost': 957.0}, {'tour': [0, 7, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 875.0}, {'tour': [0, 6, 5, 7, 8, 4, 2, 3, 1], 'cur_cost': 831.0}, {'tour': [0, 5, 3, 7, 4, 2, 8, 6, 1], 'cur_cost': 801.0}, {'tour': [8, 4, 0, 6, 7, 3, 5, 2, 1], 'cur_cost': 967.0}, {'tour': [3, 8, 2, 6, 4, 0, 7, 5, 1], 'cur_cost': 1087.0}, {'tour': array([8, 5, 4, 1, 6, 2, 0, 3, 7], dtype=int64), 'cur_cost': 1053.0}, {'tour': [6, 1, 5, 4, 7, 0, 8, 2, 3], 'cur_cost': 1091.0}]
2025-08-04 17:21:16,867 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:16,867 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:21:16,868 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 5, 4, 1, 6, 2, 0, 3, 7], dtype=int64), 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 0, 7, 1, 6, 2, 5]), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 4, 3, 7, 1, 6, 2, 5]), 'cur_cost': 1147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 8, 4, 3, 1, 6, 2, 5]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 8, 4, 7, 1, 6, 2, 5]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 0, 8, 4, 1, 6, 2, 5]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,868 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1053.00)
2025-08-04 17:21:16,869 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:21:16,869 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:21:16,869 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:21:16,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 979.0
2025-08-04 17:21:16,935 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:21:16,936 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 17:21:16,936 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:21:16,937 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:21:16,937 - ExploitationExpert - INFO - populations: [{'tour': [2, 7, 6, 5, 8, 4, 0, 1, 3], 'cur_cost': 917.0}, {'tour': [1, 3, 0, 2, 5, 7, 6, 8, 4], 'cur_cost': 1083.0}, {'tour': array([3, 6, 8, 4, 7, 2, 0, 1, 5], dtype=int64), 'cur_cost': 957.0}, {'tour': [0, 7, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 875.0}, {'tour': [0, 6, 5, 7, 8, 4, 2, 3, 1], 'cur_cost': 831.0}, {'tour': [0, 5, 3, 7, 4, 2, 8, 6, 1], 'cur_cost': 801.0}, {'tour': [8, 4, 0, 6, 7, 3, 5, 2, 1], 'cur_cost': 967.0}, {'tour': [3, 8, 2, 6, 4, 0, 7, 5, 1], 'cur_cost': 1087.0}, {'tour': array([8, 5, 4, 1, 6, 2, 0, 3, 7], dtype=int64), 'cur_cost': 1053.0}, {'tour': array([3, 0, 1, 2, 5, 7, 8, 4, 6], dtype=int64), 'cur_cost': 979.0}]
2025-08-04 17:21:16,938 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 17:21:16,938 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:21:16,939 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([3, 0, 1, 2, 5, 7, 8, 4, 6], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([5, 1, 6, 4, 7, 0, 8, 2, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 1, 6, 7, 0, 8, 2, 3]), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 5, 1, 6, 0, 8, 2, 3]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 5, 1, 7, 0, 8, 2, 3]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 4, 5, 1, 0, 8, 2, 3]), 'cur_cost': 1012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:21:16,939 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 979.00)
2025-08-04 17:21:16,940 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:21:16,940 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:21:16,941 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 6, 5, 8, 4, 0, 1, 3], 'cur_cost': 917.0, 'intermediate_solutions': [{'tour': [8, 5, 7, 3, 6, 4, 2, 0, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 3, 7, 5, 0, 2, 4, 6, 1], 'cur_cost': 963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 3, 2, 7, 5, 6, 4, 0, 1], 'cur_cost': 951.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 0, 2, 5, 7, 6, 8, 4], 'cur_cost': 1083.0, 'intermediate_solutions': [{'tour': [6, 5, 7, 3, 8, 2, 4, 0, 1], 'cur_cost': 681.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 7, 3, 4, 2, 0, 8, 1], 'cur_cost': 936.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 7, 3, 2, 4, 8, 0, 1], 'cur_cost': 799.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 8, 4, 7, 2, 0, 1, 5], dtype=int64), 'cur_cost': 957.0, 'intermediate_solutions': [{'tour': array([8, 1, 7, 5, 2, 3, 4, 0, 6]), 'cur_cost': 1137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 8, 1, 7, 2, 3, 4, 0, 6]), 'cur_cost': 1052.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 8, 1, 7, 3, 4, 0, 6]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([7, 5, 8, 1, 2, 3, 4, 0, 6]), 'cur_cost': 1017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([7, 2, 5, 8, 1, 3, 4, 0, 6]), 'cur_cost': 1127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 6, 8, 4, 2, 3, 5, 1], 'cur_cost': 875.0, 'intermediate_solutions': [{'tour': [5, 3, 2, 8, 6, 1, 7, 0, 4], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 6, 0, 2, 3, 5, 4], 'cur_cost': 1002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 6, 2, 0, 1, 7, 8, 4], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 7, 8, 4, 2, 3, 1], 'cur_cost': 831.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 6, 3, 7, 5, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 6, 3, 7, 5, 8, 2], 'cur_cost': 857.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 0, 6, 7, 3, 5, 8], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 7, 4, 2, 8, 6, 1], 'cur_cost': 801.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 6, 1, 4, 2, 5, 7], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 4, 1, 6, 3, 8, 0], 'cur_cost': 925.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 5, 3, 6, 1, 4, 2, 7], 'cur_cost': 893.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 0, 6, 7, 3, 5, 2, 1], 'cur_cost': 967.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 7, 1, 8, 2, 5, 6], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 1, 7, 4, 3, 8, 5, 6], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 7, 1, 2, 8, 5, 6, 4], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 2, 6, 4, 0, 7, 5, 1], 'cur_cost': 1087.0, 'intermediate_solutions': [{'tour': [6, 5, 3, 4, 0, 1, 7, 2, 8], 'cur_cost': 876.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 4, 0, 8, 2, 7, 1], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 3, 4, 0, 8, 7, 2, 1], 'cur_cost': 948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 4, 1, 6, 2, 0, 3, 7], dtype=int64), 'cur_cost': 1053.0, 'intermediate_solutions': [{'tour': array([8, 4, 3, 0, 7, 1, 6, 2, 5]), 'cur_cost': 1129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 8, 4, 3, 7, 1, 6, 2, 5]), 'cur_cost': 1147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 8, 4, 3, 1, 6, 2, 5]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 0, 8, 4, 7, 1, 6, 2, 5]), 'cur_cost': 1118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 0, 8, 4, 1, 6, 2, 5]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 0, 1, 2, 5, 7, 8, 4, 6], dtype=int64), 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': array([5, 1, 6, 4, 7, 0, 8, 2, 3]), 'cur_cost': 1060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 5, 1, 6, 7, 0, 8, 2, 3]), 'cur_cost': 1099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 5, 1, 6, 0, 8, 2, 3]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 5, 1, 7, 0, 8, 2, 3]), 'cur_cost': 1152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 7, 4, 5, 1, 0, 8, 2, 3]), 'cur_cost': 1012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:21:16,943 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:21:16,944 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:21:16,945 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=801.000, 多样性=0.886
2025-08-04 17:21:16,945 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:21:16,945 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:21:16,945 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:21:16,946 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.004003338481374641, 'best_improvement': -0.03221649484536082}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00828729281767957}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.001650593540886286, 'recent_improvements': [-0.033033756960759865, 0.06306597181475833, -0.03633494404253244], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:21:16,946 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:21:16,949 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:21:16,949 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_172116.solution
2025-08-04 17:21:16,960 - __main__ - INFO - 评估统计 - 总次数: 219950.66666649008, 运行时间: 9.51s, 最佳成本: 680.0
2025-08-04 17:21:16,960 - __main__ - INFO - 实例 simple1_9 处理完成
