# -*- coding: utf-8 -*-
import numpy as np
import random

def generate_greedy_path(distance_matrix):
    """
    使用贪心算法生成一条新的路径
    
    参数:
        distance_matrix: 距离矩阵
        
    返回:
        tuple: (tour, cost) 生成的路径和对应的成本
    """
    n = len(distance_matrix)
    
    # 随机选择起始城市
    start_city = random.randint(0, n-1)
    
    # 初始化路径
    tour = [start_city]
    unvisited = set(range(n))
    unvisited.remove(start_city)
    
    # 贪心构建路径
    current_city = start_city
    while unvisited:
        # 找到距离当前城市最近的未访问城市
        next_city = min(unvisited, key=lambda city: distance_matrix[current_city][city])
        tour.append(next_city)
        unvisited.remove(next_city)
        current_city = next_city
    
    # 将路径转换为numpy数组 (int64 for consistency across modules)
    tour = np.array(tour, dtype=np.int64)
    
    # 计算路径成本
    cost = 0
    for i in range(n):
        cost += distance_matrix[tour[i]][tour[(i+1) % n]]
    
    return tour, cost


def generate_random_path(distance_matrix):
    """
    生成一条随机路径，作为备选方案
    
    参数:
        distance_matrix: 距离矩阵
        
    返回:
        tuple: (tour, cost) 生成的路径和对应的成本
    """
    n = len(distance_matrix)
    
    # 生成随机排列 (ensure int64 dtype)
    tour = np.random.permutation(n).astype(np.int64)
    
    # 计算路径成本
    cost = 0
    for i in range(n):
        cost += distance_matrix[tour[i]][tour[(i+1) % n]]
    
    return tour, cost


def generate_greedy_path_with_fallback(distance_matrix, num_attempts=3):
    """
    尝试生成多条贪心路径，选择成本最低的一条
    如果生成失败，则回退到随机路径
    
    参数:
        distance_matrix: 距离矩阵
        num_attempts: 尝试次数
        
    返回:
        tuple: (tour, cost) 生成的路径和对应的成本
    """
    try:
        # 尝试多次贪心生成，选择最好的
        best_tour = None
        best_cost = float('inf')
        
        for _ in range(num_attempts):
            tour, cost = generate_greedy_path(distance_matrix)
            if cost < best_cost:
                best_tour = tour
                best_cost = cost
        
        return best_tour, best_cost
    except Exception as e:
        # 如果贪心生成失败，回退到随机生成
        print(f"贪心路径生成失败: {str(e)}，回退到随机路径生成")
        return generate_random_path(distance_matrix)


# 对外暴露的主函数，默认使用带回退的贪心生成
# 注意：这里不要直接覆盖原函数名，避免递归调用导致栈溢出
generate_path = generate_random_path