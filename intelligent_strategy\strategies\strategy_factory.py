"""
Strategy factory for creating and managing strategy instances.

This module provides a centralized factory for creating strategy instances
and managing the strategy registry for the intelligent strategy selection system.
"""

from typing import Dict, Type, Optional, Any, List
import time
import logging

from ..core.data_structures import StrategyType
from ..core.strategy_interfaces import StrategyExecutionInterface

# Import strategy implementations
from .exploration_strategies import (
    StrongExplorationStrategy,
    BalancedExplorationStrategy,
    IntelligentExplorationStrategy
)
from .exploitation_strategies import (
    CautiousExploitationStrategy,
    ModerateExploitationStrategy,
    AggressiveExploitationStrategy,
    IntensiveExploitationStrategy
)
from .hybrid_strategies import (
    AdaptiveHybridStrategy,
    IterativeHybridStrategy
)


class StrategyFactory:
    """
    Factory class for creating strategy instances.
    
    This factory manages the registry of available strategies and provides
    methods to create strategy instances based on strategy types.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the strategy factory."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Strategy registry
        self._strategy_registry: Dict[StrategyType, Type] = {}
        self._strategy_instances: Dict[StrategyType, Any] = {}
        
        # Register default strategies
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """Register default strategy implementations."""
        # Exploration strategies
        self._strategy_registry[StrategyType.STRONG_EXPLORATION] = StrongExplorationStrategy
        self._strategy_registry[StrategyType.BALANCED_EXPLORATION] = BalancedExplorationStrategy
        self._strategy_registry[StrategyType.INTELLIGENT_EXPLORATION] = IntelligentExplorationStrategy
        
        # Exploitation strategies
        self._strategy_registry[StrategyType.CAUTIOUS_EXPLOITATION] = CautiousExploitationStrategy
        self._strategy_registry[StrategyType.MODERATE_EXPLOITATION] = ModerateExploitationStrategy
        self._strategy_registry[StrategyType.AGGRESSIVE_EXPLOITATION] = AggressiveExploitationStrategy
        self._strategy_registry[StrategyType.INTENSIVE_EXPLOITATION] = IntensiveExploitationStrategy
        
        # Hybrid strategies
        self._strategy_registry[StrategyType.HYBRID] = AdaptiveHybridStrategy
    
    def create_strategy(self, strategy_type: StrategyType, config: Optional[Dict] = None) -> Any:
        """
        Create a strategy instance.
        
        Args:
            strategy_type: Type of strategy to create
            config: Optional configuration for the strategy
            
        Returns:
            Strategy instance
            
        Raises:
            ValueError: If strategy type is not registered
        """
        if strategy_type not in self._strategy_registry:
            raise ValueError(f"Strategy type {strategy_type} is not registered")
        
        # Use singleton pattern for strategy instances
        if strategy_type not in self._strategy_instances:
            strategy_class = self._strategy_registry[strategy_type]
            strategy_config = {**(self.config.get('strategies', {}).get(strategy_type.value, {})), **(config or {})}
            self._strategy_instances[strategy_type] = strategy_class(strategy_config)
        
        return self._strategy_instances[strategy_type]
    
    def get_available_strategies(self) -> List[StrategyType]:
        """Get list of available strategy types."""
        return list(self._strategy_registry.keys())
    
    def register_strategy(self, strategy_type: StrategyType, strategy_class: Type):
        """
        Register a custom strategy implementation.
        
        Args:
            strategy_type: Strategy type to register
            strategy_class: Strategy class to register
        """
        self._strategy_registry[strategy_type] = strategy_class
        
        # Remove existing instance if any
        if strategy_type in self._strategy_instances:
            del self._strategy_instances[strategy_type]
        
        self.logger.info(f"Registered custom strategy: {strategy_type}")
    
    def get_strategy_info(self, strategy_type: StrategyType) -> Dict[str, Any]:
        """
        Get information about a strategy.
        
        Args:
            strategy_type: Strategy type to get info for
            
        Returns:
            Dictionary with strategy information
        """
        if strategy_type not in self._strategy_registry:
            return {}
        
        strategy_class = self._strategy_registry[strategy_type]
        strategy_instance = self.get_strategy_instance(strategy_type)
        
        return {
            'type': strategy_type,
            'class_name': strategy_class.__name__,
            'description': strategy_class.__doc__,
            'execution_count': getattr(strategy_instance, 'execution_count', 0),
            'success_rate': getattr(strategy_instance, 'get_success_rate', lambda: 0.0)(),
            'average_improvement': getattr(strategy_instance, 'get_average_improvement', lambda: 0.0)()
        }
    
    def get_strategy_instance(self, strategy_type: StrategyType) -> Any:
        """
        Get existing strategy instance or create new one.
        
        Args:
            strategy_type: Strategy type to get instance for
            
        Returns:
            Strategy instance
        """
        if strategy_type not in self._strategy_instances:
            return self.create_strategy(strategy_type)
        return self._strategy_instances[strategy_type]
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics for all strategies."""
        stats = {
            'total_strategies': len(self._strategy_registry),
            'instantiated_strategies': len(self._strategy_instances),
            'strategy_performance': {}
        }
        
        for strategy_type in self._strategy_registry:
            if strategy_type in self._strategy_instances:
                strategy_info = self.get_strategy_info(strategy_type)
                stats['strategy_performance'][strategy_type.value] = {
                    'execution_count': strategy_info['execution_count'],
                    'success_rate': strategy_info['success_rate'],
                    'average_improvement': strategy_info['average_improvement']
                }
        
        return stats
    
    def reset_strategy_statistics(self, strategy_type: Optional[StrategyType] = None):
        """
        Reset strategy statistics.
        
        Args:
            strategy_type: Specific strategy to reset, or None for all strategies
        """
        if strategy_type is not None:
            if strategy_type in self._strategy_instances:
                strategy = self._strategy_instances[strategy_type]
                strategy.execution_count = 0
                strategy.success_count = 0
                if hasattr(strategy, 'total_improvement'):
                    strategy.total_improvement = 0.0
        else:
            # Reset all strategies
            for strategy in self._strategy_instances.values():
                strategy.execution_count = 0
                strategy.success_count = 0
                if hasattr(strategy, 'total_improvement'):
                    strategy.total_improvement = 0.0
        
        self.logger.info(f"Reset statistics for {'all strategies' if strategy_type is None else strategy_type}")


class StrategyExecutionEngine:
    """
    Engine for executing strategies with proper context management.
    
    This class provides a high-level interface for strategy execution
    with proper error handling, logging, and performance monitoring.
    """
    
    def __init__(self, factory: StrategyFactory, config: Optional[Dict] = None):
        """Initialize the strategy execution engine."""
        self.factory = factory
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Execution statistics
        self.total_executions = 0
        self.successful_executions = 0
        self.total_execution_time = 0.0
    
    def execute_strategy(self, 
                        assignment: 'StrategyAssignment',
                        context: 'IndividualContext',
                        fitness_function: callable) -> 'ExecutionResult':
        """
        Execute a strategy assignment.
        
        Args:
            assignment: Strategy assignment to execute
            context: Individual context for execution
            fitness_function: Fitness evaluation function
            
        Returns:
            Execution result
        """
        self.total_executions += 1
        start_time = time.time()
        
        try:
            # Get strategy instance
            strategy = self.factory.get_strategy_instance(assignment.strategy_type)
            
            # Execute strategy
            result = strategy.execute(assignment, context, fitness_function)
            
            # Update statistics
            execution_time = time.time() - start_time
            self.total_execution_time += execution_time
            
            if result.success:
                self.successful_executions += 1
            
            self.logger.info(
                f"Executed {assignment.strategy_type.value} for individual {assignment.individual_id}: "
                f"{'SUCCESS' if result.success else 'FAILED'} "
                f"(improvement: {result.improvement:.6f}, time: {execution_time:.3f}s)"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.total_execution_time += execution_time
            
            self.logger.error(f"Error executing strategy {assignment.strategy_type.value}: {e}")
            
            # Return failed result
            from ..core.data_structures import ExecutionResult, ExecutionStatus
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution engine statistics."""
        return {
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'success_rate': self.successful_executions / max(1, self.total_executions),
            'total_execution_time': self.total_execution_time,
            'average_execution_time': self.total_execution_time / max(1, self.total_executions),
            'strategy_statistics': self.factory.get_strategy_statistics()
        }
    
    def reset_statistics(self):
        """Reset execution engine statistics."""
        self.total_executions = 0
        self.successful_executions = 0
        self.total_execution_time = 0.0
        self.factory.reset_strategy_statistics()
        
        self.logger.info("Reset execution engine statistics")


# Global factory instance
_global_factory = None


def get_strategy_factory(config: Optional[Dict] = None) -> StrategyFactory:
    """Get the global strategy factory instance."""
    global _global_factory
    if _global_factory is None:
        _global_factory = StrategyFactory(config)
    return _global_factory


def create_execution_engine(config: Optional[Dict] = None) -> StrategyExecutionEngine:
    """Create a new strategy execution engine."""
    factory = get_strategy_factory(config)
    return StrategyExecutionEngine(factory, config)
