# -*- coding: utf-8 -*-
"""
增强采样点数据结构

支持多种类型的采样点，包括当前种群、历史精英解、中间解等，
并提供相应的管理和筛选功能。
"""

import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging


class SamplingPointType(Enum):
    """采样点类型枚举"""
    CURRENT_POPULATION = "current_population"
    HISTORICAL_ELITE = "historical_elite"
    INTERMEDIATE_SOLUTION = "intermediate_solution"
    LOCAL_OPTIMA = "local_optima"
    EXPLORATION_CANDIDATE = "exploration_candidate"
    EXPLOITATION_CANDIDATE = "exploitation_candidate"


@dataclass
class SamplingPoint:
    """采样点数据结构"""
    solution: List[int]                    # 解的路径
    fitness: float                         # 适应度值
    point_type: SamplingPointType          # 采样点类型
    iteration: int                         # 产生的迭代次数
    timestamp: float = field(default_factory=time.time)  # 时间戳
    coordinates_2d: Optional[Tuple[float, float]] = None  # 2D坐标
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据
    
    def __post_init__(self):
        """后处理初始化"""
        if self.coordinates_2d is None:
            # 如果没有提供2D坐标，将在后续计算
            self.coordinates_2d = (0.0, 0.0)
    
    @property
    def age(self) -> float:
        """获取采样点的年龄（秒）"""
        return time.time() - self.timestamp
    
    @property
    def is_elite(self) -> bool:
        """判断是否为精英解"""
        return self.point_type == SamplingPointType.HISTORICAL_ELITE
    
    @property
    def is_current(self) -> bool:
        """判断是否为当前代解"""
        return self.point_type == SamplingPointType.CURRENT_POPULATION


class SamplingPointManager:
    """采样点管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化采样点管理器
        
        参数:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 采样点存储 - 优化为支持大量数据
        self.sampling_points: List[SamplingPoint] = []

        # 高效索引结构 - 按类型分组存储以提高查询性能
        self.points_by_type: Dict[SamplingPointType, List[SamplingPoint]] = {
            point_type: [] for point_type in SamplingPointType
        }

        # 迭代索引 - 快速按迭代查找
        self.points_by_iteration: Dict[int, List[SamplingPoint]] = {}

        # 配置参数 - 支持大量采样点
        self.max_total_points = self.config.get('max_total_points', 100000)  # 大幅增加默认值
        self.cleanup_threshold = self.config.get('cleanup_threshold', 1.0)   # 禁用清理阈值
        self.diversity_threshold = self.config.get('diversity_threshold', 0.1)

        # 统计信息
        self.stats = {
            'total_added': 0,
            'total_removed': 0,
            'last_cleanup': time.time(),
            'max_points_reached': 0  # 记录达到的最大采样点数量
        }
    
    def add_sampling_point(self, point: SamplingPoint) -> bool:
        """
        添加采样点 - 优化版本支持大量数据

        参数:
            point: 采样点

        返回:
            是否成功添加
        """
        try:
            # 禁用自动清理机制 - 直接添加采样点
            # 注释掉原有的清理逻辑以保留所有历史数据
            # if len(self.sampling_points) >= self.max_total_points * self.cleanup_threshold:
            #     self._cleanup_sampling_points()

            # 添加到主列表
            self.sampling_points.append(point)

            # 添加到类型索引
            self.points_by_type[point.point_type].append(point)

            # 添加到迭代索引
            if point.iteration not in self.points_by_iteration:
                self.points_by_iteration[point.iteration] = []
            self.points_by_iteration[point.iteration].append(point)

            # 更新统计信息
            self.stats['total_added'] += 1
            current_total = len(self.sampling_points)
            if current_total > self.stats['max_points_reached']:
                self.stats['max_points_reached'] = current_total

            return True

        except Exception as e:
            self.logger.error(f"添加采样点失败: {e}")
            return False
    
    def add_current_population(self, populations: List[List[int]], 
                             fitness_values: List[float], 
                             iteration: int) -> int:
        """
        添加当前种群采样点
        
        参数:
            populations: 种群个体
            fitness_values: 适应度值
            iteration: 迭代次数
            
        返回:
            添加的采样点数量
        """
        added_count = 0
        
        # 清除旧的当前种群采样点
        self.sampling_points = [p for p in self.sampling_points 
                               if p.point_type != SamplingPointType.CURRENT_POPULATION]
        
        # 添加新的当前种群采样点
        for solution, fitness in zip(populations, fitness_values):
            point = SamplingPoint(
                solution=solution.copy(),
                fitness=fitness,
                point_type=SamplingPointType.CURRENT_POPULATION,
                iteration=iteration
            )
            
            if self.add_sampling_point(point):
                added_count += 1
        
        return added_count
    
    def add_elite_solution(self, solution: List[int], fitness: float, 
                          iteration: int, metadata: Optional[Dict] = None) -> bool:
        """
        添加精英解采样点
        
        参数:
            solution: 解的路径
            fitness: 适应度值
            iteration: 迭代次数
            metadata: 额外元数据
            
        返回:
            是否成功添加
        """
        point = SamplingPoint(
            solution=solution.copy(),
            fitness=fitness,
            point_type=SamplingPointType.HISTORICAL_ELITE,
            iteration=iteration,
            metadata=metadata or {}
        )
        
        return self.add_sampling_point(point)
    
    def add_intermediate_solution(self, solution: List[int], fitness: float,
                                iteration: int, strategy_type: str) -> bool:
        """
        添加中间解采样点
        
        参数:
            solution: 解的路径
            fitness: 适应度值
            iteration: 迭代次数
            strategy_type: 策略类型
            
        返回:
            是否成功添加
        """
        point = SamplingPoint(
            solution=solution.copy(),
            fitness=fitness,
            point_type=SamplingPointType.INTERMEDIATE_SOLUTION,
            iteration=iteration,
            metadata={'strategy_type': strategy_type}
        )
        
        return self.add_sampling_point(point)
    
    def get_points_by_type(self, point_type: SamplingPointType) -> List[SamplingPoint]:
        """
        根据类型获取采样点 - 优化版本使用索引

        参数:
            point_type: 采样点类型

        返回:
            指定类型的采样点列表
        """
        # 使用类型索引直接返回，O(1)复杂度
        return self.points_by_type[point_type].copy()
    
    def get_all_points(self) -> List[SamplingPoint]:
        """获取所有采样点"""
        return self.sampling_points.copy()

    def get_points_by_iteration(self, iteration: int) -> List[SamplingPoint]:
        """
        根据迭代获取采样点 - 高效索引查询

        参数:
            iteration: 迭代次数

        返回:
            指定迭代的采样点列表
        """
        return self.points_by_iteration.get(iteration, []).copy()

    def get_points_by_iteration_range(self, start_iter: int, end_iter: int) -> List[SamplingPoint]:
        """
        根据迭代范围获取采样点

        参数:
            start_iter: 起始迭代
            end_iter: 结束迭代

        返回:
            指定迭代范围的采样点列表
        """
        result = []
        for iteration in range(start_iter, end_iter + 1):
            result.extend(self.points_by_iteration.get(iteration, []))
        return result
    
    def get_points_for_visualization(self) -> Dict[str, List[SamplingPoint]]:
        """
        获取用于可视化的采样点，按类型分组
        
        返回:
            按类型分组的采样点字典
        """
        result = {}
        
        for point_type in SamplingPointType:
            points = self.get_points_by_type(point_type)
            if points:
                result[point_type.value] = points
        
        return result
    
    def _cleanup_sampling_points(self):
        """
        清理采样点 - 已禁用

        注意：为了保留完整的进化历史轨迹，此方法已被禁用。
        所有采样点将被永久保留，不进行任何清理操作。
        """
        # 禁用清理机制 - 保留所有历史数据
        # 原有的清理逻辑已被注释掉以确保数据完整性

        original_count = len(self.sampling_points)

        # 仅更新统计信息中的清理时间戳，不删除任何数据
        self.stats['last_cleanup'] = time.time()

        # 记录禁用状态
        self.logger.debug(f"采样点清理已禁用 - 保留所有 {original_count} 个历史数据点")

        # 原有清理逻辑已被完全禁用：
        # - 不删除任何采样点
        # - 不限制采样点数量
        # - 保留完整的进化历史轨迹
        # - 所有类型的采样点都被永久保留
    
    def _diversity_sampling(self, points: List[SamplingPoint], 
                           max_count: int) -> List[SamplingPoint]:
        """
        多样性采样
        
        参数:
            points: 采样点列表
            max_count: 最大保留数量
            
        返回:
            采样后的点列表
        """
        if len(points) <= max_count:
            return points
        
        try:
            # 简单的多样性采样：按适应度排序后均匀采样
            points_sorted = sorted(points, key=lambda p: p.fitness)
            step = len(points_sorted) / max_count
            
            sampled_points = []
            for i in range(max_count):
                index = int(i * step)
                if index < len(points_sorted):
                    sampled_points.append(points_sorted[index])
            
            return sampled_points
            
        except Exception as e:
            self.logger.error(f"多样性采样失败: {e}")
            return points[:max_count]  # 回退到简单截取
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息 - 优化版本使用索引"""
        # 使用索引结构快速计算类型统计
        type_counts = {}
        for point_type in SamplingPointType:
            type_counts[point_type.value] = len(self.points_by_type[point_type])

        return {
            'total_points': len(self.sampling_points),
            'points_by_type': type_counts,
            'total_added': self.stats['total_added'],
            'total_removed': self.stats['total_removed'],
            'last_cleanup': self.stats['last_cleanup'],
            'max_points_reached': self.stats['max_points_reached'],
            'iterations_count': len(self.points_by_iteration),
            'memory_usage_estimate_mb': self._estimate_memory_usage()
        }

    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        try:
            # 粗略估算每个采样点的内存使用
            # SamplingPoint对象 + 解向量 + 元数据
            bytes_per_point = 200  # 估算值
            total_bytes = len(self.sampling_points) * bytes_per_point

            # 索引结构的额外开销
            index_overhead = len(self.points_by_iteration) * 50

            return (total_bytes + index_overhead) / (1024 * 1024)

        except Exception:
            return 0.0
