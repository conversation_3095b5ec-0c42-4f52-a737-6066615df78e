# -*- coding: utf-8 -*-
"""
增量更新机制模块

实现高效的增量计算算法和滑动窗口机制，确保O(n)或O(n log n)复杂度的实时更新。
"""

import numpy as np
from collections import deque, defaultdict
from typing import Dict, List, Any, Optional, Tuple
import logging
from numba import jit
import time

logger = logging.getLogger(__name__)


class SlidingWindow:
    """滑动窗口管理器，用于维护历史数据和增量统计"""

    def __init__(self, window_size: int = 10):
        """
        初始化滑动窗口

        参数:
            window_size: 窗口大小
        """
        self.window_size = window_size
        self.data = deque(maxlen=window_size)
        self.statistics = {
            'mean': 0.0,
            'variance': 0.0,
            'min': float('inf'),
            'max': float('-inf'),
            'trend': 0.0,
            'acceleration': 0.0
        }
        self._sum = 0.0
        self._sum_squares = 0.0
        self._count = 0

    def update(self, new_value: float) -> Dict[str, float]:
        """
        更新滑动窗口并计算增量统计

        参数:
            new_value: 新的数据值

        返回:
            更新后的统计信息
        """
        # 如果窗口已满，移除最旧的值
        old_value = None
        if len(self.data) == self.window_size:
            old_value = self.data[0]

        self.data.append(new_value)

        # 增量更新统计信息
        self._update_statistics_incremental(new_value, old_value)

        return self.statistics.copy()

    def _update_statistics_incremental(self, new_value: float, old_value: Optional[float]):
        """增量更新统计信息，O(1)复杂度"""
        if old_value is not None:
            # 移除旧值的贡献
            self._sum -= old_value
            self._sum_squares -= old_value ** 2
        else:
            self._count += 1

        # 添加新值的贡献
        self._sum += new_value
        self._sum_squares += new_value ** 2

        # 更新基本统计量
        n = len(self.data)
        if n > 0:
            self.statistics['mean'] = self._sum / n
            if n > 1:
                self.statistics['variance'] = (self._sum_squares - self._sum ** 2 / n) / (n - 1)
            else:
                self.statistics['variance'] = 0.0

        # 更新最值
        if len(self.data) > 0:
            self.statistics['min'] = min(self.data)
            self.statistics['max'] = max(self.data)

        # 计算趋势和加速度
        self._update_trend()

    def _update_trend(self):
        """计算趋势和加速度"""
        if len(self.data) < 3:
            self.statistics['trend'] = 0.0
            self.statistics['acceleration'] = 0.0
            return

        # 使用最小二乘法计算趋势
        n = len(self.data)
        x = np.arange(n)
        y = np.array(self.data)

        # 计算斜率（趋势）
        x_mean = x.mean()
        y_mean = y.mean()
        numerator = np.sum((x - x_mean) * (y - y_mean))
        denominator = np.sum((x - x_mean) ** 2)

        if denominator > 0:
            self.statistics['trend'] = numerator / denominator
        else:
            self.statistics['trend'] = 0.0

        # 计算加速度（二阶导数的近似）
        if len(self.data) >= 3:
            recent_trend = (self.data[-1] - self.data[-2])
            prev_trend = (self.data[-2] - self.data[-3])
            self.statistics['acceleration'] = recent_trend - prev_trend
        else:
            self.statistics['acceleration'] = 0.0


class IncrementalCache:
    """增量计算缓存管理器"""

    def __init__(self, max_size: int = 1000):
        """
        初始化缓存

        参数:
            max_size: 最大缓存大小
        """
        self.max_size = max_size
        self.cache = {}
        self.access_order = deque()
        self.access_count = defaultdict(int)

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            self.access_count[key] += 1
            return self.cache[key]
        return None

    def set(self, key: str, value: Any):
        """设置缓存值"""
        if len(self.cache) >= self.max_size:
            self._evict_lru()

        self.cache[key] = value
        self.access_order.append(key)
        self.access_count[key] += 1

    def _evict_lru(self):
        """移除最少使用的缓存项"""
        if not self.access_order:
            return

        # 找到访问次数最少的键
        min_count = min(self.access_count.values())
        lru_keys = [k for k, v in self.access_count.items() if v == min_count]

        # 移除最早的LRU键
        for key in self.access_order:
            if key in lru_keys:
                del self.cache[key]
                del self.access_count[key]
                self.access_order.remove(key)
                break


class IncrementalUpdater:
    """增量更新管理器，协调各种增量计算"""

    def __init__(self, window_size: int = 10, cache_size: int = 1000):
        """
        初始化增量更新管理器

        参数:
            window_size: 滑动窗口大小
            cache_size: 缓存大小
        """
        self.window_size = window_size
        self.cache = IncrementalCache(cache_size)

        # 为不同指标维护独立的滑动窗口
        self.windows = {
            'fitness_stats': SlidingWindow(window_size),
            'diversity': SlidingWindow(window_size),
            'convergence': SlidingWindow(window_size),
            'local_optima_density': SlidingWindow(window_size),
            'gradient_variance': SlidingWindow(window_size),
            'clustering_coefficient': SlidingWindow(window_size),
            'coverage_ratio': SlidingWindow(window_size)
        }

        # 存储上一次的计算结果用于增量更新
        self.previous_state = {
            'populations': None,
            'fitness_values': None,
            'similarity_matrix': None,
            'neighbor_graph': None,
            'grid_coverage': None
        }

        self.logger = logging.getLogger(__name__)

    def update_fitness_statistics(self, fitness_values: List[float]) -> Dict[str, float]:
        """
        增量更新适应度统计信息

        参数:
            fitness_values: 当前种群的适应度值

        返回:
            更新后的统计信息
        """
        if not fitness_values:
            return {}

        # 计算基本统计量
        mean_fitness = np.mean(fitness_values)
        std_fitness = np.std(fitness_values)
        min_fitness = np.min(fitness_values)
        max_fitness = np.max(fitness_values)

        # 更新滑动窗口
        stats = self.windows['fitness_stats'].update(mean_fitness)

        # 计算改进率（如果有历史数据）
        improvement_rate = 0.0
        if self.previous_state['fitness_values'] is not None:
            prev_best = np.min(self.previous_state['fitness_values'])
            curr_best = min_fitness
            if prev_best > 0:
                improvement_rate = (prev_best - curr_best) / prev_best

        # 更新缓存
        result = {
            'mean': mean_fitness,
            'std': std_fitness,
            'min': min_fitness,
            'max': max_fitness,
            'improvement_rate': improvement_rate,
            'trend': stats['trend'],
            'acceleration': stats['acceleration']
        }

        self.cache.set('fitness_stats', result)
        self.previous_state['fitness_values'] = fitness_values.copy()

        return result

    def update_diversity_metrics(self, populations: List[List[int]],
                               fitness_values: List[float]) -> Dict[str, float]:
        """
        增量更新多样性指标

        参数:
            populations: 当前种群（路径表示）
            fitness_values: 适应度值

        返回:
            多样性指标
        """
        if not populations or len(populations) < 2:
            return {'diversity': 0.0, 'entropy': 0.0}

        # 计算种群多样性
        diversity = self._calculate_population_diversity(populations)

        # 计算适应度熵
        entropy = self._calculate_fitness_entropy(fitness_values)

        # 更新滑动窗口
        diversity_stats = self.windows['diversity'].update(diversity)

        result = {
            'diversity': diversity,
            'entropy': entropy,
            'diversity_trend': diversity_stats['trend'],
            'diversity_acceleration': diversity_stats['acceleration']
        }

        self.cache.set('diversity_metrics', result)
        return result

    def update_neighbor_graph(self, populations: List[List[int]],
                            k_neighbors: int = 5) -> Dict[str, Any]:
        """
        增量更新邻居图

        参数:
            populations: 当前种群
            k_neighbors: 邻居数量

        返回:
            邻居图信息
        """
        # 检查是否可以增量更新
        cached_graph = self.cache.get('neighbor_graph')
        prev_populations = self.previous_state.get('populations')

        if cached_graph is not None and prev_populations is not None:
            # 尝试增量更新
            updated_graph = self._incremental_neighbor_update(
                cached_graph, prev_populations, populations, k_neighbors
            )
        else:
            # 完全重新计算
            updated_graph = self._build_neighbor_graph(populations, k_neighbors)

        self.cache.set('neighbor_graph', updated_graph)
        self.previous_state['populations'] = [pop.copy() for pop in populations]

        return updated_graph

    def _calculate_population_diversity(self, populations: List[List[int]]) -> float:
        """计算种群多样性"""
        if len(populations) < 2:
            return 0.0

        n = len(populations)
        total_distance = 0.0
        count = 0

        # 计算所有个体对之间的平均距离
        for i in range(n):
            for j in range(i + 1, n):
                distance = self._hamming_distance(populations[i], populations[j])
                total_distance += distance
                count += 1

        return total_distance / count if count > 0 else 0.0

    def _calculate_fitness_entropy(self, fitness_values: List[float]) -> float:
        """计算适应度熵"""
        if not fitness_values:
            return 0.0

        # 将适应度值分箱
        hist, _ = np.histogram(fitness_values, bins=10)
        hist = hist[hist > 0]  # 移除空箱

        if len(hist) <= 1:
            return 0.0

        # 计算概率分布
        probs = hist / np.sum(hist)

        # 计算熵
        entropy = -np.sum(probs * np.log2(probs))
        return entropy

    @staticmethod
    @jit(nopython=True)
    def _hamming_distance(path1: List[int], path2: List[int]) -> int:
        """计算两个路径的汉明距离"""
        if len(path1) != len(path2):
            return max(len(path1), len(path2))

        distance = 0
        for i in range(len(path1)):
            if path1[i] != path2[i]:
                distance += 1
        return distance

    def _build_neighbor_graph(self, populations: List[List[int]],
                            k_neighbors: int) -> Dict[str, Any]:
        """构建完整的邻居图"""
        n = len(populations)
        if n <= k_neighbors:
            k_neighbors = max(1, n - 1)

        # 计算距离矩阵
        distance_matrix = np.zeros((n, n))
        for i in range(n):
            for j in range(i + 1, n):
                dist = self._hamming_distance(populations[i], populations[j])
                distance_matrix[i, j] = dist
                distance_matrix[j, i] = dist

        # 构建k-近邻图
        neighbor_graph = {}
        for i in range(n):
            # 找到k个最近邻居
            distances = distance_matrix[i]
            neighbor_indices = np.argsort(distances)[1:k_neighbors+1]  # 排除自己
            neighbor_graph[i] = {
                'neighbors': neighbor_indices.tolist(),
                'distances': distances[neighbor_indices].tolist()
            }

        return {
            'graph': neighbor_graph,
            'distance_matrix': distance_matrix,
            'k': k_neighbors,
            'timestamp': time.time()
        }

    def _incremental_neighbor_update(self, cached_graph: Dict[str, Any],
                                   prev_populations: List[List[int]],
                                   curr_populations: List[List[int]],
                                   k_neighbors: int) -> Dict[str, Any]:
        """增量更新邻居图"""
        # 检查种群大小是否变化
        if len(prev_populations) != len(curr_populations):
            # 种群大小变化，需要完全重建
            return self._build_neighbor_graph(curr_populations, k_neighbors)

        # 找出变化的个体
        changed_indices = []
        for i, (prev_pop, curr_pop) in enumerate(zip(prev_populations, curr_populations)):
            if prev_pop != curr_pop:
                changed_indices.append(i)

        # 如果变化太多，直接重建
        if len(changed_indices) > len(curr_populations) * 0.3:
            return self._build_neighbor_graph(curr_populations, k_neighbors)

        # 增量更新
        graph = cached_graph['graph'].copy()
        distance_matrix = cached_graph['distance_matrix'].copy()

        # 更新变化个体的距离
        for i in changed_indices:
            for j in range(len(curr_populations)):
                if i != j:
                    dist = self._hamming_distance(curr_populations[i], curr_populations[j])
                    distance_matrix[i, j] = dist
                    distance_matrix[j, i] = dist

        # 重新计算受影响个体的邻居
        affected_indices = set(changed_indices)
        for i in changed_indices:
            # 添加原来以i为邻居的个体
            for j, node_info in graph.items():
                if i in node_info['neighbors']:
                    affected_indices.add(j)

        # 更新受影响个体的邻居关系
        for i in affected_indices:
            distances = distance_matrix[i]
            neighbor_indices = np.argsort(distances)[1:k_neighbors+1]
            graph[i] = {
                'neighbors': neighbor_indices.tolist(),
                'distances': distances[neighbor_indices].tolist()
            }

        return {
            'graph': graph,
            'distance_matrix': distance_matrix,
            'k': k_neighbors,
            'timestamp': time.time()
        }

    def update_grid_coverage(self, populations: List[List[int]],
                           grid_size: int = 100) -> Dict[str, float]:
        """
        增量更新网格覆盖率

        参数:
            populations: 当前种群
            grid_size: 网格大小

        返回:
            覆盖率指标
        """
        if not populations:
            return {'coverage_ratio': 0.0}

        # 获取缓存的网格状态
        cached_grid = self.cache.get('grid_coverage')
        prev_populations = self.previous_state.get('populations')

        if cached_grid is not None and prev_populations is not None:
            # 增量更新网格
            grid_state = self._incremental_grid_update(
                cached_grid, prev_populations, populations, grid_size
            )
        else:
            # 完全重新计算
            grid_state = self._build_grid_coverage(populations, grid_size)

        # 计算覆盖率
        total_cells = grid_size * grid_size
        covered_cells = np.sum(grid_state['grid'] > 0)
        coverage_ratio = covered_cells / total_cells

        # 更新滑动窗口
        coverage_stats = self.windows['coverage_ratio'].update(coverage_ratio)

        result = {
            'coverage_ratio': coverage_ratio,
            'covered_cells': int(covered_cells),
            'total_cells': total_cells,
            'coverage_trend': coverage_stats['trend']
        }

        self.cache.set('grid_coverage', grid_state)
        return result

    def _build_grid_coverage(self, populations: List[List[int]],
                           grid_size: int) -> Dict[str, Any]:
        """构建网格覆盖状态"""
        grid = np.zeros((grid_size, grid_size), dtype=int)

        # 将路径映射到网格坐标
        for i, path in enumerate(populations):
            x, y = self._path_to_grid_coords(path, grid_size)
            grid[x, y] += 1

        return {
            'grid': grid,
            'grid_size': grid_size,
            'timestamp': time.time()
        }

    def _incremental_grid_update(self, cached_grid: Dict[str, Any],
                               prev_populations: List[List[int]],
                               curr_populations: List[List[int]],
                               grid_size: int) -> Dict[str, Any]:
        """增量更新网格覆盖"""
        grid = cached_grid['grid'].copy()

        # 移除旧个体的贡献
        for path in prev_populations:
            x, y = self._path_to_grid_coords(path, grid_size)
            grid[x, y] = max(0, grid[x, y] - 1)

        # 添加新个体的贡献
        for path in curr_populations:
            x, y = self._path_to_grid_coords(path, grid_size)
            grid[x, y] += 1

        return {
            'grid': grid,
            'grid_size': grid_size,
            'timestamp': time.time()
        }

    def _path_to_grid_coords(self, path: List[int], grid_size: int) -> Tuple[int, int]:
        """将路径转换为网格坐标"""
        # 使用路径的哈希值来映射到网格坐标
        path_hash = hash(tuple(path))
        x = abs(path_hash) % grid_size
        y = abs(path_hash // grid_size) % grid_size
        return x, y

    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有缓存的指标"""
        metrics = {}

        # 从缓存中获取所有指标
        for key in ['fitness_stats', 'diversity_metrics', 'grid_coverage']:
            cached_value = self.cache.get(key)
            if cached_value is not None:
                metrics[key] = cached_value

        # 添加滑动窗口统计
        window_stats = {}
        for name, window in self.windows.items():
            if len(window.data) > 0:
                window_stats[name] = window.statistics.copy()

        metrics['window_statistics'] = window_stats

        return metrics

    def reset(self):
        """重置所有状态"""
        self.cache = IncrementalCache(self.cache.max_size)
        for window in self.windows.values():
            window.data.clear()
            window._sum = 0.0
            window._sum_squares = 0.0
            window._count = 0

        self.previous_state = {
            'populations': None,
            'fitness_values': None,
            'similarity_matrix': None,
            'neighbor_graph': None,
            'grid_coverage': None
        }