2025-08-04 17:42:26,427 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 17:42:26,428 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 17:42:26,430 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:26,432 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=747.000, 多样性=0.872
2025-08-04 17:42:26,432 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:42:26,433 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.872
2025-08-04 17:42:26,434 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:42:26,457 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 17:42:26,457 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:42:26,458 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 17:42:26,458 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 17:42:26,853 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -22.460, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.784
2025-08-04 17:42:26,853 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 17:42:26,854 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:42:26,855 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:42:27,295 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:42:31,181 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_174231.html
2025-08-04 17:42:31,214 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_174231.html
2025-08-04 17:42:31,214 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 17:42:31,215 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 17:42:31,215 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.7580秒
2025-08-04 17:42:31,216 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 17:42:31,216 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -22.46, 'local_optima_density': 0.1, 'gradient_variance': 16404.0884, 'cluster_count': 0}, 'population_state': {'diversity': 0.7844444444444444, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.94268071481726, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -22.460)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.784)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300546.8535936, 'performance_metrics': {}}}
2025-08-04 17:42:31,217 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:42:31,217 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:42:31,217 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:42:31,218 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:42:31,218 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:42:31,218 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 17:42:31,218 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:42:31,218 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:31,219 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:42:31,219 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-04 17:42:31,219 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:31,219 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:42:31,220 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 17:42:31,220 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:42:31,220 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:42:31,220 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,232 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:31,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,372 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 8, 1, 2, 4, 0, 6, 5, 3], 'cur_cost': 829.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,373 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 829.00)
2025-08-04 17:42:31,373 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:42:31,373 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:42:31,373 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,374 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:42:31,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1069.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,375 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [7, 4, 0, 5, 8, 1, 2, 6, 3], 'cur_cost': 1069.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,375 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1069.00)
2025-08-04 17:42:31,375 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:42:31,376 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:42:31,376 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,376 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:31,376 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,376 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1097.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,377 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [3, 8, 1, 6, 2, 7, 5, 0, 4], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,377 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1097.00)
2025-08-04 17:42:31,377 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:42:31,377 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:42:31,378 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,378 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:31,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1035.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,379 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 5, 3, 8, 1, 4, 0, 7, 6], 'cur_cost': 1035.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,380 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1035.00)
2025-08-04 17:42:31,380 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:42:31,380 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:42:31,381 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,381 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:31,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1000.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,382 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,382 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1000.00)
2025-08-04 17:42:31,382 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:42:31,382 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:42:31,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,383 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:31,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 814.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,383 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 5, 3, 4, 2, 8, 6, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,384 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 814.00)
2025-08-04 17:42:31,384 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:42:31,384 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:42:31,384 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:31,384 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:31,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:31,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 888.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:31,385 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 6, 0, 4, 7, 3, 8, 2, 1], 'cur_cost': 888.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:31,385 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 888.00)
2025-08-04 17:42:31,386 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:42:31,386 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:31,430 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:31,434 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1196.0
2025-08-04 17:42:34,539 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 17:42:34,539 - ExploitationExpert - INFO - res_population_costs: [832.0]
2025-08-04 17:42:34,540 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:34,540 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:34,540 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 1, 2, 4, 0, 6, 5, 3], 'cur_cost': 829.0}, {'tour': [7, 4, 0, 5, 8, 1, 2, 6, 3], 'cur_cost': 1069.0}, {'tour': [3, 8, 1, 6, 2, 7, 5, 0, 4], 'cur_cost': 1097.0}, {'tour': [2, 5, 3, 8, 1, 4, 0, 7, 6], 'cur_cost': 1035.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [7, 5, 3, 4, 2, 8, 6, 0, 1], 'cur_cost': 814.0}, {'tour': [5, 6, 0, 4, 7, 3, 8, 2, 1], 'cur_cost': 888.0}, {'tour': array([2, 0, 8, 1, 5, 7, 3, 4, 6], dtype=int64), 'cur_cost': 1196.0}, {'tour': array([6, 4, 2, 8, 1, 5, 0, 7, 3], dtype=int64), 'cur_cost': 1018.0}, {'tour': array([4, 5, 1, 8, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1187.0}]
2025-08-04 17:42:34,541 - ExploitationExpert - INFO - 局部搜索耗时: 3.11秒，最大迭代次数: 10
2025-08-04 17:42:34,541 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 17:42:34,542 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 0, 8, 1, 5, 7, 3, 4, 6], dtype=int64), 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 2, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 1, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 6, 8, 1, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 8, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 2, 6, 8, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:34,542 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1196.00)
2025-08-04 17:42:34,543 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:42:34,543 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:42:34,543 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:34,544 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:34,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:34,544 - ExplorationExpert - INFO - 探索路径生成完成，成本: 849.0, 路径长度: 9, 收集中间解: 0
2025-08-04 17:42:34,545 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 17:42:34,545 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 849.00)
2025-08-04 17:42:34,546 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 17:42:34,546 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:34,546 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:34,546 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1095.0
2025-08-04 17:42:36,554 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 17:42:36,554 - ExploitationExpert - INFO - res_population_costs: [832.0, 680.0]
2025-08-04 17:42:36,554 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64), array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64)]
2025-08-04 17:42:36,555 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:36,555 - ExploitationExpert - INFO - populations: [{'tour': [7, 8, 1, 2, 4, 0, 6, 5, 3], 'cur_cost': 829.0}, {'tour': [7, 4, 0, 5, 8, 1, 2, 6, 3], 'cur_cost': 1069.0}, {'tour': [3, 8, 1, 6, 2, 7, 5, 0, 4], 'cur_cost': 1097.0}, {'tour': [2, 5, 3, 8, 1, 4, 0, 7, 6], 'cur_cost': 1035.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [7, 5, 3, 4, 2, 8, 6, 0, 1], 'cur_cost': 814.0}, {'tour': [5, 6, 0, 4, 7, 3, 8, 2, 1], 'cur_cost': 888.0}, {'tour': array([2, 0, 8, 1, 5, 7, 3, 4, 6], dtype=int64), 'cur_cost': 1196.0}, {'tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0}, {'tour': array([4, 1, 3, 7, 2, 5, 0, 6, 8], dtype=int64), 'cur_cost': 1095.0}]
2025-08-04 17:42:36,556 - ExploitationExpert - INFO - 局部搜索耗时: 2.01秒，最大迭代次数: 10
2025-08-04 17:42:36,556 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 17:42:36,557 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([4, 1, 3, 7, 2, 5, 0, 6, 8], dtype=int64), 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 8, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 5, 4, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 1, 5, 4, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 1, 5, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 8, 1, 5, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:36,557 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1095.00)
2025-08-04 17:42:36,557 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:42:36,558 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:42:36,559 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 1, 2, 4, 0, 6, 5, 3], 'cur_cost': 829.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [7, 4, 0, 5, 8, 1, 2, 6, 3], 'cur_cost': 1069.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 1, 6, 2, 7, 5, 0, 4], 'cur_cost': 1097.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 8, 1, 4, 0, 7, 6], 'cur_cost': 1035.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 3, 4, 2, 8, 6, 0, 1], 'cur_cost': 814.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 0, 4, 7, 3, 8, 2, 1], 'cur_cost': 888.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 8, 1, 5, 7, 3, 4, 6], dtype=int64), 'cur_cost': 1196.0, 'intermediate_solutions': [{'tour': array([6, 8, 1, 2, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1053.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 6, 8, 1, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 6, 8, 1, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1077.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 6, 8, 7, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 2, 6, 8, 4, 0, 5, 3], dtype=int64), 'cur_cost': 1119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 3, 7, 2, 5, 0, 6, 8], dtype=int64), 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': array([1, 5, 4, 8, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 5, 4, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 8, 1, 5, 4, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1063.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 8, 1, 5, 0, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 0, 8, 1, 5, 2, 3, 7, 6], dtype=int64), 'cur_cost': 1176.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 17:42:36,560 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:42:36,560 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:36,561 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.919
2025-08-04 17:42:36,562 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 17:42:36,562 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 17:42:36,562 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:42:36,562 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04406118707461123, 'best_improvement': -0.08969210174029452}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05382436260623259}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5555555555555556, 'new_diversity': 0.5555555555555556, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:42:36,571 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 17:42:36,571 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 17:42:36,572 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 17:42:36,572 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:36,572 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=814.000, 多样性=0.919
2025-08-04 17:42:36,573 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:42:36,573 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.919
2025-08-04 17:42:36,574 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:42:36,574 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.556
2025-08-04 17:42:36,575 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 17:42:36,576 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:42:36,576 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 17:42:36,576 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 17:42:36,583 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -14.983, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.682
2025-08-04 17:42:36,583 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 17:42:36,583 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 17:42:36,583 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:42:36,586 - visualization.landscape_visualizer - INFO - 插值约束: 34 个点被约束到最小值 680.00
2025-08-04 17:42:36,590 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 17:42:36,663 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_174236.html
2025-08-04 17:42:36,701 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_174236.html
2025-08-04 17:42:36,701 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 17:42:36,701 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 17:42:36,701 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1260秒
2025-08-04 17:42:36,702 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14.983333333333334, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 25809.236388888883, 'cluster_count': 0}, 'population_state': {'diversity': 0.6818181818181818, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0022, 'fitness_entropy': 0.9517961807358445, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14.983)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300556.583778, 'performance_metrics': {}}}
2025-08-04 17:42:36,704 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:42:36,704 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:42:36,704 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:42:36,704 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:42:36,705 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:36,705 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 17:42:36,705 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:36,705 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:36,706 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:42:36,706 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:36,706 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:36,706 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:42:36,707 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 17:42:36,707 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:42:36,707 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:42:36,707 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:36,708 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:36,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,709 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1007.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:36,709 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 6, 0, 2, 7, 3, 5, 8, 1], 'cur_cost': 1007.0, 'intermediate_solutions': [{'tour': [7, 8, 1, 5, 4, 0, 6, 2, 3], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 3, 5, 6, 0, 4, 2], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 1, 3, 2, 4, 0, 6, 5], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:36,709 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1007.00)
2025-08-04 17:42:36,709 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:42:36,710 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:42:36,710 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:36,710 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:36,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:36,712 - ExplorationExpert - INFO - 探索路径生成完成，成本: 842.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:36,712 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 5, 8, 7, 2, 6, 3], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 8, 5, 1, 2, 6, 3], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 5, 8, 1, 6, 3, 2], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:36,712 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 842.00)
2025-08-04 17:42:36,713 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:42:36,713 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:36,713 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:36,714 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 986.0
2025-08-04 17:42:37,353 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,353 - ExploitationExpert - INFO - res_population_costs: [680.0, 832.0, 680.0]
2025-08-04 17:42:37,353 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:42:37,354 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,354 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 2, 7, 3, 5, 8, 1], 'cur_cost': 1007.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': array([2, 6, 1, 0, 3, 8, 4, 7, 5], dtype=int64), 'cur_cost': 986.0}, {'tour': [2, 5, 3, 8, 1, 4, 0, 7, 6], 'cur_cost': 1035.0}, {'tour': [1, 7, 6, 8, 0, 4, 2, 3, 5], 'cur_cost': 1000.0}, {'tour': [7, 5, 3, 4, 2, 8, 6, 0, 1], 'cur_cost': 814.0}, {'tour': [5, 6, 0, 4, 7, 3, 8, 2, 1], 'cur_cost': 888.0}, {'tour': [2, 0, 8, 1, 5, 7, 3, 4, 6], 'cur_cost': 1196.0}, {'tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0}, {'tour': [4, 1, 3, 7, 2, 5, 0, 6, 8], 'cur_cost': 1095.0}]
2025-08-04 17:42:37,355 - ExploitationExpert - INFO - 局部搜索耗时: 0.64秒，最大迭代次数: 10
2025-08-04 17:42:37,355 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 17:42:37,355 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([2, 6, 1, 0, 3, 8, 4, 7, 5], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 6, 2, 7, 5, 0, 4]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 8, 3, 2, 7, 5, 0, 4]), 'cur_cost': 1085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 6, 1, 8, 3, 7, 5, 0, 4]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 1, 8, 2, 7, 5, 0, 4]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 2, 6, 1, 8, 7, 5, 0, 4]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,356 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 986.00)
2025-08-04 17:42:37,356 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:42:37,356 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:42:37,357 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,357 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,357 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,357 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1115.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,358 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 1, 3, 2, 4, 7, 6, 8], 'cur_cost': 1115.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 8, 1, 7, 0, 4, 6], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 6, 7, 0, 4, 1, 8, 3], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 8, 2, 1, 4, 0, 7, 6], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,358 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1115.00)
2025-08-04 17:42:37,359 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:42:37,359 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:42:37,359 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,359 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,361 - ExplorationExpert - INFO - 探索路径生成完成，成本: 909.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,361 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 6, 0, 7, 4, 2, 8, 3, 1], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 3, 0, 4, 2, 8, 5], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 8, 0, 4, 3, 2, 5], 'cur_cost': 1177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 8, 1, 0, 4, 2, 3, 5], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,361 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 909.00)
2025-08-04 17:42:37,362 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:42:37,362 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:42:37,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,363 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 881.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,364 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 7, 8, 2, 3, 5, 6, 0, 4], 'cur_cost': 881.0, 'intermediate_solutions': [{'tour': [7, 5, 1, 4, 2, 8, 6, 0, 3], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 4, 6, 8, 2, 0, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,364 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 881.00)
2025-08-04 17:42:37,365 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:42:37,365 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:42:37,365 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,365 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 718.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,366 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 6, 3, 7, 0, 1, 4, 2, 8], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 4, 8, 3, 7, 2, 1], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 4, 7, 3, 2, 8, 1], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 4, 1, 7, 3, 8, 2], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,367 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 718.00)
2025-08-04 17:42:37,367 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:42:37,367 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,367 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,367 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 996.0
2025-08-04 17:42:37,374 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,374 - ExploitationExpert - INFO - res_population_costs: [680.0, 832.0, 680.0]
2025-08-04 17:42:37,374 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 17:42:37,375 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,375 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 2, 7, 3, 5, 8, 1], 'cur_cost': 1007.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': array([2, 6, 1, 0, 3, 8, 4, 7, 5], dtype=int64), 'cur_cost': 986.0}, {'tour': [0, 5, 1, 3, 2, 4, 7, 6, 8], 'cur_cost': 1115.0}, {'tour': [5, 6, 0, 7, 4, 2, 8, 3, 1], 'cur_cost': 909.0}, {'tour': [1, 7, 8, 2, 3, 5, 6, 0, 4], 'cur_cost': 881.0}, {'tour': [5, 6, 3, 7, 0, 1, 4, 2, 8], 'cur_cost': 718.0}, {'tour': array([1, 6, 4, 5, 8, 3, 7, 0, 2], dtype=int64), 'cur_cost': 996.0}, {'tour': [6, 7, 8, 5, 3, 0, 4, 2, 1], 'cur_cost': 849.0}, {'tour': [4, 1, 3, 7, 2, 5, 0, 6, 8], 'cur_cost': 1095.0}]
2025-08-04 17:42:37,376 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,376 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 17:42:37,377 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([1, 6, 4, 5, 8, 3, 7, 0, 2], dtype=int64), 'cur_cost': 996.0, 'intermediate_solutions': [{'tour': array([8, 0, 2, 1, 5, 7, 3, 4, 6]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 0, 2, 5, 7, 3, 4, 6]), 'cur_cost': 1133.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 1, 8, 0, 2, 7, 3, 4, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 8, 0, 5, 7, 3, 4, 6]), 'cur_cost': 1169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 1, 8, 0, 7, 3, 4, 6]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,377 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 996.00)
2025-08-04 17:42:37,378 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:42:37,378 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:42:37,378 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,379 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,379 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,379 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,380 - ExplorationExpert - INFO - 探索路径生成完成，成本: 841.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,380 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 2, 0, 6, 5, 7, 3, 8, 1], 'cur_cost': 841.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 2, 0, 4, 3, 1], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 5, 3, 0, 1, 2, 4], 'cur_cost': 837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 8, 5, 0, 4, 2, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,381 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 841.00)
2025-08-04 17:42:37,381 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:42:37,381 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:42:37,381 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,382 - ExplorationExpert - INFO - 探索路径生成完成，成本: 970.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,383 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0, 'intermediate_solutions': [{'tour': [4, 1, 3, 7, 2, 5, 8, 6, 0], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 3, 7, 2, 5, 0, 8, 6], 'cur_cost': 1189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 7, 2, 5, 0, 1, 6, 8], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,383 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 970.00)
2025-08-04 17:42:37,383 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:42:37,384 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:42:37,384 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 2, 7, 3, 5, 8, 1], 'cur_cost': 1007.0, 'intermediate_solutions': [{'tour': [7, 8, 1, 5, 4, 0, 6, 2, 3], 'cur_cost': 1150.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 3, 5, 6, 0, 4, 2], 'cur_cost': 926.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 1, 3, 2, 4, 0, 6, 5], 'cur_cost': 954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'intermediate_solutions': [{'tour': [1, 4, 0, 5, 8, 7, 2, 6, 3], 'cur_cost': 1089.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 4, 0, 8, 5, 1, 2, 6, 3], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 4, 0, 5, 8, 1, 6, 3, 2], 'cur_cost': 1080.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 6, 1, 0, 3, 8, 4, 7, 5], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([1, 8, 3, 6, 2, 7, 5, 0, 4]), 'cur_cost': 1064.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 1, 8, 3, 2, 7, 5, 0, 4]), 'cur_cost': 1085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 6, 1, 8, 3, 7, 5, 0, 4]), 'cur_cost': 961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 6, 1, 8, 2, 7, 5, 0, 4]), 'cur_cost': 1030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 2, 6, 1, 8, 7, 5, 0, 4]), 'cur_cost': 1124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 3, 2, 4, 7, 6, 8], 'cur_cost': 1115.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 8, 1, 7, 0, 4, 6], 'cur_cost': 1121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 6, 7, 0, 4, 1, 8, 3], 'cur_cost': 1003.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 8, 2, 1, 4, 0, 7, 6], 'cur_cost': 805.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 0, 7, 4, 2, 8, 3, 1], 'cur_cost': 909.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 3, 0, 4, 2, 8, 5], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 8, 0, 4, 3, 2, 5], 'cur_cost': 1177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 8, 1, 0, 4, 2, 3, 5], 'cur_cost': 862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 8, 2, 3, 5, 6, 0, 4], 'cur_cost': 881.0, 'intermediate_solutions': [{'tour': [7, 5, 1, 4, 2, 8, 6, 0, 3], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 3, 4, 6, 8, 2, 0, 1], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 6, 3, 4, 2, 8, 0, 1], 'cur_cost': 822.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 3, 7, 0, 1, 4, 2, 8], 'cur_cost': 718.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 4, 8, 3, 7, 2, 1], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 4, 7, 3, 2, 8, 1], 'cur_cost': 997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 4, 1, 7, 3, 8, 2], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 4, 5, 8, 3, 7, 0, 2], dtype=int64), 'cur_cost': 996.0, 'intermediate_solutions': [{'tour': array([8, 0, 2, 1, 5, 7, 3, 4, 6]), 'cur_cost': 1109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 8, 0, 2, 5, 7, 3, 4, 6]), 'cur_cost': 1133.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 1, 8, 0, 2, 7, 3, 4, 6]), 'cur_cost': 1136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 8, 0, 5, 7, 3, 4, 6]), 'cur_cost': 1169.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 5, 1, 8, 0, 7, 3, 4, 6]), 'cur_cost': 1255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 0, 6, 5, 7, 3, 8, 1], 'cur_cost': 841.0, 'intermediate_solutions': [{'tour': [6, 7, 8, 5, 2, 0, 4, 3, 1], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 5, 3, 0, 1, 2, 4], 'cur_cost': 837.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 7, 8, 5, 0, 4, 2, 1], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0, 'intermediate_solutions': [{'tour': [4, 1, 3, 7, 2, 5, 8, 6, 0], 'cur_cost': 1056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 3, 7, 2, 5, 0, 8, 6], 'cur_cost': 1189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 3, 7, 2, 5, 0, 1, 6, 8], 'cur_cost': 1009.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:42:37,386 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:42:37,387 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,387 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.894
2025-08-04 17:42:37,388 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 17:42:37,388 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 17:42:37,388 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:42:37,388 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.0823829681600452, 'best_improvement': 0.11793611793611794}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.026881720430107642}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 17:42:37,389 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 17:42:37,389 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 17:42:37,389 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 17:42:37,390 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,390 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=718.000, 多样性=0.894
2025-08-04 17:42:37,390 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:42:37,391 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.894
2025-08-04 17:42:37,391 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:42:37,392 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.704
2025-08-04 17:42:37,393 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 17:42:37,393 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:42:37,393 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:42:37,394 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:42:37,401 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 9.631, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.611
2025-08-04 17:42:37,401 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 17:42:37,401 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:42:37,401 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:42:37,455 - visualization.landscape_visualizer - INFO - 插值约束: 265 个点被约束到最小值 680.00
2025-08-04 17:42:37,458 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:42:37,529 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_174237.html
2025-08-04 17:42:37,569 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_174237.html
2025-08-04 17:42:37,569 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 17:42:37,569 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 17:42:37,569 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1758秒
2025-08-04 17:42:37,570 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 9.630769230769229, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 27908.552899408278, 'cluster_count': 0}, 'population_state': {'diversity': 0.611439842209073, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0033, 'fitness_entropy': 0.9272635841016705, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 9.631)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300557.4016922, 'performance_metrics': {}}}
2025-08-04 17:42:37,570 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:42:37,571 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:42:37,571 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:42:37,571 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:42:37,571 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:37,571 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 17:42:37,572 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:37,572 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:37,572 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:42:37,572 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-04 17:42:37,573 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:37,573 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:42:37,573 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 17:42:37,573 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:42:37,573 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,574 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,574 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1028.0
2025-08-04 17:42:37,581 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,581 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,582 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,582 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,583 - ExploitationExpert - INFO - populations: [{'tour': array([5, 7, 4, 3, 8, 0, 6, 1, 2], dtype=int64), 'cur_cost': 1028.0}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0}, {'tour': [2, 6, 1, 0, 3, 8, 4, 7, 5], 'cur_cost': 986.0}, {'tour': [0, 5, 1, 3, 2, 4, 7, 6, 8], 'cur_cost': 1115.0}, {'tour': [5, 6, 0, 7, 4, 2, 8, 3, 1], 'cur_cost': 909.0}, {'tour': [1, 7, 8, 2, 3, 5, 6, 0, 4], 'cur_cost': 881.0}, {'tour': [5, 6, 3, 7, 0, 1, 4, 2, 8], 'cur_cost': 718.0}, {'tour': [1, 6, 4, 5, 8, 3, 7, 0, 2], 'cur_cost': 996.0}, {'tour': [4, 2, 0, 6, 5, 7, 3, 8, 1], 'cur_cost': 841.0}, {'tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0}]
2025-08-04 17:42:37,583 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,583 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 17:42:37,584 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([5, 7, 4, 3, 8, 0, 6, 1, 2], dtype=int64), 'cur_cost': 1028.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 2, 7, 3, 5, 8, 1]), 'cur_cost': 871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 4, 7, 3, 5, 8, 1]), 'cur_cost': 1002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 0, 6, 4, 3, 5, 8, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 0, 6, 7, 3, 5, 8, 1]), 'cur_cost': 864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 2, 0, 6, 3, 5, 8, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,584 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1028.00)
2025-08-04 17:42:37,585 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:42:37,585 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:42:37,585 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,585 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,586 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,586 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,587 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 7, 2, 4, 5, 3, 8, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 7, 4, 0, 2, 1, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 0, 7, 3, 4, 2, 1, 6], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,587 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 847.00)
2025-08-04 17:42:37,587 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 17:42:37,587 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 17:42:37,587 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,588 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,589 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1094.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,589 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 6, 3, 4, 0, 5, 7, 8, 1], 'cur_cost': 1094.0, 'intermediate_solutions': [{'tour': [2, 7, 1, 0, 3, 8, 4, 6, 5], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 5, 7, 4, 8, 3, 0], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 0, 8, 3, 4, 7, 5], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,589 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1094.00)
2025-08-04 17:42:37,589 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 17:42:37,589 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,590 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,590 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 986.0
2025-08-04 17:42:37,596 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,597 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,597 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,599 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,599 - ExploitationExpert - INFO - populations: [{'tour': array([5, 7, 4, 3, 8, 0, 6, 1, 2], dtype=int64), 'cur_cost': 1028.0}, {'tour': [6, 7, 2, 4, 5, 3, 8, 0, 1], 'cur_cost': 847.0}, {'tour': [2, 6, 3, 4, 0, 5, 7, 8, 1], 'cur_cost': 1094.0}, {'tour': array([2, 1, 6, 4, 3, 8, 5, 7, 0], dtype=int64), 'cur_cost': 986.0}, {'tour': [5, 6, 0, 7, 4, 2, 8, 3, 1], 'cur_cost': 909.0}, {'tour': [1, 7, 8, 2, 3, 5, 6, 0, 4], 'cur_cost': 881.0}, {'tour': [5, 6, 3, 7, 0, 1, 4, 2, 8], 'cur_cost': 718.0}, {'tour': [1, 6, 4, 5, 8, 3, 7, 0, 2], 'cur_cost': 996.0}, {'tour': [4, 2, 0, 6, 5, 7, 3, 8, 1], 'cur_cost': 841.0}, {'tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0}]
2025-08-04 17:42:37,600 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,600 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 17:42:37,601 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 1, 6, 4, 3, 8, 5, 7, 0], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([1, 5, 0, 3, 2, 4, 7, 6, 8]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 5, 0, 2, 4, 7, 6, 8]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 1, 5, 0, 4, 7, 6, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 1, 5, 2, 4, 7, 6, 8]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 3, 1, 5, 4, 7, 6, 8]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,601 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 986.00)
2025-08-04 17:42:37,601 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:42:37,602 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:42:37,602 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,603 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,603 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,604 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 802.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,604 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 5, 7, 6, 8, 2, 4, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 7, 1, 2, 8, 3, 4], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 7, 3, 8, 2, 4, 1], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 7, 2, 4, 8, 3, 1], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,605 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 802.00)
2025-08-04 17:42:37,605 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:42:37,606 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:42:37,606 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,606 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,607 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1148.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,607 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 3, 0, 5, 2, 6, 7, 8, 4], 'cur_cost': 1148.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 6, 3, 5, 2, 0, 4], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 8, 2, 3, 5, 4, 0, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 2, 3, 6, 5, 0, 4], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,608 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1148.00)
2025-08-04 17:42:37,608 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 17:42:37,608 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 17:42:37,608 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,608 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:42:37,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1051.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,610 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 2, 0, 5, 8, 4, 1, 3, 7], 'cur_cost': 1051.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 7, 1, 0, 4, 2, 8], 'cur_cost': 737.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 1, 0, 7, 3, 4, 2, 8], 'cur_cost': 751.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 3, 2, 7, 0, 1, 4, 8], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,610 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 1051.00)
2025-08-04 17:42:37,610 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-04 17:42:37,611 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,611 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,611 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 947.0
2025-08-04 17:42:37,619 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,619 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,619 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,620 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,620 - ExploitationExpert - INFO - populations: [{'tour': array([5, 7, 4, 3, 8, 0, 6, 1, 2], dtype=int64), 'cur_cost': 1028.0}, {'tour': [6, 7, 2, 4, 5, 3, 8, 0, 1], 'cur_cost': 847.0}, {'tour': [2, 6, 3, 4, 0, 5, 7, 8, 1], 'cur_cost': 1094.0}, {'tour': array([2, 1, 6, 4, 3, 8, 5, 7, 0], dtype=int64), 'cur_cost': 986.0}, {'tour': [3, 5, 7, 6, 8, 2, 4, 0, 1], 'cur_cost': 802.0}, {'tour': [1, 3, 0, 5, 2, 6, 7, 8, 4], 'cur_cost': 1148.0}, {'tour': [6, 2, 0, 5, 8, 4, 1, 3, 7], 'cur_cost': 1051.0}, {'tour': array([2, 4, 0, 5, 6, 7, 1, 8, 3], dtype=int64), 'cur_cost': 947.0}, {'tour': [4, 2, 0, 6, 5, 7, 3, 8, 1], 'cur_cost': 841.0}, {'tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0}]
2025-08-04 17:42:37,621 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,621 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 17:42:37,621 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 4, 0, 5, 6, 7, 1, 8, 3], dtype=int64), 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': array([4, 6, 1, 5, 8, 3, 7, 0, 2]), 'cur_cost': 937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 6, 1, 8, 3, 7, 0, 2]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 4, 6, 1, 3, 7, 0, 2]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 4, 6, 8, 3, 7, 0, 2]), 'cur_cost': 1084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 4, 6, 3, 7, 0, 2]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,622 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 947.00)
2025-08-04 17:42:37,622 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:42:37,622 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:42:37,622 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,623 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,624 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1033.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,624 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 6, 3, 7, 5, 8, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 6, 5, 7, 1, 8, 3], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 0, 6, 7, 5, 3, 8, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,624 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1033.00)
2025-08-04 17:42:37,625 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:42:37,625 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:42:37,625 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,625 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 816.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,627 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 7, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 816.0, 'intermediate_solutions': [{'tour': [7, 8, 4, 1, 3, 5, 6, 0, 2], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 1, 4, 8, 7, 3, 6, 0, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,627 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 816.00)
2025-08-04 17:42:37,628 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:42:37,628 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:42:37,629 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 4, 3, 8, 0, 6, 1, 2], dtype=int64), 'cur_cost': 1028.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 2, 7, 3, 5, 8, 1]), 'cur_cost': 871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 4, 7, 3, 5, 8, 1]), 'cur_cost': 1002.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 0, 6, 4, 3, 5, 8, 1]), 'cur_cost': 1086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 0, 6, 7, 3, 5, 8, 1]), 'cur_cost': 864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 7, 2, 0, 6, 3, 5, 8, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 2, 4, 5, 3, 8, 0, 1], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [8, 5, 3, 7, 4, 0, 2, 1, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 5, 0, 7, 3, 4, 2, 1, 6], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 3, 4, 0, 5, 7, 8, 1], 'cur_cost': 1094.0, 'intermediate_solutions': [{'tour': [2, 7, 1, 0, 3, 8, 4, 6, 5], 'cur_cost': 987.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 1, 5, 7, 4, 8, 3, 0], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 0, 8, 3, 4, 7, 5], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 6, 4, 3, 8, 5, 7, 0], dtype=int64), 'cur_cost': 986.0, 'intermediate_solutions': [{'tour': array([1, 5, 0, 3, 2, 4, 7, 6, 8]), 'cur_cost': 1113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 5, 0, 2, 4, 7, 6, 8]), 'cur_cost': 1034.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 1, 5, 0, 4, 7, 6, 8]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 1, 5, 2, 4, 7, 6, 8]), 'cur_cost': 1135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 2, 3, 1, 5, 4, 7, 6, 8]), 'cur_cost': 1201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 7, 6, 8, 2, 4, 0, 1], 'cur_cost': 802.0, 'intermediate_solutions': [{'tour': [5, 6, 0, 7, 1, 2, 8, 3, 4], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 0, 7, 3, 8, 2, 4, 1], 'cur_cost': 817.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 0, 7, 2, 4, 8, 3, 1], 'cur_cost': 940.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 0, 5, 2, 6, 7, 8, 4], 'cur_cost': 1148.0, 'intermediate_solutions': [{'tour': [1, 7, 8, 6, 3, 5, 2, 0, 4], 'cur_cost': 1005.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 8, 2, 3, 5, 4, 0, 6], 'cur_cost': 965.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 8, 2, 3, 6, 5, 0, 4], 'cur_cost': 950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 2, 0, 5, 8, 4, 1, 3, 7], 'cur_cost': 1051.0, 'intermediate_solutions': [{'tour': [5, 6, 3, 7, 1, 0, 4, 2, 8], 'cur_cost': 737.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 6, 1, 0, 7, 3, 4, 2, 8], 'cur_cost': 751.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 3, 2, 7, 0, 1, 4, 8], 'cur_cost': 877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 4, 0, 5, 6, 7, 1, 8, 3], dtype=int64), 'cur_cost': 947.0, 'intermediate_solutions': [{'tour': array([4, 6, 1, 5, 8, 3, 7, 0, 2]), 'cur_cost': 937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 6, 1, 8, 3, 7, 0, 2]), 'cur_cost': 1108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 4, 6, 1, 3, 7, 0, 2]), 'cur_cost': 1045.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 4, 6, 8, 3, 7, 0, 2]), 'cur_cost': 1084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 4, 6, 3, 7, 0, 2]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 6, 3, 7, 5, 8, 1], 'cur_cost': 892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 0, 6, 5, 7, 1, 8, 3], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 0, 6, 7, 5, 3, 8, 1], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 816.0, 'intermediate_solutions': [{'tour': [7, 8, 4, 1, 3, 5, 6, 0, 2], 'cur_cost': 939.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 1, 4, 8, 7, 3, 6, 0, 2], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 1, 5, 3, 6, 0, 2], 'cur_cost': 970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:42:37,632 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:42:37,632 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,633 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=802.000, 多样性=0.879
2025-08-04 17:42:37,633 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 17:42:37,634 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 17:42:37,634 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:42:37,634 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06638382223182264, 'best_improvement': -0.116991643454039}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.016574585635359015}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:42:37,635 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 17:42:37,635 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 17:42:37,636 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 17:42:37,636 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,636 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=802.000, 多样性=0.879
2025-08-04 17:42:37,637 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:42:37,637 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.879
2025-08-04 17:42:37,638 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:42:37,638 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.704
2025-08-04 17:42:37,640 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 17:42:37,640 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:42:37,640 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:42:37,640 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:42:37,648 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.231, 适应度梯度: 1.954, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.606
2025-08-04 17:42:37,649 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 17:42:37,649 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:42:37,649 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:42:37,652 - visualization.landscape_visualizer - INFO - 插值约束: 34 个点被约束到最小值 680.00
2025-08-04 17:42:37,656 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:42:37,775 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_174237.html
2025-08-04 17:42:37,820 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_174237.html
2025-08-04 17:42:37,821 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 17:42:37,821 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 17:42:37,821 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1813秒
2025-08-04 17:42:37,822 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23076923076923078, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1.953846153846158, 'local_optima_density': 0.23076923076923078, 'gradient_variance': 18764.136331360944, 'cluster_count': 0}, 'population_state': {'diversity': 0.6055226824457594, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.957713559837711, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.954)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300557.6492364, 'performance_metrics': {}}}
2025-08-04 17:42:37,822 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:42:37,823 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:42:37,823 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:42:37,823 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:42:37,824 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:37,824 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 17:42:37,824 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:37,825 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:37,825 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:42:37,825 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:37,826 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:37,826 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:42:37,826 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-04 17:42:37,827 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 17:42:37,827 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 17:42:37,827 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,828 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,829 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1112.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,830 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 5, 4, 7, 2, 3, 8, 0, 6], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [5, 7, 4, 3, 6, 0, 8, 1, 2], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 4, 3, 1, 6, 0, 8, 2], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 4, 3, 8, 0, 1, 2], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,831 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1112.00)
2025-08-04 17:42:37,832 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:42:37,832 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:42:37,832 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,833 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,834 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,835 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 736.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,835 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0, 'intermediate_solutions': [{'tour': [8, 7, 2, 4, 5, 3, 6, 0, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 3, 5, 4, 8, 0, 1], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 2, 4, 5, 3, 0, 1, 8], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,836 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 736.00)
2025-08-04 17:42:37,836 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:42:37,836 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,837 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,837 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1078.0
2025-08-04 17:42:37,844 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,844 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,845 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,846 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,846 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 4, 7, 2, 3, 8, 0, 6], 'cur_cost': 1112.0}, {'tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0}, {'tour': array([4, 6, 7, 3, 8, 0, 2, 5, 1], dtype=int64), 'cur_cost': 1078.0}, {'tour': [2, 1, 6, 4, 3, 8, 5, 7, 0], 'cur_cost': 986.0}, {'tour': [3, 5, 7, 6, 8, 2, 4, 0, 1], 'cur_cost': 802.0}, {'tour': [1, 3, 0, 5, 2, 6, 7, 8, 4], 'cur_cost': 1148.0}, {'tour': [6, 2, 0, 5, 8, 4, 1, 3, 7], 'cur_cost': 1051.0}, {'tour': [2, 4, 0, 5, 6, 7, 1, 8, 3], 'cur_cost': 947.0}, {'tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0}, {'tour': [5, 7, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 816.0}]
2025-08-04 17:42:37,847 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,847 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 17:42:37,848 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([4, 6, 7, 3, 8, 0, 2, 5, 1], dtype=int64), 'cur_cost': 1078.0, 'intermediate_solutions': [{'tour': array([3, 6, 2, 4, 0, 5, 7, 8, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 6, 2, 0, 5, 7, 8, 1]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 3, 6, 2, 5, 7, 8, 1]), 'cur_cost': 1044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 6, 0, 5, 7, 8, 1]), 'cur_cost': 972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 3, 6, 5, 7, 8, 1]), 'cur_cost': 974.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,849 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1078.00)
2025-08-04 17:42:37,849 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:42:37,849 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:42:37,849 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,850 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,851 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,851 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,851 - ExplorationExpert - INFO - 探索路径生成完成，成本: 890.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,851 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 4, 3, 8, 5, 1, 0], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 4, 6, 3, 8, 5, 7, 0], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 4, 3, 8, 5, 7, 0], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,852 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 890.00)
2025-08-04 17:42:37,852 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:42:37,853 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:42:37,853 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,853 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,854 - ExplorationExpert - INFO - 探索路径生成完成，成本: 882.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,855 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [3, 5, 1, 6, 8, 2, 4, 0, 7], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 8, 6, 7, 5, 3, 1], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 8, 2, 4, 0, 6, 1], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,855 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 882.00)
2025-08-04 17:42:37,855 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 17:42:37,855 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,855 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,856 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 854.0
2025-08-04 17:42:37,863 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,863 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,863 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,864 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,864 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 4, 7, 2, 3, 8, 0, 6], 'cur_cost': 1112.0}, {'tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0}, {'tour': array([4, 6, 7, 3, 8, 0, 2, 5, 1], dtype=int64), 'cur_cost': 1078.0}, {'tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0}, {'tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0}, {'tour': array([3, 8, 2, 7, 1, 4, 0, 6, 5], dtype=int64), 'cur_cost': 854.0}, {'tour': [6, 2, 0, 5, 8, 4, 1, 3, 7], 'cur_cost': 1051.0}, {'tour': [2, 4, 0, 5, 6, 7, 1, 8, 3], 'cur_cost': 947.0}, {'tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0}, {'tour': [5, 7, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 816.0}]
2025-08-04 17:42:37,865 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,865 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 17:42:37,865 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([3, 8, 2, 7, 1, 4, 0, 6, 5], dtype=int64), 'cur_cost': 854.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 5, 2, 6, 7, 8, 4]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 3, 1, 2, 6, 7, 8, 4]), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 0, 3, 1, 6, 7, 8, 4]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 0, 3, 2, 6, 7, 8, 4]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 0, 3, 6, 7, 8, 4]), 'cur_cost': 1032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,866 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 854.00)
2025-08-04 17:42:37,866 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:42:37,867 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:37,867 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:37,867 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1039.0
2025-08-04 17:42:37,873 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:37,873 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:37,874 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:37,874 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:37,875 - ExploitationExpert - INFO - populations: [{'tour': [1, 5, 4, 7, 2, 3, 8, 0, 6], 'cur_cost': 1112.0}, {'tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0}, {'tour': array([4, 6, 7, 3, 8, 0, 2, 5, 1], dtype=int64), 'cur_cost': 1078.0}, {'tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0}, {'tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0}, {'tour': array([3, 8, 2, 7, 1, 4, 0, 6, 5], dtype=int64), 'cur_cost': 854.0}, {'tour': array([3, 5, 0, 4, 7, 1, 2, 6, 8], dtype=int64), 'cur_cost': 1039.0}, {'tour': [2, 4, 0, 5, 6, 7, 1, 8, 3], 'cur_cost': 947.0}, {'tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0}, {'tour': [5, 7, 6, 3, 8, 4, 2, 0, 1], 'cur_cost': 816.0}]
2025-08-04 17:42:37,876 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:37,876 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 17:42:37,877 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([3, 5, 0, 4, 7, 1, 2, 6, 8], dtype=int64), 'cur_cost': 1039.0, 'intermediate_solutions': [{'tour': array([0, 2, 6, 5, 8, 4, 1, 3, 7]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 2, 6, 8, 4, 1, 3, 7]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 0, 2, 6, 4, 1, 3, 7]), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 5, 0, 2, 8, 4, 1, 3, 7]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 8, 5, 0, 2, 4, 1, 3, 7]), 'cur_cost': 962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:37,878 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1039.00)
2025-08-04 17:42:37,878 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:42:37,878 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:42:37,878 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,879 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,880 - ExplorationExpert - INFO - 探索路径生成完成，成本: 788.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,880 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 0, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 788.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 5, 6, 7, 1, 8, 3], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 0, 5, 6, 7, 1, 3, 8], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 5, 6, 1, 7, 8, 3], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,881 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 788.00)
2025-08-04 17:42:37,881 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:42:37,881 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:42:37,881 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,882 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:37,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 854.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,883 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [8, 4, 0, 7, 5, 3, 6, 1, 2], 'cur_cost': 854.0, 'intermediate_solutions': [{'tour': [6, 8, 1, 4, 7, 3, 5, 0, 2], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 4, 1, 7, 3, 0, 5, 2], 'cur_cost': 1141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,883 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 854.00)
2025-08-04 17:42:37,883 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:42:37,884 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:42:37,884 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:37,884 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:37,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:37,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 847.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:37,886 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 4, 8, 3, 2, 0, 1], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 3, 8, 4, 1, 0, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 1, 3, 8, 4, 2, 0], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:37,886 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 847.00)
2025-08-04 17:42:37,887 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:42:37,887 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:42:37,888 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 4, 7, 2, 3, 8, 0, 6], 'cur_cost': 1112.0, 'intermediate_solutions': [{'tour': [5, 7, 4, 3, 6, 0, 8, 1, 2], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 4, 3, 1, 6, 0, 8, 2], 'cur_cost': 1077.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 4, 3, 8, 0, 1, 2], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0, 'intermediate_solutions': [{'tour': [8, 7, 2, 4, 5, 3, 6, 0, 1], 'cur_cost': 891.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 3, 5, 4, 8, 0, 1], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 2, 4, 5, 3, 0, 1, 8], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 6, 7, 3, 8, 0, 2, 5, 1], dtype=int64), 'cur_cost': 1078.0, 'intermediate_solutions': [{'tour': array([3, 6, 2, 4, 0, 5, 7, 8, 1]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 3, 6, 2, 0, 5, 7, 8, 1]), 'cur_cost': 1105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 4, 3, 6, 2, 5, 7, 8, 1]), 'cur_cost': 1044.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 3, 6, 0, 5, 7, 8, 1]), 'cur_cost': 972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 4, 3, 6, 5, 7, 8, 1]), 'cur_cost': 974.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0, 'intermediate_solutions': [{'tour': [2, 7, 6, 4, 3, 8, 5, 1, 0], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 1, 4, 6, 3, 8, 5, 7, 0], 'cur_cost': 953.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 1, 4, 3, 8, 5, 7, 0], 'cur_cost': 995.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0, 'intermediate_solutions': [{'tour': [3, 5, 1, 6, 8, 2, 4, 0, 7], 'cur_cost': 871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 2, 8, 6, 7, 5, 3, 1], 'cur_cost': 802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 7, 8, 2, 4, 0, 6, 1], 'cur_cost': 840.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 8, 2, 7, 1, 4, 0, 6, 5], dtype=int64), 'cur_cost': 854.0, 'intermediate_solutions': [{'tour': array([0, 3, 1, 5, 2, 6, 7, 8, 4]), 'cur_cost': 1164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 3, 1, 2, 6, 7, 8, 4]), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 5, 0, 3, 1, 6, 7, 8, 4]), 'cur_cost': 1026.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 0, 3, 2, 6, 7, 8, 4]), 'cur_cost': 1125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 5, 0, 3, 6, 7, 8, 4]), 'cur_cost': 1032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 5, 0, 4, 7, 1, 2, 6, 8], dtype=int64), 'cur_cost': 1039.0, 'intermediate_solutions': [{'tour': array([0, 2, 6, 5, 8, 4, 1, 3, 7]), 'cur_cost': 996.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 2, 6, 8, 4, 1, 3, 7]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 0, 2, 6, 4, 1, 3, 7]), 'cur_cost': 1110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 5, 0, 2, 8, 4, 1, 3, 7]), 'cur_cost': 933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 8, 5, 0, 2, 4, 1, 3, 7]), 'cur_cost': 962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 788.0, 'intermediate_solutions': [{'tour': [4, 2, 0, 5, 6, 7, 1, 8, 3], 'cur_cost': 961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 0, 5, 6, 7, 1, 3, 8], 'cur_cost': 887.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 5, 6, 1, 7, 8, 3], 'cur_cost': 895.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 0, 7, 5, 3, 6, 1, 2], 'cur_cost': 854.0, 'intermediate_solutions': [{'tour': [6, 8, 1, 4, 7, 3, 5, 0, 2], 'cur_cost': 1083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 4, 1, 7, 3, 0, 5, 2], 'cur_cost': 1141.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 4, 1, 7, 3, 5, 0, 2], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 4, 8, 3, 2, 0, 1], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 3, 8, 4, 1, 0, 2], 'cur_cost': 875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 1, 3, 8, 4, 2, 0], 'cur_cost': 919.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:42:37,890 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:42:37,891 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,892 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=736.000, 多样性=0.877
2025-08-04 17:42:37,892 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 17:42:37,892 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 17:42:37,892 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:42:37,892 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06085403037070369, 'best_improvement': 0.08229426433915212}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0028089887640452846}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.011161317578605704, 'recent_improvements': [-0.04406118707461123, 0.0823829681600452, -0.06638382223182264], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7037037037037037, 'new_diversity': 0.7037037037037037, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:42:37,893 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 17:42:37,894 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 17:42:37,894 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 17:42:37,894 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:37,895 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=736.000, 多样性=0.877
2025-08-04 17:42:37,895 - PathExpert - INFO - 开始路径结构分析
2025-08-04 17:42:37,896 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.877
2025-08-04 17:42:37,896 - EliteExpert - INFO - 开始精英解分析
2025-08-04 17:42:37,897 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.704
2025-08-04 17:42:37,898 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 17:42:37,899 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 17:42:37,899 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 17:42:37,899 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 17:42:37,906 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.154, 适应度梯度: -9.354, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.595
2025-08-04 17:42:37,907 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 17:42:37,907 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 17:42:37,907 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 17:42:37,911 - visualization.landscape_visualizer - INFO - 插值约束: 111 个点被约束到最小值 680.00
2025-08-04 17:42:37,915 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 17:42:37,989 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_174237.html
2025-08-04 17:42:38,025 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_174237.html
2025-08-04 17:42:38,026 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 17:42:38,026 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 17:42:38,026 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1275秒
2025-08-04 17:42:38,026 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15384615384615385, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9.35384615384616, 'local_optima_density': 0.15384615384615385, 'gradient_variance': 23703.810177514795, 'cluster_count': 0}, 'population_state': {'diversity': 0.5946745562130178, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9345154830993555, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9.354)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754300557.9076037, 'performance_metrics': {}}}
2025-08-04 17:42:38,027 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 17:42:38,028 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 17:42:38,028 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 17:42:38,029 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 17:42:38,029 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:38,029 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 17:42:38,030 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:38,030 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:38,031 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 17:42:38,031 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-04 17:42:38,031 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 17:42:38,032 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 17:42:38,032 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 17:42:38,032 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 17:42:38,032 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:38,033 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:38,033 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1091.0
2025-08-04 17:42:38,039 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 17:42:38,039 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0]
2025-08-04 17:42:38,040 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64)]
2025-08-04 17:42:38,041 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:38,041 - ExploitationExpert - INFO - populations: [{'tour': array([1, 3, 5, 4, 2, 6, 7, 0, 8], dtype=int64), 'cur_cost': 1091.0}, {'tour': [6, 5, 3, 8, 4, 2, 7, 0, 1], 'cur_cost': 736.0}, {'tour': [4, 6, 7, 3, 8, 0, 2, 5, 1], 'cur_cost': 1078.0}, {'tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0}, {'tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0}, {'tour': [3, 8, 2, 7, 1, 4, 0, 6, 5], 'cur_cost': 854.0}, {'tour': [3, 5, 0, 4, 7, 1, 2, 6, 8], 'cur_cost': 1039.0}, {'tour': [4, 0, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 788.0}, {'tour': [8, 4, 0, 7, 5, 3, 6, 1, 2], 'cur_cost': 854.0}, {'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}]
2025-08-04 17:42:38,041 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:38,042 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 17:42:38,042 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([1, 3, 5, 4, 2, 6, 7, 0, 8], dtype=int64), 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': array([4, 5, 1, 7, 2, 3, 8, 0, 6]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 4, 5, 1, 2, 3, 8, 0, 6]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 7, 4, 5, 1, 3, 8, 0, 6]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 7, 4, 5, 2, 3, 8, 0, 6]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 7, 4, 5, 3, 8, 0, 6]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:38,043 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1091.00)
2025-08-04 17:42:38,043 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 17:42:38,043 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 17:42:38,043 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,043 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 17:42:38,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,044 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,045 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1040.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,045 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 7, 3, 2, 4, 0, 5, 1, 8], 'cur_cost': 1040.0, 'intermediate_solutions': [{'tour': [6, 5, 7, 8, 4, 2, 3, 0, 1], 'cur_cost': 812.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 8, 0, 7, 2, 4, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 8, 4, 2, 7, 6, 0, 1], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,045 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1040.00)
2025-08-04 17:42:38,045 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 17:42:38,046 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:38,046 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:38,046 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 934.0
2025-08-04 17:42:38,053 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:42:38,053 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0, 680.0]
2025-08-04 17:42:38,053 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:42:38,054 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:38,054 - ExploitationExpert - INFO - populations: [{'tour': array([1, 3, 5, 4, 2, 6, 7, 0, 8], dtype=int64), 'cur_cost': 1091.0}, {'tour': [6, 7, 3, 2, 4, 0, 5, 1, 8], 'cur_cost': 1040.0}, {'tour': array([5, 8, 7, 2, 6, 0, 1, 4, 3], dtype=int64), 'cur_cost': 934.0}, {'tour': [0, 6, 1, 2, 8, 3, 7, 5, 4], 'cur_cost': 890.0}, {'tour': [5, 7, 6, 8, 3, 4, 2, 0, 1], 'cur_cost': 882.0}, {'tour': [3, 8, 2, 7, 1, 4, 0, 6, 5], 'cur_cost': 854.0}, {'tour': [3, 5, 0, 4, 7, 1, 2, 6, 8], 'cur_cost': 1039.0}, {'tour': [4, 0, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 788.0}, {'tour': [8, 4, 0, 7, 5, 3, 6, 1, 2], 'cur_cost': 854.0}, {'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}]
2025-08-04 17:42:38,055 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:38,055 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 17:42:38,056 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([5, 8, 7, 2, 6, 0, 1, 4, 3], dtype=int64), 'cur_cost': 934.0, 'intermediate_solutions': [{'tour': array([7, 6, 4, 3, 8, 0, 2, 5, 1]), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 7, 6, 4, 8, 0, 2, 5, 1]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 3, 7, 6, 4, 0, 2, 5, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 7, 6, 8, 0, 2, 5, 1]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 3, 7, 6, 0, 2, 5, 1]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:38,056 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 934.00)
2025-08-04 17:42:38,057 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 17:42:38,057 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 17:42:38,057 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,057 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:38,058 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,058 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,058 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,058 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1064.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,058 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 1, 3, 4, 6, 5, 7, 8], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 5, 8, 3, 7, 2, 4], 'cur_cost': 889.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 7, 3, 8, 2, 5, 4], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 1, 2, 8, 7, 5, 4], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,059 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1064.00)
2025-08-04 17:42:38,059 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 17:42:38,059 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 17:42:38,059 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,060 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:38,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,061 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,061 - ExplorationExpert - INFO - 探索路径生成完成，成本: 821.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,061 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 2, 7, 3, 5, 6, 8, 1], 'cur_cost': 821.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 8, 0, 4, 2, 3, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 8, 3, 4, 0, 2, 1], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 8, 3, 2, 0, 1, 4], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,061 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 821.00)
2025-08-04 17:42:38,062 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 17:42:38,062 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 17:42:38,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,063 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:38,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,063 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 972.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,064 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [7, 3, 6, 0, 4, 8, 5, 2, 1], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 4, 1, 7, 0, 6, 5], 'cur_cost': 778.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 7, 1, 0, 4, 6, 5], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 1, 2, 4, 0, 6, 5], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,064 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 972.00)
2025-08-04 17:42:38,065 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 17:42:38,065 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 17:42:38,065 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 17:42:38,065 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1059.0
2025-08-04 17:42:38,071 - ExploitationExpert - INFO - res_population_num: 4
2025-08-04 17:42:38,072 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 832.0, 680.0]
2025-08-04 17:42:38,072 - ExploitationExpert - INFO - res_populations: [array([0, 6, 5, 3, 7, 8, 2, 4, 1], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 6, 3, 5, 8, 2, 7, 4], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 17:42:38,073 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 17:42:38,073 - ExploitationExpert - INFO - populations: [{'tour': array([1, 3, 5, 4, 2, 6, 7, 0, 8], dtype=int64), 'cur_cost': 1091.0}, {'tour': [6, 7, 3, 2, 4, 0, 5, 1, 8], 'cur_cost': 1040.0}, {'tour': array([5, 8, 7, 2, 6, 0, 1, 4, 3], dtype=int64), 'cur_cost': 934.0}, {'tour': [0, 2, 1, 3, 4, 6, 5, 7, 8], 'cur_cost': 1064.0}, {'tour': [0, 4, 2, 7, 3, 5, 6, 8, 1], 'cur_cost': 821.0}, {'tour': [7, 3, 6, 0, 4, 8, 5, 2, 1], 'cur_cost': 972.0}, {'tour': array([6, 4, 8, 7, 5, 0, 3, 2, 1], dtype=int64), 'cur_cost': 1059.0}, {'tour': [4, 0, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 788.0}, {'tour': [8, 4, 0, 7, 5, 3, 6, 1, 2], 'cur_cost': 854.0}, {'tour': [0, 3, 5, 7, 8, 4, 2, 1, 6], 'cur_cost': 847.0}]
2025-08-04 17:42:38,074 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-04 17:42:38,074 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 17:42:38,075 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([6, 4, 8, 7, 5, 0, 3, 2, 1], dtype=int64), 'cur_cost': 1059.0, 'intermediate_solutions': [{'tour': array([0, 5, 3, 4, 7, 1, 2, 6, 8]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 3, 7, 1, 2, 6, 8]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 3, 1, 2, 6, 8]), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 0, 5, 7, 1, 2, 6, 8]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 4, 0, 5, 1, 2, 6, 8]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 17:42:38,075 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1059.00)
2025-08-04 17:42:38,075 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 17:42:38,075 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 17:42:38,076 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,076 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 17:42:38,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,077 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,077 - ExplorationExpert - INFO - 探索路径生成完成，成本: 793.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,077 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 0, 1, 2, 8, 7, 3, 5, 6], 'cur_cost': 793.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 2, 8, 5, 6, 1, 3], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 1, 6, 5, 8, 3, 2], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,078 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 793.00)
2025-08-04 17:42:38,078 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 17:42:38,078 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 17:42:38,078 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,079 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:38,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,080 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,080 - ExplorationExpert - INFO - 探索路径生成完成，成本: 804.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,080 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 5, 7, 4, 2, 8, 3, 6, 0], 'cur_cost': 804.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 7, 5, 3, 6, 1, 2], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 0, 7, 6, 3, 5, 1, 2], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 5, 7, 3, 6, 1, 2], 'cur_cost': 896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,081 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 804.00)
2025-08-04 17:42:38,081 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 17:42:38,081 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 17:42:38,081 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 17:42:38,081 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 17:42:38,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,082 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 17:42:38,082 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1027.0, 路径长度: 9, 收集中间解: 3
2025-08-04 17:42:38,083 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [3, 8, 4, 5, 1, 0, 7, 6, 2], 'cur_cost': 1027.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 1, 8, 4, 2, 7, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 7, 8, 1, 2, 4, 6], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 8, 7, 4, 2, 1, 6], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 17:42:38,083 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1027.00)
2025-08-04 17:42:38,083 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 17:42:38,083 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 17:42:38,085 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 3, 5, 4, 2, 6, 7, 0, 8], dtype=int64), 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': array([4, 5, 1, 7, 2, 3, 8, 0, 6]), 'cur_cost': 1173.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 4, 5, 1, 2, 3, 8, 0, 6]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 7, 4, 5, 1, 3, 8, 0, 6]), 'cur_cost': 1198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 7, 4, 5, 2, 3, 8, 0, 6]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 2, 7, 4, 5, 3, 8, 0, 6]), 'cur_cost': 997.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 3, 2, 4, 0, 5, 1, 8], 'cur_cost': 1040.0, 'intermediate_solutions': [{'tour': [6, 5, 7, 8, 4, 2, 3, 0, 1], 'cur_cost': 812.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 5, 3, 8, 0, 7, 2, 4, 1], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 8, 4, 2, 7, 6, 0, 1], 'cur_cost': 807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 8, 7, 2, 6, 0, 1, 4, 3], dtype=int64), 'cur_cost': 934.0, 'intermediate_solutions': [{'tour': array([7, 6, 4, 3, 8, 0, 2, 5, 1]), 'cur_cost': 1157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 7, 6, 4, 8, 0, 2, 5, 1]), 'cur_cost': 1153.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 3, 7, 6, 4, 0, 2, 5, 1]), 'cur_cost': 1095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 7, 6, 8, 0, 2, 5, 1]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 8, 3, 7, 6, 0, 2, 5, 1]), 'cur_cost': 984.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 3, 4, 6, 5, 7, 8], 'cur_cost': 1064.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 5, 8, 3, 7, 2, 4], 'cur_cost': 889.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 7, 3, 8, 2, 5, 4], 'cur_cost': 979.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 3, 1, 2, 8, 7, 5, 4], 'cur_cost': 984.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 2, 7, 3, 5, 6, 8, 1], 'cur_cost': 821.0, 'intermediate_solutions': [{'tour': [5, 7, 6, 8, 0, 4, 2, 3, 1], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 6, 8, 3, 4, 0, 2, 1], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 6, 8, 3, 2, 0, 1, 4], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 6, 0, 4, 8, 5, 2, 1], 'cur_cost': 972.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 4, 1, 7, 0, 6, 5], 'cur_cost': 778.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 7, 1, 0, 4, 6, 5], 'cur_cost': 842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 1, 2, 4, 0, 6, 5], 'cur_cost': 796.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 8, 7, 5, 0, 3, 2, 1], dtype=int64), 'cur_cost': 1059.0, 'intermediate_solutions': [{'tour': array([0, 5, 3, 4, 7, 1, 2, 6, 8]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 5, 3, 7, 1, 2, 6, 8]), 'cur_cost': 1022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 0, 5, 3, 1, 2, 6, 8]), 'cur_cost': 1087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 4, 0, 5, 7, 1, 2, 6, 8]), 'cur_cost': 1075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 4, 0, 5, 1, 2, 6, 8]), 'cur_cost': 1078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 1, 2, 8, 7, 3, 5, 6], 'cur_cost': 793.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 2, 8, 5, 6, 1, 3], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 1, 6, 5, 8, 3, 2], 'cur_cost': 864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 7, 3, 8, 5, 6, 1, 2], 'cur_cost': 870.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 7, 4, 2, 8, 3, 6, 0], 'cur_cost': 804.0, 'intermediate_solutions': [{'tour': [8, 0, 4, 7, 5, 3, 6, 1, 2], 'cur_cost': 906.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 4, 0, 7, 6, 3, 5, 1, 2], 'cur_cost': 911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 5, 7, 3, 6, 1, 2], 'cur_cost': 896.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 4, 5, 1, 0, 7, 6, 2], 'cur_cost': 1027.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 1, 8, 4, 2, 7, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 7, 8, 1, 2, 4, 6], 'cur_cost': 958.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 8, 7, 4, 2, 1, 6], 'cur_cost': 874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 17:42:38,087 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 17:42:38,087 - StatsExpert - INFO - 开始统计分析
2025-08-04 17:42:38,088 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=793.000, 多样性=0.901
2025-08-04 17:42:38,088 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 17:42:38,088 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 17:42:38,089 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 17:42:38,089 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03879338111583133, 'best_improvement': -0.07744565217391304}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0281690140845073}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.010764468894670753, 'recent_improvements': [0.0823829681600452, -0.06638382223182264, 0.06085403037070369], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.7222222222222222, 'new_diversity': 0.7222222222222222, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 17:42:38,089 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 17:42:38,091 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 17:42:38,091 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_174238.solution
2025-08-04 17:42:38,092 - __main__ - INFO - 实例执行完成 - 运行时间: 13.38s, 最佳成本: 680.0
2025-08-04 17:42:38,092 - __main__ - INFO - 实例 simple1_9 处理完成
