2025-08-01 10:08:57,613 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-01 10:08:57,613 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 10:08:57,614 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:08:57,614 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=705.0, 多样性=0.671
2025-08-01 10:08:57,615 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:08:57,616 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.095
2025-08-01 10:08:57,616 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:08:57,619 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/3)
2025-08-01 10:08:57,619 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 10:08:57,619 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 10:08:57,860 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 10:08:57,860 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:08:57,938 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 10:08:58,295 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_100858.html
2025-08-01 10:08:58,348 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_100858.html
2025-08-01 10:08:58,350 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 10:08:58,350 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 10:08:58,350 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7307秒
2025-08-01 10:08:58,350 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 3, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014137.8606389, 'performance_metrics': {}}}
2025-08-01 10:08:58,351 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:08:58,351 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:08:58,351 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 705.0
  • mean_cost: 888.38
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:08:58,353 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:08:58,353 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:08:59,833 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored space and high diversity. 75% explore, split among best and worst individuals."
}
```
2025-08-01 10:08:59,834 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:08:59,834 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 10:08:59,835 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 10:08:59,835 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored space and high diversity. 75% explore, split among best and worst individuals."
}
```
2025-08-01 10:08:59,835 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:08:59,836 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 10:08:59,836 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit"
  },
  "rationale": "Focus on exploration due to unexplored space and high diversity. 75% explore, split among best and worst individuals."
}
```
2025-08-01 10:08:59,837 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:08:59,838 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:08:59,839 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:08:59,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:08:59,842 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:08:59,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:00,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1066.0, 路径长度: 9
2025-08-01 10:09:00,049 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}
2025-08-01 10:09:00,049 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:09:00,050 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:09:00,050 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:00,051 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:09:00,051 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:00,051 - ExplorationExpert - INFO - 探索路径生成完成，成本: 829.0, 路径长度: 9
2025-08-01 10:09:00,052 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}
2025-08-01 10:09:00,052 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:09:00,052 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:09:00,052 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:00,053 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:00,053 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:00,053 - ExplorationExpert - INFO - 探索路径生成完成，成本: 905.0, 路径长度: 9
2025-08-01 10:09:00,054 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}
2025-08-01 10:09:00,054 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 10:09:00,054 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 10:09:00,054 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:00,054 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:00,055 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:00,055 - ExplorationExpert - INFO - 探索路径生成完成，成本: 902.0, 路径长度: 9
2025-08-01 10:09:00,055 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}
2025-08-01 10:09:00,055 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 10:09:00,055 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:00,058 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:00,059 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1081.0
2025-08-01 10:09:02,322 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 10:09:02,322 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-01 10:09:02,322 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-01 10:09:02,323 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:02,323 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}, {'tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}, {'tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}, {'tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}, {'tour': array([8, 7, 6, 3, 5, 2, 0, 1, 4], dtype=int64), 'cur_cost': 874.0}, {'tour': array([7, 0, 4, 2, 8, 5, 6, 1, 3], dtype=int64), 'cur_cost': 837.0}, {'tour': array([7, 5, 3, 8, 1, 6, 4, 2, 0], dtype=int64), 'cur_cost': 921.0}]
2025-08-01 10:09:02,324 - ExploitationExpert - INFO - 局部搜索耗时: 2.27秒
2025-08-01 10:09:02,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 10:09:02,325 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}
2025-08-01 10:09:02,325 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 10:09:02,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:02,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:02,326 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 910.0
2025-08-01 10:09:04,881 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 10:09:04,881 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-01 10:09:04,882 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-01 10:09:04,883 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:04,883 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}, {'tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}, {'tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}, {'tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}, {'tour': array([4, 3, 5, 8, 6, 7, 2, 0, 1], dtype=int64), 'cur_cost': 910.0}, {'tour': array([7, 0, 4, 2, 8, 5, 6, 1, 3], dtype=int64), 'cur_cost': 837.0}, {'tour': array([7, 5, 3, 8, 1, 6, 4, 2, 0], dtype=int64), 'cur_cost': 921.0}]
2025-08-01 10:09:04,884 - ExploitationExpert - INFO - 局部搜索耗时: 2.56秒
2025-08-01 10:09:04,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 10:09:04,885 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([4, 3, 5, 8, 6, 7, 2, 0, 1], dtype=int64), 'cur_cost': 910.0}
2025-08-01 10:09:04,885 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 10:09:04,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:04,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:04,886 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 937.0
2025-08-01 10:09:05,496 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 10:09:05,497 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-08-01 10:09:05,497 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-01 10:09:05,498 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:05,498 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}, {'tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}, {'tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}, {'tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}, {'tour': array([4, 3, 5, 8, 6, 7, 2, 0, 1], dtype=int64), 'cur_cost': 910.0}, {'tour': array([1, 2, 7, 3, 6, 8, 5, 4, 0], dtype=int64), 'cur_cost': 937.0}, {'tour': array([7, 5, 3, 8, 1, 6, 4, 2, 0], dtype=int64), 'cur_cost': 921.0}]
2025-08-01 10:09:05,499 - ExploitationExpert - INFO - 局部搜索耗时: 0.61秒
2025-08-01 10:09:05,500 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 10:09:05,500 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([1, 2, 7, 3, 6, 8, 5, 4, 0], dtype=int64), 'cur_cost': 937.0}
2025-08-01 10:09:05,501 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 10:09:05,501 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:05,501 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:05,501 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1020.0
2025-08-01 10:09:05,551 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:05,552 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:05,553 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:05,555 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:05,555 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}, {'tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}, {'tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}, {'tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}, {'tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}, {'tour': array([4, 3, 5, 8, 6, 7, 2, 0, 1], dtype=int64), 'cur_cost': 910.0}, {'tour': array([1, 2, 7, 3, 6, 8, 5, 4, 0], dtype=int64), 'cur_cost': 937.0}, {'tour': array([8, 3, 7, 2, 1, 4, 5, 0, 6], dtype=int64), 'cur_cost': 1020.0}]
2025-08-01 10:09:05,556 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:05,557 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 10:09:05,557 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([8, 3, 7, 2, 1, 4, 5, 0, 6], dtype=int64), 'cur_cost': 1020.0}
2025-08-01 10:09:05,558 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 4, 8, 1, 2, 7, 3, 6], 'cur_cost': 1066.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 5, 7, 0, 4, 2, 1, 6], 'cur_cost': 829.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 3, 4, 0, 1, 6, 5, 2], 'cur_cost': 905.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 6, 0, 3, 5, 8, 2, 1], 'cur_cost': 902.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 0, 1, 3, 4, 8, 7, 6, 2], dtype=int64), 'cur_cost': 1081.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 3, 5, 8, 6, 7, 2, 0, 1], dtype=int64), 'cur_cost': 910.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 2, 7, 3, 6, 8, 5, 4, 0], dtype=int64), 'cur_cost': 937.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 3, 7, 2, 1, 4, 5, 0, 6], dtype=int64), 'cur_cost': 1020.0}}]
2025-08-01 10:09:05,558 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:09:05,558 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:05,559 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=829.0, 多样性=0.722
2025-08-01 10:09:05,559 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 10:09:05,559 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 10:09:05,559 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:09:05,560 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07393226569333657, 'best_improvement': -0.17588652482269504}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0769230769230768}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 10:09:05,560 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 10:09:05,561 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-01 10:09:05,561 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 10:09:05,561 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:05,562 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=829.0, 多样性=0.722
2025-08-01 10:09:05,562 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:09:05,562 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.083
2025-08-01 10:09:05,563 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:09:05,563 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-01 10:09:05,565 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/3)
2025-08-01 10:09:05,565 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 10:09:05,565 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 10:09:05,576 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:09:05,577 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 10:09:05,577 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:09:05,587 - visualization.landscape_visualizer - ERROR - 添加精英解标记失败: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-01 10:09:05,588 - visualization.landscape_visualizer - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization\landscape_visualizer.py", line 716, in _add_elite_solution_markers
    path = elite.get('tour') or elite.get('path') or elite.get('solution')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()

2025-08-01 10:09:05,678 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_100905.html
2025-08-01 10:09:05,728 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_100905.html
2025-08-01 10:09:05,728 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 10:09:05,729 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 10:09:05,729 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1640秒
2025-08-01 10:09:05,729 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 1, 'total': 3, 'progress': 0.3333333333333333}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014145.5771441, 'performance_metrics': {}}}
2025-08-01 10:09:05,730 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:09:05,730 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:09:05,730 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 829.0
  • mean_cost: 956.25
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:09:05,733 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:09:05,733 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:09:07,609 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "Iteration 1, Exploitation phase. Opportunity regions exist, so a balanced approach is taken to search the unexplored space while exploiting known good areas."
}
```
2025-08-01 10:09:07,611 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:09:07,612 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-01 10:09:07,612 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-01 10:09:07,613 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "Iteration 1, Exploitation phase. Opportunity regions exist, so a balanced approach is taken to search the unexplored space while exploiting known good areas."
}
```
2025-08-01 10:09:07,614 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:09:07,614 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-08-01 10:09:07,614 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.65,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit"
  },
  "rationale": "Iteration 1, Exploitation phase. Opportunity regions exist, so a balanced approach is taken to search the unexplored space while exploiting known good areas."
}
```
2025-08-01 10:09:07,615 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:09:07,616 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:09:07,616 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:09:07,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:07,617 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:07,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:07,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 934.0, 路径长度: 9
2025-08-01 10:09:07,617 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}
2025-08-01 10:09:07,618 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:09:07,618 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:09:07,618 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:07,618 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:09:07,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:07,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 741.0, 路径长度: 9
2025-08-01 10:09:07,619 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}
2025-08-01 10:09:07,619 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:09:07,619 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:09:07,620 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:07,620 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:07,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:07,621 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1013.0, 路径长度: 9
2025-08-01 10:09:07,621 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}
2025-08-01 10:09:07,621 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:09:07,621 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:07,621 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:07,622 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 921.0
2025-08-01 10:09:07,667 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:07,668 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:07,668 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:07,670 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:07,670 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}, {'tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}, {'tour': [5, 0, 1, 3, 4, 8, 7, 6, 2], 'cur_cost': 1081.0}, {'tour': [4, 3, 5, 8, 6, 7, 2, 0, 1], 'cur_cost': 910.0}, {'tour': [1, 2, 7, 3, 6, 8, 5, 4, 0], 'cur_cost': 937.0}, {'tour': [8, 3, 7, 2, 1, 4, 5, 0, 6], 'cur_cost': 1020.0}]
2025-08-01 10:09:07,672 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-08-01 10:09:07,675 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 10:09:07,677 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}
2025-08-01 10:09:07,678 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 10:09:07,678 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:07,679 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:07,679 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1047.0
2025-08-01 10:09:07,730 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:07,731 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:07,731 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:07,732 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:07,733 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}, {'tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}, {'tour': array([3, 4, 8, 1, 0, 5, 2, 7, 6], dtype=int64), 'cur_cost': 1047.0}, {'tour': [4, 3, 5, 8, 6, 7, 2, 0, 1], 'cur_cost': 910.0}, {'tour': [1, 2, 7, 3, 6, 8, 5, 4, 0], 'cur_cost': 937.0}, {'tour': [8, 3, 7, 2, 1, 4, 5, 0, 6], 'cur_cost': 1020.0}]
2025-08-01 10:09:07,734 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:07,734 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 10:09:07,735 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 4, 8, 1, 0, 5, 2, 7, 6], dtype=int64), 'cur_cost': 1047.0}
2025-08-01 10:09:07,735 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 10:09:07,735 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:07,735 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:07,736 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 946.0
2025-08-01 10:09:07,789 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:07,790 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:07,790 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:07,791 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:07,791 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}, {'tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}, {'tour': array([3, 4, 8, 1, 0, 5, 2, 7, 6], dtype=int64), 'cur_cost': 1047.0}, {'tour': array([6, 1, 4, 5, 3, 2, 8, 7, 0], dtype=int64), 'cur_cost': 946.0}, {'tour': [1, 2, 7, 3, 6, 8, 5, 4, 0], 'cur_cost': 937.0}, {'tour': [8, 3, 7, 2, 1, 4, 5, 0, 6], 'cur_cost': 1020.0}]
2025-08-01 10:09:07,792 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:07,792 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 10:09:07,792 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([6, 1, 4, 5, 3, 2, 8, 7, 0], dtype=int64), 'cur_cost': 946.0}
2025-08-01 10:09:07,793 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:09:07,793 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:09:07,793 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:07,794 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-01 10:09:07,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:07,794 - ExplorationExpert - INFO - 探索路径生成完成，成本: 825.0, 路径长度: 9
2025-08-01 10:09:07,794 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}
2025-08-01 10:09:07,794 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 10:09:07,795 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:07,795 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:07,795 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 928.0
2025-08-01 10:09:07,846 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:07,846 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:07,846 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:07,847 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:07,847 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}, {'tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}, {'tour': array([3, 4, 8, 1, 0, 5, 2, 7, 6], dtype=int64), 'cur_cost': 1047.0}, {'tour': array([6, 1, 4, 5, 3, 2, 8, 7, 0], dtype=int64), 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': array([6, 5, 0, 3, 7, 4, 8, 2, 1], dtype=int64), 'cur_cost': 928.0}]
2025-08-01 10:09:07,850 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:07,850 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 10:09:07,851 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([6, 5, 0, 3, 7, 4, 8, 2, 1], dtype=int64), 'cur_cost': 928.0}
2025-08-01 10:09:07,851 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 3, 5, 7, 8, 2, 1], 'cur_cost': 934.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 2, 4, 8, 1, 6, 0, 7, 3], dtype=int64), 'cur_cost': 921.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 4, 8, 1, 0, 5, 2, 7, 6], dtype=int64), 'cur_cost': 1047.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 4, 5, 3, 2, 8, 7, 0], dtype=int64), 'cur_cost': 946.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 0, 3, 7, 4, 8, 2, 1], dtype=int64), 'cur_cost': 928.0}}]
2025-08-01 10:09:07,852 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:09:07,852 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:07,853 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=741.0, 多样性=0.726
2025-08-01 10:09:07,853 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 10:09:07,853 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 10:09:07,853 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:09:07,853 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.058722146926668856, 'best_improvement': 0.10615199034981906}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0054945054945056285}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:09:07,854 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 10:09:07,854 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-01 10:09:07,854 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 10:09:07,855 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:07,855 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=741.0, 多样性=0.726
2025-08-01 10:09:07,855 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:09:07,856 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.131
2025-08-01 10:09:07,856 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:09:07,856 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-01 10:09:07,858 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/3)
2025-08-01 10:09:07,859 - LandscapeExpert - INFO - 从统计报告生成模拟数据: 8个路径, 8个适应度值
2025-08-01 10:09:07,859 - LandscapeExpert - INFO - 数据提取成功: 8个路径, 8个适应度值
2025-08-01 10:09:07,862 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:09:07,862 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 10:09:07,863 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:09:07,874 - visualization.landscape_visualizer - ERROR - 添加精英解标记失败: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-01 10:09:07,876 - visualization.landscape_visualizer - ERROR - 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization\landscape_visualizer.py", line 716, in _add_elite_solution_markers
    path = elite.get('tour') or elite.get('path') or elite.get('solution')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()

2025-08-01 10:09:07,968 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_100907.html
2025-08-01 10:09:08,064 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_100907.html
2025-08-01 10:09:08,064 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 10:09:08,064 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 10:09:08,065 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2066秒
2025-08-01 10:09:08,065 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 3, 'progress': 0.6666666666666666}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754014147.8629088, 'performance_metrics': {}}}
2025-08-01 10:09:08,066 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:09:08,067 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:09:08,067 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 8 individuals
  • diversity: 0.5
  • best_cost: 741.0
  • mean_cost: 919.38
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 100, 'iteration': 1, 'total_iterations': 3, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:09:08,069 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:09:08,069 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:09:09,596 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.25,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore"
  },
  "rationale": "Focus on exploitation is suggested. One individual assigned to explore to capitalize on unexplored space."
}
```
2025-08-01 10:09:09,597 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:09:09,597 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 10:09:09,598 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 10:09:09,598 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.25,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore"
  },
  "rationale": "Focus on exploitation is suggested. One individual assigned to explore to capitalize on unexplored space."
}
```
2025-08-01 10:09:09,599 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:09:09,599 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 10:09:09,600 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.25,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore"
  },
  "rationale": "Focus on exploitation is suggested. One individual assigned to explore to capitalize on unexplored space."
}
```
2025-08-01 10:09:09,600 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:09:09,601 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 10:09:09,601 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,601 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 991.0
2025-08-01 10:09:09,650 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,651 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,651 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,652 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,652 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': [4, 2, 0, 1, 6, 7, 3, 5, 8], 'cur_cost': 741.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': [5, 2, 4, 8, 1, 6, 0, 7, 3], 'cur_cost': 921.0}, {'tour': [3, 4, 8, 1, 0, 5, 2, 7, 6], 'cur_cost': 1047.0}, {'tour': [6, 1, 4, 5, 3, 2, 8, 7, 0], 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,653 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-08-01 10:09:09,654 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 10:09:09,654 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}
2025-08-01 10:09:09,654 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 10:09:09,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,655 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,655 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1125.0
2025-08-01 10:09:09,710 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,710 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,711 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,712 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,712 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': [0, 8, 1, 5, 3, 7, 6, 4, 2], 'cur_cost': 1013.0}, {'tour': [5, 2, 4, 8, 1, 6, 0, 7, 3], 'cur_cost': 921.0}, {'tour': [3, 4, 8, 1, 0, 5, 2, 7, 6], 'cur_cost': 1047.0}, {'tour': [6, 1, 4, 5, 3, 2, 8, 7, 0], 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,713 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:09,714 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 10:09:09,714 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}
2025-08-01 10:09:09,715 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 10:09:09,715 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,715 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,716 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 967.0
2025-08-01 10:09:09,768 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,769 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,769 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,770 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,771 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}, {'tour': [5, 2, 4, 8, 1, 6, 0, 7, 3], 'cur_cost': 921.0}, {'tour': [3, 4, 8, 1, 0, 5, 2, 7, 6], 'cur_cost': 1047.0}, {'tour': [6, 1, 4, 5, 3, 2, 8, 7, 0], 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,776 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:09,777 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 10:09:09,778 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}
2025-08-01 10:09:09,778 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:09:09,779 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,779 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,779 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1088.0
2025-08-01 10:09:09,846 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,846 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,847 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,848 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,849 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}, {'tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': [3, 4, 8, 1, 0, 5, 2, 7, 6], 'cur_cost': 1047.0}, {'tour': [6, 1, 4, 5, 3, 2, 8, 7, 0], 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,852 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 10:09:09,852 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 10:09:09,853 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}
2025-08-01 10:09:09,853 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 10:09:09,854 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,854 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,855 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 996.0
2025-08-01 10:09:09,924 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,925 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,926 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,927 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,927 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}, {'tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([8, 3, 4, 6, 7, 1, 0, 2, 5], dtype=int64), 'cur_cost': 996.0}, {'tour': [6, 1, 4, 5, 3, 2, 8, 7, 0], 'cur_cost': 946.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,928 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-01 10:09:09,929 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 10:09:09,929 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 3, 4, 6, 7, 1, 0, 2, 5], dtype=int64), 'cur_cost': 996.0}
2025-08-01 10:09:09,929 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 10:09:09,929 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,929 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,930 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1005.0
2025-08-01 10:09:09,988 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:09,988 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:09,988 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:09,989 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:09,989 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}, {'tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([8, 3, 4, 6, 7, 1, 0, 2, 5], dtype=int64), 'cur_cost': 996.0}, {'tour': array([8, 6, 3, 5, 2, 0, 4, 1, 7], dtype=int64), 'cur_cost': 1005.0}, {'tour': [1, 5, 6, 3, 4, 2, 8, 7, 0], 'cur_cost': 825.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:09,990 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-01 10:09:09,991 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 10:09:09,991 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([8, 6, 3, 5, 2, 0, 4, 1, 7], dtype=int64), 'cur_cost': 1005.0}
2025-08-01 10:09:09,991 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 10:09:09,991 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:09:09,992 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:09:09,992 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1058.0
2025-08-01 10:09:10,042 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 10:09:10,042 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-08-01 10:09:10,042 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-01 10:09:10,044 - ExploitationExpert - INFO - populations_num: 8
2025-08-01 10:09:10,044 - ExploitationExpert - INFO - populations: [{'tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}, {'tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}, {'tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}, {'tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}, {'tour': array([8, 3, 4, 6, 7, 1, 0, 2, 5], dtype=int64), 'cur_cost': 996.0}, {'tour': array([8, 6, 3, 5, 2, 0, 4, 1, 7], dtype=int64), 'cur_cost': 1005.0}, {'tour': array([6, 8, 0, 5, 7, 4, 2, 3, 1], dtype=int64), 'cur_cost': 1058.0}, {'tour': [6, 5, 0, 3, 7, 4, 8, 2, 1], 'cur_cost': 928.0}]
2025-08-01 10:09:10,046 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-08-01 10:09:10,046 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 10:09:10,047 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([6, 8, 0, 5, 7, 4, 2, 3, 1], dtype=int64), 'cur_cost': 1058.0}
2025-08-01 10:09:10,047 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 10:09:10,047 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 10:09:10,047 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:09:10,048 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-01 10:09:10,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:09:10,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 821.0, 路径长度: 9
2025-08-01 10:09:10,050 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 7, 6, 5, 3, 4, 2, 0, 1], 'cur_cost': 821.0}
2025-08-01 10:09:10,051 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 5, 8, 2, 1, 3, 6, 4], dtype=int64), 'cur_cost': 991.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 0, 2, 7, 3, 8, 1, 5, 4], dtype=int64), 'cur_cost': 1125.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 2, 4, 8, 7, 1, 5, 6, 0], dtype=int64), 'cur_cost': 967.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 1, 2, 5, 6, 4, 7, 0, 8], dtype=int64), 'cur_cost': 1088.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 3, 4, 6, 7, 1, 0, 2, 5], dtype=int64), 'cur_cost': 996.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 6, 3, 5, 2, 0, 4, 1, 7], dtype=int64), 'cur_cost': 1005.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 8, 0, 5, 7, 4, 2, 3, 1], dtype=int64), 'cur_cost': 1058.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 6, 5, 3, 4, 2, 0, 1], 'cur_cost': 821.0}}]
2025-08-01 10:09:10,052 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:09:10,052 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:09:10,053 - StatsExpert - INFO - 统计分析完成: 种群大小=8, 最优成本=821.0, 多样性=0.802
2025-08-01 10:09:10,053 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 10:09:10,054 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 10:09:10,054 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:09:10,054 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 3, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09236342809985593, 'best_improvement': -0.10796221322537113}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.10382513661202163}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:09:10,055 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 10:09:10,057 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-01 10:09:10,058 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250801_100910.solution
2025-08-01 10:09:10,058 - __main__ - INFO - 实例 simple1_9 处理完成
