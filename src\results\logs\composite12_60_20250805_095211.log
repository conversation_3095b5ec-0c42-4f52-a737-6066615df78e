2025-08-05 09:52:11,034 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-08-05 09:52:11,034 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:11,036 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,040 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9944.000, 多样性=0.974
2025-08-05 09:52:11,042 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:11,046 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.974
2025-08-05 09:52:11,048 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:11,050 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:11,050 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:11,050 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:11,051 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:11,069 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -9542.880, 聚类评分: 0.000, 覆盖率: 0.137, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:11,070 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:11,070 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:11,070 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 09:52:11,075 - visualization.landscape_visualizer - INFO - 插值约束: 25 个点被约束到最小值 9944.00
2025-08-05 09:52:11,211 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_116_20250805_095211.html
2025-08-05 09:52:11,283 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_116_20250805_095211.html
2025-08-05 09:52:11,283 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 116
2025-08-05 09:52:11,283 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:11,284 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2346秒
2025-08-05 09:52:11,284 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 232, 'max_size': 500, 'hits': 0, 'misses': 232, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 759, 'misses': 418, 'hit_rate': 0.6448598130841121, 'evictions': 318, 'ttl': 7200}}
2025-08-05 09:52:11,284 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9542.88, 'local_optima_density': 0.2, 'gradient_variance': 3279473024.7056007, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.137, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9542.880)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.137)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358731.0701642, 'performance_metrics': {}}}
2025-08-05 09:52:11,284 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:11,285 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:11,285 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:11,285 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:11,287 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,287 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:11,287 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,287 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,287 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:11,287 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,287 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,288 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:11,288 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:11,288 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:11,288 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:11,288 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,296 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67244.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,297 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67244.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,297 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 67244.00)
2025-08-05 09:52:11,297 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:11,297 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:11,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,305 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,306 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64522.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,306 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 64522.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,306 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 64522.00)
2025-08-05 09:52:11,306 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:11,307 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:11,307 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,309 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14476.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,309 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14476.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,310 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 14476.00)
2025-08-05 09:52:11,310 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:11,310 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,310 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,310 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 108707.0
2025-08-05 09:52:11,319 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:11,319 - ExploitationExpert - INFO - res_population_costs: [9649.0, 9648, 9642, 9614, 9614]
2025-08-05 09:52:11,319 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:11,321 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,321 - ExploitationExpert - INFO - populations: [{'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67244.0}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 64522.0}, {'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14476.0}, {'tour': array([14, 48,  8, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10], dtype=int64), 'cur_cost': 108707.0}, {'tour': array([56,  6, 45, 14, 29, 20, 42, 23, 58, 19, 18, 53,  8, 24, 10, 30, 12,
       51, 39, 36, 21, 17, 31,  7, 44, 15, 49,  9, 50,  2, 59, 52,  3, 35,
        5, 55,  0, 28, 11, 22, 47, 57, 26, 54, 37,  4, 40, 25, 16, 41, 38,
       46, 48, 13, 43, 32, 33,  1, 34, 27], dtype=int64), 'cur_cost': 99201.0}, {'tour': array([ 1, 36,  7,  0, 20, 25,  6, 45, 48, 56, 32, 30, 51, 14,  5, 37, 49,
       41, 42, 35, 13, 38, 58, 10, 33, 22, 21, 15, 18, 23, 16, 40, 24,  9,
       19, 54, 17, 39,  2, 57, 52,  4, 29, 55, 43, 12, 50, 59, 46,  3, 47,
       34, 44, 28, 26, 53, 27,  8, 31, 11], dtype=int64), 'cur_cost': 91489.0}, {'tour': array([32, 17, 39, 22, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 106140.0}, {'tour': array([22, 15, 25, 32, 18, 55, 54, 50, 46,  0,  5, 35, 14, 19, 11, 34, 45,
        1, 43, 49, 40, 37,  7, 16, 12, 51, 33, 52, 29, 38,  2,  3, 23, 58,
       31, 42, 13, 56, 41, 10,  6, 27, 28, 30, 47, 17, 21, 24, 57, 44,  8,
       39,  9, 36,  4, 26, 20, 59, 48, 53], dtype=int64), 'cur_cost': 85410.0}, {'tour': array([17, 55, 11,  5, 25, 42,  9, 57, 52, 54, 15,  8, 23, 45, 31, 16, 47,
       13, 18, 43, 24, 53, 48, 37, 51, 32, 46, 34, 59, 26,  2, 58, 35, 20,
       39, 27,  4, 41, 12, 33,  3, 30,  1,  7, 38, 56,  0, 29, 49, 50, 28,
        6, 14, 21, 22, 36, 19, 44, 40, 10], dtype=int64), 'cur_cost': 105919.0}, {'tour': array([11, 37, 13,  5, 20, 18, 16, 39,  7, 32, 48, 36, 55, 28, 24, 33,  2,
       45, 57,  6, 34, 49, 44, 43, 47,  0, 30, 42, 38, 22, 56, 54, 26, 15,
       27, 40, 52, 58, 46,  9, 53, 19, 21,  1,  4, 41, 50, 10,  3, 25, 59,
       23, 29, 12, 17, 31, 35,  8, 14, 51], dtype=int64), 'cur_cost': 89920.0}]
2025-08-05 09:52:11,324 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:11,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 300, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 300, 'cache_hits': 0, 'similarity_calculations': 1512, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,325 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([14, 48,  8, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10], dtype=int64), 'cur_cost': 108707.0, 'intermediate_solutions': [{'tour': array([34, 30, 17, 19,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 103141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 34, 30, 17,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 108570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 19, 34, 30, 17, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 106800.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 19, 34, 30,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 105095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17,  6, 19, 34, 30, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 108959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,325 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 108707.00)
2025-08-05 09:52:11,325 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:11,325 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:11,325 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,328 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10506.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,328 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10506.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,328 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 10506.00)
2025-08-05 09:52:11,329 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:11,329 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:11,329 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,335 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,335 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,336 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49805.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,336 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 49805.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,336 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 49805.00)
2025-08-05 09:52:11,337 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:11,337 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,337 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,337 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104104.0
2025-08-05 09:52:11,351 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:52:11,352 - ExploitationExpert - INFO - res_population_costs: [9649.0, 9648, 9642, 9614, 9614]
2025-08-05 09:52:11,352 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:11,354 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,354 - ExploitationExpert - INFO - populations: [{'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67244.0}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 64522.0}, {'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14476.0}, {'tour': array([14, 48,  8, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10], dtype=int64), 'cur_cost': 108707.0}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10506.0}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 49805.0}, {'tour': array([22, 37, 32, 24, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36], dtype=int64), 'cur_cost': 104104.0}, {'tour': array([22, 15, 25, 32, 18, 55, 54, 50, 46,  0,  5, 35, 14, 19, 11, 34, 45,
        1, 43, 49, 40, 37,  7, 16, 12, 51, 33, 52, 29, 38,  2,  3, 23, 58,
       31, 42, 13, 56, 41, 10,  6, 27, 28, 30, 47, 17, 21, 24, 57, 44,  8,
       39,  9, 36,  4, 26, 20, 59, 48, 53], dtype=int64), 'cur_cost': 85410.0}, {'tour': array([17, 55, 11,  5, 25, 42,  9, 57, 52, 54, 15,  8, 23, 45, 31, 16, 47,
       13, 18, 43, 24, 53, 48, 37, 51, 32, 46, 34, 59, 26,  2, 58, 35, 20,
       39, 27,  4, 41, 12, 33,  3, 30,  1,  7, 38, 56,  0, 29, 49, 50, 28,
        6, 14, 21, 22, 36, 19, 44, 40, 10], dtype=int64), 'cur_cost': 105919.0}, {'tour': array([11, 37, 13,  5, 20, 18, 16, 39,  7, 32, 48, 36, 55, 28, 24, 33,  2,
       45, 57,  6, 34, 49, 44, 43, 47,  0, 30, 42, 38, 22, 56, 54, 26, 15,
       27, 40, 52, 58, 46,  9, 53, 19, 21,  1,  4, 41, 50, 10,  3, 25, 59,
       23, 29, 12, 17, 31, 35,  8, 14, 51], dtype=int64), 'cur_cost': 89920.0}]
2025-08-05 09:52:11,359 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:11,359 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 301, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 301, 'cache_hits': 0, 'similarity_calculations': 1513, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,360 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([22, 37, 32, 24, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36], dtype=int64), 'cur_cost': 104104.0, 'intermediate_solutions': [{'tour': array([39, 17, 32, 22, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 39, 17, 32, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 22, 39, 17, 32, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 22, 39, 17, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 106170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 47, 22, 39, 17, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 102378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,361 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 104104.00)
2025-08-05 09:52:11,361 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:11,361 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:11,361 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,363 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13930.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,364 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13930.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,364 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 13930.00)
2025-08-05 09:52:11,364 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:11,364 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:11,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,366 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:11,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96109.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,366 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 96109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,367 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 96109.00)
2025-08-05 09:52:11,367 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:11,367 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:11,367 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,369 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,369 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10447.0, 路径长度: 60, 收集中间解: 0
2025-08-05 09:52:11,369 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10447.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,369 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10447.00)
2025-08-05 09:52:11,370 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:11,370 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:11,371 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67244.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 64522.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14476.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 48,  8, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10], dtype=int64), 'cur_cost': 108707.0, 'intermediate_solutions': [{'tour': array([34, 30, 17, 19,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 103141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 34, 30, 17,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 108570.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 19, 34, 30, 17, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 106800.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 19, 34, 30,  6, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 105095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17,  6, 19, 34, 30, 55, 16, 11, 14, 54,  9, 51,  4,  7, 56, 25,  1,
        8,  0, 29, 12, 40, 35, 48, 52, 58,  5, 28, 23, 44,  3, 38, 15, 43,
       18, 46, 41,  2, 33, 21, 49, 31, 20, 24, 37, 57, 10, 39, 45, 50, 26,
       13, 27, 36, 42, 53, 22, 47, 59, 32], dtype=int64), 'cur_cost': 108959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10506.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 49805.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 37, 32, 24, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36], dtype=int64), 'cur_cost': 104104.0, 'intermediate_solutions': [{'tour': array([39, 17, 32, 22, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105802.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 39, 17, 32, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 22, 39, 17, 32, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 105769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 22, 39, 17, 47, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 106170.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 47, 22, 39, 17, 15, 14, 42,  4, 58, 36,  1, 55, 45, 26, 20, 54,
       51, 29, 21,  9, 52, 33, 49, 28, 38, 41,  7,  2, 50, 10,  8,  6, 37,
       19, 59, 23, 44, 11, 16, 31, 18, 57, 13, 27, 34, 46, 53, 48, 35,  5,
       43, 12, 30, 25, 40, 24,  3, 56,  0], dtype=int64), 'cur_cost': 102378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13930.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 96109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10447.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:11,372 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:11,372 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,375 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10447.000, 多样性=0.912
2025-08-05 09:52:11,375 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:11,375 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:11,375 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:11,376 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06489597497794648, 'best_improvement': -0.050583266291230895}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.06349809885931545}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.033564350333673436, 'recent_improvements': [-0.12548432008402668, 0.08138368242629952, -0.05835561941667983], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.7216666666666666, 'new_diversity': 0.7216666666666666, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:11,376 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:11,376 - __main__ - INFO - composite12_60 开始进化第 2 代
2025-08-05 09:52:11,376 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:11,376 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,377 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10447.000, 多样性=0.912
2025-08-05 09:52:11,377 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:11,380 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.912
2025-08-05 09:52:11,380 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:11,382 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.722
2025-08-05 09:52:11,384 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:11,384 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:11,384 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 09:52:11,384 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 09:52:11,431 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -5988.613, 聚类评分: 0.000, 覆盖率: 0.138, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:11,432 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:11,432 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:11,432 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 09:52:11,439 - visualization.landscape_visualizer - INFO - 插值约束: 47 个点被约束到最小值 9614.00
2025-08-05 09:52:11,550 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_117_20250805_095211.html
2025-08-05 09:52:11,606 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_117_20250805_095211.html
2025-08-05 09:52:11,606 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 117
2025-08-05 09:52:11,607 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:11,607 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2238秒
2025-08-05 09:52:11,607 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -5988.613333333333, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 1369286422.0931556, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1383, 'fitness_entropy': 0.7486316789498982, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5988.613)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.138)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358731.4327302, 'performance_metrics': {}}}
2025-08-05 09:52:11,607 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:11,607 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:11,607 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:11,607 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:11,608 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,608 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:11,608 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,608 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,608 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:11,609 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,609 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,609 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:11,609 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:11,609 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:11,609 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:11,609 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,614 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60069.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,616 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 60069.0, 'intermediate_solutions': [{'tour': [35, 36, 40, 26, 54, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 46, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 70195.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 22, 52, 12, 59, 19, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 49, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,616 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 60069.00)
2025-08-05 09:52:11,616 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:11,616 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:11,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,617 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:11,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,618 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102167.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,618 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [17, 48, 8, 18, 58, 4, 25, 13, 3, 22, 43, 28, 36, 40, 31, 26, 11, 54, 16, 42, 24, 55, 52, 37, 2, 7, 21, 38, 9, 41, 15, 32, 57, 30, 5, 1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47, 6, 44, 51, 12, 27, 34, 23, 45, 0, 10], 'cur_cost': 102167.0, 'intermediate_solutions': [{'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 44, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 22], 'cur_cost': 68139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 21, 53, 2, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 68354.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 32, 4, 2, 53, 21, 15, 9, 41, 42, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 66908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,619 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 102167.00)
2025-08-05 09:52:11,619 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:11,619 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:11,619 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,620 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,621 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10476.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,622 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10476.0, 'intermediate_solutions': [{'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 24, 26, 32, 31, 25, 35, 28, 27, 29, 33, 8, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 17027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 40, 46, 39, 41, 43, 44], 'cur_cost': 18787.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 4, 18, 21, 14, 19, 32, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 19860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,622 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10476.00)
2025-08-05 09:52:11,622 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:11,622 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,622 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,622 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 102966.0
2025-08-05 09:52:11,632 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:11,633 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9642, 9648, 9649.0, 9614, 9614]
2025-08-05 09:52:11,633 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:11,635 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,635 - ExploitationExpert - INFO - populations: [{'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 60069.0}, {'tour': [17, 48, 8, 18, 58, 4, 25, 13, 3, 22, 43, 28, 36, 40, 31, 26, 11, 54, 16, 42, 24, 55, 52, 37, 2, 7, 21, 38, 9, 41, 15, 32, 57, 30, 5, 1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47, 6, 44, 51, 12, 27, 34, 23, 45, 0, 10], 'cur_cost': 102167.0}, {'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10476.0}, {'tour': array([23, 34, 39, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47], dtype=int64), 'cur_cost': 102966.0}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10506.0}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 49805.0}, {'tour': [22, 37, 32, 24, 55, 54, 34, 11, 46, 49, 41, 44, 1, 59, 50, 2, 12, 45, 7, 25, 38, 5, 52, 58, 13, 28, 6, 23, 40, 9, 15, 10, 17, 30, 16, 18, 35, 42, 56, 57, 3, 31, 53, 29, 51, 14, 20, 48, 8, 47, 26, 39, 0, 43, 27, 21, 33, 19, 4, 36], 'cur_cost': 104104.0}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13930.0}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 96109.0}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10447.0}]
2025-08-05 09:52:11,636 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:11,637 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 302, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 302, 'cache_hits': 0, 'similarity_calculations': 1515, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,638 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([23, 34, 39, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47], dtype=int64), 'cur_cost': 102966.0, 'intermediate_solutions': [{'tour': array([ 8, 48, 14, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 104804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 48, 14, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 108721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 13,  8, 48, 14, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 106515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 13,  8, 48, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 107469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 27, 13,  8, 48, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 107934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,638 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 102966.00)
2025-08-05 09:52:11,638 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:11,638 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:11,638 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,640 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10600.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,642 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10600.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 39, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 10, 41, 43, 44], 'cur_cost': 15718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 43, 41, 39, 46, 44], 'cur_cost': 10523.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 54, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,642 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 10600.00)
2025-08-05 09:52:11,642 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:11,642 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:11,642 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10540.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,645 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10540.0, 'intermediate_solutions': [{'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 2, 49, 58, 50, 44, 0, 17, 34, 35, 32, 28, 40], 'cur_cost': 55511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 35, 34, 2, 0, 44, 50, 58, 49, 17, 20, 18, 15, 14, 16, 56, 19, 52, 41, 37, 24, 8, 39, 38, 47, 43, 42, 36, 32, 28, 40], 'cur_cost': 54001.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 9, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 52019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,645 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 10540.00)
2025-08-05 09:52:11,645 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:11,645 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,645 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,646 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 101986.0
2025-08-05 09:52:11,654 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:11,654 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9642, 9648, 9649.0, 9614, 9614]
2025-08-05 09:52:11,654 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:11,657 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,657 - ExploitationExpert - INFO - populations: [{'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 60069.0}, {'tour': [17, 48, 8, 18, 58, 4, 25, 13, 3, 22, 43, 28, 36, 40, 31, 26, 11, 54, 16, 42, 24, 55, 52, 37, 2, 7, 21, 38, 9, 41, 15, 32, 57, 30, 5, 1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47, 6, 44, 51, 12, 27, 34, 23, 45, 0, 10], 'cur_cost': 102167.0}, {'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10476.0}, {'tour': array([23, 34, 39, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47], dtype=int64), 'cur_cost': 102966.0}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10600.0}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10540.0}, {'tour': array([55, 23, 58, 50, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8], dtype=int64), 'cur_cost': 101986.0}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13930.0}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 96109.0}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10447.0}]
2025-08-05 09:52:11,658 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:11,658 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 303, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 303, 'cache_hits': 0, 'similarity_calculations': 1518, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,659 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([55, 23, 58, 50, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8], dtype=int64), 'cur_cost': 101986.0, 'intermediate_solutions': [{'tour': array([32, 37, 22, 24, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 105773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 32, 37, 22, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 101542.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 24, 32, 37, 22, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 104182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 24, 32, 37, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 103420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 55, 24, 32, 37, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 104219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,659 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 101986.00)
2025-08-05 09:52:11,659 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:11,659 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:11,660 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,665 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,665 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,665 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,666 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,666 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58365.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,666 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58365.0, 'intermediate_solutions': [{'tour': [0, 21, 10, 4, 6, 5, 19, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 1, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 21855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 25, 35, 26, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 18203.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 40, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 46, 39, 41, 43, 44], 'cur_cost': 20182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,666 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 58365.00)
2025-08-05 09:52:11,667 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:11,667 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:11,667 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,668 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:11,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,669 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87948.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,669 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87948.0, 'intermediate_solutions': [{'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 51, 16, 31, 33, 47, 45, 59, 5, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 98938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 12, 28, 27, 54, 6, 25, 58, 50, 44, 3, 36, 34, 23, 56, 40, 32, 20, 43, 24, 46, 18, 37, 29, 39, 7, 41, 10, 0, 11, 22, 52, 38], 'cur_cost': 96162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 5, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 95598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,670 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 87948.00)
2025-08-05 09:52:11,670 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:11,670 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:11,670 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98041.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,673 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 98041.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 32, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 23, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 21415.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 4, 6, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10450.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 53, 57, 52, 59, 51, 50, 55, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10485.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,673 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 98041.00)
2025-08-05 09:52:11,673 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:11,673 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:11,675 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 60069.0, 'intermediate_solutions': [{'tour': [35, 36, 40, 26, 54, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 46, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 70195.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 49, 13, 22, 52, 12, 59, 19, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 36, 40, 26, 46, 34, 29, 42, 27, 25, 49, 5, 3, 31, 33, 47, 30, 2, 8, 6, 39, 4, 51, 14, 17, 16, 20, 58, 57, 21, 13, 19, 59, 12, 52, 22, 11, 32, 10, 45, 7, 53, 54, 9, 55, 18, 1, 24, 43, 37, 0, 41, 44, 28, 48, 50, 23, 15, 56, 38], 'cur_cost': 67787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [17, 48, 8, 18, 58, 4, 25, 13, 3, 22, 43, 28, 36, 40, 31, 26, 11, 54, 16, 42, 24, 55, 52, 37, 2, 7, 21, 38, 9, 41, 15, 32, 57, 30, 5, 1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47, 6, 44, 51, 12, 27, 34, 23, 45, 0, 10], 'cur_cost': 102167.0, 'intermediate_solutions': [{'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 44, 55, 51, 4, 2, 53, 21, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 22], 'cur_cost': 68139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 4, 21, 53, 2, 15, 9, 41, 42, 32, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 68354.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 7, 59, 17, 58, 50, 48, 6, 8, 30, 43, 26, 28, 1, 31, 45, 11, 52, 16, 54, 22, 55, 51, 32, 4, 2, 53, 21, 15, 9, 41, 42, 35, 38, 36, 40, 10, 0, 5, 47, 3, 33, 39, 29, 37, 27, 46, 49, 57, 14, 20, 19, 13, 12, 18, 56, 23, 34, 24, 44], 'cur_cost': 66908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10476.0, 'intermediate_solutions': [{'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 24, 26, 32, 31, 25, 35, 28, 27, 29, 33, 8, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 17027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 4, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 40, 46, 39, 41, 43, 44], 'cur_cost': 18787.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 4, 18, 21, 14, 19, 32, 13, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 19860.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 34, 39, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47], dtype=int64), 'cur_cost': 102966.0, 'intermediate_solutions': [{'tour': array([ 8, 48, 14, 13, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 104804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 48, 14, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 108721.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 13,  8, 48, 14, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 106515.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 13,  8, 48, 27, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 107469.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 27, 13,  8, 48, 53, 15, 37, 19, 41, 46,  3,  0, 56, 29, 18, 30,
       54, 12, 42, 21, 49, 58, 52, 45, 24,  4, 36, 23, 40, 43, 16, 55, 47,
       25,  1, 26, 22,  7, 33, 59, 44, 20,  5, 50,  2, 38, 31, 35, 57, 34,
       28, 51, 39,  9,  6, 17, 11, 32, 10]), 'cur_cost': 107934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10600.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 39, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 10, 41, 43, 44], 'cur_cost': 15718.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 43, 41, 39, 46, 44], 'cur_cost': 10523.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 16, 23, 20, 22, 15, 17, 19, 21, 18, 13, 12, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 54, 10, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13247.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10540.0, 'intermediate_solutions': [{'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 2, 49, 58, 50, 44, 0, 17, 34, 35, 32, 28, 40], 'cur_cost': 55511.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 9, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 35, 34, 2, 0, 44, 50, 58, 49, 17, 20, 18, 15, 14, 16, 56, 19, 52, 41, 37, 24, 8, 39, 38, 47, 43, 42, 36, 32, 28, 40], 'cur_cost': 54001.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 30, 29, 33, 27, 1, 6, 46, 54, 48, 13, 53, 7, 5, 59, 12, 55, 21, 51, 23, 57, 10, 4, 25, 26, 3, 31, 45, 36, 42, 43, 47, 38, 39, 8, 24, 37, 41, 52, 19, 56, 16, 14, 15, 18, 20, 17, 49, 9, 58, 50, 44, 0, 2, 34, 35, 32, 28, 40], 'cur_cost': 52019.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 23, 58, 50, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8], dtype=int64), 'cur_cost': 101986.0, 'intermediate_solutions': [{'tour': array([32, 37, 22, 24, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 105773.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 32, 37, 22, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 101542.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 24, 32, 37, 22, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 104182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 24, 32, 37, 55, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 103420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 55, 24, 32, 37, 54, 34, 11, 46, 49, 41, 44,  1, 59, 50,  2, 12,
       45,  7, 25, 38,  5, 52, 58, 13, 28,  6, 23, 40,  9, 15, 10, 17, 30,
       16, 18, 35, 42, 56, 57,  3, 31, 53, 29, 51, 14, 20, 48,  8, 47, 26,
       39,  0, 43, 27, 21, 33, 19,  4, 36]), 'cur_cost': 104219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58365.0, 'intermediate_solutions': [{'tour': [0, 21, 10, 4, 6, 5, 19, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 1, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 21855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13, 31, 32, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 25, 35, 26, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 18203.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 10, 4, 6, 5, 1, 8, 7, 2, 11, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 40, 14, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 46, 39, 41, 43, 44], 'cur_cost': 20182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87948.0, 'intermediate_solutions': [{'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 51, 16, 31, 33, 47, 45, 59, 5, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 98938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 5, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 1, 12, 28, 27, 54, 6, 25, 58, 50, 44, 3, 36, 34, 23, 56, 40, 32, 20, 43, 24, 46, 18, 37, 29, 39, 7, 41, 10, 0, 11, 22, 52, 38], 'cur_cost': 96162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 15, 4, 26, 21, 14, 48, 42, 8, 30, 16, 31, 33, 47, 45, 59, 51, 55, 17, 2, 53, 57, 9, 49, 13, 19, 5, 1, 38, 52, 22, 11, 0, 10, 41, 7, 39, 29, 37, 18, 46, 24, 43, 20, 32, 40, 56, 23, 34, 36, 3, 44, 50, 58, 25, 6, 54, 27, 28, 12], 'cur_cost': 95598.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 98041.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 32, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 23, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 21415.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 4, 6, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10450.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 13, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 54, 56, 53, 57, 52, 59, 51, 50, 55, 58, 49, 48, 9, 3, 2, 7, 11, 10, 6, 4, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10485.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:11,675 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:11,675 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,679 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10476.000, 多样性=0.962
2025-08-05 09:52:11,679 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:11,679 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:11,679 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:11,679 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.047109499307769725, 'best_improvement': -0.0027759165310615487}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05481120584652867}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008243853724176515, 'recent_improvements': [0.08138368242629952, -0.05835561941667983, 0.06489597497794648], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.8396825396825397, 'new_diversity': 0.8396825396825397, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:52:11,680 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:11,680 - __main__ - INFO - composite12_60 开始进化第 3 代
2025-08-05 09:52:11,680 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:11,681 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,681 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10476.000, 多样性=0.962
2025-08-05 09:52:11,681 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:11,684 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.962
2025-08-05 09:52:11,684 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:11,687 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.840
2025-08-05 09:52:11,689 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:11,689 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:11,690 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:11,690 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:11,731 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -17906.659, 聚类评分: 0.000, 覆盖率: 0.139, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:11,731 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:11,731 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:11,732 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 09:52:11,738 - visualization.landscape_visualizer - INFO - 插值约束: 37 个点被约束到最小值 9614.00
2025-08-05 09:52:11,832 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_118_20250805_095211.html
2025-08-05 09:52:11,881 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_118_20250805_095211.html
2025-08-05 09:52:11,881 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 118
2025-08-05 09:52:11,881 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:11,881 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1918秒
2025-08-05 09:52:11,881 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -17906.658823529408, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 1454119392.878893, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1394, 'fitness_entropy': 0.7725760985994748, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -17906.659)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.139)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358731.7319546, 'performance_metrics': {}}}
2025-08-05 09:52:11,882 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:11,882 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:11,882 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:11,882 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:11,882 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,882 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:11,883 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,883 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,883 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:11,883 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:11,883 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:11,884 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:11,884 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:11,884 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:11,884 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:11,884 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,887 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10537.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,887 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10537.0, 'intermediate_solutions': [{'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 32, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 1, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 58451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 55, 21, 57, 58, 13, 48, 31, 38, 10, 7, 11, 37, 32, 41, 24, 29, 34, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 61973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 42, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 44, 54], 'cur_cost': 60062.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,888 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 10537.00)
2025-08-05 09:52:11,888 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:52:11,888 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,888 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,888 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 98344.0
2025-08-05 09:52:11,899 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:11,899 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0]
2025-08-05 09:52:11,900 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:11,902 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,902 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10537.0}, {'tour': array([50, 49, 40,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6], dtype=int64), 'cur_cost': 98344.0}, {'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10476.0}, {'tour': [23, 34, 39, 40, 46, 16, 14, 29, 33, 59, 26, 8, 7, 22, 6, 56, 18, 30, 35, 49, 25, 13, 19, 32, 12, 42, 3, 45, 54, 55, 28, 5, 10, 52, 1, 58, 11, 50, 17, 27, 15, 9, 44, 4, 51, 43, 38, 48, 53, 36, 2, 20, 24, 0, 21, 37, 57, 41, 31, 47], 'cur_cost': 102966.0}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10600.0}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10540.0}, {'tour': [55, 23, 58, 50, 47, 38, 1, 24, 0, 4, 49, 27, 37, 2, 48, 14, 5, 41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33, 6, 15, 22, 32, 30, 13, 43, 7, 46, 16, 9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11, 52, 34, 53, 3, 25, 40, 20, 17, 8], 'cur_cost': 101986.0}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58365.0}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87948.0}, {'tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 98041.0}]
2025-08-05 09:52:11,903 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:11,903 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 304, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 304, 'cache_hits': 0, 'similarity_calculations': 1522, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,904 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([50, 49, 40,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6], dtype=int64), 'cur_cost': 98344.0, 'intermediate_solutions': [{'tour': array([ 8, 48, 17, 18, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 98274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18,  8, 48, 17, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 18,  8, 48, 17,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 18,  8, 48, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 100202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 58, 18,  8, 48,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,905 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 98344.00)
2025-08-05 09:52:11,905 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:11,905 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:11,905 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,907 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,907 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,908 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13996.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,908 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 13996.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 31, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 47], 'cur_cost': 16963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 21, 14, 18, 19, 30, 34, 44, 43, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 5, 4, 6, 1, 10, 8, 7, 11, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 13, 12, 23, 16, 20, 22, 17, 15, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 15976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 20, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 16839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,908 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 13996.00)
2025-08-05 09:52:11,909 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:11,909 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,909 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,909 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103696.0
2025-08-05 09:52:11,920 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:11,921 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0]
2025-08-05 09:52:11,921 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:11,923 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,924 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10537.0}, {'tour': array([50, 49, 40,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6], dtype=int64), 'cur_cost': 98344.0}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 13996.0}, {'tour': array([40,  5, 52,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35], dtype=int64), 'cur_cost': 103696.0}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10600.0}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10540.0}, {'tour': [55, 23, 58, 50, 47, 38, 1, 24, 0, 4, 49, 27, 37, 2, 48, 14, 5, 41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33, 6, 15, 22, 32, 30, 13, 43, 7, 46, 16, 9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11, 52, 34, 53, 3, 25, 40, 20, 17, 8], 'cur_cost': 101986.0}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58365.0}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87948.0}, {'tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 98041.0}]
2025-08-05 09:52:11,925 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:11,925 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 305, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 305, 'cache_hits': 0, 'similarity_calculations': 1527, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,925 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([40,  5, 52,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35], dtype=int64), 'cur_cost': 103696.0, 'intermediate_solutions': [{'tour': array([39, 34, 23, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 39, 34, 23, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 40, 39, 34, 23, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 96691.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 40, 39, 34, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 105422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 46, 40, 39, 34, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,925 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 103696.00)
2025-08-05 09:52:11,926 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:11,926 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:11,926 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,931 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54818.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,933 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 54818.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 24, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 49, 35, 25, 32, 26, 31], 'cur_cost': 20689.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 22, 12, 23, 16, 20, 13, 18, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14470.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 56, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 15070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,933 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 54818.00)
2025-08-05 09:52:11,933 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:11,933 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:11,934 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,936 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:11,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14057.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,938 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14057.0, 'intermediate_solutions': [{'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 24, 25, 35, 28, 27, 29, 33, 31, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10694.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 5, 4, 6, 1, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12], 'cur_cost': 14385.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 3, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13123.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,938 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 14057.00)
2025-08-05 09:52:11,938 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:11,938 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:11,938 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:11,938 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 80834.0
2025-08-05 09:52:11,948 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:11,949 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0]
2025-08-05 09:52:11,949 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:11,951 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:11,951 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10537.0}, {'tour': array([50, 49, 40,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6], dtype=int64), 'cur_cost': 98344.0}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 13996.0}, {'tour': array([40,  5, 52,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35], dtype=int64), 'cur_cost': 103696.0}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 54818.0}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14057.0}, {'tour': array([23, 31, 40,  2, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25], dtype=int64), 'cur_cost': 80834.0}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58365.0}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87948.0}, {'tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 98041.0}]
2025-08-05 09:52:11,953 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:11,953 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 306, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 306, 'cache_hits': 0, 'similarity_calculations': 1533, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:11,954 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([23, 31, 40,  2, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25], dtype=int64), 'cur_cost': 80834.0, 'intermediate_solutions': [{'tour': array([58, 23, 55, 50, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 102023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 58, 23, 55, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 102021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 50, 58, 23, 55, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 104202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 50, 58, 23, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 101966.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55, 47, 50, 58, 23, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 106348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:11,954 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 80834.00)
2025-08-05 09:52:11,954 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:11,954 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:11,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,960 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,960 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,961 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42899.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,961 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0, 'intermediate_solutions': [{'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 32, 36, 50, 15], 'cur_cost': 60176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 50, 32, 36, 44, 47, 42, 40, 49, 23, 52, 13, 22, 55, 3, 46, 28, 1, 24, 33, 9, 7, 37, 38, 45, 4, 0, 25, 26, 8, 18, 12, 15], 'cur_cost': 56389.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 49, 55, 22, 13, 52, 23, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,962 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 42899.00)
2025-08-05 09:52:11,962 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:11,962 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:11,962 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,968 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,968 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,969 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57689.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,969 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 57689.0, 'intermediate_solutions': [{'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 57, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 49, 33, 7, 1, 55, 20], 'cur_cost': 87950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 32, 41, 29, 34, 45, 27, 31, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 0, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,970 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 57689.00)
2025-08-05 09:52:11,970 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:11,970 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:11,970 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:11,981 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:11,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:11,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 69949.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:11,984 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 69949.0, 'intermediate_solutions': [{'tour': [18, 2, 21, 34, 15, 17, 5, 23, 54, 0, 27, 45, 51, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 101430.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 28, 26, 41, 44, 7, 33, 55, 13, 46, 16, 39, 36, 43, 20, 50, 57, 56, 37, 1, 59, 19, 47, 12, 29, 40, 8, 35, 52, 3, 14, 24, 53, 38, 22, 4, 30, 6, 58, 48, 31, 10, 25], 'cur_cost': 100551.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 2, 21, 51, 15, 17, 5, 30, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 97569.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:11,984 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 69949.00)
2025-08-05 09:52:11,984 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:11,985 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:11,989 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10537.0, 'intermediate_solutions': [{'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 32, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 1, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 58451.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 55, 21, 57, 58, 13, 48, 31, 38, 10, 7, 11, 37, 32, 41, 24, 29, 34, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 42, 44, 54], 'cur_cost': 61973.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 18, 23, 9, 51, 4, 25, 35, 3, 5, 1, 28, 36, 40, 0, 26, 27, 45, 34, 29, 24, 41, 32, 42, 37, 11, 7, 10, 38, 31, 48, 13, 58, 57, 21, 55, 16, 56, 19, 22, 50, 53, 15, 59, 2, 30, 39, 43, 46, 33, 8, 6, 52, 14, 20, 12, 49, 47, 44, 54], 'cur_cost': 60062.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 49, 40,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6], dtype=int64), 'cur_cost': 98344.0, 'intermediate_solutions': [{'tour': array([ 8, 48, 17, 18, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 98274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18,  8, 48, 17, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 18,  8, 48, 17,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102194.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 18,  8, 48, 58,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 100202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 58, 18,  8, 48,  4, 25, 13,  3, 22, 43, 28, 36, 40, 31, 26, 11,
       54, 16, 42, 24, 55, 52, 37,  2,  7, 21, 38,  9, 41, 15, 32, 57, 30,
        5,  1, 56, 19, 20, 50, 53, 33, 59, 29, 14, 39, 46, 49, 35, 47,  6,
       44, 51, 12, 27, 34, 23, 45,  0, 10]), 'cur_cost': 102112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 13996.0, 'intermediate_solutions': [{'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 31, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 47], 'cur_cost': 16963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 2, 21, 14, 18, 19, 30, 34, 44, 43, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 5, 4, 6, 1, 10, 8, 7, 11, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 13, 12, 23, 16, 20, 22, 17, 15, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 15976.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 2, 21, 14, 18, 19, 15, 17, 22, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 20, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 16839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([40,  5, 52,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35], dtype=int64), 'cur_cost': 103696.0, 'intermediate_solutions': [{'tour': array([39, 34, 23, 40, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 39, 34, 23, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 40, 39, 34, 23, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 96691.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 40, 39, 34, 46, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 105422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 46, 40, 39, 34, 16, 14, 29, 33, 59, 26,  8,  7, 22,  6, 56, 18,
       30, 35, 49, 25, 13, 19, 32, 12, 42,  3, 45, 54, 55, 28,  5, 10, 52,
        1, 58, 11, 50, 17, 27, 15,  9, 44,  4, 51, 43, 38, 48, 53, 36,  2,
       20, 24,  0, 21, 37, 57, 41, 31, 47]), 'cur_cost': 102961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 54818.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 24, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 49, 35, 25, 32, 26, 31], 'cur_cost': 20689.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 22, 12, 23, 16, 20, 13, 18, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14470.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 56, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 15070.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14057.0, 'intermediate_solutions': [{'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 24, 25, 35, 28, 27, 29, 33, 31, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10694.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 5, 4, 6, 1, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12], 'cur_cost': 14385.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 20, 19, 14, 21, 18, 13, 15, 17, 22, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 3, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13123.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 31, 40,  2, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25], dtype=int64), 'cur_cost': 80834.0, 'intermediate_solutions': [{'tour': array([58, 23, 55, 50, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 102023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([50, 58, 23, 55, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 102021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 50, 58, 23, 55, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 104202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 50, 58, 23, 47, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 101966.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55, 47, 50, 58, 23, 38,  1, 24,  0,  4, 49, 27, 37,  2, 48, 14,  5,
       41, 42, 19, 26, 57, 44, 18, 45, 28, 12, 33,  6, 15, 22, 32, 30, 13,
       43,  7, 46, 16,  9, 31, 35, 56, 59, 39, 29, 21, 54, 36, 51, 10, 11,
       52, 34, 53,  3, 25, 40, 20, 17,  8]), 'cur_cost': 106348.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0, 'intermediate_solutions': [{'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 55, 22, 13, 52, 23, 49, 40, 42, 47, 44, 32, 36, 50, 15], 'cur_cost': 60176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 50, 32, 36, 44, 47, 42, 40, 49, 23, 52, 13, 22, 55, 3, 46, 28, 1, 24, 33, 9, 7, 37, 38, 45, 4, 0, 25, 26, 8, 18, 12, 15], 'cur_cost': 56389.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [58, 59, 56, 19, 21, 2, 34, 6, 35, 30, 11, 29, 43, 10, 27, 31, 39, 41, 51, 16, 57, 53, 20, 17, 5, 48, 14, 54, 12, 18, 8, 26, 25, 0, 4, 45, 38, 37, 7, 9, 33, 24, 1, 28, 46, 3, 49, 55, 22, 13, 52, 23, 40, 42, 47, 44, 36, 32, 50, 15], 'cur_cost': 58288.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 57689.0, 'intermediate_solutions': [{'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 31, 27, 45, 34, 29, 41, 32, 57, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 49, 33, 7, 1, 55, 20], 'cur_cost': 87950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 32, 41, 29, 34, 45, 27, 31, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 0, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 8, 21, 51, 19, 35, 17, 43, 23, 54, 0, 31, 27, 45, 34, 29, 41, 32, 49, 2, 10, 9, 48, 13, 58, 6, 30, 5, 16, 22, 40, 47, 14, 3, 46, 52, 28, 12, 37, 39, 38, 26, 25, 15, 50, 24, 53, 59, 56, 11, 44, 36, 42, 4, 57, 33, 7, 1, 55, 20], 'cur_cost': 87687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 69949.0, 'intermediate_solutions': [{'tour': [18, 2, 21, 34, 15, 17, 5, 23, 54, 0, 27, 45, 51, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 30, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 101430.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 2, 21, 51, 15, 17, 5, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 28, 26, 41, 44, 7, 33, 55, 13, 46, 16, 39, 36, 43, 20, 50, 57, 56, 37, 1, 59, 19, 47, 12, 29, 40, 8, 35, 52, 3, 14, 24, 53, 38, 22, 4, 30, 6, 58, 48, 31, 10, 25], 'cur_cost': 100551.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 2, 21, 51, 15, 17, 5, 30, 23, 54, 0, 27, 45, 34, 42, 32, 49, 11, 9, 10, 31, 48, 58, 6, 4, 22, 38, 53, 24, 14, 3, 52, 35, 8, 40, 29, 12, 47, 19, 59, 1, 37, 56, 57, 50, 20, 43, 36, 39, 16, 46, 13, 55, 33, 7, 44, 41, 26, 28, 25], 'cur_cost': 97569.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:11,990 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:11,990 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:11,995 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10537.000, 多样性=0.970
2025-08-05 09:52:11,996 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:11,996 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:11,997 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:11,998 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04562600420120888, 'best_improvement': -0.005822833142420772}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0076982294072361155}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.005623060054455051, 'recent_improvements': [-0.05835561941667983, 0.06489597497794648, -0.047109499307769725], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.8396825396825397, 'new_diversity': 0.8396825396825397, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:11,999 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:11,999 - __main__ - INFO - composite12_60 开始进化第 4 代
2025-08-05 09:52:11,999 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:12,000 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:12,001 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10537.000, 多样性=0.970
2025-08-05 09:52:12,001 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:12,006 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-05 09:52:12,007 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:12,010 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.840
2025-08-05 09:52:12,012 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:12,013 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:12,013 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:12,013 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:12,070 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -13840.565, 聚类评分: 0.000, 覆盖率: 0.140, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:12,070 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:12,071 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:12,071 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 09:52:12,076 - visualization.landscape_visualizer - INFO - 插值约束: 98 个点被约束到最小值 9614.00
2025-08-05 09:52:12,187 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_119_20250805_095212.html
2025-08-05 09:52:12,230 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_119_20250805_095212.html
2025-08-05 09:52:12,230 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 119
2025-08-05 09:52:12,230 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:12,231 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2188秒
2025-08-05 09:52:12,231 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13840.564705882352, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 868982175.1964015, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1403, 'fitness_entropy': 0.7180212293982637, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13840.565)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.140)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358732.0703368, 'performance_metrics': {}}}
2025-08-05 09:52:12,231 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:12,231 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:12,231 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:12,231 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:12,232 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,232 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:12,232 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,232 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,232 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:12,232 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,233 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,233 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:12,233 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:12,233 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:12,233 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:12,233 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,235 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,236 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,236 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,236 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,236 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10529.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,237 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10529.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 32, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 1, 26, 31], 'cur_cost': 15460.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 26, 32, 25, 35, 24, 33, 29, 27, 31], 'cur_cost': 10620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 56, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 13212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,237 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 10529.00)
2025-08-05 09:52:12,237 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:52:12,237 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,237 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,237 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 91676.0
2025-08-05 09:52:12,248 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:12,248 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0]
2025-08-05 09:52:12,248 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:12,251 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,251 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10529.0}, {'tour': array([23, 30, 26, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51], dtype=int64), 'cur_cost': 91676.0}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 13996.0}, {'tour': [40, 5, 52, 8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16, 47, 9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13, 6, 48, 45, 31, 51, 59, 7, 29, 2, 10, 39, 42, 11, 34, 4, 28, 18, 30, 0, 55, 21, 1, 3, 14, 24, 35], 'cur_cost': 103696.0}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 54818.0}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14057.0}, {'tour': [23, 31, 40, 2, 18, 20, 7, 5, 24, 30, 45, 9, 10, 32, 55, 27, 47, 14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48, 1, 3, 8, 54, 41, 58, 37, 36, 46, 6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43, 39, 38, 49, 15, 52, 4, 0, 44, 25], 'cur_cost': 80834.0}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 57689.0}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 69949.0}]
2025-08-05 09:52:12,251 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:12,251 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 307, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 307, 'cache_hits': 0, 'similarity_calculations': 1540, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,252 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([23, 30, 26, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51], dtype=int64), 'cur_cost': 91676.0, 'intermediate_solutions': [{'tour': array([40, 49, 50,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 98281.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 40, 49, 50, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 96127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23,  7, 40, 49, 50, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 100019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50,  7, 40, 49, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 98703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 23,  7, 40, 49, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 100343.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,252 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 91676.00)
2025-08-05 09:52:12,252 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:12,253 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:12,253 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,255 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,256 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14023.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,256 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14023.0, 'intermediate_solutions': [{'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 35, 31, 25, 32, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 14076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 51, 44, 43, 41, 39, 46, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 18238.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 29, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 18258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,256 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 14023.00)
2025-08-05 09:52:12,256 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:12,257 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,257 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,257 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 109665.0
2025-08-05 09:52:12,269 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:12,269 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0, 9614, 9614]
2025-08-05 09:52:12,269 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:12,273 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,273 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10529.0}, {'tour': array([23, 30, 26, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51], dtype=int64), 'cur_cost': 91676.0}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14023.0}, {'tour': array([19, 48, 38, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36], dtype=int64), 'cur_cost': 109665.0}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 54818.0}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14057.0}, {'tour': [23, 31, 40, 2, 18, 20, 7, 5, 24, 30, 45, 9, 10, 32, 55, 27, 47, 14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48, 1, 3, 8, 54, 41, 58, 37, 36, 46, 6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43, 39, 38, 49, 15, 52, 4, 0, 44, 25], 'cur_cost': 80834.0}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 57689.0}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 69949.0}]
2025-08-05 09:52:12,274 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,274 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 308, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 308, 'cache_hits': 0, 'similarity_calculations': 1548, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,275 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([19, 48, 38, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36], dtype=int64), 'cur_cost': 109665.0, 'intermediate_solutions': [{'tour': array([52,  5, 40,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 104022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 52,  5, 40, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103886.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15,  8, 52,  5, 40, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40,  8, 52,  5, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103757.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 15,  8, 52,  5, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 104084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,275 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 109665.00)
2025-08-05 09:52:12,276 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:12,276 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:12,276 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,277 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:12,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,279 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67271.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,279 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 67271.0, 'intermediate_solutions': [{'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 55, 24, 0, 8, 52, 23, 34, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 61756.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 50, 48, 54, 28, 27, 37, 41, 38, 43, 26, 44, 40, 10, 25, 31, 3, 42, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 56934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 4, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 57246.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,279 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 67271.00)
2025-08-05 09:52:12,279 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:12,279 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:12,280 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,281 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,283 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10500.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,283 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10500.0, 'intermediate_solutions': [{'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 59, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 35, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 23895.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 15, 17, 19, 21, 18, 13], 'cur_cost': 16256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 25, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19615.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,283 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 10500.00)
2025-08-05 09:52:12,283 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:12,283 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,283 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,283 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 96344.0
2025-08-05 09:52:12,295 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:12,295 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9642, 9648, 9649.0, 9614, 9614]
2025-08-05 09:52:12,295 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:12,299 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,299 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10529.0}, {'tour': array([23, 30, 26, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51], dtype=int64), 'cur_cost': 91676.0}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14023.0}, {'tour': array([19, 48, 38, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36], dtype=int64), 'cur_cost': 109665.0}, {'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 67271.0}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10500.0}, {'tour': array([54, 42, 47, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10], dtype=int64), 'cur_cost': 96344.0}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 57689.0}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 69949.0}]
2025-08-05 09:52:12,301 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,301 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 309, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 309, 'cache_hits': 0, 'similarity_calculations': 1557, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,302 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([54, 42, 47, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10], dtype=int64), 'cur_cost': 96344.0, 'intermediate_solutions': [{'tour': array([40, 31, 23,  2, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 40, 31, 23, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 77473.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18,  2, 40, 31, 23, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80720.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23,  2, 40, 31, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80825.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 18,  2, 40, 31, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,302 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 96344.00)
2025-08-05 09:52:12,302 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:12,302 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:12,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,304 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:12,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,305 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,305 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85357.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,305 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 85357.0, 'intermediate_solutions': [{'tour': [49, 31, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 59, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 49384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 31, 39, 55, 22, 21, 15, 19, 16, 57, 51, 58, 50, 45, 4, 0, 35, 37, 38, 32, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 28, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 25, 24, 29, 8, 52, 12], 'cur_cost': 42901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,306 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 85357.00)
2025-08-05 09:52:12,306 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:12,306 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:12,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,308 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 09:52:12,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,309 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 73987.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,309 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 25, 17], 'cur_cost': 73987.0, 'intermediate_solutions': [{'tour': [10, 9, 43, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 7, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 59861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 57, 41, 25, 58, 54, 55, 21, 19], 'cur_cost': 62720.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 12, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 59747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,309 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 73987.00)
2025-08-05 09:52:12,309 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:12,310 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:12,310 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,312 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10475.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,313 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10475.0, 'intermediate_solutions': [{'tour': [43, 31, 36, 29, 6, 52, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 5, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 71838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 47, 40, 4, 39, 35, 25, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 70053.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 37, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34], 'cur_cost': 72182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,313 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10475.00)
2025-08-05 09:52:12,313 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:12,313 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:12,316 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10529.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 32, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 1, 26, 31], 'cur_cost': 15460.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 26, 32, 25, 35, 24, 33, 29, 27, 31], 'cur_cost': 10620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 5, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 56, 11, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 13212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 30, 26, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51], dtype=int64), 'cur_cost': 91676.0, 'intermediate_solutions': [{'tour': array([40, 49, 50,  7, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 98281.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 40, 49, 50, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 96127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23,  7, 40, 49, 50, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 100019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50,  7, 40, 49, 23, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 98703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 23,  7, 40, 49, 21, 18, 38, 26, 28, 13, 59, 29, 48, 41,  2,  4,
        1, 58, 15, 35, 11, 19, 53, 45, 54,  5, 34, 16,  8, 20,  9, 43, 33,
       56, 52, 31, 22,  3, 14, 44, 46, 17, 12, 55, 24, 37, 51, 25, 39, 36,
       10, 47, 57,  0, 32, 42, 30, 27,  6]), 'cur_cost': 100343.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14023.0, 'intermediate_solutions': [{'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 35, 31, 25, 32, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 14076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 51, 44, 43, 41, 39, 46, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 18238.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 11, 6, 4, 5, 1, 10, 7, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 29, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18], 'cur_cost': 18258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 48, 38, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36], dtype=int64), 'cur_cost': 109665.0, 'intermediate_solutions': [{'tour': array([52,  5, 40,  8, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 104022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 52,  5, 40, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103886.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15,  8, 52,  5, 40, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([40,  8, 52,  5, 15, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 103757.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([40, 15,  8, 52,  5, 26, 58, 17, 54, 25, 49, 50, 56, 46, 33, 19, 16,
       47,  9, 32, 22, 44, 38, 23, 27, 57, 43, 12, 36, 41, 20, 37, 53, 13,
        6, 48, 45, 31, 51, 59,  7, 29,  2, 10, 39, 42, 11, 34,  4, 28, 18,
       30,  0, 55, 21,  1,  3, 14, 24, 35]), 'cur_cost': 104084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 67271.0, 'intermediate_solutions': [{'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 55, 24, 0, 8, 52, 23, 34, 53, 58, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 61756.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 4, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 12, 13, 16, 1, 50, 48, 54, 28, 27, 37, 41, 38, 43, 26, 44, 40, 10, 25, 31, 3, 42, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 56934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 17, 59, 51, 20, 57, 6, 46, 7, 2, 11, 32, 9, 30, 5, 29, 45, 35, 33, 39, 36, 34, 24, 0, 8, 52, 23, 55, 53, 58, 4, 12, 13, 16, 1, 42, 3, 31, 25, 10, 40, 44, 26, 43, 38, 41, 37, 27, 28, 54, 48, 50, 18, 21, 15, 19, 14, 56, 49, 47], 'cur_cost': 57246.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10500.0, 'intermediate_solutions': [{'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 59, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 35, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 23895.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 15, 17, 19, 21, 18, 13], 'cur_cost': 16256.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 7, 3, 2, 11, 10, 1, 6, 4, 5, 9, 8, 26, 32, 31, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 25, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19615.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 42, 47, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10], dtype=int64), 'cur_cost': 96344.0, 'intermediate_solutions': [{'tour': array([40, 31, 23,  2, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 40, 31, 23, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 77473.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18,  2, 40, 31, 23, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80720.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23,  2, 40, 31, 18, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80825.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 18,  2, 40, 31, 20,  7,  5, 24, 30, 45,  9, 10, 32, 55, 27, 47,
       14, 22, 19, 28, 51, 26, 35, 12, 56, 21, 17, 48,  1,  3,  8, 54, 41,
       58, 37, 36, 46,  6, 42, 33, 59, 50, 16, 11, 53, 57, 13, 29, 34, 43,
       39, 38, 49, 15, 52,  4,  0, 44, 25]), 'cur_cost': 80832.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 85357.0, 'intermediate_solutions': [{'tour': [49, 31, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 59, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 49384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 31, 39, 55, 22, 21, 15, 19, 16, 57, 51, 58, 50, 45, 4, 0, 35, 37, 38, 32, 28, 25, 24, 29, 8, 52, 12], 'cur_cost': 42899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [49, 59, 20, 56, 54, 2, 3, 48, 18, 53, 17, 23, 14, 13, 9, 41, 40, 42, 46, 44, 43, 47, 1, 10, 6, 33, 7, 34, 30, 11, 5, 36, 27, 26, 32, 28, 38, 37, 35, 0, 4, 45, 50, 58, 51, 57, 16, 19, 15, 21, 22, 55, 39, 31, 25, 24, 29, 8, 52, 12], 'cur_cost': 42901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 25, 17], 'cur_cost': 73987.0, 'intermediate_solutions': [{'tour': [10, 9, 43, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 7, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 59861.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 12, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 57, 41, 25, 58, 54, 55, 21, 19], 'cur_cost': 62720.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 9, 7, 34, 24, 1, 31, 30, 4, 29, 36, 44, 26, 37, 12, 51, 53, 18, 20, 16, 50, 22, 17, 48, 23, 49, 56, 14, 5, 2, 33, 45, 3, 59, 52, 15, 13, 32, 38, 42, 47, 0, 35, 6, 27, 11, 39, 46, 43, 8, 40, 28, 25, 41, 57, 58, 54, 55, 21, 19], 'cur_cost': 59747.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10475.0, 'intermediate_solutions': [{'tour': [43, 31, 36, 29, 6, 52, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 5, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 71838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 47, 40, 4, 39, 35, 25, 0, 46, 10, 45, 2, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34, 37], 'cur_cost': 70053.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [43, 31, 36, 29, 6, 5, 59, 15, 18, 48, 55, 11, 24, 38, 33, 3, 51, 50, 12, 57, 7, 25, 35, 39, 4, 40, 47, 0, 46, 10, 45, 2, 37, 30, 1, 28, 53, 49, 21, 22, 54, 13, 52, 19, 58, 9, 32, 41, 8, 27, 26, 42, 44, 14, 20, 23, 16, 56, 17, 34], 'cur_cost': 72182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:12,316 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:12,316 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:12,319 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10475.000, 多样性=0.960
2025-08-05 09:52:12,319 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:12,320 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:12,320 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:12,321 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03125151422123978, 'best_improvement': 0.00588402771187245}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.009549274255156422}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0096349853883688, 'recent_improvements': [0.06489597497794648, -0.047109499307769725, 0.04562600420120888], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.8412037037037037, 'new_diversity': 0.8412037037037037, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:12,322 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:12,322 - __main__ - INFO - composite12_60 开始进化第 5 代
2025-08-05 09:52:12,322 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:12,322 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:12,323 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10475.000, 多样性=0.960
2025-08-05 09:52:12,323 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:12,325 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.960
2025-08-05 09:52:12,326 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:12,330 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.841
2025-08-05 09:52:12,331 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:12,331 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:12,332 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 09:52:12,332 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 09:52:12,388 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.316, 适应度梯度: -9232.947, 聚类评分: 0.000, 覆盖率: 0.141, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:12,388 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:12,389 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:12,389 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 09:52:12,434 - visualization.landscape_visualizer - INFO - 插值约束: 109 个点被约束到最小值 9614.00
2025-08-05 09:52:12,531 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_120_20250805_095212.html
2025-08-05 09:52:12,571 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_120_20250805_095212.html
2025-08-05 09:52:12,571 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 120
2025-08-05 09:52:12,571 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:12,571 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2400秒
2025-08-05 09:52:12,571 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3157894736842105, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -9232.947368421053, 'local_optima_density': 0.3157894736842105, 'gradient_variance': 690742628.2688088, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.141, 'fitness_entropy': 0.6231365243984397, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9232.947)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.141)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358732.3885221, 'performance_metrics': {}}}
2025-08-05 09:52:12,571 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:12,572 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:12,572 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:12,572 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:12,572 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,572 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:12,572 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,572 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,572 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:12,573 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:52:12,573 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:12,573 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:12,573 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:12,573 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:12,573 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:12,573 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,577 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:12,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,578 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,578 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62130.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,579 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [23, 52, 11, 28, 29, 38, 45, 6, 34, 3, 57, 9, 32, 7, 39, 59, 17, 53, 19, 56, 14, 48, 20, 18, 51, 10, 46, 37, 2, 31, 0, 24, 30, 5, 33, 25, 35, 43, 47, 44, 8, 41, 26, 40, 36, 1, 54, 12, 22, 15, 49, 50, 58, 55, 13, 16, 4, 27, 42, 21], 'cur_cost': 62130.0, 'intermediate_solutions': [{'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 3, 50, 58, 49, 48, 9, 51, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 44, 43, 41], 'cur_cost': 10471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 22, 14, 21, 44, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43], 'cur_cost': 16696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,579 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 62130.00)
2025-08-05 09:52:12,579 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:52:12,579 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,579 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,579 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 89045.0
2025-08-05 09:52:12,590 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 09:52:12,591 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9642, 9648, 9649.0]
2025-08-05 09:52:12,591 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64)]
2025-08-05 09:52:12,594 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,595 - ExploitationExpert - INFO - populations: [{'tour': [23, 52, 11, 28, 29, 38, 45, 6, 34, 3, 57, 9, 32, 7, 39, 59, 17, 53, 19, 56, 14, 48, 20, 18, 51, 10, 46, 37, 2, 31, 0, 24, 30, 5, 33, 25, 35, 43, 47, 44, 8, 41, 26, 40, 36, 1, 54, 12, 22, 15, 49, 50, 58, 55, 13, 16, 4, 27, 42, 21], 'cur_cost': 62130.0}, {'tour': array([28,  8,  6, 27, 34, 24,  4, 19, 23, 11, 46, 52, 15,  0, 45, 38, 50,
       14, 31, 51, 47, 57, 39, 36, 44, 26, 22, 20, 41, 13, 43,  3, 37, 49,
       16, 25, 29, 30, 55, 56,  7, 17, 58,  5, 21, 48,  2, 35, 33, 54, 12,
       32, 40, 18,  9, 10, 42,  1, 59, 53], dtype=int64), 'cur_cost': 89045.0}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14023.0}, {'tour': [19, 48, 38, 55, 58, 45, 41, 9, 40, 51, 26, 30, 24, 3, 59, 6, 8, 56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33, 4, 2, 29, 47, 49, 5, 32, 17, 28, 13, 0, 12, 42, 18, 35, 10, 22, 43, 52, 25, 1, 39, 15, 7, 50, 16, 46, 11, 36], 'cur_cost': 109665.0}, {'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 67271.0}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10500.0}, {'tour': [54, 42, 47, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26, 0, 59, 25, 44, 13, 31, 11, 5, 4, 30, 50, 34, 28, 29, 21, 20, 36, 9, 57, 53, 14, 8, 33, 45, 48, 17, 32, 6, 19, 7, 22, 3, 56, 1, 55, 41, 43, 39, 38, 58, 2, 51, 52, 49, 15, 10], 'cur_cost': 96344.0}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 85357.0}, {'tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 25, 17], 'cur_cost': 73987.0}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10475.0}]
2025-08-05 09:52:12,596 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,596 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 310, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 310, 'cache_hits': 0, 'similarity_calculations': 1567, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,597 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([28,  8,  6, 27, 34, 24,  4, 19, 23, 11, 46, 52, 15,  0, 45, 38, 50,
       14, 31, 51, 47, 57, 39, 36, 44, 26, 22, 20, 41, 13, 43,  3, 37, 49,
       16, 25, 29, 30, 55, 56,  7, 17, 58,  5, 21, 48,  2, 35, 33, 54, 12,
       32, 40, 18,  9, 10, 42,  1, 59, 53], dtype=int64), 'cur_cost': 89045.0, 'intermediate_solutions': [{'tour': array([26, 30, 23, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 94084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 26, 30, 23, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 93386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 38, 26, 30, 23, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 95789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 38, 26, 30, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 89967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 32, 38, 26, 30, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 91641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,597 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 89045.00)
2025-08-05 09:52:12,597 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:12,597 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:12,597 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,599 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,600 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,601 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10631.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,601 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 1, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 8, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10631.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 28, 5, 9, 3, 26, 32, 31, 25, 35, 4, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 16544.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 23, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19525.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,601 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10631.00)
2025-08-05 09:52:12,601 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:52:12,601 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,602 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 99418.0
2025-08-05 09:52:12,611 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:52:12,612 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9642, 9648, 9649.0, 9614]
2025-08-05 09:52:12,612 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:12,615 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,615 - ExploitationExpert - INFO - populations: [{'tour': [23, 52, 11, 28, 29, 38, 45, 6, 34, 3, 57, 9, 32, 7, 39, 59, 17, 53, 19, 56, 14, 48, 20, 18, 51, 10, 46, 37, 2, 31, 0, 24, 30, 5, 33, 25, 35, 43, 47, 44, 8, 41, 26, 40, 36, 1, 54, 12, 22, 15, 49, 50, 58, 55, 13, 16, 4, 27, 42, 21], 'cur_cost': 62130.0}, {'tour': array([28,  8,  6, 27, 34, 24,  4, 19, 23, 11, 46, 52, 15,  0, 45, 38, 50,
       14, 31, 51, 47, 57, 39, 36, 44, 26, 22, 20, 41, 13, 43,  3, 37, 49,
       16, 25, 29, 30, 55, 56,  7, 17, 58,  5, 21, 48,  2, 35, 33, 54, 12,
       32, 40, 18,  9, 10, 42,  1, 59, 53], dtype=int64), 'cur_cost': 89045.0}, {'tour': [0, 6, 1, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 8, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10631.0}, {'tour': array([39, 33, 29, 16, 15,  7, 46, 21, 13,  8, 47, 11,  1, 38, 57, 41,  6,
       27, 45, 18, 23, 59, 58, 36,  9, 49,  5, 42, 22, 31, 28, 54, 53, 30,
       19, 24, 43,  2, 56, 48, 10,  3, 14, 34, 51, 32,  0, 25, 17, 35, 40,
       26, 37, 55, 12, 44, 52, 50, 20,  4], dtype=int64), 'cur_cost': 99418.0}, {'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 67271.0}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10500.0}, {'tour': [54, 42, 47, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26, 0, 59, 25, 44, 13, 31, 11, 5, 4, 30, 50, 34, 28, 29, 21, 20, 36, 9, 57, 53, 14, 8, 33, 45, 48, 17, 32, 6, 19, 7, 22, 3, 56, 1, 55, 41, 43, 39, 38, 58, 2, 51, 52, 49, 15, 10], 'cur_cost': 96344.0}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 85357.0}, {'tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 25, 17], 'cur_cost': 73987.0}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10475.0}]
2025-08-05 09:52:12,616 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,616 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 311, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 311, 'cache_hits': 0, 'similarity_calculations': 1578, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,617 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([39, 33, 29, 16, 15,  7, 46, 21, 13,  8, 47, 11,  1, 38, 57, 41,  6,
       27, 45, 18, 23, 59, 58, 36,  9, 49,  5, 42, 22, 31, 28, 54, 53, 30,
       19, 24, 43,  2, 56, 48, 10,  3, 14, 34, 51, 32,  0, 25, 17, 35, 40,
       26, 37, 55, 12, 44, 52, 50, 20,  4], dtype=int64), 'cur_cost': 99418.0, 'intermediate_solutions': [{'tour': array([38, 48, 19, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 105322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([55, 38, 48, 19, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 55, 38, 48, 19, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109669.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 55, 38, 48, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 58, 55, 38, 48, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,617 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 99418.00)
2025-08-05 09:52:12,617 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:12,618 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:12,618 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,619 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,619 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,620 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10084.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,620 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 11, 5, 6, 4, 1, 10, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10084.0, 'intermediate_solutions': [{'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 11, 28, 32, 43, 20, 53, 54, 37, 31, 27, 15, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 68862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 31, 37, 54, 53, 20, 43, 32, 28, 15, 13, 9, 2, 3, 18, 16, 23, 41, 47, 44, 38, 45, 36, 34, 19, 33, 29, 40, 25, 46, 26, 42, 5, 4, 6, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 69731.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 22, 55, 56, 39, 24, 30, 59], 'cur_cost': 68521.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,620 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 10084.00)
2025-08-05 09:52:12,620 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:12,621 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:12,621 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,625 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:12,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,626 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53664.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,626 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [11, 53, 22, 20, 5, 31, 26, 38, 43, 7, 2, 10, 1, 6, 39, 30, 8, 29, 45, 44, 27, 33, 9, 57, 56, 13, 50, 58, 52, 19, 3, 0, 37, 40, 59, 16, 23, 12, 21, 18, 48, 41, 32, 24, 28, 25, 36, 42, 46, 35, 54, 51, 55, 15, 14, 17, 49, 4, 34, 47], 'cur_cost': 53664.0, 'intermediate_solutions': [{'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 32, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 52, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 20301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 41, 43, 44], 'cur_cost': 14690.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 51, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,626 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 53664.00)
2025-08-05 09:52:12,627 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:12,627 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:12,627 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:12,627 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 98956.0
2025-08-05 09:52:12,637 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:52:12,637 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9642, 9648, 9649.0, 9614]
2025-08-05 09:52:12,637 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 36, 45, 38, 42, 44, 40, 37, 47, 43, 46, 39, 41, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 13, 21, 14, 18, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 41, 39, 46, 43,
       47, 37, 40, 44, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 09:52:12,641 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:12,641 - ExploitationExpert - INFO - populations: [{'tour': [23, 52, 11, 28, 29, 38, 45, 6, 34, 3, 57, 9, 32, 7, 39, 59, 17, 53, 19, 56, 14, 48, 20, 18, 51, 10, 46, 37, 2, 31, 0, 24, 30, 5, 33, 25, 35, 43, 47, 44, 8, 41, 26, 40, 36, 1, 54, 12, 22, 15, 49, 50, 58, 55, 13, 16, 4, 27, 42, 21], 'cur_cost': 62130.0}, {'tour': array([28,  8,  6, 27, 34, 24,  4, 19, 23, 11, 46, 52, 15,  0, 45, 38, 50,
       14, 31, 51, 47, 57, 39, 36, 44, 26, 22, 20, 41, 13, 43,  3, 37, 49,
       16, 25, 29, 30, 55, 56,  7, 17, 58,  5, 21, 48,  2, 35, 33, 54, 12,
       32, 40, 18,  9, 10, 42,  1, 59, 53], dtype=int64), 'cur_cost': 89045.0}, {'tour': [0, 6, 1, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 8, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10631.0}, {'tour': array([39, 33, 29, 16, 15,  7, 46, 21, 13,  8, 47, 11,  1, 38, 57, 41,  6,
       27, 45, 18, 23, 59, 58, 36,  9, 49,  5, 42, 22, 31, 28, 54, 53, 30,
       19, 24, 43,  2, 56, 48, 10,  3, 14, 34, 51, 32,  0, 25, 17, 35, 40,
       26, 37, 55, 12, 44, 52, 50, 20,  4], dtype=int64), 'cur_cost': 99418.0}, {'tour': [0, 2, 11, 5, 6, 4, 1, 10, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10084.0}, {'tour': [11, 53, 22, 20, 5, 31, 26, 38, 43, 7, 2, 10, 1, 6, 39, 30, 8, 29, 45, 44, 27, 33, 9, 57, 56, 13, 50, 58, 52, 19, 3, 0, 37, 40, 59, 16, 23, 12, 21, 18, 48, 41, 32, 24, 28, 25, 36, 42, 46, 35, 54, 51, 55, 15, 14, 17, 49, 4, 34, 47], 'cur_cost': 53664.0}, {'tour': array([41, 44, 46, 53, 54, 13, 49, 43,  1, 18,  4,  5, 19, 39,  3, 47, 37,
       30, 21, 31, 27, 57, 38, 34, 15, 11, 23, 40, 17, 20,  2, 36, 45, 25,
       51, 59, 26, 16, 28, 48, 56, 14, 52,  0, 33, 22, 24,  9, 35, 10, 32,
        7, 50, 29, 55, 58,  6,  8, 42, 12], dtype=int64), 'cur_cost': 98956.0}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 85357.0}, {'tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 25, 17], 'cur_cost': 73987.0}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10475.0}]
2025-08-05 09:52:12,643 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:12,643 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 312, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 312, 'cache_hits': 0, 'similarity_calculations': 1590, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:12,645 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([41, 44, 46, 53, 54, 13, 49, 43,  1, 18,  4,  5, 19, 39,  3, 47, 37,
       30, 21, 31, 27, 57, 38, 34, 15, 11, 23, 40, 17, 20,  2, 36, 45, 25,
       51, 59, 26, 16, 28, 48, 56, 14, 52,  0, 33, 22, 24,  9, 35, 10, 32,
        7, 50, 29, 55, 58,  6,  8, 42, 12], dtype=int64), 'cur_cost': 98956.0, 'intermediate_solutions': [{'tour': array([47, 42, 54, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35, 47, 42, 54, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 35, 47, 42, 54, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 94164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 35, 47, 42, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 94439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 37, 35, 47, 42, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96339.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:12,646 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 98956.00)
2025-08-05 09:52:12,646 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:12,646 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:12,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,648 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,649 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,650 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10559.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,650 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 15, 14, 21, 18, 19, 13, 17, 22, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10559.0, 'intermediate_solutions': [{'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 51, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 40], 'cur_cost': 85366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 59, 21, 25, 57, 46, 15, 1, 12, 39, 2, 5, 58, 50, 22, 23, 34, 6, 53, 17, 3, 48, 32, 49, 7, 45, 37, 30, 47, 13, 38, 26, 33, 55, 4, 44, 41, 16, 40, 42, 36, 24, 29, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 90335.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 32, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 82237.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,650 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10559.00)
2025-08-05 09:52:12,650 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:12,650 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:12,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,655 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 09:52:12,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62573.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,656 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 29, 6, 25, 10, 7, 32, 31, 36, 3, 30, 0, 45, 4, 27, 47, 24, 43, 37, 50, 12, 56, 14, 53, 55, 20, 21, 17, 19, 22, 51, 59, 54, 15, 58, 52, 23, 11, 41, 1, 2, 9, 8, 28, 33, 38, 26, 46, 44, 34, 39, 35, 48, 49, 57, 18, 16, 13, 40, 42], 'cur_cost': 62573.0, 'intermediate_solutions': [{'tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 25, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 56, 17], 'cur_cost': 77410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 59, 0, 20, 21, 1, 30, 7, 38, 48, 35, 32, 15, 43, 52, 2, 54, 46, 19, 57, 53, 22, 18, 11, 47, 45, 34, 13, 26, 51, 12, 31, 3, 58, 49, 56, 55, 50, 44, 41, 39, 40, 42, 36, 24, 33, 29, 27, 28, 5, 9, 16, 4, 6, 10, 8, 14, 37, 25, 17], 'cur_cost': 73980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 8, 10, 6, 4, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 16, 59, 23, 37, 25, 17], 'cur_cost': 71839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,657 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 62573.00)
2025-08-05 09:52:12,657 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:12,657 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:12,657 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:12,659 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 09:52:12,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:12,660 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14196.0, 路径长度: 60, 收集中间解: 3
2025-08-05 09:52:12,660 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 5, 11, 2, 7, 8, 10, 1, 6, 4, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 13], 'cur_cost': 14196.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 9, 11, 19, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 2, 14, 21, 18, 13], 'cur_cost': 18231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 28, 30, 34, 44, 43, 36, 45, 38, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 43, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14794.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:12,660 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 14196.00)
2025-08-05 09:52:12,660 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:12,660 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:12,663 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [23, 52, 11, 28, 29, 38, 45, 6, 34, 3, 57, 9, 32, 7, 39, 59, 17, 53, 19, 56, 14, 48, 20, 18, 51, 10, 46, 37, 2, 31, 0, 24, 30, 5, 33, 25, 35, 43, 47, 44, 8, 41, 26, 40, 36, 1, 54, 12, 22, 15, 49, 50, 58, 55, 13, 16, 4, 27, 42, 21], 'cur_cost': 62130.0, 'intermediate_solutions': [{'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 3, 50, 58, 49, 48, 9, 51, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 22, 14, 21, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 44, 43, 41], 'cur_cost': 10471.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 22, 14, 21, 44, 18, 13, 15, 17, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43], 'cur_cost': 16696.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([28,  8,  6, 27, 34, 24,  4, 19, 23, 11, 46, 52, 15,  0, 45, 38, 50,
       14, 31, 51, 47, 57, 39, 36, 44, 26, 22, 20, 41, 13, 43,  3, 37, 49,
       16, 25, 29, 30, 55, 56,  7, 17, 58,  5, 21, 48,  2, 35, 33, 54, 12,
       32, 40, 18,  9, 10, 42,  1, 59, 53], dtype=int64), 'cur_cost': 89045.0, 'intermediate_solutions': [{'tour': array([26, 30, 23, 38, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 94084.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([38, 26, 30, 23, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 93386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 38, 26, 30, 23, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 95789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 38, 26, 30, 32, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 89967.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23, 32, 38, 26, 30, 35, 47, 25, 34, 58, 39, 28, 52, 53, 24, 37, 56,
        7, 31, 44, 45,  6,  4, 46, 11, 59, 15, 54, 33, 48,  0,  8, 36,  1,
       14, 57, 27, 22, 13, 41, 20,  9, 43, 50, 40, 21, 17, 42,  2,  3, 10,
       16, 19, 49,  5, 29, 55, 18, 12, 51]), 'cur_cost': 91641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 1, 15, 17, 19, 14, 21, 18, 13, 20, 16, 23, 12, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 8, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10631.0, 'intermediate_solutions': [{'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 28, 5, 9, 3, 26, 32, 31, 25, 35, 4, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 16544.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 14, 11, 2, 7, 8, 10, 6, 4, 5, 9, 3, 26, 32, 23, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 19525.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 33, 29, 16, 15,  7, 46, 21, 13,  8, 47, 11,  1, 38, 57, 41,  6,
       27, 45, 18, 23, 59, 58, 36,  9, 49,  5, 42, 22, 31, 28, 54, 53, 30,
       19, 24, 43,  2, 56, 48, 10,  3, 14, 34, 51, 32,  0, 25, 17, 35, 40,
       26, 37, 55, 12, 44, 52, 50, 20,  4], dtype=int64), 'cur_cost': 99418.0, 'intermediate_solutions': [{'tour': array([38, 48, 19, 55, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 105322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([55, 38, 48, 19, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109783.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([58, 55, 38, 48, 19, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109669.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 55, 38, 48, 58, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 58, 55, 38, 48, 45, 41,  9, 40, 51, 26, 30, 24,  3, 59,  6,  8,
       56, 44, 31, 57, 54, 27, 23, 21, 34, 14, 37, 53, 20, 33,  4,  2, 29,
       47, 49,  5, 32, 17, 28, 13,  0, 12, 42, 18, 35, 10, 22, 43, 52, 25,
        1, 39, 15,  7, 50, 16, 46, 11, 36]), 'cur_cost': 109641.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 11, 5, 6, 4, 1, 10, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10084.0, 'intermediate_solutions': [{'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 11, 28, 32, 43, 20, 53, 54, 37, 31, 27, 15, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 68862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 31, 37, 54, 53, 20, 43, 32, 28, 15, 13, 9, 2, 3, 18, 16, 23, 41, 47, 44, 38, 45, 36, 34, 19, 33, 29, 40, 25, 46, 26, 42, 5, 4, 6, 27, 11, 58, 0, 21, 35, 55, 56, 39, 24, 30, 59], 'cur_cost': 69731.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 8, 10, 17, 12, 57, 52, 51, 50, 49, 48, 7, 1, 6, 4, 5, 42, 26, 46, 25, 40, 29, 33, 19, 34, 36, 45, 38, 44, 47, 41, 23, 16, 18, 3, 2, 9, 13, 15, 28, 32, 43, 20, 53, 54, 37, 31, 27, 11, 58, 0, 21, 35, 22, 55, 56, 39, 24, 30, 59], 'cur_cost': 68521.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [11, 53, 22, 20, 5, 31, 26, 38, 43, 7, 2, 10, 1, 6, 39, 30, 8, 29, 45, 44, 27, 33, 9, 57, 56, 13, 50, 58, 52, 19, 3, 0, 37, 40, 59, 16, 23, 12, 21, 18, 48, 41, 32, 24, 28, 25, 36, 42, 46, 35, 54, 51, 55, 15, 14, 17, 49, 4, 34, 47], 'cur_cost': 53664.0, 'intermediate_solutions': [{'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 32, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 52, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 20301.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 41, 43, 44], 'cur_cost': 14690.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 18, 19, 14, 21, 13, 15, 17, 22, 20, 16, 23, 54, 56, 55, 53, 57, 52, 59, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 51, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 44, 46, 53, 54, 13, 49, 43,  1, 18,  4,  5, 19, 39,  3, 47, 37,
       30, 21, 31, 27, 57, 38, 34, 15, 11, 23, 40, 17, 20,  2, 36, 45, 25,
       51, 59, 26, 16, 28, 48, 56, 14, 52,  0, 33, 22, 24,  9, 35, 10, 32,
        7, 50, 29, 55, 58,  6,  8, 42, 12], dtype=int64), 'cur_cost': 98956.0, 'intermediate_solutions': [{'tour': array([47, 42, 54, 35, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35, 47, 42, 54, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37, 35, 47, 42, 54, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 94164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 35, 47, 42, 37, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 94439.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 37, 35, 47, 42, 16, 18, 24, 46, 12, 27, 40, 23, 26,  0, 59, 25,
       44, 13, 31, 11,  5,  4, 30, 50, 34, 28, 29, 21, 20, 36,  9, 57, 53,
       14,  8, 33, 45, 48, 17, 32,  6, 19,  7, 22,  3, 56,  1, 55, 41, 43,
       39, 38, 58,  2, 51, 52, 49, 15, 10]), 'cur_cost': 96339.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 15, 14, 21, 18, 19, 13, 17, 22, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10559.0, 'intermediate_solutions': [{'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 51, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 32, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 40], 'cur_cost': 85366.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 59, 21, 25, 57, 46, 15, 1, 12, 39, 2, 5, 58, 50, 22, 23, 34, 6, 53, 17, 3, 48, 32, 49, 7, 45, 37, 30, 47, 13, 38, 26, 33, 55, 4, 44, 41, 16, 40, 42, 36, 24, 29, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 90335.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 11, 18, 8, 9, 31, 35, 28, 27, 29, 24, 36, 42, 40, 16, 41, 44, 4, 55, 33, 26, 38, 13, 47, 30, 37, 45, 7, 49, 48, 3, 17, 53, 6, 34, 23, 22, 50, 58, 5, 2, 39, 32, 12, 1, 15, 46, 57, 25, 21, 59, 52, 56, 0, 54, 20, 10, 43, 51], 'cur_cost': 82237.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 29, 6, 25, 10, 7, 32, 31, 36, 3, 30, 0, 45, 4, 27, 47, 24, 43, 37, 50, 12, 56, 14, 53, 55, 20, 21, 17, 19, 22, 51, 59, 54, 15, 58, 52, 23, 11, 41, 1, 2, 9, 8, 28, 33, 38, 26, 46, 44, 34, 39, 35, 48, 49, 57, 18, 16, 13, 40, 42], 'cur_cost': 62573.0, 'intermediate_solutions': [{'tour': [14, 8, 10, 6, 4, 16, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 25, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 59, 23, 37, 56, 17], 'cur_cost': 77410.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 59, 0, 20, 21, 1, 30, 7, 38, 48, 35, 32, 15, 43, 52, 2, 54, 46, 19, 57, 53, 22, 18, 11, 47, 45, 34, 13, 26, 51, 12, 31, 3, 58, 49, 56, 55, 50, 44, 41, 39, 40, 42, 36, 24, 33, 29, 27, 28, 5, 9, 16, 4, 6, 10, 8, 14, 37, 25, 17], 'cur_cost': 73980.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 8, 10, 6, 4, 9, 5, 28, 27, 29, 33, 24, 36, 42, 40, 39, 41, 44, 50, 55, 56, 49, 58, 3, 31, 12, 51, 26, 13, 34, 45, 47, 11, 18, 22, 53, 57, 19, 46, 54, 2, 52, 43, 15, 32, 35, 48, 38, 7, 30, 1, 21, 20, 0, 16, 59, 23, 37, 25, 17], 'cur_cost': 71839.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 11, 2, 7, 8, 10, 1, 6, 4, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 15, 17, 22, 20, 16, 23, 13], 'cur_cost': 14196.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 9, 11, 19, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 2, 14, 21, 18, 13], 'cur_cost': 18231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 28, 30, 34, 44, 43, 36, 45, 38, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14767.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 3, 9, 11, 2, 7, 8, 10, 1, 6, 4, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 43, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14794.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:12,663 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:12,663 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:12,666 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10084.000, 多样性=0.974
2025-08-05 09:52:12,666 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:12,666 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:12,667 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:12,668 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.0778511387333267, 'best_improvement': 0.037326968973747016}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014269186270728726}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.007928992543264972, 'recent_improvements': [-0.047109499307769725, 0.04562600420120888, -0.03125151422123978], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.8444444444444444, 'new_diversity': 0.8444444444444444, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:12,669 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:12,676 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite12_60_solution.json
2025-08-05 09:52:12,677 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite12_60_20250805_095212.solution
2025-08-05 09:52:12,677 - __main__ - INFO - 实例执行完成 - 运行时间: 1.65s, 最佳成本: 9614
2025-08-05 09:52:12,677 - __main__ - INFO - 实例 composite12_60 处理完成
