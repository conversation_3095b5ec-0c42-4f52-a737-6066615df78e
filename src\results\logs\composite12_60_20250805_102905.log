2025-08-05 10:29:05,112 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-08-05 10:29:05,112 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:05,114 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:05,118 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9979.000, 多样性=0.960
2025-08-05 10:29:05,120 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:05,125 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.960
2025-08-05 10:29:05,129 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:05,133 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:05,134 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:05,134 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:05,134 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:05,166 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -18462.740, 聚类评分: 0.000, 覆盖率: 0.134, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:05,167 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:05,167 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:05,167 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 10:29:05,173 - visualization.landscape_visualizer - INFO - 插值约束: 6 个点被约束到最小值 9979.00
2025-08-05 10:29:05,176 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.0%, 梯度: 3419.14 → 3214.25
2025-08-05 10:29:05,354 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_116_20250805_102905.html
2025-08-05 10:29:05,409 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_116_20250805_102905.html
2025-08-05 10:29:05,409 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 116
2025-08-05 10:29:05,409 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:05,409 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2764秒
2025-08-05 10:29:05,410 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 232, 'max_size': 500, 'hits': 0, 'misses': 232, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 777, 'misses': 400, 'hit_rate': 0.6601529311809685, 'evictions': 300, 'ttl': 7200}}
2025-08-05 10:29:05,410 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -18462.74, 'local_optima_density': 0.1, 'gradient_variance': 2510996290.7844, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1337, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -18462.740)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.134)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360945.1675136, 'performance_metrics': {}}}
2025-08-05 10:29:05,410 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:05,410 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:05,410 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:05,410 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:05,411 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,411 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:05,412 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,412 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:05,412 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:05,412 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:05,412 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,415 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,415 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14353.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,415 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14353.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,416 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 14353.00)
2025-08-05 10:29:05,416 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:05,416 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:05,416 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,418 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,418 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,419 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14095.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,419 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14095.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,419 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 14095.00)
2025-08-05 10:29:05,419 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:05,419 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:05,419 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,421 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,422 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,422 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14445.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,422 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,422 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 14445.00)
2025-08-05 10:29:05,422 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:05,422 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:05,422 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:05,423 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103435.0
2025-08-05 10:29:05,434 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:29:05,434 - ExploitationExpert - INFO - res_population_costs: [9621.0]
2025-08-05 10:29:05,434 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-08-05 10:29:05,435 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:05,435 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14353.0}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14095.0}, {'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14445.0}, {'tour': array([34, 11, 31, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49], dtype=int64), 'cur_cost': 103435.0}, {'tour': array([47, 22, 51, 21, 56, 33,  5,  1,  6, 49, 19, 13, 32, 29, 48, 23, 46,
       27,  2, 52,  8, 20, 42, 53,  3,  9, 55, 10, 31, 11, 37, 30,  7, 43,
       34, 36, 15, 40, 12,  4,  0, 24, 58, 35, 18, 14, 57, 44, 39, 59, 25,
       38, 54, 50, 28, 41, 45, 17, 26, 16], dtype=int64), 'cur_cost': 102969.0}, {'tour': array([31, 21, 35,  1, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 112852.0}, {'tour': array([29, 10, 54, 15, 46,  8, 35, 40, 43, 36, 23, 14, 24, 37,  1,  9, 22,
       39, 32, 17, 55, 16, 38, 20, 44, 26,  2, 27, 30,  3, 57, 33, 21, 48,
       19, 41, 53, 31,  5, 56,  7, 45, 49, 34, 58, 52, 11, 13, 28,  4, 51,
       12, 50, 42, 47, 18,  0, 59, 25,  6], dtype=int64), 'cur_cost': 103293.0}, {'tour': array([15, 51,  3, 59, 50, 20, 33, 58, 18, 57,  2, 25, 53, 43, 23, 13,  4,
        9,  0, 30, 39, 12, 48, 22, 41, 21,  8, 14, 28, 17,  1, 34, 49, 37,
       10, 44, 46, 40, 56, 35, 36, 27, 38, 47, 26, 54, 42, 55,  7, 11, 24,
       31,  5, 32, 29, 52, 45, 19,  6, 16], dtype=int64), 'cur_cost': 98327.0}, {'tour': array([12, 55,  9, 35, 25, 40, 39, 32, 18, 26, 10, 58,  3, 45, 54,  8, 48,
       34,  2, 38, 52, 53, 42,  6, 15, 51,  4, 20, 33, 49, 46, 11, 29,  1,
       17, 30, 41, 44, 24, 59, 13, 28,  5, 16, 57, 27, 56, 43, 47, 31, 21,
       19,  7, 36, 50,  0, 23, 37, 22, 14], dtype=int64), 'cur_cost': 101027.0}, {'tour': array([42, 22, 37, 48,  0,  4, 17, 59, 46, 25, 47, 12, 30, 34, 55, 33,  3,
       10, 35, 13,  7, 11, 16, 27,  8, 43, 15, 45, 38,  6, 24,  1, 57, 39,
       32, 26, 41, 36, 29, 14, 58, 28,  2, 50, 21, 44, 20, 23, 40, 19, 56,
       31,  5,  9, 49, 53, 52, 18, 51, 54], dtype=int64), 'cur_cost': 99612.0}]
2025-08-05 10:29:05,438 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:05,438 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 300, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 300, 'cache_hits': 0, 'similarity_calculations': 1512, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:05,440 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([34, 11, 31, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49], dtype=int64), 'cur_cost': 103435.0, 'intermediate_solutions': [{'tour': array([25, 23, 54, 56, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 101440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 25, 23, 54, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 104082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 56, 25, 23, 54, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 101793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 56, 25, 23, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 103999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 42, 56, 25, 23, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 105935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:05,441 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 103435.00)
2025-08-05 10:29:05,441 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:05,441 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:05,441 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,451 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54854.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,452 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,452 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 54854.00)
2025-08-05 10:29:05,452 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:05,452 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:05,453 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:05,453 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 98170.0
2025-08-05 10:29:05,462 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:05,462 - ExploitationExpert - INFO - res_population_costs: [9621.0, 9620]
2025-08-05 10:29:05,462 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-08-05 10:29:05,464 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:05,464 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14353.0}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14095.0}, {'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14445.0}, {'tour': array([34, 11, 31, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49], dtype=int64), 'cur_cost': 103435.0}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54854.0}, {'tour': array([26, 29, 44, 25, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39], dtype=int64), 'cur_cost': 98170.0}, {'tour': array([29, 10, 54, 15, 46,  8, 35, 40, 43, 36, 23, 14, 24, 37,  1,  9, 22,
       39, 32, 17, 55, 16, 38, 20, 44, 26,  2, 27, 30,  3, 57, 33, 21, 48,
       19, 41, 53, 31,  5, 56,  7, 45, 49, 34, 58, 52, 11, 13, 28,  4, 51,
       12, 50, 42, 47, 18,  0, 59, 25,  6], dtype=int64), 'cur_cost': 103293.0}, {'tour': array([15, 51,  3, 59, 50, 20, 33, 58, 18, 57,  2, 25, 53, 43, 23, 13,  4,
        9,  0, 30, 39, 12, 48, 22, 41, 21,  8, 14, 28, 17,  1, 34, 49, 37,
       10, 44, 46, 40, 56, 35, 36, 27, 38, 47, 26, 54, 42, 55,  7, 11, 24,
       31,  5, 32, 29, 52, 45, 19,  6, 16], dtype=int64), 'cur_cost': 98327.0}, {'tour': array([12, 55,  9, 35, 25, 40, 39, 32, 18, 26, 10, 58,  3, 45, 54,  8, 48,
       34,  2, 38, 52, 53, 42,  6, 15, 51,  4, 20, 33, 49, 46, 11, 29,  1,
       17, 30, 41, 44, 24, 59, 13, 28,  5, 16, 57, 27, 56, 43, 47, 31, 21,
       19,  7, 36, 50,  0, 23, 37, 22, 14], dtype=int64), 'cur_cost': 101027.0}, {'tour': array([42, 22, 37, 48,  0,  4, 17, 59, 46, 25, 47, 12, 30, 34, 55, 33,  3,
       10, 35, 13,  7, 11, 16, 27,  8, 43, 15, 45, 38,  6, 24,  1, 57, 39,
       32, 26, 41, 36, 29, 14, 58, 28,  2, 50, 21, 44, 20, 23, 40, 19, 56,
       31,  5,  9, 49, 53, 52, 18, 51, 54], dtype=int64), 'cur_cost': 99612.0}]
2025-08-05 10:29:05,466 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:05,467 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 301, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 301, 'cache_hits': 0, 'similarity_calculations': 1513, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:05,468 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([26, 29, 44, 25, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39], dtype=int64), 'cur_cost': 98170.0, 'intermediate_solutions': [{'tour': array([35, 21, 31,  1, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 112833.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 35, 21, 31, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 113112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49,  1, 35, 21, 31, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 110450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  1, 35, 21, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 111003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 49,  1, 35, 21, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 112868.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:05,468 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 98170.00)
2025-08-05 10:29:05,468 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:05,468 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:05,468 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,470 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 10:29:05,470 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,471 - ExplorationExpert - INFO - 探索路径生成完成，成本: 91471.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,471 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 91471.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,471 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 91471.00)
2025-08-05 10:29:05,471 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:05,471 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:05,471 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,474 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 10:29:05,474 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,474 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86041.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,475 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,475 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 86041.00)
2025-08-05 10:29:05,475 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:05,475 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:05,475 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,481 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,481 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56076.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,481 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [25, 34, 24, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56076.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,481 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 56076.00)
2025-08-05 10:29:05,481 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:05,482 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:05,482 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,483 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,483 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,484 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10200.0, 路径长度: 60, 收集中间解: 0
2025-08-05 10:29:05,484 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10200.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,484 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10200.00)
2025-08-05 10:29:05,484 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:05,484 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:05,486 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14353.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 14095.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 11, 31, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49], dtype=int64), 'cur_cost': 103435.0, 'intermediate_solutions': [{'tour': array([25, 23, 54, 56, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 101440.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 25, 23, 54, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 104082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 56, 25, 23, 54, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 101793.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 56, 25, 23, 42, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 103999.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 42, 56, 25, 23, 11,  7, 28, 20, 44, 14, 15, 19, 35, 47, 30, 39,
       32, 37, 10, 41, 31, 26, 21, 40, 57, 24, 58, 51,  4, 48, 34, 52,  3,
        8,  0, 12,  9, 46,  5, 27, 17, 50, 55, 43, 18,  1, 59,  2, 33, 16,
       13,  6, 36, 38, 22, 29, 49, 53, 45], dtype=int64), 'cur_cost': 105935.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 29, 44, 25, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39], dtype=int64), 'cur_cost': 98170.0, 'intermediate_solutions': [{'tour': array([35, 21, 31,  1, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 112833.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 35, 21, 31, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 113112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49,  1, 35, 21, 31, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 110450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  1, 35, 21, 49, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 111003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 49,  1, 35, 21, 34, 50, 25,  0, 45, 23, 37, 32,  7, 27, 54, 14,
       40, 55, 58, 43, 24, 42, 16, 52, 17, 51, 39, 22,  3, 33,  2, 46, 15,
       41, 57, 59, 29, 26, 28,  5, 13, 11, 18, 36, 48,  6, 20, 56, 38,  8,
       53,  4,  9, 10, 19, 30, 47, 12, 44], dtype=int64), 'cur_cost': 112868.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 91471.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [25, 34, 24, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56076.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10200.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:05,486 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:05,486 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:05,489 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10200.000, 多样性=0.970
2025-08-05 10:29:05,489 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:05,489 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:05,491 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:05,491 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.11316286681945592, 'best_improvement': -0.022146507666098807}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.010412649440802052}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.011163137607014812, 'recent_improvements': [-0.06594273388969356, 0.018262874788902355, -0.043616458675663945], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 9620, 'new_best_cost': 9620, 'quality_improvement': 0.0, 'old_diversity': 0.9166666666666666, 'new_diversity': 0.9166666666666666, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:05,491 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:05,491 - __main__ - INFO - composite12_60 开始进化第 2 代
2025-08-05 10:29:05,491 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:05,491 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:05,492 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10200.000, 多样性=0.970
2025-08-05 10:29:05,492 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:05,493 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.970
2025-08-05 10:29:05,494 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:05,495 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.917
2025-08-05 10:29:05,496 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:05,496 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:05,496 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:29:05,496 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:29:05,525 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: -12947.667, 聚类评分: 0.000, 覆盖率: 0.135, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:05,526 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:05,526 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:05,526 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 10:29:05,533 - visualization.landscape_visualizer - INFO - 插值约束: 338 个点被约束到最小值 9620.00
2025-08-05 10:29:05,534 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.4%, 梯度: 4697.80 → 4254.63
2025-08-05 10:29:05,656 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_117_20250805_102905.html
2025-08-05 10:29:05,741 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_117_20250805_102905.html
2025-08-05 10:29:05,741 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 117
2025-08-05 10:29:05,741 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:05,742 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2457秒
2025-08-05 10:29:05,742 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -12947.666666666666, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 1182885541.428889, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1349, 'fitness_entropy': 0.8962406251802889, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -12947.667)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.135)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360945.5269532, 'performance_metrics': {}}}
2025-08-05 10:29:05,742 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:05,742 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:05,742 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:05,743 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:05,743 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,743 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:05,743 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,743 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:05,744 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:05,744 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:05,744 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:05,744 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:05,744 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:05,744 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:05,744 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:05,745 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,749 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59889.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,750 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 59889.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 34, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 1, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 17302.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 14, 21, 18], 'cur_cost': 14364.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 9, 59, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 16841.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,751 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 59889.00)
2025-08-05 10:29:05,751 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:05,751 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:05,751 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,756 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58730.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,757 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58730.0, 'intermediate_solutions': [{'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 54, 51, 59, 52, 57, 55, 56, 49, 58, 44, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 18639.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 20, 16, 23, 12, 50, 48, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 16192.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 19, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 21, 18, 13], 'cur_cost': 18041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,757 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 58730.00)
2025-08-05 10:29:05,757 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:05,757 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:05,757 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,759 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,759 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10513.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,760 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10513.0, 'intermediate_solutions': [{'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 32, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 54, 26, 31], 'cur_cost': 23562.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 17126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 16680.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,760 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10513.00)
2025-08-05 10:29:05,760 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:05,761 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:05,761 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:05,761 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 100172.0
2025-08-05 10:29:05,767 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:05,767 - ExploitationExpert - INFO - res_population_costs: [9620, 9621.0]
2025-08-05 10:29:05,767 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-08-05 10:29:05,768 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:05,768 - ExploitationExpert - INFO - populations: [{'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 59889.0}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58730.0}, {'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10513.0}, {'tour': array([ 3, 22, 57, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11], dtype=int64), 'cur_cost': 100172.0}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54854.0}, {'tour': [26, 29, 44, 25, 16, 30, 2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51, 37, 47, 17, 38, 15, 0, 18, 32, 34, 22, 48, 19, 12, 50, 27, 6, 36, 41, 55, 20, 8, 58, 1, 49, 42, 11, 24, 57, 4, 13, 7, 56, 43, 9, 46, 3, 5, 21, 33, 31, 53, 45, 39], 'cur_cost': 98170.0}, {'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 91471.0}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86041.0}, {'tour': [25, 34, 24, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56076.0}, {'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10200.0}]
2025-08-05 10:29:05,769 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:05,769 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 302, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 302, 'cache_hits': 0, 'similarity_calculations': 1515, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:05,770 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 3, 22, 57, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11], dtype=int64), 'cur_cost': 100172.0, 'intermediate_solutions': [{'tour': array([31, 11, 34, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103380.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37, 31, 11, 34, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 101006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 37, 31, 11, 34, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 37, 31, 11, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 24, 37, 31, 11, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103497.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:05,770 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 100172.00)
2025-08-05 10:29:05,770 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:05,770 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:05,770 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,775 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,776 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,776 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62626.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,777 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [43, 46, 10, 2, 30, 1, 29, 28, 9, 34, 0, 36, 24, 37, 38, 39, 59, 55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54, 3, 8, 7, 41, 26, 45, 32, 42, 31, 5, 47, 11, 6, 35, 51, 14, 18, 56, 21, 58, 23, 17, 19, 20, 25, 33, 27, 40, 44, 4, 49], 'cur_cost': 62626.0, 'intermediate_solutions': [{'tour': [59, 13, 58, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 55, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54869.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 23, 37, 30, 39, 36, 2, 21, 17, 20, 54, 50, 51, 24, 44, 0, 5, 47, 38, 28, 16], 'cur_cost': 60376.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 12, 19, 57, 56, 58, 18, 15, 9, 14, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 58152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,777 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 62626.00)
2025-08-05 10:29:05,777 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:05,777 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:05,777 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:05,777 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 95925.0
2025-08-05 10:29:05,786 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:29:05,786 - ExploitationExpert - INFO - res_population_costs: [9620, 9621.0, 9620, 9620, 9620, 9620]
2025-08-05 10:29:05,786 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:05,788 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:05,789 - ExploitationExpert - INFO - populations: [{'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 59889.0}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58730.0}, {'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10513.0}, {'tour': array([ 3, 22, 57, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11], dtype=int64), 'cur_cost': 100172.0}, {'tour': [43, 46, 10, 2, 30, 1, 29, 28, 9, 34, 0, 36, 24, 37, 38, 39, 59, 55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54, 3, 8, 7, 41, 26, 45, 32, 42, 31, 5, 47, 11, 6, 35, 51, 14, 18, 56, 21, 58, 23, 17, 19, 20, 25, 33, 27, 40, 44, 4, 49], 'cur_cost': 62626.0}, {'tour': array([ 3, 31,  5, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52], dtype=int64), 'cur_cost': 95925.0}, {'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 91471.0}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86041.0}, {'tour': [25, 34, 24, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56076.0}, {'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10200.0}]
2025-08-05 10:29:05,790 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:05,790 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 303, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 303, 'cache_hits': 0, 'similarity_calculations': 1518, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:05,790 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 3, 31,  5, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52], dtype=int64), 'cur_cost': 95925.0, 'intermediate_solutions': [{'tour': array([44, 29, 26, 25, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 93937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 44, 29, 26, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 25, 44, 29, 26, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 96402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26, 25, 44, 29, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 16, 25, 44, 29, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:05,791 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 95925.00)
2025-08-05 10:29:05,791 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:05,791 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:05,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,794 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14100.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,796 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14100.0, 'intermediate_solutions': [{'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 21, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 49, 28, 23], 'cur_cost': 91867.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 23, 28, 21, 27, 26, 52, 53, 50, 13, 20, 16, 24, 3, 34, 48, 11, 14, 39], 'cur_cost': 93381.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 8, 6, 5, 1, 17, 19, 7, 4, 18, 41, 32, 56, 15, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 93473.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,796 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 14100.00)
2025-08-05 10:29:05,796 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:05,796 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:05,796 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,799 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,800 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14146.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,800 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14146.0, 'intermediate_solutions': [{'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 16, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 33, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 91925.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 11, 54, 24, 42, 13, 17, 57, 2, 21, 15, 16, 3, 48, 0, 14, 37, 26, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86426.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 44, 39, 46, 26, 43, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,800 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 14146.00)
2025-08-05 10:29:05,801 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:05,801 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:05,801 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,803 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:05,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10099.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,804 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10099.0, 'intermediate_solutions': [{'tour': [25, 34, 5, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 24, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 60978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 45, 0, 46, 30, 41, 32, 24, 34, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 60247.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 34, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 24, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56144.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,804 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10099.00)
2025-08-05 10:29:05,804 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:05,804 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:05,804 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:05,810 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:05,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:05,812 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51902.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:05,812 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51902.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 37, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 31, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 18800.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14529.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 6, 4, 10, 12, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:05,812 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 51902.00)
2025-08-05 10:29:05,812 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:05,812 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:05,814 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 59889.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 34, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 1, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 17302.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 9, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 14, 21, 18], 'cur_cost': 14364.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 9, 59, 3, 2, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 16841.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58730.0, 'intermediate_solutions': [{'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 54, 51, 59, 52, 57, 55, 56, 49, 58, 44, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 18639.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 20, 16, 23, 12, 50, 48, 22, 15, 17, 19, 21, 18, 13], 'cur_cost': 16192.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 9, 6, 4, 5, 1, 10, 11, 2, 7, 19, 8, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 21, 18, 13], 'cur_cost': 18041.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10513.0, 'intermediate_solutions': [{'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 32, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 54, 26, 31], 'cur_cost': 23562.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 17126.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 0, 12, 8, 20, 16, 23, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 16680.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 22, 57, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11], dtype=int64), 'cur_cost': 100172.0, 'intermediate_solutions': [{'tour': array([31, 11, 34, 37, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103380.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37, 31, 11, 34, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 101006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 37, 31, 11, 34, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 37, 31, 11, 24, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 24, 37, 31, 11, 33, 46, 47, 52, 43, 12, 26, 59, 57,  2, 35, 48,
       36, 30, 44,  7, 28, 18, 10, 45, 58, 55, 19, 21,  6, 29, 51,  9, 16,
       53,  3,  5, 14, 32, 20, 13,  1, 23, 38,  4, 50, 40,  0, 25, 17, 42,
       41, 56, 15,  8, 22, 54, 27, 39, 49]), 'cur_cost': 103497.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [43, 46, 10, 2, 30, 1, 29, 28, 9, 34, 0, 36, 24, 37, 38, 39, 59, 55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54, 3, 8, 7, 41, 26, 45, 32, 42, 31, 5, 47, 11, 6, 35, 51, 14, 18, 56, 21, 58, 23, 17, 19, 20, 25, 33, 27, 40, 44, 4, 49], 'cur_cost': 62626.0, 'intermediate_solutions': [{'tour': [59, 13, 58, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 55, 18, 15, 9, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 54869.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 14, 12, 19, 57, 56, 58, 18, 15, 9, 35, 26, 23, 37, 30, 39, 36, 2, 21, 17, 20, 54, 50, 51, 24, 44, 0, 5, 47, 38, 28, 16], 'cur_cost': 60376.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 13, 55, 53, 7, 34, 31, 11, 29, 33, 45, 41, 40, 42, 6, 3, 4, 46, 10, 27, 43, 8, 32, 1, 25, 48, 49, 52, 22, 12, 19, 57, 56, 58, 18, 15, 9, 14, 35, 26, 28, 38, 47, 5, 0, 44, 24, 51, 50, 54, 20, 17, 21, 2, 36, 39, 30, 37, 23, 16], 'cur_cost': 58152.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 31,  5, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52], dtype=int64), 'cur_cost': 95925.0, 'intermediate_solutions': [{'tour': array([44, 29, 26, 25, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 93937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 44, 29, 26, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 25, 44, 29, 26, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 96402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26, 25, 44, 29, 16, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 16, 25, 44, 29, 30,  2, 52, 59, 35, 10, 54, 23, 14, 28, 40, 51,
       37, 47, 17, 38, 15,  0, 18, 32, 34, 22, 48, 19, 12, 50, 27,  6, 36,
       41, 55, 20,  8, 58,  1, 49, 42, 11, 24, 57,  4, 13,  7, 56, 43,  9,
       46,  3,  5, 21, 33, 31, 53, 45, 39]), 'cur_cost': 98097.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14100.0, 'intermediate_solutions': [{'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 21, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 49, 28, 23], 'cur_cost': 91867.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 8, 6, 5, 1, 15, 17, 19, 7, 4, 18, 41, 32, 56, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 23, 28, 21, 27, 26, 52, 53, 50, 13, 20, 16, 24, 3, 34, 48, 11, 14, 39], 'cur_cost': 93381.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 8, 6, 5, 1, 17, 19, 7, 4, 18, 41, 32, 56, 15, 40, 35, 57, 42, 29, 45, 36, 30, 49, 9, 38, 2, 47, 33, 10, 46, 25, 43, 31, 59, 51, 37, 0, 44, 54, 55, 22, 58, 39, 14, 11, 48, 34, 3, 24, 16, 20, 13, 50, 53, 52, 26, 27, 21, 28, 23], 'cur_cost': 93473.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14146.0, 'intermediate_solutions': [{'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 16, 35, 25, 58, 43, 44, 39, 46, 26, 37, 14, 0, 48, 3, 33, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 91925.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 43, 44, 39, 46, 11, 54, 24, 42, 13, 17, 57, 2, 21, 15, 16, 3, 48, 0, 14, 37, 26, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86426.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 12, 20, 23, 1, 8, 10, 19, 7, 4, 5, 41, 32, 56, 40, 53, 28, 52, 29, 45, 50, 30, 34, 36, 38, 27, 47, 33, 35, 25, 58, 44, 39, 46, 26, 43, 37, 14, 0, 48, 3, 16, 15, 21, 2, 57, 17, 13, 42, 24, 54, 11, 51, 49, 6, 55, 22, 31, 9, 18], 'cur_cost': 86000.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10099.0, 'intermediate_solutions': [{'tour': [25, 34, 5, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 24, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 60978.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 45, 0, 46, 30, 41, 32, 24, 34, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 60247.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 34, 32, 41, 30, 46, 0, 45, 36, 37, 3, 9, 52, 49, 54, 16, 18, 53, 12, 57, 58, 21, 23, 10, 5, 8, 27, 1, 33, 11, 28, 39, 2, 48, 22, 14, 50, 59, 20, 19, 17, 55, 13, 31, 29, 43, 42, 44, 4, 47, 7, 24, 26, 6, 51, 15, 56, 40, 38, 35], 'cur_cost': 56144.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51902.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 37, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 31, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 18800.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 1, 5, 6, 4, 10, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14529.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 6, 4, 10, 12, 11, 2, 3, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:05,814 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:05,815 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:05,818 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10099.000, 多样性=0.960
2025-08-05 10:29:05,818 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:05,818 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:05,819 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:05,819 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.053730930504999885, 'best_improvement': 0.009901960784313725}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.011068702290076197}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.047449996015276774, 'recent_improvements': [0.018262874788902355, -0.043616458675663945, 0.11316286681945592], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9620, 'new_best_cost': 9620, 'quality_improvement': 0.0, 'old_diversity': 0.6611111111111111, 'new_diversity': 0.6611111111111111, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:05,820 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:05,820 - __main__ - INFO - composite12_60 开始进化第 3 代
2025-08-05 10:29:05,820 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:05,820 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:05,821 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10099.000, 多样性=0.960
2025-08-05 10:29:05,821 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:05,824 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.960
2025-08-05 10:29:05,824 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:05,826 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.661
2025-08-05 10:29:05,828 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:05,828 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:05,829 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:29:05,829 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:29:05,870 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.312, 适应度梯度: -9767.700, 聚类评分: 0.000, 覆盖率: 0.136, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:05,870 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:05,870 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:05,870 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 10:29:05,877 - visualization.landscape_visualizer - INFO - 插值约束: 63 个点被约束到最小值 9620.00
2025-08-05 10:29:05,879 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.8%, 梯度: 4087.98 → 3726.87
2025-08-05 10:29:05,988 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_118_20250805_102905.html
2025-08-05 10:29:06,063 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_118_20250805_102905.html
2025-08-05 10:29:06,063 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 118
2025-08-05 10:29:06,064 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:06,064 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2363秒
2025-08-05 10:29:06,064 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3125, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -9767.7, 'local_optima_density': 0.3125, 'gradient_variance': 1049147844.3300002, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1361, 'fitness_entropy': 0.7508072359050909, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9767.700)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.136)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360945.8701973, 'performance_metrics': {}}}
2025-08-05 10:29:06,064 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:06,064 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:06,064 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:06,064 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:06,065 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,065 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:06,065 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,065 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,065 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:06,065 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,065 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,066 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:06,066 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:06,066 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:06,066 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:06,066 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,074 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,075 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59756.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,076 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59756.0, 'intermediate_solutions': [{'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 28, 44, 30, 25, 45, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 64073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 59, 40, 36, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 60241.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 36, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 62495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,076 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 59756.00)
2025-08-05 10:29:06,077 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:06,077 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:06,077 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,085 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,086 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53530.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,087 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53530.0, 'intermediate_solutions': [{'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 20, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 19], 'cur_cost': 58658.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 54, 15, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 31, 9, 32, 43, 2, 24, 47, 3, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58806.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,087 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 53530.00)
2025-08-05 10:29:06,087 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:06,087 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:06,087 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,096 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52997.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,098 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52997.0, 'intermediate_solutions': [{'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 42, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 19, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 23139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 21, 16, 23, 1, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12, 13, 18, 14, 19, 17, 15, 22, 20, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14565.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 34, 27, 29, 33, 24, 30, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10567.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,099 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 52997.00)
2025-08-05 10:29:06,099 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:06,100 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,100 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,101 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 99592.0
2025-08-05 10:29:06,113 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:06,114 - ExploitationExpert - INFO - res_population_costs: [9620, 9620, 9620, 9620, 9620, 9621.0, 9620]
2025-08-05 10:29:06,114 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:06,117 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,117 - ExploitationExpert - INFO - populations: [{'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59756.0}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53530.0}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52997.0}, {'tour': array([ 9, 11, 33, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48], dtype=int64), 'cur_cost': 99592.0}, {'tour': [43, 46, 10, 2, 30, 1, 29, 28, 9, 34, 0, 36, 24, 37, 38, 39, 59, 55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54, 3, 8, 7, 41, 26, 45, 32, 42, 31, 5, 47, 11, 6, 35, 51, 14, 18, 56, 21, 58, 23, 17, 19, 20, 25, 33, 27, 40, 44, 4, 49], 'cur_cost': 62626.0}, {'tour': [3, 31, 5, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25, 4, 12, 55, 24, 40, 28, 51, 32, 41, 54, 2, 27, 58, 13, 36, 20, 6, 7, 48, 49, 30, 38, 10, 1, 8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34, 37, 44, 9, 16, 0, 19, 56, 57, 52], 'cur_cost': 95925.0}, {'tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14100.0}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14146.0}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10099.0}, {'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51902.0}]
2025-08-05 10:29:06,118 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,118 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 304, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 304, 'cache_hits': 0, 'similarity_calculations': 1522, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,119 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 9, 11, 33, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48], dtype=int64), 'cur_cost': 99592.0, 'intermediate_solutions': [{'tour': array([57, 22,  3, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 102787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 57, 22,  3, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 102769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([53, 52, 57, 22,  3,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 52, 57, 22, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 53, 52, 57, 22,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,119 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 99592.00)
2025-08-05 10:29:06,120 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:06,120 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,120 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,120 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 95514.0
2025-08-05 10:29:06,129 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:06,129 - ExploitationExpert - INFO - res_population_costs: [9620, 9620, 9620, 9620, 9620, 9621.0, 9620, 9620]
2025-08-05 10:29:06,129 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64)]
2025-08-05 10:29:06,132 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,133 - ExploitationExpert - INFO - populations: [{'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59756.0}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53530.0}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52997.0}, {'tour': array([ 9, 11, 33, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48], dtype=int64), 'cur_cost': 99592.0}, {'tour': array([49, 39, 58, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17], dtype=int64), 'cur_cost': 95514.0}, {'tour': [3, 31, 5, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25, 4, 12, 55, 24, 40, 28, 51, 32, 41, 54, 2, 27, 58, 13, 36, 20, 6, 7, 48, 49, 30, 38, 10, 1, 8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34, 37, 44, 9, 16, 0, 19, 56, 57, 52], 'cur_cost': 95925.0}, {'tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14100.0}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14146.0}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10099.0}, {'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51902.0}]
2025-08-05 10:29:06,135 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,135 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 305, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 305, 'cache_hits': 0, 'similarity_calculations': 1527, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,136 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([49, 39, 58, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17], dtype=int64), 'cur_cost': 95514.0, 'intermediate_solutions': [{'tour': array([10, 46, 43,  2, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 63139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 10, 46, 43, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 62603.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30,  2, 10, 46, 43,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 62990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43,  2, 10, 46, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 64856.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 30,  2, 10, 46,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 64770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,136 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 95514.00)
2025-08-05 10:29:06,137 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:06,137 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 86813.0
2025-08-05 10:29:06,146 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:29:06,146 - ExploitationExpert - INFO - res_population_costs: [9620, 9620, 9620, 9620, 9620, 9621.0, 9620, 9620, 9620, 9614]
2025-08-05 10:29:06,147 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:06,150 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,150 - ExploitationExpert - INFO - populations: [{'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59756.0}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53530.0}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52997.0}, {'tour': array([ 9, 11, 33, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48], dtype=int64), 'cur_cost': 99592.0}, {'tour': array([49, 39, 58, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17], dtype=int64), 'cur_cost': 95514.0}, {'tour': array([16, 26, 33, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12], dtype=int64), 'cur_cost': 86813.0}, {'tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14100.0}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14146.0}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10099.0}, {'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51902.0}]
2025-08-05 10:29:06,152 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:06,152 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 306, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 306, 'cache_hits': 0, 'similarity_calculations': 1533, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,153 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([16, 26, 33, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12], dtype=int64), 'cur_cost': 86813.0, 'intermediate_solutions': [{'tour': array([ 5, 31,  3, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35,  5, 31,  3, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 98360.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 35,  5, 31,  3, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 96283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 35,  5, 31, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 26, 35,  5, 31, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,153 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 86813.00)
2025-08-05 10:29:06,153 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:06,153 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:06,153 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,155 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 10:29:06,155 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,156 - ExplorationExpert - INFO - 探索路径生成完成，成本: 75671.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,156 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75671.0, 'intermediate_solutions': [{'tour': [52, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 0, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 4, 8, 52, 59, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 1, 10, 6, 5, 9, 3, 11, 2, 7, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 16824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 1, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 18186.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,156 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 75671.00)
2025-08-05 10:29:06,157 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:06,157 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:06,157 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,158 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,159 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14343.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,160 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14343.0, 'intermediate_solutions': [{'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 44, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 34, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 16661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 48, 53, 54, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 49, 57, 55, 56, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,160 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 14343.00)
2025-08-05 10:29:06,160 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:06,160 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:06,160 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,165 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,165 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,165 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,166 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52326.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,166 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52326.0, 'intermediate_solutions': [{'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 14, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 29, 21, 18, 13], 'cur_cost': 21120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 13, 18, 21, 14, 19, 17, 15, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 51, 44, 43, 41, 39], 'cur_cost': 12469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 56, 18, 13], 'cur_cost': 12112.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,166 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 52326.00)
2025-08-05 10:29:06,167 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:06,167 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:06,167 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,172 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57915.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,174 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0, 'intermediate_solutions': [{'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 56, 59, 53, 52, 51, 22, 49, 15, 21, 25, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 52920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 39, 6, 0, 10, 13, 14, 20, 16, 19, 18, 55, 4, 11, 33, 35, 34, 41, 7, 8, 3, 58, 48, 12, 17, 56, 21, 15, 49, 22, 51, 52, 53, 59, 25, 40, 5, 27, 2, 46, 44, 50], 'cur_cost': 51907.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [57, 23, 54, 58, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,174 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 57915.00)
2025-08-05 10:29:06,174 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:06,175 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:06,177 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59756.0, 'intermediate_solutions': [{'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 36, 40, 59, 4, 35, 6, 43, 9, 11, 27, 28, 44, 30, 25, 45, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 64073.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 59, 40, 36, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 60241.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 57, 17, 12, 20, 13, 2, 51, 1, 8, 29, 40, 59, 4, 35, 6, 43, 9, 11, 27, 45, 44, 30, 25, 28, 31, 41, 26, 34, 10, 7, 50, 58, 19, 15, 21, 55, 16, 36, 3, 0, 32, 38, 47, 24, 42, 52, 56, 53, 23, 48, 49, 5, 37, 46, 33, 39, 18, 14, 22], 'cur_cost': 62495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53530.0, 'intermediate_solutions': [{'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 20, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 19], 'cur_cost': 58658.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 3, 31, 9, 32, 43, 2, 24, 47, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 54, 15, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 7, 28, 26, 29, 30, 36, 33, 46, 11, 39, 40, 10, 0, 5, 48, 58, 56, 19, 12, 13, 53, 21, 16, 31, 9, 32, 43, 2, 24, 47, 3, 25, 8, 42, 45, 27, 52, 51, 50, 49, 22, 14, 23, 15, 54, 17, 57, 55, 41, 6, 34, 37, 44, 38, 35, 59, 18, 20], 'cur_cost': 58806.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52997.0, 'intermediate_solutions': [{'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 42, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 19, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 23139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 21, 16, 23, 1, 10, 11, 7, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12, 13, 18, 14, 19, 17, 15, 22, 20, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14565.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 16, 23, 20, 22, 15, 17, 19, 14, 18, 13, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 8, 26, 32, 31, 25, 35, 28, 34, 27, 29, 33, 24, 30, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10567.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 11, 33, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48], dtype=int64), 'cur_cost': 99592.0, 'intermediate_solutions': [{'tour': array([57, 22,  3, 52, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 102787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 57, 22,  3, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 102769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([53, 52, 57, 22,  3,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 52, 57, 22, 53,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100560.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 53, 52, 57, 22,  1, 16, 47, 27,  5, 17, 26, 34,  6, 46, 51, 55,
       23, 28, 32, 19,  7, 45, 25, 21,  4, 33, 20, 18, 29, 49, 41, 39, 14,
       37, 38, 13,  0, 54, 35, 36,  2,  9, 50, 15, 43,  8, 56, 59, 48, 44,
       30, 42, 58, 24, 40, 31, 10, 12, 11]), 'cur_cost': 100185.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 39, 58, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17], dtype=int64), 'cur_cost': 95514.0, 'intermediate_solutions': [{'tour': array([10, 46, 43,  2, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 63139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 10, 46, 43, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 62603.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30,  2, 10, 46, 43,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 62990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43,  2, 10, 46, 30,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 64856.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 30,  2, 10, 46,  1, 29, 28,  9, 34,  0, 36, 24, 37, 38, 39, 59,
       55, 50, 16, 57, 52, 12, 53, 13, 22, 48, 15, 54,  3,  8,  7, 41, 26,
       45, 32, 42, 31,  5, 47, 11,  6, 35, 51, 14, 18, 56, 21, 58, 23, 17,
       19, 20, 25, 33, 27, 40, 44,  4, 49]), 'cur_cost': 64770.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 26, 33, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12], dtype=int64), 'cur_cost': 86813.0, 'intermediate_solutions': [{'tour': array([ 5, 31,  3, 35, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35,  5, 31,  3, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 98360.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 35,  5, 31,  3, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 96283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 35,  5, 31, 26, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 26, 35,  5, 31, 23, 21, 53, 15, 46, 22, 50, 42, 29, 25,  4, 12,
       55, 24, 40, 28, 51, 32, 41, 54,  2, 27, 58, 13, 36, 20,  6,  7, 48,
       49, 30, 38, 10,  1,  8, 45, 33, 18, 17, 39, 59, 11, 14, 43, 47, 34,
       37, 44,  9, 16,  0, 19, 56, 57, 52]), 'cur_cost': 95910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75671.0, 'intermediate_solutions': [{'tour': [52, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 0, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 14924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 4, 8, 52, 59, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 1, 10, 6, 5, 9, 3, 11, 2, 7, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 16824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 1, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 18186.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14343.0, 'intermediate_solutions': [{'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 44, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 34, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 16661.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 48, 53, 54, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 20, 11, 2, 7, 10, 1, 6, 4, 5, 9, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 49, 57, 55, 56, 58, 54, 53, 48, 50, 12, 23, 16, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52326.0, 'intermediate_solutions': [{'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 14, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 29, 21, 18, 13], 'cur_cost': 21120.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 13, 18, 21, 14, 19, 17, 15, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 51, 44, 43, 41, 39], 'cur_cost': 12469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 10, 8, 7, 2, 11, 3, 9, 5, 6, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 56, 18, 13], 'cur_cost': 12112.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0, 'intermediate_solutions': [{'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 56, 59, 53, 52, 51, 22, 49, 15, 21, 25, 17, 12, 48, 58, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 52920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [57, 23, 54, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 39, 6, 0, 10, 13, 14, 20, 16, 19, 18, 55, 4, 11, 33, 35, 34, 41, 7, 8, 3, 58, 48, 12, 17, 56, 21, 15, 49, 22, 51, 52, 53, 59, 25, 40, 5, 27, 2, 46, 44, 50], 'cur_cost': 51907.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [57, 23, 54, 58, 9, 31, 32, 29, 43, 47, 37, 1, 30, 45, 28, 26, 38, 24, 36, 42, 46, 2, 27, 5, 40, 25, 59, 53, 52, 51, 22, 49, 15, 21, 56, 17, 12, 48, 3, 8, 7, 41, 34, 35, 33, 11, 4, 55, 18, 19, 16, 20, 14, 13, 10, 0, 6, 39, 44, 50], 'cur_cost': 51829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:06,177 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:06,178 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:06,183 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14343.000, 多样性=0.983
2025-08-05 10:29:06,183 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:06,183 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:06,183 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:06,185 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.25692158872089244, 'best_improvement': -0.4202396276859095}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.024314936318023908}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.048673694590331905, 'recent_improvements': [-0.043616458675663945, 0.11316286681945592, 0.053730930504999885], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.5992592592592593, 'new_diversity': 0.5992592592592593, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:06,186 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:06,186 - __main__ - INFO - composite12_60 开始进化第 4 代
2025-08-05 10:29:06,186 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:06,186 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:06,187 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14343.000, 多样性=0.983
2025-08-05 10:29:06,188 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:06,192 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.983
2025-08-05 10:29:06,192 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:06,198 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.599
2025-08-05 10:29:06,200 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:06,201 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:06,201 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:29:06,201 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:29:06,256 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -13007.830, 聚类评分: 0.000, 覆盖率: 0.137, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:06,256 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:06,256 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:06,256 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 10:29:06,287 - visualization.landscape_visualizer - INFO - 插值约束: 4 个点被约束到最小值 9614.00
2025-08-05 10:29:06,288 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.0%, 梯度: 3921.69 → 3648.30
2025-08-05 10:29:06,412 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_119_20250805_102906.html
2025-08-05 10:29:06,505 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_119_20250805_102906.html
2025-08-05 10:29:06,505 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 119
2025-08-05 10:29:06,505 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:06,505 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3058秒
2025-08-05 10:29:06,505 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13007.83, 'local_optima_density': 0.15, 'gradient_variance': 602824248.0890999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1371, 'fitness_entropy': 0.7665474176031084, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13007.830)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.137)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360946.2561233, 'performance_metrics': {}}}
2025-08-05 10:29:06,506 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:06,506 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:06,506 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:06,506 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:06,506 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,506 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:06,507 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,507 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:06,507 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:06,508 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:06,508 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,510 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10639.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,511 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10639.0, 'intermediate_solutions': [{'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 44, 51, 20, 14, 49, 13, 25, 37, 40, 45, 7, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59656.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 12, 58, 52, 50, 46, 27, 29, 44, 45, 40, 37, 25, 13, 49, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59778.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 9, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 57562.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,512 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 10639.00)
2025-08-05 10:29:06,512 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:06,512 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:06,512 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,513 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 60
2025-08-05 10:29:06,513 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,513 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 75051.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,514 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75051.0, 'intermediate_solutions': [{'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 33, 59, 18, 14, 48, 58, 9, 50, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 57636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 22, 49, 57, 53, 24, 44, 46, 42, 1, 36, 26, 7, 6, 4, 10, 17, 12, 16, 56, 13, 52, 39, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53487.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 56, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 51571.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,514 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 75051.00)
2025-08-05 10:29:06,514 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:06,514 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:06,515 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,516 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,516 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,516 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,517 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,517 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,517 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10439.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,517 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10439.0, 'intermediate_solutions': [{'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 30, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 29, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 53028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 50, 56, 17, 49, 20, 57, 2, 43, 45, 42, 44, 46, 33, 6, 53, 21, 18, 14, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 21, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 53011.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,517 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 10439.00)
2025-08-05 10:29:06,517 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:06,518 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,518 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,518 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 96445.0
2025-08-05 10:29:06,527 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:29:06,527 - ExploitationExpert - INFO - res_population_costs: [9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0, 9614]
2025-08-05 10:29:06,527 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:06,531 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,531 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10639.0}, {'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75051.0}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10439.0}, {'tour': array([47, 50, 40, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5], dtype=int64), 'cur_cost': 96445.0}, {'tour': [49, 39, 58, 52, 47, 42, 37, 18, 8, 21, 9, 40, 2, 5, 55, 44, 4, 43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11, 46, 57, 25, 19, 36, 10, 56, 12, 22, 3, 6, 0, 34, 31, 38, 14, 1, 54, 29, 41, 7, 48, 16, 53, 20, 17], 'cur_cost': 95514.0}, {'tour': [16, 26, 33, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52, 8, 7, 2, 58, 22, 11, 34, 3, 10, 39, 27, 53, 29, 14, 38, 41, 23, 9, 0, 15, 28, 57, 32, 43, 5, 6, 1, 44, 13, 19, 30, 31, 40, 18, 50, 37, 21, 4, 25, 35, 48, 49, 12], 'cur_cost': 86813.0}, {'tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75671.0}, {'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14343.0}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52326.0}, {'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0}]
2025-08-05 10:29:06,532 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:06,532 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 307, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 307, 'cache_hits': 0, 'similarity_calculations': 1540, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,533 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([47, 50, 40, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5], dtype=int64), 'cur_cost': 96445.0, 'intermediate_solutions': [{'tour': array([33, 11,  9, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 99956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 33, 11,  9, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 99645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([56, 16, 33, 11,  9, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 97486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 16, 33, 11, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 101859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 56, 16, 33, 11, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 100030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,534 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 96445.00)
2025-08-05 10:29:06,534 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:06,534 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,534 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,534 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 101855.0
2025-08-05 10:29:06,542 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:29:06,542 - ExploitationExpert - INFO - res_population_costs: [9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0, 9614]
2025-08-05 10:29:06,542 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:06,546 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,547 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10639.0}, {'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75051.0}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10439.0}, {'tour': array([47, 50, 40, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5], dtype=int64), 'cur_cost': 96445.0}, {'tour': array([ 6, 36, 32, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4], dtype=int64), 'cur_cost': 101855.0}, {'tour': [16, 26, 33, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52, 8, 7, 2, 58, 22, 11, 34, 3, 10, 39, 27, 53, 29, 14, 38, 41, 23, 9, 0, 15, 28, 57, 32, 43, 5, 6, 1, 44, 13, 19, 30, 31, 40, 18, 50, 37, 21, 4, 25, 35, 48, 49, 12], 'cur_cost': 86813.0}, {'tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75671.0}, {'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14343.0}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52326.0}, {'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0}]
2025-08-05 10:29:06,548 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:06,548 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 308, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 308, 'cache_hits': 0, 'similarity_calculations': 1548, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,549 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 6, 36, 32, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4], dtype=int64), 'cur_cost': 101855.0, 'intermediate_solutions': [{'tour': array([58, 39, 49, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95517.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 58, 39, 49, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 52, 58, 39, 49, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 99878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 52, 58, 39, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 91196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 47, 52, 58, 39, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,549 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 101855.00)
2025-08-05 10:29:06,549 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:06,549 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,549 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,549 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 98683.0
2025-08-05 10:29:06,562 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:29:06,562 - ExploitationExpert - INFO - res_population_costs: [9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0, 9614]
2025-08-05 10:29:06,562 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-08-05 10:29:06,566 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,566 - ExploitationExpert - INFO - populations: [{'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10639.0}, {'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75051.0}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10439.0}, {'tour': array([47, 50, 40, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5], dtype=int64), 'cur_cost': 96445.0}, {'tour': array([ 6, 36, 32, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4], dtype=int64), 'cur_cost': 101855.0}, {'tour': array([28, 26, 25, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10], dtype=int64), 'cur_cost': 98683.0}, {'tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75671.0}, {'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 14343.0}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52326.0}, {'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0}]
2025-08-05 10:29:06,568 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,568 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 309, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 309, 'cache_hits': 0, 'similarity_calculations': 1557, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,569 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([28, 26, 25, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10], dtype=int64), 'cur_cost': 98683.0, 'intermediate_solutions': [{'tour': array([33, 26, 16, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 88191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 33, 26, 16, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 88874.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 51, 33, 26, 16, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 93076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 51, 33, 26, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 85117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 42, 51, 33, 26, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 89285.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,569 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 98683.00)
2025-08-05 10:29:06,570 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:06,570 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:06,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,572 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13956.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,574 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 13956.0, 'intermediate_solutions': [{'tour': [11, 30, 43, 38, 26, 31, 4, 27, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 35, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 49, 39, 59, 3, 1, 8, 24, 10, 44, 23, 5, 58, 7, 18, 28, 27, 29, 19, 15, 16, 37, 25, 46, 20, 14, 2, 45, 34, 36, 41, 6, 13, 57, 40, 22, 51], 'cur_cost': 75678.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 30, 43, 38, 14, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 79419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,574 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 13956.00)
2025-08-05 10:29:06,574 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:06,574 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:06,574 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,580 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,580 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,580 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,581 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49537.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,581 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 49537.0, 'intermediate_solutions': [{'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 47, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 12, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 25027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 42, 47, 37, 40, 46, 39, 41, 5, 4, 6, 1, 10, 8, 7, 11, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 18827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 2, 19, 14, 9, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 18151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,582 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 49537.00)
2025-08-05 10:29:06,582 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:06,582 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:06,582 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,584 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,584 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,585 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14065.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,586 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14065.0, 'intermediate_solutions': [{'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 57, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 52, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 38, 41, 9, 28, 30, 5, 0, 54, 21, 16, 20, 59, 22, 15, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 54076.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 47, 26, 37, 35, 49], 'cur_cost': 54579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,586 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 14065.00)
2025-08-05 10:29:06,586 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:06,586 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:06,586 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,594 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,594 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,594 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,596 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52295.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,596 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 52295.0, 'intermediate_solutions': [{'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 27, 30, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 28, 33, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 55, 16, 48, 54, 51, 58, 3, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 60077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,597 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 52295.00)
2025-08-05 10:29:06,597 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:06,597 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:06,599 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10639.0, 'intermediate_solutions': [{'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 44, 51, 20, 14, 49, 13, 25, 37, 40, 45, 7, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59656.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 9, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 7, 51, 20, 14, 12, 58, 52, 50, 46, 27, 29, 44, 45, 40, 37, 25, 13, 49, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 59778.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [57, 59, 3, 11, 5, 26, 31, 1, 30, 33, 32, 10, 24, 47, 42, 2, 56, 55, 21, 54, 22, 17, 53, 48, 16, 4, 35, 6, 41, 0, 36, 34, 38, 9, 7, 51, 20, 14, 49, 13, 25, 37, 40, 45, 44, 29, 27, 46, 50, 52, 58, 12, 23, 19, 18, 15, 8, 28, 43, 39], 'cur_cost': 57562.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75051.0, 'intermediate_solutions': [{'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 33, 59, 18, 14, 48, 58, 9, 50, 5, 39, 52, 13, 56, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 57636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 22, 49, 57, 53, 24, 44, 46, 42, 1, 36, 26, 7, 6, 4, 10, 17, 12, 16, 56, 13, 52, 39, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 53487.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 30, 43, 38, 45, 2, 32, 41, 8, 35, 11, 3, 51, 21, 50, 59, 18, 14, 48, 58, 9, 33, 5, 39, 52, 13, 16, 12, 17, 10, 4, 6, 7, 26, 36, 1, 42, 46, 44, 24, 56, 53, 57, 49, 22, 0, 28, 34, 37, 47, 40, 54, 55, 19, 20, 23, 15, 25, 27], 'cur_cost': 51571.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10439.0, 'intermediate_solutions': [{'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 30, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 21, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 19, 10, 32, 25, 29, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 53028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 50, 56, 17, 49, 20, 57, 2, 43, 45, 42, 44, 46, 33, 6, 53, 21, 18, 14, 15, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 52947.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 59, 3, 1, 8, 41, 39, 4, 35, 27, 29, 31, 0, 28, 26, 40, 5, 34, 48, 55, 13, 12, 51, 16, 23, 14, 18, 53, 6, 33, 46, 44, 42, 45, 43, 2, 57, 20, 49, 17, 56, 50, 15, 21, 19, 10, 32, 25, 30, 7, 54, 58, 9, 36, 38, 37, 24, 47, 52, 22], 'cur_cost': 53011.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 50, 40, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5], dtype=int64), 'cur_cost': 96445.0, 'intermediate_solutions': [{'tour': array([33, 11,  9, 16, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 99956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 33, 11,  9, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 99645.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([56, 16, 33, 11,  9, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 97486.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 16, 33, 11, 56, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 101859.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 56, 16, 33, 11, 41,  1, 31,  3, 47, 42, 23, 13, 39,  6, 14, 57,
       30, 28, 55,  2,  5, 36, 59, 54, 40,  0, 49,  4, 53, 52, 43, 29, 22,
        7, 44, 46,  8, 10, 19, 18, 26, 51, 35, 27, 20, 58, 24, 45, 34, 12,
       15, 32, 21, 38, 17, 25, 50, 37, 48]), 'cur_cost': 100030.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 36, 32, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4], dtype=int64), 'cur_cost': 101855.0, 'intermediate_solutions': [{'tour': array([58, 39, 49, 52, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95517.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 58, 39, 49, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95593.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([47, 52, 58, 39, 49, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 99878.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 52, 58, 39, 47, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 91196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 47, 52, 58, 39, 42, 37, 18,  8, 21,  9, 40,  2,  5, 55, 44,  4,
       43, 59, 28, 35, 30, 15, 27, 50, 32, 45, 26, 24, 13, 51, 33, 23, 11,
       46, 57, 25, 19, 36, 10, 56, 12, 22,  3,  6,  0, 34, 31, 38, 14,  1,
       54, 29, 41,  7, 48, 16, 53, 20, 17]), 'cur_cost': 95618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 26, 25, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10], dtype=int64), 'cur_cost': 98683.0, 'intermediate_solutions': [{'tour': array([33, 26, 16, 51, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 88191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 33, 26, 16, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 88874.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([42, 51, 33, 26, 16, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 93076.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 51, 33, 26, 42, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 85117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 42, 51, 33, 26, 36, 46, 47, 17, 20, 56, 24, 45, 59, 55, 54, 52,
        8,  7,  2, 58, 22, 11, 34,  3, 10, 39, 27, 53, 29, 14, 38, 41, 23,
        9,  0, 15, 28, 57, 32, 43,  5,  6,  1, 44, 13, 19, 30, 31, 40, 18,
       50, 37, 21,  4, 25, 35, 48, 49, 12]), 'cur_cost': 89285.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 13956.0, 'intermediate_solutions': [{'tour': [11, 30, 43, 38, 26, 31, 4, 27, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 14, 20, 46, 25, 37, 16, 15, 19, 29, 35, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 75709.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 30, 43, 38, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 49, 39, 59, 3, 1, 8, 24, 10, 44, 23, 5, 58, 7, 18, 28, 27, 29, 19, 15, 16, 37, 25, 46, 20, 14, 2, 45, 34, 36, 41, 6, 13, 57, 40, 22, 51], 'cur_cost': 75678.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 30, 43, 38, 14, 26, 31, 4, 35, 33, 32, 0, 47, 42, 50, 56, 55, 21, 9, 54, 12, 17, 53, 48, 52, 13, 6, 41, 36, 34, 45, 2, 20, 46, 25, 37, 16, 15, 19, 29, 27, 28, 18, 7, 58, 5, 23, 44, 10, 24, 8, 1, 3, 59, 39, 49, 57, 40, 22, 51], 'cur_cost': 79419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 49537.0, 'intermediate_solutions': [{'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 47, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 12, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 25027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 2, 19, 14, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 42, 47, 37, 40, 46, 39, 41, 5, 4, 6, 1, 10, 8, 7, 11, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 18827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 2, 19, 14, 9, 21, 18, 13, 15, 22, 20, 16, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 3, 11, 7, 8, 10, 1, 6, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 18151.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14065.0, 'intermediate_solutions': [{'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 57, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 52, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 52258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 38, 41, 9, 28, 30, 5, 0, 54, 21, 16, 20, 59, 22, 15, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 26, 47, 37, 35, 49], 'cur_cost': 54076.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 2, 6, 29, 3, 8, 11, 4, 42, 45, 46, 43, 34, 27, 10, 48, 56, 52, 23, 53, 15, 22, 59, 20, 16, 21, 54, 0, 5, 30, 28, 9, 41, 38, 7, 32, 33, 24, 44, 31, 39, 36, 25, 40, 57, 51, 50, 55, 18, 14, 12, 17, 13, 58, 1, 47, 26, 37, 35, 49], 'cur_cost': 54579.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 52295.0, 'intermediate_solutions': [{'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 27, 30, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [59, 55, 16, 48, 54, 51, 58, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 3, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 28, 33, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 57899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [59, 55, 16, 48, 54, 51, 58, 3, 57, 9, 41, 42, 5, 4, 31, 10, 0, 6, 25, 11, 34, 43, 56, 17, 12, 14, 19, 52, 20, 13, 32, 2, 26, 45, 44, 8, 33, 28, 36, 40, 30, 27, 39, 7, 1, 38, 50, 49, 22, 18, 21, 15, 35, 46, 29, 24, 47, 37, 53, 23], 'cur_cost': 60077.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:06,600 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:06,600 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:06,603 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10439.000, 多样性=0.945
2025-08-05 10:29:06,603 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:06,603 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:06,603 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:06,605 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.1476632756608843, 'best_improvement': 0.2721885240186851}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.038432554634514074}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.1850422277701742, 'recent_improvements': [0.11316286681945592, 0.053730930504999885, -0.25692158872089244], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.5672727272727273, 'new_diversity': 0.5672727272727273, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:06,606 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:06,606 - __main__ - INFO - composite12_60 开始进化第 5 代
2025-08-05 10:29:06,606 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:06,607 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:06,607 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10439.000, 多样性=0.945
2025-08-05 10:29:06,608 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:06,610 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.945
2025-08-05 10:29:06,610 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:06,615 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.567
2025-08-05 10:29:06,617 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:06,617 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:06,617 - LandscapeExpert - INFO - 添加精英解数据: 11个精英解
2025-08-05 10:29:06,617 - LandscapeExpert - INFO - 数据提取成功: 21个路径, 21个适应度值
2025-08-05 10:29:06,694 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.190, 适应度梯度: -10453.590, 聚类评分: 0.000, 覆盖率: 0.138, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:06,694 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:06,694 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:06,694 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite12_60
2025-08-05 10:29:06,701 - visualization.landscape_visualizer - INFO - 插值约束: 93 个点被约束到最小值 9614.00
2025-08-05 10:29:06,702 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 3739.21 → 3468.16
2025-08-05 10:29:06,850 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\landscape_composite12_60_iter_120_20250805_102906.html
2025-08-05 10:29:06,923 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite12_60\dashboard_composite12_60_iter_120_20250805_102906.html
2025-08-05 10:29:06,923 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 120
2025-08-05 10:29:06,923 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:06,923 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3067秒
2025-08-05 10:29:06,924 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.19047619047619047, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -10453.590476190475, 'local_optima_density': 0.19047619047619047, 'gradient_variance': 890323432.7751474, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1381, 'fitness_entropy': 0.640009986715779, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -10453.590)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.138)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360946.6940768, 'performance_metrics': {}}}
2025-08-05 10:29:06,924 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:06,924 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:06,924 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:06,924 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:06,925 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,925 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:06,925 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,925 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,925 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:06,925 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:29:06,925 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:06,926 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:06,926 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:06,926 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:06,926 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:06,926 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,928 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,928 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,929 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14145.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,929 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 15, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14145.0, 'intermediate_solutions': [{'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 8, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 50, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 5, 4, 39, 41, 43, 44], 'cur_cost': 13207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 4, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13377.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,929 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 14145.00)
2025-08-05 10:29:06,929 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:06,930 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:06,930 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,931 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,931 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,932 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10607.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,932 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 6, 20, 16, 23, 12, 14, 21, 18, 19, 15, 17, 22, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10607.0, 'intermediate_solutions': [{'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 20, 42, 5, 26, 36, 15, 6, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 79431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 18, 43, 47, 37, 45, 34, 30, 22, 44, 7, 27, 24, 35, 25, 20, 15, 36, 26, 5, 42, 6, 1, 10, 11, 4, 56, 13, 52, 48, 49, 33, 9, 51, 12, 14, 57, 59, 40, 21, 28, 3, 17, 19, 8, 41, 32, 2, 23, 38, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 21, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 71358.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,933 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 10607.00)
2025-08-05 10:29:06,933 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:06,933 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:06,933 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,938 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 60
2025-08-05 10:29:06,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:06,940 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53640.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:06,940 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 0, 24, 4, 40, 43, 32, 10, 11, 29, 5, 26, 38, 46, 30, 2, 33, 1, 8, 27, 3, 53, 57, 51, 52, 19, 54, 23, 48, 22, 56, 49, 14, 17, 18, 20, 50, 55, 58, 41, 9, 31, 34, 42, 39, 44, 47, 37, 45, 7, 28, 25, 13, 21, 15, 12, 59, 16, 35, 36], 'cur_cost': 53640.0, 'intermediate_solutions': [{'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 5, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 55, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 1, 10, 11, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12, 23, 16, 20, 22, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 16, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 16100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:06,940 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 53640.00)
2025-08-05 10:29:06,940 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:29:06,940 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,940 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,941 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 91026.0
2025-08-05 10:29:06,954 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 10:29:06,954 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0]
2025-08-05 10:29:06,955 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-08-05 10:29:06,959 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,959 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 15, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14145.0}, {'tour': [0, 8, 6, 20, 16, 23, 12, 14, 21, 18, 19, 15, 17, 22, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10607.0}, {'tour': [6, 0, 24, 4, 40, 43, 32, 10, 11, 29, 5, 26, 38, 46, 30, 2, 33, 1, 8, 27, 3, 53, 57, 51, 52, 19, 54, 23, 48, 22, 56, 49, 14, 17, 18, 20, 50, 55, 58, 41, 9, 31, 34, 42, 39, 44, 47, 37, 45, 7, 28, 25, 13, 21, 15, 12, 59, 16, 35, 36], 'cur_cost': 53640.0}, {'tour': array([36, 42, 25, 40, 56, 53, 43, 50,  8, 57, 20,  1,  9, 52, 26, 48, 41,
       30, 38, 46, 37, 33, 35,  6, 31,  2, 59, 49,  3, 23,  5, 44, 34, 21,
       51, 14, 39, 13, 11,  0,  7, 55,  4, 54, 47, 45, 17, 28, 10, 15, 16,
       18, 29, 24, 58, 32, 12, 19, 22, 27], dtype=int64), 'cur_cost': 91026.0}, {'tour': [6, 36, 32, 59, 24, 8, 38, 13, 48, 19, 16, 52, 25, 3, 17, 33, 18, 5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21, 30, 7, 43, 50, 2, 12, 55, 40, 58, 45, 1, 49, 9, 22, 44, 39, 34, 10, 26, 41, 37, 42, 27, 0, 57, 4], 'cur_cost': 101855.0}, {'tour': [28, 26, 25, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35, 8, 41, 49, 48, 45, 24, 59, 1, 14, 7, 3, 30, 5, 47, 34, 21, 51, 53, 11, 55, 20, 0, 54, 57, 42, 4, 9, 52, 2, 13, 29, 23, 6, 27, 17, 39, 36, 38, 31, 46, 19, 33, 10], 'cur_cost': 98683.0}, {'tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 13956.0}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 49537.0}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14065.0}, {'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 52295.0}]
2025-08-05 10:29:06,960 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,960 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 310, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 310, 'cache_hits': 0, 'similarity_calculations': 1567, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,961 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 42, 25, 40, 56, 53, 43, 50,  8, 57, 20,  1,  9, 52, 26, 48, 41,
       30, 38, 46, 37, 33, 35,  6, 31,  2, 59, 49,  3, 23,  5, 44, 34, 21,
       51, 14, 39, 13, 11,  0,  7, 55,  4, 54, 47, 45, 17, 28, 10, 15, 16,
       18, 29, 24, 58, 32, 12, 19, 22, 27], dtype=int64), 'cur_cost': 91026.0, 'intermediate_solutions': [{'tour': array([40, 50, 47, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 40, 50, 47, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([56, 24, 40, 50, 47, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 94238.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 24, 40, 50, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 93906.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 56, 24, 40, 50, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,961 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 91026.00)
2025-08-05 10:29:06,961 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:06,961 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,961 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,961 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 97167.0
2025-08-05 10:29:06,971 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:29:06,971 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0, 9614]
2025-08-05 10:29:06,971 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64)]
2025-08-05 10:29:06,976 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,976 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 15, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14145.0}, {'tour': [0, 8, 6, 20, 16, 23, 12, 14, 21, 18, 19, 15, 17, 22, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10607.0}, {'tour': [6, 0, 24, 4, 40, 43, 32, 10, 11, 29, 5, 26, 38, 46, 30, 2, 33, 1, 8, 27, 3, 53, 57, 51, 52, 19, 54, 23, 48, 22, 56, 49, 14, 17, 18, 20, 50, 55, 58, 41, 9, 31, 34, 42, 39, 44, 47, 37, 45, 7, 28, 25, 13, 21, 15, 12, 59, 16, 35, 36], 'cur_cost': 53640.0}, {'tour': array([36, 42, 25, 40, 56, 53, 43, 50,  8, 57, 20,  1,  9, 52, 26, 48, 41,
       30, 38, 46, 37, 33, 35,  6, 31,  2, 59, 49,  3, 23,  5, 44, 34, 21,
       51, 14, 39, 13, 11,  0,  7, 55,  4, 54, 47, 45, 17, 28, 10, 15, 16,
       18, 29, 24, 58, 32, 12, 19, 22, 27], dtype=int64), 'cur_cost': 91026.0}, {'tour': array([22, 58, 54, 39, 41, 24, 13,  9,  7, 16, 42, 51,  1, 17,  2, 15, 11,
       32, 40, 50,  6, 18,  4, 59,  0, 27, 37, 36, 21, 30, 26, 44, 31, 49,
       38, 28, 34, 14, 10, 57, 20, 12, 29, 33,  8, 19, 35, 47, 23,  5, 46,
       43,  3, 56, 53, 45, 25, 55, 48, 52], dtype=int64), 'cur_cost': 97167.0}, {'tour': [28, 26, 25, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35, 8, 41, 49, 48, 45, 24, 59, 1, 14, 7, 3, 30, 5, 47, 34, 21, 51, 53, 11, 55, 20, 0, 54, 57, 42, 4, 9, 52, 2, 13, 29, 23, 6, 27, 17, 39, 36, 38, 31, 46, 19, 33, 10], 'cur_cost': 98683.0}, {'tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 13956.0}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 49537.0}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14065.0}, {'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 52295.0}]
2025-08-05 10:29:06,978 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,978 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 311, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 311, 'cache_hits': 0, 'similarity_calculations': 1578, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,979 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([22, 58, 54, 39, 41, 24, 13,  9,  7, 16, 42, 51,  1, 17,  2, 15, 11,
       32, 40, 50,  6, 18,  4, 59,  0, 27, 37, 36, 21, 30, 26, 44, 31, 49,
       38, 28, 34, 14, 10, 57, 20, 12, 29, 33,  8, 19, 35, 47, 23,  5, 46,
       43,  3, 56, 53, 45, 25, 55, 48, 52], dtype=int64), 'cur_cost': 97167.0, 'intermediate_solutions': [{'tour': array([32, 36,  6, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([59, 32, 36,  6, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101982.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 59, 32, 36,  6,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 59, 32, 36, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 24, 59, 32, 36,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,979 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 97167.00)
2025-08-05 10:29:06,979 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:06,979 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:06,979 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:06,979 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 111703.0
2025-08-05 10:29:06,989 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:29:06,989 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9620, 9621.0, 9614]
2025-08-05 10:29:06,989 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 48, 53, 55, 56, 49, 58, 57, 52, 50, 51,
       59,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 59, 51, 50, 52, 57, 58, 49, 56,
       55, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 18, 14, 21, 13, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  5,  6, 11,  7,  2,  3,  9, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64)]
2025-08-05 10:29:06,994 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:06,994 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 15, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14145.0}, {'tour': [0, 8, 6, 20, 16, 23, 12, 14, 21, 18, 19, 15, 17, 22, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10607.0}, {'tour': [6, 0, 24, 4, 40, 43, 32, 10, 11, 29, 5, 26, 38, 46, 30, 2, 33, 1, 8, 27, 3, 53, 57, 51, 52, 19, 54, 23, 48, 22, 56, 49, 14, 17, 18, 20, 50, 55, 58, 41, 9, 31, 34, 42, 39, 44, 47, 37, 45, 7, 28, 25, 13, 21, 15, 12, 59, 16, 35, 36], 'cur_cost': 53640.0}, {'tour': array([36, 42, 25, 40, 56, 53, 43, 50,  8, 57, 20,  1,  9, 52, 26, 48, 41,
       30, 38, 46, 37, 33, 35,  6, 31,  2, 59, 49,  3, 23,  5, 44, 34, 21,
       51, 14, 39, 13, 11,  0,  7, 55,  4, 54, 47, 45, 17, 28, 10, 15, 16,
       18, 29, 24, 58, 32, 12, 19, 22, 27], dtype=int64), 'cur_cost': 91026.0}, {'tour': array([22, 58, 54, 39, 41, 24, 13,  9,  7, 16, 42, 51,  1, 17,  2, 15, 11,
       32, 40, 50,  6, 18,  4, 59,  0, 27, 37, 36, 21, 30, 26, 44, 31, 49,
       38, 28, 34, 14, 10, 57, 20, 12, 29, 33,  8, 19, 35, 47, 23,  5, 46,
       43,  3, 56, 53, 45, 25, 55, 48, 52], dtype=int64), 'cur_cost': 97167.0}, {'tour': array([28,  0,  6, 48, 29, 14, 34, 45, 36, 59,  1, 30,  9, 41, 56, 50, 38,
        2, 40, 57, 24, 18,  4, 19, 39, 51,  5, 49, 25, 17, 10, 43, 11, 26,
       46, 52, 16, 53, 42, 33, 55, 47, 31, 22, 13, 44, 35, 21, 54, 20,  8,
       15, 37, 23,  7, 12, 32, 27, 58,  3], dtype=int64), 'cur_cost': 111703.0}, {'tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 13956.0}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 49537.0}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14065.0}, {'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 52295.0}]
2025-08-05 10:29:06,995 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:06,996 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 312, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 312, 'cache_hits': 0, 'similarity_calculations': 1590, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:06,997 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([28,  0,  6, 48, 29, 14, 34, 45, 36, 59,  1, 30,  9, 41, 56, 50, 38,
        2, 40, 57, 24, 18,  4, 19, 39, 51,  5, 49, 25, 17, 10, 43, 11, 26,
       46, 52, 16, 53, 42, 33, 55, 47, 31, 22, 13, 44, 35, 21, 54, 20,  8,
       15, 37, 23,  7, 12, 32, 27, 58,  3], dtype=int64), 'cur_cost': 111703.0, 'intermediate_solutions': [{'tour': array([25, 26, 28, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 98693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 25, 26, 28, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 100555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 56, 25, 26, 28, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 100870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 56, 25, 26, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 102879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 22, 56, 25, 26, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 102819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:06,997 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 111703.00)
2025-08-05 10:29:06,997 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:06,997 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:06,998 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:06,999 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:06,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14240.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:07,001 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 22, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14240.0, 'intermediate_solutions': [{'tour': [0, 4, 19, 2, 7, 11, 39, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 3, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 19308.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 1, 10, 6, 5, 9, 3, 11, 7, 2, 19, 4, 17, 13, 21, 14, 18], 'cur_cost': 18040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 35, 28, 27, 29, 33, 24, 30, 25, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,001 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 14240.00)
2025-08-05 10:29:07,001 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:07,001 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:07,001 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,003 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:07,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13959.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:07,004 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 21, 3, 2, 7, 11, 10, 1, 6, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 13959.0, 'intermediate_solutions': [{'tour': [11, 31, 22, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 8, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 53155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 15, 19, 49, 47, 28, 37, 42, 33, 5, 46, 43, 45, 40, 36, 32, 29, 10, 26, 24, 27, 35, 4, 22, 20, 51, 18, 21, 12, 13, 23], 'cur_cost': 49589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 13, 49, 19, 15, 23], 'cur_cost': 51411.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,004 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 13959.00)
2025-08-05 10:29:07,004 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:07,004 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:07,005 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,006 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:07,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,007 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,007 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10592.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:07,007 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 20, 14, 21, 18, 19, 15, 17, 22, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10592.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 2, 7, 11, 6, 9, 5, 3, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 59, 51, 44, 43, 41, 39, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 18318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 51, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 16694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,008 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10592.00)
2025-08-05 10:29:07,008 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:07,008 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:07,008 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,010 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 60
2025-08-05 10:29:07,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10208.0, 路径长度: 60, 收集中间解: 3
2025-08-05 10:29:07,011 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 9, 11, 2, 7, 8, 10, 1, 6, 4, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10208.0, 'intermediate_solutions': [{'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 38, 2, 27, 41, 6, 29, 25, 23, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 56136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 15, 59, 19, 14, 58, 56, 53, 49, 50, 55, 7, 44, 32, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 55713.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 5, 11, 1, 35, 8, 24, 12, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 17, 48, 52, 13, 3], 'cur_cost': 56089.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,011 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10208.00)
2025-08-05 10:29:07,011 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:07,011 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:07,013 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 15, 6, 4, 5, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 18, 21, 14, 19, 13, 17, 22, 20, 16, 23, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14145.0, 'intermediate_solutions': [{'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 8, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 4, 5, 50, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 5, 4, 39, 41, 43, 44], 'cur_cost': 13207.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 18, 20, 16, 23, 12, 14, 21, 13, 19, 17, 22, 54, 56, 55, 53, 57, 52, 59, 51, 50, 4, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 13377.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 6, 20, 16, 23, 12, 14, 21, 18, 19, 15, 17, 22, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 4, 5, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 44, 34, 30, 28, 27, 29, 33, 24, 35, 25, 32, 26, 31], 'cur_cost': 10607.0, 'intermediate_solutions': [{'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 21, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 20, 42, 5, 26, 36, 15, 6, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 79431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 18, 43, 47, 37, 45, 34, 30, 22, 44, 7, 27, 24, 35, 25, 20, 15, 36, 26, 5, 42, 6, 1, 10, 11, 4, 56, 13, 52, 48, 49, 33, 9, 51, 12, 14, 57, 59, 40, 21, 28, 3, 17, 19, 8, 41, 32, 2, 23, 38, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 75068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 18, 43, 38, 23, 2, 32, 41, 8, 19, 17, 3, 28, 40, 59, 57, 14, 12, 51, 9, 33, 49, 48, 52, 13, 56, 4, 11, 10, 1, 6, 42, 5, 26, 36, 15, 20, 21, 25, 35, 24, 27, 7, 44, 22, 30, 34, 45, 37, 47, 58, 54, 55, 39, 46, 16, 50, 53, 0], 'cur_cost': 71358.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 24, 4, 40, 43, 32, 10, 11, 29, 5, 26, 38, 46, 30, 2, 33, 1, 8, 27, 3, 53, 57, 51, 52, 19, 54, 23, 48, 22, 56, 49, 14, 17, 18, 20, 50, 55, 58, 41, 9, 31, 34, 42, 39, 44, 47, 37, 45, 7, 28, 25, 13, 21, 15, 12, 59, 16, 35, 36], 'cur_cost': 53640.0, 'intermediate_solutions': [{'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 16, 23, 12, 54, 56, 5, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 55, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 15838.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 1, 10, 11, 2, 3, 9, 48, 49, 58, 50, 51, 59, 52, 57, 53, 55, 56, 54, 12, 23, 16, 20, 22, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 14475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 19, 14, 21, 18, 13, 15, 17, 22, 20, 23, 12, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 11, 10, 1, 6, 4, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 16, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 16100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 42, 25, 40, 56, 53, 43, 50,  8, 57, 20,  1,  9, 52, 26, 48, 41,
       30, 38, 46, 37, 33, 35,  6, 31,  2, 59, 49,  3, 23,  5, 44, 34, 21,
       51, 14, 39, 13, 11,  0,  7, 55,  4, 54, 47, 45, 17, 28, 10, 15, 16,
       18, 29, 24, 58, 32, 12, 19, 22, 27], dtype=int64), 'cur_cost': 91026.0, 'intermediate_solutions': [{'tour': array([40, 50, 47, 24, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96426.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 40, 50, 47, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([56, 24, 40, 50, 47, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 94238.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 24, 40, 50, 56, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 93906.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 56, 24, 40, 50, 38, 12,  6, 46,  9, 21, 54,  4, 35,  0, 55, 10,
       23, 17,  1, 20, 16, 39, 11, 14,  3, 36, 58, 28, 29, 53, 59, 31, 32,
       27, 13, 22, 26,  2, 48,  8, 42, 25, 19, 37, 45, 30, 41, 43, 57,  7,
       33, 34, 18, 52, 44, 15, 51, 49,  5]), 'cur_cost': 96445.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 58, 54, 39, 41, 24, 13,  9,  7, 16, 42, 51,  1, 17,  2, 15, 11,
       32, 40, 50,  6, 18,  4, 59,  0, 27, 37, 36, 21, 30, 26, 44, 31, 49,
       38, 28, 34, 14, 10, 57, 20, 12, 29, 33,  8, 19, 35, 47, 23,  5, 46,
       43,  3, 56, 53, 45, 25, 55, 48, 52], dtype=int64), 'cur_cost': 97167.0, 'intermediate_solutions': [{'tour': array([32, 36,  6, 59, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([59, 32, 36,  6, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101982.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 59, 32, 36,  6,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 59, 32, 36, 24,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 24, 59, 32, 36,  8, 38, 13, 48, 19, 16, 52, 25,  3, 17, 33, 18,
        5, 56, 14, 54, 31, 29, 47, 15, 11, 20, 28, 23, 51, 46, 35, 53, 21,
       30,  7, 43, 50,  2, 12, 55, 40, 58, 45,  1, 49,  9, 22, 44, 39, 34,
       10, 26, 41, 37, 42, 27,  0, 57,  4]), 'cur_cost': 101968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([28,  0,  6, 48, 29, 14, 34, 45, 36, 59,  1, 30,  9, 41, 56, 50, 38,
        2, 40, 57, 24, 18,  4, 19, 39, 51,  5, 49, 25, 17, 10, 43, 11, 26,
       46, 52, 16, 53, 42, 33, 55, 47, 31, 22, 13, 44, 35, 21, 54, 20,  8,
       15, 37, 23,  7, 12, 32, 27, 58,  3], dtype=int64), 'cur_cost': 111703.0, 'intermediate_solutions': [{'tour': array([25, 26, 28, 56, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 98693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 25, 26, 28, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 100555.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 56, 25, 26, 28, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 100870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 56, 25, 26, 22, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 102879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 22, 56, 25, 26, 50, 43, 58, 44, 15, 40, 32, 37, 18, 16, 12, 35,
        8, 41, 49, 48, 45, 24, 59,  1, 14,  7,  3, 30,  5, 47, 34, 21, 51,
       53, 11, 55, 20,  0, 54, 57, 42,  4,  9, 52,  2, 13, 29, 23,  6, 27,
       17, 39, 36, 38, 31, 46, 19, 33, 10]), 'cur_cost': 102819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 4, 8, 7, 2, 11, 3, 9, 5, 6, 10, 1, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14240.0, 'intermediate_solutions': [{'tour': [0, 4, 19, 2, 7, 11, 39, 9, 5, 6, 10, 1, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 3, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 19308.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 22, 20, 16, 23, 12, 50, 48, 53, 54, 58, 49, 56, 55, 57, 52, 59, 51, 44, 43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27, 28, 35, 25, 31, 32, 26, 8, 1, 10, 6, 5, 9, 3, 11, 7, 2, 19, 4, 17, 13, 21, 14, 18], 'cur_cost': 18040.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 19, 2, 7, 11, 3, 9, 5, 6, 10, 1, 8, 26, 32, 31, 35, 28, 27, 29, 33, 24, 30, 25, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 13, 21, 14, 18], 'cur_cost': 14020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 21, 3, 2, 7, 11, 10, 1, 6, 5, 9, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 13], 'cur_cost': 13959.0, 'intermediate_solutions': [{'tour': [11, 31, 22, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 8, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 49, 19, 15, 13, 23], 'cur_cost': 53155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 15, 19, 49, 47, 28, 37, 42, 33, 5, 46, 43, 45, 40, 36, 32, 29, 10, 26, 24, 27, 35, 4, 22, 20, 51, 18, 21, 12, 13, 23], 'cur_cost': 49589.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 31, 8, 1, 0, 30, 6, 25, 39, 41, 38, 44, 34, 3, 52, 55, 59, 58, 14, 54, 53, 9, 7, 57, 56, 17, 2, 48, 50, 16, 12, 21, 18, 51, 20, 22, 4, 35, 27, 24, 26, 10, 29, 32, 36, 40, 45, 43, 46, 5, 33, 42, 37, 28, 47, 13, 49, 19, 15, 23], 'cur_cost': 51411.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 20, 14, 21, 18, 19, 15, 17, 22, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10592.0, 'intermediate_solutions': [{'tour': [0, 1, 16, 2, 7, 11, 6, 9, 5, 3, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 14250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 59, 51, 44, 43, 41, 39, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 18318.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 16, 2, 7, 11, 3, 9, 5, 6, 51, 4, 10, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 16694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 9, 11, 2, 7, 8, 10, 1, 6, 4, 3, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44, 51, 59, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13], 'cur_cost': 10208.0, 'intermediate_solutions': [{'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 38, 2, 27, 41, 6, 29, 25, 23, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 56136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 5, 11, 1, 35, 8, 24, 42, 0, 33, 10, 15, 59, 19, 14, 58, 56, 53, 49, 50, 55, 7, 44, 32, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 12, 17, 48, 52, 13, 3], 'cur_cost': 55713.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 5, 11, 1, 35, 8, 24, 12, 42, 0, 33, 10, 32, 44, 7, 55, 50, 49, 53, 56, 58, 14, 19, 59, 15, 18, 54, 22, 20, 21, 16, 23, 2, 27, 41, 6, 29, 25, 38, 9, 57, 46, 47, 43, 36, 40, 45, 37, 4, 34, 28, 26, 30, 39, 51, 17, 48, 52, 13, 3], 'cur_cost': 56089.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:07,014 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:07,014 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,017 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10208.000, 多样性=0.953
2025-08-05 10:29:07,017 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:07,017 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:07,017 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:07,019 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.036131686801495415, 'best_improvement': 0.022128556375131718}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.007836990595611062}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.0469661725779422, 'recent_improvements': [0.053730930504999885, -0.25692158872089244, 0.1476632756608843], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9614, 'new_best_cost': 9614, 'quality_improvement': 0.0, 'old_diversity': 0.6315656565656566, 'new_diversity': 0.6315656565656566, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:07,020 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:07,024 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite12_60_solution.json
2025-08-05 10:29:07,024 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite12_60_20250805_102907.solution
2025-08-05 10:29:07,024 - __main__ - INFO - 实例执行完成 - 运行时间: 1.94s, 最佳成本: 9614
2025-08-05 10:29:07,025 - __main__ - INFO - 实例 composite12_60 处理完成
