2025-08-05 09:52:19,084 - __main__ - INFO - pr76 开始进化第 1 代
2025-08-05 09:52:19,084 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:19,088 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,094 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=137933.000, 多样性=0.986
2025-08-05 09:52:19,101 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:19,108 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.986
2025-08-05 09:52:19,111 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:19,115 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:19,115 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:19,116 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:19,116 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:19,151 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -17032.340, 聚类评分: 0.000, 覆盖率: 0.161, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:19,152 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:19,152 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:19,152 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 09:52:19,158 - visualization.landscape_visualizer - INFO - 插值约束: 10 个点被约束到最小值 137933.00
2025-08-05 09:52:19,284 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_141_20250805_095219.html
2025-08-05 09:52:19,371 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_141_20250805_095219.html
2025-08-05 09:52:19,371 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 141
2025-08-05 09:52:19,371 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:19,371 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2564秒
2025-08-05 09:52:19,371 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 282, 'max_size': 500, 'hits': 0, 'misses': 282, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 934, 'misses': 498, 'hit_rate': 0.6522346368715084, 'evictions': 398, 'ttl': 7200}}
2025-08-05 09:52:19,371 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -17032.33999999999, 'local_optima_density': 0.1, 'gradient_variance': 35262401477.776405, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1605, 'fitness_entropy': 0.8173454221465103, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -17032.340)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.161)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358739.1521406, 'performance_metrics': {}}}
2025-08-05 09:52:19,372 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:19,372 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:19,372 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:19,372 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:19,374 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,374 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:19,374 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,374 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:19,374 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:19,374 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,374 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:19,374 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:19,375 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:19,375 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:19,375 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:19,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,386 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 390669.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,387 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 390669.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,387 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 390669.00)
2025-08-05 09:52:19,387 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:19,387 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:19,387 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,391 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146804.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,392 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146804.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,393 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 146804.00)
2025-08-05 09:52:19,393 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:19,393 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:19,393 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,396 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145701.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,397 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145701.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,397 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 145701.00)
2025-08-05 09:52:19,397 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:19,397 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:19,397 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,400 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155699.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,400 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 155699.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,401 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 155699.00)
2025-08-05 09:52:19,401 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:19,401 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:19,401 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,403 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,403 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 161614.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,404 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 161614.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,404 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 161614.00)
2025-08-05 09:52:19,404 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:19,404 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:19,404 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,406 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 09:52:19,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 518239.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,406 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 518239.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,407 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 518239.00)
2025-08-05 09:52:19,407 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:19,407 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,407 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,407 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 592475.0
2025-08-05 09:52:19,421 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,422 - ExploitationExpert - INFO - res_population_costs: [114036.0, 112931, 112925, 112036, 110745, 109916, 108444]
2025-08-05 09:52:19,422 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:19,425 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,425 - ExploitationExpert - INFO - populations: [{'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 390669.0}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146804.0}, {'tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145701.0}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 155699.0}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 161614.0}, {'tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 518239.0}, {'tour': array([ 7, 27, 54, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3], dtype=int64), 'cur_cost': 592475.0}, {'tour': array([ 2, 53, 58, 56, 72, 31, 55, 18, 33,  4, 54, 67, 32, 39, 40, 13, 27,
       73, 50, 41,  6, 14, 28, 17, 35, 64, 21, 25, 30, 44, 26, 71, 19, 68,
        9, 34, 22, 43,  8,  0, 29, 20, 49, 61, 15, 60, 57, 12, 63, 11, 36,
       59, 37, 38, 74, 69, 62, 48, 16, 47, 66,  7, 24, 65, 51, 75,  1,  3,
        5, 52, 23, 46, 10, 42, 45, 70], dtype=int64), 'cur_cost': 564097.0}, {'tour': array([24, 34, 38, 59,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 601406.0}, {'tour': array([54, 40, 24, 15, 28, 70, 57, 67, 23, 18, 65, 45, 53, 64, 14, 36, 19,
        4,  3,  6, 52, 71,  1, 55, 35, 33, 42, 27, 68, 32, 26, 21, 44, 13,
       74, 69, 46, 43, 61, 59, 31, 29, 12, 62, 49, 73, 17, 25, 48, 10, 51,
       75, 39, 56, 72, 11, 63,  0, 30, 66, 41, 60,  9,  8,  5, 38, 16,  2,
       20, 50, 58, 47, 22,  7, 34, 37], dtype=int64), 'cur_cost': 539848.0}]
2025-08-05 09:52:19,427 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:19,427 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 365, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 365, 'cache_hits': 0, 'similarity_calculations': 1902, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,428 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 7, 27, 54, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3], dtype=int64), 'cur_cost': 592475.0, 'intermediate_solutions': [{'tour': array([ 0, 18, 34, 31, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 599741.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  0, 18, 34, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 600060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([44, 31,  0, 18, 34, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 599096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 31,  0, 18, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 596580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 44, 31,  0, 18, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 602740.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,428 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 592475.00)
2025-08-05 09:52:19,429 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:19,429 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:19,429 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,439 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,440 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,440 - ExplorationExpert - INFO - 探索路径生成完成，成本: 406637.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,440 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 406637.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,441 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 406637.00)
2025-08-05 09:52:19,441 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:19,441 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,441 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,442 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 560500.0
2025-08-05 09:52:19,454 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,454 - ExploitationExpert - INFO - res_population_costs: [114036.0, 112931, 112925, 112036, 110745, 109916, 108444]
2025-08-05 09:52:19,454 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:19,457 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,457 - ExploitationExpert - INFO - populations: [{'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 390669.0}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146804.0}, {'tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145701.0}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 155699.0}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 161614.0}, {'tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 518239.0}, {'tour': array([ 7, 27, 54, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3], dtype=int64), 'cur_cost': 592475.0}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 406637.0}, {'tour': array([ 3, 26, 49, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75], dtype=int64), 'cur_cost': 560500.0}, {'tour': array([54, 40, 24, 15, 28, 70, 57, 67, 23, 18, 65, 45, 53, 64, 14, 36, 19,
        4,  3,  6, 52, 71,  1, 55, 35, 33, 42, 27, 68, 32, 26, 21, 44, 13,
       74, 69, 46, 43, 61, 59, 31, 29, 12, 62, 49, 73, 17, 25, 48, 10, 51,
       75, 39, 56, 72, 11, 63,  0, 30, 66, 41, 60,  9,  8,  5, 38, 16,  2,
       20, 50, 58, 47, 22,  7, 34, 37], dtype=int64), 'cur_cost': 539848.0}]
2025-08-05 09:52:19,459 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:19,460 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 366, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 366, 'cache_hits': 0, 'similarity_calculations': 1903, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,461 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 3, 26, 49, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75], dtype=int64), 'cur_cost': 560500.0, 'intermediate_solutions': [{'tour': array([38, 34, 24, 59,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 611904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([59, 38, 34, 24,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 601457.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 59, 38, 34, 24, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 601434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 59, 38, 34,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 600404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24,  3, 59, 38, 34, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 598498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,461 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 560500.00)
2025-08-05 09:52:19,461 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:19,461 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:19,461 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,464 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,465 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,465 - ExplorationExpert - INFO - 探索路径生成完成，成本: 175402.0, 路径长度: 76, 收集中间解: 0
2025-08-05 09:52:19,465 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 175402.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,466 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 175402.00)
2025-08-05 09:52:19,466 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:19,466 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:19,468 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 390669.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146804.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145701.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 155699.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 161614.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 518239.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 27, 54, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3], dtype=int64), 'cur_cost': 592475.0, 'intermediate_solutions': [{'tour': array([ 0, 18, 34, 31, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 599741.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31,  0, 18, 34, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 600060.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([44, 31,  0, 18, 34, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 599096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 31,  0, 18, 44, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 596580.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 44, 31,  0, 18, 56,  4,  6, 48, 59, 58, 42, 10, 52, 12, 72,  8,
       20, 13, 30, 22, 32, 62, 67, 70, 73,  2, 40, 36, 14, 37, 24, 51, 68,
       45, 16, 53, 28, 75, 60, 11, 43, 65, 33, 49, 64, 69, 17, 50,  1, 63,
       46, 19, 47, 61, 71, 74, 38, 55,  7, 26, 21, 27, 29, 54, 25, 23, 35,
        3, 15,  9, 39,  5, 66, 41, 57], dtype=int64), 'cur_cost': 602740.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 406637.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 26, 49, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75], dtype=int64), 'cur_cost': 560500.0, 'intermediate_solutions': [{'tour': array([38, 34, 24, 59,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 611904.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([59, 38, 34, 24,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 601457.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 59, 38, 34, 24, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 601434.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 59, 38, 34,  3, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 600404.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24,  3, 59, 38, 34, 13, 74, 16, 45, 54,  6, 31, 57, 12, 26, 39, 64,
       33, 65, 58, 49, 55, 62, 40, 25, 15, 48, 30, 71,  5, 66, 32, 53, 35,
       44, 75, 70, 23, 18, 68, 20, 73, 22,  8,  0, 67, 72, 19, 51, 69, 17,
       37,  7, 43, 29, 60, 21, 11, 41, 47, 36, 50, 56, 61, 46,  9, 52, 14,
        1, 28, 42,  4, 10, 63, 27,  2], dtype=int64), 'cur_cost': 598498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 175402.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:19,468 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:19,468 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,472 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145701.000, 多样性=0.962
2025-08-05 09:52:19,472 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:19,472 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:19,472 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:19,473 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07581582574439895, 'best_improvement': -0.05631719747993591}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02402135231316733}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.058127342638798075, 'recent_improvements': [-0.025020135266392435, -0.09243919672223729, 0.09123455001120373], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 108444, 'new_best_cost': 108444, 'quality_improvement': 0.0, 'old_diversity': 0.8264411027568922, 'new_diversity': 0.8264411027568922, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:19,474 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:19,474 - __main__ - INFO - pr76 开始进化第 2 代
2025-08-05 09:52:19,474 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:19,474 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,475 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145701.000, 多样性=0.962
2025-08-05 09:52:19,476 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:19,479 - PathExpert - INFO - 路径结构分析完成: 公共边数量=11, 路径相似性=0.962
2025-08-05 09:52:19,479 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:19,484 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.826
2025-08-05 09:52:19,486 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:19,486 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:19,486 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:19,486 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:19,542 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -35826.647, 聚类评分: 0.000, 覆盖率: 0.162, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:19,543 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:19,543 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:19,543 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 09:52:19,549 - visualization.landscape_visualizer - INFO - 插值约束: 217 个点被约束到最小值 108444.00
2025-08-05 09:52:19,667 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_142_20250805_095219.html
2025-08-05 09:52:19,724 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_142_20250805_095219.html
2025-08-05 09:52:19,724 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 142
2025-08-05 09:52:19,725 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:19,725 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2388秒
2025-08-05 09:52:19,725 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -35826.64705882353, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 24702854582.99543, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1621, 'fitness_entropy': 0.7342827499973427, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -35826.647)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.162)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358739.5438643, 'performance_metrics': {}}}
2025-08-05 09:52:19,725 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:19,726 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:19,726 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:19,726 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:19,727 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,727 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:19,727 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,727 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:19,727 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:19,727 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:19,728 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:19,728 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:19,728 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:19,728 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:19,728 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:19,728 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,731 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,732 - ExplorationExpert - INFO - 探索路径生成完成，成本: 172277.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,732 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 172277.0, 'intermediate_solutions': [{'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 64, 22, 29, 33, 59, 42, 56, 48, 2, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 422270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 64, 48, 56, 42, 59, 33, 29, 22, 2, 24, 11, 32, 9, 13, 19, 34, 41, 52, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 389711.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 53, 47, 36, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 397630.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,732 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 172277.00)
2025-08-05 09:52:19,733 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:19,733 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:19,733 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,740 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,740 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,741 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,741 - ExplorationExpert - INFO - 探索路径生成完成，成本: 428724.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,742 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [62, 34, 60, 41, 59, 18, 5, 7, 17, 40, 25, 52, 54, 57, 39, 29, 8, 32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24, 2, 22, 31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14, 6, 1, 12, 4, 45, 9, 30, 20, 35, 21, 46, 0, 3, 68, 64, 56, 70, 69, 43, 16, 73, 13, 74, 67, 61, 71, 72, 75], 'cur_cost': 428724.0, 'intermediate_solutions': [{'tour': [0, 3, 17, 15, 13, 12, 14, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 148462.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 67, 68, 46, 47, 43, 44, 45, 23, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 161362.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 9, 2, 1, 22, 21, 20, 8, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 157954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,742 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 428724.00)
2025-08-05 09:52:19,742 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:19,742 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:19,742 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,749 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,749 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,750 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 391083.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,751 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 391083.0, 'intermediate_solutions': [{'tour': [0, 74, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 5, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 158017.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 73, 25, 26, 32], 'cur_cost': 153499.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 10, 11, 12, 13, 5, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 151460.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,751 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 391083.00)
2025-08-05 09:52:19,751 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:19,751 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:19,751 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,753 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165379.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,754 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 165379.0, 'intermediate_solutions': [{'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 57, 54, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 157565.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 70, 48, 49, 66, 67, 68, 45, 44, 46, 47, 43, 26, 25, 31, 32, 38, 37, 73, 7, 5, 9, 8, 11, 13, 12, 14, 15, 16, 71, 72, 69, 75, 74], 'cur_cost': 178864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 46, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 172529.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,755 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 165379.00)
2025-08-05 09:52:19,755 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:19,755 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:19,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,762 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,762 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,763 - ExplorationExpert - INFO - 探索路径生成完成，成本: 404161.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,763 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 404161.0, 'intermediate_solutions': [{'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 69, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 17], 'cur_cost': 178522.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 21, 20, 24, 25, 26, 32, 31, 4, 3, 2, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 180116.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 21, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 176678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,763 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 404161.00)
2025-08-05 09:52:19,763 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:19,763 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:19,764 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,770 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:19,770 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,770 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,771 - ExplorationExpert - INFO - 探索路径生成完成，成本: 411232.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,771 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 411232.0, 'intermediate_solutions': [{'tour': [26, 5, 53, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 17, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 529191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 68, 1, 20, 47, 32, 3, 74, 0, 7, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 531272.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 5, 17, 10, 24, 23, 4, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70], 'cur_cost': 518305.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,772 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 411232.00)
2025-08-05 09:52:19,772 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:19,772 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,772 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,773 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 596382.0
2025-08-05 09:52:19,784 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,784 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:19,784 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:19,789 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,789 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 172277.0}, {'tour': [62, 34, 60, 41, 59, 18, 5, 7, 17, 40, 25, 52, 54, 57, 39, 29, 8, 32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24, 2, 22, 31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14, 6, 1, 12, 4, 45, 9, 30, 20, 35, 21, 46, 0, 3, 68, 64, 56, 70, 69, 43, 16, 73, 13, 74, 67, 61, 71, 72, 75], 'cur_cost': 428724.0}, {'tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 391083.0}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 165379.0}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 404161.0}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 411232.0}, {'tour': array([17, 50, 75, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26], dtype=int64), 'cur_cost': 596382.0}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 406637.0}, {'tour': [3, 26, 49, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34, 0, 15, 59, 60, 6, 50, 39, 5, 70, 28, 10, 54, 8, 37, 63, 31, 22, 53, 65, 62, 56, 32, 73, 12, 18, 64, 40, 2, 17, 44, 71, 9, 33, 19, 29, 4, 21, 7, 74, 30, 58, 25, 24, 43, 36, 48, 66, 1, 69, 27, 46, 45, 38, 47, 61, 72, 57, 13, 68, 42, 75], 'cur_cost': 560500.0}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 175402.0}]
2025-08-05 09:52:19,790 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:19,790 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 367, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 367, 'cache_hits': 0, 'similarity_calculations': 1905, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,791 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([17, 50, 75, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26], dtype=int64), 'cur_cost': 596382.0, 'intermediate_solutions': [{'tour': array([54, 27,  7, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 589921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([74, 54, 27,  7, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 591639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 74, 54, 27,  7, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 597108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 74, 54, 27, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 584711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 30, 74, 54, 27, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 587784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,792 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 596382.00)
2025-08-05 09:52:19,792 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:19,792 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:19,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,795 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 147122.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,797 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 147122.0, 'intermediate_solutions': [{'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 58, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 49, 22, 0, 74, 66], 'cur_cost': 409627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 70, 55, 39, 61, 60, 54, 63, 48, 49, 68, 23, 3, 45, 24, 16, 2, 11, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 418373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 75, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 409667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,797 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 147122.00)
2025-08-05 09:52:19,797 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:19,797 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,797 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,797 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 584534.0
2025-08-05 09:52:19,811 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,811 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:19,811 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:19,814 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,814 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 172277.0}, {'tour': [62, 34, 60, 41, 59, 18, 5, 7, 17, 40, 25, 52, 54, 57, 39, 29, 8, 32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24, 2, 22, 31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14, 6, 1, 12, 4, 45, 9, 30, 20, 35, 21, 46, 0, 3, 68, 64, 56, 70, 69, 43, 16, 73, 13, 74, 67, 61, 71, 72, 75], 'cur_cost': 428724.0}, {'tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 391083.0}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 165379.0}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 404161.0}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 411232.0}, {'tour': array([17, 50, 75, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26], dtype=int64), 'cur_cost': 596382.0}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 147122.0}, {'tour': array([60, 27, 30, 68, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14], dtype=int64), 'cur_cost': 584534.0}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 175402.0}]
2025-08-05 09:52:19,815 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:19,815 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 368, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 368, 'cache_hits': 0, 'similarity_calculations': 1908, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,816 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([60, 27, 30, 68, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14], dtype=int64), 'cur_cost': 584534.0, 'intermediate_solutions': [{'tour': array([49, 26,  3, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 572428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 49, 26,  3, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 573597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 41, 49, 26,  3, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 563811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 41, 49, 26, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 564939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 55, 41, 49, 26, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 560203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,816 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 584534.00)
2025-08-05 09:52:19,817 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:19,817 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:19,817 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,819 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:19,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 159934.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:19,820 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159934.0, 'intermediate_solutions': [{'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 49, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 13, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 221443.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 44, 45, 23, 24, 21, 22, 1, 3, 2, 7, 6, 5, 8, 9, 15, 14, 13, 12, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 182520.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 57, 56, 62, 63, 61, 60, 58, 54, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 179969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,821 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 159934.00)
2025-08-05 09:52:19,821 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:19,821 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:19,823 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 172277.0, 'intermediate_solutions': [{'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 64, 22, 29, 33, 59, 42, 56, 48, 2, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 422270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 64, 48, 56, 42, 59, 33, 29, 22, 2, 24, 11, 32, 9, 13, 19, 34, 41, 52, 65, 66, 44, 21, 4, 5, 0, 74, 3, 36, 53, 47, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 389711.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 40, 50, 62, 54, 51, 67, 55, 39, 18, 10, 14, 58, 25, 35, 37, 31, 8, 17, 60, 27, 49, 70, 52, 41, 34, 19, 13, 9, 32, 11, 24, 2, 22, 29, 33, 59, 42, 56, 48, 64, 65, 66, 44, 21, 4, 5, 0, 74, 3, 53, 47, 36, 20, 23, 68, 57, 43, 69, 45, 28, 30, 38, 12, 73, 7, 6, 15, 1, 16, 46, 61, 63, 71, 72, 75], 'cur_cost': 397630.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [62, 34, 60, 41, 59, 18, 5, 7, 17, 40, 25, 52, 54, 57, 39, 29, 8, 32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24, 2, 22, 31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14, 6, 1, 12, 4, 45, 9, 30, 20, 35, 21, 46, 0, 3, 68, 64, 56, 70, 69, 43, 16, 73, 13, 74, 67, 61, 71, 72, 75], 'cur_cost': 428724.0, 'intermediate_solutions': [{'tour': [0, 3, 17, 15, 13, 12, 14, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 148462.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 2, 1, 22, 21, 20, 24, 67, 68, 46, 47, 43, 44, 45, 23, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 161362.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 17, 15, 14, 12, 13, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 9, 2, 1, 22, 21, 20, 8, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 157954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 391083.0, 'intermediate_solutions': [{'tour': [0, 74, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 5, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 158017.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 73, 25, 26, 32], 'cur_cost': 153499.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 10, 11, 12, 13, 5, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 7, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 151460.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 165379.0, 'intermediate_solutions': [{'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 57, 54, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 157565.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 70, 48, 49, 66, 67, 68, 45, 44, 46, 47, 43, 26, 25, 31, 32, 38, 37, 73, 7, 5, 9, 8, 11, 13, 12, 14, 15, 16, 71, 72, 69, 75, 74], 'cur_cost': 178864.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 10, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 15, 14, 12, 13, 11, 8, 9, 46, 5, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 172529.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 404161.0, 'intermediate_solutions': [{'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 69, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 21, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 17], 'cur_cost': 178522.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 21, 20, 24, 25, 26, 32, 31, 4, 3, 2, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 180116.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 22, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 21, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 4, 31, 32, 26, 25, 24, 20, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 74, 75, 69], 'cur_cost': 176678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 411232.0, 'intermediate_solutions': [{'tour': [26, 5, 53, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 17, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 529191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 5, 17, 10, 24, 23, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 68, 1, 20, 47, 32, 3, 74, 0, 7, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70, 4], 'cur_cost': 531272.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 5, 17, 10, 24, 23, 4, 21, 55, 15, 42, 36, 35, 58, 33, 39, 40, 31, 27, 54, 60, 62, 49, 51, 50, 41, 34, 19, 13, 9, 56, 11, 48, 52, 22, 53, 59, 12, 29, 30, 64, 65, 66, 44, 8, 7, 0, 74, 3, 32, 47, 20, 1, 68, 57, 25, 45, 28, 67, 38, 73, 71, 6, 37, 16, 46, 2, 63, 18, 72, 43, 14, 69, 75, 61, 70], 'cur_cost': 518305.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 50, 75, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26], dtype=int64), 'cur_cost': 596382.0, 'intermediate_solutions': [{'tour': array([54, 27,  7, 74, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 589921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([74, 54, 27,  7, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 591639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 74, 54, 27,  7, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 597108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 74, 54, 27, 30, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 584711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 30, 74, 54, 27, 63, 62,  4, 64, 57, 61, 46, 45, 29, 39,  2, 42,
       44, 14, 43, 20, 59,  5, 33, 49, 52, 34, 66, 28, 41, 18, 48, 23, 17,
       32, 19, 72,  8, 70, 47, 10, 55, 24, 56, 35,  0,  6, 22, 21, 53, 26,
       37, 13, 69, 11, 60, 15, 75, 31, 73,  1, 36, 38, 12, 58,  9, 25, 71,
       67, 65, 40, 16, 68, 50, 51,  3]), 'cur_cost': 587784.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 147122.0, 'intermediate_solutions': [{'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 58, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 49, 22, 0, 74, 66], 'cur_cost': 409627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 75, 7, 34, 70, 55, 39, 61, 60, 54, 63, 48, 49, 68, 23, 3, 45, 24, 16, 2, 11, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 418373.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [47, 33, 31, 10, 25, 46, 19, 14, 73, 4, 13, 27, 40, 50, 43, 42, 38, 26, 57, 35, 9, 29, 32, 51, 65, 72, 37, 62, 64, 28, 41, 44, 1, 8, 75, 20, 53, 67, 52, 18, 15, 36, 6, 12, 17, 30, 5, 21, 7, 34, 11, 2, 16, 24, 45, 3, 23, 68, 49, 48, 63, 54, 60, 61, 39, 55, 70, 56, 59, 71, 69, 58, 22, 0, 74, 66], 'cur_cost': 409667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 27, 30, 68, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14], dtype=int64), 'cur_cost': 584534.0, 'intermediate_solutions': [{'tour': array([49, 26,  3, 41, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 572428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 49, 26,  3, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 573597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 41, 49, 26,  3, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 563811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 41, 49, 26, 55, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 564939.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 55, 41, 49, 26, 20, 11, 35, 14, 67, 51, 23, 52, 16, 34,  0, 15,
       59, 60,  6, 50, 39,  5, 70, 28, 10, 54,  8, 37, 63, 31, 22, 53, 65,
       62, 56, 32, 73, 12, 18, 64, 40,  2, 17, 44, 71,  9, 33, 19, 29,  4,
       21,  7, 74, 30, 58, 25, 24, 43, 36, 48, 66,  1, 69, 27, 46, 45, 38,
       47, 61, 72, 57, 13, 68, 42, 75]), 'cur_cost': 560203.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159934.0, 'intermediate_solutions': [{'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 49, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 13, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 221443.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 44, 45, 23, 24, 21, 22, 1, 3, 2, 7, 6, 5, 8, 9, 15, 14, 13, 12, 11, 10, 16, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 182520.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 20, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 57, 56, 62, 63, 61, 60, 58, 54, 59, 40, 39, 33, 34, 35, 36, 16, 10, 11, 12, 13, 14, 15, 9, 8, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 179969.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:19,823 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:19,823 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,827 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=147122.000, 多样性=0.983
2025-08-05 09:52:19,828 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:19,828 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:19,828 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:19,829 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.026664454559365714, 'best_improvement': -0.009752850014756247}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.02157398966879367}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.08412751123331812, 'recent_improvements': [-0.09243919672223729, 0.09123455001120373, 0.07581582574439895], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 108444, 'new_best_cost': 108444, 'quality_improvement': 0.0, 'old_diversity': 0.8264411027568922, 'new_diversity': 0.8264411027568922, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:19,829 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:19,829 - __main__ - INFO - pr76 开始进化第 3 代
2025-08-05 09:52:19,829 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:19,830 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,830 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=147122.000, 多样性=0.983
2025-08-05 09:52:19,831 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:19,834 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.983
2025-08-05 09:52:19,834 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:19,837 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.826
2025-08-05 09:52:19,839 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:19,839 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:19,839 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:19,839 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:19,893 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -54069.329, 聚类评分: 0.000, 覆盖率: 0.163, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:19,893 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:19,893 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:19,893 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 09:52:19,913 - visualization.landscape_visualizer - INFO - 插值约束: 162 个点被约束到最小值 108444.00
2025-08-05 09:52:20,054 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_143_20250805_095219.html
2025-08-05 09:52:20,117 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_143_20250805_095219.html
2025-08-05 09:52:20,117 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 143
2025-08-05 09:52:20,117 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:20,118 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2795秒
2025-08-05 09:52:20,118 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -54069.32941176471, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 31479426211.190315, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.163, 'fitness_entropy': 0.8607727996741097, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -54069.329)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.163)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358739.8930144, 'performance_metrics': {}}}
2025-08-05 09:52:20,118 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:20,118 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:20,118 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:20,119 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:20,119 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,119 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:20,119 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,119 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:20,119 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:20,119 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,120 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:20,120 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:20,120 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:20,120 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:20,121 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:20,121 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,125 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,126 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,127 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146968.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,127 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146968.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 19, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 44, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 194744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 61, 63, 62, 56, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 174135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 52, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 179867.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,127 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 146968.00)
2025-08-05 09:52:20,127 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 09:52:20,127 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,127 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,128 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 577136.0
2025-08-05 09:52:20,142 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,143 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,143 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,147 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,147 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146968.0}, {'tour': array([ 2, 36, 61, 33, 17, 23, 14, 66,  5, 12, 59, 62, 26, 48, 34, 43,  0,
       52,  1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54,
       22, 16, 30, 27, 63, 71, 42, 37, 29,  8, 64, 35, 44, 20, 10, 46, 57,
       15,  7, 32, 73,  9,  4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19,
        6,  3, 40, 65, 49, 50, 74, 41], dtype=int64), 'cur_cost': 577136.0}, {'tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 391083.0}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 165379.0}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 404161.0}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 411232.0}, {'tour': [17, 50, 75, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15, 1, 66, 55, 23, 5, 32, 70, 0, 27, 46, 59, 2, 68, 6, 19, 29, 35, 8, 47, 10, 3, 60, 33, 49, 74, 39, 25, 18, 41, 28, 9, 37, 38, 64, 45, 57, 31, 61, 52, 11, 54, 4, 69, 43, 34, 7, 58, 51, 65, 22, 44, 12, 67, 20, 62, 71, 73, 21, 53, 48, 26], 'cur_cost': 596382.0}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 147122.0}, {'tour': [60, 27, 30, 68, 63, 11, 22, 18, 0, 6, 1, 44, 29, 23, 4, 53, 51, 48, 71, 47, 69, 39, 55, 20, 2, 46, 58, 45, 40, 59, 54, 28, 10, 72, 12, 35, 15, 3, 24, 19, 49, 13, 26, 9, 52, 8, 43, 75, 61, 33, 16, 56, 65, 31, 37, 17, 36, 7, 70, 5, 62, 34, 67, 50, 73, 41, 25, 57, 66, 32, 21, 38, 74, 64, 42, 14], 'cur_cost': 584534.0}, {'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159934.0}]
2025-08-05 09:52:20,148 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,148 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 369, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 369, 'cache_hits': 0, 'similarity_calculations': 1912, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,149 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 2, 36, 61, 33, 17, 23, 14, 66,  5, 12, 59, 62, 26, 48, 34, 43,  0,
       52,  1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54,
       22, 16, 30, 27, 63, 71, 42, 37, 29,  8, 64, 35, 44, 20, 10, 46, 57,
       15,  7, 32, 73,  9,  4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19,
        6,  3, 40, 65, 49, 50, 74, 41], dtype=int64), 'cur_cost': 577136.0, 'intermediate_solutions': [{'tour': array([60, 34, 62, 41, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 428136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 60, 34, 62, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 423202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([59, 41, 60, 34, 62, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 428993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([62, 41, 60, 34, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 426934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([62, 59, 41, 60, 34, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 422274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,149 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 577136.00)
2025-08-05 09:52:20,150 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:20,150 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:20,150 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,152 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,154 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,154 - ExplorationExpert - INFO - 探索路径生成完成，成本: 171733.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,154 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 171733.0, 'intermediate_solutions': [{'tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 23, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 14, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 399227.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [70, 50, 56, 54, 55, 38, 37, 7, 74, 22, 68, 63, 71, 57, 67, 61, 40, 58, 49, 46, 75, 23, 29, 33, 31, 65, 60, 52, 21, 8, 34, 28, 42, 25, 30, 73, 10, 3, 19, 6, 0, 12, 5, 17, 26, 1, 9, 2, 4, 35, 18, 24, 44, 43, 27, 14, 36, 59, 32, 39, 51, 47, 41, 64, 53, 72, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 400219.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [70, 17, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 401932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,154 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 171733.00)
2025-08-05 09:52:20,154 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:20,154 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:20,155 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,162 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:20,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,163 - ExplorationExpert - INFO - 探索路径生成完成，成本: 402138.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,163 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402138.0, 'intermediate_solutions': [{'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 72, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 7], 'cur_cost': 198408.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 68, 73, 75, 74, 1, 22, 21, 20, 24, 23, 45, 44, 46, 47, 43, 26, 25, 31, 32, 38, 37, 16, 14, 67, 66, 69, 70, 71, 72], 'cur_cost': 192038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 60, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 183917.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,163 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 402138.00)
2025-08-05 09:52:20,163 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:20,164 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:20,164 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,166 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 09:52:20,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,166 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,167 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,167 - ExplorationExpert - INFO - 探索路径生成完成，成本: 584279.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,167 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [68, 11, 14, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28, 55, 30, 65, 64, 4, 5, 67, 42, 27, 10, 29, 32, 0, 41, 52, 2, 63, 31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53, 8, 59, 3, 46, 62, 24, 18, 6, 48, 7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20, 35, 22, 26, 73, 44, 15, 9, 1], 'cur_cost': 584279.0, 'intermediate_solutions': [{'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 0, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 15, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 410871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 57, 46, 66, 49, 44, 62, 48, 64, 39, 40, 9, 75, 24, 11, 0, 2, 29, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 412378.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 40, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 412245.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,167 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 584279.00)
2025-08-05 09:52:20,167 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:20,168 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:20,168 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,175 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:20,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,175 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,176 - ExplorationExpert - INFO - 探索路径生成完成，成本: 412601.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,176 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 412601.0, 'intermediate_solutions': [{'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 74, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 21], 'cur_cost': 402545.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 22, 62, 57, 74], 'cur_cost': 427873.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 5, 29, 51, 58, 49, 64, 17, 8, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 416553.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,176 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 412601.00)
2025-08-05 09:52:20,177 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:20,177 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,177 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,177 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 581740.0
2025-08-05 09:52:20,193 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,194 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,194 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,199 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,199 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146968.0}, {'tour': array([ 2, 36, 61, 33, 17, 23, 14, 66,  5, 12, 59, 62, 26, 48, 34, 43,  0,
       52,  1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54,
       22, 16, 30, 27, 63, 71, 42, 37, 29,  8, 64, 35, 44, 20, 10, 46, 57,
       15,  7, 32, 73,  9,  4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19,
        6,  3, 40, 65, 49, 50, 74, 41], dtype=int64), 'cur_cost': 577136.0}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 171733.0}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402138.0}, {'tour': [68, 11, 14, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28, 55, 30, 65, 64, 4, 5, 67, 42, 27, 10, 29, 32, 0, 41, 52, 2, 63, 31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53, 8, 59, 3, 46, 62, 24, 18, 6, 48, 7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20, 35, 22, 26, 73, 44, 15, 9, 1], 'cur_cost': 584279.0}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 412601.0}, {'tour': array([26, 47, 34, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7], dtype=int64), 'cur_cost': 581740.0}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 147122.0}, {'tour': [60, 27, 30, 68, 63, 11, 22, 18, 0, 6, 1, 44, 29, 23, 4, 53, 51, 48, 71, 47, 69, 39, 55, 20, 2, 46, 58, 45, 40, 59, 54, 28, 10, 72, 12, 35, 15, 3, 24, 19, 49, 13, 26, 9, 52, 8, 43, 75, 61, 33, 16, 56, 65, 31, 37, 17, 36, 7, 70, 5, 62, 34, 67, 50, 73, 41, 25, 57, 66, 32, 21, 38, 74, 64, 42, 14], 'cur_cost': 584534.0}, {'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159934.0}]
2025-08-05 09:52:20,201 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,202 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 370, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 370, 'cache_hits': 0, 'similarity_calculations': 1917, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,203 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([26, 47, 34, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7], dtype=int64), 'cur_cost': 581740.0, 'intermediate_solutions': [{'tour': array([75, 50, 17, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 590332.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 75, 50, 17, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 596590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 16, 75, 50, 17, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 593277.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 16, 75, 50, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 591857.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 30, 16, 75, 50, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 586587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,203 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 581740.00)
2025-08-05 09:52:20,203 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:20,203 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:20,204 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,207 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,208 - ExplorationExpert - INFO - 探索路径生成完成，成本: 136183.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,208 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136183.0, 'intermediate_solutions': [{'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 28, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 40, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 162924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 32, 31, 37, 26, 25, 15, 73], 'cur_cost': 152863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 53, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 152631.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,208 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 136183.00)
2025-08-05 09:52:20,208 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:20,208 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,208 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,209 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 589515.0
2025-08-05 09:52:20,221 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,221 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,221 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,224 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,224 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146968.0}, {'tour': array([ 2, 36, 61, 33, 17, 23, 14, 66,  5, 12, 59, 62, 26, 48, 34, 43,  0,
       52,  1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54,
       22, 16, 30, 27, 63, 71, 42, 37, 29,  8, 64, 35, 44, 20, 10, 46, 57,
       15,  7, 32, 73,  9,  4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19,
        6,  3, 40, 65, 49, 50, 74, 41], dtype=int64), 'cur_cost': 577136.0}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 171733.0}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402138.0}, {'tour': [68, 11, 14, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28, 55, 30, 65, 64, 4, 5, 67, 42, 27, 10, 29, 32, 0, 41, 52, 2, 63, 31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53, 8, 59, 3, 46, 62, 24, 18, 6, 48, 7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20, 35, 22, 26, 73, 44, 15, 9, 1], 'cur_cost': 584279.0}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 412601.0}, {'tour': array([26, 47, 34, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7], dtype=int64), 'cur_cost': 581740.0}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136183.0}, {'tour': array([28, 48, 74, 61,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15], dtype=int64), 'cur_cost': 589515.0}, {'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159934.0}]
2025-08-05 09:52:20,226 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,226 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 371, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 371, 'cache_hits': 0, 'similarity_calculations': 1923, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,227 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([28, 48, 74, 61,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15], dtype=int64), 'cur_cost': 589515.0, 'intermediate_solutions': [{'tour': array([30, 27, 60, 68, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 581484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 30, 27, 60, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 582505.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([63, 68, 30, 27, 60, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 584652.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 68, 30, 27, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 586617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 63, 68, 30, 27, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 575499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,228 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 589515.00)
2025-08-05 09:52:20,228 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:20,228 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:20,228 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,231 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 147422.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,232 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 147422.0, 'intermediate_solutions': [{'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 8, 35, 36, 17, 16, 10, 11, 14, 15, 13, 34, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 175342.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 70, 48, 49, 66, 67, 71, 72, 69, 75, 74], 'cur_cost': 176596.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 12, 4, 20, 24, 23, 21, 22, 1, 2, 3, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159251.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,233 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 147422.00)
2025-08-05 09:52:20,233 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:20,233 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:20,236 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 146968.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 19, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 44, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 194744.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 61, 63, 62, 56, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 174135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 2, 3, 9, 10, 11, 12, 13, 15, 16, 17, 36, 35, 34, 33, 39, 52, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73, 69], 'cur_cost': 179867.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 36, 61, 33, 17, 23, 14, 66,  5, 12, 59, 62, 26, 48, 34, 43,  0,
       52,  1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54,
       22, 16, 30, 27, 63, 71, 42, 37, 29,  8, 64, 35, 44, 20, 10, 46, 57,
       15,  7, 32, 73,  9,  4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19,
        6,  3, 40, 65, 49, 50, 74, 41], dtype=int64), 'cur_cost': 577136.0, 'intermediate_solutions': [{'tour': array([60, 34, 62, 41, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 428136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 60, 34, 62, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 423202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([59, 41, 60, 34, 62, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 428993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([62, 41, 60, 34, 59, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 426934.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([62, 59, 41, 60, 34, 18,  5,  7, 17, 40, 25, 52, 54, 57, 39, 29,  8,
       32, 36, 28, 23, 49, 48, 42, 53, 44, 27, 37, 58, 38, 11, 24,  2, 22,
       31, 33, 65, 47, 51, 50, 55, 66, 63, 26, 10, 15, 19, 14,  6,  1, 12,
        4, 45,  9, 30, 20, 35, 21, 46,  0,  3, 68, 64, 56, 70, 69, 43, 16,
       73, 13, 74, 67, 61, 71, 72, 75]), 'cur_cost': 422274.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 171733.0, 'intermediate_solutions': [{'tour': [70, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 23, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 17, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 14, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 399227.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [70, 50, 56, 54, 55, 38, 37, 7, 74, 22, 68, 63, 71, 57, 67, 61, 40, 58, 49, 46, 75, 23, 29, 33, 31, 65, 60, 52, 21, 8, 34, 28, 42, 25, 30, 73, 10, 3, 19, 6, 0, 12, 5, 17, 26, 1, 9, 2, 4, 35, 18, 24, 44, 43, 27, 14, 36, 59, 32, 39, 51, 47, 41, 64, 53, 72, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 400219.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [70, 17, 50, 56, 54, 55, 72, 53, 64, 41, 47, 51, 39, 32, 59, 36, 14, 27, 43, 44, 24, 18, 35, 4, 2, 9, 1, 26, 5, 12, 0, 6, 19, 3, 10, 73, 30, 25, 42, 28, 34, 8, 21, 52, 60, 65, 31, 33, 29, 23, 75, 46, 49, 58, 40, 61, 67, 57, 71, 63, 68, 22, 74, 7, 37, 38, 11, 13, 20, 16, 15, 48, 62, 66, 69, 45], 'cur_cost': 401932.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402138.0, 'intermediate_solutions': [{'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 72, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 7], 'cur_cost': 198408.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 68, 73, 75, 74, 1, 22, 21, 20, 24, 23, 45, 44, 46, 47, 43, 26, 25, 31, 32, 38, 37, 16, 14, 67, 66, 69, 70, 71, 72], 'cur_cost': 192038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 4, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 60, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 183917.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [68, 11, 14, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28, 55, 30, 65, 64, 4, 5, 67, 42, 27, 10, 29, 32, 0, 41, 52, 2, 63, 31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53, 8, 59, 3, 46, 62, 24, 18, 6, 48, 7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20, 35, 22, 26, 73, 44, 15, 9, 1], 'cur_cost': 584279.0, 'intermediate_solutions': [{'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 0, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 15, 11, 24, 75, 9, 40, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 410871.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 57, 46, 66, 49, 44, 62, 48, 64, 39, 40, 9, 75, 24, 11, 0, 2, 29, 70, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 412378.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 71, 68, 58, 32, 4, 21, 41, 61, 28, 30, 35, 27, 37, 60, 42, 54, 53, 65, 34, 26, 50, 51, 43, 45, 22, 20, 19, 8, 15, 17, 33, 18, 16, 31, 5, 25, 59, 56, 36, 29, 2, 0, 11, 24, 75, 9, 39, 64, 48, 62, 44, 49, 66, 46, 57, 70, 40, 63, 69, 72, 14, 13, 12, 10, 7, 6, 3, 38, 52, 47, 67, 1, 74, 23, 73], 'cur_cost': 412245.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 412601.0, 'intermediate_solutions': [{'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 74, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 21], 'cur_cost': 402545.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 29, 51, 58, 49, 64, 17, 8, 5, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 22, 62, 57, 74], 'cur_cost': 427873.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 24, 47, 27, 41, 54, 32, 34, 18, 36, 25, 45, 19, 12, 30, 6, 0, 44, 55, 28, 40, 16, 38, 53, 31, 15, 60, 72, 65, 61, 70, 66, 67, 26, 52, 63, 35, 5, 29, 51, 58, 49, 64, 17, 8, 2, 9, 33, 59, 50, 23, 43, 39, 4, 3, 21, 75, 10, 7, 37, 13, 14, 1, 73, 56, 71, 42, 20, 46, 68, 69, 48, 57, 62, 22, 74], 'cur_cost': 416553.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 47, 34, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7], dtype=int64), 'cur_cost': 581740.0, 'intermediate_solutions': [{'tour': array([75, 50, 17, 16, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 590332.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 75, 50, 17, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 596590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 16, 75, 50, 17, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 593277.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 16, 75, 50, 30, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 591857.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 30, 16, 75, 50, 56, 63, 36, 40, 42, 24, 72, 14, 13, 15,  1, 66,
       55, 23,  5, 32, 70,  0, 27, 46, 59,  2, 68,  6, 19, 29, 35,  8, 47,
       10,  3, 60, 33, 49, 74, 39, 25, 18, 41, 28,  9, 37, 38, 64, 45, 57,
       31, 61, 52, 11, 54,  4, 69, 43, 34,  7, 58, 51, 65, 22, 44, 12, 67,
       20, 62, 71, 73, 21, 53, 48, 26]), 'cur_cost': 586587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136183.0, 'intermediate_solutions': [{'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 28, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 40, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 162924.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 32, 31, 37, 26, 25, 15, 73], 'cur_cost': 152863.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 53, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 73], 'cur_cost': 152631.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 48, 74, 61,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15], dtype=int64), 'cur_cost': 589515.0, 'intermediate_solutions': [{'tour': array([30, 27, 60, 68, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 581484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([68, 30, 27, 60, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 582505.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([63, 68, 30, 27, 60, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 584652.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([60, 68, 30, 27, 63, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 586617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([60, 63, 68, 30, 27, 11, 22, 18,  0,  6,  1, 44, 29, 23,  4, 53, 51,
       48, 71, 47, 69, 39, 55, 20,  2, 46, 58, 45, 40, 59, 54, 28, 10, 72,
       12, 35, 15,  3, 24, 19, 49, 13, 26,  9, 52,  8, 43, 75, 61, 33, 16,
       56, 65, 31, 37, 17, 36,  7, 70,  5, 62, 34, 67, 50, 73, 41, 25, 57,
       66, 32, 21, 38, 74, 64, 42, 14]), 'cur_cost': 575499.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 147422.0, 'intermediate_solutions': [{'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 8, 35, 36, 17, 16, 10, 11, 14, 15, 13, 34, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 175342.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 12, 20, 24, 23, 21, 22, 1, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 70, 48, 49, 66, 67, 71, 72, 69, 75, 74], 'cur_cost': 176596.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 12, 4, 20, 24, 23, 21, 22, 1, 2, 3, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 14, 15, 13, 8, 5, 6, 7, 73, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 68, 67, 66, 49, 48, 70, 71, 72, 69, 75, 74], 'cur_cost': 159251.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:20,236 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:20,236 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:20,240 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=136183.000, 多样性=0.976
2025-08-05 09:52:20,240 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:20,240 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:20,240 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:20,241 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.0007981043247442405, 'best_improvement': 0.07435325784043174}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007138607971445478}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.05894950228528473, 'recent_improvements': [0.09123455001120373, 0.07581582574439895, -0.026664454559365714], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 108444, 'new_best_cost': 108444, 'quality_improvement': 0.0, 'old_diversity': 0.8264411027568922, 'new_diversity': 0.8264411027568922, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:20,242 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:20,242 - __main__ - INFO - pr76 开始进化第 4 代
2025-08-05 09:52:20,242 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:20,242 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:20,243 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=136183.000, 多样性=0.976
2025-08-05 09:52:20,244 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:20,247 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-05 09:52:20,247 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:20,249 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.826
2025-08-05 09:52:20,251 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:20,252 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:20,252 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:20,252 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:20,314 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -38990.612, 聚类评分: 0.000, 覆盖率: 0.164, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:20,315 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:20,315 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:20,315 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 09:52:20,338 - visualization.landscape_visualizer - INFO - 插值约束: 116 个点被约束到最小值 108444.00
2025-08-05 09:52:20,478 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_144_20250805_095220.html
2025-08-05 09:52:20,545 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_144_20250805_095220.html
2025-08-05 09:52:20,546 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 144
2025-08-05 09:52:20,546 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:20,546 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2949秒
2025-08-05 09:52:20,546 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.11764705882352941, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -38990.61176470587, 'local_optima_density': 0.11764705882352941, 'gradient_variance': 36206679429.225746, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1638, 'fitness_entropy': 0.7725760985994748, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -38990.612)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.164)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358740.3151429, 'performance_metrics': {}}}
2025-08-05 09:52:20,546 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:20,547 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:20,547 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:20,547 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:20,547 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,547 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:20,547 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,547 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:20,548 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:20,548 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 09:52:20,548 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:20,548 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:20,548 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:20,548 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:20,548 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:20,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,551 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154454.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,552 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 20, 15, 14, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 154454.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 68, 46, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 150327.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 9, 8, 7, 6, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 164475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 67, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 169012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,552 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 154454.00)
2025-08-05 09:52:20,552 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:20,552 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:20,552 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 531480.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,555 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [55, 18, 61, 15, 14, 28, 31, 11, 10, 12, 43, 3, 26, 48, 34, 60, 0, 52, 1, 44, 23, 41, 42, 56, 58, 27, 30, 5, 19, 4, 21, 45, 68, 54, 22, 16, 65, 70, 63, 17, 36, 37, 33, 72, 39, 35, 59, 9, 57, 2, 62, 64, 7, 32, 73, 6, 50, 46, 69, 25, 71, 75, 66, 53, 67, 74, 47, 20, 24, 40, 51, 13, 49, 38, 29, 8], 'cur_cost': 531480.0, 'intermediate_solutions': [{'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 64, 35, 41, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 74, 44], 'cur_cost': 573139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 35, 64, 44, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 74, 41], 'cur_cost': 567612.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 64, 35, 44, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 45, 74, 41], 'cur_cost': 578396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,555 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 531480.00)
2025-08-05 09:52:20,555 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:20,555 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:20,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,561 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:20,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 392372.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,563 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [49, 55, 65, 56, 32, 37, 31, 17, 38, 53, 42, 29, 16, 25, 41, 48, 66, 62, 43, 64, 72, 54, 36, 10, 12, 2, 9, 8, 6, 0, 44, 20, 28, 51, 26, 50, 27, 4, 19, 18, 15, 1, 23, 7, 21, 22, 34, 59, 57, 52, 40, 61, 30, 47, 60, 14, 5, 33, 58, 67, 24, 68, 46, 39, 11, 13, 35, 3, 73, 45, 75, 74, 63, 70, 71, 69], 'cur_cost': 392372.0, 'intermediate_solutions': [{'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 14, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 61, 15, 73, 69], 'cur_cost': 209370.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 180977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 28, 29, 30, 18, 19, 4, 5, 27, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 183446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,564 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 392372.00)
2025-08-05 09:52:20,564 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:20,564 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:20,564 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,566 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 163902.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,568 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 14, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 15, 8, 5, 6, 7, 2, 3, 4, 19, 25, 26, 32, 31, 37, 38, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 163902.0, 'intermediate_solutions': [{'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 75, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 36, 72], 'cur_cost': 389849.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 55, 41, 56, 42, 30, 7, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 416683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 33, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,568 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 163902.00)
2025-08-05 09:52:20,568 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:20,568 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,568 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,569 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 559339.0
2025-08-05 09:52:20,581 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,581 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,581 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,585 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,585 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 20, 15, 14, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 154454.0}, {'tour': [55, 18, 61, 15, 14, 28, 31, 11, 10, 12, 43, 3, 26, 48, 34, 60, 0, 52, 1, 44, 23, 41, 42, 56, 58, 27, 30, 5, 19, 4, 21, 45, 68, 54, 22, 16, 65, 70, 63, 17, 36, 37, 33, 72, 39, 35, 59, 9, 57, 2, 62, 64, 7, 32, 73, 6, 50, 46, 69, 25, 71, 75, 66, 53, 67, 74, 47, 20, 24, 40, 51, 13, 49, 38, 29, 8], 'cur_cost': 531480.0}, {'tour': [49, 55, 65, 56, 32, 37, 31, 17, 38, 53, 42, 29, 16, 25, 41, 48, 66, 62, 43, 64, 72, 54, 36, 10, 12, 2, 9, 8, 6, 0, 44, 20, 28, 51, 26, 50, 27, 4, 19, 18, 15, 1, 23, 7, 21, 22, 34, 59, 57, 52, 40, 61, 30, 47, 60, 14, 5, 33, 58, 67, 24, 68, 46, 39, 11, 13, 35, 3, 73, 45, 75, 74, 63, 70, 71, 69], 'cur_cost': 392372.0}, {'tour': [0, 9, 14, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 15, 8, 5, 6, 7, 2, 3, 4, 19, 25, 26, 32, 31, 37, 38, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 163902.0}, {'tour': array([21, 51, 67, 50, 71, 53, 47, 69, 30, 31, 57, 64, 33, 46,  1, 45, 68,
        9, 61, 73, 36,  4,  0, 19, 29, 70, 20, 35, 17, 66, 22, 59, 56, 40,
       54, 23, 43,  5, 38,  8,  6, 11, 65, 44, 15, 24, 42, 63, 37, 13, 14,
       26, 75, 27, 55, 58, 48, 74, 49,  3, 62, 18, 72, 39, 52, 32, 41,  7,
       34, 12, 28, 16,  2, 25, 10, 60], dtype=int64), 'cur_cost': 559339.0}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 412601.0}, {'tour': [26, 47, 34, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23, 6, 49, 27, 70, 65, 0, 13, 45, 5, 57, 64, 32, 61, 38, 16, 72, 63, 9, 42, 46, 8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17, 4, 60, 22, 59, 30, 55, 25, 41, 74, 2, 14, 11, 18, 3, 73, 66, 35, 75, 48, 71, 36, 69, 37, 50, 1, 7], 'cur_cost': 581740.0}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136183.0}, {'tour': [28, 48, 74, 61, 6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72, 56, 4, 11, 33, 20, 37, 69, 16, 36, 32, 3, 0, 51, 14, 24, 26, 19, 23, 25, 30, 31, 8, 71, 41, 29, 73, 52, 5, 68, 64, 45, 66, 35, 46, 49, 2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53, 40, 1, 42, 9, 7, 59, 34, 15], 'cur_cost': 589515.0}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 147422.0}]
2025-08-05 09:52:20,587 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,587 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 372, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 372, 'cache_hits': 0, 'similarity_calculations': 1930, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,589 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([21, 51, 67, 50, 71, 53, 47, 69, 30, 31, 57, 64, 33, 46,  1, 45, 68,
        9, 61, 73, 36,  4,  0, 19, 29, 70, 20, 35, 17, 66, 22, 59, 56, 40,
       54, 23, 43,  5, 38,  8,  6, 11, 65, 44, 15, 24, 42, 63, 37, 13, 14,
       26, 75, 27, 55, 58, 48, 74, 49,  3, 62, 18, 72, 39, 52, 32, 41,  7,
       34, 12, 28, 16,  2, 25, 10, 60], dtype=int64), 'cur_cost': 559339.0, 'intermediate_solutions': [{'tour': array([14, 11, 68, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 594041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 14, 11, 68, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 594407.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 12, 14, 11, 68, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 576475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([68, 12, 14, 11, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 586578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([68, 13, 12, 14, 11, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 584466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,589 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 559339.00)
2025-08-05 09:52:20,589 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:52:20,589 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:52:20,589 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,592 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 09:52:20,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,592 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,593 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,593 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,593 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145557.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,593 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 7, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145557.0, 'intermediate_solutions': [{'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 10, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 5, 28, 12, 13, 74, 45, 71], 'cur_cost': 406769.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 8, 24, 11, 3, 38, 64, 58, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 423061.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 35, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 415716.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,594 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 145557.00)
2025-08-05 09:52:20,594 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:52:20,594 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,594 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,594 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 576918.0
2025-08-05 09:52:20,607 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,608 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,608 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,611 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,612 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 20, 15, 14, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 154454.0}, {'tour': [55, 18, 61, 15, 14, 28, 31, 11, 10, 12, 43, 3, 26, 48, 34, 60, 0, 52, 1, 44, 23, 41, 42, 56, 58, 27, 30, 5, 19, 4, 21, 45, 68, 54, 22, 16, 65, 70, 63, 17, 36, 37, 33, 72, 39, 35, 59, 9, 57, 2, 62, 64, 7, 32, 73, 6, 50, 46, 69, 25, 71, 75, 66, 53, 67, 74, 47, 20, 24, 40, 51, 13, 49, 38, 29, 8], 'cur_cost': 531480.0}, {'tour': [49, 55, 65, 56, 32, 37, 31, 17, 38, 53, 42, 29, 16, 25, 41, 48, 66, 62, 43, 64, 72, 54, 36, 10, 12, 2, 9, 8, 6, 0, 44, 20, 28, 51, 26, 50, 27, 4, 19, 18, 15, 1, 23, 7, 21, 22, 34, 59, 57, 52, 40, 61, 30, 47, 60, 14, 5, 33, 58, 67, 24, 68, 46, 39, 11, 13, 35, 3, 73, 45, 75, 74, 63, 70, 71, 69], 'cur_cost': 392372.0}, {'tour': [0, 9, 14, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 15, 8, 5, 6, 7, 2, 3, 4, 19, 25, 26, 32, 31, 37, 38, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 163902.0}, {'tour': array([21, 51, 67, 50, 71, 53, 47, 69, 30, 31, 57, 64, 33, 46,  1, 45, 68,
        9, 61, 73, 36,  4,  0, 19, 29, 70, 20, 35, 17, 66, 22, 59, 56, 40,
       54, 23, 43,  5, 38,  8,  6, 11, 65, 44, 15, 24, 42, 63, 37, 13, 14,
       26, 75, 27, 55, 58, 48, 74, 49,  3, 62, 18, 72, 39, 52, 32, 41,  7,
       34, 12, 28, 16,  2, 25, 10, 60], dtype=int64), 'cur_cost': 559339.0}, {'tour': [0, 5, 7, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145557.0}, {'tour': array([50, 67,  9, 23,  2, 62, 32,  4, 52, 13, 53,  3, 24, 55, 75, 68, 48,
       11, 59, 39, 63, 47, 66, 74, 37, 34, 69, 27, 35, 40, 45,  0, 51,  5,
       25, 44, 64, 46, 72, 15, 61, 33, 19, 17, 36,  8, 31, 73, 38, 56, 28,
       42, 22, 43, 58, 14,  6, 12, 60, 21, 30,  1, 57,  7, 71, 29, 10, 16,
       54, 49, 70, 65, 18, 26, 41, 20], dtype=int64), 'cur_cost': 576918.0}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136183.0}, {'tour': [28, 48, 74, 61, 6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72, 56, 4, 11, 33, 20, 37, 69, 16, 36, 32, 3, 0, 51, 14, 24, 26, 19, 23, 25, 30, 31, 8, 71, 41, 29, 73, 52, 5, 68, 64, 45, 66, 35, 46, 49, 2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53, 40, 1, 42, 9, 7, 59, 34, 15], 'cur_cost': 589515.0}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 147422.0}]
2025-08-05 09:52:20,613 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,613 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 373, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 373, 'cache_hits': 0, 'similarity_calculations': 1938, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,614 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([50, 67,  9, 23,  2, 62, 32,  4, 52, 13, 53,  3, 24, 55, 75, 68, 48,
       11, 59, 39, 63, 47, 66, 74, 37, 34, 69, 27, 35, 40, 45,  0, 51,  5,
       25, 44, 64, 46, 72, 15, 61, 33, 19, 17, 36,  8, 31, 73, 38, 56, 28,
       42, 22, 43, 58, 14,  6, 12, 60, 21, 30,  1, 57,  7, 71, 29, 10, 16,
       54, 49, 70, 65, 18, 26, 41, 20], dtype=int64), 'cur_cost': 576918.0, 'intermediate_solutions': [{'tour': array([34, 47, 26, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 580218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 34, 47, 26, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 578619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([54, 29, 34, 47, 26, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 586166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26, 29, 34, 47, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 579792.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 54, 29, 34, 47, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 580378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,614 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 576918.00)
2025-08-05 09:52:20,614 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:20,614 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:20,615 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,616 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 09:52:20,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,618 - ExplorationExpert - INFO - 探索路径生成完成，成本: 558583.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,618 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [49, 9, 65, 56, 32, 28, 27, 42, 12, 43, 29, 16, 48, 34, 64, 66, 52, 57, 44, 23, 41, 36, 60, 2, 30, 5, 19, 4, 35, 45, 68, 54, 22, 50, 6, 70, 63, 17, 15, 1, 33, 72, 39, 8, 59, 26, 62, 61, 7, 18, 73, 67, 69, 25, 71, 75, 46, 53, 11, 20, 21, 3, 40, 38, 51, 31, 14, 55, 0, 10, 37, 74, 24, 13, 47, 58], 'cur_cost': 558583.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 61, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 36, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 161961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 75, 74, 73, 14, 15, 25, 26, 32, 31, 37, 38, 72, 71, 70, 69, 66, 67, 68, 46, 47, 43], 'cur_cost': 151083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 60, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,618 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 558583.00)
2025-08-05 09:52:20,618 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:20,618 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:20,619 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:20,619 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 602086.0
2025-08-05 09:52:20,629 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:20,630 - ExploitationExpert - INFO - res_population_costs: [108444, 109916, 110745, 112036, 112925, 112931, 114036.0]
2025-08-05 09:52:20,630 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3, 29, 30, 18, 19,  4,  5,  6,  7,  8,  9, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 40, 59, 58, 60,
       61, 57, 33, 34, 32, 31, 28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 56, 55, 54, 51, 52, 53, 41, 42, 27,
       26, 25, 28, 31, 32, 34, 33, 57, 61, 60, 58, 59, 40, 39, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9, 18, 30, 29, 19,  4,
        5,  7,  6,  2,  3,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 16,
       17, 36, 35, 37, 38, 39, 40, 59, 58, 60, 61, 57, 33, 34, 10,  9,  4,
       19, 18, 30, 29, 28, 31, 32, 27, 25, 26, 42, 41, 53, 52, 51, 54, 55,
       56, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 56, 57, 61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 26, 25, 27, 42, 41, 53, 52, 47,
       46, 68, 67, 69, 66, 49, 48, 51, 54, 55, 50, 65, 64, 70, 71, 72, 63,
       62, 61, 60, 56, 57, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 32,
       31, 28, 29, 30, 18, 19,  4,  9, 10, 16, 15, 14, 73, 13, 12, 11,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8, 11, 12, 13, 73, 14, 15, 17,
       36, 35, 16, 10,  9,  4, 19, 18, 30, 29, 28, 27, 32, 31, 34, 33, 37,
       38, 39, 40, 59, 58, 57, 56, 60, 61, 62, 63, 72, 71, 70, 64, 65, 50,
       55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47, 43, 44,
       45, 23, 26, 25, 24, 20, 21, 22], dtype=int64)]
2025-08-05 09:52:20,633 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:20,633 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 20, 15, 14, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 154454.0}, {'tour': [55, 18, 61, 15, 14, 28, 31, 11, 10, 12, 43, 3, 26, 48, 34, 60, 0, 52, 1, 44, 23, 41, 42, 56, 58, 27, 30, 5, 19, 4, 21, 45, 68, 54, 22, 16, 65, 70, 63, 17, 36, 37, 33, 72, 39, 35, 59, 9, 57, 2, 62, 64, 7, 32, 73, 6, 50, 46, 69, 25, 71, 75, 66, 53, 67, 74, 47, 20, 24, 40, 51, 13, 49, 38, 29, 8], 'cur_cost': 531480.0}, {'tour': [49, 55, 65, 56, 32, 37, 31, 17, 38, 53, 42, 29, 16, 25, 41, 48, 66, 62, 43, 64, 72, 54, 36, 10, 12, 2, 9, 8, 6, 0, 44, 20, 28, 51, 26, 50, 27, 4, 19, 18, 15, 1, 23, 7, 21, 22, 34, 59, 57, 52, 40, 61, 30, 47, 60, 14, 5, 33, 58, 67, 24, 68, 46, 39, 11, 13, 35, 3, 73, 45, 75, 74, 63, 70, 71, 69], 'cur_cost': 392372.0}, {'tour': [0, 9, 14, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 15, 8, 5, 6, 7, 2, 3, 4, 19, 25, 26, 32, 31, 37, 38, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 163902.0}, {'tour': array([21, 51, 67, 50, 71, 53, 47, 69, 30, 31, 57, 64, 33, 46,  1, 45, 68,
        9, 61, 73, 36,  4,  0, 19, 29, 70, 20, 35, 17, 66, 22, 59, 56, 40,
       54, 23, 43,  5, 38,  8,  6, 11, 65, 44, 15, 24, 42, 63, 37, 13, 14,
       26, 75, 27, 55, 58, 48, 74, 49,  3, 62, 18, 72, 39, 52, 32, 41,  7,
       34, 12, 28, 16,  2, 25, 10, 60], dtype=int64), 'cur_cost': 559339.0}, {'tour': [0, 5, 7, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145557.0}, {'tour': array([50, 67,  9, 23,  2, 62, 32,  4, 52, 13, 53,  3, 24, 55, 75, 68, 48,
       11, 59, 39, 63, 47, 66, 74, 37, 34, 69, 27, 35, 40, 45,  0, 51,  5,
       25, 44, 64, 46, 72, 15, 61, 33, 19, 17, 36,  8, 31, 73, 38, 56, 28,
       42, 22, 43, 58, 14,  6, 12, 60, 21, 30,  1, 57,  7, 71, 29, 10, 16,
       54, 49, 70, 65, 18, 26, 41, 20], dtype=int64), 'cur_cost': 576918.0}, {'tour': [49, 9, 65, 56, 32, 28, 27, 42, 12, 43, 29, 16, 48, 34, 64, 66, 52, 57, 44, 23, 41, 36, 60, 2, 30, 5, 19, 4, 35, 45, 68, 54, 22, 50, 6, 70, 63, 17, 15, 1, 33, 72, 39, 8, 59, 26, 62, 61, 7, 18, 73, 67, 69, 25, 71, 75, 46, 53, 11, 20, 21, 3, 40, 38, 51, 31, 14, 55, 0, 10, 37, 74, 24, 13, 47, 58], 'cur_cost': 558583.0}, {'tour': array([18, 31, 37, 62, 24, 11, 54, 48, 13, 59, 36, 35, 16,  2,  1, 41, 22,
        4, 69, 28, 75, 65, 55, 19, 47, 33, 32, 52,  0, 72, 60, 46,  5, 68,
       12, 67, 42, 53, 45, 57, 58, 10, 34, 70, 30, 74, 66, 64, 21, 71, 29,
       51, 39, 44, 56, 49, 40, 73, 25, 27, 43,  9, 20, 26,  8,  3, 15, 23,
       63, 38,  6, 50, 14,  7, 61, 17], dtype=int64), 'cur_cost': 602086.0}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 147422.0}]
2025-08-05 09:52:20,635 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:20,635 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 374, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 374, 'cache_hits': 0, 'similarity_calculations': 1947, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:20,636 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([18, 31, 37, 62, 24, 11, 54, 48, 13, 59, 36, 35, 16,  2,  1, 41, 22,
        4, 69, 28, 75, 65, 55, 19, 47, 33, 32, 52,  0, 72, 60, 46,  5, 68,
       12, 67, 42, 53, 45, 57, 58, 10, 34, 70, 30, 74, 66, 64, 21, 71, 29,
       51, 39, 44, 56, 49, 40, 73, 25, 27, 43,  9, 20, 26,  8,  3, 15, 23,
       63, 38,  6, 50, 14,  7, 61, 17], dtype=int64), 'cur_cost': 602086.0, 'intermediate_solutions': [{'tour': array([74, 48, 28, 61,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 584488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([61, 74, 48, 28,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 586468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 61, 74, 48, 28, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 583622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 61, 74, 48,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 590066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28,  6, 61, 74, 48, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 580601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:20,636 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 602086.00)
2025-08-05 09:52:20,636 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:20,636 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:20,636 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:20,645 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 09:52:20,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:20,646 - ExplorationExpert - INFO - 探索路径生成完成，成本: 380147.0, 路径长度: 76, 收集中间解: 3
2025-08-05 09:52:20,647 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [52, 26, 3, 20, 11, 37, 57, 51, 54, 42, 34, 32, 17, 33, 38, 12, 18, 15, 10, 40, 62, 49, 59, 56, 31, 24, 21, 2, 5, 41, 43, 45, 29, 23, 1, 75, 16, 53, 58, 63, 61, 70, 69, 64, 48, 47, 19, 25, 36, 4, 9, 8, 22, 44, 55, 27, 39, 14, 7, 30, 6, 46, 68, 60, 50, 28, 0, 73, 13, 74, 35, 65, 67, 66, 71, 72], 'cur_cost': 380147.0, 'intermediate_solutions': [{'tour': [0, 20, 2, 10, 11, 12, 13, 14, 62, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 15, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 185506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 75, 74, 25, 26, 32, 31, 37, 73], 'cur_cost': 159310.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 4, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 168367.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:20,647 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 380147.00)
2025-08-05 09:52:20,647 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:20,647 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:20,650 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 15, 14, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 154454.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 68, 46, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 150327.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 9, 8, 7, 6, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 164475.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 15, 14, 12, 13, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 67, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 169012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [55, 18, 61, 15, 14, 28, 31, 11, 10, 12, 43, 3, 26, 48, 34, 60, 0, 52, 1, 44, 23, 41, 42, 56, 58, 27, 30, 5, 19, 4, 21, 45, 68, 54, 22, 16, 65, 70, 63, 17, 36, 37, 33, 72, 39, 35, 59, 9, 57, 2, 62, 64, 7, 32, 73, 6, 50, 46, 69, 25, 71, 75, 66, 53, 67, 74, 47, 20, 24, 40, 51, 13, 49, 38, 29, 8], 'cur_cost': 531480.0, 'intermediate_solutions': [{'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 64, 35, 41, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 74, 44], 'cur_cost': 573139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 45, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 35, 64, 44, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 74, 41], 'cur_cost': 567612.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 36, 61, 33, 17, 23, 14, 66, 5, 12, 59, 62, 26, 48, 34, 43, 0, 52, 1, 55, 39, 18, 24, 56, 58, 72, 60, 13, 51, 31, 21, 68, 54, 22, 16, 30, 27, 63, 71, 42, 37, 29, 8, 64, 35, 44, 20, 10, 46, 57, 15, 7, 32, 73, 9, 4, 28, 69, 25, 70, 75, 38, 53, 67, 11, 47, 19, 6, 3, 40, 65, 49, 50, 45, 74, 41], 'cur_cost': 578396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [49, 55, 65, 56, 32, 37, 31, 17, 38, 53, 42, 29, 16, 25, 41, 48, 66, 62, 43, 64, 72, 54, 36, 10, 12, 2, 9, 8, 6, 0, 44, 20, 28, 51, 26, 50, 27, 4, 19, 18, 15, 1, 23, 7, 21, 22, 34, 59, 57, 52, 40, 61, 30, 47, 60, 14, 5, 33, 58, 67, 24, 68, 46, 39, 11, 13, 35, 3, 73, 45, 75, 74, 63, 70, 71, 69], 'cur_cost': 392372.0, 'intermediate_solutions': [{'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 14, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 61, 15, 73, 69], 'cur_cost': 209370.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 180977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 7, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 28, 29, 30, 18, 19, 4, 5, 27, 6, 2, 3, 9, 8, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75, 12, 14, 15, 73, 69], 'cur_cost': 183446.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 14, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 15, 8, 5, 6, 7, 2, 3, 4, 19, 25, 26, 32, 31, 37, 38, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 20, 21, 22, 1, 74, 75, 73, 72, 71, 70, 69], 'cur_cost': 163902.0, 'intermediate_solutions': [{'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 75, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 36, 72], 'cur_cost': 389849.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 33, 18, 55, 41, 56, 42, 30, 7, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 416683.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [68, 25, 8, 13, 1, 28, 31, 35, 9, 73, 16, 3, 2, 14, 11, 34, 33, 15, 12, 20, 44, 23, 4, 27, 54, 40, 29, 6, 5, 53, 39, 37, 51, 52, 61, 60, 71, 65, 70, 49, 46, 32, 50, 18, 7, 30, 42, 56, 41, 55, 63, 64, 67, 43, 26, 48, 59, 62, 66, 57, 38, 36, 21, 19, 0, 74, 10, 22, 24, 47, 69, 45, 58, 17, 75, 72], 'cur_cost': 402375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 51, 67, 50, 71, 53, 47, 69, 30, 31, 57, 64, 33, 46,  1, 45, 68,
        9, 61, 73, 36,  4,  0, 19, 29, 70, 20, 35, 17, 66, 22, 59, 56, 40,
       54, 23, 43,  5, 38,  8,  6, 11, 65, 44, 15, 24, 42, 63, 37, 13, 14,
       26, 75, 27, 55, 58, 48, 74, 49,  3, 62, 18, 72, 39, 52, 32, 41,  7,
       34, 12, 28, 16,  2, 25, 10, 60], dtype=int64), 'cur_cost': 559339.0, 'intermediate_solutions': [{'tour': array([14, 11, 68, 12, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 594041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 14, 11, 68, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 594407.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 12, 14, 11, 68, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 576475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([68, 12, 14, 11, 13, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 586578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([68, 13, 12, 14, 11, 23, 16, 17, 43, 39, 34, 49, 58, 56, 40, 60, 28,
       55, 30, 65, 64,  4,  5, 67, 42, 27, 10, 29, 32,  0, 41, 52,  2, 63,
       31, 50, 70, 47, 38, 66, 69, 61, 74, 72, 21, 51, 57, 53,  8, 59,  3,
       46, 62, 24, 18,  6, 48,  7, 71, 37, 75, 36, 45, 54, 19, 33, 25, 20,
       35, 22, 26, 73, 44, 15,  9,  1]), 'cur_cost': 584466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 7, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 145557.0, 'intermediate_solutions': [{'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 10, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 5, 28, 12, 13, 74, 45, 71], 'cur_cost': 406769.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 35, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 8, 24, 11, 3, 38, 64, 58, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 423061.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 40, 31, 4, 18, 14, 41, 19, 29, 59, 37, 34, 36, 5, 73, 39, 63, 26, 27, 50, 44, 47, 51, 62, 57, 58, 64, 38, 3, 35, 11, 24, 8, 17, 15, 30, 42, 16, 1, 32, 25, 9, 20, 22, 75, 6, 53, 52, 65, 49, 33, 56, 72, 68, 23, 48, 46, 21, 43, 69, 70, 61, 54, 66, 60, 67, 2, 0, 7, 10, 28, 12, 13, 74, 45, 71], 'cur_cost': 415716.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 67,  9, 23,  2, 62, 32,  4, 52, 13, 53,  3, 24, 55, 75, 68, 48,
       11, 59, 39, 63, 47, 66, 74, 37, 34, 69, 27, 35, 40, 45,  0, 51,  5,
       25, 44, 64, 46, 72, 15, 61, 33, 19, 17, 36,  8, 31, 73, 38, 56, 28,
       42, 22, 43, 58, 14,  6, 12, 60, 21, 30,  1, 57,  7, 71, 29, 10, 16,
       54, 49, 70, 65, 18, 26, 41, 20], dtype=int64), 'cur_cost': 576918.0, 'intermediate_solutions': [{'tour': array([34, 47, 26, 29, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 580218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 34, 47, 26, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 578619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([54, 29, 34, 47, 26, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 586166.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26, 29, 34, 47, 54, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 579792.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 54, 29, 34, 47, 67, 51, 33, 43, 28, 21, 62, 19, 23,  6, 49, 27,
       70, 65,  0, 13, 45,  5, 57, 64, 32, 61, 38, 16, 72, 63,  9, 42, 46,
        8, 10, 68, 31, 58, 56, 52, 44, 39, 40, 20, 15, 24, 53, 12, 17,  4,
       60, 22, 59, 30, 55, 25, 41, 74,  2, 14, 11, 18,  3, 73, 66, 35, 75,
       48, 71, 36, 69, 37, 50,  1,  7]), 'cur_cost': 580378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [49, 9, 65, 56, 32, 28, 27, 42, 12, 43, 29, 16, 48, 34, 64, 66, 52, 57, 44, 23, 41, 36, 60, 2, 30, 5, 19, 4, 35, 45, 68, 54, 22, 50, 6, 70, 63, 17, 15, 1, 33, 72, 39, 8, 59, 26, 62, 61, 7, 18, 73, 67, 69, 25, 71, 75, 46, 53, 11, 20, 21, 3, 40, 38, 51, 31, 14, 55, 0, 10, 37, 74, 24, 13, 47, 58], 'cur_cost': 558583.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 61, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 36, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 161961.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 75, 74, 73, 14, 15, 25, 26, 32, 31, 37, 38, 72, 71, 70, 69, 66, 67, 68, 46, 47, 43], 'cur_cost': 151083.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 5, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 60, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 6, 7, 2, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 136927.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 31, 37, 62, 24, 11, 54, 48, 13, 59, 36, 35, 16,  2,  1, 41, 22,
        4, 69, 28, 75, 65, 55, 19, 47, 33, 32, 52,  0, 72, 60, 46,  5, 68,
       12, 67, 42, 53, 45, 57, 58, 10, 34, 70, 30, 74, 66, 64, 21, 71, 29,
       51, 39, 44, 56, 49, 40, 73, 25, 27, 43,  9, 20, 26,  8,  3, 15, 23,
       63, 38,  6, 50, 14,  7, 61, 17], dtype=int64), 'cur_cost': 602086.0, 'intermediate_solutions': [{'tour': array([74, 48, 28, 61,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 584488.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([61, 74, 48, 28,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 586468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 61, 74, 48, 28, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 583622.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 61, 74, 48,  6, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 590066.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28,  6, 61, 74, 48, 67, 60, 12, 62, 54, 70, 50, 18, 57, 47, 43, 72,
       56,  4, 11, 33, 20, 37, 69, 16, 36, 32,  3,  0, 51, 14, 24, 26, 19,
       23, 25, 30, 31,  8, 71, 41, 29, 73, 52,  5, 68, 64, 45, 66, 35, 46,
       49,  2, 13, 75, 44, 63, 10, 65, 39, 58, 38, 21, 17, 22, 55, 27, 53,
       40,  1, 42,  9,  7, 59, 34, 15]), 'cur_cost': 580601.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [52, 26, 3, 20, 11, 37, 57, 51, 54, 42, 34, 32, 17, 33, 38, 12, 18, 15, 10, 40, 62, 49, 59, 56, 31, 24, 21, 2, 5, 41, 43, 45, 29, 23, 1, 75, 16, 53, 58, 63, 61, 70, 69, 64, 48, 47, 19, 25, 36, 4, 9, 8, 22, 44, 55, 27, 39, 14, 7, 30, 6, 46, 68, 60, 50, 28, 0, 73, 13, 74, 35, 65, 67, 66, 71, 72], 'cur_cost': 380147.0, 'intermediate_solutions': [{'tour': [0, 20, 2, 10, 11, 12, 13, 14, 62, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 15, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 185506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 75, 74, 25, 26, 32, 31, 37, 73], 'cur_cost': 159310.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 5, 6, 7, 8, 9, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 4, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 168367.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:20,651 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:20,651 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:20,655 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145557.000, 多样性=0.971
2025-08-05 09:52:20,655 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:20,655 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:20,655 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:20,656 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.058065781505154235, 'best_improvement': -0.06883384857140759}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005092869982025218}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03750886070982736, 'recent_improvements': [0.07581582574439895, -0.026664454559365714, 0.0007981043247442405], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 108444, 'new_best_cost': 108444, 'quality_improvement': 0.0, 'old_diversity': 0.8264411027568922, 'new_diversity': 0.8264411027568922, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:20,658 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:20,658 - __main__ - INFO - pr76 开始进化第 5 代
2025-08-05 09:52:20,658 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:20,658 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:20,659 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=145557.000, 多样性=0.971
2025-08-05 09:52:20,660 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:20,666 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.971
2025-08-05 09:52:20,666 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:20,669 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.826
2025-08-05 09:52:20,671 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:20,672 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:20,672 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:20,672 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:20,727 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.118, 适应度梯度: -57834.212, 聚类评分: 0.000, 覆盖率: 0.165, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:20,727 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:20,727 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:20,727 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 09:52:20,734 - visualization.landscape_visualizer - INFO - 插值约束: 610 个点被约束到最小值 108444.00
