2025-08-05 10:29:13,823 - __main__ - INFO - pr76 开始进化第 1 代
2025-08-05 10:29:13,823 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:13,825 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:13,829 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=149569.000, 多样性=0.984
2025-08-05 10:29:13,832 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:13,839 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.984
2025-08-05 10:29:13,842 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:13,846 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:13,847 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:13,847 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:13,847 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:13,876 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -35789.400, 聚类评分: 0.000, 覆盖率: 0.159, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:13,877 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:13,877 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:13,877 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 10:29:13,883 - visualization.landscape_visualizer - INFO - 插值约束: 119 个点被约束到最小值 149569.00
2025-08-05 10:29:13,885 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 22264.47 → 20961.53
2025-08-05 10:29:14,011 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_141_20250805_102913.html
2025-08-05 10:29:14,080 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_141_20250805_102913.html
2025-08-05 10:29:14,081 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 141
2025-08-05 10:29:14,081 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:14,081 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2351秒
2025-08-05 10:29:14,081 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 282, 'max_size': 500, 'hits': 0, 'misses': 282, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 934, 'misses': 482, 'hit_rate': 0.6596045197740112, 'evictions': 382, 'ttl': 7200}}
2025-08-05 10:29:14,081 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -35789.40000000001, 'local_optima_density': 0.1, 'gradient_variance': 41543041024.520004, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.159, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -35789.400)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.159)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360953.876129, 'performance_metrics': {}}}
2025-08-05 10:29:14,082 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:14,082 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:14,082 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:14,082 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:14,083 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,083 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:14,084 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,084 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:14,084 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:14,084 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:14,084 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,097 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,097 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 395204.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,098 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395204.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,098 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 395204.00)
2025-08-05 10:29:14,099 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:14,099 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:14,099 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,109 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,110 - ExplorationExpert - INFO - 探索路径生成完成，成本: 380311.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,110 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 380311.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,111 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 380311.00)
2025-08-05 10:29:14,111 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:14,111 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:14,111 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,114 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,114 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165213.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,114 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 165213.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,115 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 165213.00)
2025-08-05 10:29:14,115 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:14,115 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:14,115 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,117 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:14,117 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 476996.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,118 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 476996.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,118 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 476996.00)
2025-08-05 10:29:14,118 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:14,118 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,118 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,119 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 610128.0
2025-08-05 10:29:14,132 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:14,132 - ExploitationExpert - INFO - res_population_costs: [113650.0, 112983, 110135]
2025-08-05 10:29:14,132 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,135 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,135 - ExploitationExpert - INFO - populations: [{'tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395204.0}, {'tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 380311.0}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 165213.0}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 476996.0}, {'tour': array([19, 62, 46, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18], dtype=int64), 'cur_cost': 610128.0}, {'tour': array([25, 56, 68, 48,  3, 24,  0, 60,  5, 64, 52, 45,  4, 38, 34, 40, 41,
       67,  1,  7, 47, 59, 73, 66, 44,  9, 27, 72, 69, 22, 65, 53, 37, 61,
       12,  6, 28, 51, 36, 18, 23, 62, 16, 49,  2, 20, 30, 26, 50, 33, 46,
       11, 42, 39, 58, 55, 13, 71, 10, 29, 14, 63, 57, 15, 43, 21, 19, 74,
       70, 31, 32,  8, 54, 17, 75, 35], dtype=int64), 'cur_cost': 591762.0}, {'tour': array([28, 70,  4, 38,  9, 66, 47, 24, 62, 29, 22,  7, 41, 16, 72, 68, 25,
       17, 73,  6, 75, 11,  5, 23, 43, 14, 42, 27, 64, 10, 48, 44, 59, 49,
       20,  1, 52, 74, 56, 40, 46, 60, 50, 37, 39, 58, 21, 26, 31, 35, 13,
       54, 63, 33, 67, 69,  2, 65, 15, 55, 71, 12,  3, 36, 34, 45, 32, 19,
       18, 57,  8, 51, 53,  0, 30, 61], dtype=int64), 'cur_cost': 570999.0}, {'tour': array([71, 12, 51, 69, 44,  6, 52, 62, 72, 23,  7, 42, 73, 21, 58, 50, 41,
       33, 57, 36,  9, 66,  4,  1, 40, 34, 75, 22, 15,  2, 30, 39, 37, 68,
        5, 65, 10,  8, 54, 32, 56, 19, 59, 53, 61, 70, 60, 14, 67, 45, 29,
       11, 63, 18, 28, 26, 25, 31, 43, 27, 64,  0, 16, 20,  3, 74, 47, 13,
       24, 48, 49, 38, 17, 46, 55, 35], dtype=int64), 'cur_cost': 566544.0}, {'tour': array([21, 60, 70, 55, 47,  3, 34, 33, 26,  0, 58, 16, 69,  4, 19, 56, 22,
       25, 43, 65, 48, 27, 38, 51, 66, 24, 59, 45, 42,  9, 35, 54, 73, 71,
       32, 63, 28, 53, 64, 23, 10,  5, 12, 68, 37,  6, 15, 72, 36, 40, 46,
        8,  1, 17, 14, 41,  7, 61, 57, 67, 11, 44, 29, 74, 75, 50, 52, 30,
       31, 39, 49, 62, 18, 20, 13,  2], dtype=int64), 'cur_cost': 564166.0}, {'tour': array([22, 40, 42, 43, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 624338.0}]
2025-08-05 10:29:14,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:14,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 365, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 365, 'cache_hits': 0, 'similarity_calculations': 1902, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,138 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([19, 62, 46, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18], dtype=int64), 'cur_cost': 610128.0, 'intermediate_solutions': [{'tour': array([73, 44, 43, 70, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 593196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([70, 73, 44, 43, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 602626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 70, 73, 44, 43, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 602830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 70, 73, 44, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 609204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 32, 70, 73, 44, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 613480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,138 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 610128.00)
2025-08-05 10:29:14,138 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:14,138 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:14,139 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,148 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,148 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,149 - ExplorationExpert - INFO - 探索路径生成完成，成本: 396835.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,149 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,149 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 396835.00)
2025-08-05 10:29:14,149 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:14,149 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:14,149 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,160 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 414023.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,160 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 414023.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,160 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 414023.00)
2025-08-05 10:29:14,161 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:14,161 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:14,161 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,164 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 168230.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,164 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 168230.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,165 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 168230.00)
2025-08-05 10:29:14,165 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:14,165 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:14,165 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,168 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,168 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154887.0, 路径长度: 76, 收集中间解: 0
2025-08-05 10:29:14,169 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 154887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,169 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 154887.00)
2025-08-05 10:29:14,169 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:14,169 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,169 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,169 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 604030.0
2025-08-05 10:29:14,180 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:14,181 - ExploitationExpert - INFO - res_population_costs: [113650.0, 112983, 110135, 109118.0]
2025-08-05 10:29:14,181 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64)]
2025-08-05 10:29:14,183 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,183 - ExploitationExpert - INFO - populations: [{'tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395204.0}, {'tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 380311.0}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 165213.0}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 476996.0}, {'tour': array([19, 62, 46, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18], dtype=int64), 'cur_cost': 610128.0}, {'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396835.0}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 414023.0}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 168230.0}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 154887.0}, {'tour': array([49, 14, 60, 45, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35], dtype=int64), 'cur_cost': 604030.0}]
2025-08-05 10:29:14,184 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:14,184 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 366, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 366, 'cache_hits': 0, 'similarity_calculations': 1903, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,185 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([49, 14, 60, 45, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35], dtype=int64), 'cur_cost': 604030.0, 'intermediate_solutions': [{'tour': array([42, 40, 22, 43, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 623986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 42, 40, 22, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 628390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 43, 42, 40, 22,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 620748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 43, 42, 40, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 619762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 50, 43, 42, 40,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 621292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,185 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 604030.00)
2025-08-05 10:29:14,185 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:14,185 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:14,187 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395204.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 380311.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 165213.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 476996.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 62, 46, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18], dtype=int64), 'cur_cost': 610128.0, 'intermediate_solutions': [{'tour': array([73, 44, 43, 70, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 593196.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([70, 73, 44, 43, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 602626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 70, 73, 44, 43, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 602830.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43, 70, 73, 44, 32, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 609204.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 32, 70, 73, 44, 40, 13, 56, 68, 31,  7, 71, 20, 62, 29, 34, 74,
       39, 61, 30, 12, 51, 19,  2,  4,  0, 54, 47, 57, 60, 11, 14,  9,  3,
       10, 37, 72, 16, 55, 50, 17, 21, 65, 64, 59, 45,  5, 27,  1, 48,  8,
       58, 22, 66, 28, 24, 35, 25, 49, 15, 46, 41, 67, 42, 23, 75, 52, 26,
       63, 18, 33, 69, 53, 38,  6, 36], dtype=int64), 'cur_cost': 613480.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396835.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 414023.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 168230.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 154887.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 14, 60, 45, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35], dtype=int64), 'cur_cost': 604030.0, 'intermediate_solutions': [{'tour': array([42, 40, 22, 43, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 623986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 42, 40, 22, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 628390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([50, 43, 42, 40, 22,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 620748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 43, 42, 40, 50,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 619762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 50, 43, 42, 40,  8, 12, 33, 52, 44, 63, 48,  7, 34,  4, 37, 14,
       56, 18,  3, 23, 20, 13, 53, 61,  6, 65, 11, 27, 72, 35,  5, 30, 28,
       26, 36, 58,  0, 62, 73, 55, 45, 21, 71,  2, 32, 69, 51,  9, 41, 39,
       66, 59, 16, 57, 24, 70, 68, 29, 46, 74, 31, 60, 15, 19, 75, 64, 38,
       47, 17, 67, 49, 10,  1, 54, 25], dtype=int64), 'cur_cost': 621292.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:14,187 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:14,188 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,192 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=154887.000, 多样性=0.978
2025-08-05 10:29:14,192 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:14,192 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:14,192 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:14,192 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05447713289208237, 'best_improvement': -0.035555496125534033}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.006240713224368407}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.059933088349398866, 'recent_improvements': [0.08771864737879666, 0.04005791189905079, -0.03214752932000109], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 109118.0, 'new_best_cost': 109118.0, 'quality_improvement': 0.0, 'old_diversity': 0.9342105263157895, 'new_diversity': 0.9342105263157895, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:29:14,193 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:14,193 - __main__ - INFO - pr76 开始进化第 2 代
2025-08-05 10:29:14,193 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:14,193 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,193 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=154887.000, 多样性=0.978
2025-08-05 10:29:14,194 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:14,197 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.978
2025-08-05 10:29:14,198 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:14,201 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.934
2025-08-05 10:29:14,203 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:14,203 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:14,203 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:14,203 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:14,248 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -2870.271, 聚类评分: 0.000, 覆盖率: 0.160, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:14,249 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:14,249 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:14,249 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 10:29:14,255 - visualization.landscape_visualizer - INFO - 插值约束: 28 个点被约束到最小值 109118.00
2025-08-05 10:29:14,258 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.4%, 梯度: 12959.87 → 12126.81
2025-08-05 10:29:14,407 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_142_20250805_102914.html
2025-08-05 10:29:14,486 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_142_20250805_102914.html
2025-08-05 10:29:14,486 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 142
2025-08-05 10:29:14,486 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:14,486 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2837秒
2025-08-05 10:29:14,487 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2870.271428571434, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 50325516831.42633, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1601, 'fitness_entropy': 0.9101663100395065, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2870.271)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.160)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360954.2495341, 'performance_metrics': {}}}
2025-08-05 10:29:14,487 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:14,487 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:14,487 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:14,487 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:14,488 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,488 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:14,488 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,488 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,488 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:14,488 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,488 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,489 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:14,489 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:14,489 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:14,489 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:14,489 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,492 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,492 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,493 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,493 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162567.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,493 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162567.0, 'intermediate_solutions': [{'tour': [19, 6, 73, 7, 32, 52, 65, 67, 63, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 401249.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 6, 36, 12, 4, 21, 3, 8, 2, 46, 51, 41, 42, 9, 22, 24, 23, 30, 31, 58, 68, 50, 64, 62, 40, 33, 43, 55, 57, 49, 27, 5, 28, 59, 61, 71, 60, 34, 37, 35, 13, 74, 20, 10, 39, 17, 29, 53, 26, 44, 67, 63, 65, 52, 32, 7, 73, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 402534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 10, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395551.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,494 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 162567.00)
2025-08-05 10:29:14,494 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:14,494 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:14,494 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,497 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,497 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,498 - ExplorationExpert - INFO - 探索路径生成完成，成本: 139661.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,499 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 139661.0, 'intermediate_solutions': [{'tour': [59, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 13, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 387382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 31, 56, 39, 34, 55, 42, 44, 51, 59, 60, 33, 23, 3, 35, 37, 40, 62, 58, 41, 11, 9, 26, 8, 36, 25, 2, 74, 6, 21, 43, 29, 18, 28, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 389303.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 2, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 389121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,499 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 139661.00)
2025-08-05 10:29:14,499 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:14,499 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:14,499 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,502 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,503 - ExplorationExpert - INFO - 探索路径生成完成，成本: 142512.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,503 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 142512.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 14, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 42, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 193659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 56, 57, 58, 59, 40, 39, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 172153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 68, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 176515.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,503 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 142512.00)
2025-08-05 10:29:14,503 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:14,503 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:14,503 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,506 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,507 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,508 - ExplorationExpert - INFO - 探索路径生成完成，成本: 138384.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,508 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 138384.0, 'intermediate_solutions': [{'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 70, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 58, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 491129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 60, 6, 14, 17, 63, 15, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 477278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 41, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 480966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,508 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 138384.00)
2025-08-05 10:29:14,508 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:14,508 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,508 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,509 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 566953.0
2025-08-05 10:29:14,522 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:14,522 - ExploitationExpert - INFO - res_population_costs: [109118.0, 110135, 112983, 113650.0]
2025-08-05 10:29:14,522 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,524 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,524 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162567.0}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 139661.0}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 142512.0}, {'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 138384.0}, {'tour': array([16, 55, 52, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12], dtype=int64), 'cur_cost': 566953.0}, {'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396835.0}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 414023.0}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 168230.0}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 154887.0}, {'tour': [49, 14, 60, 45, 15, 0, 39, 8, 66, 30, 44, 22, 74, 25, 65, 26, 53, 67, 42, 29, 4, 47, 18, 46, 16, 52, 7, 72, 68, 21, 36, 54, 64, 62, 70, 19, 13, 63, 1, 61, 24, 41, 33, 20, 34, 11, 69, 5, 38, 55, 12, 32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59, 2, 6, 3, 73, 23, 51, 50, 27, 9, 37, 40, 35], 'cur_cost': 604030.0}]
2025-08-05 10:29:14,525 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:14,525 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 367, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 367, 'cache_hits': 0, 'similarity_calculations': 1905, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,526 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([16, 55, 52, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12], dtype=int64), 'cur_cost': 566953.0, 'intermediate_solutions': [{'tour': array([46, 62, 19, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 614858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 46, 62, 19, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 612240.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([71, 20, 46, 62, 19, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 612735.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 20, 46, 62, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 592224.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 71, 20, 46, 62, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 610309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,526 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 566953.00)
2025-08-05 10:29:14,526 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:14,526 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:14,526 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,528 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:14,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,529 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,529 - ExplorationExpert - INFO - 探索路径生成完成，成本: 358232.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,530 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 6, 14, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 5, 2, 10, 73, 12, 37, 32, 3, 25, 26, 43, 47, 46, 75, 68, 67, 20, 21, 70, 1, 72, 38, 74, 31, 13, 33, 71, 45, 7, 11, 44, 0, 54, 16, 69, 23, 66, 24], 'cur_cost': 358232.0, 'intermediate_solutions': [{'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 34, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 28, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 36, 5, 11, 7, 12, 31, 38, 37, 32, 60, 57, 33, 27, 28, 48, 56, 49, 47, 24, 26, 25, 20, 14, 3, 17, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 395884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 37, 38, 35, 4, 41, 59, 51, 30, 19, 32, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 399381.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,530 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 358232.00)
2025-08-05 10:29:14,530 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:14,530 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:14,530 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,533 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,533 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 141330.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,534 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141330.0, 'intermediate_solutions': [{'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 62, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 47, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 420732.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 64, 40, 43, 50, 56, 14, 74, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 428641.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 62, 71, 3, 69, 6, 13, 22, 75, 63, 72], 'cur_cost': 438324.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,534 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 141330.00)
2025-08-05 10:29:14,534 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:14,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,536 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,538 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170620.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,538 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170620.0, 'intermediate_solutions': [{'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 72, 49, 48, 70, 71, 66, 38, 37, 73, 69], 'cur_cost': 192405.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 22, 21, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170415.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 49, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177442.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,538 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 170620.00)
2025-08-05 10:29:14,538 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:14,538 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:14,538 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,540 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,541 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154135.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,541 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 8, 29, 30, 18, 19, 4, 5, 6, 7, 28, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 170146.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 70, 69, 66, 67, 68, 46, 47, 43, 44, 45, 75, 74, 1, 22, 21, 23, 24, 20, 3, 9, 8, 7, 6, 5, 4, 19, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 179121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 69, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 174122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,542 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 154135.00)
2025-08-05 10:29:14,542 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:14,542 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,542 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,542 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 586704.0
2025-08-05 10:29:14,554 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:14,554 - ExploitationExpert - INFO - res_population_costs: [109118.0, 110135, 112983, 113650.0]
2025-08-05 10:29:14,554 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,557 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,557 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162567.0}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 139661.0}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 142512.0}, {'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 138384.0}, {'tour': array([16, 55, 52, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12], dtype=int64), 'cur_cost': 566953.0}, {'tour': [22, 6, 14, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 5, 2, 10, 73, 12, 37, 32, 3, 25, 26, 43, 47, 46, 75, 68, 67, 20, 21, 70, 1, 72, 38, 74, 31, 13, 33, 71, 45, 7, 11, 44, 0, 54, 16, 69, 23, 66, 24], 'cur_cost': 358232.0}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141330.0}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170620.0}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0}, {'tour': array([ 6, 68, 21, 61, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31], dtype=int64), 'cur_cost': 586704.0}]
2025-08-05 10:29:14,558 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:14,558 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 368, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 368, 'cache_hits': 0, 'similarity_calculations': 1908, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,559 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 6, 68, 21, 61, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31], dtype=int64), 'cur_cost': 586704.0, 'intermediate_solutions': [{'tour': array([60, 14, 49, 45, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 597143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 60, 14, 49, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 605200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 45, 60, 14, 49,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 601122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 45, 60, 14, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 588139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 15, 45, 60, 14,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 603322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,559 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 586704.00)
2025-08-05 10:29:14,559 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:14,559 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:14,561 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162567.0, 'intermediate_solutions': [{'tour': [19, 6, 73, 7, 32, 52, 65, 67, 63, 44, 26, 53, 29, 17, 39, 10, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 401249.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 6, 36, 12, 4, 21, 3, 8, 2, 46, 51, 41, 42, 9, 22, 24, 23, 30, 31, 58, 68, 50, 64, 62, 40, 33, 43, 55, 57, 49, 27, 5, 28, 59, 61, 71, 60, 34, 37, 35, 13, 74, 20, 10, 39, 17, 29, 53, 26, 44, 67, 63, 65, 52, 32, 7, 73, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 402534.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 6, 73, 7, 32, 52, 65, 63, 67, 44, 26, 53, 29, 17, 39, 20, 74, 13, 35, 37, 34, 60, 71, 61, 59, 28, 5, 27, 49, 57, 55, 43, 33, 40, 62, 64, 50, 68, 58, 31, 30, 23, 24, 22, 9, 42, 41, 51, 46, 2, 8, 3, 21, 4, 12, 36, 10, 56, 66, 47, 25, 11, 38, 16, 1, 0, 14, 54, 48, 18, 45, 69, 70, 72, 15, 75], 'cur_cost': 395551.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 139661.0, 'intermediate_solutions': [{'tour': [59, 31, 28, 18, 29, 43, 21, 6, 74, 2, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 13, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 387382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 31, 56, 39, 34, 55, 42, 44, 51, 59, 60, 33, 23, 3, 35, 37, 40, 62, 58, 41, 11, 9, 26, 8, 36, 25, 2, 74, 6, 21, 43, 29, 18, 28, 57, 30, 14, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 389303.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 31, 28, 18, 29, 43, 21, 6, 74, 25, 36, 8, 26, 9, 11, 41, 58, 62, 40, 37, 35, 3, 23, 33, 60, 59, 51, 44, 42, 55, 34, 39, 56, 57, 30, 14, 2, 12, 73, 75, 16, 27, 53, 32, 38, 65, 63, 52, 67, 64, 48, 54, 71, 70, 61, 72, 69, 20, 1, 4, 24, 45, 68, 47, 50, 49, 46, 0, 19, 5, 10, 17, 7, 15, 22, 66], 'cur_cost': 389121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 142512.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 14, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 42, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 193659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 56, 57, 58, 59, 40, 39, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 172153.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 21, 20, 24, 23, 45, 44, 43, 47, 46, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 2, 3, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 68, 70, 71, 72, 38, 37, 31, 32, 26, 25, 22, 1, 74, 75, 73, 69], 'cur_cost': 176515.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 138384.0, 'intermediate_solutions': [{'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 70, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 58, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 491129.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 41, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 60, 6, 14, 17, 63, 15, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 477278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 8, 7, 18, 20, 43, 65, 45, 44, 2, 26, 36, 68, 67, 39, 41, 10, 58, 52, 40, 35, 27, 71, 29, 59, 28, 51, 4, 49, 55, 34, 3, 33, 57, 30, 64, 50, 73, 75, 31, 23, 24, 22, 9, 42, 46, 48, 56, 62, 70, 72, 69, 66, 47, 11, 38, 16, 0, 5, 61, 37, 15, 63, 17, 14, 6, 60, 25, 21, 54, 1, 74, 19, 32, 12, 53], 'cur_cost': 480966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 55, 52, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12], dtype=int64), 'cur_cost': 566953.0, 'intermediate_solutions': [{'tour': array([46, 62, 19, 20, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 614858.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 46, 62, 19, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 612240.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([71, 20, 46, 62, 19, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 612735.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 20, 46, 62, 71, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 592224.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 71, 20, 46, 62, 14, 55, 53, 11, 34, 61, 49, 30,  7, 68, 25, 27,
       44, 73, 60, 15,  0, 33, 31, 17, 63, 42, 43, 64,  8, 22, 51, 45, 28,
       56,  4, 10, 65, 24, 16, 57,  1, 48, 58, 36, 40, 59, 26, 70, 72,  9,
       13, 69, 12, 75, 47,  2, 74, 37, 23, 54,  3, 67, 50, 21, 52,  5, 66,
       39,  6, 35, 29, 38, 32, 41, 18]), 'cur_cost': 610309.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 6, 14, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 5, 2, 10, 73, 12, 37, 32, 3, 25, 26, 43, 47, 46, 75, 68, 67, 20, 21, 70, 1, 72, 38, 74, 31, 13, 33, 71, 45, 7, 11, 44, 0, 54, 16, 69, 23, 66, 24], 'cur_cost': 358232.0, 'intermediate_solutions': [{'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 34, 27, 33, 57, 60, 32, 37, 38, 35, 4, 41, 59, 51, 30, 19, 21, 28, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 396881.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 36, 5, 11, 7, 12, 31, 38, 37, 32, 60, 57, 33, 27, 28, 48, 56, 49, 47, 24, 26, 25, 20, 14, 3, 17, 35, 4, 41, 59, 51, 30, 19, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 395884.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 36, 5, 11, 7, 12, 31, 17, 3, 14, 20, 25, 26, 24, 47, 49, 56, 48, 28, 27, 33, 57, 60, 37, 38, 35, 4, 41, 59, 51, 30, 19, 32, 21, 34, 50, 54, 52, 42, 62, 67, 44, 0, 74, 45, 66, 29, 39, 63, 65, 69, 46, 61, 53, 9, 1, 6, 15, 16, 55, 72, 58, 64, 68, 70, 10, 73, 8, 13, 18, 2, 23, 22, 75, 43, 71], 'cur_cost': 399381.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141330.0, 'intermediate_solutions': [{'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 62, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 69, 47, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 420732.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 64, 40, 43, 50, 56, 14, 74, 66, 48, 57, 67, 69, 62, 71, 3, 6, 13, 22, 75, 63, 72], 'cur_cost': 428641.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [36, 37, 26, 20, 8, 11, 10, 18, 28, 33, 54, 70, 46, 27, 31, 51, 55, 34, 25, 35, 15, 32, 61, 49, 44, 41, 39, 19, 9, 1, 42, 65, 60, 17, 16, 58, 53, 59, 47, 68, 30, 12, 4, 73, 5, 2, 29, 52, 38, 7, 23, 45, 0, 21, 24, 74, 14, 56, 50, 43, 40, 64, 66, 48, 57, 67, 62, 71, 3, 69, 6, 13, 22, 75, 63, 72], 'cur_cost': 438324.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170620.0, 'intermediate_solutions': [{'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 72, 49, 48, 70, 71, 66, 38, 37, 73, 69], 'cur_cost': 192405.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 22, 21, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170415.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 7, 5, 6, 2, 3, 4, 19, 18, 30, 29, 28, 49, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177442.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 8, 29, 30, 18, 19, 4, 5, 6, 7, 28, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 170146.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 70, 69, 66, 67, 68, 46, 47, 43, 44, 45, 75, 74, 1, 22, 21, 23, 24, 20, 3, 9, 8, 7, 6, 5, 4, 19, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 179121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 10, 11, 12, 14, 15, 16, 17, 36, 35, 34, 33, 69, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 70, 71, 72, 38, 37, 31, 32, 26, 25, 73], 'cur_cost': 174122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 68, 21, 61, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31], dtype=int64), 'cur_cost': 586704.0, 'intermediate_solutions': [{'tour': array([60, 14, 49, 45, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 597143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 60, 14, 49, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 605200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 45, 60, 14, 49,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 601122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 45, 60, 14, 15,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 588139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 15, 45, 60, 14,  0, 39,  8, 66, 30, 44, 22, 74, 25, 65, 26, 53,
       67, 42, 29,  4, 47, 18, 46, 16, 52,  7, 72, 68, 21, 36, 54, 64, 62,
       70, 19, 13, 63,  1, 61, 24, 41, 33, 20, 34, 11, 69,  5, 38, 55, 12,
       32, 71, 75, 10, 58, 17, 57, 48, 56, 31, 43, 28, 59,  2,  6,  3, 73,
       23, 51, 50, 27,  9, 37, 40, 35]), 'cur_cost': 603322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:14,561 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:14,562 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,565 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=138384.000, 多样性=0.934
2025-08-05 10:29:14,565 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:14,565 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:14,565 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:14,566 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.1549522930324901, 'best_improvement': 0.10654864514129656}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.04485645933014334}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.007209610496515796, 'recent_improvements': [0.04005791189905079, -0.03214752932000109, 0.05447713289208237], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 109118.0, 'new_best_cost': 109118.0, 'quality_improvement': 0.0, 'old_diversity': 0.9342105263157895, 'new_diversity': 0.9342105263157895, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:29:14,566 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:14,566 - __main__ - INFO - pr76 开始进化第 3 代
2025-08-05 10:29:14,566 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:14,567 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,567 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=138384.000, 多样性=0.934
2025-08-05 10:29:14,568 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:14,570 - PathExpert - INFO - 路径结构分析完成: 公共边数量=52, 路径相似性=0.934
2025-08-05 10:29:14,570 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:14,572 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.934
2025-08-05 10:29:14,573 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:14,574 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:14,574 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:14,574 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:14,615 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -51341.614, 聚类评分: 0.000, 覆盖率: 0.161, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:14,615 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:14,615 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:14,615 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 10:29:14,620 - visualization.landscape_visualizer - INFO - 插值约束: 55 个点被约束到最小值 109118.00
2025-08-05 10:29:14,621 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.6%, 梯度: 13387.20 → 12640.04
2025-08-05 10:29:14,736 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_143_20250805_102914.html
2025-08-05 10:29:14,810 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_143_20250805_102914.html
2025-08-05 10:29:14,810 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 143
2025-08-05 10:29:14,810 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:14,810 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2375秒
2025-08-05 10:29:14,811 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -51341.61428571427, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 32254792565.15693, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1608, 'fitness_entropy': 0.7419158534223445, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -51341.614)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.161)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360954.6158679, 'performance_metrics': {}}}
2025-08-05 10:29:14,811 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:14,811 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:14,811 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:14,811 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:14,813 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,813 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:14,813 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:14,813 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:14,813 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:14,814 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:14,814 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,815 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:14,815 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,816 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,816 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,816 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,817 - ExplorationExpert - INFO - 探索路径生成完成，成本: 390551.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,817 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 390551.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 44, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 62, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 196294.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 26, 25, 31, 32, 38, 37, 10, 9, 8, 7, 6, 5, 4, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 48, 49, 64, 65, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 39, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 170023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 9, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,817 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 390551.00)
2025-08-05 10:29:14,817 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:14,817 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:14,817 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,819 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,820 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150510.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,821 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 150510.0, 'intermediate_solutions': [{'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 49, 64, 65, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 9, 8, 5, 2, 3, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 48, 49, 64, 65, 50, 55, 54, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 154396.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 54, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 142269.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,821 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 150510.00)
2025-08-05 10:29:14,821 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:14,821 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:14,821 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,824 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,824 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,825 - ExplorationExpert - INFO - 探索路径生成完成，成本: 171815.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,825 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 171815.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 61, 40, 59, 58, 57, 56, 62, 63, 39, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 153916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 26, 32, 31, 37, 25], 'cur_cost': 152912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 62, 63, 61, 60, 54, 56, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,825 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 171815.00)
2025-08-05 10:29:14,825 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:14,825 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:14,825 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,827 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:14,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,828 - ExplorationExpert - INFO - 探索路径生成完成，成本: 517540.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,828 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517540.0, 'intermediate_solutions': [{'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 59, 40, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 139465.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 75, 74, 1, 25, 26, 32, 31, 37, 38, 72, 71, 70, 69, 66], 'cur_cost': 162733.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [37, 0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 159331.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,828 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 517540.00)
2025-08-05 10:29:14,829 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:14,829 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,829 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,829 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 566727.0
2025-08-05 10:29:14,842 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:14,843 - ExploitationExpert - INFO - res_population_costs: [109118.0, 110135, 112983, 113650.0, 108234]
2025-08-05 10:29:14,843 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,845 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,845 - ExploitationExpert - INFO - populations: [{'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 390551.0}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 150510.0}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 171815.0}, {'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517540.0}, {'tour': array([47,  7, 56, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25], dtype=int64), 'cur_cost': 566727.0}, {'tour': [22, 6, 14, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 5, 2, 10, 73, 12, 37, 32, 3, 25, 26, 43, 47, 46, 75, 68, 67, 20, 21, 70, 1, 72, 38, 74, 31, 13, 33, 71, 45, 7, 11, 44, 0, 54, 16, 69, 23, 66, 24], 'cur_cost': 358232.0}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141330.0}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170620.0}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0}, {'tour': [6, 68, 21, 61, 40, 42, 38, 56, 52, 71, 46, 0, 37, 44, 28, 57, 29, 75, 2, 25, 41, 8, 3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74, 35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47, 13, 36, 69, 22, 1, 53, 19, 54, 55, 67, 7, 45, 9, 59, 24, 39, 63, 17, 62, 4, 5, 20, 10, 26, 31], 'cur_cost': 586704.0}]
2025-08-05 10:29:14,846 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:14,846 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 369, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 369, 'cache_hits': 0, 'similarity_calculations': 1912, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,847 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([47,  7, 56, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25], dtype=int64), 'cur_cost': 566727.0, 'intermediate_solutions': [{'tour': array([52, 55, 16, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 578567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 52, 55, 16, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 576032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([63, 42, 52, 55, 16, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 582132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 42, 52, 55, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 560909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 63, 42, 52, 55, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 565686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,847 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 566727.00)
2025-08-05 10:29:14,847 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:14,847 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,847 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,847 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 576695.0
2025-08-05 10:29:14,857 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:14,857 - ExploitationExpert - INFO - res_population_costs: [109118.0, 110135, 112983, 113650.0, 108234]
2025-08-05 10:29:14,857 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,859 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,859 - ExploitationExpert - INFO - populations: [{'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 390551.0}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 150510.0}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 171815.0}, {'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517540.0}, {'tour': array([47,  7, 56, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25], dtype=int64), 'cur_cost': 566727.0}, {'tour': array([56, 28, 47, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42], dtype=int64), 'cur_cost': 576695.0}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141330.0}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 170620.0}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0}, {'tour': [6, 68, 21, 61, 40, 42, 38, 56, 52, 71, 46, 0, 37, 44, 28, 57, 29, 75, 2, 25, 41, 8, 3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74, 35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47, 13, 36, 69, 22, 1, 53, 19, 54, 55, 67, 7, 45, 9, 59, 24, 39, 63, 17, 62, 4, 5, 20, 10, 26, 31], 'cur_cost': 586704.0}]
2025-08-05 10:29:14,860 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:14,861 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 370, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 370, 'cache_hits': 0, 'similarity_calculations': 1917, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,861 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([56, 28, 47, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42], dtype=int64), 'cur_cost': 576695.0, 'intermediate_solutions': [{'tour': array([14,  6, 22, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 371022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14,  6, 22, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 372036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([36, 17, 14,  6, 22, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 370402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 17, 14,  6, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 367414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 36, 17, 14,  6, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 364738.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,862 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 576695.00)
2025-08-05 10:29:14,862 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:14,862 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:14,862 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,864 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:14,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,865 - ExplorationExpert - INFO - 探索路径生成完成，成本: 154710.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,865 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154710.0, 'intermediate_solutions': [{'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 2, 3, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 22, 1, 2, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 145802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 60, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 154572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,866 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 154710.00)
2025-08-05 10:29:14,866 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:14,866 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:14,866 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,875 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,876 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,876 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,876 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,876 - ExplorationExpert - INFO - 探索路径生成完成，成本: 396805.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,876 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 396805.0, 'intermediate_solutions': [{'tour': [0, 5, 28, 7, 6, 2, 3, 4, 19, 18, 30, 29, 15, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 182167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177323.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69, 58], 'cur_cost': 178970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,877 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 396805.00)
2025-08-05 10:29:14,877 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:14,877 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:14,877 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:14,885 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:14,885 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:14,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 395530.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:14,886 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 395530.0, 'intermediate_solutions': [{'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 17, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 155427.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 28, 29, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 163812.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:14,887 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 395530.00)
2025-08-05 10:29:14,887 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:14,887 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:14,887 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:14,887 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 576394.0
2025-08-05 10:29:14,900 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:14,900 - ExploitationExpert - INFO - res_population_costs: [109118.0, 110135, 112983, 113650.0, 108234]
2025-08-05 10:29:14,900 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:14,902 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:14,902 - ExploitationExpert - INFO - populations: [{'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 390551.0}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 150510.0}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 171815.0}, {'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517540.0}, {'tour': array([47,  7, 56, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25], dtype=int64), 'cur_cost': 566727.0}, {'tour': array([56, 28, 47, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42], dtype=int64), 'cur_cost': 576695.0}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154710.0}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 396805.0}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 395530.0}, {'tour': array([47, 55,  8,  5,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16], dtype=int64), 'cur_cost': 576394.0}]
2025-08-05 10:29:14,904 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:14,905 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 371, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 371, 'cache_hits': 0, 'similarity_calculations': 1923, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:14,906 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([47, 55,  8,  5,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16], dtype=int64), 'cur_cost': 576394.0, 'intermediate_solutions': [{'tour': array([21, 68,  6, 61, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 587191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([61, 21, 68,  6, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 593399.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 61, 21, 68,  6, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 588020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 61, 21, 68, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 593280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 40, 61, 21, 68, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 585892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:14,906 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 576394.00)
2025-08-05 10:29:14,906 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:14,906 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:14,909 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 390551.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 44, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 62, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 196294.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 26, 25, 31, 32, 38, 37, 10, 9, 8, 7, 6, 5, 4, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 48, 49, 64, 65, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 39, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 170023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 10, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 74, 75, 73, 9, 68, 67, 66, 69, 70, 71, 72], 'cur_cost': 162093.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 150510.0, 'intermediate_solutions': [{'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 49, 64, 65, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 9, 8, 5, 2, 3, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 48, 49, 64, 65, 50, 55, 54, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 154396.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 6, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 55, 50, 65, 64, 54, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 8, 9, 7, 1, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 142269.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 171815.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 61, 40, 59, 58, 57, 56, 62, 63, 39, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 153916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 26, 32, 31, 37, 25], 'cur_cost': 152912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 62, 63, 61, 60, 54, 56, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 9, 8, 10, 15, 14, 12, 13, 73, 7, 6, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517540.0, 'intermediate_solutions': [{'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 59, 40, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 139465.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 75, 74, 1, 25, 26, 32, 31, 37, 38, 72, 71, 70, 69, 66], 'cur_cost': 162733.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [37, 0, 22, 17, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 73, 2, 3, 20, 24, 23, 21, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 31, 32, 26, 25, 1, 74, 75], 'cur_cost': 159331.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([47,  7, 56, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25], dtype=int64), 'cur_cost': 566727.0, 'intermediate_solutions': [{'tour': array([52, 55, 16, 42, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 578567.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 52, 55, 16, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 576032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([63, 42, 52, 55, 16, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 582132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 42, 52, 55, 63, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 560909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 63, 42, 52, 55, 50, 20, 19, 72, 64, 10, 67,  0, 13, 36,  4, 62,
       33, 54, 41, 22, 32, 68, 37, 30, 70,  8, 34, 45, 46, 35, 40, 61,  7,
       28, 48, 51, 58, 27, 57, 66, 29, 21, 23, 15,  2, 43, 39, 24,  1, 11,
       53, 14, 31, 65, 25,  3, 69, 44, 75, 38, 71,  6,  5, 17, 49, 73, 56,
       60, 26, 47, 74,  9, 18, 59, 12]), 'cur_cost': 565686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 28, 47, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42], dtype=int64), 'cur_cost': 576695.0, 'intermediate_solutions': [{'tour': array([14,  6, 22, 17, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 371022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14,  6, 22, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 372036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([36, 17, 14,  6, 22, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 370402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 17, 14,  6, 36, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 367414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 36, 17, 14,  6, 15, 35, 34, 39, 40, 59, 58, 57, 56, 62, 63, 61,
       60, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18,
       19,  4,  9,  8,  5,  2, 10, 73, 12, 37, 32,  3, 25, 26, 43, 47, 46,
       75, 68, 67, 20, 21, 70,  1, 72, 38, 74, 31, 13, 33, 71, 45,  7, 11,
       44,  0, 54, 16, 69, 23, 66, 24]), 'cur_cost': 364738.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154710.0, 'intermediate_solutions': [{'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 2, 3, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 141853.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 22, 1, 2, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 145802.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 19, 12, 13, 11, 10, 16, 60, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 4, 5, 6, 7, 8, 9, 3, 2, 1, 22, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73, 74, 75], 'cur_cost': 154572.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 396805.0, 'intermediate_solutions': [{'tour': [0, 5, 28, 7, 6, 2, 3, 4, 19, 18, 30, 29, 15, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 182167.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69], 'cur_cost': 177323.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 7, 6, 2, 3, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 8, 9, 31, 32, 26, 25, 24, 20, 21, 22, 1, 74, 75, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 70, 71, 72, 38, 37, 73, 69, 58], 'cur_cost': 178970.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 395530.0, 'intermediate_solutions': [{'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 16, 17, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 155427.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 14, 13, 12, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 28, 29, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 163812.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 18, 9, 8, 5, 6, 7, 2, 3, 4, 19, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 14, 15, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 74, 75, 73, 48, 49, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 154135.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 55,  8,  5,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16], dtype=int64), 'cur_cost': 576394.0, 'intermediate_solutions': [{'tour': array([21, 68,  6, 61, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 587191.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([61, 21, 68,  6, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 593399.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 61, 21, 68,  6, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 588020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 61, 21, 68, 40, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 593280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 40, 61, 21, 68, 42, 38, 56, 52, 71, 46,  0, 37, 44, 28, 57, 29,
       75,  2, 25, 41,  8,  3, 12, 48, 30, 15, 34, 18, 43, 64, 33, 70, 74,
       35, 27, 72, 73, 16, 65, 58, 66, 60, 32, 23, 11, 50, 51, 14, 49, 47,
       13, 36, 69, 22,  1, 53, 19, 54, 55, 67,  7, 45,  9, 59, 24, 39, 63,
       17, 62,  4,  5, 20, 10, 26, 31]), 'cur_cost': 585892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:14,909 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:14,909 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,913 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=150510.000, 多样性=0.974
2025-08-05 10:29:14,913 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:14,913 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:14,913 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:14,914 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.19742905988491924, 'best_improvement': -0.08762573707943115}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04289292423293645}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.09354991117624561, 'recent_improvements': [-0.03214752932000109, 0.05447713289208237, 0.1549522930324901], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 108234, 'new_best_cost': 108234, 'quality_improvement': 0.0, 'old_diversity': 0.881578947368421, 'new_diversity': 0.881578947368421, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:14,915 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:14,915 - __main__ - INFO - pr76 开始进化第 4 代
2025-08-05 10:29:14,915 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:14,915 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:14,916 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=150510.000, 多样性=0.974
2025-08-05 10:29:14,916 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:14,919 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.974
2025-08-05 10:29:14,920 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:14,922 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.882
2025-08-05 10:29:14,924 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:14,924 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:14,924 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:14,924 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:14,965 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -4731.573, 聚类评分: 0.000, 覆盖率: 0.162, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:14,965 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:14,965 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:14,965 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 10:29:14,970 - visualization.landscape_visualizer - INFO - 插值约束: 169 个点被约束到最小值 108234.00
2025-08-05 10:29:14,972 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.3%, 梯度: 18071.06 → 16388.62
2025-08-05 10:29:15,086 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_144_20250805_102915.html
2025-08-05 10:29:15,167 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_144_20250805_102915.html
2025-08-05 10:29:15,168 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 144
2025-08-05 10:29:15,168 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:15,168 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2433秒
2025-08-05 10:29:15,169 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4731.573333333335, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 26579400135.076622, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1618, 'fitness_entropy': 0.8453348043012463, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4731.573)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.162)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360954.9650462, 'performance_metrics': {}}}
2025-08-05 10:29:15,169 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:15,169 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:15,170 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:15,170 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:15,170 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,171 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:15,171 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,171 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,171 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:15,171 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,173 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,173 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:15,173 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:15,174 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:15,174 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:15,174 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,178 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:15,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 418024.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,180 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 418024.0, 'intermediate_solutions': [{'tour': [16, 19, 64, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 6, 14, 23, 62, 69], 'cur_cost': 387303.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 45, 70, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 393338.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 56, 64, 14, 23, 62, 69], 'cur_cost': 387323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,180 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 418024.00)
2025-08-05 10:29:15,181 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:15,181 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:15,181 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,190 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:15,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 397415.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,192 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 397415.0, 'intermediate_solutions': [{'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 37, 70, 71, 72, 38, 69, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 182501.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 66, 67, 68, 46, 47, 43, 44, 45, 75, 74, 1, 22, 21, 23, 24, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 167764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 27, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 155489.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,192 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 397415.00)
2025-08-05 10:29:15,192 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:15,193 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:15,193 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,195 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:15,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,196 - ExplorationExpert - INFO - 探索路径生成完成，成本: 535586.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,196 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 535586.0, 'intermediate_solutions': [{'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 75, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 35, 69, 70, 71, 72], 'cur_cost': 203747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 25, 26, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 21, 22, 1, 3, 2, 7, 6, 5, 4, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 178540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 33, 39, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 174227.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,196 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 535586.00)
2025-08-05 10:29:15,197 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:15,197 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:15,197 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,200 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,200 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,201 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 161490.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,201 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 161490.0, 'intermediate_solutions': [{'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 75, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 40, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 503505.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 37, 43, 73, 32, 5, 4, 3, 8, 13, 12, 19, 15, 30, 31, 29, 40, 42, 34, 33, 39, 52, 48, 58, 49, 61, 68, 62, 41, 54, 2, 55, 53, 63, 56, 51, 0, 67, 10, 64, 27, 28, 35, 36, 18, 14, 6, 11, 22, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 514487.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 39, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517048.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,202 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 161490.00)
2025-08-05 10:29:15,202 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:15,202 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,202 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,202 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 563547.0
2025-08-05 10:29:15,216 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:15,216 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0]
2025-08-05 10:29:15,216 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,218 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,218 - ExploitationExpert - INFO - populations: [{'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 418024.0}, {'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 397415.0}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 535586.0}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 161490.0}, {'tour': array([20, 65, 17, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67], dtype=int64), 'cur_cost': 563547.0}, {'tour': [56, 28, 47, 69, 2, 8, 26, 64, 4, 27, 46, 71, 19, 44, 6, 29, 21, 58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32, 16, 68, 17, 37, 60, 57, 45, 72, 22, 0, 65, 63, 43, 38, 40, 25, 9, 7, 35, 53, 20, 59, 23, 18, 5, 30, 12, 34, 54, 1, 55, 51, 62, 3, 49, 67, 24, 14, 50, 66, 15, 42], 'cur_cost': 576695.0}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154710.0}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 396805.0}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 395530.0}, {'tour': [47, 55, 8, 5, 2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73, 62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56, 9, 6, 3, 69, 72, 57, 70, 74, 17, 36, 0, 66, 27, 26, 71, 12, 4, 52, 7, 64, 59, 11, 15, 45, 22, 13, 1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50, 24, 58, 32, 38, 23, 68, 46, 16], 'cur_cost': 576394.0}]
2025-08-05 10:29:15,220 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:15,220 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 372, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 372, 'cache_hits': 0, 'similarity_calculations': 1930, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,221 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([20, 65, 17, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67], dtype=int64), 'cur_cost': 563547.0, 'intermediate_solutions': [{'tour': array([56,  7, 47, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 565048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([74, 56,  7, 47, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 569879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 74, 56,  7, 47, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 562012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 74, 56,  7, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 567251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 24, 74, 56,  7, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 566772.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,222 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 563547.00)
2025-08-05 10:29:15,222 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:15,222 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,222 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,222 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 559560.0
2025-08-05 10:29:15,236 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:15,236 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0]
2025-08-05 10:29:15,236 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,239 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,239 - ExploitationExpert - INFO - populations: [{'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 418024.0}, {'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 397415.0}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 535586.0}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 161490.0}, {'tour': array([20, 65, 17, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67], dtype=int64), 'cur_cost': 563547.0}, {'tour': array([48, 55, 59, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31], dtype=int64), 'cur_cost': 559560.0}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154710.0}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 396805.0}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 395530.0}, {'tour': [47, 55, 8, 5, 2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73, 62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56, 9, 6, 3, 69, 72, 57, 70, 74, 17, 36, 0, 66, 27, 26, 71, 12, 4, 52, 7, 64, 59, 11, 15, 45, 22, 13, 1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50, 24, 58, 32, 38, 23, 68, 46, 16], 'cur_cost': 576394.0}]
2025-08-05 10:29:15,240 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:15,240 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 373, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 373, 'cache_hits': 0, 'similarity_calculations': 1938, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,241 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([48, 55, 59, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31], dtype=int64), 'cur_cost': 559560.0, 'intermediate_solutions': [{'tour': array([47, 28, 56, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 578452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([69, 47, 28, 56,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 577642.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 69, 47, 28, 56,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 586498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([56, 69, 47, 28,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 569351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([56,  2, 69, 47, 28,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 583431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,241 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 559560.00)
2025-08-05 10:29:15,241 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:15,241 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:15,241 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,244 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:15,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,244 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 519550.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,245 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 519550.0, 'intermediate_solutions': [{'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 75, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 49], 'cur_cost': 207464.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 48, 49, 64, 65, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 39, 33, 34, 35, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 167774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 13, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154847.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,245 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 519550.00)
2025-08-05 10:29:15,245 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:15,245 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:15,245 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,248 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,248 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,249 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,249 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,249 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,249 - ExplorationExpert - INFO - 探索路径生成完成，成本: 168672.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,249 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 168672.0, 'intermediate_solutions': [{'tour': [46, 31, 33, 37, 68, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 12, 69, 64, 7], 'cur_cost': 412259.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 18, 2, 27, 49, 56, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 403920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 47, 18, 34, 61, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 394967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,250 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 168672.00)
2025-08-05 10:29:15,250 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:15,250 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:15,250 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,258 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:15,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,259 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,259 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,259 - ExplorationExpert - INFO - 探索路径生成完成，成本: 372163.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,259 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 372163.0, 'intermediate_solutions': [{'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 24, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 10, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 414567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 24, 74, 12, 3, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 401934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 15, 0, 75, 7, 60, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 410236.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,260 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 372163.00)
2025-08-05 10:29:15,260 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:15,260 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,260 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,260 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 601030.0
2025-08-05 10:29:15,274 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:15,275 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0]
2025-08-05 10:29:15,275 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,277 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,277 - ExploitationExpert - INFO - populations: [{'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 418024.0}, {'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 397415.0}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 535586.0}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 161490.0}, {'tour': array([20, 65, 17, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67], dtype=int64), 'cur_cost': 563547.0}, {'tour': array([48, 55, 59, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31], dtype=int64), 'cur_cost': 559560.0}, {'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 519550.0}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 168672.0}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 372163.0}, {'tour': array([24, 33, 38, 17, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49], dtype=int64), 'cur_cost': 601030.0}]
2025-08-05 10:29:15,279 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:15,279 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 374, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 374, 'cache_hits': 0, 'similarity_calculations': 1947, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,280 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([24, 33, 38, 17, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49], dtype=int64), 'cur_cost': 601030.0, 'intermediate_solutions': [{'tour': array([ 8, 55, 47,  5,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 577464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  8, 55, 47,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 578004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  5,  8, 55, 47, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 574922.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47,  5,  8, 55,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 588891.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47,  2,  5,  8, 55, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 582687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,280 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 601030.00)
2025-08-05 10:29:15,280 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:15,280 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:15,283 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 418024.0, 'intermediate_solutions': [{'tour': [16, 19, 64, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 6, 14, 23, 62, 69], 'cur_cost': 387303.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 56, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 45, 70, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 64, 14, 23, 62, 69], 'cur_cost': 393338.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 19, 6, 42, 15, 36, 20, 35, 34, 33, 39, 40, 59, 58, 4, 63, 60, 61, 54, 55, 32, 50, 65, 49, 48, 51, 52, 53, 41, 28, 27, 30, 29, 31, 18, 9, 8, 5, 57, 66, 3, 2, 12, 13, 73, 10, 37, 22, 21, 11, 25, 26, 43, 47, 46, 70, 45, 44, 24, 68, 67, 7, 1, 17, 71, 74, 75, 0, 72, 38, 56, 64, 14, 23, 62, 69], 'cur_cost': 387323.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 397415.0, 'intermediate_solutions': [{'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 37, 70, 71, 72, 38, 69, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 182501.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 66, 67, 68, 46, 47, 43, 44, 45, 75, 74, 1, 22, 21, 23, 24, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 167764.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 2, 12, 13, 11, 10, 16, 17, 36, 35, 34, 27, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 28, 29, 30, 19, 4, 5, 6, 7, 8, 9, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 15, 14, 73], 'cur_cost': 155489.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 535586.0, 'intermediate_solutions': [{'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 75, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 35, 69, 70, 71, 72], 'cur_cost': 203747.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 31, 25, 26, 48, 49, 66, 67, 68, 46, 47, 43, 44, 45, 23, 24, 21, 22, 1, 3, 2, 7, 6, 5, 4, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 178540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 20, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 33, 39, 34, 35, 36, 17, 16, 10, 15, 14, 12, 13, 8, 9, 4, 5, 6, 7, 2, 3, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 74, 75, 69, 70, 71, 72], 'cur_cost': 174227.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 161490.0, 'intermediate_solutions': [{'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 39, 33, 34, 42, 75, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 40, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 503505.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 37, 43, 73, 32, 5, 4, 3, 8, 13, 12, 19, 15, 30, 31, 29, 40, 42, 34, 33, 39, 52, 48, 58, 49, 61, 68, 62, 41, 54, 2, 55, 53, 63, 56, 51, 0, 67, 10, 64, 27, 28, 35, 36, 18, 14, 6, 11, 22, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 514487.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 6, 14, 18, 36, 35, 28, 27, 64, 10, 67, 0, 51, 56, 63, 53, 55, 2, 54, 41, 62, 68, 61, 49, 58, 48, 52, 33, 34, 42, 40, 29, 31, 30, 15, 19, 12, 13, 8, 3, 4, 5, 32, 73, 43, 37, 24, 21, 46, 25, 26, 74, 47, 45, 70, 69, 72, 39, 75, 38, 71, 7, 1, 59, 9, 66, 44, 60, 65, 20, 16, 50, 23, 17, 57], 'cur_cost': 517048.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 65, 17, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67], dtype=int64), 'cur_cost': 563547.0, 'intermediate_solutions': [{'tour': array([56,  7, 47, 74, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 565048.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([74, 56,  7, 47, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 569879.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 74, 56,  7, 47, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 562012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47, 74, 56,  7, 24, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 567251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 24, 74, 56,  7, 43, 11, 63, 68, 26, 52,  8, 16, 15, 32, 50, 54,
       67, 37, 36, 73, 59, 35, 66, 38, 44, 48, 75, 41, 19, 13,  6, 71, 12,
       70, 31,  3, 10, 72, 60,  2, 40,  4, 45,  0,  5, 65, 64, 20, 53, 34,
        1, 30, 69, 23, 22, 21, 17, 18, 14, 33, 42, 58,  9, 55, 49, 62, 27,
       28, 51, 29, 61, 57, 46, 39, 25]), 'cur_cost': 566772.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 55, 59, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31], dtype=int64), 'cur_cost': 559560.0, 'intermediate_solutions': [{'tour': array([47, 28, 56, 69,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 578452.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([69, 47, 28, 56,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 577642.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 69, 47, 28, 56,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 586498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([56, 69, 47, 28,  2,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 569351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([56,  2, 69, 47, 28,  8, 26, 64,  4, 27, 46, 71, 19, 44,  6, 29, 21,
       58, 61, 13, 41, 33, 70, 39, 48, 36, 74, 10, 52, 31, 73, 75, 11, 32,
       16, 68, 17, 37, 60, 57, 45, 72, 22,  0, 65, 63, 43, 38, 40, 25,  9,
        7, 35, 53, 20, 59, 23, 18,  5, 30, 12, 34, 54,  1, 55, 51, 62,  3,
       49, 67, 24, 14, 50, 66, 15, 42]), 'cur_cost': 583431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 519550.0, 'intermediate_solutions': [{'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 75, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 49], 'cur_cost': 207464.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 36, 48, 49, 64, 65, 50, 55, 54, 60, 61, 63, 62, 56, 57, 58, 59, 40, 39, 33, 34, 35, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 167774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 4, 20, 24, 23, 21, 22, 1, 2, 3, 5, 6, 7, 8, 9, 11, 13, 12, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 25, 26, 32, 31, 37, 38, 43, 47, 46, 44, 45, 68, 67, 66, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 154847.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 168672.0, 'intermediate_solutions': [{'tour': [46, 31, 33, 37, 68, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 18, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 12, 69, 64, 7], 'cur_cost': 412259.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 18, 2, 27, 49, 56, 34, 61, 47, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 403920.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 31, 33, 37, 12, 38, 29, 53, 35, 10, 11, 14, 40, 39, 63, 72, 62, 55, 43, 41, 28, 52, 42, 24, 19, 3, 23, 32, 50, 54, 26, 21, 0, 20, 44, 25, 36, 5, 17, 58, 56, 49, 27, 2, 47, 18, 34, 61, 57, 70, 30, 13, 22, 1, 16, 60, 71, 48, 59, 15, 9, 8, 45, 51, 65, 66, 67, 4, 73, 74, 75, 6, 68, 69, 64, 7], 'cur_cost': 394967.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 372163.0, 'intermediate_solutions': [{'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 24, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 3, 12, 74, 10, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 414567.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 60, 15, 0, 75, 7, 14, 24, 74, 12, 3, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 401934.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 39, 32, 29, 33, 37, 58, 16, 11, 6, 31, 40, 50, 49, 68, 47, 20, 18, 27, 45, 26, 25, 2, 13, 21, 48, 56, 54, 71, 70, 46, 53, 30, 4, 28, 36, 10, 8, 38, 57, 61, 62, 52, 43, 41, 17, 73, 19, 22, 42, 9, 59, 64, 35, 51, 63, 69, 55, 15, 0, 75, 7, 60, 14, 3, 12, 74, 24, 23, 5, 1, 67, 66, 65, 72, 44], 'cur_cost': 410236.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 33, 38, 17, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49], dtype=int64), 'cur_cost': 601030.0, 'intermediate_solutions': [{'tour': array([ 8, 55, 47,  5,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 577464.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  8, 55, 47,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 578004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2,  5,  8, 55, 47, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 574922.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47,  5,  8, 55,  2, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 588891.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47,  2,  5,  8, 55, 30, 10, 49, 61, 31, 44, 60, 29, 20, 65, 35, 73,
       62, 40, 67, 14, 53, 33, 21, 34, 18, 42, 19, 56,  9,  6,  3, 69, 72,
       57, 70, 74, 17, 36,  0, 66, 27, 26, 71, 12,  4, 52,  7, 64, 59, 11,
       15, 45, 22, 13,  1, 51, 39, 25, 41, 43, 48, 75, 63, 54, 37, 28, 50,
       24, 58, 32, 38, 23, 68, 46, 16]), 'cur_cost': 582687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:15,283 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:15,284 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:15,288 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=161490.000, 多样性=0.974
2025-08-05 10:29:15,288 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:15,288 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:15,288 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:15,288 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07257449797742667, 'best_improvement': -0.07295196332469603}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.000300210147103163}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.1259530963885008, 'recent_improvements': [0.05447713289208237, 0.1549522930324901, -0.19742905988491924], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 108234, 'new_best_cost': 108234, 'quality_improvement': 0.0, 'old_diversity': 0.881578947368421, 'new_diversity': 0.881578947368421, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:15,289 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:15,289 - __main__ - INFO - pr76 开始进化第 5 代
2025-08-05 10:29:15,289 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:15,290 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:15,291 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=161490.000, 多样性=0.974
2025-08-05 10:29:15,291 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:15,294 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.974
2025-08-05 10:29:15,294 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:15,296 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.882
2025-08-05 10:29:15,298 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:15,298 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:15,298 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:15,298 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:15,339 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -3701.693, 聚类评分: 0.000, 覆盖率: 0.163, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:15,339 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:15,340 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:15,340 - visualization.landscape_visualizer - INFO - 设置当前实例名: pr76
2025-08-05 10:29:15,345 - visualization.landscape_visualizer - INFO - 插值约束: 18 个点被约束到最小值 108234.00
2025-08-05 10:29:15,346 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 21457.69 → 20198.90
2025-08-05 10:29:15,458 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\landscape_pr76_iter_145_20250805_102915.html
2025-08-05 10:29:15,529 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_pr76\dashboard_pr76_iter_145_20250805_102915.html
2025-08-05 10:29:15,529 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 145
2025-08-05 10:29:15,530 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:15,530 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2319秒
2025-08-05 10:29:15,530 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.13333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3701.693333333331, 'local_optima_density': 0.13333333333333333, 'gradient_variance': 23850104876.948624, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1626, 'fitness_entropy': 0.9346062324545016, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3701.693)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.163)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360955.3394246, 'performance_metrics': {}}}
2025-08-05 10:29:15,530 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:15,530 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:15,530 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:15,531 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:15,531 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,531 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:15,532 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,532 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,532 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:15,532 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:15,532 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:15,533 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:15,533 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:15,533 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:15,533 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:15,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,537 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:15,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,538 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,539 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 481946.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,540 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [26, 65, 20, 11, 10, 15, 35, 12, 13, 36, 41, 4, 52, 58, 32, 63, 28, 27, 42, 57, 55, 50, 51, 66, 38, 24, 59, 40, 49, 56, 53, 75, 71, 62, 70, 48, 34, 29, 33, 0, 39, 72, 3, 7, 61, 54, 44, 43, 1, 21, 47, 45, 2, 22, 23, 25, 6, 69, 31, 67, 73, 8, 5, 74, 18, 60, 16, 14, 9, 68, 37, 17, 30, 46, 64, 19], 'cur_cost': 481946.0, 'intermediate_solutions': [{'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 73, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 54, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 433328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 41, 56, 30, 18, 37, 51, 73, 14, 21], 'cur_cost': 424585.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 33, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 417698.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,540 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 481946.00)
2025-08-05 10:29:15,540 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:15,540 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:15,540 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,544 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,544 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145667.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,545 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 12, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 13, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145667.0, 'intermediate_solutions': [{'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 46, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 41, 65, 69], 'cur_cost': 401773.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 75, 2, 12, 6, 17, 7, 21, 55, 27, 4, 1, 73, 13, 14, 74, 68, 46, 65, 69], 'cur_cost': 414816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 49, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 61, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 408633.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,546 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 145667.00)
2025-08-05 10:29:15,546 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:15,546 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:15,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,548 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 76
2025-08-05 10:29:15,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,549 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,549 - ExplorationExpert - INFO - 探索路径生成完成，成本: 502138.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,549 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [56, 7, 16, 45, 34, 32, 23, 35, 17, 36, 9, 40, 59, 62, 37, 50, 30, 29, 61, 57, 55, 33, 52, 66, 38, 24, 68, 54, 49, 53, 41, 42, 27, 28, 31, 48, 18, 19, 70, 0, 6, 72, 3, 11, 10, 71, 44, 73, 1, 43, 20, 22, 2, 21, 13, 25, 74, 69, 47, 67, 12, 8, 46, 4, 5, 51, 75, 14, 15, 65, 26, 60, 64, 58, 39, 63], 'cur_cost': 502138.0, 'intermediate_solutions': [{'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 74, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 46, 69, 26, 47, 15, 0], 'cur_cost': 505123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 7, 13, 11, 6, 16, 4, 72, 70, 10, 9, 27, 31, 29, 64, 35, 28, 48, 40, 52, 59, 3, 49, 24, 50, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 546081.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 30, 62, 37, 63, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 533184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,549 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 502138.00)
2025-08-05 10:29:15,550 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:15,550 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:15,550 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,553 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,553 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 144378.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,554 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 20, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 144378.0, 'intermediate_solutions': [{'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 56, 65, 64, 55, 54, 57, 50, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 170346.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 17, 36, 35, 34, 33, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 163890.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 33, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 169291.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,555 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 144378.00)
2025-08-05 10:29:15,555 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:15,555 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,555 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,556 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 566423.0
2025-08-05 10:29:15,582 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:29:15,582 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0, 108159]
2025-08-05 10:29:15,582 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,587 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,587 - ExploitationExpert - INFO - populations: [{'tour': [26, 65, 20, 11, 10, 15, 35, 12, 13, 36, 41, 4, 52, 58, 32, 63, 28, 27, 42, 57, 55, 50, 51, 66, 38, 24, 59, 40, 49, 56, 53, 75, 71, 62, 70, 48, 34, 29, 33, 0, 39, 72, 3, 7, 61, 54, 44, 43, 1, 21, 47, 45, 2, 22, 23, 25, 6, 69, 31, 67, 73, 8, 5, 74, 18, 60, 16, 14, 9, 68, 37, 17, 30, 46, 64, 19], 'cur_cost': 481946.0}, {'tour': [0, 17, 12, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 13, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145667.0}, {'tour': [56, 7, 16, 45, 34, 32, 23, 35, 17, 36, 9, 40, 59, 62, 37, 50, 30, 29, 61, 57, 55, 33, 52, 66, 38, 24, 68, 54, 49, 53, 41, 42, 27, 28, 31, 48, 18, 19, 70, 0, 6, 72, 3, 11, 10, 71, 44, 73, 1, 43, 20, 22, 2, 21, 13, 25, 74, 69, 47, 67, 12, 8, 46, 4, 5, 51, 75, 14, 15, 65, 26, 60, 64, 58, 39, 63], 'cur_cost': 502138.0}, {'tour': [0, 20, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 144378.0}, {'tour': array([60, 69, 28, 75,  0, 35, 18,  1, 33, 31, 16, 12, 45, 22, 44, 67, 64,
       57, 70, 19, 15, 11, 71,  3, 61, 56, 29, 41, 32, 63, 58,  7, 59, 14,
       43, 24, 36, 13, 23, 20, 47, 21,  8, 52, 30, 26, 40, 17, 54, 27, 34,
       49, 46, 72,  4, 55, 74, 62,  6, 66,  9, 42, 39,  5, 68, 51, 38,  2,
       53, 73, 10, 48, 25, 50, 65, 37], dtype=int64), 'cur_cost': 566423.0}, {'tour': [48, 55, 59, 45, 19, 32, 18, 53, 75, 58, 6, 46, 1, 36, 56, 72, 64, 12, 38, 49, 57, 24, 9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39, 43, 63, 35, 47, 42, 3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29, 27, 62, 4, 2, 44, 66, 17, 7, 34, 73, 15, 10, 50, 8, 60, 22, 21, 0, 23, 28, 70, 61, 13, 5, 31], 'cur_cost': 559560.0}, {'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 519550.0}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 168672.0}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 372163.0}, {'tour': [24, 33, 38, 17, 41, 21, 58, 30, 42, 18, 31, 8, 63, 74, 25, 1, 15, 44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35, 0, 12, 43, 56, 68, 32, 39, 59, 3, 60, 10, 61, 48, 14, 34, 47, 23, 11, 9, 51, 4, 57, 46, 66, 73, 45, 26, 6, 40, 22, 69, 75, 19, 7, 27, 53, 2, 52, 5, 20, 36, 65, 28, 64, 67, 54, 49], 'cur_cost': 601030.0}]
2025-08-05 10:29:15,588 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:29:15,589 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 375, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 375, 'cache_hits': 0, 'similarity_calculations': 1957, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,590 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([60, 69, 28, 75,  0, 35, 18,  1, 33, 31, 16, 12, 45, 22, 44, 67, 64,
       57, 70, 19, 15, 11, 71,  3, 61, 56, 29, 41, 32, 63, 58,  7, 59, 14,
       43, 24, 36, 13, 23, 20, 47, 21,  8, 52, 30, 26, 40, 17, 54, 27, 34,
       49, 46, 72,  4, 55, 74, 62,  6, 66,  9, 42, 39,  5, 68, 51, 38,  2,
       53, 73, 10, 48, 25, 50, 65, 37], dtype=int64), 'cur_cost': 566423.0, 'intermediate_solutions': [{'tour': array([17, 65, 20, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 574136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 17, 65, 20, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 570466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 36, 17, 65, 20, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 565291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 36, 17, 65, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 570232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 11, 36, 17, 65, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 560298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,590 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 566423.00)
2025-08-05 10:29:15,591 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:15,591 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,591 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,592 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 561394.0
2025-08-05 10:29:15,610 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:29:15,610 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0, 108159]
2025-08-05 10:29:15,610 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,613 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,613 - ExploitationExpert - INFO - populations: [{'tour': [26, 65, 20, 11, 10, 15, 35, 12, 13, 36, 41, 4, 52, 58, 32, 63, 28, 27, 42, 57, 55, 50, 51, 66, 38, 24, 59, 40, 49, 56, 53, 75, 71, 62, 70, 48, 34, 29, 33, 0, 39, 72, 3, 7, 61, 54, 44, 43, 1, 21, 47, 45, 2, 22, 23, 25, 6, 69, 31, 67, 73, 8, 5, 74, 18, 60, 16, 14, 9, 68, 37, 17, 30, 46, 64, 19], 'cur_cost': 481946.0}, {'tour': [0, 17, 12, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 13, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145667.0}, {'tour': [56, 7, 16, 45, 34, 32, 23, 35, 17, 36, 9, 40, 59, 62, 37, 50, 30, 29, 61, 57, 55, 33, 52, 66, 38, 24, 68, 54, 49, 53, 41, 42, 27, 28, 31, 48, 18, 19, 70, 0, 6, 72, 3, 11, 10, 71, 44, 73, 1, 43, 20, 22, 2, 21, 13, 25, 74, 69, 47, 67, 12, 8, 46, 4, 5, 51, 75, 14, 15, 65, 26, 60, 64, 58, 39, 63], 'cur_cost': 502138.0}, {'tour': [0, 20, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 144378.0}, {'tour': array([60, 69, 28, 75,  0, 35, 18,  1, 33, 31, 16, 12, 45, 22, 44, 67, 64,
       57, 70, 19, 15, 11, 71,  3, 61, 56, 29, 41, 32, 63, 58,  7, 59, 14,
       43, 24, 36, 13, 23, 20, 47, 21,  8, 52, 30, 26, 40, 17, 54, 27, 34,
       49, 46, 72,  4, 55, 74, 62,  6, 66,  9, 42, 39,  5, 68, 51, 38,  2,
       53, 73, 10, 48, 25, 50, 65, 37], dtype=int64), 'cur_cost': 566423.0}, {'tour': array([23, 69, 72, 42, 65, 48, 38,  0, 50, 21, 29, 52, 53, 47,  5,  6, 18,
       45,  3,  2, 37, 41, 14,  4, 62, 24, 10, 63, 49, 19, 34, 51, 59, 32,
       74, 68, 17,  7, 16,  8, 60, 27,  1, 25, 43, 75, 58, 15, 33, 73, 55,
       61, 44, 22, 30, 56, 67, 12, 46, 70, 20, 11, 35, 13, 71, 57, 54, 64,
       40, 66, 36, 31, 39, 28,  9, 26], dtype=int64), 'cur_cost': 561394.0}, {'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 519550.0}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 168672.0}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 372163.0}, {'tour': [24, 33, 38, 17, 41, 21, 58, 30, 42, 18, 31, 8, 63, 74, 25, 1, 15, 44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35, 0, 12, 43, 56, 68, 32, 39, 59, 3, 60, 10, 61, 48, 14, 34, 47, 23, 11, 9, 51, 4, 57, 46, 66, 73, 45, 26, 6, 40, 22, 69, 75, 19, 7, 27, 53, 2, 52, 5, 20, 36, 65, 28, 64, 67, 54, 49], 'cur_cost': 601030.0}]
2025-08-05 10:29:15,614 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:15,614 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 376, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 376, 'cache_hits': 0, 'similarity_calculations': 1968, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,616 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([23, 69, 72, 42, 65, 48, 38,  0, 50, 21, 29, 52, 53, 47,  5,  6, 18,
       45,  3,  2, 37, 41, 14,  4, 62, 24, 10, 63, 49, 19, 34, 51, 59, 32,
       74, 68, 17,  7, 16,  8, 60, 27,  1, 25, 43, 75, 58, 15, 33, 73, 55,
       61, 44, 22, 30, 56, 67, 12, 46, 70, 20, 11, 35, 13, 71, 57, 54, 64,
       40, 66, 36, 31, 39, 28,  9, 26], dtype=int64), 'cur_cost': 561394.0, 'intermediate_solutions': [{'tour': array([59, 55, 48, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 554251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 59, 55, 48, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 562234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 45, 59, 55, 48, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 558331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 45, 59, 55, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 564562.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 19, 45, 59, 55, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 565358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,616 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 561394.00)
2025-08-05 10:29:15,616 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:15,616 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:15,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,621 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,622 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174778.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,622 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 16, 21, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 22, 1, 2, 3, 5, 6, 7, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 174778.0, 'intermediate_solutions': [{'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 53, 4, 52, 58, 32, 63, 27, 42, 28, 41, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 520525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 59, 24, 67, 66, 51, 50, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 525102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 49, 2, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 12, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 528790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,622 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 174778.00)
2025-08-05 10:29:15,622 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:15,622 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:15,622 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,625 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 76
2025-08-05 10:29:15,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 171501.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,626 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 17, 2, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 3, 1, 22, 74, 75, 73, 69], 'cur_cost': 171501.0, 'intermediate_solutions': [{'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 11, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 63, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 210337.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 13, 12, 11, 10, 17, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 28, 29, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 178315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 45, 44, 43, 47, 46, 68, 75, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 197662.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,626 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 171501.00)
2025-08-05 10:29:15,626 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:15,626 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:15,626 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:15,634 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 76
2025-08-05 10:29:15,634 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,634 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,634 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,635 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:15,635 - ExplorationExpert - INFO - 探索路径生成完成，成本: 371684.0, 路径长度: 76, 收集中间解: 3
2025-08-05 10:29:15,635 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [40, 48, 49, 31, 27, 30, 33, 60, 59, 64, 67, 47, 46, 58, 51, 32, 50, 42, 20, 22, 43, 26, 38, 61, 57, 65, 41, 19, 21, 53, 39, 55, 25, 44, 23, 0, 29, 18, 17, 9, 6, 13, 8, 3, 1, 10, 73, 7, 74, 15, 5, 37, 12, 11, 16, 14, 36, 4, 35, 56, 62, 72, 66, 69, 54, 28, 2, 45, 52, 34, 68, 24, 75, 63, 70, 71], 'cur_cost': 371684.0, 'intermediate_solutions': [{'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 37, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 3, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 376426.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 74, 75, 5, 46, 45, 23, 25, 15, 13, 37, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 370357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 72, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 16, 6, 0, 67], 'cur_cost': 386645.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:15,635 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 371684.00)
2025-08-05 10:29:15,635 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:15,635 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:15,635 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:15,636 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 546192.0
2025-08-05 10:29:15,649 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:29:15,649 - ExploitationExpert - INFO - res_population_costs: [108234, 109118.0, 110135, 112983, 113650.0, 108159]
2025-08-05 10:29:15,649 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 30, 18, 19, 29,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 33, 34, 38, 37, 35, 36, 17, 16, 15, 14, 73,
       13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19, 18, 30, 29, 28, 31, 32,
       27, 26, 25,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 25,
       26, 28, 31, 32, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 33, 34, 35,
       36, 17, 16, 10, 11, 12, 13, 73, 14, 15, 37, 38, 39, 40, 59, 58, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 29, 19, 18, 30, 31, 32, 27, 42, 41, 53, 52, 51, 48, 49, 50, 54,
       57, 56, 55, 65, 64, 70, 71, 72, 63, 62, 61, 60, 58, 59, 40, 39, 33,
       34, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  8,  9,  4,
        3,  2,  5,  6,  7,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7, 73, 13, 12, 14, 15, 11,  8,
        9, 10, 16, 17, 36, 35, 37, 38, 34, 33, 39, 40, 59, 58, 60, 61, 62,
       63, 72, 71, 70, 64, 65, 55, 56, 57, 54, 50, 49, 48, 51, 52, 53, 41,
       42, 27, 32, 31, 30, 18, 19, 29, 28, 25, 26, 43, 47, 66, 69, 67, 68,
       46, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-08-05 10:29:15,652 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:15,652 - ExploitationExpert - INFO - populations: [{'tour': [26, 65, 20, 11, 10, 15, 35, 12, 13, 36, 41, 4, 52, 58, 32, 63, 28, 27, 42, 57, 55, 50, 51, 66, 38, 24, 59, 40, 49, 56, 53, 75, 71, 62, 70, 48, 34, 29, 33, 0, 39, 72, 3, 7, 61, 54, 44, 43, 1, 21, 47, 45, 2, 22, 23, 25, 6, 69, 31, 67, 73, 8, 5, 74, 18, 60, 16, 14, 9, 68, 37, 17, 30, 46, 64, 19], 'cur_cost': 481946.0}, {'tour': [0, 17, 12, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 13, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145667.0}, {'tour': [56, 7, 16, 45, 34, 32, 23, 35, 17, 36, 9, 40, 59, 62, 37, 50, 30, 29, 61, 57, 55, 33, 52, 66, 38, 24, 68, 54, 49, 53, 41, 42, 27, 28, 31, 48, 18, 19, 70, 0, 6, 72, 3, 11, 10, 71, 44, 73, 1, 43, 20, 22, 2, 21, 13, 25, 74, 69, 47, 67, 12, 8, 46, 4, 5, 51, 75, 14, 15, 65, 26, 60, 64, 58, 39, 63], 'cur_cost': 502138.0}, {'tour': [0, 20, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 144378.0}, {'tour': array([60, 69, 28, 75,  0, 35, 18,  1, 33, 31, 16, 12, 45, 22, 44, 67, 64,
       57, 70, 19, 15, 11, 71,  3, 61, 56, 29, 41, 32, 63, 58,  7, 59, 14,
       43, 24, 36, 13, 23, 20, 47, 21,  8, 52, 30, 26, 40, 17, 54, 27, 34,
       49, 46, 72,  4, 55, 74, 62,  6, 66,  9, 42, 39,  5, 68, 51, 38,  2,
       53, 73, 10, 48, 25, 50, 65, 37], dtype=int64), 'cur_cost': 566423.0}, {'tour': array([23, 69, 72, 42, 65, 48, 38,  0, 50, 21, 29, 52, 53, 47,  5,  6, 18,
       45,  3,  2, 37, 41, 14,  4, 62, 24, 10, 63, 49, 19, 34, 51, 59, 32,
       74, 68, 17,  7, 16,  8, 60, 27,  1, 25, 43, 75, 58, 15, 33, 73, 55,
       61, 44, 22, 30, 56, 67, 12, 46, 70, 20, 11, 35, 13, 71, 57, 54, 64,
       40, 66, 36, 31, 39, 28,  9, 26], dtype=int64), 'cur_cost': 561394.0}, {'tour': [0, 16, 21, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 22, 1, 2, 3, 5, 6, 7, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 174778.0}, {'tour': [0, 17, 2, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 3, 1, 22, 74, 75, 73, 69], 'cur_cost': 171501.0}, {'tour': [40, 48, 49, 31, 27, 30, 33, 60, 59, 64, 67, 47, 46, 58, 51, 32, 50, 42, 20, 22, 43, 26, 38, 61, 57, 65, 41, 19, 21, 53, 39, 55, 25, 44, 23, 0, 29, 18, 17, 9, 6, 13, 8, 3, 1, 10, 73, 7, 74, 15, 5, 37, 12, 11, 16, 14, 36, 4, 35, 56, 62, 72, 66, 69, 54, 28, 2, 45, 52, 34, 68, 24, 75, 63, 70, 71], 'cur_cost': 371684.0}, {'tour': array([56, 62, 73, 15, 10, 54, 14, 38, 50, 32, 29, 60, 41, 64, 63, 28, 20,
       44,  1, 13,  3, 75,  4, 12, 35, 33, 21, 30, 16, 36,  7, 58, 47, 45,
       40, 59, 65, 52, 11, 24, 61, 51,  0,  2, 27, 37, 46, 57, 31,  6, 68,
       18, 34, 69, 26, 48, 22, 17, 72,  8, 43, 25, 70, 19, 66, 49, 39,  5,
       71, 67, 53, 42, 55, 74,  9, 23], dtype=int64), 'cur_cost': 546192.0}]
2025-08-05 10:29:15,654 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:15,654 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 377, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 377, 'cache_hits': 0, 'similarity_calculations': 1980, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:15,655 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([56, 62, 73, 15, 10, 54, 14, 38, 50, 32, 29, 60, 41, 64, 63, 28, 20,
       44,  1, 13,  3, 75,  4, 12, 35, 33, 21, 30, 16, 36,  7, 58, 47, 45,
       40, 59, 65, 52, 11, 24, 61, 51,  0,  2, 27, 37, 46, 57, 31,  6, 68,
       18, 34, 69, 26, 48, 22, 17, 72,  8, 43, 25, 70, 19, 66, 49, 39,  5,
       71, 67, 53, 42, 55, 74,  9, 23], dtype=int64), 'cur_cost': 546192.0, 'intermediate_solutions': [{'tour': array([38, 33, 24, 17, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 605875.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 38, 33, 24, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 602520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([41, 17, 38, 33, 24, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 591024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 17, 38, 33, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 598748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 41, 17, 38, 33, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 600862.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:15,655 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 546192.00)
2025-08-05 10:29:15,655 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:15,655 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:15,658 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [26, 65, 20, 11, 10, 15, 35, 12, 13, 36, 41, 4, 52, 58, 32, 63, 28, 27, 42, 57, 55, 50, 51, 66, 38, 24, 59, 40, 49, 56, 53, 75, 71, 62, 70, 48, 34, 29, 33, 0, 39, 72, 3, 7, 61, 54, 44, 43, 1, 21, 47, 45, 2, 22, 23, 25, 6, 69, 31, 67, 73, 8, 5, 74, 18, 60, 16, 14, 9, 68, 37, 17, 30, 46, 64, 19], 'cur_cost': 481946.0, 'intermediate_solutions': [{'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 73, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 14, 54, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 433328.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 33, 8, 61, 26, 3, 31, 16, 41, 56, 30, 18, 37, 51, 73, 14, 21], 'cur_cost': 424585.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 19, 2, 12, 15, 11, 35, 64, 17, 36, 39, 53, 52, 58, 32, 65, 63, 60, 54, 57, 55, 59, 50, 66, 38, 44, 74, 40, 49, 48, 28, 42, 71, 62, 70, 27, 9, 29, 5, 0, 4, 72, 6, 7, 13, 34, 33, 10, 20, 1, 23, 46, 24, 45, 43, 75, 25, 47, 69, 68, 67, 8, 61, 26, 3, 31, 16, 14, 73, 51, 37, 18, 30, 56, 41, 21], 'cur_cost': 417698.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 12, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 13, 73, 2, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25], 'cur_cost': 145667.0, 'intermediate_solutions': [{'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 46, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 6, 12, 2, 75, 74, 68, 41, 65, 69], 'cur_cost': 401773.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 49, 61, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 75, 2, 12, 6, 17, 7, 21, 55, 27, 4, 1, 73, 13, 14, 74, 68, 46, 65, 69], 'cur_cost': 414816.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 49, 56, 34, 5, 23, 19, 33, 9, 29, 36, 58, 62, 37, 15, 30, 18, 25, 28, 39, 50, 63, 52, 67, 24, 42, 31, 60, 43, 53, 51, 64, 71, 47, 48, 57, 40, 70, 72, 38, 16, 3, 11, 8, 20, 44, 54, 35, 59, 10, 41, 66, 22, 0, 45, 32, 14, 13, 73, 1, 4, 27, 55, 21, 7, 17, 61, 6, 12, 2, 75, 74, 68, 46, 65, 69], 'cur_cost': 408633.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [56, 7, 16, 45, 34, 32, 23, 35, 17, 36, 9, 40, 59, 62, 37, 50, 30, 29, 61, 57, 55, 33, 52, 66, 38, 24, 68, 54, 49, 53, 41, 42, 27, 28, 31, 48, 18, 19, 70, 0, 6, 72, 3, 11, 10, 71, 44, 73, 1, 43, 20, 22, 2, 21, 13, 25, 74, 69, 47, 67, 12, 8, 46, 4, 5, 51, 75, 14, 15, 65, 26, 60, 64, 58, 39, 63], 'cur_cost': 502138.0, 'intermediate_solutions': [{'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 74, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 46, 69, 26, 47, 15, 0], 'cur_cost': 505123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 62, 37, 63, 30, 60, 25, 54, 39, 33, 7, 13, 11, 6, 16, 4, 72, 70, 10, 9, 27, 31, 29, 64, 35, 28, 48, 40, 52, 59, 3, 49, 24, 50, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 546081.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [56, 19, 20, 14, 34, 5, 23, 22, 17, 42, 41, 36, 58, 30, 62, 37, 63, 60, 25, 54, 39, 33, 50, 24, 49, 3, 59, 52, 40, 48, 28, 35, 64, 29, 31, 27, 9, 10, 70, 72, 4, 16, 6, 11, 13, 7, 2, 38, 57, 21, 46, 1, 66, 45, 75, 43, 32, 18, 68, 73, 12, 71, 61, 55, 44, 51, 67, 8, 53, 65, 74, 69, 26, 47, 15, 0], 'cur_cost': 533184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 3, 2, 5, 6, 7, 1, 22, 21, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71, 72, 38, 37, 31, 32, 26, 25, 74, 75, 73], 'cur_cost': 144378.0, 'intermediate_solutions': [{'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 56, 65, 64, 55, 54, 57, 50, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 170346.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 17, 36, 35, 34, 33, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 163890.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 16, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 33, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 21, 22, 1, 2, 3, 5, 6, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 169291.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 69, 28, 75,  0, 35, 18,  1, 33, 31, 16, 12, 45, 22, 44, 67, 64,
       57, 70, 19, 15, 11, 71,  3, 61, 56, 29, 41, 32, 63, 58,  7, 59, 14,
       43, 24, 36, 13, 23, 20, 47, 21,  8, 52, 30, 26, 40, 17, 54, 27, 34,
       49, 46, 72,  4, 55, 74, 62,  6, 66,  9, 42, 39,  5, 68, 51, 38,  2,
       53, 73, 10, 48, 25, 50, 65, 37], dtype=int64), 'cur_cost': 566423.0, 'intermediate_solutions': [{'tour': array([17, 65, 20, 36, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 574136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 17, 65, 20, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 570466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 36, 17, 65, 20, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 565291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 36, 17, 65, 11, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 570232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 11, 36, 17, 65, 26, 22, 32, 14,  0, 58, 70, 38,  4, 71, 50, 16,
       29, 12, 35, 60, 59, 52, 72, 31, 30, 68, 15,  8,  7, 34, 75, 55, 21,
       10,  9, 51, 64, 47, 63, 39, 18, 48, 44, 61, 54, 13, 43, 46, 74, 62,
       53,  2, 25, 42, 37, 33,  3, 49, 56, 23, 66, 19, 28,  5, 24,  6, 73,
       27, 45, 57, 69, 41, 40,  1, 67]), 'cur_cost': 560298.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 69, 72, 42, 65, 48, 38,  0, 50, 21, 29, 52, 53, 47,  5,  6, 18,
       45,  3,  2, 37, 41, 14,  4, 62, 24, 10, 63, 49, 19, 34, 51, 59, 32,
       74, 68, 17,  7, 16,  8, 60, 27,  1, 25, 43, 75, 58, 15, 33, 73, 55,
       61, 44, 22, 30, 56, 67, 12, 46, 70, 20, 11, 35, 13, 71, 57, 54, 64,
       40, 66, 36, 31, 39, 28,  9, 26], dtype=int64), 'cur_cost': 561394.0, 'intermediate_solutions': [{'tour': array([59, 55, 48, 45, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 554251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([45, 59, 55, 48, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 562234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 45, 59, 55, 48, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 558331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 45, 59, 55, 19, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 564562.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 19, 45, 59, 55, 32, 18, 53, 75, 58,  6, 46,  1, 36, 56, 72, 64,
       12, 38, 49, 57, 24,  9, 51, 33, 52, 65, 14, 54, 41, 30, 16, 26, 39,
       43, 63, 35, 47, 42,  3, 37, 20, 40, 69, 67, 71, 11, 25, 68, 74, 29,
       27, 62,  4,  2, 44, 66, 17,  7, 34, 73, 15, 10, 50,  8, 60, 22, 21,
        0, 23, 28, 70, 61, 13,  5, 31]), 'cur_cost': 565358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 21, 11, 10, 15, 14, 12, 13, 8, 9, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 37, 38, 32, 31, 25, 26, 43, 47, 46, 44, 45, 23, 24, 20, 22, 1, 2, 3, 5, 6, 7, 73, 74, 75, 68, 67, 66, 49, 48, 70, 71, 72, 69], 'cur_cost': 174778.0, 'intermediate_solutions': [{'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 53, 4, 52, 58, 32, 63, 27, 42, 28, 41, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 520525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 49, 2, 12, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 59, 24, 67, 66, 51, 50, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 525102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 49, 2, 15, 35, 64, 33, 36, 41, 4, 52, 58, 32, 63, 27, 42, 28, 53, 50, 51, 66, 67, 24, 59, 40, 60, 43, 62, 61, 29, 70, 48, 57, 39, 5, 34, 12, 38, 72, 3, 37, 8, 20, 10, 54, 1, 23, 47, 46, 44, 0, 45, 71, 69, 13, 17, 6, 21, 31, 68, 14, 11, 65, 74, 18, 30, 9, 55, 7, 25, 16, 56, 73, 19, 75, 26], 'cur_cost': 528790.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 2, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 51, 52, 53, 41, 42, 27, 28, 29, 30, 18, 19, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 36, 35, 34, 33, 39, 40, 59, 58, 57, 56, 62, 63, 61, 60, 54, 55, 50, 65, 64, 70, 71, 72, 38, 37, 31, 32, 26, 25, 3, 1, 22, 74, 75, 73, 69], 'cur_cost': 171501.0, 'intermediate_solutions': [{'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 11, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 63, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 210337.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 13, 12, 11, 10, 17, 36, 35, 34, 33, 39, 40, 59, 58, 60, 61, 63, 62, 56, 57, 54, 55, 64, 65, 50, 51, 52, 53, 41, 42, 27, 28, 29, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 75, 45, 44, 43, 47, 46, 68, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 178315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 2, 6, 7, 5, 4, 19, 18, 30, 29, 28, 27, 42, 41, 53, 52, 51, 50, 65, 64, 55, 54, 57, 56, 62, 63, 61, 60, 58, 59, 40, 39, 33, 34, 35, 36, 17, 10, 11, 12, 13, 14, 15, 9, 8, 3, 20, 24, 23, 21, 22, 1, 74, 45, 44, 43, 47, 46, 68, 75, 67, 66, 49, 48, 26, 25, 31, 32, 38, 37, 73, 72, 71, 70, 69], 'cur_cost': 197662.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [40, 48, 49, 31, 27, 30, 33, 60, 59, 64, 67, 47, 46, 58, 51, 32, 50, 42, 20, 22, 43, 26, 38, 61, 57, 65, 41, 19, 21, 53, 39, 55, 25, 44, 23, 0, 29, 18, 17, 9, 6, 13, 8, 3, 1, 10, 73, 7, 74, 15, 5, 37, 12, 11, 16, 14, 36, 4, 35, 56, 62, 72, 66, 69, 54, 28, 2, 45, 52, 34, 68, 24, 75, 63, 70, 71], 'cur_cost': 371684.0, 'intermediate_solutions': [{'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 37, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 3, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 376426.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 74, 75, 5, 46, 45, 23, 25, 15, 13, 37, 10, 1, 51, 55, 61, 70, 71, 72, 16, 6, 0, 67], 'cur_cost': 370357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 38, 54, 58, 52, 42, 26, 44, 41, 53, 19, 36, 35, 34, 60, 65, 64, 31, 17, 4, 14, 30, 12, 33, 32, 43, 22, 11, 73, 20, 24, 68, 49, 56, 50, 28, 72, 40, 39, 9, 3, 7, 29, 47, 21, 2, 8, 59, 57, 62, 66, 69, 63, 48, 18, 37, 13, 15, 25, 23, 45, 46, 5, 75, 74, 10, 1, 51, 55, 61, 70, 71, 16, 6, 0, 67], 'cur_cost': 386645.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 62, 73, 15, 10, 54, 14, 38, 50, 32, 29, 60, 41, 64, 63, 28, 20,
       44,  1, 13,  3, 75,  4, 12, 35, 33, 21, 30, 16, 36,  7, 58, 47, 45,
       40, 59, 65, 52, 11, 24, 61, 51,  0,  2, 27, 37, 46, 57, 31,  6, 68,
       18, 34, 69, 26, 48, 22, 17, 72,  8, 43, 25, 70, 19, 66, 49, 39,  5,
       71, 67, 53, 42, 55, 74,  9, 23], dtype=int64), 'cur_cost': 546192.0, 'intermediate_solutions': [{'tour': array([38, 33, 24, 17, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 605875.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 38, 33, 24, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 602520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([41, 17, 38, 33, 24, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 591024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 17, 38, 33, 41, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 598748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 41, 17, 38, 33, 21, 58, 30, 42, 18, 31,  8, 63, 74, 25,  1, 15,
       44, 71, 55, 13, 62, 70, 16, 37, 50, 72, 29, 35,  0, 12, 43, 56, 68,
       32, 39, 59,  3, 60, 10, 61, 48, 14, 34, 47, 23, 11,  9, 51,  4, 57,
       46, 66, 73, 45, 26,  6, 40, 22, 69, 75, 19,  7, 27, 53,  2, 52,  5,
       20, 36, 65, 28, 64, 67, 54, 49]), 'cur_cost': 600862.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:15,659 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:15,659 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:15,663 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=144378.000, 多样性=0.966
2025-08-05 10:29:15,663 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:15,663 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:15,663 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:15,664 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10345772828359835, 'best_improvement': 0.10596321753668958}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.008403361344537917}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.11376339550495838, 'recent_improvements': [0.1549522930324901, -0.19742905988491924, -0.07257449797742667], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 108159, 'new_best_cost': 108159, 'quality_improvement': 0.0, 'old_diversity': 0.8061403508771929, 'new_diversity': 0.8061403508771929, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:15,664 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:15,668 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\pr76_solution.json
2025-08-05 10:29:15,668 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\pr76_20250805_102915.solution
2025-08-05 10:29:15,668 - __main__ - INFO - 实例执行完成 - 运行时间: 1.85s, 最佳成本: 108159
2025-08-05 10:29:15,668 - __main__ - INFO - 实例 pr76 处理完成
