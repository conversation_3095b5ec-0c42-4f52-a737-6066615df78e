# -*- coding: utf-8 -*-
"""
重构后的StatsExpert示例

展示如何使用新的工具模块来消除代码冗余。
这是一个示例文件，展示重构前后的对比。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from experts.base.expert_base import ExpertBase
from utils.analysis_utils import AnalysisUtils, PathUtils, DataValidator
from utils.similarity_utils import SimilarityCalculator, DiversityAnalyzer


class RefactoredStatsExpert(ExpertBase):
    """重构后的统计分析专家"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix=None):
        """
        分析种群统计特征
        
        参数:
            populations: 种群列表
            distance_matrix: 距离矩阵（可选）
            
        返回:
            分析结果字典
        """
        self.logger.info("开始种群统计分析")
        
        # 数据验证
        if not DataValidator.validate_population_data(populations):
            self.logger.warning("种群数据验证失败")
            return self._get_empty_analysis_result()
        
        # 使用统一工具提取数据
        costs = PathUtils.extract_costs_safely(populations)
        tours = PathUtils.extract_tours_safely(populations)
        
        if not costs or not tours:
            self.logger.warning("无法提取有效的成本或路径数据")
            return self._get_empty_analysis_result()
        
        # 使用统一工具计算基础统计
        cost_stats = AnalysisUtils.calculate_basic_stats(costs)
        
        # 计算百分位数
        percentiles = AnalysisUtils.calculate_percentiles(costs, [25, 50, 75, 90, 95])
        cost_stats.update({
            "q25": percentiles[0],
            "median": percentiles[1], 
            "q75": percentiles[2],
            "q90": percentiles[3],
            "q95": percentiles[4]
        })
        
        # 使用统一的多样性计算
        diversity_analysis = DiversityAnalyzer.analyze_population_diversity(tours)
        
        # 计算种群规模统计
        population_stats = {
            "size": len(populations),
            "valid_tours": len(tours),
            "avg_tour_length": sum(len(tour) for tour in tours) / len(tours) if tours else 0
        }
        
        # 分析适应度分布
        fitness_distribution = self._analyze_fitness_distribution(costs)
        
        analysis_result = {
            "cost_stats": cost_stats,
            "diversity_analysis": diversity_analysis,
            "population_stats": population_stats,
            "fitness_distribution": fitness_distribution,
            "analysis_metadata": {
                "total_individuals": len(populations),
                "valid_individuals": len(costs),
                "analysis_timestamp": self._get_timestamp()
            }
        }
        
        self.logger.info(f"统计分析完成: 种群大小={len(populations)}, "
                        f"平均成本={cost_stats['mean']:.3f}, "
                        f"多样性={diversity_analysis['hamming_diversity']:.3f}")
        
        return analysis_result
    
    def generate_report(self, analysis_result, coordinates=None, distance_matrix=None):
        """
        生成统计分析报告
        
        参数:
            analysis_result: 分析结果
            coordinates: 坐标信息（可选）
            distance_matrix: 距离矩阵（可选）
            
        返回:
            统计报告
        """
        if not analysis_result:
            return self._get_empty_report()
        
        cost_stats = analysis_result.get("cost_stats", {})
        diversity_analysis = analysis_result.get("diversity_analysis", {})
        population_stats = analysis_result.get("population_stats", {})
        
        # 生成综合报告
        report = {
            "summary": {
                "population_size": population_stats.get("size", 0),
                "best_cost": cost_stats.get("min", float('inf')),
                "worst_cost": cost_stats.get("max", 0),
                "avg_cost": cost_stats.get("mean", 0),
                "cost_std": cost_stats.get("std", 0),
                "diversity_score": diversity_analysis.get("hamming_diversity", 0)
            },
            "detailed_stats": {
                "cost_statistics": cost_stats,
                "diversity_metrics": diversity_analysis,
                "population_info": population_stats,
                "fitness_distribution": analysis_result.get("fitness_distribution", {})
            },
            "quality_indicators": {
                "convergence_level": self._calculate_convergence_level(cost_stats),
                "diversity_level": self._classify_diversity_level(diversity_analysis),
                "population_health": self._assess_population_health(analysis_result)
            }
        }
        
        # 添加空间信息（如果可用）
        if coordinates is not None:
            spatial_info = self._analyze_spatial_distribution(coordinates, distance_matrix)
            report["spatial_analysis"] = spatial_info
        
        return report
    
    def _get_empty_analysis_result(self):
        """返回空的分析结果"""
        return {
            "cost_stats": {"min": 0, "max": 0, "mean": 0, "std": 0},
            "diversity_analysis": {"hamming_diversity": 0, "edge_diversity": 0},
            "population_stats": {"size": 0, "valid_tours": 0, "avg_tour_length": 0},
            "fitness_distribution": {},
            "analysis_metadata": {"total_individuals": 0, "valid_individuals": 0}
        }
    
    def _get_empty_report(self):
        """返回空的报告"""
        return {
            "summary": {
                "population_size": 0,
                "best_cost": float('inf'),
                "worst_cost": 0,
                "avg_cost": 0,
                "cost_std": 0,
                "diversity_score": 0
            },
            "detailed_stats": {},
            "quality_indicators": {
                "convergence_level": "unknown",
                "diversity_level": "unknown", 
                "population_health": "poor"
            }
        }
    
    def _analyze_fitness_distribution(self, costs):
        """分析适应度分布"""
        if not costs:
            return {}
        
        # 计算分布特征
        sorted_costs = sorted(costs)
        n = len(costs)
        
        return {
            "range": max(costs) - min(costs),
            "iqr": AnalysisUtils.calculate_percentiles(costs, [75])[0] - 
                   AnalysisUtils.calculate_percentiles(costs, [25])[0],
            "skewness": self._calculate_skewness(costs),
            "top_10_percent_avg": sum(sorted_costs[:max(1, n//10)]) / max(1, n//10),
            "bottom_10_percent_avg": sum(sorted_costs[-max(1, n//10):]) / max(1, n//10)
        }
    
    def _calculate_skewness(self, values):
        """计算偏度"""
        if len(values) < 3:
            return 0.0
        
        mean = sum(values) / len(values)
        std = AnalysisUtils.calculate_std(values)
        
        if std == 0:
            return 0.0
        
        skewness = sum((x - mean) ** 3 for x in values) / (len(values) * std ** 3)
        return skewness
    
    def _calculate_convergence_level(self, cost_stats):
        """计算收敛水平"""
        if not cost_stats or cost_stats.get("mean", 0) == 0:
            return "unknown"
        
        cv = cost_stats.get("std", 0) / cost_stats.get("mean", 1)  # 变异系数
        
        if cv < 0.05:
            return "high"
        elif cv < 0.15:
            return "medium"
        else:
            return "low"
    
    def _classify_diversity_level(self, diversity_analysis):
        """分类多样性水平"""
        diversity_score = diversity_analysis.get("hamming_diversity", 0)
        
        if diversity_score > 0.7:
            return "high"
        elif diversity_score > 0.3:
            return "medium"
        else:
            return "low"
    
    def _assess_population_health(self, analysis_result):
        """评估种群健康状况"""
        cost_stats = analysis_result.get("cost_stats", {})
        diversity_analysis = analysis_result.get("diversity_analysis", {})
        
        # 综合评估
        convergence_score = 1.0 - min(1.0, cost_stats.get("std", 0) / max(1.0, cost_stats.get("mean", 1)))
        diversity_score = diversity_analysis.get("hamming_diversity", 0)
        
        health_score = (convergence_score + diversity_score) / 2
        
        if health_score > 0.7:
            return "excellent"
        elif health_score > 0.5:
            return "good"
        elif health_score > 0.3:
            return "fair"
        else:
            return "poor"
    
    def _analyze_spatial_distribution(self, coordinates, distance_matrix):
        """分析空间分布（如果有坐标信息）"""
        # 这里可以添加空间分析逻辑
        # 为了简化，返回基本信息
        return {
            "has_coordinates": coordinates is not None,
            "has_distance_matrix": distance_matrix is not None,
            "coordinate_count": len(coordinates) if coordinates else 0
        }
    
    def _get_timestamp(self):
        """获取时间戳"""
        import time
        return time.time()


# 对比：原始实现 vs 重构后实现
class ComparisonExample:
    """展示重构前后的对比"""
    
    @staticmethod
    def show_redundancy_elimination():
        """展示冗余消除的效果"""
        
        print("=== 重构前后对比 ===")
        print()
        
        print("1. 标准差计算 - 重构前:")
        print("   - StatsExpert._calculate_std() - 15行代码")
        print("   - EliteExpert._calculate_std() - 15行代码 (完全重复)")
        print("   - 总计: 30行重复代码")
        print()
        
        print("1. 标准差计算 - 重构后:")
        print("   - AnalysisUtils.calculate_std() - 统一实现")
        print("   - 所有专家共享使用")
        print("   - 节省: 15行代码")
        print()
        
        print("2. 多样性计算 - 重构前:")
        print("   - utils.calculate_population_diversity() - 60行")
        print("   - PathExpert._calculate_path_similarity() - 30行")
        print("   - EliteExpert._analyze_elite_diversity() - 35行")
        print("   - 其他多处重复实现")
        print("   - 总计: ~200行重复/相似代码")
        print()
        
        print("2. 多样性计算 - 重构后:")
        print("   - SimilarityCalculator - 统一接口")
        print("   - DiversityAnalyzer - 高级分析功能")
        print("   - 支持多种计算方法")
        print("   - 节省: ~150行代码")
        print()
        
        print("3. 数据提取 - 重构前:")
        print("   - 每个专家都有自己的数据提取逻辑")
        print("   - 重复的验证和转换代码")
        print("   - 总计: ~80行重复代码")
        print()
        
        print("3. 数据提取 - 重构后:")
        print("   - PathUtils.extract_tours_safely()")
        print("   - PathUtils.extract_costs_safely()")
        print("   - DataValidator.validate_population_data()")
        print("   - 节省: ~60行代码")
        print()
        
        print("总体效果:")
        print("- 代码减少: ~225行")
        print("- 维护性提升: 统一接口，易于修改")
        print("- 性能提升: 避免重复计算")
        print("- 可扩展性: 新专家可直接使用工具函数")


if __name__ == "__main__":
    # 展示对比效果
    ComparisonExample.show_redundancy_elimination()
