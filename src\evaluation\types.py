"""
评估统计系统的类型定义

包含所有枚举类型、数据类和统计结构的定义。
"""

import time
from enum import Enum, auto
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import threading

class EvaluationType(Enum):
    """评估类型枚举"""
    FULL_EVALUATION = "full_evaluation"           # 完整路径评估
    DELTA_EVALUATION = "delta_evaluation"         # 增量评估（如2-opt）
    LOCAL_SEARCH = "local_search"                 # 局部搜索评估
    INITIALIZATION = "initialization"             # 初始化评估
    CROSSOVER = "crossover"                       # 交叉操作评估
    MUTATION = "mutation"                         # 变异操作评估
    SELECTION = "selection"                       # 选择操作评估
    ELITE_VERIFICATION = "elite_verification"     # 精英解验证评估
    STRATEGY_EXECUTION = "strategy_execution"     # 策略执行评估
    NEIGHBORHOOD_EVALUATION = "neighborhood"     # 邻域评估

class AlgorithmPhase(Enum):
    """算法阶段枚举"""
    INITIALIZATION = "initialization"             # 初始化阶段
    LANDSCAPE_ANALYSIS = "landscape_analysis"     # 景观分析阶段
    STRATEGY_SELECTION = "strategy_selection"     # 策略选择阶段
    EVOLUTION = "evolution"                       # 进化阶段
    LOCAL_SEARCH = "local_search"                 # 局部搜索阶段
    ASSESSMENT = "assessment"                     # 评估阶段
    TERMINATION = "termination"                   # 终止阶段

class EvaluationComplexity(Enum):
    """评估复杂度枚举"""
    O_1 = "O(1)"                                  # 常数时间复杂度
    O_N = "O(n)"                                  # 线性时间复杂度
    O_N2 = "O(n²)"                                # 平方时间复杂度

@dataclass
class EvaluationRecord:
    """单次评估记录"""
    timestamp: float = field(default_factory=time.time)
    evaluation_type: EvaluationType = EvaluationType.FULL_EVALUATION
    algorithm_phase: AlgorithmPhase = AlgorithmPhase.INITIALIZATION
    thread_id: int = field(default_factory=lambda: threading.get_ident())
    iteration: Optional[int] = None
    count: int = 1
    cost_value: Optional[float] = None
    improvement: Optional[float] = None
    complexity: EvaluationComplexity = EvaluationComplexity.O_N
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class EvaluationStatistics:
    """评估统计信息"""
    total_count: int = 0
    start_time: float = field(default_factory=time.time)
    last_update_time: float = field(default_factory=time.time)
    
    # 按类型统计
    by_type: Dict[EvaluationType, int] = field(default_factory=lambda: {
        eval_type: 0 for eval_type in EvaluationType
    })
    
    # 按阶段统计
    by_phase: Dict[AlgorithmPhase, int] = field(default_factory=lambda: {
        phase: 0 for phase in AlgorithmPhase
    })
    
    # 按复杂度统计
    by_complexity: Dict[EvaluationComplexity, int] = field(default_factory=lambda: {
        complexity: 0 for complexity in EvaluationComplexity
    })
    
    # 按线程统计
    by_thread: Dict[int, int] = field(default_factory=dict)
    
    # 成本统计
    total_cost_sum: float = 0.0
    min_cost: Optional[float] = None
    max_cost: Optional[float] = None
    best_cost: Optional[float] = None
    
    # 改进统计
    total_improvement: float = 0.0
    improvement_count: int = 0
    
    def get_elapsed_time(self) -> float:
        """获取运行时间"""
        return self.last_update_time - self.start_time
    
    def get_evaluations_per_second(self) -> float:
        """获取每秒评估次数"""
        elapsed = self.get_elapsed_time()
        return self.total_count / elapsed if elapsed > 0 else 0.0
    
    def get_average_cost(self) -> Optional[float]:
        """获取平均成本"""
        if self.total_count > 0 and self.total_cost_sum > 0:
            return self.total_cost_sum / self.total_count
        return None
    
    def get_average_improvement(self) -> Optional[float]:
        """获取平均改进"""
        if self.improvement_count > 0:
            return self.total_improvement / self.improvement_count
        return None
    
    def update_cost_statistics(self, cost_value: float):
        """更新成本统计"""
        if cost_value is not None:
            self.total_cost_sum += cost_value
            
            if self.min_cost is None or cost_value < self.min_cost:
                self.min_cost = cost_value
            
            if self.max_cost is None or cost_value > self.max_cost:
                self.max_cost = cost_value
            
            if self.best_cost is None or cost_value < self.best_cost:
                self.best_cost = cost_value
    
    def update_improvement_statistics(self, improvement: float):
        """更新改进统计"""
        if improvement is not None:
            self.total_improvement += improvement
            self.improvement_count += 1

@dataclass
class PhaseStatistics:
    """阶段统计信息"""
    phase: AlgorithmPhase
    start_time: float
    end_time: Optional[float] = None
    evaluation_count: int = 0
    cost_improvements: List[float] = field(default_factory=list)
    
    def get_duration(self) -> Optional[float]:
        """获取阶段持续时间"""
        if self.end_time is not None:
            return self.end_time - self.start_time
        return None
    
    def get_evaluations_per_second(self) -> Optional[float]:
        """获取阶段内每秒评估次数"""
        duration = self.get_duration()
        if duration is not None and duration > 0:
            return self.evaluation_count / duration
        return None
