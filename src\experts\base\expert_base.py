# -*- coding: utf-8 -*-
"""
专家基类模块

包含所有专家类的基类定义，提供统一的接口和基础功能。
"""

import logging


class ExpertBase:
    """专家基类，所有专家模块继承自此类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def analyze(self, *args, **kwargs):
        """专家分析方法，由子类实现"""
        raise NotImplementedError
    
    def generate_report(self, analysis_result):
        """生成分析报告，由子类实现"""
        raise NotImplementedError
