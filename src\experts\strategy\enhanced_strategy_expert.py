# -*- coding: utf-8 -*-
"""
增强策略选择专家模块

重新设计的策略专家，实现个体级精准策略选择，充分利用适应度景观分析和个体状态信息。
"""

import json
import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from experts.base.expert_base import ExpertBase
from experts.analysis.individual_state_analyzer import IndividualStateAnalyzer, IndividualStateMetrics
from experts.strategy.strategy_parser import StrategyResponseParser, ParsedStrategyResponse
from experts.prompts.enhanced_strategy_prompts import generate_individual_strategy_prompt


class StrategyType(Enum):
    """策略类型枚举"""
    STRONG_EXPLORATION = "strong_exploration"
    BALANCED_EXPLORATION = "balanced_exploration" 
    INTELLIGENT_EXPLORATION = "intelligent_exploration"
    CAUTIOUS_EXPLOITATION = "cautious_exploitation"
    MODERATE_EXPLOITATION = "moderate_exploitation"
    AGGRESSIVE_EXPLOITATION = "aggressive_exploitation"
    INTENSIVE_EXPLOITATION = "intensive_exploitation"
    ADAPTIVE_HYBRID = "adaptive_hybrid"
    COLLABORATIVE_ESCAPE = "collaborative_escape"


class StagnationLevel(Enum):
    """停滞程度枚举"""
    NONE = "none"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class IndividualFeatures:
    """个体特征数据类"""
    individual_id: int
    fitness_value: float
    fitness_rank: int
    fitness_percentile: float
    
    # 停滞状态
    stagnation_duration: int = 0
    stagnation_level: StagnationLevel = StagnationLevel.NONE
    last_improvement_iteration: int = 0
    
    # 多样性贡献
    diversity_contribution: float = 0.0
    distance_to_best: float = float('inf')
    distance_to_centroid: float = 0.0
    
    # 景观特征
    local_ruggedness: float = 0.0
    local_gradient_strength: float = 0.0
    improvement_potential: float = 0.0
    
    # 历史表现
    recent_improvements: List[float] = field(default_factory=list)
    strategy_success_history: Dict[str, float] = field(default_factory=dict)
    preferred_strategy_types: List[str] = field(default_factory=list)


@dataclass
class StrategyAssignment:
    """策略分配结果数据类"""
    individual_id: int
    strategy_type: StrategyType
    confidence: float
    reasoning: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: float = 0.5
    expected_improvement: float = 0.0


@dataclass
class LandscapeContext:
    """景观上下文信息"""
    global_ruggedness: float
    modality: str
    deceptiveness: str
    gradient_strength: float
    population_diversity: float
    convergence_trend: float
    evolution_phase: str
    difficult_regions: List[Dict] = field(default_factory=list)
    opportunity_regions: List[Dict] = field(default_factory=list)


class EnhancedStrategyExpert(ExpertBase):
    """
    增强策略选择专家
    
    实现个体级精准策略选择，充分利用适应度景观分析和个体状态信息，
    通过优化的LLM提示工程实现智能策略推理。
    """
    
    def __init__(self, interface_llm=None, config: Optional[Dict] = None):
        super().__init__()
        self.interface_llm = interface_llm
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 策略选择历史
        self.strategy_history: List[Dict] = []
        self.performance_feedback: Dict[str, List[float]] = {}
        
        # 个体状态缓存
        self.individual_states_cache: Dict[int, IndividualFeatures] = {}

        # 初始化子模块
        self.state_analyzer = IndividualStateAnalyzer(self.config.get('state_analyzer', {}))
        self.response_parser = StrategyResponseParser(self.config.get('response_parser', {}))

        # 配置参数
        self.enable_llm_reasoning = self.config.get('enable_llm_reasoning', True)
        self.fallback_to_algorithmic = self.config.get('fallback_to_algorithmic', True)
        self.max_llm_retries = self.config.get('max_llm_retries', 3)
        
    def analyze(self, landscape_report: Dict, populations: List[Dict], 
                iteration: int, strategy_feedback: Optional[Dict] = None) -> Tuple[List[str], str]:
        """
        主要分析方法：为每个个体分配最优策略
        
        Args:
            landscape_report: 景观分析报告
            populations: 当前种群
            iteration: 当前迭代次数
            strategy_feedback: 上次策略执行反馈
            
        Returns:
            Tuple[策略分配列表, 详细分析报告]
        """
        self.logger.info(f"开始增强策略分配分析 (迭代 {iteration})")
        start_time = time.time()
        
        try:
            # 1. 使用状态分析器分析个体状态
            individual_metrics = self.state_analyzer.analyze_population_states(
                populations, iteration
            )

            # 2. 转换为特征格式（保持兼容性）
            individual_features = self._convert_metrics_to_features(individual_metrics)

            # 3. 构建景观上下文
            landscape_context = self._build_landscape_context(landscape_report)

            # 4. 执行策略选择
            self.logger.info(f"策略选择条件检查: enable_llm_reasoning={self.enable_llm_reasoning}, interface_llm={self.interface_llm is not None}")
            if self.enable_llm_reasoning and self.interface_llm is not None:
                strategy_assignments = self._llm_based_strategy_selection(
                    individual_features, landscape_context, iteration, strategy_feedback
                )
            else:
                self.logger.info("使用算法策略选择")
                strategy_assignments = self._algorithmic_strategy_selection(
                    individual_features, landscape_context, iteration
                )
            
            # 4. 转换为兼容格式
            strategy_list = self._convert_to_legacy_format(strategy_assignments)
            
            # 5. 生成详细报告
            detailed_report = self._generate_detailed_report(
                strategy_assignments, landscape_context, iteration
            )
            
            # 6. 更新历史记录
            self._update_strategy_history(strategy_assignments, iteration)
            
            execution_time = time.time() - start_time
            self.logger.info(f"策略分配完成，耗时: {execution_time:.3f}秒")
            
            return strategy_list, detailed_report
            
        except Exception as e:
            self.logger.error(f"策略分配过程中发生错误: {str(e)}")
            # 回退到简单策略分配
            fallback_strategies = self._fallback_strategy_assignment(len(populations))
            return fallback_strategies, f"错误回退: {str(e)}"
    
    def _extract_individual_features(self, populations: List[Dict], iteration: int) -> List[IndividualFeatures]:
        """提取个体特征信息"""
        features_list = []
        
        # 计算适应度排名和百分位
        fitness_values = [(i, pop.get('cur_cost', float('inf'))) for i, pop in enumerate(populations)]
        fitness_values.sort(key=lambda x: x[1])  # 按适应度排序
        
        # 计算种群中心点（用于距离计算）
        if populations:
            tours = [pop.get('tour', []) for pop in populations if pop.get('tour')]
            centroid = self._calculate_population_centroid(tours)
        else:
            centroid = []
        
        for rank, (original_idx, fitness) in enumerate(fitness_values):
            pop = populations[original_idx]
            
            # 获取或创建个体特征
            individual_id = original_idx
            if individual_id in self.individual_states_cache:
                features = self.individual_states_cache[individual_id]
                # 更新基本信息
                old_fitness = features.fitness_value
                features.fitness_value = fitness
                features.fitness_rank = rank
                features.fitness_percentile = rank / len(populations)
                
                # 更新停滞状态
                if abs(old_fitness - fitness) < 1e-6:  # 没有改进
                    features.stagnation_duration += 1
                else:
                    features.stagnation_duration = 0
                    features.last_improvement_iteration = iteration
                    features.recent_improvements.append(old_fitness - fitness)
                    
            else:
                features = IndividualFeatures(
                    individual_id=individual_id,
                    fitness_value=fitness,
                    fitness_rank=rank,
                    fitness_percentile=rank / len(populations),
                    last_improvement_iteration=iteration
                )
                self.individual_states_cache[individual_id] = features
            
            # 更新停滞等级
            features.stagnation_level = self._determine_stagnation_level(features.stagnation_duration)
            
            # 计算多样性贡献和距离
            if pop.get('tour'):
                features.diversity_contribution = self._calculate_diversity_contribution(
                    pop['tour'], [p.get('tour', []) for p in populations if p.get('tour')]
                )
                features.distance_to_centroid = self._calculate_distance_to_centroid(
                    pop['tour'], centroid
                )
            
            # 计算与最优解的距离
            if rank == 0:
                features.distance_to_best = 0.0
            else:
                best_fitness = fitness_values[0][1]
                features.distance_to_best = abs(fitness - best_fitness)
            
            features_list.append(features)
        
        return features_list
    
    def _build_landscape_context(self, landscape_report: Dict) -> LandscapeContext:
        """构建景观上下文信息"""
        # 安全地提取景观特征
        search_space = landscape_report.get('search_space_features', {})
        population_state = landscape_report.get('population_state', {})
        
        return LandscapeContext(
            global_ruggedness=search_space.get('ruggedness', 0.5),
            modality=search_space.get('modality', 'unknown'),
            deceptiveness=search_space.get('deceptiveness', 'unknown'),
            gradient_strength=search_space.get('gradient_strength', 0.5),
            population_diversity=population_state.get('diversity', 0.5),
            convergence_trend=population_state.get('convergence', 0.5),
            evolution_phase=landscape_report.get('evolution_phase', 'exploration'),
            difficult_regions=landscape_report.get('difficult_regions', []),
            opportunity_regions=landscape_report.get('opportunity_regions', [])
        )

    def _llm_based_strategy_selection(self, individual_features: List[IndividualFeatures],
                                    landscape_context: LandscapeContext, iteration: int,
                                    strategy_feedback: Optional[Dict] = None) -> List[StrategyAssignment]:
        """基于LLM的策略选择"""
        self.logger.info("使用LLM进行策略选择")

        try:
            # 生成优化的提示词
            prompt = self._generate_enhanced_strategy_prompt(
                individual_features, landscape_context, iteration, strategy_feedback
            )

            # 调用LLM
            for attempt in range(self.max_llm_retries):
                try:
                    response = self.interface_llm.get_response(prompt)
                    self.logger.info(f"LLM响应 (尝试 {attempt + 1}): {response}")

                    # 使用新的解析器解析响应
                    parsed_response = self.response_parser.parse_strategy_response(
                        response, len(individual_features)
                    )

                    # 检查解析结果
                    if parsed_response.strategy_assignments and not parsed_response.parsing_errors:
                        self.logger.info(f"成功解析LLM响应，获得 {len(parsed_response.strategy_assignments)} 个策略分配")

                        # 转换为StrategyAssignment格式
                        strategy_assignments = self._convert_parsed_to_assignments(parsed_response.strategy_assignments)
                        return strategy_assignments
                    else:
                        error_msg = f"解析错误: {parsed_response.parsing_errors}"
                        self.logger.warning(error_msg)
                        if attempt == self.max_llm_retries - 1:
                            raise ValueError(error_msg)

                except Exception as e:
                    self.logger.warning(f"LLM调用尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt == self.max_llm_retries - 1:
                        raise e

            # 如果所有尝试都失败，回退到算法方法
            self.logger.warning("LLM策略选择失败，回退到算法方法")
            return self._algorithmic_strategy_selection(individual_features, landscape_context, iteration)

        except Exception as e:
            self.logger.error(f"LLM策略选择过程中发生错误: {str(e)}")
            if self.fallback_to_algorithmic:
                return self._algorithmic_strategy_selection(individual_features, landscape_context, iteration)
            else:
                raise e

    def _algorithmic_strategy_selection(self, individual_features: List[IndividualFeatures],
                                      landscape_context: LandscapeContext,
                                      iteration: int) -> List[StrategyAssignment]:
        """基于算法的策略选择（回退方案）"""
        self.logger.info("使用算法进行策略选择")

        strategy_assignments = []

        for features in individual_features:
            # 基于个体特征和景观上下文选择策略
            strategy_type, confidence, reasoning = self._select_strategy_algorithmically(
                features, landscape_context
            )

            assignment = StrategyAssignment(
                individual_id=features.individual_id,
                strategy_type=strategy_type,
                confidence=confidence,
                reasoning=reasoning,
                parameters=self._generate_strategy_parameters(strategy_type, features),
                priority=self._calculate_priority(features, landscape_context)
            )

            strategy_assignments.append(assignment)

        return strategy_assignments

    def _select_strategy_algorithmically(self, features: IndividualFeatures,
                                       landscape_context: LandscapeContext) -> Tuple[StrategyType, float, str]:
        """算法化策略选择逻辑"""

        # 基于适应度百分位的基础策略倾向
        if features.fitness_percentile <= 0.1:  # 顶级个体 (前10%)
            base_tendency = "exploitation"
        elif features.fitness_percentile <= 0.3:  # 优秀个体 (前30%)
            base_tendency = "balanced"
        else:  # 普通个体 (后70%)
            base_tendency = "exploration"

        # 基于停滞状态调整
        if features.stagnation_level in [StagnationLevel.HIGH, StagnationLevel.CRITICAL]:
            stagnation_adjustment = "strong_exploration"
        elif features.stagnation_level == StagnationLevel.MODERATE:
            stagnation_adjustment = "exploration"
        else:
            stagnation_adjustment = base_tendency

        # 基于景观特征调整
        if landscape_context.global_ruggedness > 0.7:  # 高粗糙度
            landscape_adjustment = "exploration"
        elif landscape_context.population_diversity < 0.3:  # 低多样性
            landscape_adjustment = "exploration"
        elif landscape_context.convergence_trend > 0.7:  # 高收敛
            landscape_adjustment = "exploitation"
        else:
            landscape_adjustment = stagnation_adjustment

        # 综合决策
        final_decision = landscape_adjustment

        # 映射到具体策略类型
        strategy_mapping = {
            "strong_exploration": StrategyType.STRONG_EXPLORATION,
            "exploration": StrategyType.BALANCED_EXPLORATION,
            "balanced": StrategyType.INTELLIGENT_EXPLORATION,
            "exploitation": StrategyType.MODERATE_EXPLOITATION
        }

        strategy_type = strategy_mapping.get(final_decision, StrategyType.BALANCED_EXPLORATION)

        # 计算置信度
        confidence = self._calculate_algorithmic_confidence(features, landscape_context)

        # 生成推理说明
        reasoning = f"基于适应度百分位{features.fitness_percentile:.2f}，停滞等级{features.stagnation_level.value}，" \
                   f"景观粗糙度{landscape_context.global_ruggedness:.2f}选择{strategy_type.value}"

        return strategy_type, confidence, reasoning

    def _calculate_algorithmic_confidence(self, features: IndividualFeatures,
                                        landscape_context: LandscapeContext) -> float:
        """计算算法策略选择的置信度"""
        confidence_factors = []

        # 基于适应度排名的置信度
        if features.fitness_percentile <= 0.1:
            confidence_factors.append(0.9)  # 顶级个体，高置信度
        elif features.fitness_percentile <= 0.5:
            confidence_factors.append(0.7)  # 中等个体，中等置信度
        else:
            confidence_factors.append(0.5)  # 普通个体，低置信度

        # 基于停滞状态的置信度
        stagnation_confidence = {
            StagnationLevel.NONE: 0.8,
            StagnationLevel.LOW: 0.7,
            StagnationLevel.MODERATE: 0.6,
            StagnationLevel.HIGH: 0.9,  # 高停滞时策略选择更明确
            StagnationLevel.CRITICAL: 0.95
        }
        confidence_factors.append(stagnation_confidence[features.stagnation_level])

        # 基于景观特征的置信度
        if landscape_context.global_ruggedness > 0.8 or landscape_context.global_ruggedness < 0.2:
            confidence_factors.append(0.8)  # 极端景观特征，策略选择更明确
        else:
            confidence_factors.append(0.6)  # 中等景观特征，策略选择不太明确

        return np.mean(confidence_factors)

    def _generate_strategy_parameters(self, strategy_type: StrategyType,
                                    features: IndividualFeatures) -> Dict[str, Any]:
        """为策略生成参数配置"""
        base_params = {
            "individual_id": features.individual_id,
            "fitness_percentile": features.fitness_percentile,
            "stagnation_duration": features.stagnation_duration
        }

        # 根据策略类型添加特定参数
        if strategy_type in [StrategyType.STRONG_EXPLORATION, StrategyType.BALANCED_EXPLORATION]:
            base_params.update({
                "exploration_radius": 0.3 if strategy_type == StrategyType.STRONG_EXPLORATION else 0.2,
                "diversification_strength": 0.8 if strategy_type == StrategyType.STRONG_EXPLORATION else 0.6,
                "random_component": 0.4 if strategy_type == StrategyType.STRONG_EXPLORATION else 0.3
            })
        elif strategy_type in [StrategyType.MODERATE_EXPLOITATION, StrategyType.AGGRESSIVE_EXPLOITATION]:
            base_params.update({
                "local_search_depth": 3 if strategy_type == StrategyType.AGGRESSIVE_EXPLOITATION else 2,
                "elite_influence": 0.8 if strategy_type == StrategyType.AGGRESSIVE_EXPLOITATION else 0.6,
                "perturbation_strength": 0.2 if strategy_type == StrategyType.AGGRESSIVE_EXPLOITATION else 0.3
            })

        return base_params

    def _convert_metrics_to_features(self, metrics_list: List[IndividualStateMetrics]) -> List[IndividualFeatures]:
        """将状态分析器的指标转换为特征格式"""
        features_list = []

        for metrics in metrics_list:
            features = IndividualFeatures(
                individual_id=metrics.individual_id,
                fitness_value=metrics.fitness_value,
                fitness_rank=metrics.fitness_rank,
                fitness_percentile=metrics.fitness_percentile,
                stagnation_duration=metrics.stagnation_duration,
                stagnation_level=metrics.stagnation_level,
                diversity_contribution=metrics.diversity_contribution,
                distance_to_best=metrics.distance_to_best,
                local_ruggedness=metrics.local_ruggedness,
                improvement_potential=metrics.improvement_potential,
                recent_improvements=metrics.recent_improvements,
                preferred_strategy_types=metrics.preferred_strategies
            )
            features_list.append(features)

        return features_list

    def _convert_to_legacy_format(self, strategy_assignments: List[StrategyAssignment]) -> List[str]:
        """转换策略分配为旧版兼容格式"""
        strategy_list = []

        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value

            # 将详细策略类型转换为简化的explore/exploit格式
            if 'exploration' in strategy_type or strategy_type in ['adaptive_hybrid', 'collaborative_escape']:
                legacy_strategy = 'explore'
            elif 'exploitation' in strategy_type:
                legacy_strategy = 'exploit'
            else:
                legacy_strategy = 'explore'  # 默认为探索

            strategy_list.append(legacy_strategy)

        return strategy_list

    def _convert_parsed_to_assignments(self, parsed_assignments) -> List[StrategyAssignment]:
        """将解析后的策略分配转换为StrategyAssignment格式"""
        assignments = []

        for parsed in parsed_assignments:
            assignment = StrategyAssignment(
                individual_id=parsed.individual_id,
                strategy_type=parsed.strategy_type,
                confidence=parsed.confidence,
                reasoning=parsed.reasoning,
                priority=parsed.priority,
                expected_improvement=parsed.expected_improvement,
                parameters=parsed.parameters
            )
            assignments.append(assignment)

        return assignments

    def _calculate_priority(self, features: IndividualFeatures,
                          landscape_context: LandscapeContext) -> float:
        """计算策略执行优先级"""
        priority_factors = []

        # 基于适应度的优先级
        priority_factors.append(1.0 - features.fitness_percentile)  # 适应度越好，优先级越高

        # 基于停滞状态的优先级
        stagnation_priority = {
            StagnationLevel.NONE: 0.3,
            StagnationLevel.LOW: 0.4,
            StagnationLevel.MODERATE: 0.6,
            StagnationLevel.HIGH: 0.8,
            StagnationLevel.CRITICAL: 1.0
        }
        priority_factors.append(stagnation_priority[features.stagnation_level])

        # 基于多样性贡献的优先级
        priority_factors.append(features.diversity_contribution)

        return np.mean(priority_factors)

    def _generate_enhanced_strategy_prompt(self, individual_features: List[IndividualFeatures],
                                         landscape_context: LandscapeContext, iteration: int,
                                         strategy_feedback: Optional[Dict] = None) -> str:
        """生成增强的策略选择提示词"""

        # 转换个体特征为字典格式
        individual_features_dict = []
        for features in individual_features:
            feature_dict = {
                'individual_id': features.individual_id,
                'fitness_value': features.fitness_value,
                'fitness_rank': features.fitness_rank,
                'fitness_percentile': features.fitness_percentile,
                'stagnation_duration': features.stagnation_duration,
                'stagnation_level': features.stagnation_level.value,
                'diversity_contribution': features.diversity_contribution,
                'distance_to_best': features.distance_to_best,
                'recent_improvements': len(features.recent_improvements),
                'preferred_strategies': features.preferred_strategy_types
            }
            individual_features_dict.append(feature_dict)

        # 转换景观上下文为字典格式
        landscape_context_dict = {
            'global_ruggedness': landscape_context.global_ruggedness,
            'modality': landscape_context.modality,
            'deceptiveness': landscape_context.deceptiveness,
            'gradient_strength': landscape_context.gradient_strength,
            'population_diversity': landscape_context.population_diversity,
            'convergence_trend': landscape_context.convergence_trend,
            'evolution_phase': landscape_context.evolution_phase,
            'difficult_regions': landscape_context.difficult_regions,
            'opportunity_regions': landscape_context.opportunity_regions
        }

        # 构建反馈摘要
        feedback_summary = "无历史反馈"
        if strategy_feedback:
            feedback_summary = f"上次策略反馈: {strategy_feedback.get('summary', '无摘要')}"

        # 使用新的提示生成函数
        return generate_individual_strategy_prompt(
            individual_features_dict,
            landscape_context_dict,
            iteration,
            feedback_summary
        )



    def _generate_detailed_report(self, strategy_assignments: List[StrategyAssignment],
                                landscape_context: LandscapeContext, iteration: int) -> str:
        """生成详细的策略分配报告"""

        # 统计策略分布
        strategy_counts = {}
        total_confidence = 0
        total_priority = 0

        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
            total_confidence += assignment.confidence
            total_priority += assignment.priority

        avg_confidence = total_confidence / len(strategy_assignments) if strategy_assignments else 0
        avg_priority = total_priority / len(strategy_assignments) if strategy_assignments else 0

        # 计算探索/开发比例
        exploration_strategies = ['strong_exploration', 'balanced_exploration', 'intelligent_exploration']
        exploitation_strategies = ['cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation', 'intensive_exploitation']

        exploration_count = sum(strategy_counts.get(s, 0) for s in exploration_strategies)
        exploitation_count = sum(strategy_counts.get(s, 0) for s in exploitation_strategies)
        total_count = len(strategy_assignments)

        exploration_ratio = exploration_count / total_count if total_count > 0 else 0
        exploitation_ratio = exploitation_count / total_count if total_count > 0 else 0

        # 生成报告
        report = f"""
=== 增强策略分配报告 (迭代 {iteration}) ===

【总体统计】
- 种群规模: {len(strategy_assignments)}
- 平均置信度: {avg_confidence:.3f}
- 平均优先级: {avg_priority:.3f}
- 探索策略比例: {exploration_ratio:.2f} ({exploration_count}/{total_count})
- 开发策略比例: {exploitation_ratio:.2f} ({exploitation_count}/{total_count})

【策略分布】"""

        for strategy_type, count in sorted(strategy_counts.items()):
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            report += f"\n- {strategy_type}: {count} 个体 ({percentage:.1f}%)"

        report += f"""

【景观特征影响】
- 全局粗糙度: {landscape_context.global_ruggedness:.3f}
- 种群多样性: {landscape_context.population_diversity:.3f}
- 收敛趋势: {landscape_context.convergence_trend:.3f}
- 进化阶段: {landscape_context.evolution_phase}

【个体策略详情】"""

        for assignment in strategy_assignments[:10]:  # 只显示前10个个体的详情
            report += f"""
个体 {assignment.individual_id}: {assignment.strategy_type.value}
  - 置信度: {assignment.confidence:.3f}
  - 优先级: {assignment.priority:.3f}
  - 推理: {assignment.reasoning[:100]}{'...' if len(assignment.reasoning) > 100 else ''}"""

        if len(strategy_assignments) > 10:
            report += f"\n... (还有 {len(strategy_assignments) - 10} 个个体)"

        return report

    def _update_strategy_history(self, strategy_assignments: List[StrategyAssignment], iteration: int):
        """更新策略选择历史"""
        history_entry = {
            'iteration': iteration,
            'timestamp': time.time(),
            'assignments': [
                {
                    'individual_id': assignment.individual_id,
                    'strategy_type': assignment.strategy_type.value,
                    'confidence': assignment.confidence,
                    'priority': assignment.priority
                }
                for assignment in strategy_assignments
            ],
            'strategy_distribution': self._calculate_strategy_distribution(strategy_assignments)
        }

        self.strategy_history.append(history_entry)

        # 保持历史记录在合理范围内
        if len(self.strategy_history) > 100:
            self.strategy_history = self.strategy_history[-100:]

    def _calculate_strategy_distribution(self, strategy_assignments: List[StrategyAssignment]) -> Dict[str, float]:
        """计算策略分布"""
        if not strategy_assignments:
            return {}

        strategy_counts = {}
        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

        total = len(strategy_assignments)
        return {strategy: count / total for strategy, count in strategy_counts.items()}

    def _fallback_strategy_assignment(self, population_size: int) -> List[str]:
        """回退策略分配"""
        self.logger.warning("使用回退策略分配")

        # 简单的交替分配策略
        strategies = []
        for i in range(population_size):
            if i % 3 == 0:
                strategies.append("explore")
            elif i % 3 == 1:
                strategies.append("exploit")
            else:
                strategies.append("explore")

        return strategies

    # 辅助方法
    def _determine_stagnation_level(self, stagnation_duration: int) -> StagnationLevel:
        """确定停滞等级"""
        if stagnation_duration == 0:
            return StagnationLevel.NONE
        elif stagnation_duration <= 2:
            return StagnationLevel.LOW
        elif stagnation_duration <= 5:
            return StagnationLevel.MODERATE
        elif stagnation_duration <= 10:
            return StagnationLevel.HIGH
        else:
            return StagnationLevel.CRITICAL

    def _calculate_population_centroid(self, tours: List[List[int]]) -> List[int]:
        """计算种群中心点"""
        if not tours:
            return []

        # 简化实现：返回第一个tour作为中心点
        # 在实际应用中，可以使用更复杂的中心点计算方法
        return tours[0] if tours else []

    def _calculate_diversity_contribution(self, individual_tour: List[int], all_tours: List[List[int]]) -> float:
        """计算个体的多样性贡献"""
        if len(all_tours) <= 1:
            return 0.0

        try:
            total_distance = 0.0
            count = 0

            for other_tour in all_tours:
                if not self._tours_equal(other_tour, individual_tour):
                    # 计算汉明距离 - 安全地处理numpy数组
                    distance = self._safe_hamming_distance(individual_tour, other_tour)
                    total_distance += distance
                    count += 1

            if count == 0:
                return 0.0

            avg_distance = total_distance / count
            max_possible_distance = len(individual_tour) if individual_tour else 1

            return min(1.0, avg_distance / max_possible_distance)

        except Exception as e:
            self.logger.warning(f"计算多样性贡献时出错: {e}")
            return 0.5  # 默认中等多样性贡献

    def _calculate_distance_to_centroid(self, individual_tour: List[int], centroid: List[int]) -> float:
        """计算个体到种群中心的距离"""
        if not individual_tour or not centroid or len(individual_tour) != len(centroid):
            return 0.0

        try:
            distance = self._safe_hamming_distance(individual_tour, centroid)
            return distance / len(individual_tour)
        except Exception as e:
            self.logger.warning(f"计算到中心点距离时出错: {e}")
            return 0.0

    def _generate_detailed_report(self, strategy_assignments: List[StrategyAssignment],
                                landscape_context: LandscapeContext, iteration: int) -> str:
        """生成详细的策略分配报告"""

        # 统计策略分布
        strategy_counts = {}
        total_confidence = 0
        total_priority = 0

        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
            total_confidence += assignment.confidence
            total_priority += assignment.priority

        avg_confidence = total_confidence / len(strategy_assignments) if strategy_assignments else 0
        avg_priority = total_priority / len(strategy_assignments) if strategy_assignments else 0

        # 计算探索/开发比例
        exploration_strategies = ['strong_exploration', 'balanced_exploration', 'intelligent_exploration']
        exploitation_strategies = ['cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation', 'intensive_exploitation']

        exploration_count = sum(strategy_counts.get(s, 0) for s in exploration_strategies)
        exploitation_count = sum(strategy_counts.get(s, 0) for s in exploitation_strategies)
        total_count = len(strategy_assignments)

        exploration_ratio = exploration_count / total_count if total_count > 0 else 0
        exploitation_ratio = exploitation_count / total_count if total_count > 0 else 0

        # 生成报告
        report = f"""
=== 增强策略分配报告 (迭代 {iteration}) ===

【总体统计】
- 种群规模: {len(strategy_assignments)}
- 平均置信度: {avg_confidence:.3f}
- 平均优先级: {avg_priority:.3f}
- 探索策略比例: {exploration_ratio:.2f} ({exploration_count}/{total_count})
- 开发策略比例: {exploitation_ratio:.2f} ({exploitation_count}/{total_count})

【策略分布】"""

        for strategy_type, count in sorted(strategy_counts.items()):
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            report += f"\n- {strategy_type}: {count} 个体 ({percentage:.1f}%)"

        report += f"""

【景观特征影响】
- 全局粗糙度: {landscape_context.global_ruggedness:.3f}
- 种群多样性: {landscape_context.population_diversity:.3f}
- 收敛趋势: {landscape_context.convergence_trend:.3f}
- 进化阶段: {landscape_context.evolution_phase}

【个体策略详情】"""

        for assignment in strategy_assignments[:10]:  # 只显示前10个个体的详情
            report += f"""
个体 {assignment.individual_id}: {assignment.strategy_type.value}
  - 置信度: {assignment.confidence:.3f}
  - 优先级: {assignment.priority:.3f}
  - 推理: {assignment.reasoning[:100]}{'...' if len(assignment.reasoning) > 100 else ''}"""

        if len(strategy_assignments) > 10:
            report += f"\n... (还有 {len(strategy_assignments) - 10} 个个体)"

        return report

    def _update_strategy_history(self, strategy_assignments: List[StrategyAssignment], iteration: int):
        """更新策略选择历史"""
        history_entry = {
            'iteration': iteration,
            'timestamp': time.time(),
            'assignments': [
                {
                    'individual_id': assignment.individual_id,
                    'strategy_type': assignment.strategy_type.value,
                    'confidence': assignment.confidence,
                    'priority': assignment.priority
                }
                for assignment in strategy_assignments
            ],
            'strategy_distribution': self._calculate_strategy_distribution(strategy_assignments)
        }

        self.strategy_history.append(history_entry)

        # 保持历史记录在合理范围内
        if len(self.strategy_history) > 100:
            self.strategy_history = self.strategy_history[-100:]

    def _calculate_strategy_distribution(self, strategy_assignments: List[StrategyAssignment]) -> Dict[str, float]:
        """计算策略分布"""
        if not strategy_assignments:
            return {}

        strategy_counts = {}
        for assignment in strategy_assignments:
            strategy_type = assignment.strategy_type.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

        total = len(strategy_assignments)
        return {strategy: count / total for strategy, count in strategy_counts.items()}

    def _fallback_strategy_assignment(self, population_size: int) -> List[str]:
        """回退策略分配"""
        self.logger.warning("使用回退策略分配")

        # 简单的交替分配策略
        strategies = []
        for i in range(population_size):
            if i % 3 == 0:
                strategies.append("explore")
            elif i % 3 == 1:
                strategies.append("exploit")
            else:
                strategies.append("explore")

        return strategies



    def _tours_equal(self, tour1: List[int], tour2: List[int]) -> bool:
        """安全地比较两个路径是否相等"""
        if len(tour1) != len(tour2):
            return False

        # 转换为numpy数组进行比较
        try:
            import numpy as np
            arr1 = np.array(tour1) if not isinstance(tour1, np.ndarray) else tour1
            arr2 = np.array(tour2) if not isinstance(tour2, np.ndarray) else tour2
            return np.array_equal(arr1, arr2)
        except Exception:
            # 如果numpy比较失败，使用安全的逐元素比较
            return all(self._safe_element_equal(a, b) for a, b in zip(tour1, tour2))

    def _safe_element_equal(self, a, b) -> bool:
        """安全地比较两个元素是否相等"""
        try:
            # 处理numpy数组元素的比较
            if hasattr(a, 'item'):
                a = a.item()
            if hasattr(b, 'item'):
                b = b.item()
            return a == b
        except Exception:
            return False

    def _safe_hamming_distance(self, tour1: List[int], tour2: List[int]) -> int:
        """安全地计算两个路径的汉明距离"""
        if len(tour1) != len(tour2):
            return len(tour1)  # 如果长度不同，返回最大可能距离

        try:
            import numpy as np
            # 确保都是numpy数组
            arr1 = np.array(tour1) if not isinstance(tour1, np.ndarray) else tour1
            arr2 = np.array(tour2) if not isinstance(tour2, np.ndarray) else tour2
            # 使用numpy的不等比较，然后求和
            return int(np.sum(arr1 != arr2))
        except Exception:
            # 如果numpy操作失败，使用安全的逐元素比较
            return sum(1 for a, b in zip(tour1, tour2) if not self._safe_element_equal(a, b))
