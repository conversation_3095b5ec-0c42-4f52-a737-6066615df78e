2025-07-31 17:32:46,478 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-07-31 17:32:46,478 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-31 17:32:46,478 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:32:46,479 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=747.0, 多样性=0.767
2025-07-31 17:32:46,479 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:32:46,480 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.144
2025-07-31 17:32:46,480 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:32:46,482 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-07-31 17:32:46,482 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753954366.4820776, 'status': 'default_fallback'}}
2025-07-31 17:32:46,482 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:32:46,482 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:32:46,482 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 747.0
  • mean_cost: 988.4
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:32:46,483 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:32:46,483 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:32:47,941 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Exploration phase with moderate diversity. Encourage broader search initially, then exploit the best."
}
```
2025-07-31 17:32:47,942 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:32:47,942 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:32:47,942 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:32:47,942 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Exploration phase with moderate diversity. Encourage broader search initially, then exploit the best."
}
```
2025-07-31 17:32:47,942 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:32:47,942 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:32:47,942 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Exploration phase with moderate diversity. Encourage broader search initially, then exploit the best."
}
```
2025-07-31 17:32:47,942 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:32:47,942 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:32:47,942 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:32:47,942 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:32:47,943 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:32:47,943 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:32:48,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 918.0, 路径长度: 9
2025-07-31 17:32:48,059 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 0, 6, 7, 3, 8, 5, 2, 1], 'cur_cost': 918.0}
2025-07-31 17:32:48,059 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:32:48,059 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:32:48,059 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:32:48,059 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:32:48,059 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:32:48,059 - ExplorationExpert - INFO - 探索路径生成完成，成本: 792.0, 路径长度: 9
2025-07-31 17:32:48,060 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 7, 8, 3, 6, 5, 0, 1, 2], 'cur_cost': 792.0}
2025-07-31 17:32:48,060 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:32:48,060 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:32:48,060 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:32:48,061 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:32:48,061 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:32:48,061 - ExplorationExpert - INFO - 探索路径生成完成，成本: 831.0, 路径长度: 9
2025-07-31 17:32:48,061 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}
2025-07-31 17:32:48,061 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:32:48,061 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:32:48,063 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:32:48,064 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 815.0
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - res_population_num: 1
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - populations: [{'tour': [4, 0, 6, 7, 3, 8, 5, 2, 1], 'cur_cost': 918.0}, {'tour': [4, 7, 8, 3, 6, 5, 0, 1, 2], 'cur_cost': 792.0}, {'tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}, {'tour': array([6, 5, 3, 2, 4, 7, 8, 0, 1], dtype=int64), 'cur_cost': 815.0}, {'tour': array([4, 7, 6, 0, 5, 1, 3, 2, 8], dtype=int64), 'cur_cost': 1075.0}]
2025-07-31 17:32:49,337 - ExploitationExpert - INFO - 局部搜索耗时: 1.27秒
2025-07-31 17:32:49,339 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-31 17:32:49,339 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 5, 3, 2, 4, 7, 8, 0, 1], dtype=int64), 'cur_cost': 815.0}
2025-07-31 17:32:49,339 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:32:49,339 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:32:49,339 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:32:49,339 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1102.0
2025-07-31 17:32:51,074 - ExploitationExpert - INFO - res_population_num: 1
2025-07-31 17:32:51,074 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-07-31 17:32:51,075 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-07-31 17:32:51,075 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:32:51,075 - ExploitationExpert - INFO - populations: [{'tour': [4, 0, 6, 7, 3, 8, 5, 2, 1], 'cur_cost': 918.0}, {'tour': [4, 7, 8, 3, 6, 5, 0, 1, 2], 'cur_cost': 792.0}, {'tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}, {'tour': array([6, 5, 3, 2, 4, 7, 8, 0, 1], dtype=int64), 'cur_cost': 815.0}, {'tour': array([6, 4, 0, 3, 2, 7, 5, 8, 1], dtype=int64), 'cur_cost': 1102.0}]
2025-07-31 17:32:51,076 - ExploitationExpert - INFO - 局部搜索耗时: 1.74秒
2025-07-31 17:32:51,076 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-31 17:32:51,076 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([6, 4, 0, 3, 2, 7, 5, 8, 1], dtype=int64), 'cur_cost': 1102.0}
2025-07-31 17:32:51,077 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 6, 7, 3, 8, 5, 2, 1], 'cur_cost': 918.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 8, 3, 6, 5, 0, 1, 2], 'cur_cost': 792.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 5, 3, 2, 4, 7, 8, 0, 1], dtype=int64), 'cur_cost': 815.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 4, 0, 3, 2, 7, 5, 8, 1], dtype=int64), 'cur_cost': 1102.0}}]
2025-07-31 17:32:51,077 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:32:51,077 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:32:51,077 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=792.0, 多样性=0.744
2025-07-31 17:32:51,077 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-31 17:32:51,078 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-07-31 17:32:51,078 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:32:51,078 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.004192938450270815, 'best_improvement': -0.060240963855421686}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02898550724637671}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-31 17:32:51,078 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-31 17:32:51,078 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-07-31 17:32:51,078 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-31 17:32:51,078 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:32:51,078 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=792.0, 多样性=0.744
2025-07-31 17:32:51,079 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:32:51,079 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.156
2025-07-31 17:32:51,079 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:32:51,079 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-07-31 17:32:51,081 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-07-31 17:32:51,081 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753954371.0819387, 'status': 'default_fallback'}}
2025-07-31 17:32:51,081 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:32:51,081 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:32:51,081 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 792.0
  • mean_cost: 891.6
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploitation
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 80, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'slight_improvement', 'impro...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:32:51,081 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:32:51,081 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:32:52,394 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore"
  },
  "rationale": "Exploitation is preferred given the exploitation phase and slight improvement on cost. Three individuals are assigned to exploitation."
}
```
2025-07-31 17:32:52,394 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:32:52,394 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-31 17:32:52,394 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-31 17:32:52,394 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore"
  },
  "rationale": "Exploitation is preferred given the exploitation phase and slight improvement on cost. Three individuals are assigned to exploitation."
}
```
2025-07-31 17:32:52,395 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:32:52,395 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-31 17:32:52,395 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore"
  },
  "rationale": "Exploitation is preferred given the exploitation phase and slight improvement on cost. Three individuals are assigned to exploitation."
}
```
2025-07-31 17:32:52,395 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:32:52,395 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-07-31 17:32:52,395 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:32:52,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:32:52,396 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1022.0
2025-07-31 17:32:52,431 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:32:52,432 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:32:52,432 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-07-31 17:32:52,433 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:32:52,433 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 1, 5, 6, 7, 4, 8, 2], dtype=int64), 'cur_cost': 1022.0}, {'tour': [4, 7, 8, 3, 6, 5, 0, 1, 2], 'cur_cost': 792.0}, {'tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}, {'tour': [6, 5, 3, 2, 4, 7, 8, 0, 1], 'cur_cost': 815.0}, {'tour': [6, 4, 0, 3, 2, 7, 5, 8, 1], 'cur_cost': 1102.0}]
2025-07-31 17:32:52,433 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒
2025-07-31 17:32:52,434 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-31 17:32:52,434 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([0, 3, 1, 5, 6, 7, 4, 8, 2], dtype=int64), 'cur_cost': 1022.0}
2025-07-31 17:32:52,434 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-31 17:32:52,434 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:32:52,434 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:32:52,435 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 933.0
2025-07-31 17:32:52,472 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:32:52,472 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:32:52,473 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-07-31 17:32:52,473 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:32:52,474 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 1, 5, 6, 7, 4, 8, 2], dtype=int64), 'cur_cost': 1022.0}, {'tour': array([6, 3, 4, 1, 0, 8, 2, 5, 7], dtype=int64), 'cur_cost': 933.0}, {'tour': [6, 3, 7, 8, 5, 4, 2, 0, 1], 'cur_cost': 831.0}, {'tour': [6, 5, 3, 2, 4, 7, 8, 0, 1], 'cur_cost': 815.0}, {'tour': [6, 4, 0, 3, 2, 7, 5, 8, 1], 'cur_cost': 1102.0}]
2025-07-31 17:32:52,474 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒
2025-07-31 17:32:52,475 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-31 17:32:52,475 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([6, 3, 4, 1, 0, 8, 2, 5, 7], dtype=int64), 'cur_cost': 933.0}
2025-07-31 17:32:52,475 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-07-31 17:32:52,475 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:32:52,475 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:32:52,475 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 974.0
2025-07-31 17:32:52,524 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:32:52,524 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680]
2025-07-31 17:32:52,524 - ExploitationExpert - INFO - res_populations: [array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-07-31 17:32:52,526 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:32:52,526 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 1, 5, 6, 7, 4, 8, 2], dtype=int64), 'cur_cost': 1022.0}, {'tour': array([6, 3, 4, 1, 0, 8, 2, 5, 7], dtype=int64), 'cur_cost': 933.0}, {'tour': array([6, 7, 8, 4, 5, 3, 0, 2, 1], dtype=int64), 'cur_cost': 974.0}, {'tour': [6, 5, 3, 2, 4, 7, 8, 0, 1], 'cur_cost': 815.0}, {'tour': [6, 4, 0, 3, 2, 7, 5, 8, 1], 'cur_cost': 1102.0}]
2025-07-31 17:32:52,527 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-07-31 17:32:52,527 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-31 17:32:52,528 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 7, 8, 4, 5, 3, 0, 2, 1], dtype=int64), 'cur_cost': 974.0}
2025-07-31 17:32:52,528 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-07-31 17:32:52,528 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-07-31 17:32:52,528 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:32:52,529 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-07-31 17:32:52,529 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:32:52,529 - ExplorationExpert - INFO - 探索路径生成完成，成本: 949.0, 路径长度: 9
2025-07-31 17:32:52,529 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 1, 4, 0, 7, 5, 6, 2, 8], 'cur_cost': 949.0}
2025-07-31 17:32:52,529 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-07-31 17:32:52,529 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-31 17:32:52,530 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:32:52,530 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-07-31 17:32:52,530 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:32:52,530 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1027.0, 路径长度: 9
2025-07-31 17:32:52,531 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 7, 2, 6, 0, 4, 8, 3, 5], 'cur_cost': 1027.0}
2025-07-31 17:32:52,531 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 1, 5, 6, 7, 4, 8, 2], dtype=int64), 'cur_cost': 1022.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 4, 1, 0, 8, 2, 5, 7], dtype=int64), 'cur_cost': 933.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 8, 4, 5, 3, 0, 2, 1], dtype=int64), 'cur_cost': 974.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 1, 4, 0, 7, 5, 6, 2, 8], 'cur_cost': 949.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 2, 6, 0, 4, 8, 3, 5], 'cur_cost': 1027.0}}]
2025-07-31 17:32:52,531 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:32:52,532 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:32:52,532 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=933.0, 多样性=0.778
2025-07-31 17:32:52,532 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-31 17:32:52,532 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-07-31 17:32:52,533 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:32:52,533 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07008046860309845, 'best_improvement': -0.17803030303030304}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.044776119402985065}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:32:52,533 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-31 17:32:52,538 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-07-31 17:32:52,538 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250731_173252.solution
2025-07-31 17:32:52,538 - __main__ - INFO - 实例 simple1_9 处理完成
