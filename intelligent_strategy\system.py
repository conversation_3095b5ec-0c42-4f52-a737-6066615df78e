"""
Main system interface for the Intelligent Strategy Selection System.

This module provides the primary interface for integrating the fitness
landscape-driven strategy selection system with the EoH-TSP-Solver framework.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

from .core import IndividualState, LandscapeAnalyzer, StrategySelectionInterface
from .core.data_structures import StrategyAssignment, ExecutionResult, ExecutionStatus, StrategyType
from .strategies import StrategyFactory, get_strategy_factory
from .llm_interface import LLMStrategyInterface
from .monitoring import PerformanceMonitor, StructuredLogger
from .config import SystemConfig


@dataclass
class StrategyAssignment:
    """Strategy assignment for an individual."""
    individual_id: int
    strategy_type: str
    strategy_instance: Any
    parameters: Dict
    confidence: float
    reasoning: str


@dataclass
class ExecutionResult:
    """Result of strategy execution."""
    individual_id: int
    strategy_type: str
    execution_time: float
    success: bool
    improvement: float
    new_fitness: float
    metadata: Dict


class IntelligentStrategySystem:
    """
    Main interface for the Intelligent Strategy Selection System.
    
    This class orchestrates the entire strategy selection and execution process,
    integrating landscape analysis, LLM-based decision making, and performance monitoring.
    """
    
    def __init__(self, config: SystemConfig):
        """Initialize the intelligent strategy system."""
        self.config = config
        self.logger = StructuredLogger('intelligent_strategy')
        
        # Core components
        self.landscape_analyzer = LandscapeAnalyzer(config.landscape_config)
        self.llm_interface = LLMStrategyInterface(config.llm_config)
        self.performance_monitor = PerformanceMonitor()
        
        # Strategy registries
        self.exploration_strategies = {}
        self.exploitation_strategies = {}
        
        # State tracking
        self.individual_states = {}
        self.execution_history = []
        
        self._initialize_strategies()
        
    def _initialize_strategies(self):
        """Initialize strategy instances."""
        # This will be implemented in Phase 2
        pass
    
    def select_strategies(self,
                         population: List[Any],
                         iteration: int,
                         landscape_report: Optional[Dict] = None,
                         distance_matrix: Optional[Any] = None) -> Dict[int, StrategyAssignment]:
        """
        Select strategies for all individuals in the population.

        Args:
            population: List of individuals in the current population
            iteration: Current iteration number
            landscape_report: Optional landscape analysis report
            distance_matrix: Optional distance matrix for TSP

        Returns:
            Dictionary mapping individual IDs to strategy assignments
        """
        from .integration import get_eoh_integrator

        # Get the EoH integrator
        integrator = get_eoh_integrator(self.config)

        # Use the adapter for strategy selection
        if hasattr(integrator.adapter, 'llm_interface'):
            # Convert population to internal format
            eoh_individuals = integrator.adapter._convert_population_to_internal(population)

            # Update individual states
            if distance_matrix is not None:
                integrator.adapter._update_individual_states(eoh_individuals, distance_matrix, iteration)

            # Analyze landscape
            landscape_features = integrator.adapter._analyze_landscape(
                eoh_individuals, distance_matrix, landscape_report or {}
            )

            # Select strategies using LLM
            individual_states = [integrator.adapter.individual_states.get(ind.individual_id)
                               for ind in eoh_individuals
                               if ind.individual_id in integrator.adapter.individual_states]

            population_context = {
                'population_size': len(population),
                'current_iteration': iteration,
                'best_fitness': min(ind.cur_cost for ind in eoh_individuals) if eoh_individuals else float('inf'),
                'worst_fitness': max(ind.cur_cost for ind in eoh_individuals) if eoh_individuals else 0.0
            }

            return integrator.adapter.llm_interface.select_strategies(
                landscape_analysis=landscape_features,
                individual_states=individual_states,
                population_context=population_context
            )

        # Fallback to simple strategy assignment
        return {i: StrategyAssignment(
            individual_id=i,
            strategy_type=StrategyType.BALANCED_EXPLORATION,
            confidence=0.5,
            reasoning="Fallback strategy selection"
        ) for i in range(len(population))}
    
    def execute_strategies(self,
                          strategy_assignments: Dict[int, StrategyAssignment],
                          population: List[Any],
                          distance_matrix: Optional[Any] = None) -> List[ExecutionResult]:
        """
        Execute assigned strategies for the population.

        Args:
            strategy_assignments: Strategy assignments from select_strategies
            population: Current population
            distance_matrix: Optional distance matrix for TSP

        Returns:
            List of execution results
        """
        from .integration import get_eoh_integrator

        # Get the EoH integrator
        integrator = get_eoh_integrator(self.config)

        # Use the adapter for strategy execution
        if hasattr(integrator.adapter, 'execution_engine') and distance_matrix is not None:
            # Convert population to internal format
            eoh_individuals = integrator.adapter._convert_population_to_internal(population)

            # Execute strategies
            return integrator.adapter._execute_strategies(
                strategy_assignments, eoh_individuals, distance_matrix
            )

        # Fallback to mock execution results
        return [ExecutionResult(
            individual_id=assignment.individual_id,
            strategy_type=assignment.strategy_type,
            status=ExecutionStatus.COMPLETED,
            execution_time=0.1,
            success=True,
            old_fitness=population[assignment.individual_id].get('cur_cost', 0.0) if assignment.individual_id < len(population) else 0.0,
            new_fitness=population[assignment.individual_id].get('cur_cost', 0.0) if assignment.individual_id < len(population) else 0.0
        ) for assignment in strategy_assignments.values()]
    
    def collect_feedback(self, 
                        execution_results: List[ExecutionResult]) -> Dict:
        """
        Collect and process execution feedback.
        
        Args:
            execution_results: Results from strategy execution
            
        Returns:
            Processed feedback report
        """
        # This will be implemented in Phase 1
        pass
    
    def get_performance_metrics(self) -> Dict:
        """Get current performance metrics."""
        return self.performance_monitor.collect_metrics()
    
    def shutdown(self):
        """Gracefully shutdown the system."""
        self.logger.logger.info("Shutting down Intelligent Strategy System")
        # Cleanup resources
        pass
