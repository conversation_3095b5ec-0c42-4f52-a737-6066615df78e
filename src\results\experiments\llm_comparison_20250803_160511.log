2025-08-03 16:05:11,375 - INFO - 开始LLM策略选择对比实验
2025-08-03 16:05:11,376 - INFO - 准备运行实验: 基线算法化策略选择
2025-08-03 16:05:11,376 - INFO - 开始实验: baseline_algorithmic
2025-08-03 16:05:11,376 - INFO - LLM启用: False, 模拟模式: <PERSON>als<PERSON>, 迭代次数: 5
2025-08-03 16:05:11,377 - INFO - 执行命令: D:\Anaconda3\python.exe src/main.py --func_begin 24 --func_end 24 --iter_num 5 --pop_size 20
2025-08-03 16:10:11,542 - ERROR - 实验 baseline_algorithmic 超时
2025-08-03 16:10:13,543 - INFO - 准备运行实验: LLM策略选择（智能模拟）
2025-08-03 16:10:13,592 - INFO - 开始实验: llm_mock_intelligent
2025-08-03 16:10:13,593 - INFO - <PERSON>M启用: True, 模拟模式: True, 迭代次数: 5
2025-08-03 16:10:13,594 - INFO - 执行命令: D:\Anaconda3\python.exe src/main.py --func_begin 24 --func_end 24 --iter_num 5 --pop_size 20
2025-08-03 16:10:32,333 - INFO - 实验 llm_mock_intelligent 完成
2025-08-03 16:10:32,334 - INFO - 执行时间: 18.74秒
2025-08-03 16:10:32,334 - INFO - 返回码: 0
2025-08-03 16:10:34,337 - INFO - 开始分析实验结果
2025-08-03 16:10:34,339 - INFO - 分析完成，成功实验: 1/2
2025-08-03 16:10:34,341 - INFO - 对比报告已生成: src\results\experiments\comparison_report.md
2025-08-03 16:10:34,341 - INFO - 所有实验完成
2025-08-03 16:10:34,341 - INFO - 实验日志: src\results\experiments\llm_comparison_20250803_160511.log
2025-08-03 16:10:34,342 - INFO - 对比报告: src\results\experiments\comparison_report.md
