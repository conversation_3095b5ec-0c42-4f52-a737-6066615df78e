# -*- coding: utf-8 -*-
"""
个体状态分析器

负责提取和分析个体的多维特征，包括适应度、排名、停滞状态、多样性贡献等信息。
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum


class StagnationLevel(Enum):
    """停滞程度枚举"""
    NONE = "none"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class IndividualStateMetrics:
    """个体状态指标"""
    individual_id: int
    fitness_value: float
    fitness_rank: int
    fitness_percentile: float
    
    # 停滞分析
    stagnation_duration: int = 0
    stagnation_level: StagnationLevel = StagnationLevel.NONE
    last_improvement_iteration: int = 0
    improvement_rate: float = 0.0
    
    # 多样性分析
    diversity_contribution: float = 0.0
    distance_to_best: float = float('inf')
    distance_to_centroid: float = 0.0
    local_density: float = 0.0
    
    # 景观特征
    local_ruggedness: float = 0.0
    local_gradient_strength: float = 0.0
    improvement_potential: float = 0.0
    neighborhood_quality: float = 0.0
    
    # 历史表现
    recent_improvements: List[float] = field(default_factory=list)
    performance_trend: float = 0.0
    strategy_success_history: Dict[str, List[bool]] = field(default_factory=dict)
    preferred_strategies: List[str] = field(default_factory=list)


class IndividualStateAnalyzer:
    """
    个体状态分析器
    
    提供个体状态的全面分析，包括适应度分析、停滞检测、多样性评估、
    景观特征提取和历史表现分析。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 历史状态缓存
        self.individual_history: Dict[int, List[IndividualStateMetrics]] = {}
        self.population_history: List[List[Dict]] = []
        
        # 配置参数
        self.history_window = self.config.get('history_window', 20)
        self.improvement_threshold = self.config.get('improvement_threshold', 1e-6)
        self.diversity_calculation_method = self.config.get('diversity_method', 'hamming')
        
    def analyze_population_states(self, populations: List[Dict], 
                                iteration: int,
                                distance_matrix: Optional[np.ndarray] = None) -> List[IndividualStateMetrics]:
        """
        分析整个种群的个体状态
        
        Args:
            populations: 种群数据列表
            iteration: 当前迭代次数
            distance_matrix: 距离矩阵（可选）
            
        Returns:
            个体状态指标列表
        """
        self.logger.info(f"开始分析种群状态 (迭代 {iteration}, 种群大小: {len(populations)})")
        
        # 1. 基础适应度分析
        fitness_analysis = self._analyze_fitness_distribution(populations)
        
        # 2. 计算种群中心点
        population_centroid = self._calculate_population_centroid(populations)
        
        # 3. 分析每个个体
        individual_metrics = []
        for i, individual in enumerate(populations):
            metrics = self._analyze_individual_state(
                individual, i, fitness_analysis, population_centroid, 
                populations, iteration, distance_matrix
            )
            individual_metrics.append(metrics)
        
        # 4. 更新历史记录
        self._update_population_history(populations, iteration)
        
        # 5. 计算相对指标
        self._calculate_relative_metrics(individual_metrics, populations)
        
        self.logger.info(f"种群状态分析完成，分析了 {len(individual_metrics)} 个个体")
        return individual_metrics
    
    def _analyze_fitness_distribution(self, populations: List[Dict]) -> Dict[str, Any]:
        """分析适应度分布"""
        fitness_values = [pop.get('cur_cost', float('inf')) for pop in populations]
        
        # 排序获取排名信息
        sorted_fitness = sorted(enumerate(fitness_values), key=lambda x: x[1])
        
        # 计算统计信息
        fitness_stats = {
            'min_fitness': min(fitness_values),
            'max_fitness': max(fitness_values),
            'mean_fitness': np.mean(fitness_values),
            'std_fitness': np.std(fitness_values),
            'median_fitness': np.median(fitness_values),
            'fitness_range': max(fitness_values) - min(fitness_values),
            'sorted_indices': [idx for idx, _ in sorted_fitness],
            'sorted_fitness': [fitness for _, fitness in sorted_fitness]
        }
        
        return fitness_stats
    
    def _calculate_population_centroid(self, populations: List[Dict]) -> List[int]:
        """计算种群中心点"""
        tours = [pop.get('tour', []) for pop in populations if pop.get('tour')]
        
        if not tours:
            return []
        
        # 简化实现：使用最优解作为中心点
        # 在实际应用中可以使用更复杂的中心点计算方法
        fitness_values = [(i, pop.get('cur_cost', float('inf'))) for i, pop in enumerate(populations)]
        best_idx = min(fitness_values, key=lambda x: x[1])[0]
        
        return populations[best_idx].get('tour', [])
    
    def _analyze_individual_state(self, individual: Dict, individual_idx: int,
                                fitness_analysis: Dict, population_centroid: List[int],
                                populations: List[Dict], iteration: int,
                                distance_matrix: Optional[np.ndarray] = None) -> IndividualStateMetrics:
        """分析单个个体状态"""
        
        individual_id = individual_idx
        fitness_value = individual.get('cur_cost', float('inf'))
        tour = individual.get('tour', [])
        
        # 基础适应度信息
        sorted_indices = fitness_analysis['sorted_indices']
        rank = sorted_indices.index(individual_idx)
        percentile = rank / len(populations) if len(populations) > 0 else 0.0
        
        # 获取历史状态
        historical_states = self.individual_history.get(individual_id, [])
        
        # 停滞分析
        stagnation_info = self._analyze_stagnation(
            individual_id, fitness_value, historical_states, iteration
        )
        
        # 多样性分析
        diversity_info = self._analyze_diversity(
            tour, populations, population_centroid, fitness_analysis
        )
        
        # 景观特征分析
        landscape_info = self._analyze_local_landscape(
            individual, populations, distance_matrix
        )
        
        # 历史表现分析
        performance_info = self._analyze_historical_performance(historical_states)
        
        # 构建个体状态指标
        metrics = IndividualStateMetrics(
            individual_id=individual_id,
            fitness_value=fitness_value,
            fitness_rank=rank,
            fitness_percentile=percentile,
            
            # 停滞信息
            stagnation_duration=stagnation_info['duration'],
            stagnation_level=stagnation_info['level'],
            last_improvement_iteration=stagnation_info['last_improvement'],
            improvement_rate=stagnation_info['improvement_rate'],
            
            # 多样性信息
            diversity_contribution=diversity_info['contribution'],
            distance_to_best=diversity_info['distance_to_best'],
            distance_to_centroid=diversity_info['distance_to_centroid'],
            local_density=diversity_info['local_density'],
            
            # 景观信息
            local_ruggedness=landscape_info['ruggedness'],
            local_gradient_strength=landscape_info['gradient_strength'],
            improvement_potential=landscape_info['improvement_potential'],
            neighborhood_quality=landscape_info['neighborhood_quality'],
            
            # 历史表现
            recent_improvements=performance_info['recent_improvements'],
            performance_trend=performance_info['trend'],
            strategy_success_history=performance_info['strategy_history'],
            preferred_strategies=performance_info['preferred_strategies']
        )
        
        # 更新个体历史
        self._update_individual_history(individual_id, metrics)
        
        return metrics
    
    def _analyze_stagnation(self, individual_id: int, current_fitness: float,
                          historical_states: List[IndividualStateMetrics],
                          iteration: int) -> Dict[str, Any]:
        """分析个体停滞状态"""
        
        if not historical_states:
            return {
                'duration': 0,
                'level': StagnationLevel.NONE,
                'last_improvement': iteration,
                'improvement_rate': 0.0
            }
        
        # 计算停滞持续时间
        stagnation_duration = 0
        last_improvement_iteration = iteration
        recent_improvements = []
        
        for i in range(len(historical_states) - 1, -1, -1):
            prev_state = historical_states[i]
            improvement = prev_state.fitness_value - current_fitness
            
            if improvement > self.improvement_threshold:
                last_improvement_iteration = iteration - (len(historical_states) - i)
                break
            else:
                stagnation_duration += 1
                
            recent_improvements.append(improvement)
        
        # 确定停滞等级
        if stagnation_duration == 0:
            stagnation_level = StagnationLevel.NONE
        elif stagnation_duration <= 2:
            stagnation_level = StagnationLevel.LOW
        elif stagnation_duration <= 5:
            stagnation_level = StagnationLevel.MODERATE
        elif stagnation_duration <= 10:
            stagnation_level = StagnationLevel.HIGH
        else:
            stagnation_level = StagnationLevel.CRITICAL
        
        # 计算改进率
        improvement_rate = 0.0
        if len(recent_improvements) > 1:
            positive_improvements = [imp for imp in recent_improvements if imp > 0]
            improvement_rate = len(positive_improvements) / len(recent_improvements)
        
        return {
            'duration': stagnation_duration,
            'level': stagnation_level,
            'last_improvement': last_improvement_iteration,
            'improvement_rate': improvement_rate
        }
    
    def _analyze_diversity(self, individual_tour: List[int], populations: List[Dict],
                         population_centroid: List[int], fitness_analysis: Dict) -> Dict[str, Any]:
        """分析个体多样性贡献"""
        
        if not individual_tour:
            return {
                'contribution': 0.0,
                'distance_to_best': float('inf'),
                'distance_to_centroid': 0.0,
                'local_density': 0.0
            }
        
        all_tours = [pop.get('tour', []) for pop in populations if pop.get('tour')]
        
        # 计算多样性贡献
        diversity_contribution = self._calculate_diversity_contribution(individual_tour, all_tours)
        
        # 计算到最优解的距离
        best_idx = fitness_analysis['sorted_indices'][0]
        best_tour = populations[best_idx].get('tour', [])
        distance_to_best = self._calculate_tour_distance(individual_tour, best_tour)
        
        # 计算到中心点的距离
        distance_to_centroid = self._calculate_tour_distance(individual_tour, population_centroid)
        
        # 计算局部密度
        local_density = self._calculate_local_density(individual_tour, all_tours)
        
        return {
            'contribution': diversity_contribution,
            'distance_to_best': distance_to_best,
            'distance_to_centroid': distance_to_centroid,
            'local_density': local_density
        }
    
    def _analyze_local_landscape(self, individual: Dict, populations: List[Dict],
                               distance_matrix: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """分析个体局部景观特征"""
        
        # 简化实现，返回默认值
        # 在实际应用中，这里应该实现复杂的景观分析算法
        
        return {
            'ruggedness': 0.5,  # 局部粗糙度
            'gradient_strength': 0.5,  # 梯度强度
            'improvement_potential': 0.5,  # 改进潜力
            'neighborhood_quality': 0.5  # 邻域质量
        }
    
    def _analyze_historical_performance(self, historical_states: List[IndividualStateMetrics]) -> Dict[str, Any]:
        """分析历史表现"""
        
        if not historical_states:
            return {
                'recent_improvements': [],
                'trend': 0.0,
                'strategy_history': {},
                'preferred_strategies': []
            }
        
        # 提取近期改进
        recent_improvements = []
        for state in historical_states[-10:]:  # 最近10次状态
            recent_improvements.extend(state.recent_improvements)
        
        # 计算表现趋势
        if len(historical_states) >= 2:
            recent_fitness = [state.fitness_value for state in historical_states[-5:]]
            if len(recent_fitness) >= 2:
                trend = np.polyfit(range(len(recent_fitness)), recent_fitness, 1)[0]
            else:
                trend = 0.0
        else:
            trend = 0.0
        
        # 策略成功历史（简化实现）
        strategy_history = {}
        preferred_strategies = []
        
        return {
            'recent_improvements': recent_improvements,
            'trend': trend,
            'strategy_history': strategy_history,
            'preferred_strategies': preferred_strategies
        }

    def _calculate_diversity_contribution(self, individual_tour: List[int], all_tours: List[List[int]]) -> float:
        """计算个体的多样性贡献"""
        if len(all_tours) <= 1:
            return 0.0

        try:
            total_distance = 0.0
            count = 0

            for other_tour in all_tours:
                if not self._tours_equal(other_tour, individual_tour):
                    distance = self._calculate_tour_distance(individual_tour, other_tour)
                    total_distance += distance
                    count += 1

            if count == 0:
                return 0.0

            avg_distance = total_distance / count
            return min(1.0, avg_distance)

        except Exception as e:
            self.logger.warning(f"计算多样性贡献时出错: {e}")
            return 0.5

    def _tours_equal(self, tour1: List[int], tour2: List[int]) -> bool:
        """安全地比较两个tour是否相等"""
        try:
            if len(tour1) != len(tour2):
                return False
            return all(a == b for a, b in zip(tour1, tour2))
        except Exception:
            return False

    def _calculate_tour_distance(self, tour1: List[int], tour2: List[int]) -> float:
        """计算两个路径之间的距离"""
        if not tour1 or not tour2 or len(tour1) != len(tour2):
            return 0.0

        try:
            if self.diversity_calculation_method == 'hamming':
                # 汉明距离 - 使用安全的比较方法
                distance = sum(self._safe_element_compare(a, b) for a, b in zip(tour1, tour2))
                return distance / len(tour1)
            elif self.diversity_calculation_method == 'edge_based':
                # 基于边的距离
                edges1 = set((tour1[i], tour1[(i+1) % len(tour1)]) for i in range(len(tour1)))
                edges2 = set((tour2[i], tour2[(i+1) % len(tour2)]) for i in range(len(tour2)))
                common_edges = len(edges1.intersection(edges2))
                return 1.0 - (common_edges / len(tour1))
            else:
                # 默认使用汉明距离 - 使用安全的比较方法
                distance = sum(self._safe_element_compare(a, b) for a, b in zip(tour1, tour2))
                return distance / len(tour1)

        except Exception as e:
            self.logger.warning(f"计算路径距离时出错: {e}")
            return 0.0

    def _safe_element_compare(self, a, b) -> int:
        """安全地比较两个元素是否不相等，返回0或1"""
        try:
            # 处理numpy数组元素的比较
            if hasattr(a, 'item'):
                a = a.item()
            if hasattr(b, 'item'):
                b = b.item()
            return 1 if a != b else 0
        except Exception:
            return 0

    def _calculate_local_density(self, individual_tour: List[int], all_tours: List[List[int]]) -> float:
        """计算个体的局部密度"""
        if len(all_tours) <= 1:
            return 0.0

        try:
            # 计算与所有其他个体的平均距离
            distances = []
            for other_tour in all_tours:
                if not self._tours_equal(other_tour, individual_tour):
                    distance = self._calculate_tour_distance(individual_tour, other_tour)
                    distances.append(distance)

            if not distances:
                return 0.0

            avg_distance = np.mean(distances)
            # 密度与平均距离成反比
            density = 1.0 / (1.0 + avg_distance)
            return density

        except Exception as e:
            self.logger.warning(f"计算局部密度时出错: {e}")
            return 0.5

    def _calculate_relative_metrics(self, individual_metrics: List[IndividualStateMetrics],
                                  populations: List[Dict]):
        """计算相对指标"""
        if not individual_metrics:
            return

        # 计算多样性贡献的相对排名
        diversity_values = [metrics.diversity_contribution for metrics in individual_metrics]
        diversity_ranks = np.argsort(np.argsort(diversity_values)[::-1])  # 降序排名

        for i, metrics in enumerate(individual_metrics):
            metrics.diversity_rank = diversity_ranks[i]
            metrics.diversity_percentile = diversity_ranks[i] / len(individual_metrics)

    def _update_individual_history(self, individual_id: int, metrics: IndividualStateMetrics):
        """更新个体历史记录"""
        if individual_id not in self.individual_history:
            self.individual_history[individual_id] = []

        self.individual_history[individual_id].append(metrics)

        # 保持历史记录在合理范围内
        if len(self.individual_history[individual_id]) > self.history_window:
            self.individual_history[individual_id] = self.individual_history[individual_id][-self.history_window:]

    def _update_population_history(self, populations: List[Dict], iteration: int):
        """更新种群历史记录"""
        population_snapshot = {
            'iteration': iteration,
            'population_size': len(populations),
            'fitness_values': [pop.get('cur_cost', float('inf')) for pop in populations],
            'best_fitness': min(pop.get('cur_cost', float('inf')) for pop in populations),
            'mean_fitness': np.mean([pop.get('cur_cost', float('inf')) for pop in populations]),
            'diversity': self._calculate_population_diversity(populations)
        }

        self.population_history.append(population_snapshot)

        # 保持历史记录在合理范围内
        if len(self.population_history) > self.history_window:
            self.population_history = self.population_history[-self.history_window:]

    def _calculate_population_diversity(self, populations: List[Dict]) -> float:
        """计算种群多样性"""
        tours = [pop.get('tour', []) for pop in populations if pop.get('tour')]

        if len(tours) <= 1:
            return 0.0

        try:
            total_distance = 0.0
            count = 0

            for i in range(len(tours)):
                for j in range(i + 1, len(tours)):
                    distance = self._calculate_tour_distance(tours[i], tours[j])
                    total_distance += distance
                    count += 1

            if count == 0:
                return 0.0

            avg_distance = total_distance / count
            return min(1.0, avg_distance)

        except Exception as e:
            self.logger.warning(f"计算种群多样性时出错: {e}")
            return 0.5

    def get_population_summary(self) -> Dict[str, Any]:
        """获取种群状态摘要"""
        if not self.population_history:
            return {}

        latest = self.population_history[-1]

        summary = {
            'current_iteration': latest['iteration'],
            'population_size': latest['population_size'],
            'best_fitness': latest['best_fitness'],
            'mean_fitness': latest['mean_fitness'],
            'diversity': latest['diversity'],
            'individual_count': len(self.individual_history),
            'history_length': len(self.population_history)
        }

        # 计算趋势
        if len(self.population_history) >= 2:
            prev = self.population_history[-2]
            summary['fitness_improvement'] = prev['best_fitness'] - latest['best_fitness']
            summary['diversity_change'] = latest['diversity'] - prev['diversity']

        return summary

    def get_individual_summary(self, individual_id: int) -> Optional[Dict[str, Any]]:
        """获取个体状态摘要"""
        if individual_id not in self.individual_history:
            return None

        history = self.individual_history[individual_id]
        if not history:
            return None

        latest = history[-1]

        summary = {
            'individual_id': individual_id,
            'current_fitness': latest.fitness_value,
            'fitness_rank': latest.fitness_rank,
            'fitness_percentile': latest.fitness_percentile,
            'stagnation_duration': latest.stagnation_duration,
            'stagnation_level': latest.stagnation_level.value,
            'diversity_contribution': latest.diversity_contribution,
            'improvement_potential': latest.improvement_potential,
            'history_length': len(history)
        }

        # 计算历史趋势
        if len(history) >= 2:
            prev = history[-2]
            summary['fitness_change'] = prev.fitness_value - latest.fitness_value
            summary['rank_change'] = prev.fitness_rank - latest.fitness_rank

        return summary
