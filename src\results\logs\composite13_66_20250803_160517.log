2025-08-03 16:05:17,812 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:05:17,812 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:05:17,815 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:05:17,827 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10053.000, 多样性=0.962
2025-08-03 16:05:17,831 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:05:17,837 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.962
2025-08-03 16:05:17,878 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:05:17,881 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-03 16:05:17,881 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:05:17,881 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:05:17,881 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:05:18,093 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -5045.600, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:05:18,093 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:05:18,094 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:05:18,146 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:05:18,421 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_160518.html
2025-08-03 16:05:18,468 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_160518.html
2025-08-03 16:05:18,468 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:05:18,468 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:05:18,469 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.5881秒
2025-08-03 16:05:18,469 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:05:18,469 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -5045.5999999999985, 'local_optima_density': 0.1, 'gradient_variance': 1293528270.9720001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8941881858672536, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5045.600)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208318.0938537, 'performance_metrics': {}}}
2025-08-03 16:05:18,469 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:05:18,469 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:05:18,469 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:05:18,470 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:18,470 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:05:18,470 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:05:18,470 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:18,470 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:05:18,470 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:05:18,470 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:18,470 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:05:18,470 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:05:18,471 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:05:18,471 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:05:18,471 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:18,475 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:18,475 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:18,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12348.0, 路径长度: 66
2025-08-03 16:05:18,607 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}
2025-08-03 16:05:18,607 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12348.00)
2025-08-03 16:05:18,607 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:05:18,608 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:18,609 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:18,610 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 118766.0
2025-08-03 16:05:20,116 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:05:20,116 - ExploitationExpert - INFO - res_population_costs: [9821.0]
2025-08-03 16:05:20,117 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:05:20,117 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:20,118 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': array([ 7,  3,  9, 11,  1,  0, 10,  8,  2,  6,  4,  5, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10269.0}, {'tour': array([65, 52, 63, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10080.0}, {'tour': array([16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10076.0}, {'tour': array([17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10053.0}, {'tour': array([46, 57, 29, 41, 10,  9, 38, 58, 25, 43,  7, 23, 14, 51, 40, 60, 52,
       65, 59,  6, 13, 20, 48, 35, 12, 63,  1,  2, 21, 15, 44,  8, 31, 32,
       56, 53, 47, 22, 64, 36,  3, 49, 45,  5, 61, 34,  4, 50, 42, 19, 26,
       11, 37, 27, 39, 33, 54, 17,  0, 55, 30, 16, 62, 24, 28, 18],
      dtype=int64), 'cur_cost': 103715.0}, {'tour': array([57, 52, 51, 18, 54, 14, 45, 24, 38, 17, 46, 21, 28,  0, 40, 19, 11,
       20, 27, 22, 13, 30, 35, 34, 16, 26, 48,  8, 56, 25,  4,  6, 42,  3,
       41, 53, 23, 47,  5, 37,  1, 50, 65, 49, 10, 36, 33, 39, 29, 64,  2,
       31, 44, 61, 15, 63,  9, 43, 59, 60,  7, 32, 58, 12, 62, 55],
      dtype=int64), 'cur_cost': 112900.0}, {'tour': array([38,  6, 35, 51, 55, 39, 21, 11, 41, 59, 48, 52, 18, 17, 22, 61, 32,
        8, 49,  1, 37, 50, 20,  7, 45,  2, 46, 33, 63, 47, 28, 62, 26, 23,
        5, 34, 14, 13, 31, 42,  0, 19, 64, 30, 56, 27, 43, 44, 40, 12, 58,
        3, 53,  9, 65, 54,  4, 25, 57, 15, 29, 60, 16, 10, 36, 24],
      dtype=int64), 'cur_cost': 123367.0}, {'tour': array([46, 12, 52, 38, 45, 34, 47, 10, 18, 15, 11, 57, 60, 61, 16, 41, 33,
       54,  7, 53, 30, 37,  9, 40, 59, 26, 64, 24, 49, 62, 63, 21, 36, 32,
        0, 35,  6, 20,  4,  1,  2,  8, 65, 39, 48, 28, 19, 55, 22, 17, 29,
       50, 31,  3,  5, 23, 44, 13, 42, 56, 27, 51, 58, 25, 14, 43],
      dtype=int64), 'cur_cost': 103721.0}, {'tour': array([21, 65, 14, 55,  5,  1, 29, 50, 10, 49,  3, 37, 59, 24, 51,  4, 23,
        2, 56, 15, 33, 32, 43, 63, 48, 18, 60, 34, 25, 38, 20, 39, 52, 45,
       26, 61, 58, 30, 41, 28, 22, 31, 44, 19,  7,  6, 47, 17,  8, 53,  0,
       54, 57, 12, 62, 27, 11, 13, 64, 35, 36, 40, 16, 42,  9, 46],
      dtype=int64), 'cur_cost': 116531.0}, {'tour': array([12, 25, 38,  2,  3, 18, 35, 64, 14, 45, 27, 51, 47, 41,  8, 53, 30,
        9,  6, 40,  0, 52, 50, 42, 34, 24,  1, 13, 33, 57, 37, 16, 10, 39,
       36, 28, 55, 46, 54, 44, 32, 21, 15, 43, 62,  7, 19,  5, 11, 17,  4,
       20, 31, 60, 23, 26, 22, 61, 59, 49, 29, 65, 58, 56, 63, 48],
      dtype=int64), 'cur_cost': 105527.0}, {'tour': array([36, 24, 29, 30, 49, 48, 55, 22, 44,  0,  8, 34, 43, 51,  5, 23, 46,
       20,  3,  2, 26, 47, 64, 28, 56, 61, 31, 19, 59, 11, 32, 25, 12, 17,
       39, 52, 10, 60,  4, 14,  1, 33, 18, 13, 27, 63, 37, 40, 41, 53, 54,
       57, 65, 38,  6, 35, 62, 16,  9, 45,  7, 21, 15, 50, 58, 42],
      dtype=int64), 'cur_cost': 100376.0}, {'tour': array([53, 34, 39, 31,  2, 17, 10, 12,  4, 40,  8, 48, 49, 28, 56, 44, 16,
       58, 35, 29, 62,  9, 60, 21,  0,  1, 30, 65, 18, 37, 50, 63, 32, 52,
       47, 55, 64, 36, 22, 14,  6, 15, 24, 25, 38, 41, 20, 45, 51, 57, 11,
       33, 54, 27, 42, 23,  3, 59, 26, 43,  7, 46, 61, 13,  5, 19],
      dtype=int64), 'cur_cost': 118715.0}, {'tour': array([60, 51, 27, 25, 65,  2, 10, 28, 31, 47, 41, 37, 24, 53, 38, 32, 14,
       49, 63, 22, 58, 36, 55, 54, 46, 42,  0, 45, 64, 12, 23, 29,  8, 26,
       39, 16, 11, 50, 56, 20, 15, 17,  6,  7, 34,  4, 57, 48, 62, 61, 35,
        3,  5, 43,  1, 30, 40, 19, 44, 33, 52, 18, 13, 21,  9, 59],
      dtype=int64), 'cur_cost': 107178.0}, {'tour': array([55, 46, 12, 48,  4,  1, 24, 33,  5, 11, 35,  3, 45, 56, 36, 61, 63,
       30, 50, 38, 62, 27, 22, 26, 57, 10, 53, 41, 19, 40, 29, 44, 14,  7,
       51,  6, 60, 31, 59, 13, 52, 49, 18, 37,  8, 65, 34, 54, 16, 47, 20,
       58, 64, 42, 23, 21, 32,  9, 25, 39,  2,  0, 43, 17, 15, 28],
      dtype=int64), 'cur_cost': 117237.0}, {'tour': array([40, 63, 28,  7, 44, 12, 48,  5, 61, 35, 52, 17, 26, 64, 19, 20, 57,
       32, 59, 65, 16, 21, 62, 42,  3, 27, 13, 11, 14, 53, 51, 23, 43, 33,
       58, 55, 34,  2,  0, 39, 36, 56, 49, 31, 30, 29,  9, 50,  1,  4, 18,
       60, 10, 37, 46, 45, 47, 41, 54,  6, 24, 15,  8, 38, 25, 22],
      dtype=int64), 'cur_cost': 114969.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:20,126 - ExploitationExpert - INFO - 局部搜索耗时: 1.52秒
2025-08-03 16:05:20,126 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:05:20,126 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}
2025-08-03 16:05:20,126 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 118766.00)
2025-08-03 16:05:20,126 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:05:20,126 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:05:20,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:20,131 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:20,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:20,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12864.0, 路径长度: 66
2025-08-03 16:05:20,131 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}
2025-08-03 16:05:20,131 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12864.00)
2025-08-03 16:05:20,131 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:05:20,132 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:05:20,132 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:20,136 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:20,136 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:20,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12341.0, 路径长度: 66
2025-08-03 16:05:20,136 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}
2025-08-03 16:05:20,136 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 12341.00)
2025-08-03 16:05:20,136 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:05:20,136 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:20,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:20,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99814.0
2025-08-03 16:05:21,645 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:05:21,645 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0]
2025-08-03 16:05:21,645 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:21,646 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:21,646 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': array([17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10053.0}, {'tour': array([46, 57, 29, 41, 10,  9, 38, 58, 25, 43,  7, 23, 14, 51, 40, 60, 52,
       65, 59,  6, 13, 20, 48, 35, 12, 63,  1,  2, 21, 15, 44,  8, 31, 32,
       56, 53, 47, 22, 64, 36,  3, 49, 45,  5, 61, 34,  4, 50, 42, 19, 26,
       11, 37, 27, 39, 33, 54, 17,  0, 55, 30, 16, 62, 24, 28, 18],
      dtype=int64), 'cur_cost': 103715.0}, {'tour': array([57, 52, 51, 18, 54, 14, 45, 24, 38, 17, 46, 21, 28,  0, 40, 19, 11,
       20, 27, 22, 13, 30, 35, 34, 16, 26, 48,  8, 56, 25,  4,  6, 42,  3,
       41, 53, 23, 47,  5, 37,  1, 50, 65, 49, 10, 36, 33, 39, 29, 64,  2,
       31, 44, 61, 15, 63,  9, 43, 59, 60,  7, 32, 58, 12, 62, 55],
      dtype=int64), 'cur_cost': 112900.0}, {'tour': array([38,  6, 35, 51, 55, 39, 21, 11, 41, 59, 48, 52, 18, 17, 22, 61, 32,
        8, 49,  1, 37, 50, 20,  7, 45,  2, 46, 33, 63, 47, 28, 62, 26, 23,
        5, 34, 14, 13, 31, 42,  0, 19, 64, 30, 56, 27, 43, 44, 40, 12, 58,
        3, 53,  9, 65, 54,  4, 25, 57, 15, 29, 60, 16, 10, 36, 24],
      dtype=int64), 'cur_cost': 123367.0}, {'tour': array([46, 12, 52, 38, 45, 34, 47, 10, 18, 15, 11, 57, 60, 61, 16, 41, 33,
       54,  7, 53, 30, 37,  9, 40, 59, 26, 64, 24, 49, 62, 63, 21, 36, 32,
        0, 35,  6, 20,  4,  1,  2,  8, 65, 39, 48, 28, 19, 55, 22, 17, 29,
       50, 31,  3,  5, 23, 44, 13, 42, 56, 27, 51, 58, 25, 14, 43],
      dtype=int64), 'cur_cost': 103721.0}, {'tour': array([21, 65, 14, 55,  5,  1, 29, 50, 10, 49,  3, 37, 59, 24, 51,  4, 23,
        2, 56, 15, 33, 32, 43, 63, 48, 18, 60, 34, 25, 38, 20, 39, 52, 45,
       26, 61, 58, 30, 41, 28, 22, 31, 44, 19,  7,  6, 47, 17,  8, 53,  0,
       54, 57, 12, 62, 27, 11, 13, 64, 35, 36, 40, 16, 42,  9, 46],
      dtype=int64), 'cur_cost': 116531.0}, {'tour': array([12, 25, 38,  2,  3, 18, 35, 64, 14, 45, 27, 51, 47, 41,  8, 53, 30,
        9,  6, 40,  0, 52, 50, 42, 34, 24,  1, 13, 33, 57, 37, 16, 10, 39,
       36, 28, 55, 46, 54, 44, 32, 21, 15, 43, 62,  7, 19,  5, 11, 17,  4,
       20, 31, 60, 23, 26, 22, 61, 59, 49, 29, 65, 58, 56, 63, 48],
      dtype=int64), 'cur_cost': 105527.0}, {'tour': array([36, 24, 29, 30, 49, 48, 55, 22, 44,  0,  8, 34, 43, 51,  5, 23, 46,
       20,  3,  2, 26, 47, 64, 28, 56, 61, 31, 19, 59, 11, 32, 25, 12, 17,
       39, 52, 10, 60,  4, 14,  1, 33, 18, 13, 27, 63, 37, 40, 41, 53, 54,
       57, 65, 38,  6, 35, 62, 16,  9, 45,  7, 21, 15, 50, 58, 42],
      dtype=int64), 'cur_cost': 100376.0}, {'tour': array([53, 34, 39, 31,  2, 17, 10, 12,  4, 40,  8, 48, 49, 28, 56, 44, 16,
       58, 35, 29, 62,  9, 60, 21,  0,  1, 30, 65, 18, 37, 50, 63, 32, 52,
       47, 55, 64, 36, 22, 14,  6, 15, 24, 25, 38, 41, 20, 45, 51, 57, 11,
       33, 54, 27, 42, 23,  3, 59, 26, 43,  7, 46, 61, 13,  5, 19],
      dtype=int64), 'cur_cost': 118715.0}, {'tour': array([60, 51, 27, 25, 65,  2, 10, 28, 31, 47, 41, 37, 24, 53, 38, 32, 14,
       49, 63, 22, 58, 36, 55, 54, 46, 42,  0, 45, 64, 12, 23, 29,  8, 26,
       39, 16, 11, 50, 56, 20, 15, 17,  6,  7, 34,  4, 57, 48, 62, 61, 35,
        3,  5, 43,  1, 30, 40, 19, 44, 33, 52, 18, 13, 21,  9, 59],
      dtype=int64), 'cur_cost': 107178.0}, {'tour': array([55, 46, 12, 48,  4,  1, 24, 33,  5, 11, 35,  3, 45, 56, 36, 61, 63,
       30, 50, 38, 62, 27, 22, 26, 57, 10, 53, 41, 19, 40, 29, 44, 14,  7,
       51,  6, 60, 31, 59, 13, 52, 49, 18, 37,  8, 65, 34, 54, 16, 47, 20,
       58, 64, 42, 23, 21, 32,  9, 25, 39,  2,  0, 43, 17, 15, 28],
      dtype=int64), 'cur_cost': 117237.0}, {'tour': array([40, 63, 28,  7, 44, 12, 48,  5, 61, 35, 52, 17, 26, 64, 19, 20, 57,
       32, 59, 65, 16, 21, 62, 42,  3, 27, 13, 11, 14, 53, 51, 23, 43, 33,
       58, 55, 34,  2,  0, 39, 36, 56, 49, 31, 30, 29,  9, 50,  1,  4, 18,
       60, 10, 37, 46, 45, 47, 41, 54,  6, 24, 15,  8, 38, 25, 22],
      dtype=int64), 'cur_cost': 114969.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:21,655 - ExploitationExpert - INFO - 局部搜索耗时: 1.52秒
2025-08-03 16:05:21,655 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:05:21,655 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}
2025-08-03 16:05:21,656 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 99814.00)
2025-08-03 16:05:21,656 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:05:21,656 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:05:21,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:21,670 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:21,670 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:21,670 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59420.0, 路径长度: 66
2025-08-03 16:05:21,670 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}
2025-08-03 16:05:21,670 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 59420.00)
2025-08-03 16:05:21,670 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:05:21,671 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:05:21,671 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:21,674 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:21,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:21,675 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12820.0, 路径长度: 66
2025-08-03 16:05:21,675 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}
2025-08-03 16:05:21,675 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12820.00)
2025-08-03 16:05:21,675 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:05:21,675 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:21,675 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:21,675 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102678.0
2025-08-03 16:05:22,164 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 16:05:22,164 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9565, 9540, 9540, 9538, 9527, 9527]
2025-08-03 16:05:22,164 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:05:22,167 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,167 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}, {'tour': array([38,  6, 35, 51, 55, 39, 21, 11, 41, 59, 48, 52, 18, 17, 22, 61, 32,
        8, 49,  1, 37, 50, 20,  7, 45,  2, 46, 33, 63, 47, 28, 62, 26, 23,
        5, 34, 14, 13, 31, 42,  0, 19, 64, 30, 56, 27, 43, 44, 40, 12, 58,
        3, 53,  9, 65, 54,  4, 25, 57, 15, 29, 60, 16, 10, 36, 24],
      dtype=int64), 'cur_cost': 123367.0}, {'tour': array([46, 12, 52, 38, 45, 34, 47, 10, 18, 15, 11, 57, 60, 61, 16, 41, 33,
       54,  7, 53, 30, 37,  9, 40, 59, 26, 64, 24, 49, 62, 63, 21, 36, 32,
        0, 35,  6, 20,  4,  1,  2,  8, 65, 39, 48, 28, 19, 55, 22, 17, 29,
       50, 31,  3,  5, 23, 44, 13, 42, 56, 27, 51, 58, 25, 14, 43],
      dtype=int64), 'cur_cost': 103721.0}, {'tour': array([21, 65, 14, 55,  5,  1, 29, 50, 10, 49,  3, 37, 59, 24, 51,  4, 23,
        2, 56, 15, 33, 32, 43, 63, 48, 18, 60, 34, 25, 38, 20, 39, 52, 45,
       26, 61, 58, 30, 41, 28, 22, 31, 44, 19,  7,  6, 47, 17,  8, 53,  0,
       54, 57, 12, 62, 27, 11, 13, 64, 35, 36, 40, 16, 42,  9, 46],
      dtype=int64), 'cur_cost': 116531.0}, {'tour': array([12, 25, 38,  2,  3, 18, 35, 64, 14, 45, 27, 51, 47, 41,  8, 53, 30,
        9,  6, 40,  0, 52, 50, 42, 34, 24,  1, 13, 33, 57, 37, 16, 10, 39,
       36, 28, 55, 46, 54, 44, 32, 21, 15, 43, 62,  7, 19,  5, 11, 17,  4,
       20, 31, 60, 23, 26, 22, 61, 59, 49, 29, 65, 58, 56, 63, 48],
      dtype=int64), 'cur_cost': 105527.0}, {'tour': array([36, 24, 29, 30, 49, 48, 55, 22, 44,  0,  8, 34, 43, 51,  5, 23, 46,
       20,  3,  2, 26, 47, 64, 28, 56, 61, 31, 19, 59, 11, 32, 25, 12, 17,
       39, 52, 10, 60,  4, 14,  1, 33, 18, 13, 27, 63, 37, 40, 41, 53, 54,
       57, 65, 38,  6, 35, 62, 16,  9, 45,  7, 21, 15, 50, 58, 42],
      dtype=int64), 'cur_cost': 100376.0}, {'tour': array([53, 34, 39, 31,  2, 17, 10, 12,  4, 40,  8, 48, 49, 28, 56, 44, 16,
       58, 35, 29, 62,  9, 60, 21,  0,  1, 30, 65, 18, 37, 50, 63, 32, 52,
       47, 55, 64, 36, 22, 14,  6, 15, 24, 25, 38, 41, 20, 45, 51, 57, 11,
       33, 54, 27, 42, 23,  3, 59, 26, 43,  7, 46, 61, 13,  5, 19],
      dtype=int64), 'cur_cost': 118715.0}, {'tour': array([60, 51, 27, 25, 65,  2, 10, 28, 31, 47, 41, 37, 24, 53, 38, 32, 14,
       49, 63, 22, 58, 36, 55, 54, 46, 42,  0, 45, 64, 12, 23, 29,  8, 26,
       39, 16, 11, 50, 56, 20, 15, 17,  6,  7, 34,  4, 57, 48, 62, 61, 35,
        3,  5, 43,  1, 30, 40, 19, 44, 33, 52, 18, 13, 21,  9, 59],
      dtype=int64), 'cur_cost': 107178.0}, {'tour': array([55, 46, 12, 48,  4,  1, 24, 33,  5, 11, 35,  3, 45, 56, 36, 61, 63,
       30, 50, 38, 62, 27, 22, 26, 57, 10, 53, 41, 19, 40, 29, 44, 14,  7,
       51,  6, 60, 31, 59, 13, 52, 49, 18, 37,  8, 65, 34, 54, 16, 47, 20,
       58, 64, 42, 23, 21, 32,  9, 25, 39,  2,  0, 43, 17, 15, 28],
      dtype=int64), 'cur_cost': 117237.0}, {'tour': array([40, 63, 28,  7, 44, 12, 48,  5, 61, 35, 52, 17, 26, 64, 19, 20, 57,
       32, 59, 65, 16, 21, 62, 42,  3, 27, 13, 11, 14, 53, 51, 23, 43, 33,
       58, 55, 34,  2,  0, 39, 36, 56, 49, 31, 30, 29,  9, 50,  1,  4, 18,
       60, 10, 37, 46, 45, 47, 41, 54,  6, 24, 15,  8, 38, 25, 22],
      dtype=int64), 'cur_cost': 114969.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:22,173 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-08-03 16:05:22,173 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:05:22,173 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}
2025-08-03 16:05:22,173 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 102678.00)
2025-08-03 16:05:22,173 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:05:22,173 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:05:22,173 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,186 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:22,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65045.0, 路径长度: 66
2025-08-03 16:05:22,187 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}
2025-08-03 16:05:22,187 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 65045.00)
2025-08-03 16:05:22,187 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:05:22,188 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:05:22,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,192 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:22,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,192 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12836.0, 路径长度: 66
2025-08-03 16:05:22,192 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}
2025-08-03 16:05:22,192 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12836.00)
2025-08-03 16:05:22,192 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:05:22,192 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,192 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,193 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 115590.0
2025-08-03 16:05:22,259 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:05:22,259 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9565, 9540, 9540, 9538, 9527, 9527, 9527, 9524, 9524]
2025-08-03 16:05:22,260 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:22,263 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,263 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}, {'tour': array([12, 25, 38,  2,  3, 18, 35, 64, 14, 45, 27, 51, 47, 41,  8, 53, 30,
        9,  6, 40,  0, 52, 50, 42, 34, 24,  1, 13, 33, 57, 37, 16, 10, 39,
       36, 28, 55, 46, 54, 44, 32, 21, 15, 43, 62,  7, 19,  5, 11, 17,  4,
       20, 31, 60, 23, 26, 22, 61, 59, 49, 29, 65, 58, 56, 63, 48],
      dtype=int64), 'cur_cost': 105527.0}, {'tour': array([36, 24, 29, 30, 49, 48, 55, 22, 44,  0,  8, 34, 43, 51,  5, 23, 46,
       20,  3,  2, 26, 47, 64, 28, 56, 61, 31, 19, 59, 11, 32, 25, 12, 17,
       39, 52, 10, 60,  4, 14,  1, 33, 18, 13, 27, 63, 37, 40, 41, 53, 54,
       57, 65, 38,  6, 35, 62, 16,  9, 45,  7, 21, 15, 50, 58, 42],
      dtype=int64), 'cur_cost': 100376.0}, {'tour': array([53, 34, 39, 31,  2, 17, 10, 12,  4, 40,  8, 48, 49, 28, 56, 44, 16,
       58, 35, 29, 62,  9, 60, 21,  0,  1, 30, 65, 18, 37, 50, 63, 32, 52,
       47, 55, 64, 36, 22, 14,  6, 15, 24, 25, 38, 41, 20, 45, 51, 57, 11,
       33, 54, 27, 42, 23,  3, 59, 26, 43,  7, 46, 61, 13,  5, 19],
      dtype=int64), 'cur_cost': 118715.0}, {'tour': array([60, 51, 27, 25, 65,  2, 10, 28, 31, 47, 41, 37, 24, 53, 38, 32, 14,
       49, 63, 22, 58, 36, 55, 54, 46, 42,  0, 45, 64, 12, 23, 29,  8, 26,
       39, 16, 11, 50, 56, 20, 15, 17,  6,  7, 34,  4, 57, 48, 62, 61, 35,
        3,  5, 43,  1, 30, 40, 19, 44, 33, 52, 18, 13, 21,  9, 59],
      dtype=int64), 'cur_cost': 107178.0}, {'tour': array([55, 46, 12, 48,  4,  1, 24, 33,  5, 11, 35,  3, 45, 56, 36, 61, 63,
       30, 50, 38, 62, 27, 22, 26, 57, 10, 53, 41, 19, 40, 29, 44, 14,  7,
       51,  6, 60, 31, 59, 13, 52, 49, 18, 37,  8, 65, 34, 54, 16, 47, 20,
       58, 64, 42, 23, 21, 32,  9, 25, 39,  2,  0, 43, 17, 15, 28],
      dtype=int64), 'cur_cost': 117237.0}, {'tour': array([40, 63, 28,  7, 44, 12, 48,  5, 61, 35, 52, 17, 26, 64, 19, 20, 57,
       32, 59, 65, 16, 21, 62, 42,  3, 27, 13, 11, 14, 53, 51, 23, 43, 33,
       58, 55, 34,  2,  0, 39, 36, 56, 49, 31, 30, 29,  9, 50,  1,  4, 18,
       60, 10, 37, 46, 45, 47, 41, 54,  6, 24, 15,  8, 38, 25, 22],
      dtype=int64), 'cur_cost': 114969.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:22,268 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:22,268 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:05:22,268 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}
2025-08-03 16:05:22,268 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 115590.00)
2025-08-03 16:05:22,268 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:05:22,269 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:05:22,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,272 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:22,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,273 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12339.0, 路径长度: 66
2025-08-03 16:05:22,273 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}
2025-08-03 16:05:22,273 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12339.00)
2025-08-03 16:05:22,273 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:05:22,273 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:05:22,273 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,276 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:05:22,277 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97235.0, 路径长度: 66
2025-08-03 16:05:22,277 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}
2025-08-03 16:05:22,277 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 97235.00)
2025-08-03 16:05:22,277 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:05:22,277 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,277 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,278 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 111160.0
2025-08-03 16:05:22,340 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:05:22,340 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9565, 9540, 9540, 9538, 9527, 9527, 9527, 9524, 9524]
2025-08-03 16:05:22,340 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:22,344 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,344 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': array([56, 31, 65, 19,  1, 45, 64, 10, 11, 55,  7, 20, 53, 15, 34,  2, 44,
       43,  0,  5, 40, 30,  8, 58, 28, 42,  6, 54, 33, 48, 57, 59, 32, 52,
       49, 63, 18, 29, 47, 16, 21,  4, 41, 35, 61, 50, 13, 46, 36,  3, 62,
       60, 39, 12, 14,  9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38],
      dtype=int64), 'cur_cost': 111160.0}, {'tour': array([60, 51, 27, 25, 65,  2, 10, 28, 31, 47, 41, 37, 24, 53, 38, 32, 14,
       49, 63, 22, 58, 36, 55, 54, 46, 42,  0, 45, 64, 12, 23, 29,  8, 26,
       39, 16, 11, 50, 56, 20, 15, 17,  6,  7, 34,  4, 57, 48, 62, 61, 35,
        3,  5, 43,  1, 30, 40, 19, 44, 33, 52, 18, 13, 21,  9, 59],
      dtype=int64), 'cur_cost': 107178.0}, {'tour': array([55, 46, 12, 48,  4,  1, 24, 33,  5, 11, 35,  3, 45, 56, 36, 61, 63,
       30, 50, 38, 62, 27, 22, 26, 57, 10, 53, 41, 19, 40, 29, 44, 14,  7,
       51,  6, 60, 31, 59, 13, 52, 49, 18, 37,  8, 65, 34, 54, 16, 47, 20,
       58, 64, 42, 23, 21, 32,  9, 25, 39,  2,  0, 43, 17, 15, 28],
      dtype=int64), 'cur_cost': 117237.0}, {'tour': array([40, 63, 28,  7, 44, 12, 48,  5, 61, 35, 52, 17, 26, 64, 19, 20, 57,
       32, 59, 65, 16, 21, 62, 42,  3, 27, 13, 11, 14, 53, 51, 23, 43, 33,
       58, 55, 34,  2,  0, 39, 36, 56, 49, 31, 30, 29,  9, 50,  1,  4, 18,
       60, 10, 37, 46, 45, 47, 41, 54,  6, 24, 15,  8, 38, 25, 22],
      dtype=int64), 'cur_cost': 114969.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:22,348 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:22,348 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:05:22,348 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([56, 31, 65, 19,  1, 45, 64, 10, 11, 55,  7, 20, 53, 15, 34,  2, 44,
       43,  0,  5, 40, 30,  8, 58, 28, 42,  6, 54, 33, 48, 57, 59, 32, 52,
       49, 63, 18, 29, 47, 16, 21,  4, 41, 35, 61, 50, 13, 46, 36,  3, 62,
       60, 39, 12, 14,  9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38],
      dtype=int64), 'cur_cost': 111160.0}
2025-08-03 16:05:22,348 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 111160.00)
2025-08-03 16:05:22,348 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:05:22,349 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:05:22,349 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,352 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:05:22,352 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,353 - ExplorationExpert - INFO - 探索路径生成完成，成本: 73769.0, 路径长度: 66
2025-08-03 16:05:22,353 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}
2025-08-03 16:05:22,353 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 73769.00)
2025-08-03 16:05:22,353 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:05:22,354 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:05:22,354 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,358 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:22,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12939.0, 路径长度: 66
2025-08-03 16:05:22,358 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}
2025-08-03 16:05:22,358 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12939.00)
2025-08-03 16:05:22,359 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:05:22,359 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,359 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,359 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 94030.0
2025-08-03 16:05:22,416 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:05:22,417 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9565, 9540, 9540, 9538, 9527, 9527, 9527, 9524, 9524]
2025-08-03 16:05:22,417 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:22,422 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,422 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': array([56, 31, 65, 19,  1, 45, 64, 10, 11, 55,  7, 20, 53, 15, 34,  2, 44,
       43,  0,  5, 40, 30,  8, 58, 28, 42,  6, 54, 33, 48, 57, 59, 32, 52,
       49, 63, 18, 29, 47, 16, 21,  4, 41, 35, 61, 50, 13, 46, 36,  3, 62,
       60, 39, 12, 14,  9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38],
      dtype=int64), 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': array([26, 57, 60, 50, 45,  5,  7, 42, 61, 46, 18, 19, 54,  4, 13, 65,  9,
       14, 63, 29,  2, 30, 34, 49, 48, 31, 10, 17, 53,  0, 38, 47, 55, 58,
       22, 20, 43, 59, 11, 23,  6,  3,  1, 12, 33, 16, 62, 21, 39,  8, 64,
       32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37],
      dtype=int64), 'cur_cost': 94030.0}, {'tour': array([44, 22, 52, 42, 50, 21, 59, 10,  6, 24,  4,  9, 61,  3, 38,  8, 57,
       36,  7, 43, 49, 40, 60, 18, 31, 39, 12, 26,  2, 58, 45, 65,  0, 25,
       17, 41, 30, 13, 27, 34, 37, 51, 16, 53, 29, 28, 20, 62, 33, 11, 23,
        1, 47,  5, 56, 32, 46, 14, 55, 48, 19, 54, 15, 63, 64, 35],
      dtype=int64), 'cur_cost': 108766.0}, {'tour': array([41, 15,  6, 60, 49, 54,  7, 11, 22, 59,  1, 56, 20, 10, 43, 33,  4,
       64, 13, 35, 58, 57, 45, 42, 21, 14,  0, 50, 26, 40, 19, 65, 52, 31,
       51, 25, 53,  8, 44, 46, 39, 63, 30, 36, 23, 62,  3,  2, 37, 24, 28,
       16, 61, 48, 18, 27, 34, 29, 55, 32,  9, 47,  5, 12, 38, 17],
      dtype=int64), 'cur_cost': 101621.0}, {'tour': array([21, 27, 64, 34, 41, 31,  9, 48, 12, 25, 37, 59, 13, 52, 20, 32, 51,
        3, 46, 35, 61, 62, 54, 23, 28, 17, 42, 26, 36, 53, 18, 60, 56, 50,
       11,  8, 58,  2, 40, 55, 45,  4, 30,  6, 47, 49, 14, 33, 65, 57, 24,
       43, 16,  0,  7,  1, 19,  5, 44, 38, 39, 22, 63, 10, 15, 29],
      dtype=int64), 'cur_cost': 106861.0}]
2025-08-03 16:05:22,425 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:22,425 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:05:22,425 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([26, 57, 60, 50, 45,  5,  7, 42, 61, 46, 18, 19, 54,  4, 13, 65,  9,
       14, 63, 29,  2, 30, 34, 49, 48, 31, 10, 17, 53,  0, 38, 47, 55, 58,
       22, 20, 43, 59, 11, 23,  6,  3,  1, 12, 33, 16, 62, 21, 39,  8, 64,
       32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37],
      dtype=int64), 'cur_cost': 94030.0}
2025-08-03 16:05:22,426 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 94030.00)
2025-08-03 16:05:22,426 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:05:22,426 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:05:22,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,437 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:22,437 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,437 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60077.0, 路径长度: 66
2025-08-03 16:05:22,437 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}
2025-08-03 16:05:22,437 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 60077.00)
2025-08-03 16:05:22,437 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:05:22,437 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:05:22,437 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,441 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:05:22,441 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,441 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90344.0, 路径长度: 66
2025-08-03 16:05:22,441 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}
2025-08-03 16:05:22,441 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 90344.00)
2025-08-03 16:05:22,441 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:05:22,442 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,442 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,442 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 107527.0
2025-08-03 16:05:22,507 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:05:22,507 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9565, 9540, 9540, 9538, 9527, 9527, 9527, 9524, 9524]
2025-08-03 16:05:22,507 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:22,511 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,511 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}, {'tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': array([56, 31, 65, 19,  1, 45, 64, 10, 11, 55,  7, 20, 53, 15, 34,  2, 44,
       43,  0,  5, 40, 30,  8, 58, 28, 42,  6, 54, 33, 48, 57, 59, 32, 52,
       49, 63, 18, 29, 47, 16, 21,  4, 41, 35, 61, 50, 13, 46, 36,  3, 62,
       60, 39, 12, 14,  9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38],
      dtype=int64), 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': array([26, 57, 60, 50, 45,  5,  7, 42, 61, 46, 18, 19, 54,  4, 13, 65,  9,
       14, 63, 29,  2, 30, 34, 49, 48, 31, 10, 17, 53,  0, 38, 47, 55, 58,
       22, 20, 43, 59, 11, 23,  6,  3,  1, 12, 33, 16, 62, 21, 39,  8, 64,
       32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37],
      dtype=int64), 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': array([61, 39,  7, 11,  4, 36, 33,  9,  0, 59, 47, 53, 45, 26, 32, 16, 29,
       24, 54, 27, 40, 56, 23, 58, 41, 17,  6, 46, 13, 43, 50, 28, 21, 12,
        8, 49, 51, 34,  3, 14,  1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42,
        2, 60, 48, 35, 57, 19,  5, 44, 10, 30, 37, 55, 18, 64, 62],
      dtype=int64), 'cur_cost': 107527.0}]
2025-08-03 16:05:22,514 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:22,514 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:05:22,514 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([61, 39,  7, 11,  4, 36, 33,  9,  0, 59, 47, 53, 45, 26, 32, 16, 29,
       24, 54, 27, 40, 56, 23, 58, 41, 17,  6, 46, 13, 43, 50, 28, 21, 12,
        8, 49, 51, 34,  3, 14,  1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42,
        2, 60, 48, 35, 57, 19,  5, 44, 10, 30, 37, 55, 18, 64, 62],
      dtype=int64), 'cur_cost': 107527.0}
2025-08-03 16:05:22,514 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 107527.00)
2025-08-03 16:05:22,514 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:05:22,514 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:05:22,515 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 13, 12, 22, 23, 16, 18, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12348.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([62, 29, 59, 41, 57,  2, 28, 31, 24, 65, 44, 12, 15, 64, 16, 45, 17,
       51,  5, 40, 50,  0, 26, 54, 48, 60,  1, 13, 25, 32, 58, 36, 42, 52,
       33, 22, 27, 35, 19,  7, 37, 39,  6,  9, 34, 49, 18, 10, 43, 53, 47,
        3, 21, 56, 46,  4, 11, 23, 20, 14, 63,  8, 38, 55, 61, 30],
      dtype=int64), 'cur_cost': 118766.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 18, 59,  8, 33, 48, 15,  5, 32, 58, 62,  2, 45, 35, 43, 49, 51,
       24, 19, 41, 29, 27,  0,  4, 60, 65,  1, 40, 12, 20, 52, 42, 34,  3,
       54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17,  9, 26, 10,
       22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57,  7, 37, 47,  6],
      dtype=int64), 'cur_cost': 102678.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 11, 62, 15, 43, 59, 12, 40,  0,  7, 19, 54, 39, 35, 18, 46, 38,
       56, 64, 55, 31, 17, 42,  2, 33, 10,  6, 24, 61, 32, 60, 21,  5, 25,
        3, 37, 53, 16, 36,  1, 50, 48, 22, 14, 34, 51, 65, 41,  4, 30,  8,
       26, 23, 49,  9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63],
      dtype=int64), 'cur_cost': 115590.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 31, 65, 19,  1, 45, 64, 10, 11, 55,  7, 20, 53, 15, 34,  2, 44,
       43,  0,  5, 40, 30,  8, 58, 28, 42,  6, 54, 33, 48, 57, 59, 32, 52,
       49, 63, 18, 29, 47, 16, 21,  4, 41, 35, 61, 50, 13, 46, 36,  3, 62,
       60, 39, 12, 14,  9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38],
      dtype=int64), 'cur_cost': 111160.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 57, 60, 50, 45,  5,  7, 42, 61, 46, 18, 19, 54,  4, 13, 65,  9,
       14, 63, 29,  2, 30, 34, 49, 48, 31, 10, 17, 53,  0, 38, 47, 55, 58,
       22, 20, 43, 59, 11, 23,  6,  3,  1, 12, 33, 16, 62, 21, 39,  8, 64,
       32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37],
      dtype=int64), 'cur_cost': 94030.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 39,  7, 11,  4, 36, 33,  9,  0, 59, 47, 53, 45, 26, 32, 16, 29,
       24, 54, 27, 40, 56, 23, 58, 41, 17,  6, 46, 13, 43, 50, 28, 21, 12,
        8, 49, 51, 34,  3, 14,  1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42,
        2, 60, 48, 35, 57, 19,  5, 44, 10, 30, 37, 55, 18, 64, 62],
      dtype=int64), 'cur_cost': 107527.0}}]
2025-08-03 16:05:22,515 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:05:22,515 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:05:22,526 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12339.000, 多样性=0.952
2025-08-03 16:05:22,526 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:05:22,527 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:05:22,527 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:05:22,528 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.0028935203745250673, 'best_improvement': -0.22739480752014324}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.010111893907998354}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9524, 'new_best_cost': 9524, 'quality_improvement': 0.0, 'old_diversity': 0.8363636363636364, 'new_diversity': 0.8363636363636364, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:05:22,529 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:05:22,529 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-03 16:05:22,529 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-03 16:05:22,530 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:05:22,531 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12339.000, 多样性=0.952
2025-08-03 16:05:22,531 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:05:22,536 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.952
2025-08-03 16:05:22,536 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:05:22,541 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.836
2025-08-03 16:05:22,543 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-03 16:05:22,543 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:05:22,543 - LandscapeExpert - INFO - 添加精英解数据: 11个精英解
2025-08-03 16:05:22,543 - LandscapeExpert - INFO - 数据提取成功: 31个路径, 31个适应度值
2025-08-03 16:05:22,698 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.097, 适应度梯度: -11644.968, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:05:22,699 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-03 16:05:22,699 - LandscapeExpert - INFO - 提取到 11 个精英解
2025-08-03 16:05:22,761 - visualization.landscape_visualizer - INFO - 已添加 11 个精英解标记
2025-08-03 16:05:22,822 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250803_160522.html
2025-08-03 16:05:22,861 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250803_160522.html
2025-08-03 16:05:22,861 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-03 16:05:22,861 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-03 16:05:22,862 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3191秒
2025-08-03 16:05:22,862 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.0967741935483871, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -11644.967741935487, 'local_optima_density': 0.0967741935483871, 'gradient_variance': 759956707.3912175, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0051, 'fitness_entropy': 0.745834093833261, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -11644.968)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208322.6997921, 'performance_metrics': {}}}
2025-08-03 16:05:22,862 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:05:22,862 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 1)
2025-08-03 16:05:22,862 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 1, 种群大小: 20)
2025-08-03 16:05:22,862 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:22,862 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:05:22,862 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:05:22,863 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:22,863 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:05:22,863 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:05:22,863 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:05:22,863 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:05:22,863 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 0, 11, 6} (总数: 4, 保护比例: 0.20)
2025-08-03 16:05:22,863 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:05:22,863 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:05:22,864 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,867 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:22,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12855.0, 路径长度: 66
2025-08-03 16:05:22,867 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}
2025-08-03 16:05:22,867 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12855.00)
2025-08-03 16:05:22,867 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:05:22,867 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,868 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,868 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 102118.0
2025-08-03 16:05:22,934 - ExploitationExpert - INFO - res_population_num: 12
2025-08-03 16:05:22,934 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521]
2025-08-03 16:05:22,934 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:22,938 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:22,938 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [0, 19, 16, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12864.0}, {'tour': [0, 11, 22, 20, 21, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12341.0}, {'tour': array([36, 15, 11, 60, 24, 31, 44, 40, 35,  7, 52, 12, 13, 48,  6, 42, 33,
       37,  0,  5,  3, 56, 41, 55, 28, 29, 22, 54, 43, 61, 62, 65, 26, 25,
       63, 32, 59, 57, 38, 17,  4,  2, 53, 47, 34, 45, 49, 39, 30, 50, 18,
       64, 16, 14,  1, 51, 19, 21, 10, 58, 20, 46,  9, 27, 23,  8],
      dtype=int64), 'cur_cost': 99814.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': [16, 18, 59, 8, 33, 48, 15, 5, 32, 58, 62, 2, 45, 35, 43, 49, 51, 24, 19, 41, 29, 27, 0, 4, 60, 65, 1, 40, 12, 20, 52, 42, 34, 3, 54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17, 9, 26, 10, 22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57, 7, 37, 47, 6], 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': [52, 11, 62, 15, 43, 59, 12, 40, 0, 7, 19, 54, 39, 35, 18, 46, 38, 56, 64, 55, 31, 17, 42, 2, 33, 10, 6, 24, 61, 32, 60, 21, 5, 25, 3, 37, 53, 16, 36, 1, 50, 48, 22, 14, 34, 51, 65, 41, 4, 30, 8, 26, 23, 49, 9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63], 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': [56, 31, 65, 19, 1, 45, 64, 10, 11, 55, 7, 20, 53, 15, 34, 2, 44, 43, 0, 5, 40, 30, 8, 58, 28, 42, 6, 54, 33, 48, 57, 59, 32, 52, 49, 63, 18, 29, 47, 16, 21, 4, 41, 35, 61, 50, 13, 46, 36, 3, 62, 60, 39, 12, 14, 9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38], 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': [26, 57, 60, 50, 45, 5, 7, 42, 61, 46, 18, 19, 54, 4, 13, 65, 9, 14, 63, 29, 2, 30, 34, 49, 48, 31, 10, 17, 53, 0, 38, 47, 55, 58, 22, 20, 43, 59, 11, 23, 6, 3, 1, 12, 33, 16, 62, 21, 39, 8, 64, 32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37], 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:22,940 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:22,940 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-03 16:05:22,940 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}
2025-08-03 16:05:22,940 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 102118.00)
2025-08-03 16:05:22,940 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:05:22,940 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:05:22,940 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,949 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:22,949 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68993.0, 路径长度: 66
2025-08-03 16:05:22,949 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}
2025-08-03 16:05:22,950 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 68993.00)
2025-08-03 16:05:22,950 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:05:22,950 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:05:22,950 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:22,959 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:22,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:22,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54069.0, 路径长度: 66
2025-08-03 16:05:22,960 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}
2025-08-03 16:05:22,960 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 54069.00)
2025-08-03 16:05:22,960 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:05:22,960 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:22,960 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:22,961 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 118466.0
2025-08-03 16:05:23,014 - ExploitationExpert - INFO - res_population_num: 14
2025-08-03 16:05:23,015 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521]
2025-08-03 16:05:23,015 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:23,022 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,022 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [19, 33, 7, 23, 22, 36, 3, 11, 8, 4, 17, 29, 35, 32, 26, 34, 2, 64, 10, 61, 62, 53, 0, 20, 31, 15, 1, 5, 57, 9, 63, 18, 49, 50, 14, 6, 55, 12, 21, 25, 24, 16, 13, 37, 39, 51, 42, 41, 43, 38, 44, 40, 45, 48, 30, 56, 58, 54, 60, 65, 47, 27, 46, 28, 59, 52], 'cur_cost': 59420.0}, {'tour': [0, 4, 21, 6, 2, 8, 5, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12820.0}, {'tour': [16, 18, 59, 8, 33, 48, 15, 5, 32, 58, 62, 2, 45, 35, 43, 49, 51, 24, 19, 41, 29, 27, 0, 4, 60, 65, 1, 40, 12, 20, 52, 42, 34, 3, 54, 38, 55, 28, 13, 23, 39, 36, 61, 53, 14, 30, 46, 17, 9, 26, 10, 22, 50, 64, 31, 25, 44, 56, 63, 21, 11, 57, 7, 37, 47, 6], 'cur_cost': 102678.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': [52, 11, 62, 15, 43, 59, 12, 40, 0, 7, 19, 54, 39, 35, 18, 46, 38, 56, 64, 55, 31, 17, 42, 2, 33, 10, 6, 24, 61, 32, 60, 21, 5, 25, 3, 37, 53, 16, 36, 1, 50, 48, 22, 14, 34, 51, 65, 41, 4, 30, 8, 26, 23, 49, 9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63], 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': [56, 31, 65, 19, 1, 45, 64, 10, 11, 55, 7, 20, 53, 15, 34, 2, 44, 43, 0, 5, 40, 30, 8, 58, 28, 42, 6, 54, 33, 48, 57, 59, 32, 52, 49, 63, 18, 29, 47, 16, 21, 4, 41, 35, 61, 50, 13, 46, 36, 3, 62, 60, 39, 12, 14, 9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38], 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': [26, 57, 60, 50, 45, 5, 7, 42, 61, 46, 18, 19, 54, 4, 13, 65, 9, 14, 63, 29, 2, 30, 34, 49, 48, 31, 10, 17, 53, 0, 38, 47, 55, 58, 22, 20, 43, 59, 11, 23, 6, 3, 1, 12, 33, 16, 62, 21, 39, 8, 64, 32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37], 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:23,023 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-08-03 16:05:23,024 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-03 16:05:23,024 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}
2025-08-03 16:05:23,024 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 118466.00)
2025-08-03 16:05:23,024 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:05:23,024 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:05:23,024 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,027 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:05:23,027 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,028 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107003.0, 路径长度: 66
2025-08-03 16:05:23,028 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}
2025-08-03 16:05:23,028 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 107003.00)
2025-08-03 16:05:23,028 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:05:23,028 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:05:23,028 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,031 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:05:23,031 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,031 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110837.0, 路径长度: 66
2025-08-03 16:05:23,031 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}
2025-08-03 16:05:23,031 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 110837.00)
2025-08-03 16:05:23,031 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:05:23,031 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:23,031 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:23,032 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 110375.0
2025-08-03 16:05:23,098 - ExploitationExpert - INFO - res_population_num: 16
2025-08-03 16:05:23,098 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:05:23,098 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:23,104 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,104 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}, {'tour': [21, 6, 60, 61, 15, 34, 19, 24, 22, 35, 36, 20, 11, 54, 53, 2, 1, 63, 52, 0, 23, 29, 7, 14, 10, 5, 16, 9, 17, 12, 49, 39, 27, 31, 43, 51, 41, 45, 38, 47, 37, 33, 40, 44, 30, 4, 58, 8, 59, 3, 57, 55, 48, 26, 25, 28, 18, 46, 50, 13, 42, 56, 64, 62, 65, 32], 'cur_cost': 65045.0}, {'tour': [0, 16, 8, 7, 3, 9, 11, 1, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12836.0}, {'tour': [52, 11, 62, 15, 43, 59, 12, 40, 0, 7, 19, 54, 39, 35, 18, 46, 38, 56, 64, 55, 31, 17, 42, 2, 33, 10, 6, 24, 61, 32, 60, 21, 5, 25, 3, 37, 53, 16, 36, 1, 50, 48, 22, 14, 34, 51, 65, 41, 4, 30, 8, 26, 23, 49, 9, 47, 20, 29, 44, 13, 57, 27, 45, 28, 58, 63], 'cur_cost': 115590.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': [56, 31, 65, 19, 1, 45, 64, 10, 11, 55, 7, 20, 53, 15, 34, 2, 44, 43, 0, 5, 40, 30, 8, 58, 28, 42, 6, 54, 33, 48, 57, 59, 32, 52, 49, 63, 18, 29, 47, 16, 21, 4, 41, 35, 61, 50, 13, 46, 36, 3, 62, 60, 39, 12, 14, 9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38], 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': [26, 57, 60, 50, 45, 5, 7, 42, 61, 46, 18, 19, 54, 4, 13, 65, 9, 14, 63, 29, 2, 30, 34, 49, 48, 31, 10, 17, 53, 0, 38, 47, 55, 58, 22, 20, 43, 59, 11, 23, 6, 3, 1, 12, 33, 16, 62, 21, 39, 8, 64, 32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37], 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:23,106 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-08-03 16:05:23,106 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-03 16:05:23,106 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}
2025-08-03 16:05:23,106 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 110375.00)
2025-08-03 16:05:23,106 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:05:23,106 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:05:23,106 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,109 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:23,110 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,110 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12427.0, 路径长度: 66
2025-08-03 16:05:23,110 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}
2025-08-03 16:05:23,110 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12427.00)
2025-08-03 16:05:23,110 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:05:23,110 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:05:23,110 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,113 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:23,113 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,113 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12409.0, 路径长度: 66
2025-08-03 16:05:23,113 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}
2025-08-03 16:05:23,114 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12409.00)
2025-08-03 16:05:23,114 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:05:23,114 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:23,114 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:23,114 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 110451.0
2025-08-03 16:05:23,184 - ExploitationExpert - INFO - res_population_num: 18
2025-08-03 16:05:23,184 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:05:23,184 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:23,192 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,192 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}, {'tour': [0, 12, 15, 18, 16, 23, 22, 17, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12339.0}, {'tour': [23, 21, 36, 16, 9, 17, 3, 35, 34, 1, 61, 60, 7, 10, 32, 57, 22, 12, 49, 18, 47, 44, 59, 38, 13, 30, 58, 62, 33, 24, 43, 2, 65, 55, 46, 5, 64, 0, 29, 51, 4, 11, 56, 40, 27, 25, 15, 28, 48, 41, 26, 52, 54, 19, 42, 8, 6, 20, 63, 45, 53, 31, 14, 37, 50, 39], 'cur_cost': 97235.0}, {'tour': [56, 31, 65, 19, 1, 45, 64, 10, 11, 55, 7, 20, 53, 15, 34, 2, 44, 43, 0, 5, 40, 30, 8, 58, 28, 42, 6, 54, 33, 48, 57, 59, 32, 52, 49, 63, 18, 29, 47, 16, 21, 4, 41, 35, 61, 50, 13, 46, 36, 3, 62, 60, 39, 12, 14, 9, 24, 51, 23, 37, 26, 27, 25, 17, 22, 38], 'cur_cost': 111160.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': [26, 57, 60, 50, 45, 5, 7, 42, 61, 46, 18, 19, 54, 4, 13, 65, 9, 14, 63, 29, 2, 30, 34, 49, 48, 31, 10, 17, 53, 0, 38, 47, 55, 58, 22, 20, 43, 59, 11, 23, 6, 3, 1, 12, 33, 16, 62, 21, 39, 8, 64, 32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37], 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:23,194 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:05:23,194 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-03 16:05:23,194 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}
2025-08-03 16:05:23,194 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 110451.00)
2025-08-03 16:05:23,194 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:05:23,194 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:05:23,194 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,204 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:23,204 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,204 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49824.0, 路径长度: 66
2025-08-03 16:05:23,204 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}
2025-08-03 16:05:23,204 - experts.management.collaboration_manager - INFO - 个体 11 保留原路径 (成本: 49824.00)
2025-08-03 16:05:23,204 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:05:23,204 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:05:23,204 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,207 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:23,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,208 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12801.0, 路径长度: 66
2025-08-03 16:05:23,208 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}
2025-08-03 16:05:23,208 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12801.00)
2025-08-03 16:05:23,208 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:05:23,208 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:23,208 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:23,208 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 111211.0
2025-08-03 16:05:23,280 - ExploitationExpert - INFO - res_population_num: 18
2025-08-03 16:05:23,281 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:05:23,281 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:05:23,288 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,288 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': array([63, 30, 21, 37, 38, 51,  4, 58, 36, 65, 22, 11, 34, 54,  5,  6, 52,
       19,  2, 41,  0, 39, 35, 44, 24, 62, 25, 49, 46,  7, 45, 42, 16, 61,
        9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48,  1, 31, 32,  8, 47,
       15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29,  3, 64, 56, 18],
      dtype=int64), 'cur_cost': 111211.0}, {'tour': [9, 4, 13, 23, 6, 8, 3, 15, 21, 27, 55, 53, 63, 52, 61, 57, 54, 5, 16, 62, 1, 12, 17, 18, 14, 45, 51, 40, 44, 30, 48, 38, 26, 19, 28, 37, 34, 32, 33, 20, 56, 2, 35, 47, 41, 11, 7, 31, 10, 22, 46, 24, 49, 60, 36, 64, 50, 25, 42, 39, 43, 0, 59, 58, 29, 65], 'cur_cost': 73769.0}, {'tour': [0, 19, 10, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12939.0}, {'tour': [26, 57, 60, 50, 45, 5, 7, 42, 61, 46, 18, 19, 54, 4, 13, 65, 9, 14, 63, 29, 2, 30, 34, 49, 48, 31, 10, 17, 53, 0, 38, 47, 55, 58, 22, 20, 43, 59, 11, 23, 6, 3, 1, 12, 33, 16, 62, 21, 39, 8, 64, 32, 28, 15, 41, 51, 25, 27, 36, 44, 24, 40, 56, 52, 35, 37], 'cur_cost': 94030.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:23,291 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:05:23,291 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-03 16:05:23,291 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([63, 30, 21, 37, 38, 51,  4, 58, 36, 65, 22, 11, 34, 54,  5,  6, 52,
       19,  2, 41,  0, 39, 35, 44, 24, 62, 25, 49, 46,  7, 45, 42, 16, 61,
        9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48,  1, 31, 32,  8, 47,
       15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29,  3, 64, 56, 18],
      dtype=int64), 'cur_cost': 111211.0}
2025-08-03 16:05:23,291 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 111211.00)
2025-08-03 16:05:23,291 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:05:23,291 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:05:23,291 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,301 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:23,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49365.0, 路径长度: 66
2025-08-03 16:05:23,302 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}
2025-08-03 16:05:23,302 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 49365.00)
2025-08-03 16:05:23,302 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:05:23,302 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:05:23,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,305 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:23,306 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,306 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12892.0, 路径长度: 66
2025-08-03 16:05:23,306 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}
2025-08-03 16:05:23,306 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12892.00)
2025-08-03 16:05:23,306 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:05:23,306 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:23,306 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:23,307 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 97967.0
2025-08-03 16:05:23,403 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 16:05:23,403 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:05:23,403 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:23,409 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,409 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': array([63, 30, 21, 37, 38, 51,  4, 58, 36, 65, 22, 11, 34, 54,  5,  6, 52,
       19,  2, 41,  0, 39, 35, 44, 24, 62, 25, 49, 46,  7, 45, 42, 16, 61,
        9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48,  1, 31, 32,  8, 47,
       15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29,  3, 64, 56, 18],
      dtype=int64), 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': array([34, 53, 11, 56, 37, 55, 38, 23,  3, 62, 31, 59, 51, 10, 18,  2, 65,
       64, 54, 16, 24, 15, 14, 44, 41, 52, 63,  0,  7,  5, 61, 22, 12, 33,
       20, 40, 29,  1, 21, 17,  6, 50, 42, 30, 35,  9, 47,  4, 26, 28, 13,
       43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58,  8, 27],
      dtype=int64), 'cur_cost': 97967.0}, {'tour': [37, 5, 65, 52, 55, 62, 15, 36, 31, 14, 24, 26, 25, 20, 19, 27, 3, 59, 56, 17, 23, 2, 22, 30, 16, 32, 1, 11, 13, 35, 33, 29, 49, 43, 47, 41, 28, 46, 18, 6, 4, 57, 58, 60, 10, 7, 61, 9, 54, 64, 0, 40, 39, 38, 45, 44, 50, 21, 34, 12, 8, 63, 48, 42, 51, 53], 'cur_cost': 60077.0}, {'tour': [9, 16, 13, 18, 12, 14, 15, 21, 53, 1, 62, 59, 56, 60, 54, 52, 22, 11, 6, 0, 40, 4, 8, 38, 48, 35, 28, 58, 34, 65, 32, 31, 51, 29, 2, 50, 3, 55, 23, 57, 19, 36, 20, 30, 33, 49, 64, 27, 39, 47, 43, 42, 24, 10, 17, 7, 5, 44, 46, 25, 26, 61, 45, 63, 37, 41], 'cur_cost': 90344.0}, {'tour': [61, 39, 7, 11, 4, 36, 33, 9, 0, 59, 47, 53, 45, 26, 32, 16, 29, 24, 54, 27, 40, 56, 23, 58, 41, 17, 6, 46, 13, 43, 50, 28, 21, 12, 8, 49, 51, 34, 3, 14, 1, 31, 22, 20, 65, 38, 52, 63, 15, 25, 42, 2, 60, 48, 35, 57, 19, 5, 44, 10, 30, 37, 55, 18, 64, 62], 'cur_cost': 107527.0}]
2025-08-03 16:05:23,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:05:23,412 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-03 16:05:23,412 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([34, 53, 11, 56, 37, 55, 38, 23,  3, 62, 31, 59, 51, 10, 18,  2, 65,
       64, 54, 16, 24, 15, 14, 44, 41, 52, 63,  0,  7,  5, 61, 22, 12, 33,
       20, 40, 29,  1, 21, 17,  6, 50, 42, 30, 35,  9, 47,  4, 26, 28, 13,
       43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58,  8, 27],
      dtype=int64), 'cur_cost': 97967.0}
2025-08-03 16:05:23,412 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 97967.00)
2025-08-03 16:05:23,413 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:05:23,413 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:05:23,413 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,424 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:05:23,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64015.0, 路径长度: 66
2025-08-03 16:05:23,425 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}
2025-08-03 16:05:23,426 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 64015.00)
2025-08-03 16:05:23,426 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:05:23,426 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:05:23,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:05:23,429 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:05:23,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:05:23,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12729.0, 路径长度: 66
2025-08-03 16:05:23,430 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}
2025-08-03 16:05:23,430 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12729.00)
2025-08-03 16:05:23,430 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:05:23,430 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:05:23,430 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:05:23,431 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 103807.0
2025-08-03 16:05:23,510 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 16:05:23,510 - ExploitationExpert - INFO - res_population_costs: [9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 16:05:23,511 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:05:23,524 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:05:23,524 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}, {'tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': array([63, 30, 21, 37, 38, 51,  4, 58, 36, 65, 22, 11, 34, 54,  5,  6, 52,
       19,  2, 41,  0, 39, 35, 44, 24, 62, 25, 49, 46,  7, 45, 42, 16, 61,
        9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48,  1, 31, 32,  8, 47,
       15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29,  3, 64, 56, 18],
      dtype=int64), 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': array([34, 53, 11, 56, 37, 55, 38, 23,  3, 62, 31, 59, 51, 10, 18,  2, 65,
       64, 54, 16, 24, 15, 14, 44, 41, 52, 63,  0,  7,  5, 61, 22, 12, 33,
       20, 40, 29,  1, 21, 17,  6, 50, 42, 30, 35,  9, 47,  4, 26, 28, 13,
       43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58,  8, 27],
      dtype=int64), 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': array([52, 12, 17, 60,  2, 31, 33, 58, 64,  4, 19, 35, 36, 18,  1, 14, 44,
       65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53,
        0, 54, 40, 61,  6, 28,  9, 62, 39, 41, 59, 48, 21, 15, 42,  3, 38,
       46, 27, 47, 55, 22,  7,  8, 49, 13, 32, 23,  5, 30, 29, 10],
      dtype=int64), 'cur_cost': 103807.0}]
2025-08-03 16:05:23,527 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:05:23,527 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-03 16:05:23,527 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([52, 12, 17, 60,  2, 31, 33, 58, 64,  4, 19, 35, 36, 18,  1, 14, 44,
       65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53,
        0, 54, 40, 61,  6, 28,  9, 62, 39, 41, 59, 48, 21, 15, 42,  3, 38,
       46, 27, 47, 55, 22,  7,  8, 49, 13, 32, 23,  5, 30, 29, 10],
      dtype=int64), 'cur_cost': 103807.0}
2025-08-03 16:05:23,528 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 103807.00)
2025-08-03 16:05:23,528 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:05:23,528 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:05:23,529 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 17, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 19, 16, 23, 22, 12, 15, 14, 20, 21, 13, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12855.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 35, 14, 40, 33,  5,  2, 48,  3, 51, 39, 15, 20, 29,  4,  1, 47,
       50, 22, 46, 32, 25, 64, 31, 36, 34, 52, 59, 26, 23, 43, 57, 42, 65,
       56, 38, 55, 54, 19, 53, 63, 62, 41, 16, 44, 13, 45, 18, 12, 27,  0,
       24, 17, 10, 28, 49, 11, 58,  7, 21, 60, 37,  8, 30,  9, 61],
      dtype=int64), 'cur_cost': 102118.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([46, 30, 55, 34, 52, 26, 17, 16, 37,  1, 61,  0, 18, 39, 48, 58, 33,
        9,  6,  7, 51, 27,  4,  2, 50, 53, 44, 14, 63, 32,  8, 65, 62, 21,
       54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13,  3, 15, 41, 29, 59,
       36, 40, 12, 22, 49,  5, 38, 42, 35, 43, 11, 56, 23, 31, 64],
      dtype=int64), 'cur_cost': 118466.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36,  4,  9, 50, 16, 45,
        5,  8,  0, 41, 30, 37, 19, 15, 10, 52,  6, 53,  3, 51, 65, 43, 21,
       60, 25, 58, 42, 57,  7, 56, 35,  2, 12, 11, 55,  1, 46, 32, 20, 31,
       61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29],
      dtype=int64), 'cur_cost': 110375.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 61, 53, 19, 41, 20, 42, 57, 14,  4, 62, 15, 51, 56, 26,  9, 31,
       21, 18, 25, 46, 27, 22, 44, 43, 49,  2, 63, 35, 23, 47, 40,  1, 12,
       55, 48, 10, 65,  0, 29, 11,  5, 13, 28, 38,  6, 54, 30, 34, 45, 36,
       50, 59, 39, 37, 58,  3, 16, 64,  8, 17, 24, 52, 60, 32,  7],
      dtype=int64), 'cur_cost': 110451.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 30, 21, 37, 38, 51,  4, 58, 36, 65, 22, 11, 34, 54,  5,  6, 52,
       19,  2, 41,  0, 39, 35, 44, 24, 62, 25, 49, 46,  7, 45, 42, 16, 61,
        9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48,  1, 31, 32,  8, 47,
       15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29,  3, 64, 56, 18],
      dtype=int64), 'cur_cost': 111211.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 53, 11, 56, 37, 55, 38, 23,  3, 62, 31, 59, 51, 10, 18,  2, 65,
       64, 54, 16, 24, 15, 14, 44, 41, 52, 63,  0,  7,  5, 61, 22, 12, 33,
       20, 40, 29,  1, 21, 17,  6, 50, 42, 30, 35,  9, 47,  4, 26, 28, 13,
       43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58,  8, 27],
      dtype=int64), 'cur_cost': 97967.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 12, 17, 60,  2, 31, 33, 58, 64,  4, 19, 35, 36, 18,  1, 14, 44,
       65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53,
        0, 54, 40, 61,  6, 28,  9, 62, 39, 41, 59, 48, 21, 15, 42,  3, 38,
       46, 27, 47, 55, 22,  7,  8, 49, 13, 32, 23,  5, 30, 29, 10],
      dtype=int64), 'cur_cost': 103807.0}}]
2025-08-03 16:05:23,529 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:05:23,529 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:05:23,539 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12409.000, 多样性=0.966
2025-08-03 16:05:23,540 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-03 16:05:23,540 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-03 16:05:23,540 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:05:23,543 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.014204350289287583, 'best_improvement': -0.005673069130399546}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01415054843841577}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 19, 'new_count': 19, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7500443026758816, 'new_diversity': 0.7500443026758816, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:05:23,547 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-03 16:05:23,547 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-03 16:05:23,547 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-03 16:05:23,547 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:05:23,548 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12409.000, 多样性=0.966
2025-08-03 16:05:23,548 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:05:23,555 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-03 16:05:23,556 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:05:23,568 - EliteExpert - INFO - 精英解分析完成: 精英解数量=19, 多样性=0.750
2025-08-03 16:05:23,570 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-03 16:05:23,570 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:05:23,570 - LandscapeExpert - INFO - 添加精英解数据: 19个精英解
2025-08-03 16:05:23,570 - LandscapeExpert - INFO - 数据提取成功: 39个路径, 39个适应度值
2025-08-03 16:05:23,825 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.205, 适应度梯度: -13925.179, 聚类评分: 0.000, 覆盖率: 0.008, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:05:23,825 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-03 16:05:23,825 - LandscapeExpert - INFO - 提取到 19 个精英解
2025-08-03 16:05:23,897 - visualization.landscape_visualizer - INFO - 已添加 19 个精英解标记
2025-08-03 16:05:23,965 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250803_160523.html
2025-08-03 16:05:24,005 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250803_160523.html
2025-08-03 16:05:24,006 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-03 16:05:24,006 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-03 16:05:24,006 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.4364秒
2025-08-03 16:05:24,006 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.20512820512820512, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13925.179487179494, 'local_optima_density': 0.20512820512820512, 'gradient_variance': 769260985.7431692, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0079, 'fitness_entropy': 0.6588861272553949, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13925.179)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.008)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208323.825271, 'performance_metrics': {}}}
2025-08-03 16:05:24,006 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:05:24,006 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 2)
2025-08-03 16:05:24,006 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 2, 种群大小: 20)
2025-08-03 16:05:24,013 - experts.analysis.individual_state_analyzer - INFO - 种群状态分析完成，分析了 20 个个体
2025-08-03 16:05:24,013 - experts.strategy.enhanced_strategy_expert - INFO - 使用LLM进行策略选择
2025-08-03 16:05:24,013 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:05:34,221 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:05:36,222 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:05:46,422 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:05:48,424 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:05:58,920 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:05:58,921 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 1): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:05:58,922 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:05:58,924 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:05:58,924 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:05:58,925 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:05:58,925 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:06:09,137 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:06:11,137 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:06:21,328 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:06:23,329 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:06:33,550 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:06:33,551 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 2): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:06:33,551 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:06:33,551 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:06:33,551 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:06:33,552 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:06:33,552 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:06:44,792 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:06:46,793 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:06:58,042 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:00,043 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:07:10,212 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:10,212 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 3): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:10,213 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:07:10,213 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:07:10,214 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:07:10,215 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:07:10,216 - experts.strategy.enhanced_strategy_expert - WARNING - LLM调用尝试 3 失败: 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:07:10,216 - experts.strategy.enhanced_strategy_expert - ERROR - LLM策略选择过程中发生错误: 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:07:10,217 - experts.strategy.enhanced_strategy_expert - INFO - 使用算法进行策略选择
2025-08-03 16:07:10,217 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: <StagnationLevel.NONE: 'none'>
2025-08-03 16:07:10,217 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:07:10,217 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:07:10,217 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: <StagnationLevel.NONE: 'none'>
2025-08-03 16:07:10,217 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:07:10,218 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:07:10,218 - __main__ - INFO - 策略分配完整报告: 错误回退: <StagnationLevel.NONE: 'none'>
2025-08-03 16:07:10,219 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:07:10,219 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 9, 18, 12} (总数: 4, 保护比例: 0.20)
2025-08-03 16:07:10,219 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:07:10,219 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:07:10,219 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,227 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61296.0, 路径长度: 66
2025-08-03 16:07:10,227 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}
2025-08-03 16:07:10,228 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 61296.00)
2025-08-03 16:07:10,228 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:07:10,228 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,228 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,228 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108535.0
2025-08-03 16:07:10,302 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 16:07:10,302 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:07:10,302 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:07:10,308 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,309 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [59, 15, 1, 23, 31, 32, 3, 62, 0, 6, 63, 11, 22, 16, 5, 58, 7, 57, 14, 19, 37, 30, 12, 40, 20, 35, 10, 55, 64, 65, 39, 51, 44, 47, 38, 36, 17, 21, 33, 28, 4, 54, 2, 53, 48, 34, 29, 18, 9, 13, 27, 8, 26, 25, 43, 45, 46, 49, 41, 42, 56, 60, 61, 52, 50, 24], 'cur_cost': 68993.0}, {'tour': [30, 31, 16, 9, 4, 11, 23, 20, 35, 22, 19, 17, 24, 29, 3, 63, 13, 21, 7, 55, 57, 5, 0, 2, 12, 36, 34, 1, 14, 39, 50, 51, 25, 28, 48, 49, 43, 15, 33, 42, 46, 18, 44, 38, 32, 26, 6, 56, 53, 62, 65, 59, 10, 8, 61, 52, 64, 40, 41, 47, 45, 37, 27, 58, 60, 54], 'cur_cost': 54069.0}, {'tour': [46, 30, 55, 34, 52, 26, 17, 16, 37, 1, 61, 0, 18, 39, 48, 58, 33, 9, 6, 7, 51, 27, 4, 2, 50, 53, 44, 14, 63, 32, 8, 65, 62, 21, 54, 28, 47, 25, 45, 19, 57, 20, 24, 10, 60, 13, 3, 15, 41, 29, 59, 36, 40, 12, 22, 49, 5, 38, 42, 35, 43, 11, 56, 23, 31, 64], 'cur_cost': 118466.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': [14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36, 4, 9, 50, 16, 45, 5, 8, 0, 41, 30, 37, 19, 15, 10, 52, 6, 53, 3, 51, 65, 43, 21, 60, 25, 58, 42, 57, 7, 56, 35, 2, 12, 11, 55, 1, 46, 32, 20, 31, 61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29], 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': [33, 61, 53, 19, 41, 20, 42, 57, 14, 4, 62, 15, 51, 56, 26, 9, 31, 21, 18, 25, 46, 27, 22, 44, 43, 49, 2, 63, 35, 23, 47, 40, 1, 12, 55, 48, 10, 65, 0, 29, 11, 5, 13, 28, 38, 6, 54, 30, 34, 45, 36, 50, 59, 39, 37, 58, 3, 16, 64, 8, 17, 24, 52, 60, 32, 7], 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': [63, 30, 21, 37, 38, 51, 4, 58, 36, 65, 22, 11, 34, 54, 5, 6, 52, 19, 2, 41, 0, 39, 35, 44, 24, 62, 25, 49, 46, 7, 45, 42, 16, 61, 9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48, 1, 31, 32, 8, 47, 15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29, 3, 64, 56, 18], 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': [34, 53, 11, 56, 37, 55, 38, 23, 3, 62, 31, 59, 51, 10, 18, 2, 65, 64, 54, 16, 24, 15, 14, 44, 41, 52, 63, 0, 7, 5, 61, 22, 12, 33, 20, 40, 29, 1, 21, 17, 6, 50, 42, 30, 35, 9, 47, 4, 26, 28, 13, 43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58, 8, 27], 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,309 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:07:10,309 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-03 16:07:10,310 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}
2025-08-03 16:07:10,310 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 108535.00)
2025-08-03 16:07:10,310 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:07:10,310 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:07:10,310 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,313 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:07:10,314 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,316 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96273.0, 路径长度: 66
2025-08-03 16:07:10,316 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}
2025-08-03 16:07:10,317 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 96273.00)
2025-08-03 16:07:10,317 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:07:10,317 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:07:10,317 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,321 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:07:10,321 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14699.0, 路径长度: 66
2025-08-03 16:07:10,321 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}
2025-08-03 16:07:10,322 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 14699.00)
2025-08-03 16:07:10,322 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:07:10,322 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,322 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,322 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 100147.0
2025-08-03 16:07:10,411 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 16:07:10,412 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:07:10,412 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:07:10,423 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,424 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [17, 24, 3, 32, 23, 11, 14, 58, 63, 16, 26, 34, 13, 57, 7, 61, 5, 40, 12, 15, 44, 9, 18, 47, 36, 21, 39, 48, 2, 38, 4, 29, 46, 65, 22, 10, 25, 19, 50, 62, 31, 55, 37, 1, 33, 45, 30, 8, 6, 64, 52, 51, 20, 49, 42, 60, 59, 41, 56, 53, 27, 28, 0, 54, 43, 35], 'cur_cost': 107003.0}, {'tour': [17, 16, 2, 48, 5, 58, 63, 33, 24, 38, 57, 23, 37, 9, 12, 47, 13, 55, 7, 42, 3, 36, 41, 28, 46, 54, 44, 34, 29, 31, 6, 65, 59, 20, 64, 43, 45, 49, 52, 21, 0, 61, 18, 32, 14, 10, 30, 19, 39, 53, 35, 62, 4, 15, 60, 50, 26, 11, 56, 25, 22, 51, 8, 1, 40, 27], 'cur_cost': 110837.0}, {'tour': [14, 17, 18, 64, 22, 39, 13, 33, 26, 24, 40, 36, 4, 9, 50, 16, 45, 5, 8, 0, 41, 30, 37, 19, 15, 10, 52, 6, 53, 3, 51, 65, 43, 21, 60, 25, 58, 42, 57, 7, 56, 35, 2, 12, 11, 55, 1, 46, 32, 20, 31, 61, 23, 38, 27, 34, 54, 44, 59, 48, 49, 28, 63, 47, 62, 29], 'cur_cost': 110375.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': [33, 61, 53, 19, 41, 20, 42, 57, 14, 4, 62, 15, 51, 56, 26, 9, 31, 21, 18, 25, 46, 27, 22, 44, 43, 49, 2, 63, 35, 23, 47, 40, 1, 12, 55, 48, 10, 65, 0, 29, 11, 5, 13, 28, 38, 6, 54, 30, 34, 45, 36, 50, 59, 39, 37, 58, 3, 16, 64, 8, 17, 24, 52, 60, 32, 7], 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': [63, 30, 21, 37, 38, 51, 4, 58, 36, 65, 22, 11, 34, 54, 5, 6, 52, 19, 2, 41, 0, 39, 35, 44, 24, 62, 25, 49, 46, 7, 45, 42, 16, 61, 9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48, 1, 31, 32, 8, 47, 15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29, 3, 64, 56, 18], 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': [34, 53, 11, 56, 37, 55, 38, 23, 3, 62, 31, 59, 51, 10, 18, 2, 65, 64, 54, 16, 24, 15, 14, 44, 41, 52, 63, 0, 7, 5, 61, 22, 12, 33, 20, 40, 29, 1, 21, 17, 6, 50, 42, 30, 35, 9, 47, 4, 26, 28, 13, 43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58, 8, 27], 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,425 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:07:10,425 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-03 16:07:10,426 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}
2025-08-03 16:07:10,426 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 100147.00)
2025-08-03 16:07:10,426 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:07:10,426 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:07:10,426 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,435 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,435 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,436 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56879.0, 路径长度: 66
2025-08-03 16:07:10,436 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}
2025-08-03 16:07:10,436 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 56879.00)
2025-08-03 16:07:10,436 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:07:10,436 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:07:10,436 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,439 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:07:10,439 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,439 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12848.0, 路径长度: 66
2025-08-03 16:07:10,439 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}
2025-08-03 16:07:10,440 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12848.00)
2025-08-03 16:07:10,440 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:07:10,440 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,440 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,440 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 114322.0
2025-08-03 16:07:10,521 - ExploitationExpert - INFO - res_population_num: 19
2025-08-03 16:07:10,521 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:07:10,521 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:07:10,527 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,527 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}, {'tour': [0, 3, 13, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12427.0}, {'tour': [0, 19, 12, 23, 16, 18, 17, 22, 15, 14, 20, 21, 13, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12409.0}, {'tour': [33, 61, 53, 19, 41, 20, 42, 57, 14, 4, 62, 15, 51, 56, 26, 9, 31, 21, 18, 25, 46, 27, 22, 44, 43, 49, 2, 63, 35, 23, 47, 40, 1, 12, 55, 48, 10, 65, 0, 29, 11, 5, 13, 28, 38, 6, 54, 30, 34, 45, 36, 50, 59, 39, 37, 58, 3, 16, 64, 8, 17, 24, 52, 60, 32, 7], 'cur_cost': 110451.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': [63, 30, 21, 37, 38, 51, 4, 58, 36, 65, 22, 11, 34, 54, 5, 6, 52, 19, 2, 41, 0, 39, 35, 44, 24, 62, 25, 49, 46, 7, 45, 42, 16, 61, 9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48, 1, 31, 32, 8, 47, 15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29, 3, 64, 56, 18], 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': [34, 53, 11, 56, 37, 55, 38, 23, 3, 62, 31, 59, 51, 10, 18, 2, 65, 64, 54, 16, 24, 15, 14, 44, 41, 52, 63, 0, 7, 5, 61, 22, 12, 33, 20, 40, 29, 1, 21, 17, 6, 50, 42, 30, 35, 9, 47, 4, 26, 28, 13, 43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58, 8, 27], 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,529 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:07:10,529 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-03 16:07:10,529 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}
2025-08-03 16:07:10,529 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 114322.00)
2025-08-03 16:07:10,529 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:07:10,529 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:07:10,529 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,534 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:07:10,534 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,534 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12441.0, 路径长度: 66
2025-08-03 16:07:10,534 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}
2025-08-03 16:07:10,534 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 12441.00)
2025-08-03 16:07:10,534 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:07:10,534 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:07:10,534 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,537 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:07:10,537 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,537 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107460.0, 路径长度: 66
2025-08-03 16:07:10,537 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}
2025-08-03 16:07:10,538 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 107460.00)
2025-08-03 16:07:10,538 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:07:10,538 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,538 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,538 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 111185.0
2025-08-03 16:07:10,623 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:07:10,623 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521]
2025-08-03 16:07:10,624 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:07:10,630 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,630 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}, {'tour': [37, 12, 19, 30, 14, 15, 16, 18, 20, 27, 25, 13, 10, 11, 63, 22, 43, 40, 17, 28, 6, 8, 3, 53, 23, 31, 33, 24, 36, 2, 59, 9, 1, 62, 61, 64, 21, 32, 29, 35, 46, 48, 38, 39, 45, 34, 26, 47, 49, 5, 60, 54, 56, 58, 55, 65, 57, 4, 0, 52, 41, 51, 44, 50, 42, 7], 'cur_cost': 49824.0}, {'tour': [0, 14, 23, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12801.0}, {'tour': [63, 30, 21, 37, 38, 51, 4, 58, 36, 65, 22, 11, 34, 54, 5, 6, 52, 19, 2, 41, 0, 39, 35, 44, 24, 62, 25, 49, 46, 7, 45, 42, 16, 61, 9, 17, 53, 26, 27, 55, 28, 10, 14, 50, 12, 48, 1, 31, 32, 8, 47, 15, 40, 33, 23, 57, 60, 59, 20, 13, 43, 29, 3, 64, 56, 18], 'cur_cost': 111211.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': [34, 53, 11, 56, 37, 55, 38, 23, 3, 62, 31, 59, 51, 10, 18, 2, 65, 64, 54, 16, 24, 15, 14, 44, 41, 52, 63, 0, 7, 5, 61, 22, 12, 33, 20, 40, 29, 1, 21, 17, 6, 50, 42, 30, 35, 9, 47, 4, 26, 28, 13, 43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58, 8, 27], 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,632 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:07:10,632 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-03 16:07:10,633 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}
2025-08-03 16:07:10,633 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 111185.00)
2025-08-03 16:07:10,633 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:07:10,633 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:07:10,633 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,636 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:07:10,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12852.0, 路径长度: 66
2025-08-03 16:07:10,637 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}
2025-08-03 16:07:10,637 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12852.00)
2025-08-03 16:07:10,637 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:07:10,637 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:07:10,637 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,646 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67170.0, 路径长度: 66
2025-08-03 16:07:10,647 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}
2025-08-03 16:07:10,648 - experts.management.collaboration_manager - INFO - 个体 12 保留原路径 (成本: 67170.00)
2025-08-03 16:07:10,649 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:07:10,650 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,651 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 96858.0
2025-08-03 16:07:10,729 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:07:10,730 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521]
2025-08-03 16:07:10,730 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:07:10,737 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,737 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': array([55, 33,  6, 31, 25,  0, 43,  3, 30,  7, 38, 22,  5, 54, 15, 28, 20,
       39, 37, 36, 46, 51, 29, 21,  9, 34, 16, 17, 18, 59, 61,  2, 11,  1,
       32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14,  8, 53, 41, 58, 24,  4,
       10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60],
      dtype=int64), 'cur_cost': 96858.0}, {'tour': [39, 13, 18, 22, 23, 19, 31, 24, 3, 57, 5, 8, 62, 63, 58, 56, 1, 9, 17, 32, 6, 11, 4, 65, 0, 20, 12, 40, 51, 27, 34, 28, 14, 33, 30, 35, 26, 2, 59, 54, 49, 41, 21, 16, 36, 10, 37, 15, 29, 47, 48, 44, 43, 50, 42, 45, 25, 7, 53, 61, 64, 52, 55, 60, 46, 38], 'cur_cost': 49365.0}, {'tour': [0, 13, 22, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12892.0}, {'tour': [34, 53, 11, 56, 37, 55, 38, 23, 3, 62, 31, 59, 51, 10, 18, 2, 65, 64, 54, 16, 24, 15, 14, 44, 41, 52, 63, 0, 7, 5, 61, 22, 12, 33, 20, 40, 29, 1, 21, 17, 6, 50, 42, 30, 35, 9, 47, 4, 26, 28, 13, 43, 19, 25, 57, 49, 48, 32, 60, 39, 36, 46, 45, 58, 8, 27], 'cur_cost': 97967.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,739 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:07:10,739 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-03 16:07:10,740 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([55, 33,  6, 31, 25,  0, 43,  3, 30,  7, 38, 22,  5, 54, 15, 28, 20,
       39, 37, 36, 46, 51, 29, 21,  9, 34, 16, 17, 18, 59, 61,  2, 11,  1,
       32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14,  8, 53, 41, 58, 24,  4,
       10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60],
      dtype=int64), 'cur_cost': 96858.0}
2025-08-03 16:07:10,740 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 96858.00)
2025-08-03 16:07:10,740 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:07:10,740 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:07:10,740 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,743 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:07:10,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,743 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103737.0, 路径长度: 66
2025-08-03 16:07:10,743 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}
2025-08-03 16:07:10,743 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 103737.00)
2025-08-03 16:07:10,744 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:07:10,744 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:07:10,744 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,756 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62896.0, 路径长度: 66
2025-08-03 16:07:10,757 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}
2025-08-03 16:07:10,757 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 62896.00)
2025-08-03 16:07:10,757 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:07:10,757 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,757 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,758 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 108057.0
2025-08-03 16:07:10,840 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:07:10,840 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521]
2025-08-03 16:07:10,841 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:07:10,850 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,851 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': array([55, 33,  6, 31, 25,  0, 43,  3, 30,  7, 38, 22,  5, 54, 15, 28, 20,
       39, 37, 36, 46, 51, 29, 21,  9, 34, 16, 17, 18, 59, 61,  2, 11,  1,
       32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14,  8, 53, 41, 58, 24,  4,
       10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60],
      dtype=int64), 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': array([15, 58, 50, 30, 23, 39,  5, 32, 20, 26, 29, 61, 21, 34, 53,  9, 13,
       49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12,  0, 19,  1, 52, 59,
       46,  8, 36, 28, 63, 41, 35, 16, 54,  6, 43, 14, 51, 22, 18, 42,  4,
       57, 11, 45, 55, 44, 47, 33, 65,  2, 27,  7, 64,  3, 56, 38],
      dtype=int64), 'cur_cost': 108057.0}, {'tour': [18, 26, 32, 20, 12, 30, 7, 11, 53, 58, 13, 8, 63, 56, 60, 9, 16, 28, 17, 0, 55, 2, 65, 64, 21, 27, 37, 24, 4, 36, 35, 31, 43, 19, 22, 47, 49, 46, 44, 51, 42, 34, 40, 15, 29, 5, 6, 54, 52, 57, 14, 10, 23, 48, 38, 41, 45, 50, 3, 61, 1, 62, 39, 25, 33, 59], 'cur_cost': 64015.0}, {'tour': [0, 22, 3, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': [52, 12, 17, 60, 2, 31, 33, 58, 64, 4, 19, 35, 36, 18, 1, 14, 44, 65, 56, 26, 63, 24, 16, 43, 51, 25, 11, 45, 50, 37, 57, 34, 20, 53, 0, 54, 40, 61, 6, 28, 9, 62, 39, 41, 59, 48, 21, 15, 42, 3, 38, 46, 27, 47, 55, 22, 7, 8, 49, 13, 32, 23, 5, 30, 29, 10], 'cur_cost': 103807.0}]
2025-08-03 16:07:10,855 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:07:10,856 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-03 16:07:10,856 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([15, 58, 50, 30, 23, 39,  5, 32, 20, 26, 29, 61, 21, 34, 53,  9, 13,
       49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12,  0, 19,  1, 52, 59,
       46,  8, 36, 28, 63, 41, 35, 16, 54,  6, 43, 14, 51, 22, 18, 42,  4,
       57, 11, 45, 55, 44, 47, 33, 65,  2, 27,  7, 64,  3, 56, 38],
      dtype=int64), 'cur_cost': 108057.0}
2025-08-03 16:07:10,856 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 108057.00)
2025-08-03 16:07:10,856 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:07:10,856 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:07:10,856 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,867 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 69169.0, 路径长度: 66
2025-08-03 16:07:10,867 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}
2025-08-03 16:07:10,867 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 69169.00)
2025-08-03 16:07:10,868 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:07:10,868 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:07:10,868 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:07:10,877 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:07:10,878 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:07:10,878 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49351.0, 路径长度: 66
2025-08-03 16:07:10,878 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}
2025-08-03 16:07:10,878 - experts.management.collaboration_manager - INFO - 个体 18 保留原路径 (成本: 49351.00)
2025-08-03 16:07:10,878 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:07:10,878 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:07:10,878 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:07:10,879 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 115780.0
2025-08-03 16:07:10,963 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:07:10,963 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0, 9521]
2025-08-03 16:07:10,963 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:07:10,971 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:07:10,971 - ExploitationExpert - INFO - populations: [{'tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}, {'tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': array([55, 33,  6, 31, 25,  0, 43,  3, 30,  7, 38, 22,  5, 54, 15, 28, 20,
       39, 37, 36, 46, 51, 29, 21,  9, 34, 16, 17, 18, 59, 61,  2, 11,  1,
       32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14,  8, 53, 41, 58, 24,  4,
       10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60],
      dtype=int64), 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': array([15, 58, 50, 30, 23, 39,  5, 32, 20, 26, 29, 61, 21, 34, 53,  9, 13,
       49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12,  0, 19,  1, 52, 59,
       46,  8, 36, 28, 63, 41, 35, 16, 54,  6, 43, 14, 51, 22, 18, 42,  4,
       57, 11, 45, 55, 44, 47, 33, 65,  2, 27,  7, 64,  3, 56, 38],
      dtype=int64), 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': array([49, 56, 54, 10, 23, 62, 55,  3, 38, 31, 50,  8, 15,  2, 26,  7, 51,
        1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41,  6, 42,  0, 19, 21,
       29, 13, 45, 24,  9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33,
        4, 58, 59, 44, 36,  5, 53, 40, 17, 64, 39, 60, 11, 30, 18],
      dtype=int64), 'cur_cost': 115780.0}]
2025-08-03 16:07:10,974 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:07:10,974 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:07:10,974 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([49, 56, 54, 10, 23, 62, 55,  3, 38, 31, 50,  8, 15,  2, 26,  7, 51,
        1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41,  6, 42,  0, 19, 21,
       29, 13, 45, 24,  9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33,
        4, 58, 59, 44, 36,  5, 53, 40, 17, 64, 39, 60, 11, 30, 18],
      dtype=int64), 'cur_cost': 115780.0}
2025-08-03 16:07:10,974 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 115780.00)
2025-08-03 16:07:10,974 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:07:10,974 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:07:10,975 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 27, 4, 60, 1, 57, 53, 14, 24, 25, 18, 35, 23, 8, 61, 63, 56, 62, 10, 0, 15, 6, 65, 58, 13, 34, 19, 22, 7, 36, 5, 59, 20, 9, 33, 11, 21, 30, 48, 41, 51, 46, 16, 31, 17, 26, 28, 32, 37, 2, 3, 29, 49, 43, 47, 39, 44, 50, 40, 45, 55, 52, 64, 54, 38, 42], 'cur_cost': 61296.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 13, 16, 65, 38, 57, 58, 60, 56, 23, 32, 62, 50, 21, 41, 53, 37,
       59, 18, 47, 52, 64, 30, 33,  0, 17, 29, 26,  4, 63, 11, 19, 34, 36,
       45,  6, 48,  2, 55, 42,  9, 20,  1, 12, 14, 24, 61, 25,  5, 40,  8,
       51, 15, 39, 44, 27, 35, 43,  3, 54, 49, 22,  7, 10, 31, 46],
      dtype=int64), 'cur_cost': 108535.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40,  0, 19, 52, 23, 12, 60,
        2,  5, 65, 37, 35,  3, 38, 51,  8, 31, 22, 39, 17, 32, 26, 15, 43,
       20, 59,  1,  9,  7, 57, 62, 46, 10, 63,  6, 18, 42, 44, 27,  4, 41,
       33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24],
      dtype=int64), 'cur_cost': 100147.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 34, 32, 41,  5, 47,  6, 50, 35, 53, 31, 64,  7, 52,  8, 40, 60,
        9, 29, 57, 21, 62, 17, 26,  2, 25, 15, 63, 42, 45, 56, 48, 16, 49,
       22, 19, 38, 30, 33, 28, 23, 39,  0, 11, 36, 59, 10, 18, 58, 46, 55,
        3, 24, 13, 27, 43, 37,  4, 44, 51, 61, 20, 12,  1, 14, 65],
      dtype=int64), 'cur_cost': 114322.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65,  6, 24, 38, 57,
        4, 32, 21, 47, 55, 39, 11, 30, 45,  3,  5, 53,  0, 17, 16, 60, 15,
       42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22,  8,
       19, 52,  7, 31, 61, 29,  9, 23, 64, 54, 51, 46, 10, 27, 14],
      dtype=int64), 'cur_cost': 111185.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 33,  6, 31, 25,  0, 43,  3, 30,  7, 38, 22,  5, 54, 15, 28, 20,
       39, 37, 36, 46, 51, 29, 21,  9, 34, 16, 17, 18, 59, 61,  2, 11,  1,
       32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14,  8, 53, 41, 58, 24,  4,
       10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60],
      dtype=int64), 'cur_cost': 96858.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 58, 50, 30, 23, 39,  5, 32, 20, 26, 29, 61, 21, 34, 53,  9, 13,
       49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12,  0, 19,  1, 52, 59,
       46,  8, 36, 28, 63, 41, 35, 16, 54,  6, 43, 14, 51, 22, 18, 42,  4,
       57, 11, 45, 55, 44, 47, 33, 65,  2, 27,  7, 64,  3, 56, 38],
      dtype=int64), 'cur_cost': 108057.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 56, 54, 10, 23, 62, 55,  3, 38, 31, 50,  8, 15,  2, 26,  7, 51,
        1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41,  6, 42,  0, 19, 21,
       29, 13, 45, 24,  9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33,
        4, 58, 59, 44, 36,  5, 53, 40, 17, 64, 39, 60, 11, 30, 18],
      dtype=int64), 'cur_cost': 115780.0}}]
2025-08-03 16:07:10,976 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:07:10,976 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:07:10,989 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12441.000, 多样性=0.973
2025-08-03 16:07:10,989 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-03 16:07:10,990 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-03 16:07:10,990 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:07:10,993 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.030101770833004544, 'best_improvement': -0.0025787734708679184}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.007100396301188968}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 20, 'new_count': 20, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7497607655502393, 'new_diversity': 0.7497607655502393, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 16:07:10,998 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-03 16:07:10,998 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-03 16:07:10,998 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-03 16:07:10,998 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:07:10,999 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12441.000, 多样性=0.973
2025-08-03 16:07:11,000 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:07:11,004 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-03 16:07:11,004 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:07:11,020 - EliteExpert - INFO - 精英解分析完成: 精英解数量=20, 多样性=0.750
2025-08-03 16:07:11,021 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-03 16:07:11,022 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:07:11,022 - LandscapeExpert - INFO - 添加精英解数据: 20个精英解
2025-08-03 16:07:11,022 - LandscapeExpert - INFO - 数据提取成功: 40个路径, 40个适应度值
2025-08-03 16:07:11,271 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.275, 适应度梯度: -13172.230, 聚类评分: 0.000, 覆盖率: 0.010, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:07:11,271 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-03 16:07:11,271 - LandscapeExpert - INFO - 提取到 20 个精英解
2025-08-03 16:07:11,286 - visualization.landscape_visualizer - INFO - 已添加 20 个精英解标记
2025-08-03 16:07:11,357 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_4_20250803_160711.html
2025-08-03 16:07:11,401 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_4_20250803_160711.html
2025-08-03 16:07:11,401 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-03 16:07:11,401 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-03 16:07:11,401 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3800秒
2025-08-03 16:07:11,401 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.275, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13172.23, 'local_optima_density': 0.275, 'gradient_variance': 902042965.8231001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.01, 'fitness_entropy': 0.7018802134600574, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13172.230)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.010)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208431.2712162, 'performance_metrics': {}}}
2025-08-03 16:07:11,401 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:07:11,402 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 3)
2025-08-03 16:07:11,402 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 3, 种群大小: 20)
2025-08-03 16:07:11,408 - experts.analysis.individual_state_analyzer - INFO - 种群状态分析完成，分析了 20 个个体
2025-08-03 16:07:11,408 - experts.strategy.enhanced_strategy_expert - INFO - 使用LLM进行策略选择
2025-08-03 16:07:11,409 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:07:21,585 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:23,587 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:07:33,800 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:35,801 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:07:47,401 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:47,401 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 1): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:47,401 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:07:47,401 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:07:47,401 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:07:47,401 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:07:47,401 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:07:57,619 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:07:59,620 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:08:11,109 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:13,110 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:08:23,352 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:23,353 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 2): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:23,353 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:08:23,353 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:08:23,353 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:08:23,353 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:08:23,353 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:08:33,591 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:35,591 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:08:45,789 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:47,790 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:08:57,981 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:57,985 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 3): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:08:57,985 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:08:57,985 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:08:57,986 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:08:57,986 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:08:57,986 - experts.strategy.enhanced_strategy_expert - WARNING - LLM调用尝试 3 失败: 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:08:57,987 - experts.strategy.enhanced_strategy_expert - ERROR - LLM策略选择过程中发生错误: 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:08:57,987 - experts.strategy.enhanced_strategy_expert - INFO - 使用算法进行策略选择
2025-08-03 16:08:57,987 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: <StagnationLevel.LOW: 'low'>
2025-08-03 16:08:57,987 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:08:57,987 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:08:57,987 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: <StagnationLevel.LOW: 'low'>
2025-08-03 16:08:57,987 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:08:57,987 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:08:57,988 - __main__ - INFO - 策略分配完整报告: 错误回退: <StagnationLevel.LOW: 'low'>
2025-08-03 16:08:57,988 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:08:57,988 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 3, 11, 6} (总数: 4, 保护比例: 0.20)
2025-08-03 16:08:57,988 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:08:57,988 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:08:57,989 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:57,991 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:08:57,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:57,992 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14686.0, 路径长度: 66
2025-08-03 16:08:57,992 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}
2025-08-03 16:08:57,992 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 14686.00)
2025-08-03 16:08:57,992 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:08:57,992 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:57,992 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:57,993 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100934.0
2025-08-03 16:08:59,026 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,026 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,026 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,033 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,033 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [46, 15, 55, 64, 1, 39, 16, 63, 57, 17, 22, 48, 37, 45, 26, 14, 19, 44, 36, 12, 28, 50, 34, 10, 6, 2, 38, 8, 29, 62, 9, 33, 43, 25, 30, 52, 7, 4, 54, 24, 31, 32, 13, 18, 53, 3, 59, 49, 47, 60, 40, 42, 65, 56, 11, 0, 20, 41, 51, 21, 58, 27, 23, 5, 35, 61], 'cur_cost': 96273.0}, {'tour': [0, 22, 9, 16, 18, 12, 17, 15, 14, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14699.0}, {'tour': [53, 64, 36, 16, 30, 48, 21, 28, 49, 55, 40, 0, 19, 52, 23, 12, 60, 2, 5, 65, 37, 35, 3, 38, 51, 8, 31, 22, 39, 17, 32, 26, 15, 43, 20, 59, 1, 9, 7, 57, 62, 46, 10, 63, 6, 18, 42, 44, 27, 4, 41, 33, 14, 25, 58, 61, 45, 29, 34, 56, 13, 47, 50, 11, 54, 24], 'cur_cost': 100147.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': [54, 34, 32, 41, 5, 47, 6, 50, 35, 53, 31, 64, 7, 52, 8, 40, 60, 9, 29, 57, 21, 62, 17, 26, 2, 25, 15, 63, 42, 45, 56, 48, 16, 49, 22, 19, 38, 30, 33, 28, 23, 39, 0, 11, 36, 59, 10, 18, 58, 46, 55, 3, 24, 13, 27, 43, 37, 4, 44, 51, 61, 20, 12, 1, 14, 65], 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': [2, 1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65, 6, 24, 38, 57, 4, 32, 21, 47, 55, 39, 11, 30, 45, 3, 5, 53, 0, 17, 16, 60, 15, 42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22, 8, 19, 52, 7, 31, 61, 29, 9, 23, 64, 54, 51, 46, 10, 27, 14], 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': [55, 33, 6, 31, 25, 0, 43, 3, 30, 7, 38, 22, 5, 54, 15, 28, 20, 39, 37, 36, 46, 51, 29, 21, 9, 34, 16, 17, 18, 59, 61, 2, 11, 1, 32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14, 8, 53, 41, 58, 24, 4, 10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60], 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': [15, 58, 50, 30, 23, 39, 5, 32, 20, 26, 29, 61, 21, 34, 53, 9, 13, 49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12, 0, 19, 1, 52, 59, 46, 8, 36, 28, 63, 41, 35, 16, 54, 6, 43, 14, 51, 22, 18, 42, 4, 57, 11, 45, 55, 44, 47, 33, 65, 2, 27, 7, 64, 3, 56, 38], 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,034 - ExploitationExpert - INFO - 局部搜索耗时: 1.04秒
2025-08-03 16:08:59,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,035 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}
2025-08-03 16:08:59,035 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 100934.00)
2025-08-03 16:08:59,035 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:08:59,035 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:08:59,035 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,039 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98350.0, 路径长度: 66
2025-08-03 16:08:59,039 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}
2025-08-03 16:08:59,039 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 98350.00)
2025-08-03 16:08:59,039 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:08:59,040 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:08:59,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,042 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:08:59,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,043 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14167.0, 路径长度: 66
2025-08-03 16:08:59,043 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}
2025-08-03 16:08:59,043 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 14167.00)
2025-08-03 16:08:59,043 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:08:59,043 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,043 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,043 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 114186.0
2025-08-03 16:08:59,133 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,133 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,133 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,140 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,140 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [4, 60, 8, 11, 9, 2, 23, 20, 30, 7, 22, 34, 36, 1, 6, 61, 53, 16, 17, 12, 18, 43, 44, 13, 5, 14, 33, 15, 37, 24, 19, 25, 35, 46, 21, 40, 51, 49, 48, 41, 45, 38, 32, 42, 26, 50, 27, 39, 56, 55, 65, 63, 57, 59, 52, 58, 62, 54, 0, 64, 3, 10, 29, 31, 28, 47], 'cur_cost': 56879.0}, {'tour': [0, 6, 12, 5, 4, 8, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12848.0}, {'tour': [54, 34, 32, 41, 5, 47, 6, 50, 35, 53, 31, 64, 7, 52, 8, 40, 60, 9, 29, 57, 21, 62, 17, 26, 2, 25, 15, 63, 42, 45, 56, 48, 16, 49, 22, 19, 38, 30, 33, 28, 23, 39, 0, 11, 36, 59, 10, 18, 58, 46, 55, 3, 24, 13, 27, 43, 37, 4, 44, 51, 61, 20, 12, 1, 14, 65], 'cur_cost': 114322.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': [2, 1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65, 6, 24, 38, 57, 4, 32, 21, 47, 55, 39, 11, 30, 45, 3, 5, 53, 0, 17, 16, 60, 15, 42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22, 8, 19, 52, 7, 31, 61, 29, 9, 23, 64, 54, 51, 46, 10, 27, 14], 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': [55, 33, 6, 31, 25, 0, 43, 3, 30, 7, 38, 22, 5, 54, 15, 28, 20, 39, 37, 36, 46, 51, 29, 21, 9, 34, 16, 17, 18, 59, 61, 2, 11, 1, 32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14, 8, 53, 41, 58, 24, 4, 10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60], 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': [15, 58, 50, 30, 23, 39, 5, 32, 20, 26, 29, 61, 21, 34, 53, 9, 13, 49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12, 0, 19, 1, 52, 59, 46, 8, 36, 28, 63, 41, 35, 16, 54, 6, 43, 14, 51, 22, 18, 42, 4, 57, 11, 45, 55, 44, 47, 33, 65, 2, 27, 7, 64, 3, 56, 38], 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,141 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:08:59,142 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,142 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}
2025-08-03 16:08:59,142 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 114186.00)
2025-08-03 16:08:59,142 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:08:59,142 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:08:59,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,145 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:08:59,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14231.0, 路径长度: 66
2025-08-03 16:08:59,146 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}
2025-08-03 16:08:59,147 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14231.00)
2025-08-03 16:08:59,147 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:08:59,148 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:08:59,149 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,155 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,156 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,156 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115696.0, 路径长度: 66
2025-08-03 16:08:59,156 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}
2025-08-03 16:08:59,157 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 115696.00)
2025-08-03 16:08:59,157 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:08:59,157 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,157 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,157 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104724.0
2025-08-03 16:08:59,252 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,252 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,252 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,258 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,259 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}, {'tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}, {'tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}, {'tour': [0, 16, 15, 23, 22, 12, 17, 18, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12441.0}, {'tour': [12, 15, 4, 22, 23, 6, 63, 19, 44, 13, 5, 24, 8, 3, 1, 11, 9, 33, 52, 32, 54, 51, 50, 26, 60, 43, 59, 56, 38, 45, 20, 16, 64, 46, 55, 62, 30, 18, 36, 10, 41, 25, 53, 42, 47, 17, 37, 28, 34, 7, 29, 2, 27, 49, 58, 65, 48, 35, 21, 40, 61, 31, 57, 39, 0, 14], 'cur_cost': 107460.0}, {'tour': [2, 1, 50, 43, 63, 18, 40, 56, 49, 44, 37, 13, 65, 6, 24, 38, 57, 4, 32, 21, 47, 55, 39, 11, 30, 45, 3, 5, 53, 0, 17, 16, 60, 15, 42, 28, 25, 34, 20, 58, 26, 33, 59, 12, 35, 48, 62, 36, 41, 22, 8, 19, 52, 7, 31, 61, 29, 9, 23, 64, 54, 51, 46, 10, 27, 14], 'cur_cost': 111185.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': [55, 33, 6, 31, 25, 0, 43, 3, 30, 7, 38, 22, 5, 54, 15, 28, 20, 39, 37, 36, 46, 51, 29, 21, 9, 34, 16, 17, 18, 59, 61, 2, 11, 1, 32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14, 8, 53, 41, 58, 24, 4, 10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60], 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': [15, 58, 50, 30, 23, 39, 5, 32, 20, 26, 29, 61, 21, 34, 53, 9, 13, 49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12, 0, 19, 1, 52, 59, 46, 8, 36, 28, 63, 41, 35, 16, 54, 6, 43, 14, 51, 22, 18, 42, 4, 57, 11, 45, 55, 44, 47, 33, 65, 2, 27, 7, 64, 3, 56, 38], 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,260 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:08:59,260 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,260 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}
2025-08-03 16:08:59,260 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 104724.00)
2025-08-03 16:08:59,261 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:08:59,261 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:08:59,261 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,270 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:08:59,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68456.0, 路径长度: 66
2025-08-03 16:08:59,271 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}
2025-08-03 16:08:59,271 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 68456.00)
2025-08-03 16:08:59,271 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:08:59,271 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:08:59,271 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,274 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:08:59,274 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,275 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12852.0, 路径长度: 66
2025-08-03 16:08:59,275 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}
2025-08-03 16:08:59,275 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12852.00)
2025-08-03 16:08:59,275 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:08:59,275 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,275 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,275 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 100324.0
2025-08-03 16:08:59,367 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,368 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,368 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,374 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,374 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}, {'tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}, {'tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}, {'tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}, {'tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}, {'tour': [0, 7, 23, 24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 12, 22, 15, 14, 20, 21, 13, 19, 17, 9, 11, 1, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12852.0}, {'tour': [37, 18, 35, 13, 33, 36, 17, 9, 59, 8, 4, 62, 20, 34, 23, 6, 7, 55, 22, 28, 43, 41, 26, 40, 12, 49, 15, 1, 56, 21, 30, 31, 27, 3, 58, 61, 52, 16, 14, 24, 47, 25, 32, 42, 44, 39, 50, 5, 2, 60, 64, 63, 57, 10, 54, 11, 0, 19, 46, 45, 38, 48, 51, 29, 53, 65], 'cur_cost': 67170.0}, {'tour': [55, 33, 6, 31, 25, 0, 43, 3, 30, 7, 38, 22, 5, 54, 15, 28, 20, 39, 37, 36, 46, 51, 29, 21, 9, 34, 16, 17, 18, 59, 61, 2, 11, 1, 32, 48, 12, 45, 13, 56, 62, 57, 64, 52, 14, 8, 53, 41, 58, 24, 4, 10, 49, 65, 47, 26, 42, 19, 27, 63, 50, 40, 44, 23, 35, 60], 'cur_cost': 96858.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': [15, 58, 50, 30, 23, 39, 5, 32, 20, 26, 29, 61, 21, 34, 53, 9, 13, 49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12, 0, 19, 1, 52, 59, 46, 8, 36, 28, 63, 41, 35, 16, 54, 6, 43, 14, 51, 22, 18, 42, 4, 57, 11, 45, 55, 44, 47, 33, 65, 2, 27, 7, 64, 3, 56, 38], 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,376 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:08:59,376 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,376 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}
2025-08-03 16:08:59,377 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 100324.00)
2025-08-03 16:08:59,377 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:08:59,377 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:08:59,377 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,383 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,384 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114307.0, 路径长度: 66
2025-08-03 16:08:59,384 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [64, 21, 12, 53, 38, 14, 17, 13, 63, 25, 62, 56, 30, 45, 33, 24, 39, 46, 7, 47, 9, 4, 35, 36, 3, 55, 22, 0, 58, 5, 23, 18, 49, 10, 44, 61, 50, 43, 59, 48, 1, 41, 8, 19, 26, 2, 54, 34, 11, 60, 51, 52, 27, 40, 37, 15, 6, 20, 57, 29, 65, 42, 28, 32, 31, 16], 'cur_cost': 114307.0}
2025-08-03 16:08:59,385 - experts.management.collaboration_manager - INFO - 个体 11 保留原路径 (成本: 114307.00)
2025-08-03 16:08:59,385 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:08:59,385 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:08:59,385 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,394 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:08:59,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,395 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51380.0, 路径长度: 66
2025-08-03 16:08:59,395 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [61, 22, 15, 9, 17, 19, 31, 34, 37, 13, 33, 5, 65, 60, 54, 52, 62, 16, 29, 30, 24, 20, 36, 4, 64, 3, 0, 8, 7, 2, 58, 40, 39, 50, 46, 47, 38, 42, 45, 26, 14, 6, 63, 56, 53, 59, 11, 21, 28, 1, 18, 10, 55, 23, 25, 43, 41, 44, 48, 49, 51, 27, 35, 32, 12, 57], 'cur_cost': 51380.0}
2025-08-03 16:08:59,395 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 51380.00)
2025-08-03 16:08:59,395 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:08:59,395 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,395 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 107001.0
2025-08-03 16:08:59,486 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,486 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,486 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,493 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,493 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}, {'tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}, {'tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}, {'tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}, {'tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}, {'tour': [64, 21, 12, 53, 38, 14, 17, 13, 63, 25, 62, 56, 30, 45, 33, 24, 39, 46, 7, 47, 9, 4, 35, 36, 3, 55, 22, 0, 58, 5, 23, 18, 49, 10, 44, 61, 50, 43, 59, 48, 1, 41, 8, 19, 26, 2, 54, 34, 11, 60, 51, 52, 27, 40, 37, 15, 6, 20, 57, 29, 65, 42, 28, 32, 31, 16], 'cur_cost': 114307.0}, {'tour': [61, 22, 15, 9, 17, 19, 31, 34, 37, 13, 33, 5, 65, 60, 54, 52, 62, 16, 29, 30, 24, 20, 36, 4, 64, 3, 0, 8, 7, 2, 58, 40, 39, 50, 46, 47, 38, 42, 45, 26, 14, 6, 63, 56, 53, 59, 11, 21, 28, 1, 18, 10, 55, 23, 25, 43, 41, 44, 48, 49, 51, 27, 35, 32, 12, 57], 'cur_cost': 51380.0}, {'tour': array([18, 52, 53, 34, 41, 11,  1, 15, 25, 21, 44, 47, 16,  9, 32, 39, 50,
       14, 57,  8, 38, 58, 33, 60, 48,  0, 13,  4, 55, 59, 35, 22, 36, 63,
        7, 45, 10, 61, 26, 54, 29, 27, 24, 23, 43, 51,  6, 28, 64, 17, 40,
        3, 20, 12, 56, 46, 31, 30,  2, 49, 42,  5, 65, 62, 37, 19],
      dtype=int64), 'cur_cost': 107001.0}, {'tour': [46, 60, 12, 23, 9, 24, 17, 20, 34, 8, 26, 63, 36, 35, 43, 33, 58, 31, 2, 38, 15, 3, 7, 19, 11, 64, 55, 18, 14, 45, 32, 16, 53, 39, 29, 65, 56, 0, 62, 54, 51, 21, 10, 30, 61, 4, 25, 47, 41, 22, 27, 42, 48, 6, 37, 52, 13, 59, 44, 5, 57, 1, 28, 49, 50, 40], 'cur_cost': 103737.0}, {'tour': [60, 63, 54, 4, 13, 31, 18, 35, 16, 36, 3, 59, 9, 23, 7, 21, 22, 20, 37, 28, 25, 40, 51, 19, 43, 15, 14, 33, 29, 6, 11, 62, 57, 61, 53, 2, 55, 65, 8, 58, 1, 0, 32, 17, 47, 41, 39, 45, 12, 26, 30, 48, 46, 49, 44, 27, 42, 38, 24, 10, 52, 56, 5, 64, 50, 34], 'cur_cost': 62896.0}, {'tour': [15, 58, 50, 30, 23, 39, 5, 32, 20, 26, 29, 61, 21, 34, 53, 9, 13, 49, 17, 62, 10, 24, 37, 60, 25, 31, 48, 40, 12, 0, 19, 1, 52, 59, 46, 8, 36, 28, 63, 41, 35, 16, 54, 6, 43, 14, 51, 22, 18, 42, 4, 57, 11, 45, 55, 44, 47, 33, 65, 2, 27, 7, 64, 3, 56, 38], 'cur_cost': 108057.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,495 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:08:59,495 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,495 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([18, 52, 53, 34, 41, 11,  1, 15, 25, 21, 44, 47, 16,  9, 32, 39, 50,
       14, 57,  8, 38, 58, 33, 60, 48,  0, 13,  4, 55, 59, 35, 22, 36, 63,
        7, 45, 10, 61, 26, 54, 29, 27, 24, 23, 43, 51,  6, 28, 64, 17, 40,
        3, 20, 12, 56, 46, 31, 30,  2, 49, 42,  5, 65, 62, 37, 19],
      dtype=int64), 'cur_cost': 107001.0}
2025-08-03 16:08:59,495 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 107001.00)
2025-08-03 16:08:59,495 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:08:59,495 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:08:59,495 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,500 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,500 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,500 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107147.0, 路径长度: 66
2025-08-03 16:08:59,500 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [12, 7, 16, 15, 20, 61, 53, 11, 56, 30, 23, 33, 29, 27, 3, 46, 9, 43, 5, 0, 24, 52, 40, 6, 1, 41, 44, 42, 48, 2, 54, 22, 10, 36, 32, 57, 34, 39, 4, 49, 13, 31, 50, 26, 60, 18, 62, 45, 55, 21, 14, 64, 35, 37, 19, 8, 58, 38, 25, 28, 59, 63, 17, 51, 65, 47], 'cur_cost': 107147.0}
2025-08-03 16:08:59,501 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 107147.00)
2025-08-03 16:08:59,502 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:08:59,502 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:08:59,502 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,505 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,505 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,505 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102833.0, 路径长度: 66
2025-08-03 16:08:59,505 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [53, 64, 21, 48, 23, 15, 31, 17, 54, 43, 61, 39, 8, 29, 36, 35, 30, 19, 51, 0, 1, 65, 42, 27, 62, 50, 9, 28, 41, 22, 12, 14, 32, 25, 38, 49, 52, 57, 44, 16, 56, 59, 24, 13, 40, 55, 11, 34, 45, 5, 6, 4, 47, 26, 18, 37, 2, 46, 3, 10, 58, 20, 63, 33, 60, 7], 'cur_cost': 102833.0}
2025-08-03 16:08:59,505 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 102833.00)
2025-08-03 16:08:59,505 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:08:59,505 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,506 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,506 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 117107.0
2025-08-03 16:08:59,588 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,588 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,588 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,594 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,594 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}, {'tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}, {'tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}, {'tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}, {'tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}, {'tour': [64, 21, 12, 53, 38, 14, 17, 13, 63, 25, 62, 56, 30, 45, 33, 24, 39, 46, 7, 47, 9, 4, 35, 36, 3, 55, 22, 0, 58, 5, 23, 18, 49, 10, 44, 61, 50, 43, 59, 48, 1, 41, 8, 19, 26, 2, 54, 34, 11, 60, 51, 52, 27, 40, 37, 15, 6, 20, 57, 29, 65, 42, 28, 32, 31, 16], 'cur_cost': 114307.0}, {'tour': [61, 22, 15, 9, 17, 19, 31, 34, 37, 13, 33, 5, 65, 60, 54, 52, 62, 16, 29, 30, 24, 20, 36, 4, 64, 3, 0, 8, 7, 2, 58, 40, 39, 50, 46, 47, 38, 42, 45, 26, 14, 6, 63, 56, 53, 59, 11, 21, 28, 1, 18, 10, 55, 23, 25, 43, 41, 44, 48, 49, 51, 27, 35, 32, 12, 57], 'cur_cost': 51380.0}, {'tour': array([18, 52, 53, 34, 41, 11,  1, 15, 25, 21, 44, 47, 16,  9, 32, 39, 50,
       14, 57,  8, 38, 58, 33, 60, 48,  0, 13,  4, 55, 59, 35, 22, 36, 63,
        7, 45, 10, 61, 26, 54, 29, 27, 24, 23, 43, 51,  6, 28, 64, 17, 40,
        3, 20, 12, 56, 46, 31, 30,  2, 49, 42,  5, 65, 62, 37, 19],
      dtype=int64), 'cur_cost': 107001.0}, {'tour': [12, 7, 16, 15, 20, 61, 53, 11, 56, 30, 23, 33, 29, 27, 3, 46, 9, 43, 5, 0, 24, 52, 40, 6, 1, 41, 44, 42, 48, 2, 54, 22, 10, 36, 32, 57, 34, 39, 4, 49, 13, 31, 50, 26, 60, 18, 62, 45, 55, 21, 14, 64, 35, 37, 19, 8, 58, 38, 25, 28, 59, 63, 17, 51, 65, 47], 'cur_cost': 107147.0}, {'tour': [53, 64, 21, 48, 23, 15, 31, 17, 54, 43, 61, 39, 8, 29, 36, 35, 30, 19, 51, 0, 1, 65, 42, 27, 62, 50, 9, 28, 41, 22, 12, 14, 32, 25, 38, 49, 52, 57, 44, 16, 56, 59, 24, 13, 40, 55, 11, 34, 45, 5, 6, 4, 47, 26, 18, 37, 2, 46, 3, 10, 58, 20, 63, 33, 60, 7], 'cur_cost': 102833.0}, {'tour': array([24, 29, 11, 26, 38, 34, 46, 37,  7, 50, 63, 41,  2, 36, 54, 44, 31,
       16, 57, 56, 47, 49, 64, 20, 30, 12, 51, 35, 28, 61, 59, 58, 14, 60,
       52, 22, 45,  5, 18,  9,  0, 27, 19, 62, 13,  1, 21,  6, 33, 53, 25,
       65, 17, 10, 43, 15, 42, 55, 40,  4, 48, 39,  8, 32,  3, 23],
      dtype=int64), 'cur_cost': 117107.0}, {'tour': [3, 58, 62, 9, 12, 18, 22, 26, 25, 36, 1, 59, 20, 19, 34, 11, 17, 4, 60, 23, 7, 13, 6, 5, 15, 2, 57, 49, 45, 35, 8, 56, 47, 50, 42, 14, 32, 40, 38, 39, 33, 21, 24, 10, 64, 16, 37, 28, 43, 46, 30, 41, 48, 51, 44, 31, 0, 61, 63, 65, 53, 52, 55, 54, 27, 29], 'cur_cost': 69169.0}, {'tour': [4, 61, 17, 22, 31, 29, 20, 3, 7, 59, 9, 12, 36, 25, 13, 0, 6, 64, 55, 8, 18, 16, 43, 41, 23, 28, 26, 32, 21, 1, 5, 15, 27, 19, 39, 42, 46, 45, 38, 30, 34, 24, 35, 37, 47, 33, 49, 11, 56, 57, 58, 53, 60, 62, 63, 52, 10, 2, 14, 48, 44, 50, 51, 40, 54, 65], 'cur_cost': 49351.0}, {'tour': [49, 56, 54, 10, 23, 62, 55, 3, 38, 31, 50, 8, 15, 2, 26, 7, 51, 1, 22, 14, 28, 32, 63, 34, 48, 57, 35, 12, 41, 6, 42, 0, 19, 21, 29, 13, 45, 24, 9, 43, 47, 52, 46, 37, 27, 61, 25, 65, 20, 16, 33, 4, 58, 59, 44, 36, 5, 53, 40, 17, 64, 39, 60, 11, 30, 18], 'cur_cost': 115780.0}]
2025-08-03 16:08:59,597 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:08:59,597 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,597 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([24, 29, 11, 26, 38, 34, 46, 37,  7, 50, 63, 41,  2, 36, 54, 44, 31,
       16, 57, 56, 47, 49, 64, 20, 30, 12, 51, 35, 28, 61, 59, 58, 14, 60,
       52, 22, 45,  5, 18,  9,  0, 27, 19, 62, 13,  1, 21,  6, 33, 53, 25,
       65, 17, 10, 43, 15, 42, 55, 40,  4, 48, 39,  8, 32,  3, 23],
      dtype=int64), 'cur_cost': 117107.0}
2025-08-03 16:08:59,597 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 117107.00)
2025-08-03 16:08:59,597 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:08:59,597 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:08:59,598 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,602 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:08:59,602 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12837.0, 路径长度: 66
2025-08-03 16:08:59,602 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [0, 19, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}
2025-08-03 16:08:59,602 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 12837.00)
2025-08-03 16:08:59,602 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:08:59,602 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:08:59,603 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:08:59,605 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:08:59,606 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:08:59,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106183.0, 路径长度: 66
2025-08-03 16:08:59,606 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [48, 1, 20, 15, 31, 19, 43, 52, 39, 25, 7, 54, 24, 9, 3, 44, 5, 2, 17, 29, 22, 0, 14, 49, 47, 37, 38, 42, 45, 63, 28, 33, 21, 55, 61, 6, 23, 30, 53, 34, 46, 18, 16, 50, 11, 62, 12, 27, 60, 36, 51, 56, 10, 26, 13, 57, 65, 32, 40, 58, 4, 8, 59, 35, 64, 41], 'cur_cost': 106183.0}
2025-08-03 16:08:59,606 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 106183.00)
2025-08-03 16:08:59,606 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:08:59,606 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:08:59,606 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:08:59,607 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 101451.0
2025-08-03 16:08:59,688 - ExploitationExpert - INFO - res_population_num: 20
2025-08-03 16:08:59,688 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9524, 9527, 9527, 9527, 9538, 9540, 9540, 9565, 9577.0, 9821.0]
2025-08-03 16:08:59,689 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 26, 25, 37, 36,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:08:59,695 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:08:59,695 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}, {'tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}, {'tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}, {'tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}, {'tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}, {'tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}, {'tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}, {'tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}, {'tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}, {'tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}, {'tour': [64, 21, 12, 53, 38, 14, 17, 13, 63, 25, 62, 56, 30, 45, 33, 24, 39, 46, 7, 47, 9, 4, 35, 36, 3, 55, 22, 0, 58, 5, 23, 18, 49, 10, 44, 61, 50, 43, 59, 48, 1, 41, 8, 19, 26, 2, 54, 34, 11, 60, 51, 52, 27, 40, 37, 15, 6, 20, 57, 29, 65, 42, 28, 32, 31, 16], 'cur_cost': 114307.0}, {'tour': [61, 22, 15, 9, 17, 19, 31, 34, 37, 13, 33, 5, 65, 60, 54, 52, 62, 16, 29, 30, 24, 20, 36, 4, 64, 3, 0, 8, 7, 2, 58, 40, 39, 50, 46, 47, 38, 42, 45, 26, 14, 6, 63, 56, 53, 59, 11, 21, 28, 1, 18, 10, 55, 23, 25, 43, 41, 44, 48, 49, 51, 27, 35, 32, 12, 57], 'cur_cost': 51380.0}, {'tour': array([18, 52, 53, 34, 41, 11,  1, 15, 25, 21, 44, 47, 16,  9, 32, 39, 50,
       14, 57,  8, 38, 58, 33, 60, 48,  0, 13,  4, 55, 59, 35, 22, 36, 63,
        7, 45, 10, 61, 26, 54, 29, 27, 24, 23, 43, 51,  6, 28, 64, 17, 40,
        3, 20, 12, 56, 46, 31, 30,  2, 49, 42,  5, 65, 62, 37, 19],
      dtype=int64), 'cur_cost': 107001.0}, {'tour': [12, 7, 16, 15, 20, 61, 53, 11, 56, 30, 23, 33, 29, 27, 3, 46, 9, 43, 5, 0, 24, 52, 40, 6, 1, 41, 44, 42, 48, 2, 54, 22, 10, 36, 32, 57, 34, 39, 4, 49, 13, 31, 50, 26, 60, 18, 62, 45, 55, 21, 14, 64, 35, 37, 19, 8, 58, 38, 25, 28, 59, 63, 17, 51, 65, 47], 'cur_cost': 107147.0}, {'tour': [53, 64, 21, 48, 23, 15, 31, 17, 54, 43, 61, 39, 8, 29, 36, 35, 30, 19, 51, 0, 1, 65, 42, 27, 62, 50, 9, 28, 41, 22, 12, 14, 32, 25, 38, 49, 52, 57, 44, 16, 56, 59, 24, 13, 40, 55, 11, 34, 45, 5, 6, 4, 47, 26, 18, 37, 2, 46, 3, 10, 58, 20, 63, 33, 60, 7], 'cur_cost': 102833.0}, {'tour': array([24, 29, 11, 26, 38, 34, 46, 37,  7, 50, 63, 41,  2, 36, 54, 44, 31,
       16, 57, 56, 47, 49, 64, 20, 30, 12, 51, 35, 28, 61, 59, 58, 14, 60,
       52, 22, 45,  5, 18,  9,  0, 27, 19, 62, 13,  1, 21,  6, 33, 53, 25,
       65, 17, 10, 43, 15, 42, 55, 40,  4, 48, 39,  8, 32,  3, 23],
      dtype=int64), 'cur_cost': 117107.0}, {'tour': [0, 19, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}, {'tour': [48, 1, 20, 15, 31, 19, 43, 52, 39, 25, 7, 54, 24, 9, 3, 44, 5, 2, 17, 29, 22, 0, 14, 49, 47, 37, 38, 42, 45, 63, 28, 33, 21, 55, 61, 6, 23, 30, 53, 34, 46, 18, 16, 50, 11, 62, 12, 27, 60, 36, 51, 56, 10, 26, 13, 57, 65, 32, 40, 58, 4, 8, 59, 35, 64, 41], 'cur_cost': 106183.0}, {'tour': array([38, 47, 59, 39, 23, 50, 21, 43, 49, 14, 30, 57, 11, 42, 65,  3, 46,
       62,  9, 60,  5, 61, 33, 55, 19, 22,  1, 54, 31, 37,  4,  8, 24, 41,
       36, 27, 45, 16,  2, 35, 48, 15, 25, 64, 63, 28, 10, 18, 52, 13, 51,
       17, 56, 53, 12, 26, 29, 32,  0,  6, 40, 58,  7, 20, 34, 44],
      dtype=int64), 'cur_cost': 101451.0}]
2025-08-03 16:08:59,698 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:08:59,698 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-03 16:08:59,698 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([38, 47, 59, 39, 23, 50, 21, 43, 49, 14, 30, 57, 11, 42, 65,  3, 46,
       62,  9, 60,  5, 61, 33, 55, 19, 22,  1, 54, 31, 37,  4,  8, 24, 41,
       36, 27, 45, 16,  2, 35, 48, 15, 25, 64, 63, 28, 10, 18, 52, 13, 51,
       17, 56, 53, 12, 26, 29, 32,  0,  6, 40, 58,  7, 20, 34, 44],
      dtype=int64), 'cur_cost': 101451.0}
2025-08-03 16:08:59,698 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 101451.00)
2025-08-03 16:08:59,698 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:08:59,698 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:08:59,700 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14686.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 24, 47, 37, 65, 44, 48, 29, 26, 58,  4, 23, 35, 36,  6, 10, 14,
       31, 39, 53, 59, 15, 18,  2, 12, 61,  3, 57, 21, 55, 49, 22, 34,  7,
       51, 25, 20, 33, 38,  1, 45, 41, 42, 27,  8, 62, 17, 54, 28,  0, 60,
       63, 11, 56,  9, 43, 40, 52,  5, 64, 13, 32, 46, 30, 19, 50],
      dtype=int64), 'cur_cost': 100934.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [53, 64, 41, 21, 48, 16, 15, 31, 37, 61, 59, 56, 65, 54, 43, 10, 6, 52, 38, 39, 7, 60, 25, 30, 19, 9, 27, 51, 46, 63, 3, 44, 40, 33, 24, 13, 11, 2, 45, 29, 62, 50, 12, 34, 4, 55, 22, 0, 18, 14, 28, 17, 23, 47, 1, 49, 32, 8, 42, 36, 58, 20, 26, 57, 35, 5], 'cur_cost': 98350.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 25, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14167.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 24, 57, 19, 62,  1, 65, 44, 29, 33, 49, 11, 48, 60,  9,  7, 17,
       50, 14,  8, 41, 58, 47, 12, 55, 46, 25, 39, 16, 59, 13, 22, 21, 56,
       30,  6, 31, 43, 32, 45, 54, 35, 51, 10, 53,  2, 42, 64,  4,  3, 38,
       40, 20,  0, 26, 34, 18, 37, 28, 36, 63, 23, 15,  5, 61, 27],
      dtype=int64), 'cur_cost': 114186.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 24, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14231.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 10, 16, 1, 61, 20, 17, 13, 53, 38, 32, 30, 52, 27, 63, 15, 7, 23, 11, 33, 24, 56, 19, 45, 28, 34, 39, 22, 0, 46, 47, 65, 43, 44, 64, 31, 51, 35, 6, 36, 55, 48, 9, 3, 2, 59, 21, 26, 54, 41, 50, 5, 25, 18, 49, 58, 37, 60, 62, 29, 40, 4, 42, 14, 8, 57], 'cur_cost': 115696.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 22, 11,  0, 59, 34, 41, 63, 23,  8, 48, 37, 25,  3, 53, 40,  9,
       52, 38,  6,  2, 60, 19, 14, 47, 32, 50, 21, 56, 43, 57, 26, 12, 42,
       13, 16, 45, 39, 35,  1, 27, 31, 49, 18, 36, 46, 54, 33, 55, 65, 61,
        7,  4, 62, 58, 44, 17, 15,  5, 51, 20, 30, 10, 24, 64, 28],
      dtype=int64), 'cur_cost': 104724.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [46, 42, 48, 35, 12, 3, 57, 22, 37, 13, 7, 10, 61, 15, 27, 0, 62, 8, 59, 11, 5, 4, 54, 23, 18, 28, 16, 21, 2, 6, 55, 53, 40, 19, 29, 33, 25, 17, 26, 1, 36, 20, 14, 31, 47, 51, 50, 34, 9, 58, 63, 39, 49, 38, 30, 43, 24, 32, 41, 45, 56, 60, 64, 65, 52, 44], 'cur_cost': 68456.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 20, 9, 11, 7, 3, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 18,  7,  8, 16, 22, 51, 44, 12, 14, 15, 52, 61,  2, 21, 24, 17,
       37, 27, 41, 32, 10, 53, 63, 65, 43,  6, 26,  9, 49, 54, 45, 39, 30,
       59, 31, 34, 36, 56, 13, 29, 57, 38,  1,  0, 58, 47, 20,  4, 62, 23,
       40, 19, 11,  3, 50, 48,  5, 55, 28, 33, 46, 64, 42, 25, 60],
      dtype=int64), 'cur_cost': 100324.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [64, 21, 12, 53, 38, 14, 17, 13, 63, 25, 62, 56, 30, 45, 33, 24, 39, 46, 7, 47, 9, 4, 35, 36, 3, 55, 22, 0, 58, 5, 23, 18, 49, 10, 44, 61, 50, 43, 59, 48, 1, 41, 8, 19, 26, 2, 54, 34, 11, 60, 51, 52, 27, 40, 37, 15, 6, 20, 57, 29, 65, 42, 28, 32, 31, 16], 'cur_cost': 114307.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [61, 22, 15, 9, 17, 19, 31, 34, 37, 13, 33, 5, 65, 60, 54, 52, 62, 16, 29, 30, 24, 20, 36, 4, 64, 3, 0, 8, 7, 2, 58, 40, 39, 50, 46, 47, 38, 42, 45, 26, 14, 6, 63, 56, 53, 59, 11, 21, 28, 1, 18, 10, 55, 23, 25, 43, 41, 44, 48, 49, 51, 27, 35, 32, 12, 57], 'cur_cost': 51380.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 52, 53, 34, 41, 11,  1, 15, 25, 21, 44, 47, 16,  9, 32, 39, 50,
       14, 57,  8, 38, 58, 33, 60, 48,  0, 13,  4, 55, 59, 35, 22, 36, 63,
        7, 45, 10, 61, 26, 54, 29, 27, 24, 23, 43, 51,  6, 28, 64, 17, 40,
        3, 20, 12, 56, 46, 31, 30,  2, 49, 42,  5, 65, 62, 37, 19],
      dtype=int64), 'cur_cost': 107001.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [12, 7, 16, 15, 20, 61, 53, 11, 56, 30, 23, 33, 29, 27, 3, 46, 9, 43, 5, 0, 24, 52, 40, 6, 1, 41, 44, 42, 48, 2, 54, 22, 10, 36, 32, 57, 34, 39, 4, 49, 13, 31, 50, 26, 60, 18, 62, 45, 55, 21, 14, 64, 35, 37, 19, 8, 58, 38, 25, 28, 59, 63, 17, 51, 65, 47], 'cur_cost': 107147.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [53, 64, 21, 48, 23, 15, 31, 17, 54, 43, 61, 39, 8, 29, 36, 35, 30, 19, 51, 0, 1, 65, 42, 27, 62, 50, 9, 28, 41, 22, 12, 14, 32, 25, 38, 49, 52, 57, 44, 16, 56, 59, 24, 13, 40, 55, 11, 34, 45, 5, 6, 4, 47, 26, 18, 37, 2, 46, 3, 10, 58, 20, 63, 33, 60, 7], 'cur_cost': 102833.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 29, 11, 26, 38, 34, 46, 37,  7, 50, 63, 41,  2, 36, 54, 44, 31,
       16, 57, 56, 47, 49, 64, 20, 30, 12, 51, 35, 28, 61, 59, 58, 14, 60,
       52, 22, 45,  5, 18,  9,  0, 27, 19, 62, 13,  1, 21,  6, 33, 53, 25,
       65, 17, 10, 43, 15, 42, 55, 40,  4, 48, 39,  8, 32,  3, 23],
      dtype=int64), 'cur_cost': 117107.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 23, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12837.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [48, 1, 20, 15, 31, 19, 43, 52, 39, 25, 7, 54, 24, 9, 3, 44, 5, 2, 17, 29, 22, 0, 14, 49, 47, 37, 38, 42, 45, 63, 28, 33, 21, 55, 61, 6, 23, 30, 53, 34, 46, 18, 16, 50, 11, 62, 12, 27, 60, 36, 51, 56, 10, 26, 13, 57, 65, 32, 40, 58, 4, 8, 59, 35, 64, 41], 'cur_cost': 106183.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 47, 59, 39, 23, 50, 21, 43, 49, 14, 30, 57, 11, 42, 65,  3, 46,
       62,  9, 60,  5, 61, 33, 55, 19, 22,  1, 54, 31, 37,  4,  8, 24, 41,
       36, 27, 45, 16,  2, 35, 48, 15, 25, 64, 63, 28, 10, 18, 52, 13, 51,
       17, 56, 53, 12, 26, 29, 32,  0,  6, 40, 58,  7, 20, 34, 44],
      dtype=int64), 'cur_cost': 101451.0}}]
2025-08-03 16:08:59,700 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:08:59,700 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:08:59,710 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12837.000, 多样性=0.971
2025-08-03 16:08:59,710 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-03 16:08:59,710 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-03 16:08:59,710 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:08:59,716 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03622540249242482, 'best_improvement': -0.03183023872679045}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0013936710936217852}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.016497645603764804, 'recent_improvements': [0.0028935203745250673, -0.014204350289287583, -0.030101770833004544], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 20, 'new_count': 20, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7497607655502393, 'new_diversity': 0.7497607655502393, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-03 16:08:59,721 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-03 16:08:59,721 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-03 16:08:59,721 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-03 16:08:59,721 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:08:59,722 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12837.000, 多样性=0.971
2025-08-03 16:08:59,722 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:08:59,727 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.971
2025-08-03 16:08:59,727 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:08:59,730 - EliteExpert - INFO - 精英解分析完成: 精英解数量=20, 多样性=0.750
2025-08-03 16:08:59,732 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-03 16:08:59,732 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:08:59,732 - LandscapeExpert - INFO - 添加精英解数据: 20个精英解
2025-08-03 16:08:59,732 - LandscapeExpert - INFO - 数据提取成功: 40个路径, 40个适应度值
2025-08-03 16:08:59,990 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: -13747.375, 聚类评分: 0.000, 覆盖率: 0.012, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:08:59,990 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-03 16:08:59,990 - LandscapeExpert - INFO - 提取到 20 个精英解
2025-08-03 16:09:00,002 - visualization.landscape_visualizer - INFO - 已添加 20 个精英解标记
2025-08-03 16:09:00,073 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_5_20250803_160900.html
2025-08-03 16:09:00,119 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_5_20250803_160900.html
2025-08-03 16:09:00,119 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-03 16:09:00,119 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-03 16:09:00,120 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3877秒
2025-08-03 16:09:00,120 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13747.375, 'local_optima_density': 0.25, 'gradient_variance': 839181132.178375, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.012, 'fitness_entropy': 0.6634514191898931, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13747.375)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.012)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754208539.9909966, 'performance_metrics': {}}}
2025-08-03 16:09:00,120 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:09:00,120 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 4)
2025-08-03 16:09:00,120 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 4, 种群大小: 20)
2025-08-03 16:09:00,129 - experts.analysis.individual_state_analyzer - INFO - 种群状态分析完成，分析了 20 个个体
2025-08-03 16:09:00,129 - experts.strategy.enhanced_strategy_expert - INFO - 使用LLM进行策略选择
2025-08-03 16:09:00,130 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:09:10,340 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:12,341 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:09:22,585 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:24,586 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:09:34,795 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:34,795 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 1): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:34,795 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:09:34,795 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:09:34,795 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:09:34,796 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:09:34,796 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-03 16:09:45,003 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:47,005 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-08-03 16:09:57,205 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:09:59,207 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-08-03 16:10:09,429 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:10:09,429 - experts.strategy.enhanced_strategy_expert - INFO - LLM响应 (尝试 2): API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-08-03 16:10:09,429 - experts.strategy.strategy_parser - INFO - 开始解析策略响应，种群大小: 20
2025-08-03 16:10:09,429 - experts.strategy.strategy_parser - ERROR - 策略响应解析失败: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:10:09,429 - experts.strategy.strategy_parser - WARNING - 创建回退策略响应: JSON提取失败: 无法从响应中提取有效的JSON数据
2025-08-03 16:10:09,429 - experts.strategy.enhanced_strategy_expert - WARNING - 解析错误: ['JSON提取失败: 无法从响应中提取有效的JSON数据']
2025-08-03 16:10:09,429 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
