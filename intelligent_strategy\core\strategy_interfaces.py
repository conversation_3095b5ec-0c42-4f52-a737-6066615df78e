"""
Core interfaces for the intelligent strategy selection system.

This module defines the fundamental interfaces that all strategy selection
and execution components must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from .individual_state import IndividualState, IndividualContext
from .data_structures import StrategyAssignment, ExecutionResult, FeedbackReport
from .landscape_analysis import LandscapeFeatures


class StrategySelectionInterface(ABC):
    """
    Abstract interface for strategy selection systems.
    
    This interface defines the contract that all strategy selection
    implementations must follow.
    """
    
    @abstractmethod
    def select_strategies(self, 
                         landscape_analysis: LandscapeFeatures,
                         individual_states: List[IndividualState],
                         population_context: Dict[str, Any]) -> Dict[int, StrategyAssignment]:
        """
        Select strategies for all individuals in the population.
        
        Args:
            landscape_analysis: Current fitness landscape analysis
            individual_states: List of individual states
            population_context: Additional population-level context
            
        Returns:
            Dictionary mapping individual IDs to strategy assignments
        """
        pass
    
    @abstractmethod
    def update_from_feedback(self, feedback: FeedbackReport) -> None:
        """
        Update strategy selection based on execution feedback.
        
        Args:
            feedback: Feedback report from strategy execution
        """
        pass
    
    @abstractmethod
    def get_selection_confidence(self, individual_id: int) -> float:
        """
        Get confidence level for the last strategy selection.
        
        Args:
            individual_id: ID of the individual
            
        Returns:
            Confidence level [0,1]
        """
        pass


class ExecutionFeedbackInterface(ABC):
    """
    Abstract interface for execution feedback collection.
    
    This interface defines how execution results are collected
    and processed for system improvement.
    """
    
    @abstractmethod
    def collect_feedback(self, 
                        execution_results: List[ExecutionResult]) -> FeedbackReport:
        """
        Collect and aggregate execution feedback.
        
        Args:
            execution_results: List of strategy execution results
            
        Returns:
            Aggregated feedback report
        """
        pass
    
    @abstractmethod
    def analyze_performance_trends(self, 
                                 feedback_history: List[FeedbackReport]) -> Dict[str, Any]:
        """
        Analyze performance trends over time.
        
        Args:
            feedback_history: Historical feedback reports
            
        Returns:
            Performance trend analysis
        """
        pass
    
    @abstractmethod
    def identify_improvement_opportunities(self, 
                                         current_feedback: FeedbackReport) -> List[Dict[str, Any]]:
        """
        Identify opportunities for system improvement.
        
        Args:
            current_feedback: Current feedback report
            
        Returns:
            List of improvement opportunities
        """
        pass


class StrategyExecutionInterface(ABC):
    """
    Abstract interface for strategy execution.
    
    This interface defines how strategies are executed on individuals.
    """
    
    @abstractmethod
    def execute_strategy(self, 
                        assignment: StrategyAssignment,
                        context: IndividualContext,
                        fitness_function: Callable) -> ExecutionResult:
        """
        Execute a strategy assignment on an individual.
        
        Args:
            assignment: Strategy assignment to execute
            context: Individual context for execution
            fitness_function: Fitness evaluation function
            
        Returns:
            Execution result
        """
        pass
    
    @abstractmethod
    def execute_batch(self, 
                     assignments: List[StrategyAssignment],
                     contexts: List[IndividualContext],
                     fitness_function: Callable) -> List[ExecutionResult]:
        """
        Execute multiple strategy assignments in batch.
        
        Args:
            assignments: List of strategy assignments
            contexts: List of individual contexts
            fitness_function: Fitness evaluation function
            
        Returns:
            List of execution results
        """
        pass
    
    @abstractmethod
    def get_execution_statistics(self) -> Dict[str, Any]:
        """
        Get execution statistics and performance metrics.
        
        Returns:
            Dictionary of execution statistics
        """
        pass


class LandscapeAnalysisInterface(ABC):
    """
    Abstract interface for landscape analysis.
    
    This interface defines how fitness landscape analysis is performed.
    """
    
    @abstractmethod
    def analyze_global_landscape(self, 
                               individual_states: List[IndividualState]) -> Dict[str, float]:
        """
        Analyze global landscape characteristics.
        
        Args:
            individual_states: Current individual states
            
        Returns:
            Global landscape features
        """
        pass
    
    @abstractmethod
    def analyze_local_landscape(self, 
                              individual_state: IndividualState,
                              fitness_function: Optional[Callable] = None) -> Dict[str, float]:
        """
        Analyze local landscape around an individual.
        
        Args:
            individual_state: Individual state to analyze
            fitness_function: Optional fitness function for sampling
            
        Returns:
            Local landscape features
        """
        pass
    
    @abstractmethod
    def detect_landscape_changes(self, 
                               current_features: LandscapeFeatures,
                               historical_features: List[LandscapeFeatures]) -> Dict[str, Any]:
        """
        Detect significant changes in landscape characteristics.
        
        Args:
            current_features: Current landscape features
            historical_features: Historical landscape features
            
        Returns:
            Change detection results
        """
        pass


@dataclass
class SystemConfiguration:
    """
    Configuration for the intelligent strategy system.
    """
    
    # System parameters
    max_workers: int = 8
    cache_size: int = 1000
    timeout_seconds: int = 300
    
    # Strategy parameters
    exploration_bias: float = 0.5
    exploitation_bias: float = 0.5
    adaptation_rate: float = 0.1
    
    # Performance parameters
    performance_history_length: int = 100
    feedback_aggregation_window: int = 10
    
    # Resource management
    memory_threshold: float = 0.8
    cpu_utilization_target: float = 0.85
    
    def validate(self) -> bool:
        """Validate configuration parameters."""
        if self.max_workers <= 0:
            return False
        if self.cache_size <= 0:
            return False
        if self.timeout_seconds <= 0:
            return False
        if not 0.0 <= self.exploration_bias <= 1.0:
            return False
        if not 0.0 <= self.exploitation_bias <= 1.0:
            return False
        if not 0.0 <= self.adaptation_rate <= 1.0:
            return False
        if self.performance_history_length <= 0:
            return False
        if self.feedback_aggregation_window <= 0:
            return False
        if not 0.0 <= self.memory_threshold <= 1.0:
            return False
        if not 0.0 <= self.cpu_utilization_target <= 1.0:
            return False
        
        return True


class AdaptationInterface(ABC):
    """
    Abstract interface for system adaptation and learning.
    
    This interface defines how the system adapts its behavior
    based on performance feedback and environmental changes.
    """
    
    @abstractmethod
    def adapt_parameters(self, 
                        feedback: FeedbackReport,
                        landscape_changes: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt system parameters based on feedback and landscape changes.
        
        Args:
            feedback: Current feedback report
            landscape_changes: Detected landscape changes
            
        Returns:
            Updated parameter configuration
        """
        pass
    
    @abstractmethod
    def learn_from_history(self, 
                          feedback_history: List[FeedbackReport]) -> Dict[str, Any]:
        """
        Learn patterns from historical performance data.
        
        Args:
            feedback_history: Historical feedback reports
            
        Returns:
            Learned patterns and insights
        """
        pass
    
    @abstractmethod
    def predict_performance(self, 
                          proposed_assignment: StrategyAssignment,
                          context: IndividualContext) -> float:
        """
        Predict expected performance for a strategy assignment.
        
        Args:
            proposed_assignment: Proposed strategy assignment
            context: Individual context
            
        Returns:
            Predicted performance score
        """
        pass
