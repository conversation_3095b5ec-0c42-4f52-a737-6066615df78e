"""
Exploitation strategy implementations for the intelligent strategy selection system.

This module implements various exploitation strategies designed to intensively
search local regions and optimize solutions through local search techniques.
"""

import random
import time
import numpy as np
from typing import List, Dict, Optional, Any, Callable, Tuple
from abc import ABC, abstractmethod
import logging

from ..core.individual_state import IndividualState, IndividualContext
from ..core.data_structures import StrategyAssignment, ExecutionResult, ExploitationParameters, StrategyType, ExecutionStatus
from ..core.strategy_interfaces import StrategyExecutionInterface


class ExploitationStrategy(ABC):
    """
    Abstract base class for exploitation strategies.
    
    All exploitation strategies must implement the execute method
    to perform local search operations on individuals.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the exploitation strategy."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.execution_count = 0
        self.success_count = 0
        self.total_improvement = 0.0
    
    @abstractmethod
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """
        Execute the exploitation strategy.
        
        Args:
            assignment: Strategy assignment with parameters
            context: Individual context for execution
            fitness_function: Fitness evaluation function
            
        Returns:
            Execution result
        """
        pass
    
    def get_success_rate(self) -> float:
        """Get the success rate of this strategy."""
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count
    
    def get_average_improvement(self) -> float:
        """Get the average improvement of this strategy."""
        if self.success_count == 0:
            return 0.0
        return self.total_improvement / self.success_count


class CautiousExploitationStrategy(ExploitationStrategy):
    """
    Cautious exploitation strategy with conservative local search.
    
    This strategy performs careful local search with small steps
    and high patience to avoid disrupting good solutions.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute cautious exploitation strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploitation parameters
            params = assignment.exploitation_params or ExploitationParameters()
            
            # Adjust parameters for cautious exploitation
            step_size = min(0.05, params.step_size)
            patience = max(10, params.patience)
            search_depth = min(5, params.search_depth)
            
            # Perform cautious local search
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            no_improvement_count = 0
            
            for iteration in range(search_depth):
                # Generate small neighborhood
                neighbors = self._generate_cautious_neighborhood(
                    best_solution, step_size, size=5
                )
                
                # Evaluate neighbors
                improved = False
                for neighbor in neighbors:
                    new_fitness = fitness_function(neighbor)
                    operations_count += 1
                    
                    if new_fitness < best_fitness:
                        best_solution = neighbor.copy()
                        best_fitness = new_fitness
                        improved = True
                        no_improvement_count = 0
                        break
                
                if not improved:
                    no_improvement_count += 1
                    if no_improvement_count >= patience:
                        break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
                self.total_improvement += final_improvement
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=iteration + 1,
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'step_size': step_size,
                    'patience_used': no_improvement_count,
                    'early_termination': no_improvement_count >= patience
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in cautious exploitation strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _generate_cautious_neighborhood(self, 
                                      solution: List[int],
                                      step_size: float,
                                      size: int = 5) -> List[List[int]]:
        """Generate a small neighborhood for cautious search."""
        neighbors = []
        n = len(solution)
        
        for _ in range(size):
            neighbor = solution.copy()
            
            # Small local moves only
            if random.random() < 0.7:
                # Adjacent swap
                i = random.randint(0, n-2)
                neighbor[i], neighbor[i+1] = neighbor[i+1], neighbor[i]
            else:
                # Small 2-opt move
                max_range = max(2, int(n * step_size))
                i = random.randint(0, n - max_range)
                j = i + random.randint(1, min(max_range, n - i - 1))
                neighbor[i:j+1] = neighbor[i:j+1][::-1]
            
            neighbors.append(neighbor)
        
        return neighbors


class ModerateExploitationStrategy(ExploitationStrategy):
    """
    Moderate exploitation strategy with balanced local search.
    
    This strategy provides standard local optimization with
    moderate search depth and step sizes.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute moderate exploitation strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploitation parameters
            params = assignment.exploitation_params or ExploitationParameters()
            
            # Perform moderate local search
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Multi-start local search
            num_starts = max(2, int(3 * params.local_search_intensity))
            
            for start in range(num_starts):
                # Start from current best or slight perturbation
                if start == 0:
                    start_solution = best_solution.copy()
                else:
                    start_solution = self._slight_perturbation(best_solution)
                
                # Local search from this starting point
                local_result = self._local_search(
                    start_solution, fitness_function, params
                )
                
                operations_count += local_result['operations']
                
                # Update best if improved
                if local_result['fitness'] < best_fitness:
                    best_solution = local_result['solution']
                    best_fitness = local_result['fitness']
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
                self.total_improvement += final_improvement
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=num_starts,
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'starts_performed': num_starts
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in moderate exploitation strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _slight_perturbation(self, solution: List[int]) -> List[int]:
        """Generate a slight perturbation for multi-start search."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Single small move
        if random.random() < 0.5:
            # Adjacent swap
            i = random.randint(0, n-2)
            perturbed[i], perturbed[i+1] = perturbed[i+1], perturbed[i]
        else:
            # Small reversal
            i = random.randint(0, n-3)
            j = i + random.randint(1, 2)
            perturbed[i:j+1] = perturbed[i:j+1][::-1]
        
        return perturbed
    
    def _local_search(self, 
                     start_solution: List[int],
                     fitness_function: Callable,
                     params: ExploitationParameters) -> Dict[str, Any]:
        """Perform local search from a starting solution."""
        current_solution = start_solution.copy()
        current_fitness = fitness_function(current_solution)
        operations = 1
        
        no_improvement_count = 0
        
        for iteration in range(params.search_depth):
            # Generate neighborhood
            neighbors = self._generate_2opt_neighborhood(
                current_solution, max_neighbors=10
            )
            
            # Find best neighbor
            best_neighbor = None
            best_neighbor_fitness = current_fitness
            
            for neighbor in neighbors:
                neighbor_fitness = fitness_function(neighbor)
                operations += 1
                
                if neighbor_fitness < best_neighbor_fitness:
                    best_neighbor = neighbor
                    best_neighbor_fitness = neighbor_fitness
            
            # Update if improved
            if best_neighbor is not None and best_neighbor_fitness < current_fitness:
                current_solution = best_neighbor
                current_fitness = best_neighbor_fitness
                no_improvement_count = 0
            else:
                no_improvement_count += 1
                if no_improvement_count >= params.patience:
                    break
        
        return {
            'solution': current_solution,
            'fitness': current_fitness,
            'operations': operations
        }
    
    def _generate_2opt_neighborhood(self, 
                                  solution: List[int],
                                  max_neighbors: int = 10) -> List[List[int]]:
        """Generate 2-opt neighborhood."""
        neighbors = []
        n = len(solution)
        
        # Generate random 2-opt moves
        attempts = 0
        while len(neighbors) < max_neighbors and attempts < max_neighbors * 2:
            i, j = sorted(random.sample(range(n), 2))
            if j - i > 1:  # Ensure meaningful reversal
                neighbor = solution.copy()
                neighbor[i:j+1] = neighbor[i:j+1][::-1]
                neighbors.append(neighbor)
            attempts += 1
        
        return neighbors


class AggressiveExploitationStrategy(ExploitationStrategy):
    """
    Aggressive exploitation strategy with intensive local search.
    
    This strategy performs intensive local search with large step sizes
    and deep search to maximize exploitation of promising regions.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute aggressive exploitation strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploitation parameters
            params = assignment.exploitation_params or ExploitationParameters()
            
            # Adjust parameters for aggressive exploitation
            search_depth = max(20, params.search_depth)
            local_search_intensity = max(0.8, params.local_search_intensity)
            
            # Perform aggressive local search
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Intensive 2-opt search
            improved = True
            iteration = 0
            
            while improved and iteration < search_depth:
                improved = False
                iteration += 1
                
                # Systematic 2-opt search
                for i in range(len(best_solution)):
                    for j in range(i + 2, len(best_solution)):
                        # 2-opt move
                        neighbor = best_solution.copy()
                        neighbor[i:j] = neighbor[i:j][::-1]
                        
                        new_fitness = fitness_function(neighbor)
                        operations_count += 1
                        
                        if new_fitness < best_fitness:
                            best_solution = neighbor
                            best_fitness = new_fitness
                            improved = True
                            break
                    
                    if improved:
                        break
                
                # Additional random search if no improvement
                if not improved and iteration < search_depth // 2:
                    random_result = self._random_intensive_search(
                        best_solution, best_fitness, fitness_function, 
                        int(20 * local_search_intensity)
                    )
                    
                    operations_count += random_result['operations']
                    
                    if random_result['improved']:
                        best_solution = random_result['solution']
                        best_fitness = random_result['fitness']
                        improved = True
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
                self.total_improvement += final_improvement
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=iteration,
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'search_depth_used': iteration,
                    'systematic_search_completed': not improved
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in aggressive exploitation strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _random_intensive_search(self, 
                               current_solution: List[int],
                               current_fitness: float,
                               fitness_function: Callable,
                               max_operations: int) -> Dict[str, Any]:
        """Perform random intensive search."""
        best_solution = current_solution.copy()
        best_fitness = current_fitness
        operations = 0
        
        for _ in range(max_operations):
            # Generate random 2-opt or 3-opt move
            if random.random() < 0.8:
                neighbor = self._random_2opt_move(current_solution)
            else:
                neighbor = self._random_3opt_move(current_solution)
            
            new_fitness = fitness_function(neighbor)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = neighbor
                best_fitness = new_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
    
    def _random_2opt_move(self, solution: List[int]) -> List[int]:
        """Generate a random 2-opt move."""
        neighbor = solution.copy()
        n = len(solution)
        
        i, j = sorted(random.sample(range(n), 2))
        if j - i > 1:
            neighbor[i:j] = neighbor[i:j][::-1]
        
        return neighbor
    
    def _random_3opt_move(self, solution: List[int]) -> List[int]:
        """Generate a random 3-opt move."""
        neighbor = solution.copy()
        n = len(solution)
        
        # Select three points
        points = sorted(random.sample(range(n), 3))
        i, j, k = points
        
        # Perform 3-opt reconnection (simplified)
        segment1 = neighbor[:i]
        segment2 = neighbor[i:j]
        segment3 = neighbor[j:k]
        segment4 = neighbor[k:]
        
        # Random reconnection
        reconnection_type = random.randint(0, 2)
        if reconnection_type == 0:
            neighbor = segment1 + segment2[::-1] + segment3 + segment4
        elif reconnection_type == 1:
            neighbor = segment1 + segment3 + segment2 + segment4
        else:
            neighbor = segment1 + segment3[::-1] + segment2[::-1] + segment4
        
        return neighbor


class IntensiveExploitationStrategy(ExploitationStrategy):
    """
    Intensive exploitation strategy with maximum local search intensity.
    
    This strategy performs the most intensive local search possible,
    using all available computational resources for local optimization.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute intensive exploitation strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploitation parameters
            params = assignment.exploitation_params or ExploitationParameters()
            
            # Maximum intensity parameters
            search_depth = max(50, params.search_depth * 2)
            max_time = assignment.time_budget
            
            # Perform intensive local search
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Multi-level intensive search
            search_levels = [
                ('2opt_systematic', 0.4),
                ('2opt_random', 0.3),
                ('3opt_random', 0.2),
                ('or_opt', 0.1)
            ]
            
            for level_name, time_fraction in search_levels:
                level_time_budget = max_time * time_fraction
                level_start_time = time.time()
                
                if level_name == '2opt_systematic':
                    result = self._systematic_2opt_search(
                        best_solution, best_fitness, fitness_function, level_time_budget
                    )
                elif level_name == '2opt_random':
                    result = self._random_2opt_search(
                        best_solution, best_fitness, fitness_function, level_time_budget
                    )
                elif level_name == '3opt_random':
                    result = self._random_3opt_search(
                        best_solution, best_fitness, fitness_function, level_time_budget
                    )
                else:  # or_opt
                    result = self._or_opt_search(
                        best_solution, best_fitness, fitness_function, level_time_budget
                    )
                
                operations_count += result['operations']
                
                if result['improved']:
                    best_solution = result['solution']
                    best_fitness = result['fitness']
                
                # Check time budget
                if time.time() - start_time > max_time:
                    break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
                self.total_improvement += final_improvement
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=len(search_levels),
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'search_levels_completed': len(search_levels),
                    'time_budget_used': execution_time / max_time
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in intensive exploitation strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _systematic_2opt_search(self, 
                              solution: List[int],
                              current_fitness: float,
                              fitness_function: Callable,
                              time_budget: float) -> Dict[str, Any]:
        """Perform systematic 2-opt search."""
        best_solution = solution.copy()
        best_fitness = current_fitness
        operations = 0
        start_time = time.time()
        
        n = len(solution)
        
        for i in range(n):
            for j in range(i + 2, n):
                # Check time budget
                if time.time() - start_time > time_budget:
                    break
                
                # 2-opt move
                neighbor = solution.copy()
                neighbor[i:j] = neighbor[i:j][::-1]
                
                new_fitness = fitness_function(neighbor)
                operations += 1
                
                if new_fitness < best_fitness:
                    best_solution = neighbor
                    best_fitness = new_fitness
            
            if time.time() - start_time > time_budget:
                break
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
    
    def _random_2opt_search(self, 
                          solution: List[int],
                          current_fitness: float,
                          fitness_function: Callable,
                          time_budget: float) -> Dict[str, Any]:
        """Perform random 2-opt search."""
        best_solution = solution.copy()
        best_fitness = current_fitness
        operations = 0
        start_time = time.time()
        
        while time.time() - start_time < time_budget:
            neighbor = self._random_2opt_move(solution)
            new_fitness = fitness_function(neighbor)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = neighbor
                best_fitness = new_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
    
    def _random_3opt_search(self, 
                          solution: List[int],
                          current_fitness: float,
                          fitness_function: Callable,
                          time_budget: float) -> Dict[str, Any]:
        """Perform random 3-opt search."""
        best_solution = solution.copy()
        best_fitness = current_fitness
        operations = 0
        start_time = time.time()
        
        while time.time() - start_time < time_budget:
            neighbor = self._random_3opt_move(solution)
            new_fitness = fitness_function(neighbor)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = neighbor
                best_fitness = new_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
    
    def _or_opt_search(self, 
                     solution: List[int],
                     current_fitness: float,
                     fitness_function: Callable,
                     time_budget: float) -> Dict[str, Any]:
        """Perform Or-opt search."""
        best_solution = solution.copy()
        best_fitness = current_fitness
        operations = 0
        start_time = time.time()
        
        n = len(solution)
        
        # Or-opt moves: relocate segments of length 1, 2, or 3
        for segment_length in [1, 2, 3]:
            for i in range(n - segment_length + 1):
                for j in range(n - segment_length + 1):
                    if abs(i - j) <= segment_length:
                        continue  # Skip overlapping positions
                    
                    # Check time budget
                    if time.time() - start_time > time_budget:
                        break
                    
                    # Or-opt move
                    neighbor = solution.copy()
                    segment = neighbor[i:i+segment_length]
                    del neighbor[i:i+segment_length]
                    
                    # Adjust insertion position
                    insert_pos = j if j < i else j - segment_length
                    for k, element in enumerate(segment):
                        neighbor.insert(insert_pos + k, element)
                    
                    new_fitness = fitness_function(neighbor)
                    operations += 1
                    
                    if new_fitness < best_fitness:
                        best_solution = neighbor
                        best_fitness = new_fitness
                
                if time.time() - start_time > time_budget:
                    break
            
            if time.time() - start_time > time_budget:
                break
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
