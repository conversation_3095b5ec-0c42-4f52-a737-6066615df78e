2025-08-05 09:51:55,982 - __main__ - INFO - composite2_34 开始进化第 1 代
2025-08-05 09:51:55,983 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:51:55,984 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:55,986 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3837.000, 多样性=0.966
2025-08-05 09:51:55,988 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:55,990 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-05 09:51:55,992 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:55,995 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:51:55,995 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:55,995 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:51:55,995 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:51:56,011 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -2332.620, 聚类评分: 0.000, 覆盖率: 0.082, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:56,012 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:51:56,012 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:51:56,012 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 09:51:56,117 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_66_20250805_095156.html
2025-08-05 09:51:56,185 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_66_20250805_095156.html
2025-08-05 09:51:56,185 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 66
2025-08-05 09:51:56,185 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:51:56,185 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1892秒
2025-08-05 09:51:56,186 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 132, 'max_size': 500, 'hits': 0, 'misses': 132, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 435, 'misses': 232, 'hit_rate': 0.6521739130434783, 'evictions': 132, 'ttl': 7200}}
2025-08-05 09:51:56,186 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2332.62, 'local_optima_density': 0.1, 'gradient_variance': 96352155.53159998, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0821, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2332.620)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.082)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358716.011912, 'performance_metrics': {}}}
2025-08-05 09:51:56,186 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:56,186 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:56,186 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:56,186 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:56,187 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,187 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:51:56,187 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,187 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,188 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:56,188 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,188 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,188 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:56,188 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:56,188 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:56,188 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:56,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,189 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6119.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,190 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 6119.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,190 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 6119.00)
2025-08-05 09:51:56,190 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:56,190 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:56,190 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,193 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,193 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,193 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13292.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,193 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13292.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,194 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 13292.00)
2025-08-05 09:51:56,194 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:56,194 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:56,194 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,195 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 09:51:56,195 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,195 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19732.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,196 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 19732.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,196 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 19732.00)
2025-08-05 09:51:56,196 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:56,196 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,196 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,196 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 20491.0
2025-08-05 09:51:56,203 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 09:51:56,203 - ExploitationExpert - INFO - res_population_costs: [3576.0]
2025-08-05 09:51:56,203 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,203 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,203 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 6119.0}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13292.0}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 19732.0}, {'tour': array([ 9, 32, 24, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15],
      dtype=int64), 'cur_cost': 20491.0}, {'tour': array([ 7, 18, 23, 22,  9, 20, 27,  0,  4, 31,  6, 16, 29, 13, 10, 24,  2,
       32, 28, 30, 17,  5, 14, 19,  8, 26, 15, 25, 21,  3, 33, 12, 11,  1],
      dtype=int64), 'cur_cost': 24165.0}, {'tour': array([24, 18,  2, 32,  0, 11,  3, 15, 14, 16, 10,  8,  5, 20, 17, 21, 22,
       27,  1, 31,  9, 30, 33, 13, 25, 28, 23, 12,  7, 26,  6,  4, 29, 19],
      dtype=int64), 'cur_cost': 19403.0}, {'tour': array([ 4, 21, 14,  9, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25838.0}, {'tour': array([15, 26,  3, 17, 21, 10, 14, 25,  5, 22,  4, 33, 20, 23, 19, 16, 27,
        8, 18,  2,  0,  9, 32, 30,  6,  7, 31, 12, 28,  1, 13, 29, 11, 24],
      dtype=int64), 'cur_cost': 25234.0}, {'tour': array([21, 27, 30, 28, 20,  1, 32, 31,  4, 13, 11,  9,  7, 33, 23,  3,  5,
       18, 25,  8, 17, 19, 29,  6, 14, 22,  0, 15, 16, 12, 24, 10,  2, 26],
      dtype=int64), 'cur_cost': 20841.0}, {'tour': array([ 8,  2, 12,  1,  6, 26, 20, 13, 16,  4, 27, 11, 14, 10,  9, 23, 24,
       33,  0, 19, 32, 31, 22, 28, 30,  5,  3, 29, 15, 17, 25,  7, 18, 21],
      dtype=int64), 'cur_cost': 20439.0}]
2025-08-05 09:51:56,205 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:56,206 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 170, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 170, 'cache_hits': 0, 'similarity_calculations': 732, 'cache_hit_rate': 0.0, 'cache_size': 732}}
2025-08-05 09:51:56,206 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 9, 32, 24, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15],
      dtype=int64), 'cur_cost': 20491.0, 'intermediate_solutions': [{'tour': array([31, 19,  4,  1, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 23606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 31, 19,  4, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 25341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1, 31, 19,  4,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 24943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  1, 31, 19, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 24412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 16,  1, 31, 19,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 25342.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,206 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 20491.00)
2025-08-05 09:51:56,206 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:56,207 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:56,207 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,209 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,209 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,209 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14357.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,209 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14357.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,210 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 14357.00)
2025-08-05 09:51:56,210 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:56,210 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:56,210 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,211 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,211 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,211 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4939.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,211 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,211 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 4939.00)
2025-08-05 09:51:56,211 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:56,212 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,212 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,212 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 22827.0
2025-08-05 09:51:56,219 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:56,220 - ExploitationExpert - INFO - res_population_costs: [3576.0, 3575.0, 3575, 3575]
2025-08-05 09:51:56,220 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,221 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,221 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 6119.0}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13292.0}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 19732.0}, {'tour': array([ 9, 32, 24, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15],
      dtype=int64), 'cur_cost': 20491.0}, {'tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14357.0}, {'tour': [0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4939.0}, {'tour': array([23,  4, 33, 15,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24],
      dtype=int64), 'cur_cost': 22827.0}, {'tour': array([15, 26,  3, 17, 21, 10, 14, 25,  5, 22,  4, 33, 20, 23, 19, 16, 27,
        8, 18,  2,  0,  9, 32, 30,  6,  7, 31, 12, 28,  1, 13, 29, 11, 24],
      dtype=int64), 'cur_cost': 25234.0}, {'tour': array([21, 27, 30, 28, 20,  1, 32, 31,  4, 13, 11,  9,  7, 33, 23,  3,  5,
       18, 25,  8, 17, 19, 29,  6, 14, 22,  0, 15, 16, 12, 24, 10,  2, 26],
      dtype=int64), 'cur_cost': 20841.0}, {'tour': array([ 8,  2, 12,  1,  6, 26, 20, 13, 16,  4, 27, 11, 14, 10,  9, 23, 24,
       33,  0, 19, 32, 31, 22, 28, 30,  5,  3, 29, 15, 17, 25,  7, 18, 21],
      dtype=int64), 'cur_cost': 20439.0}]
2025-08-05 09:51:56,223 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:56,223 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 171, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 171, 'cache_hits': 0, 'similarity_calculations': 733, 'cache_hit_rate': 0.0, 'cache_size': 733}}
2025-08-05 09:51:56,224 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([23,  4, 33, 15,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24],
      dtype=int64), 'cur_cost': 22827.0, 'intermediate_solutions': [{'tour': array([14, 21,  4,  9, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 14, 21,  4, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  9, 14, 21,  4, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  9, 14, 21, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 24618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 33,  9, 14, 21, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 26249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,224 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 22827.00)
2025-08-05 09:51:56,224 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:56,224 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:56,225 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,227 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,227 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13299.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,227 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,228 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 13299.00)
2025-08-05 09:51:56,228 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:56,228 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:56,228 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,229 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6159.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,229 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 6159.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,229 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 6159.00)
2025-08-05 09:51:56,229 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:56,229 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:56,229 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,231 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13552.0, 路径长度: 34, 收集中间解: 0
2025-08-05 09:51:56,232 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13552.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,232 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 13552.00)
2025-08-05 09:51:56,232 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:56,232 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:56,233 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 6119.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13292.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 19732.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 32, 24, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15],
      dtype=int64), 'cur_cost': 20491.0, 'intermediate_solutions': [{'tour': array([31, 19,  4,  1, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 23606.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 31, 19,  4, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 25341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  1, 31, 19,  4,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 24943.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  1, 31, 19, 16,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 24412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 16,  1, 31, 19,  0,  7, 10, 29, 26, 21, 32, 18, 12, 30, 14, 33,
       11, 17, 15, 25, 22,  8, 13,  6, 20, 24,  3,  9,  5, 27,  2, 23, 28],
      dtype=int64), 'cur_cost': 25342.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14357.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4939.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([23,  4, 33, 15,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24],
      dtype=int64), 'cur_cost': 22827.0, 'intermediate_solutions': [{'tour': array([14, 21,  4,  9, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 14, 21,  4, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33,  9, 14, 21,  4, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 25838.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  9, 14, 21, 33, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 24618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 33,  9, 14, 21, 30, 23, 18,  0, 27, 24, 29, 10,  5, 22,  3,  8,
       15, 17, 11, 25, 16, 12,  1, 31, 13, 28,  7, 26, 20,  2,  6, 19, 32],
      dtype=int64), 'cur_cost': 26249.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 6159.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13552.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:56,234 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:56,234 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,236 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4939.000, 多样性=0.935
2025-08-05 09:51:56,236 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:51:56,236 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:51:56,236 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:56,236 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.016904266903984774, 'best_improvement': -0.2872035444357571}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0317997293640053}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06117321114272309, 'recent_improvements': [0.00021947158308814607, -0.13742232299950394, 0.12256589386853432], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 3575.0, 'new_best_cost': 3575.0, 'quality_improvement': 0.0, 'old_diversity': 0.20588235294117646, 'new_diversity': 0.20588235294117646, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:56,237 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:51:56,237 - __main__ - INFO - composite2_34 开始进化第 2 代
2025-08-05 09:51:56,237 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:51:56,237 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,238 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4939.000, 多样性=0.935
2025-08-05 09:51:56,238 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:56,239 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.935
2025-08-05 09:51:56,240 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:56,241 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.206
2025-08-05 09:51:56,243 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:51:56,243 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:56,243 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:56,243 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:56,262 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.214, 适应度梯度: 211.914, 聚类评分: 0.000, 覆盖率: 0.084, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:56,262 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:51:56,262 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:56,262 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 09:51:56,362 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_67_20250805_095156.html
2025-08-05 09:51:56,405 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_67_20250805_095156.html
2025-08-05 09:51:56,406 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 67
2025-08-05 09:51:56,406 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:51:56,406 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1642秒
2025-08-05 09:51:56,406 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.21428571428571427, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 211.9142857142858, 'local_optima_density': 0.21428571428571427, 'gradient_variance': 22320163.089795914, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0835, 'fitness_entropy': 0.9134443205612239, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.084)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 211.914)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358716.262463, 'performance_metrics': {}}}
2025-08-05 09:51:56,406 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:56,406 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:56,406 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:56,406 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:56,407 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,407 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:51:56,407 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,408 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,408 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:56,408 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,408 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,408 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:56,408 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:56,409 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:56,409 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:56,409 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,411 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14261.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,412 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14261.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 20, 21, 12, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 19], 'cur_cost': 8641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 16, 9, 15, 13, 14, 10, 12], 'cur_cost': 7341.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 8, 3, 4, 33, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 7801.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,412 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 14261.00)
2025-08-05 09:51:56,412 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:56,412 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:56,412 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,413 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,413 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,414 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,414 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4851.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,414 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4851.0, 'intermediate_solutions': [{'tour': [22, 29, 23, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 31, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13351.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 15, 16, 6, 14, 5, 8, 9, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 12909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 6, 16, 15, 17, 30, 27, 25, 23, 14, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13413.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,414 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 4851.00)
2025-08-05 09:51:56,415 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:56,415 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:56,415 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,415 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,416 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,416 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,416 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,416 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,416 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6877.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,416 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 20, 7, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6877.0, 'intermediate_solutions': [{'tour': [22, 11, 5, 18, 3, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 32, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 20987.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 1, 19, 12, 24, 15, 7, 10, 13, 16, 14], 'cur_cost': 19676.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 0, 21, 6, 20, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 18571.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,417 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 6877.00)
2025-08-05 09:51:56,417 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:56,417 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,417 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,417 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 27112.0
2025-08-05 09:51:56,426 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:56,426 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3576.0]
2025-08-05 09:51:56,426 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,427 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,428 - ExploitationExpert - INFO - populations: [{'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14261.0}, {'tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4851.0}, {'tour': [0, 20, 7, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6877.0}, {'tour': array([30, 24, 11, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7],
      dtype=int64), 'cur_cost': 27112.0}, {'tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14357.0}, {'tour': [0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4939.0}, {'tour': [23, 4, 33, 15, 1, 16, 0, 13, 19, 9, 32, 21, 2, 26, 29, 22, 3, 5, 12, 11, 31, 10, 6, 7, 25, 14, 30, 28, 27, 18, 8, 17, 20, 24], 'cur_cost': 22827.0}, {'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0}, {'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 6159.0}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13552.0}]
2025-08-05 09:51:56,428 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:56,428 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 172, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 172, 'cache_hits': 0, 'similarity_calculations': 735, 'cache_hit_rate': 0.0, 'cache_size': 735}}
2025-08-05 09:51:56,429 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([30, 24, 11, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7],
      dtype=int64), 'cur_cost': 27112.0, 'intermediate_solutions': [{'tour': array([24, 32,  9, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 24, 32,  9, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 22, 24, 32,  9, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 22, 24, 32, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 23, 22, 24, 32, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,429 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 27112.00)
2025-08-05 09:51:56,429 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:56,429 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:56,429 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,431 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,431 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,432 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15448.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,432 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 13, 8, 0, 16, 3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32, 1, 14, 12, 17, 30, 19, 23, 24, 15, 7, 4, 6, 5, 11, 10, 2, 18], 'cur_cost': 15448.0, 'intermediate_solutions': [{'tour': [7, 10, 12, 3, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14369.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 10, 12, 7, 14, 9, 16, 6, 1, 33, 20, 26, 27, 19, 29, 28, 32, 17, 21, 23, 25, 31, 24, 22, 30, 4, 5, 0, 15, 13, 8, 2, 11, 18], 'cur_cost': 15564.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 25, 23, 21, 17, 32, 28, 31, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14321.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,432 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 15448.00)
2025-08-05 09:51:56,432 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:56,433 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:56,433 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,433 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,434 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,434 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4867.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,435 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4867.0, 'intermediate_solutions': [{'tour': [0, 23, 6, 21, 20, 18, 8, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 6, 21, 20, 18, 11, 12, 10, 14, 13, 15, 16, 1, 9, 7, 2, 5, 4, 3, 33, 27, 26, 32, 28, 29, 30, 31, 25, 19, 17, 24, 22, 23], 'cur_cost': 6010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 10, 12, 11], 'cur_cost': 4987.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,435 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 4867.00)
2025-08-05 09:51:56,435 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:56,435 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,435 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,435 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 21331.0
2025-08-05 09:51:56,447 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:51:56,447 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3576.0]
2025-08-05 09:51:56,447 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,449 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,449 - ExploitationExpert - INFO - populations: [{'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14261.0}, {'tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4851.0}, {'tour': [0, 20, 7, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6877.0}, {'tour': array([30, 24, 11, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7],
      dtype=int64), 'cur_cost': 27112.0}, {'tour': [9, 13, 8, 0, 16, 3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32, 1, 14, 12, 17, 30, 19, 23, 24, 15, 7, 4, 6, 5, 11, 10, 2, 18], 'cur_cost': 15448.0}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4867.0}, {'tour': array([ 8,  4,  2, 11, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27],
      dtype=int64), 'cur_cost': 21331.0}, {'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0}, {'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 6159.0}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13552.0}]
2025-08-05 09:51:56,450 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:56,450 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 173, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 173, 'cache_hits': 0, 'similarity_calculations': 738, 'cache_hit_rate': 0.0, 'cache_size': 738}}
2025-08-05 09:51:56,450 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 8,  4,  2, 11, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27],
      dtype=int64), 'cur_cost': 21331.0, 'intermediate_solutions': [{'tour': array([33,  4, 23, 15,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 33,  4, 23,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 15, 33,  4, 23, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 15, 33,  4,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 21823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23,  1, 15, 33,  4, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 22810.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,451 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21331.00)
2025-08-05 09:51:56,451 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:56,451 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:56,451 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,453 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12014.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,454 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12014.0, 'intermediate_solutions': [{'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 17, 24, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 7, 4, 3, 11, 12, 18, 19, 23, 20, 26, 22, 28, 29, 31, 33, 32, 17, 24, 30, 6, 1, 27, 25, 21, 15], 'cur_cost': 13370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 10, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,454 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12014.00)
2025-08-05 09:51:56,454 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:56,454 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:56,454 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,455 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,455 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,456 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4797.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,456 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4797.0, 'intermediate_solutions': [{'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 11, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 8], 'cur_cost': 7118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 17, 23, 22, 6, 10, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 7221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 13, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 14, 12, 11], 'cur_cost': 8355.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,456 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 4797.00)
2025-08-05 09:51:56,456 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:56,456 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:56,456 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,457 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,457 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,458 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6000.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,458 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6000.0, 'intermediate_solutions': [{'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 2, 28, 25, 9, 5, 26, 11, 12, 24, 30], 'cur_cost': 16683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 25, 28, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13581.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 11, 2, 12, 24, 30], 'cur_cost': 14908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,458 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 6000.00)
2025-08-05 09:51:56,458 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:56,458 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:56,459 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14261.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 20, 21, 12, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 19], 'cur_cost': 8641.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 7, 16, 9, 15, 13, 14, 10, 12], 'cur_cost': 7341.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 8, 3, 4, 33, 1, 2, 6, 7, 9, 16, 15, 13, 14, 10, 12], 'cur_cost': 7801.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4851.0, 'intermediate_solutions': [{'tour': [22, 29, 23, 18, 32, 0, 7, 11, 12, 9, 8, 5, 14, 6, 16, 15, 17, 30, 27, 25, 31, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13351.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 15, 16, 6, 14, 5, 8, 9, 17, 30, 27, 25, 23, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 12909.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 29, 31, 18, 32, 0, 7, 11, 12, 9, 8, 5, 6, 16, 15, 17, 30, 27, 25, 23, 14, 20, 24, 26, 21, 19, 33, 28, 1, 3, 4, 2, 13, 10], 'cur_cost': 13413.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 7, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6877.0, 'intermediate_solutions': [{'tour': [22, 11, 5, 18, 3, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 32, 4, 21, 6, 20, 0, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 20987.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 21, 6, 20, 0, 27, 30, 2, 1, 19, 12, 24, 15, 7, 10, 13, 16, 14], 'cur_cost': 19676.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 11, 5, 18, 32, 17, 9, 8, 25, 31, 29, 28, 26, 33, 23, 3, 4, 0, 21, 6, 20, 27, 30, 2, 7, 15, 24, 12, 19, 1, 10, 13, 16, 14], 'cur_cost': 18571.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 24, 11, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7],
      dtype=int64), 'cur_cost': 27112.0, 'intermediate_solutions': [{'tour': array([24, 32,  9, 22, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21612.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 24, 32,  9, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 22, 24, 32,  9, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 22, 24, 32, 23, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 23, 22, 24, 32, 21, 30,  4,  5,  7,  1, 28, 26, 11, 17, 13, 27,
       18,  8, 16, 12, 10,  2,  6, 29, 14, 33,  3, 20, 31, 19, 25,  0, 15]), 'cur_cost': 21219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 13, 8, 0, 16, 3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32, 1, 14, 12, 17, 30, 19, 23, 24, 15, 7, 4, 6, 5, 11, 10, 2, 18], 'cur_cost': 15448.0, 'intermediate_solutions': [{'tour': [7, 10, 12, 3, 14, 9, 0, 5, 4, 30, 22, 24, 31, 25, 23, 21, 17, 32, 28, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14369.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 10, 12, 7, 14, 9, 16, 6, 1, 33, 20, 26, 27, 19, 29, 28, 32, 17, 21, 23, 25, 31, 24, 22, 30, 4, 5, 0, 15, 13, 8, 2, 11, 18], 'cur_cost': 15564.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 10, 12, 7, 14, 9, 0, 5, 4, 30, 22, 24, 25, 23, 21, 17, 32, 28, 31, 29, 19, 27, 26, 20, 33, 1, 6, 16, 15, 13, 8, 2, 11, 18], 'cur_cost': 14321.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4867.0, 'intermediate_solutions': [{'tour': [0, 23, 6, 21, 20, 18, 8, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 6, 21, 20, 18, 11, 12, 10, 14, 13, 15, 16, 1, 9, 7, 2, 5, 4, 3, 33, 27, 26, 32, 28, 29, 30, 31, 25, 19, 17, 24, 22, 23], 'cur_cost': 6010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 0, 8, 6, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 10, 12, 11], 'cur_cost': 4987.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8,  4,  2, 11, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27],
      dtype=int64), 'cur_cost': 21331.0, 'intermediate_solutions': [{'tour': array([33,  4, 23, 15,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23098.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 33,  4, 23,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 15, 33,  4, 23, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 23893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 15, 33,  4,  1, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 21823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23,  1, 15, 33,  4, 16,  0, 13, 19,  9, 32, 21,  2, 26, 29, 22,  3,
        5, 12, 11, 31, 10,  6,  7, 25, 14, 30, 28, 27, 18,  8, 17, 20, 24]), 'cur_cost': 22810.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12014.0, 'intermediate_solutions': [{'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 30, 17, 24, 32, 33, 31, 29, 28, 22, 26, 20, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13299.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 9, 16, 2, 5, 14, 13, 0, 7, 4, 3, 11, 12, 18, 19, 23, 20, 26, 22, 28, 29, 31, 33, 32, 17, 24, 30, 6, 1, 27, 25, 21, 15], 'cur_cost': 13370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 9, 16, 2, 5, 14, 13, 0, 30, 24, 17, 32, 33, 31, 29, 28, 22, 26, 20, 10, 23, 19, 18, 12, 11, 3, 4, 7, 6, 1, 27, 25, 21, 15], 'cur_cost': 13401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4797.0, 'intermediate_solutions': [{'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 11, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 8], 'cur_cost': 7118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 17, 23, 22, 6, 10, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 13, 14, 12, 11], 'cur_cost': 7221.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 6, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 13, 32, 26, 27, 33, 8, 3, 4, 5, 2, 7, 9, 1, 16, 15, 14, 12, 11], 'cur_cost': 8355.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6000.0, 'intermediate_solutions': [{'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 2, 28, 25, 9, 5, 26, 11, 12, 24, 30], 'cur_cost': 16683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 25, 28, 9, 5, 2, 11, 12, 24, 30], 'cur_cost': 13581.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 17, 18, 22, 29, 23, 19, 20, 15, 7, 14, 6, 10, 3, 0, 13, 4, 8, 1, 16, 21, 32, 31, 33, 26, 28, 25, 9, 5, 11, 2, 12, 24, 30], 'cur_cost': 14908.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:56,460 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:56,460 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,462 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4797.000, 多样性=0.908
2025-08-05 09:51:56,462 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:51:56,462 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:51:56,462 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:56,463 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.010423692115499457, 'best_improvement': 0.028750759263008706}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02865129280223633}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07716329495174434, 'recent_improvements': [-0.13742232299950394, 0.12256589386853432, 0.016904266903984774], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 3575.0, 'new_best_cost': 3575.0, 'quality_improvement': 0.0, 'old_diversity': 0.20588235294117646, 'new_diversity': 0.20588235294117646, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:51:56,463 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:51:56,463 - __main__ - INFO - composite2_34 开始进化第 3 代
2025-08-05 09:51:56,463 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:51:56,463 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,464 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4797.000, 多样性=0.908
2025-08-05 09:51:56,464 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:56,465 - PathExpert - INFO - 路径结构分析完成: 公共边数量=6, 路径相似性=0.908
2025-08-05 09:51:56,466 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:56,466 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.206
2025-08-05 09:51:56,468 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:51:56,468 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:56,468 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:51:56,469 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:51:56,488 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: -409.614, 聚类评分: 0.000, 覆盖率: 0.085, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:56,488 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:51:56,488 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:56,488 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 09:51:56,493 - visualization.landscape_visualizer - INFO - 插值约束: 7 个点被约束到最小值 3575.00
2025-08-05 09:51:56,584 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_68_20250805_095156.html
2025-08-05 09:51:56,647 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_68_20250805_095156.html
2025-08-05 09:51:56,647 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 68
2025-08-05 09:51:56,647 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:51:56,647 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1789秒
2025-08-05 09:51:56,647 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -409.6142857142855, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 45405598.84836734, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0845, 'fitness_entropy': 0.8053204460925903, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -409.614)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.085)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358716.4886713, 'performance_metrics': {}}}
2025-08-05 09:51:56,647 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:56,648 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:56,648 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:56,648 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:56,648 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,648 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:51:56,648 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,649 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,649 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:56,649 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,649 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,649 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:56,649 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:56,649 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:56,650 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:56,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,651 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 09:51:56,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,652 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17346.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,652 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17346.0, 'intermediate_solutions': [{'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 3, 14, 5, 12, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 16976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 4, 0, 7, 6, 16, 1, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14263.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 32, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 8, 9], 'cur_cost': 15459.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,652 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 17346.00)
2025-08-05 09:51:56,652 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:56,652 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:56,652 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,653 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6238.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,654 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6238.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 21, 20, 18, 1, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 23, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 1, 4, 3, 8, 33, 27, 26, 32, 28, 29, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6572.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 2, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6684.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,654 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 6238.00)
2025-08-05 09:51:56,654 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:56,655 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:56,655 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,655 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7015.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,656 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7015.0, 'intermediate_solutions': [{'tour': [0, 20, 7, 25, 19, 22, 23, 17, 18, 24, 21, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 7402.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 7, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 21, 7, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6894.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,656 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 7015.00)
2025-08-05 09:51:56,656 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:56,657 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,657 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,657 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 24096.0
2025-08-05 09:51:56,668 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 09:51:56,669 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3576.0, 3575]
2025-08-05 09:51:56,669 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,670 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,670 - ExploitationExpert - INFO - populations: [{'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17346.0}, {'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6238.0}, {'tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7015.0}, {'tour': array([28, 30, 24, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9],
      dtype=int64), 'cur_cost': 24096.0}, {'tour': [9, 13, 8, 0, 16, 3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32, 1, 14, 12, 17, 30, 19, 23, 24, 15, 7, 4, 6, 5, 11, 10, 2, 18], 'cur_cost': 15448.0}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4867.0}, {'tour': [8, 4, 2, 11, 20, 22, 28, 17, 12, 6, 7, 21, 26, 15, 14, 31, 0, 32, 18, 25, 9, 24, 23, 5, 10, 16, 1, 13, 33, 29, 3, 19, 30, 27], 'cur_cost': 21331.0}, {'tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12014.0}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4797.0}, {'tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6000.0}]
2025-08-05 09:51:56,671 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:56,671 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 174, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 174, 'cache_hits': 0, 'similarity_calculations': 742, 'cache_hit_rate': 0.0, 'cache_size': 742}}
2025-08-05 09:51:56,671 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([28, 30, 24, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9],
      dtype=int64), 'cur_cost': 24096.0, 'intermediate_solutions': [{'tour': array([11, 24, 30, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 25799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 11, 24, 30,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 27113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 31, 11, 24, 30, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 26658.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 31, 11, 24,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 26608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  9, 31, 11, 24, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 27455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,671 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 24096.00)
2025-08-05 09:51:56,672 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:56,672 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,672 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,672 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 19872.0
2025-08-05 09:51:56,682 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 09:51:56,683 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3576.0, 3575, 3575.0, 3575, 3575]
2025-08-05 09:51:56,683 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,685 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,685 - ExploitationExpert - INFO - populations: [{'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17346.0}, {'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6238.0}, {'tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7015.0}, {'tour': array([28, 30, 24, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9],
      dtype=int64), 'cur_cost': 24096.0}, {'tour': array([22, 12, 13, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4],
      dtype=int64), 'cur_cost': 19872.0}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4867.0}, {'tour': [8, 4, 2, 11, 20, 22, 28, 17, 12, 6, 7, 21, 26, 15, 14, 31, 0, 32, 18, 25, 9, 24, 23, 5, 10, 16, 1, 13, 33, 29, 3, 19, 30, 27], 'cur_cost': 21331.0}, {'tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12014.0}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4797.0}, {'tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6000.0}]
2025-08-05 09:51:56,686 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:56,686 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 175, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 175, 'cache_hits': 0, 'similarity_calculations': 747, 'cache_hit_rate': 0.0, 'cache_size': 747}}
2025-08-05 09:51:56,687 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([22, 12, 13, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4],
      dtype=int64), 'cur_cost': 19872.0, 'intermediate_solutions': [{'tour': array([ 8, 13,  9,  0, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  8, 13,  9, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  0,  8, 13,  9,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 14558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  0,  8, 13, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 14242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 16,  0,  8, 13,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,687 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 19872.00)
2025-08-05 09:51:56,688 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:56,688 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:56,688 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,690 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,690 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17062.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,692 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [28, 19, 26, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17062.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 6, 27, 33, 8, 3, 4, 5, 26, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 10, 14, 13, 15, 16, 2, 12, 11], 'cur_cost': 6177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 25, 15, 13, 14, 10, 12, 11], 'cur_cost': 6948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,692 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 17062.00)
2025-08-05 09:51:56,692 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:56,692 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,692 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,693 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 24445.0
2025-08-05 09:51:56,706 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:51:56,706 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3576.0, 3575, 3575.0, 3575, 3575, 3575, 3575]
2025-08-05 09:51:56,706 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,709 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,709 - ExploitationExpert - INFO - populations: [{'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17346.0}, {'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6238.0}, {'tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7015.0}, {'tour': array([28, 30, 24, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9],
      dtype=int64), 'cur_cost': 24096.0}, {'tour': array([22, 12, 13, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4],
      dtype=int64), 'cur_cost': 19872.0}, {'tour': [28, 19, 26, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17062.0}, {'tour': array([14,  1, 30, 27, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19],
      dtype=int64), 'cur_cost': 24445.0}, {'tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12014.0}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4797.0}, {'tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 6000.0}]
2025-08-05 09:51:56,711 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:56,711 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 176, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 176, 'cache_hits': 0, 'similarity_calculations': 753, 'cache_hit_rate': 0.0, 'cache_size': 753}}
2025-08-05 09:51:56,712 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([14,  1, 30, 27, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19],
      dtype=int64), 'cur_cost': 24445.0, 'intermediate_solutions': [{'tour': array([ 2,  4,  8, 11, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 21392.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2,  4,  8, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 21845.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 11,  2,  4,  8, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 22162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 11,  2,  4, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 22264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 20, 11,  2,  4, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 23369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,712 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 24445.00)
2025-08-05 09:51:56,712 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:56,712 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:56,712 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,713 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,714 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,715 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6963.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,715 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6963.0, 'intermediate_solutions': [{'tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 28, 19, 31, 27, 26, 21, 30, 22, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 14252.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 23, 25, 29, 5, 3, 33, 20, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12264.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 23, 25, 29, 20, 33, 3, 5, 8, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 4, 1, 7, 16, 24], 'cur_cost': 12016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,715 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 6963.00)
2025-08-05 09:51:56,715 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:56,715 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:56,715 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,716 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,717 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,717 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4824.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,717 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4824.0, 'intermediate_solutions': [{'tour': [0, 7, 5, 20, 21, 19, 22, 12, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 23, 11], 'cur_cost': 7648.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 12, 10, 11], 'cur_cost': 4833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 32, 11], 'cur_cost': 6994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,717 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 4824.00)
2025-08-05 09:51:56,718 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:56,718 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:56,718 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,720 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,721 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15932.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,721 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [9, 10, 5, 11, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15932.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 29, 30, 31, 28, 32, 26, 27, 33], 'cur_cost': 6027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 1, 3, 4, 8, 25, 18, 20, 19, 24, 22, 23, 17, 11, 12, 10, 14, 13, 15, 16, 2, 9, 6, 7, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 7715.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 3, 4, 8, 7, 6, 9, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5898.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,721 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 15932.00)
2025-08-05 09:51:56,721 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:56,721 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:56,725 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17346.0, 'intermediate_solutions': [{'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 3, 14, 5, 12, 2, 1, 16, 6, 7, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 16976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 4, 0, 7, 6, 16, 1, 11, 10, 13, 17, 31, 25, 26, 23, 32, 8, 9], 'cur_cost': 14263.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 30, 33, 21, 28, 24, 29, 18, 20, 27, 19, 15, 12, 14, 5, 3, 2, 1, 16, 6, 7, 32, 0, 4, 11, 10, 13, 17, 31, 25, 26, 23, 8, 9], 'cur_cost': 15459.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 6238.0, 'intermediate_solutions': [{'tour': [0, 5, 2, 21, 20, 18, 1, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 23, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 2, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 1, 4, 3, 8, 33, 27, 26, 32, 28, 29, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6572.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 2, 28, 32, 26, 27, 33, 8, 3, 4, 1, 6, 7, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6684.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7015.0, 'intermediate_solutions': [{'tour': [0, 20, 7, 25, 19, 22, 23, 17, 18, 24, 21, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 7402.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 7, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6875.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 21, 7, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 9, 2, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6894.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 30, 24, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9],
      dtype=int64), 'cur_cost': 24096.0, 'intermediate_solutions': [{'tour': array([11, 24, 30, 31,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 25799.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 11, 24, 30,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 27113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 31, 11, 24, 30, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 26658.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 31, 11, 24,  9, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 26608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  9, 31, 11, 24, 10, 22,  6, 19,  2, 33, 21, 16,  1, 26, 27, 14,
       20, 28,  3,  8, 17, 32, 15, 25,  4, 13,  0, 12, 23, 18,  5, 29,  7]), 'cur_cost': 27455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 12, 13, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4],
      dtype=int64), 'cur_cost': 19872.0, 'intermediate_solutions': [{'tour': array([ 8, 13,  9,  0, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  8, 13,  9, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16,  0,  8, 13,  9,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 14558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  0,  8, 13, 16,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 14242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 16,  0,  8, 13,  3, 29, 27, 33, 25, 21, 20, 31, 22, 26, 28, 32,
        1, 14, 12, 17, 30, 19, 23, 24, 15,  7,  4,  6,  5, 11, 10,  2, 18]), 'cur_cost': 15449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [28, 19, 26, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17062.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 6, 27, 33, 8, 3, 4, 5, 26, 7, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 10, 14, 13, 15, 16, 2, 12, 11], 'cur_cost': 6177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 1, 22, 23, 17, 20, 21, 19, 18, 24, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 2, 16, 25, 15, 13, 14, 10, 12, 11], 'cur_cost': 6948.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  1, 30, 27, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19],
      dtype=int64), 'cur_cost': 24445.0, 'intermediate_solutions': [{'tour': array([ 2,  4,  8, 11, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 21392.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2,  4,  8, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 21845.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([20, 11,  2,  4,  8, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 22162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8, 11,  2,  4, 20, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 22264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 20, 11,  2,  4, 22, 28, 17, 12,  6,  7, 21, 26, 15, 14, 31,  0,
       32, 18, 25,  9, 24, 23,  5, 10, 16,  1, 13, 33, 29,  3, 19, 30, 27]), 'cur_cost': 23369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6963.0, 'intermediate_solutions': [{'tour': [18, 23, 25, 29, 20, 33, 3, 5, 10, 14, 0, 2, 9, 12, 28, 19, 31, 27, 26, 21, 30, 22, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 14252.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 23, 25, 29, 5, 3, 33, 20, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 8, 4, 1, 7, 16, 24], 'cur_cost': 12264.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [18, 23, 25, 29, 20, 33, 3, 5, 8, 10, 14, 0, 2, 9, 12, 22, 19, 31, 27, 26, 21, 30, 28, 32, 17, 11, 13, 15, 6, 4, 1, 7, 16, 24], 'cur_cost': 12016.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4824.0, 'intermediate_solutions': [{'tour': [0, 7, 5, 20, 21, 19, 22, 12, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 23, 11], 'cur_cost': 7648.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 12, 10, 11], 'cur_cost': 4833.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 5, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 26, 27, 33, 8, 3, 4, 1, 2, 6, 9, 16, 15, 13, 14, 10, 12, 32, 11], 'cur_cost': 6994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 5, 11, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15932.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 1, 3, 4, 8, 7, 6, 9, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 29, 30, 31, 28, 32, 26, 27, 33], 'cur_cost': 6027.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 1, 3, 4, 8, 25, 18, 20, 19, 24, 22, 23, 17, 11, 12, 10, 14, 13, 15, 16, 2, 9, 6, 7, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 7715.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 1, 3, 4, 8, 7, 6, 9, 5, 2, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 19, 20, 18, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5898.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:56,725 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:56,725 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,729 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4824.000, 多样性=0.958
2025-08-05 09:51:56,729 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:51:56,729 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:51:56,729 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:56,730 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04347507641122519, 'best_improvement': -0.005628517823639775}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05395683453237428}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.06649479299201688, 'recent_improvements': [0.12256589386853432, 0.016904266903984774, -0.010423692115499457], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 3575.0, 'new_best_cost': 3575.0, 'quality_improvement': 0.0, 'old_diversity': 0.5411764705882353, 'new_diversity': 0.5411764705882353, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:56,731 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:51:56,732 - __main__ - INFO - composite2_34 开始进化第 4 代
2025-08-05 09:51:56,732 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:51:56,732 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:56,733 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4824.000, 多样性=0.958
2025-08-05 09:51:56,733 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:56,735 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.958
2025-08-05 09:51:56,735 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:56,738 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.541
2025-08-05 09:51:56,740 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:51:56,740 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:56,740 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 09:51:56,740 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 09:51:56,789 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.450, 适应度梯度: -3222.780, 聚类评分: 0.000, 覆盖率: 0.086, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:56,789 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:51:56,789 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:56,789 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 09:51:56,795 - visualization.landscape_visualizer - INFO - 插值约束: 25 个点被约束到最小值 3575.00
2025-08-05 09:51:56,893 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_69_20250805_095156.html
2025-08-05 09:51:56,950 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_69_20250805_095156.html
2025-08-05 09:51:56,950 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 69
2025-08-05 09:51:56,951 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:51:56,951 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2104秒
2025-08-05 09:51:56,951 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3222.78, 'local_optima_density': 0.45, 'gradient_variance': 44505499.31559999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.086, 'fitness_entropy': 0.7665474176031084, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3222.780)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.086)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358716.7898664, 'performance_metrics': {}}}
2025-08-05 09:51:56,951 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:56,951 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:56,952 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:56,952 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:56,952 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,952 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:51:56,952 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,953 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,953 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:56,953 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 09:51:56,953 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:56,953 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:56,954 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:56,954 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:56,954 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:56,954 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,956 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,957 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15405.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,957 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 15405.0, 'intermediate_solutions': [{'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 29, 19, 25, 26, 18, 8, 13, 27], 'cur_cost': 18307.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 16, 32, 2, 3, 5, 14, 12, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17412.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 15, 17, 21, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 18423.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,957 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 15405.00)
2025-08-05 09:51:56,957 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:56,957 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:56,957 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,958 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 09:51:56,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20658.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,960 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 20658.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 27, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 5, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 9297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18, 19], 'cur_cost': 6261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 17, 16, 15, 14, 10, 12, 11, 9, 0, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,960 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 20658.00)
2025-08-05 09:51:56,960 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:56,960 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:56,960 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,961 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:56,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,962 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3871.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,962 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3871.0, 'intermediate_solutions': [{'tour': [0, 20, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 19, 21, 18], 'cur_cost': 7015.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 7, 8, 3, 2, 5, 6, 9, 11, 10, 16, 15, 14, 13, 12, 4, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7032.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 17, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18], 'cur_cost': 9006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,962 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 3871.00)
2025-08-05 09:51:56,962 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:56,962 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,963 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,963 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 24477.0
2025-08-05 09:51:56,974 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 09:51:56,974 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3576.0]
2025-08-05 09:51:56,974 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,977 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,977 - ExploitationExpert - INFO - populations: [{'tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 15405.0}, {'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 20658.0}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3871.0}, {'tour': array([22, 29,  2, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16],
      dtype=int64), 'cur_cost': 24477.0}, {'tour': [22, 12, 13, 32, 11, 16, 7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14, 33, 9, 18, 30, 31, 26, 1, 8, 5, 2, 6, 3, 0, 23, 15, 29, 4], 'cur_cost': 19872.0}, {'tour': [28, 19, 26, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17062.0}, {'tour': [14, 1, 30, 27, 21, 4, 24, 12, 20, 2, 18, 25, 13, 11, 5, 9, 16, 7, 26, 3, 23, 22, 17, 0, 32, 10, 33, 15, 8, 28, 31, 29, 6, 19], 'cur_cost': 24445.0}, {'tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6963.0}, {'tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4824.0}, {'tour': [9, 10, 5, 11, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15932.0}]
2025-08-05 09:51:56,978 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:56,978 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 177, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 177, 'cache_hits': 0, 'similarity_calculations': 760, 'cache_hit_rate': 0.0, 'cache_size': 760}}
2025-08-05 09:51:56,979 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([22, 29,  2, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16],
      dtype=int64), 'cur_cost': 24477.0, 'intermediate_solutions': [{'tour': array([24, 30, 28, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 24, 30, 28, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 23835.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 12, 24, 30, 28,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 12, 24, 30, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 25146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 17, 12, 24, 30,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,979 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 24477.00)
2025-08-05 09:51:56,979 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:51:56,979 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,979 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:56,979 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 20132.0
2025-08-05 09:51:56,990 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 09:51:56,990 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3576.0, 3575]
2025-08-05 09:51:56,990 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:56,993 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:56,994 - ExploitationExpert - INFO - populations: [{'tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 15405.0}, {'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 20658.0}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3871.0}, {'tour': array([22, 29,  2, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16],
      dtype=int64), 'cur_cost': 24477.0}, {'tour': array([27, 10, 16,  5,  3,  4, 12, 23,  0,  1, 21, 19, 17, 14, 13, 29, 26,
        8, 32, 25,  6, 22, 24, 15, 11, 33,  7, 28,  9, 31,  2, 30, 18, 20],
      dtype=int64), 'cur_cost': 20132.0}, {'tour': [28, 19, 26, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17062.0}, {'tour': [14, 1, 30, 27, 21, 4, 24, 12, 20, 2, 18, 25, 13, 11, 5, 9, 16, 7, 26, 3, 23, 22, 17, 0, 32, 10, 33, 15, 8, 28, 31, 29, 6, 19], 'cur_cost': 24445.0}, {'tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6963.0}, {'tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4824.0}, {'tour': [9, 10, 5, 11, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15932.0}]
2025-08-05 09:51:56,995 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:56,995 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 178, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 178, 'cache_hits': 0, 'similarity_calculations': 768, 'cache_hit_rate': 0.0, 'cache_size': 768}}
2025-08-05 09:51:56,996 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([27, 10, 16,  5,  3,  4, 12, 23,  0,  1, 21, 19, 17, 14, 13, 29, 26,
        8, 32, 25,  6, 22, 24, 15, 11, 33,  7, 28,  9, 31,  2, 30, 18, 20],
      dtype=int64), 'cur_cost': 20132.0, 'intermediate_solutions': [{'tour': array([13, 12, 22, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32, 13, 12, 22, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 32, 13, 12, 22, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 20237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 32, 13, 12, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 18666.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 11, 32, 13, 12, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:56,996 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 20132.00)
2025-08-05 09:51:56,996 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:56,996 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:56,996 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:56,998 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:56,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,999 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:56,999 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14166.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:56,999 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14166.0, 'intermediate_solutions': [{'tour': [28, 19, 26, 23, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 25, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17522.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 19, 26, 25, 0, 8, 16, 2, 1, 20, 31, 22, 12, 15, 3, 14, 10, 21, 27, 17, 32, 29, 33, 18, 24, 30, 23, 13, 9, 11, 6, 7, 4, 5], 'cur_cost': 17043.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 19, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 26, 1, 2], 'cur_cost': 17495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:56,999 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 14166.00)
2025-08-05 09:51:56,999 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:56,999 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:56,999 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,000 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 22795.0
2025-08-05 09:51:57,012 - ExploitationExpert - INFO - res_population_num: 11
2025-08-05 09:51:57,012 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3576.0, 3575]
2025-08-05 09:51:57,012 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:57,015 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,015 - ExploitationExpert - INFO - populations: [{'tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 15405.0}, {'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 20658.0}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3871.0}, {'tour': array([22, 29,  2, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16],
      dtype=int64), 'cur_cost': 24477.0}, {'tour': array([27, 10, 16,  5,  3,  4, 12, 23,  0,  1, 21, 19, 17, 14, 13, 29, 26,
        8, 32, 25,  6, 22, 24, 15, 11, 33,  7, 28,  9, 31,  2, 30, 18, 20],
      dtype=int64), 'cur_cost': 20132.0}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14166.0}, {'tour': array([12,  3, 16, 28, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0],
      dtype=int64), 'cur_cost': 22795.0}, {'tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 6963.0}, {'tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4824.0}, {'tour': [9, 10, 5, 11, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15932.0}]
2025-08-05 09:51:57,016 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:57,016 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 179, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 179, 'cache_hits': 0, 'similarity_calculations': 777, 'cache_hit_rate': 0.0, 'cache_size': 777}}
2025-08-05 09:51:57,017 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([12,  3, 16, 28, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0],
      dtype=int64), 'cur_cost': 22795.0, 'intermediate_solutions': [{'tour': array([30,  1, 14, 27, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 25371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 30,  1, 14, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 24414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 27, 30,  1, 14,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 23291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 27, 30,  1, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 25211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 21, 27, 30,  1,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 23425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,017 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 22795.00)
2025-08-05 09:51:57,017 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:57,017 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:57,018 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,020 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:57,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,021 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,021 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14250.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,021 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 19, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14250.0, 'intermediate_solutions': [{'tour': [0, 20, 2, 19, 21, 22, 7, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 23, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 10935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 2, 19, 21, 22, 23, 5, 4, 3, 8, 33, 27, 26, 32, 28, 29, 30, 31, 25, 24, 18, 17, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 6, 3, 4, 5, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 7012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,022 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 14250.00)
2025-08-05 09:51:57,022 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:57,022 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:57,022 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,024 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:57,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13501.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,026 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13501.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 9, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 20, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 5990.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 13, 15, 16, 9, 14, 10, 12, 11], 'cur_cost': 6124.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 4, 10, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 12, 11], 'cur_cost': 5141.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,026 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 13501.00)
2025-08-05 09:51:57,026 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:51:57,026 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:51:57,026 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,028 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 09:51:57,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21537.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,029 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [18, 26, 4, 12, 8, 10, 33, 11, 25, 3, 6, 13, 16, 28, 7, 32, 23, 22, 24, 21, 20, 1, 5, 9, 31, 30, 19, 17, 27, 15, 29, 0, 14, 2], 'cur_cost': 21537.0, 'intermediate_solutions': [{'tour': [9, 11, 5, 10, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 10, 5, 11, 4, 3, 29, 25, 17, 13, 2, 15, 16, 0, 32, 30, 28, 26, 24, 33, 19, 23, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 5, 11, 4, 23, 3, 29, 25, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 17945.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,029 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 21537.00)
2025-08-05 09:51:57,029 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:57,029 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:57,031 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 15405.0, 'intermediate_solutions': [{'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 29, 19, 25, 26, 18, 8, 13, 27], 'cur_cost': 18307.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 17, 21, 15, 16, 32, 2, 3, 5, 14, 12, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 17412.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 24, 11, 31, 28, 10, 20, 22, 33, 15, 17, 21, 12, 14, 5, 3, 2, 32, 16, 6, 7, 0, 23, 1, 9, 4, 13, 19, 25, 26, 18, 8, 29, 27], 'cur_cost': 18423.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 20658.0, 'intermediate_solutions': [{'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 27, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 5, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 9297.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 17, 16, 15, 14, 10, 12, 11, 9, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18, 19], 'cur_cost': 6261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 17, 16, 15, 14, 10, 12, 11, 9, 0, 6, 5, 4, 3, 8, 7, 2, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 19, 18], 'cur_cost': 5352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3871.0, 'intermediate_solutions': [{'tour': [0, 20, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 19, 21, 18], 'cur_cost': 7015.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 1, 7, 8, 3, 2, 5, 6, 9, 11, 10, 16, 15, 14, 13, 12, 4, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 17, 20, 21, 18], 'cur_cost': 7032.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 4, 12, 13, 14, 15, 16, 10, 11, 9, 6, 17, 5, 2, 3, 8, 7, 1, 33, 30, 29, 28, 32, 31, 25, 26, 27, 24, 22, 23, 20, 21, 18], 'cur_cost': 9006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 29,  2, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16],
      dtype=int64), 'cur_cost': 24477.0, 'intermediate_solutions': [{'tour': array([24, 30, 28, 12, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 24, 30, 28, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 23835.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 12, 24, 30, 28,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([28, 12, 24, 30, 17,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 25146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([28, 17, 12, 24, 30,  2, 16, 33, 31, 19, 29, 18,  1, 15, 25, 21,  6,
       22, 23, 26,  3,  5,  0,  8, 32, 11,  7, 20, 14,  4, 13, 27, 10,  9]), 'cur_cost': 24636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 10, 16,  5,  3,  4, 12, 23,  0,  1, 21, 19, 17, 14, 13, 29, 26,
        8, 32, 25,  6, 22, 24, 15, 11, 33,  7, 28,  9, 31,  2, 30, 18, 20],
      dtype=int64), 'cur_cost': 20132.0, 'intermediate_solutions': [{'tour': array([13, 12, 22, 32, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19070.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32, 13, 12, 22, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19410.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 32, 13, 12, 22, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 20237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 32, 13, 12, 11, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 18666.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 11, 32, 13, 12, 16,  7, 28, 24, 27, 19, 25, 20, 10, 17, 21, 14,
       33,  9, 18, 30, 31, 26,  1,  8,  5,  2,  6,  3,  0, 23, 15, 29,  4]), 'cur_cost': 19936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14166.0, 'intermediate_solutions': [{'tour': [28, 19, 26, 23, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 25, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 1, 2], 'cur_cost': 17522.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 19, 26, 25, 0, 8, 16, 2, 1, 20, 31, 22, 12, 15, 3, 14, 10, 21, 27, 17, 32, 29, 33, 18, 24, 30, 23, 13, 9, 11, 6, 7, 4, 5], 'cur_cost': 17043.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 19, 25, 0, 8, 16, 5, 4, 7, 6, 11, 9, 13, 23, 30, 24, 18, 33, 29, 32, 17, 27, 21, 10, 14, 3, 15, 12, 22, 31, 20, 26, 1, 2], 'cur_cost': 17495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  3, 16, 28, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0],
      dtype=int64), 'cur_cost': 22795.0, 'intermediate_solutions': [{'tour': array([30,  1, 14, 27, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 25371.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 30,  1, 14, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 24414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 27, 30,  1, 14,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 23291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 27, 30,  1, 21,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 25211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 21, 27, 30,  1,  4, 24, 12, 20,  2, 18, 25, 13, 11,  5,  9, 16,
        7, 26,  3, 23, 22, 17,  0, 32, 10, 33, 15,  8, 28, 31, 29,  6, 19]), 'cur_cost': 23425.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 19, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14250.0, 'intermediate_solutions': [{'tour': [0, 20, 2, 19, 21, 22, 7, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 3, 4, 5, 6, 23, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 10935.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 2, 19, 21, 22, 23, 5, 4, 3, 8, 33, 27, 26, 32, 28, 29, 30, 31, 25, 24, 18, 17, 6, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 8988.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 2, 19, 21, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 6, 3, 4, 5, 7, 9, 1, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 7012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13501.0, 'intermediate_solutions': [{'tour': [0, 3, 4, 9, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 20, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 5990.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 13, 15, 16, 9, 14, 10, 12, 11], 'cur_cost': 6124.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 4, 10, 20, 21, 19, 22, 23, 17, 18, 24, 25, 31, 30, 29, 28, 32, 26, 27, 33, 8, 7, 6, 5, 2, 1, 9, 16, 15, 13, 14, 12, 11], 'cur_cost': 5141.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [18, 26, 4, 12, 8, 10, 33, 11, 25, 3, 6, 13, 16, 28, 7, 32, 23, 22, 24, 21, 20, 1, 5, 9, 31, 30, 19, 17, 27, 15, 29, 0, 14, 2], 'cur_cost': 21537.0, 'intermediate_solutions': [{'tour': [9, 11, 5, 10, 4, 3, 29, 25, 23, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 10, 5, 11, 4, 3, 29, 25, 17, 13, 2, 15, 16, 0, 32, 30, 28, 26, 24, 33, 19, 23, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 15918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 10, 5, 11, 4, 23, 3, 29, 25, 19, 33, 24, 26, 28, 30, 32, 0, 16, 15, 2, 13, 17, 22, 27, 8, 7, 12, 14, 1, 6, 31, 21, 20, 18], 'cur_cost': 17945.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:51:57,032 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:57,032 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,034 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3871.000, 多样性=0.966
2025-08-05 09:51:57,034 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:51:57,034 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:51:57,034 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:57,035 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.0028272904018603706, 'best_improvement': 0.19755389718076286}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008873720136518623}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.030189671657604982, 'recent_improvements': [0.016904266903984774, -0.010423692115499457, -0.04347507641122519], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 3575.0, 'new_best_cost': 3575.0, 'quality_improvement': 0.0, 'old_diversity': 0.5240641711229946, 'new_diversity': 0.5240641711229946, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:57,036 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:51:57,036 - __main__ - INFO - composite2_34 开始进化第 5 代
2025-08-05 09:51:57,036 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:51:57,036 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,037 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=3871.000, 多样性=0.966
2025-08-05 09:51:57,037 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:51:57,039 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-05 09:51:57,039 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:51:57,042 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.524
2025-08-05 09:51:57,043 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:51:57,044 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:51:57,044 - LandscapeExpert - INFO - 添加精英解数据: 11个精英解
2025-08-05 09:51:57,044 - LandscapeExpert - INFO - 数据提取成功: 21个路径, 21个适应度值
2025-08-05 09:51:57,087 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.476, 适应度梯度: -3069.210, 聚类评分: 0.000, 覆盖率: 0.087, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:51:57,088 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:51:57,088 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:51:57,088 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite2_34
2025-08-05 09:51:57,095 - visualization.landscape_visualizer - INFO - 插值约束: 55 个点被约束到最小值 3575.00
2025-08-05 09:51:57,199 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\landscape_composite2_34_iter_70_20250805_095157.html
2025-08-05 09:51:57,240 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite2_34\dashboard_composite2_34_iter_70_20250805_095157.html
2025-08-05 09:51:57,240 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 70
2025-08-05 09:51:57,240 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:51:57,240 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1969秒
2025-08-05 09:51:57,241 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.47619047619047616, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3069.2095238095235, 'local_optima_density': 0.47619047619047616, 'gradient_variance': 33037745.297052164, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0871, 'fitness_entropy': 0.7454143017410054, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3069.210)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.087)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358717.088176, 'performance_metrics': {}}}
2025-08-05 09:51:57,241 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:51:57,241 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:51:57,241 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:51:57,241 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:51:57,242 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:57,242 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:51:57,242 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:57,243 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,243 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:51:57,243 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 09:51:57,243 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:51:57,243 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:51:57,243 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:51:57,244 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:51:57,244 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:51:57,244 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6872.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,246 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 24, 7, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 6872.0, 'intermediate_solutions': [{'tour': [33, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 18], 'cur_cost': 15431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 25, 19, 28, 32, 17, 12, 7, 16, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 16599.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 31, 33], 'cur_cost': 15100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,246 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 6872.00)
2025-08-05 09:51:57,246 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:51:57,246 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:51:57,246 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,247 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:57,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,248 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4891.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,248 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 4, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 1, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4891.0, 'intermediate_solutions': [{'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 29, 0, 31, 12, 6, 15, 23, 18, 24, 1, 16], 'cur_cost': 21074.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 16, 29], 'cur_cost': 20684.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 13, 27, 30, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 20, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 19876.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,248 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 4891.00)
2025-08-05 09:51:57,248 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:51:57,248 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:51:57,249 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,251 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:57,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14219.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,252 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [16, 15, 3, 33, 21, 19, 20, 28, 24, 10, 0, 1, 5, 7, 4, 12, 23, 18, 29, 32, 26, 30, 22, 25, 17, 31, 9, 6, 8, 2, 13, 14, 11, 27], 'cur_cost': 14219.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 28, 31, 30, 29, 25, 32, 26, 27, 33], 'cur_cost': 3976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 30, 31, 25, 19, 18, 20, 21, 24, 22, 29, 28, 32, 26, 27, 33], 'cur_cost': 5152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 23, 22, 24, 21, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,252 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 14219.00)
2025-08-05 09:51:57,252 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 09:51:57,252 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,252 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 24930.0
2025-08-05 09:51:57,262 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:51:57,263 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3575, 3576.0, 3575]
2025-08-05 09:51:57,263 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:57,266 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,266 - ExploitationExpert - INFO - populations: [{'tour': [0, 24, 7, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 6872.0}, {'tour': [0, 8, 4, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 1, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4891.0}, {'tour': [16, 15, 3, 33, 21, 19, 20, 28, 24, 10, 0, 1, 5, 7, 4, 12, 23, 18, 29, 32, 26, 30, 22, 25, 17, 31, 9, 6, 8, 2, 13, 14, 11, 27], 'cur_cost': 14219.0}, {'tour': array([ 2, 28, 30, 14, 27, 29, 20, 22,  9,  6, 24,  1,  7, 18, 33, 23,  3,
       13, 25, 10, 26, 32, 16,  4, 21,  8, 15, 11, 12, 17,  0, 31,  5, 19],
      dtype=int64), 'cur_cost': 24930.0}, {'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 8, 32, 25, 6, 22, 24, 15, 11, 33, 7, 28, 9, 31, 2, 30, 18, 20], 'cur_cost': 20132.0}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14166.0}, {'tour': [12, 3, 16, 28, 32, 9, 14, 8, 26, 31, 7, 1, 33, 29, 6, 30, 10, 27, 17, 24, 13, 19, 18, 4, 21, 23, 25, 15, 11, 22, 5, 20, 2, 0], 'cur_cost': 22795.0}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 19, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14250.0}, {'tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13501.0}, {'tour': [18, 26, 4, 12, 8, 10, 33, 11, 25, 3, 6, 13, 16, 28, 7, 32, 23, 22, 24, 21, 20, 1, 5, 9, 31, 30, 19, 17, 27, 15, 29, 0, 14, 2], 'cur_cost': 21537.0}]
2025-08-05 09:51:57,266 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:51:57,266 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 180, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 180, 'cache_hits': 0, 'similarity_calculations': 787, 'cache_hit_rate': 0.0, 'cache_size': 787}}
2025-08-05 09:51:57,267 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 2, 28, 30, 14, 27, 29, 20, 22,  9,  6, 24,  1,  7, 18, 33, 23,  3,
       13, 25, 10, 26, 32, 16,  4, 21,  8, 15, 11, 12, 17,  0, 31,  5, 19],
      dtype=int64), 'cur_cost': 24930.0, 'intermediate_solutions': [{'tour': array([ 2, 29, 22, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  2, 29, 22, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 28,  2, 29, 22, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 28,  2, 29, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 18, 28,  2, 29, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 23269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,267 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 24930.00)
2025-08-05 09:51:57,267 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:51:57,267 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:51:57,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,269 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 34
2025-08-05 09:51:57,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,270 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20424.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,270 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 3, 21, 19, 15, 16, 0, 17, 9, 25, 31, 4, 29, 28, 32, 26, 33, 6, 30, 24, 13, 7, 23, 20, 22, 5, 14, 11, 18, 1, 12, 27, 2, 10], 'cur_cost': 20424.0, 'intermediate_solutions': [{'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 8, 32, 6, 25, 22, 24, 15, 11, 33, 7, 28, 9, 31, 2, 30, 18, 20], 'cur_cost': 20579.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 8, 32, 25, 6, 22, 24, 15, 11, 33, 7, 28, 9, 31, 2, 20, 18, 30], 'cur_cost': 19620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 32, 25, 6, 22, 24, 15, 11, 33, 7, 28, 8, 9, 31, 2, 30, 18, 20], 'cur_cost': 18383.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,270 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 20424.00)
2025-08-05 09:51:57,270 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 09:51:57,270 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 09:51:57,270 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,271 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 34
2025-08-05 09:51:57,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,272 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5104.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,272 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 15, 13, 2, 5, 6, 7, 8, 3, 4, 1, 9, 16, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5104.0, 'intermediate_solutions': [{'tour': [15, 0, 14, 8, 12, 24, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 2, 1], 'cur_cost': 14380.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 22, 10, 6, 9, 7, 3, 4, 5, 13, 16, 20, 18, 33, 30, 32, 26, 19, 31, 24, 1], 'cur_cost': 14124.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 27, 25, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,272 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 5104.00)
2025-08-05 09:51:57,272 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 09:51:57,272 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,272 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,272 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 22057.0
2025-08-05 09:51:57,283 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 09:51:57,284 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3575, 3576.0, 3575]
2025-08-05 09:51:57,284 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:57,287 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,287 - ExploitationExpert - INFO - populations: [{'tour': [0, 24, 7, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 6872.0}, {'tour': [0, 8, 4, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 1, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4891.0}, {'tour': [16, 15, 3, 33, 21, 19, 20, 28, 24, 10, 0, 1, 5, 7, 4, 12, 23, 18, 29, 32, 26, 30, 22, 25, 17, 31, 9, 6, 8, 2, 13, 14, 11, 27], 'cur_cost': 14219.0}, {'tour': array([ 2, 28, 30, 14, 27, 29, 20, 22,  9,  6, 24,  1,  7, 18, 33, 23,  3,
       13, 25, 10, 26, 32, 16,  4, 21,  8, 15, 11, 12, 17,  0, 31,  5, 19],
      dtype=int64), 'cur_cost': 24930.0}, {'tour': [8, 3, 21, 19, 15, 16, 0, 17, 9, 25, 31, 4, 29, 28, 32, 26, 33, 6, 30, 24, 13, 7, 23, 20, 22, 5, 14, 11, 18, 1, 12, 27, 2, 10], 'cur_cost': 20424.0}, {'tour': [0, 15, 13, 2, 5, 6, 7, 8, 3, 4, 1, 9, 16, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5104.0}, {'tour': array([17, 27, 32, 14, 24, 18, 25,  0, 28,  9, 30, 10, 11, 12,  7,  2, 19,
       33, 26,  5, 22, 20,  3,  6, 16, 13,  8, 21, 15,  1, 23, 31,  4, 29],
      dtype=int64), 'cur_cost': 22057.0}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 19, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14250.0}, {'tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13501.0}, {'tour': [18, 26, 4, 12, 8, 10, 33, 11, 25, 3, 6, 13, 16, 28, 7, 32, 23, 22, 24, 21, 20, 1, 5, 9, 31, 30, 19, 17, 27, 15, 29, 0, 14, 2], 'cur_cost': 21537.0}]
2025-08-05 09:51:57,288 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:57,288 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 181, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 181, 'cache_hits': 0, 'similarity_calculations': 798, 'cache_hit_rate': 0.0, 'cache_size': 798}}
2025-08-05 09:51:57,289 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([17, 27, 32, 14, 24, 18, 25,  0, 28,  9, 30, 10, 11, 12,  7,  2, 19,
       33, 26,  5, 22, 20,  3,  6, 16, 13,  8, 21, 15,  1, 23, 31,  4, 29],
      dtype=int64), 'cur_cost': 22057.0, 'intermediate_solutions': [{'tour': array([16,  3, 12, 28, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22764.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 16,  3, 12, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 24036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 28, 16,  3, 12,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 28, 16,  3, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 24059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 32, 28, 16,  3,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,289 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 22057.00)
2025-08-05 09:51:57,289 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:51:57,289 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:51:57,289 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,291 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:57,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,292 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,292 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,292 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14065.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,292 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [12, 15, 17, 31, 28, 32, 20, 27, 22, 10, 14, 13, 16, 9, 8, 0, 11, 7, 30, 26, 19, 33, 29, 23, 6, 4, 3, 5, 25, 24, 18, 21, 1, 2], 'cur_cost': 14065.0, 'intermediate_solutions': [{'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 2, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 19], 'cur_cost': 16565.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 32, 20, 22, 30, 29, 27, 26, 31, 33, 19, 24, 18, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 13981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 19, 33, 31, 26, 27, 29, 30, 24, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14166.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,292 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 14065.00)
2025-08-05 09:51:57,292 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:51:57,292 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:51:57,293 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:51:57,294 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 34
2025-08-05 09:51:57,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:51:57,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14413.0, 路径长度: 34, 收集中间解: 3
2025-08-05 09:51:57,295 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [32, 20, 21, 31, 17, 14, 16, 10, 3, 15, 0, 11, 6, 7, 9, 2, 33, 29, 28, 19, 22, 18, 27, 24, 13, 5, 4, 1, 8, 12, 25, 30, 26, 23], 'cur_cost': 14413.0, 'intermediate_solutions': [{'tour': [33, 9, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 17, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13723.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 4, 3, 11, 21, 26, 19, 25], 'cur_cost': 13137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 17, 29, 28, 0, 7, 13, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 14, 11, 3, 4], 'cur_cost': 13500.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:51:57,295 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 14413.00)
2025-08-05 09:51:57,296 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:51:57,296 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:51:57,296 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:51:57,296 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 20665.0
2025-08-05 09:51:57,307 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 09:51:57,307 - ExploitationExpert - INFO - res_population_costs: [3575.0, 3575, 3575, 3575, 3575.0, 3575, 3575, 3575, 3575, 3575, 3576.0, 3575, 3575]
2025-08-05 09:51:57,307 - ExploitationExpert - INFO - res_populations: [array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  5,  2,  4,  1,  3,  8],
      dtype=int64), array([ 0, 33, 30, 29, 28, 27, 26, 32, 31, 25, 24, 22, 19, 21, 20, 18, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 20, 18, 19, 21, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0, 33, 30, 29, 27, 28, 26, 32, 31, 25, 24, 22, 21, 19, 18, 20, 23,
       17, 10, 14, 15, 13, 12, 11, 16,  9,  7,  6,  2,  5,  4,  1,  3,  8],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  2,  5,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 14, 13, 11, 12, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 15, 11, 12, 13, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 27, 28, 29, 30, 33],
      dtype=int64), array([ 0,  8,  3,  1,  4,  5,  2,  6,  7,  9, 16, 11, 12, 13, 15, 14, 10,
       17, 23, 18, 20, 21, 19, 22, 24, 25, 31, 32, 26, 28, 27, 29, 30, 33],
      dtype=int64)]
2025-08-05 09:51:57,311 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:51:57,311 - ExploitationExpert - INFO - populations: [{'tour': [0, 24, 7, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 6872.0}, {'tour': [0, 8, 4, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 1, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4891.0}, {'tour': [16, 15, 3, 33, 21, 19, 20, 28, 24, 10, 0, 1, 5, 7, 4, 12, 23, 18, 29, 32, 26, 30, 22, 25, 17, 31, 9, 6, 8, 2, 13, 14, 11, 27], 'cur_cost': 14219.0}, {'tour': array([ 2, 28, 30, 14, 27, 29, 20, 22,  9,  6, 24,  1,  7, 18, 33, 23,  3,
       13, 25, 10, 26, 32, 16,  4, 21,  8, 15, 11, 12, 17,  0, 31,  5, 19],
      dtype=int64), 'cur_cost': 24930.0}, {'tour': [8, 3, 21, 19, 15, 16, 0, 17, 9, 25, 31, 4, 29, 28, 32, 26, 33, 6, 30, 24, 13, 7, 23, 20, 22, 5, 14, 11, 18, 1, 12, 27, 2, 10], 'cur_cost': 20424.0}, {'tour': [0, 15, 13, 2, 5, 6, 7, 8, 3, 4, 1, 9, 16, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5104.0}, {'tour': array([17, 27, 32, 14, 24, 18, 25,  0, 28,  9, 30, 10, 11, 12,  7,  2, 19,
       33, 26,  5, 22, 20,  3,  6, 16, 13,  8, 21, 15,  1, 23, 31,  4, 29],
      dtype=int64), 'cur_cost': 22057.0}, {'tour': [12, 15, 17, 31, 28, 32, 20, 27, 22, 10, 14, 13, 16, 9, 8, 0, 11, 7, 30, 26, 19, 33, 29, 23, 6, 4, 3, 5, 25, 24, 18, 21, 1, 2], 'cur_cost': 14065.0}, {'tour': [32, 20, 21, 31, 17, 14, 16, 10, 3, 15, 0, 11, 6, 7, 9, 2, 33, 29, 28, 19, 22, 18, 27, 24, 13, 5, 4, 1, 8, 12, 25, 30, 26, 23], 'cur_cost': 14413.0}, {'tour': array([23, 24,  4, 29,  0, 17, 33, 28,  3, 21,  7,  5, 12, 16, 10, 13, 32,
        1, 31, 19, 20, 18, 25, 27,  2, 26, 30,  8, 14,  9, 22,  6, 15, 11],
      dtype=int64), 'cur_cost': 20665.0}]
2025-08-05 09:51:57,312 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:51:57,312 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 182, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 182, 'cache_hits': 0, 'similarity_calculations': 810, 'cache_hit_rate': 0.0, 'cache_size': 810}}
2025-08-05 09:51:57,313 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([23, 24,  4, 29,  0, 17, 33, 28,  3, 21,  7,  5, 12, 16, 10, 13, 32,
        1, 31, 19, 20, 18, 25, 27,  2, 26, 30,  8, 14,  9, 22,  6, 15, 11],
      dtype=int64), 'cur_cost': 20665.0, 'intermediate_solutions': [{'tour': array([ 4, 26, 18, 12,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 20477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 26, 18,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 21494.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 12,  4, 26, 18, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 20517.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 12,  4, 26,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 21779.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  8, 12,  4, 26, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 22288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:51:57,313 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 20665.00)
2025-08-05 09:51:57,313 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:51:57,313 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:51:57,315 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 7, 11, 13, 14, 15, 16, 10, 12, 9, 6, 5, 4, 3, 8, 1, 2, 33, 30, 29, 28, 32, 31, 25, 26, 27, 22, 23, 17, 20, 21, 19, 18], 'cur_cost': 6872.0, 'intermediate_solutions': [{'tour': [33, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 18], 'cur_cost': 15431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 25, 19, 28, 32, 17, 12, 7, 16, 21, 22, 0, 9, 2, 1, 8, 4, 11, 24, 31, 33], 'cur_cost': 16599.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 18, 26, 27, 30, 20, 29, 23, 15, 5, 14, 3, 6, 10, 13, 16, 7, 12, 17, 32, 28, 19, 25, 21, 22, 0, 9, 2, 1, 8, 4, 11, 31, 33], 'cur_cost': 15100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 21, 20, 18, 23, 22, 24, 17, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33, 3, 1, 5, 6, 7, 9, 2, 16, 15, 13, 14, 10, 12, 11], 'cur_cost': 4891.0, 'intermediate_solutions': [{'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 29, 0, 31, 12, 6, 15, 23, 18, 24, 1, 16], 'cur_cost': 21074.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 13, 27, 30, 20, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 16, 29], 'cur_cost': 20684.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 13, 27, 30, 2, 10, 33, 11, 14, 9, 5, 4, 3, 8, 28, 7, 17, 32, 26, 19, 20, 25, 21, 1, 0, 31, 12, 6, 15, 23, 18, 24, 29, 16], 'cur_cost': 19876.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [16, 15, 3, 33, 21, 19, 20, 28, 24, 10, 0, 1, 5, 7, 4, 12, 23, 18, 29, 32, 26, 30, 22, 25, 17, 31, 9, 6, 8, 2, 13, 14, 11, 27], 'cur_cost': 14219.0, 'intermediate_solutions': [{'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 28, 31, 30, 29, 25, 32, 26, 27, 33], 'cur_cost': 3976.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 17, 23, 30, 31, 25, 19, 18, 20, 21, 24, 22, 29, 28, 32, 26, 27, 33], 'cur_cost': 5152.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 5, 4, 3, 8, 7, 6, 2, 1, 16, 15, 13, 14, 10, 12, 11, 23, 22, 24, 21, 17, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 3957.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 28, 30, 14, 27, 29, 20, 22,  9,  6, 24,  1,  7, 18, 33, 23,  3,
       13, 25, 10, 26, 32, 16,  4, 21,  8, 15, 11, 12, 17,  0, 31,  5, 19],
      dtype=int64), 'cur_cost': 24930.0, 'intermediate_solutions': [{'tour': array([ 2, 29, 22, 28, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24075.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28,  2, 29, 22, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 28,  2, 29, 22, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 28,  2, 29, 18, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 24477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 18, 28,  2, 29, 25, 20, 26, 10, 33, 24,  0, 23,  6,  5,  1, 27,
       17,  7, 11,  9, 31,  8,  4, 19,  3, 30, 21, 15, 32, 14, 13, 12, 16]), 'cur_cost': 23269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 21, 19, 15, 16, 0, 17, 9, 25, 31, 4, 29, 28, 32, 26, 33, 6, 30, 24, 13, 7, 23, 20, 22, 5, 14, 11, 18, 1, 12, 27, 2, 10], 'cur_cost': 20424.0, 'intermediate_solutions': [{'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 8, 32, 6, 25, 22, 24, 15, 11, 33, 7, 28, 9, 31, 2, 30, 18, 20], 'cur_cost': 20579.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 8, 32, 25, 6, 22, 24, 15, 11, 33, 7, 28, 9, 31, 2, 20, 18, 30], 'cur_cost': 19620.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 10, 16, 5, 3, 4, 12, 23, 0, 1, 21, 19, 17, 14, 13, 29, 26, 32, 25, 6, 22, 24, 15, 11, 33, 7, 28, 8, 9, 31, 2, 30, 18, 20], 'cur_cost': 18383.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 13, 2, 5, 6, 7, 8, 3, 4, 1, 9, 16, 14, 10, 12, 11, 17, 23, 22, 24, 21, 20, 18, 19, 25, 31, 30, 29, 28, 32, 26, 27, 33], 'cur_cost': 5104.0, 'intermediate_solutions': [{'tour': [15, 0, 14, 8, 12, 24, 11, 17, 21, 25, 27, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 2, 1], 'cur_cost': 14380.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 25, 27, 29, 28, 23, 22, 10, 6, 9, 7, 3, 4, 5, 13, 16, 20, 18, 33, 30, 32, 26, 19, 31, 24, 1], 'cur_cost': 14124.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 0, 14, 8, 12, 2, 11, 17, 21, 27, 25, 29, 28, 23, 31, 19, 26, 32, 30, 33, 18, 20, 16, 13, 5, 4, 3, 7, 9, 6, 10, 22, 24, 1], 'cur_cost': 14268.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 27, 32, 14, 24, 18, 25,  0, 28,  9, 30, 10, 11, 12,  7,  2, 19,
       33, 26,  5, 22, 20,  3,  6, 16, 13,  8, 21, 15,  1, 23, 31,  4, 29],
      dtype=int64), 'cur_cost': 22057.0, 'intermediate_solutions': [{'tour': array([16,  3, 12, 28, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22764.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 16,  3, 12, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 24036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 28, 16,  3, 12,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 28, 16,  3, 32,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 24059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 32, 28, 16,  3,  9, 14,  8, 26, 31,  7,  1, 33, 29,  6, 30, 10,
       27, 17, 24, 13, 19, 18,  4, 21, 23, 25, 15, 11, 22,  5, 20,  2,  0]), 'cur_cost': 22321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [12, 15, 17, 31, 28, 32, 20, 27, 22, 10, 14, 13, 16, 9, 8, 0, 11, 7, 30, 26, 19, 33, 29, 23, 6, 4, 3, 5, 25, 24, 18, 21, 1, 2], 'cur_cost': 14065.0, 'intermediate_solutions': [{'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 24, 2, 33, 31, 26, 27, 29, 30, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 19], 'cur_cost': 16565.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 32, 20, 22, 30, 29, 27, 26, 31, 33, 19, 24, 18, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 13981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 11, 6, 7, 13, 0, 14, 3, 5, 16, 10, 18, 19, 33, 31, 26, 27, 29, 30, 24, 22, 20, 32, 17, 15, 8, 4, 1, 25, 23, 28, 21, 9, 2], 'cur_cost': 14166.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [32, 20, 21, 31, 17, 14, 16, 10, 3, 15, 0, 11, 6, 7, 9, 2, 33, 29, 28, 19, 22, 18, 27, 24, 13, 5, 4, 1, 8, 12, 25, 30, 26, 23], 'cur_cost': 14413.0, 'intermediate_solutions': [{'tour': [33, 9, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 17, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 11, 3, 4], 'cur_cost': 13723.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 17, 29, 28, 0, 7, 13, 14, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 4, 3, 11, 21, 26, 19, 25], 'cur_cost': 13137.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 17, 29, 28, 0, 7, 13, 10, 16, 2, 1, 6, 15, 5, 8, 12, 9, 30, 27, 31, 32, 22, 23, 24, 18, 20, 25, 19, 26, 21, 14, 11, 3, 4], 'cur_cost': 13500.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 24,  4, 29,  0, 17, 33, 28,  3, 21,  7,  5, 12, 16, 10, 13, 32,
        1, 31, 19, 20, 18, 25, 27,  2, 26, 30,  8, 14,  9, 22,  6, 15, 11],
      dtype=int64), 'cur_cost': 20665.0, 'intermediate_solutions': [{'tour': array([ 4, 26, 18, 12,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 20477.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4, 26, 18,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 21494.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 12,  4, 26, 18, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 20517.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18, 12,  4, 26,  8, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 21779.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18,  8, 12,  4, 26, 10, 33, 11, 25,  3,  6, 13, 16, 28,  7, 32, 23,
       22, 24, 21, 20,  1,  5,  9, 31, 30, 19, 17, 27, 15, 29,  0, 14,  2]), 'cur_cost': 22288.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:51:57,315 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:51:57,315 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:51:57,318 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=4891.000, 多样性=0.967
2025-08-05 09:51:57,318 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:51:57,318 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:51:57,318 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:51:57,319 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04881609121108437, 'best_improvement': -0.2634978041849651}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0006765899864681978}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.006625491258679912, 'recent_improvements': [-0.010423692115499457, -0.04347507641122519, 0.0028272904018603706], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 3575.0, 'new_best_cost': 3575.0, 'quality_improvement': 0.0, 'old_diversity': 0.48190045248868774, 'new_diversity': 0.48190045248868774, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:51:57,320 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:51:57,325 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite2_34_solution.json
2025-08-05 09:51:57,326 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite2_34_20250805_095157.solution
2025-08-05 09:51:57,326 - __main__ - INFO - 实例执行完成 - 运行时间: 1.35s, 最佳成本: 3575.0
2025-08-05 09:51:57,326 - __main__ - INFO - 实例 composite2_34 处理完成
