2025-08-04 16:59:19,470 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-08-04 16:59:19,470 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-04 16:59:19,472 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:19,474 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.894
2025-08-04 16:59:19,475 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:59:19,476 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.894
2025-08-04 16:59:19,477 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:59:19,507 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-04 16:59:19,508 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:59:19,508 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-04 16:59:19,508 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-04 16:59:19,769 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -25.740, 聚类评分: 0.000, 覆盖率: 0.001, 收敛趋势: 0.000, 多样性: 0.804
2025-08-04 16:59:19,770 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-04 16:59:19,770 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-04 16:59:19,770 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:59:20,177 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记，坐标系统已统一
2025-08-04 16:59:23,888 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_1_20250804_165923.html
2025-08-04 16:59:23,923 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_1_20250804_165923.html
2025-08-04 16:59:23,923 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-04 16:59:23,924 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-04 16:59:23,924 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 4.4165秒
2025-08-04 16:59:23,924 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-04 16:59:23,924 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -25.74, 'local_optima_density': 0.1, 'gradient_variance': 29907.0484, 'cluster_count': 0}, 'population_state': {'diversity': 0.8044444444444444, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.001, 'fitness_entropy': 0.9464119282150146, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -25.740)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.001)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.804)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754297959.7708044, 'performance_metrics': {}}}
2025-08-04 16:59:23,925 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:59:23,925 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:59:23,925 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:59:23,926 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:59:23,926 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 16:59:23,926 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-04 16:59:23,926 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 16:59:23,927 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:23,927 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:59:23,927 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-04 16:59:23,927 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:23,928 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:59:23,928 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-04 16:59:23,928 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:59:23,929 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:59:23,929 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:23,941 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:23,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,170 - ExplorationExpert - INFO - 探索路径生成完成，成本: 984.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,170 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 4, 2, 5, 7, 6, 8, 1], 'cur_cost': 984.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,171 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 984.00)
2025-08-04 16:59:24,171 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:59:24,171 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:59:24,171 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:24,172 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:24,172 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,172 - ExplorationExpert - INFO - 探索路径生成完成，成本: 986.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,173 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 2, 7, 6, 0, 3, 5, 1, 8], 'cur_cost': 986.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,173 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 986.00)
2025-08-04 16:59:24,173 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:59:24,173 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:59:24,173 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:24,174 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:24,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1012.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,174 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 8, 1, 6, 3, 4, 2, 7, 0], 'cur_cost': 1012.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,175 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1012.00)
2025-08-04 16:59:24,175 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:59:24,175 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:59:24,175 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:24,176 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:24,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,176 - ExplorationExpert - INFO - 探索路径生成完成，成本: 813.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,176 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 3, 7, 0, 4, 8, 2, 1, 6], 'cur_cost': 813.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,177 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 813.00)
2025-08-04 16:59:24,177 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:59:24,177 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:59:24,177 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:24,178 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:24,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,178 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1044.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,178 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 6, 2, 3, 1, 0, 7, 5, 8], 'cur_cost': 1044.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,178 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1044.00)
2025-08-04 16:59:24,179 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:59:24,179 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:59:24,179 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:24,179 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:24,180 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:24,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 681.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:24,180 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:24,180 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 681.00)
2025-08-04 16:59:24,181 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-04 16:59:24,181 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:24,183 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:24,186 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 899.0
2025-08-04 16:59:25,208 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 16:59:25,209 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 16:59:25,209 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 16:59:25,209 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:25,209 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 2, 5, 7, 6, 8, 1], 'cur_cost': 984.0}, {'tour': [4, 2, 7, 6, 0, 3, 5, 1, 8], 'cur_cost': 986.0}, {'tour': [5, 8, 1, 6, 3, 4, 2, 7, 0], 'cur_cost': 1012.0}, {'tour': [5, 3, 7, 0, 4, 8, 2, 1, 6], 'cur_cost': 813.0}, {'tour': [4, 6, 2, 3, 1, 0, 7, 5, 8], 'cur_cost': 1044.0}, {'tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0}, {'tour': array([4, 2, 1, 0, 3, 6, 8, 7, 5], dtype=int64), 'cur_cost': 899.0}, {'tour': array([8, 4, 5, 6, 3, 2, 1, 0, 7], dtype=int64), 'cur_cost': 907.0}, {'tour': array([6, 8, 0, 1, 4, 2, 5, 7, 3], dtype=int64), 'cur_cost': 886.0}, {'tour': array([5, 4, 8, 3, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1149.0}]
2025-08-04 16:59:25,211 - ExploitationExpert - INFO - 局部搜索耗时: 1.03秒，最大迭代次数: 10
2025-08-04 16:59:25,211 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-04 16:59:25,211 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([4, 2, 1, 0, 3, 6, 8, 7, 5], dtype=int64), 'cur_cost': 899.0, 'intermediate_solutions': [{'tour': array([0, 8, 5, 4, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 8, 5, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 0, 8, 5, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 0, 8, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 6, 4, 0, 8, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:25,212 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 899.00)
2025-08-04 16:59:25,212 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:59:25,213 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:59:25,213 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:25,214 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:25,214 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:25,214 - ExplorationExpert - INFO - 探索路径生成完成，成本: 965.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:25,214 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 8, 1, 7, 4, 2, 3, 5, 6], 'cur_cost': 965.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:25,215 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 965.00)
2025-08-04 16:59:25,215 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:59:25,215 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:59:25,215 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:25,215 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:25,216 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:25,216 - ExplorationExpert - INFO - 探索路径生成完成，成本: 884.0, 路径长度: 9, 收集中间解: 0
2025-08-04 16:59:25,216 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 6, 4, 2, 5, 3, 7, 8, 0], 'cur_cost': 884.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-04 16:59:25,216 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 884.00)
2025-08-04 16:59:25,217 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 16:59:25,217 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:25,217 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:25,218 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1058.0
2025-08-04 16:59:27,024 - ExploitationExpert - INFO - res_population_num: 1
2025-08-04 16:59:27,024 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-08-04 16:59:27,024 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-08-04 16:59:27,025 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,025 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 2, 5, 7, 6, 8, 1], 'cur_cost': 984.0}, {'tour': [4, 2, 7, 6, 0, 3, 5, 1, 8], 'cur_cost': 986.0}, {'tour': [5, 8, 1, 6, 3, 4, 2, 7, 0], 'cur_cost': 1012.0}, {'tour': [5, 3, 7, 0, 4, 8, 2, 1, 6], 'cur_cost': 813.0}, {'tour': [4, 6, 2, 3, 1, 0, 7, 5, 8], 'cur_cost': 1044.0}, {'tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0}, {'tour': array([4, 2, 1, 0, 3, 6, 8, 7, 5], dtype=int64), 'cur_cost': 899.0}, {'tour': [0, 8, 1, 7, 4, 2, 3, 5, 6], 'cur_cost': 965.0}, {'tour': [1, 6, 4, 2, 5, 3, 7, 8, 0], 'cur_cost': 884.0}, {'tour': array([0, 4, 3, 1, 6, 7, 8, 2, 5], dtype=int64), 'cur_cost': 1058.0}]
2025-08-04 16:59:27,026 - ExploitationExpert - INFO - 局部搜索耗时: 1.81秒，最大迭代次数: 10
2025-08-04 16:59:27,026 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-04 16:59:27,026 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([0, 4, 3, 1, 6, 7, 8, 2, 5], dtype=int64), 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': array([8, 4, 5, 3, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 8, 4, 5, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 8, 4, 5, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 8, 4, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 8, 4, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,027 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1058.00)
2025-08-04 16:59:27,027 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:59:27,027 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:59:27,028 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 2, 5, 7, 6, 8, 1], 'cur_cost': 984.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 7, 6, 0, 3, 5, 1, 8], 'cur_cost': 986.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 1, 6, 3, 4, 2, 7, 0], 'cur_cost': 1012.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 7, 0, 4, 8, 2, 1, 6], 'cur_cost': 813.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 2, 3, 1, 0, 7, 5, 8], 'cur_cost': 1044.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 2, 1, 0, 3, 6, 8, 7, 5], dtype=int64), 'cur_cost': 899.0, 'intermediate_solutions': [{'tour': array([0, 8, 5, 4, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 8, 5, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 4, 0, 8, 5, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 0, 8, 6, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 6, 4, 0, 8, 2, 7, 3, 1], dtype=int64), 'cur_cost': 1062.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 1, 7, 4, 2, 3, 5, 6], 'cur_cost': 965.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 4, 2, 5, 3, 7, 8, 0], 'cur_cost': 884.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 4, 3, 1, 6, 7, 8, 2, 5], dtype=int64), 'cur_cost': 1058.0, 'intermediate_solutions': [{'tour': array([8, 4, 5, 3, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 8, 4, 5, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 8, 4, 5, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1010.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 3, 8, 4, 1, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1011.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 3, 8, 4, 7, 2, 6, 0], dtype=int64), 'cur_cost': 1142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 16:59:27,030 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:59:27,030 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,031 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.889
2025-08-04 16:59:27,031 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-04 16:59:27,031 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-04 16:59:27,032 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:59:27,032 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03074069922222199, 'best_improvement': 0.0}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005524861878452966}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 1, 'new_count': 1, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.0, 'new_diversity': 0.0, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 16:59:27,032 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-04 16:59:27,033 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-08-04 16:59:27,033 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-04 16:59:27,033 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,034 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=681.000, 多样性=0.889
2025-08-04 16:59:27,034 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:59:27,035 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.889
2025-08-04 16:59:27,036 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:59:27,036 - EliteExpert - INFO - 精英解分析完成: 精英解数量=1, 多样性=0.000
2025-08-04 16:59:27,038 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-04 16:59:27,038 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:59:27,038 - LandscapeExpert - INFO - 添加精英解数据: 1个精英解
2025-08-04 16:59:27,039 - LandscapeExpert - INFO - 数据提取成功: 11个路径, 11个适应度值
2025-08-04 16:59:27,043 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.182, 适应度梯度: 1.655, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 0.721
2025-08-04 16:59:27,044 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-04 16:59:27,044 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:59:27,044 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:59:27,047 - visualization.landscape_visualizer - INFO - 插值约束: 11 个点被约束到最小值 680.00
2025-08-04 16:59:27,050 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:59:27,128 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_2_20250804_165927.html
2025-08-04 16:59:27,164 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_2_20250804_165927.html
2025-08-04 16:59:27,164 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-04 16:59:27,164 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-04 16:59:27,165 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1260秒
2025-08-04 16:59:27,165 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.18181818181818182, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1.6545454545454534, 'local_optima_density': 0.18181818181818182, 'gradient_variance': 26842.146115702482, 'cluster_count': 0}, 'population_state': {'diversity': 0.7206611570247934, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0021, 'fitness_entropy': 0.960058249314534, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.721)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.655)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754297967.0440183, 'performance_metrics': {}}}
2025-08-04 16:59:27,166 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:59:27,166 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:59:27,166 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:59:27,166 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:59:27,167 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,167 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-04 16:59:27,167 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,167 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,168 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:59:27,168 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,168 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,169 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:59:27,169 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 16:59:27,170 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:59:27,170 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:59:27,170 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,171 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,172 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1131.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,172 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 3, 4, 7, 0, 8, 6, 2, 1], 'cur_cost': 1131.0, 'intermediate_solutions': [{'tour': [0, 8, 4, 2, 5, 7, 6, 3, 1], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 2, 5, 1, 8, 6, 7], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 4, 2, 5, 7, 8, 1], 'cur_cost': 977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,172 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1131.00)
2025-08-04 16:59:27,172 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:59:27,173 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:59:27,173 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,173 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,173 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,174 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 872.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,174 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 6, 3, 0, 5, 1, 8], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 0, 6, 7, 2, 5, 1, 8], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 7, 0, 3, 5, 1, 8], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,175 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 872.00)
2025-08-04 16:59:27,175 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:59:27,175 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:59:27,175 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,176 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,177 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 885.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,177 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 7, 5, 6, 0, 4, 3, 8, 2], 'cur_cost': 885.0, 'intermediate_solutions': [{'tour': [1, 8, 5, 6, 3, 4, 2, 7, 0], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 1, 6, 3, 4, 2, 0, 7], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 1, 6, 3, 4, 2, 7], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,177 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 885.00)
2025-08-04 16:59:27,178 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:59:27,178 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:59:27,178 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,178 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,178 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,179 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,179 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1074.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,179 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 2, 7, 6, 1, 4, 3, 8, 0], 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': [5, 3, 4, 0, 7, 8, 2, 1, 6], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 7, 4, 8, 2, 1, 6], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,180 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1074.00)
2025-08-04 16:59:27,180 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 16:59:27,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,180 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,181 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1255.0
2025-08-04 16:59:27,245 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 16:59:27,246 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 16:59:27,246 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:59:27,246 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,247 - ExploitationExpert - INFO - populations: [{'tour': [5, 3, 4, 7, 0, 8, 6, 2, 1], 'cur_cost': 1131.0}, {'tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0}, {'tour': [1, 7, 5, 6, 0, 4, 3, 8, 2], 'cur_cost': 885.0}, {'tour': [5, 2, 7, 6, 1, 4, 3, 8, 0], 'cur_cost': 1074.0}, {'tour': array([4, 7, 0, 3, 2, 5, 1, 8, 6], dtype=int64), 'cur_cost': 1255.0}, {'tour': [1, 4, 2, 8, 3, 7, 5, 6, 0], 'cur_cost': 681.0}, {'tour': [4, 2, 1, 0, 3, 6, 8, 7, 5], 'cur_cost': 899.0}, {'tour': [0, 8, 1, 7, 4, 2, 3, 5, 6], 'cur_cost': 965.0}, {'tour': [1, 6, 4, 2, 5, 3, 7, 8, 0], 'cur_cost': 884.0}, {'tour': [0, 4, 3, 1, 6, 7, 8, 2, 5], 'cur_cost': 1058.0}]
2025-08-04 16:59:27,247 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:59:27,247 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-04 16:59:27,248 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([4, 7, 0, 3, 2, 5, 1, 8, 6], dtype=int64), 'cur_cost': 1255.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 3, 1, 0, 7, 5, 8]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 6, 4, 1, 0, 7, 5, 8]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 6, 4, 0, 7, 5, 8]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 2, 6, 1, 0, 7, 5, 8]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 3, 2, 6, 0, 7, 5, 8]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,249 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1255.00)
2025-08-04 16:59:27,249 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:59:27,249 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:59:27,249 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,250 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 741.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,252 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 8, 3, 7, 2, 6, 0], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 3, 7, 6, 5, 0], 'cur_cost': 722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 8, 3, 5, 7, 6, 0], 'cur_cost': 694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,253 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 741.00)
2025-08-04 16:59:27,254 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:59:27,254 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:59:27,254 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,255 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,256 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,256 - ExplorationExpert - INFO - 探索路径生成完成，成本: 819.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,256 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0, 'intermediate_solutions': [{'tour': [4, 2, 1, 5, 3, 6, 8, 7, 0], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 1, 0, 3, 6, 8, 7, 5], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 2, 1, 0, 3, 6, 7, 5], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,257 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 819.00)
2025-08-04 16:59:27,257 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:59:27,257 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:59:27,257 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,258 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,258 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,259 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1003.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,259 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [0, 8, 5, 7, 4, 2, 3, 1, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 5, 3, 2, 4, 7, 1, 6], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 7, 4, 2, 3, 5, 0, 6], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,259 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1003.00)
2025-08-04 16:59:27,260 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:59:27,260 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:59:27,260 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,260 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,260 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,261 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,261 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,261 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,261 - ExplorationExpert - INFO - 探索路径生成完成，成本: 774.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,261 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 2, 5, 3, 7, 8, 0], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 6, 1, 3, 7, 8, 0], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 2, 5, 3, 7, 8, 0], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,262 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 774.00)
2025-08-04 16:59:27,262 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 16:59:27,262 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,262 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,263 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 949.0
2025-08-04 16:59:27,322 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 16:59:27,322 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 16:59:27,322 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:59:27,323 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,323 - ExploitationExpert - INFO - populations: [{'tour': [5, 3, 4, 7, 0, 8, 6, 2, 1], 'cur_cost': 1131.0}, {'tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0}, {'tour': [1, 7, 5, 6, 0, 4, 3, 8, 2], 'cur_cost': 885.0}, {'tour': [5, 2, 7, 6, 1, 4, 3, 8, 0], 'cur_cost': 1074.0}, {'tour': array([4, 7, 0, 3, 2, 5, 1, 8, 6], dtype=int64), 'cur_cost': 1255.0}, {'tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0}, {'tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': array([7, 3, 0, 6, 1, 5, 4, 2, 8], dtype=int64), 'cur_cost': 949.0}]
2025-08-04 16:59:27,324 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 16:59:27,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-04 16:59:27,325 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([7, 3, 0, 6, 1, 5, 4, 2, 8], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([3, 4, 0, 1, 6, 7, 8, 2, 5]), 'cur_cost': 870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 4, 0, 6, 7, 8, 2, 5]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 3, 4, 0, 7, 8, 2, 5]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 4, 6, 7, 8, 2, 5]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 6, 1, 3, 4, 7, 8, 2, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,325 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 949.00)
2025-08-04 16:59:27,325 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:59:27,325 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:59:27,326 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 4, 7, 0, 8, 6, 2, 1], 'cur_cost': 1131.0, 'intermediate_solutions': [{'tour': [0, 8, 4, 2, 5, 7, 6, 3, 1], 'cur_cost': 920.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 4, 2, 5, 1, 8, 6, 7], 'cur_cost': 1120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 6, 4, 2, 5, 7, 8, 1], 'cur_cost': 977.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 6, 3, 0, 5, 1, 8], 'cur_cost': 1055.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 3, 0, 6, 7, 2, 5, 1, 8], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 7, 0, 3, 5, 1, 8], 'cur_cost': 1032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 5, 6, 0, 4, 3, 8, 2], 'cur_cost': 885.0, 'intermediate_solutions': [{'tour': [1, 8, 5, 6, 3, 4, 2, 7, 0], 'cur_cost': 878.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 1, 6, 3, 4, 2, 0, 7], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 8, 1, 6, 3, 4, 2, 7], 'cur_cost': 1012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 7, 6, 1, 4, 3, 8, 0], 'cur_cost': 1074.0, 'intermediate_solutions': [{'tour': [5, 3, 4, 0, 7, 8, 2, 1, 6], 'cur_cost': 865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 0, 7, 4, 8, 2, 1, 6], 'cur_cost': 886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 0, 4, 2, 1, 6], 'cur_cost': 788.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 7, 0, 3, 2, 5, 1, 8, 6], dtype=int64), 'cur_cost': 1255.0, 'intermediate_solutions': [{'tour': array([2, 6, 4, 3, 1, 0, 7, 5, 8]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 2, 6, 4, 1, 0, 7, 5, 8]), 'cur_cost': 969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 3, 2, 6, 4, 0, 7, 5, 8]), 'cur_cost': 1167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 2, 6, 1, 0, 7, 5, 8]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 3, 2, 6, 0, 7, 5, 8]), 'cur_cost': 1056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0, 'intermediate_solutions': [{'tour': [1, 4, 5, 8, 3, 7, 2, 6, 0], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 2, 8, 3, 7, 6, 5, 0], 'cur_cost': 722.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 2, 8, 3, 5, 7, 6, 0], 'cur_cost': 694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0, 'intermediate_solutions': [{'tour': [4, 2, 1, 5, 3, 6, 8, 7, 0], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 1, 0, 3, 6, 8, 7, 5], 'cur_cost': 899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 2, 1, 0, 3, 6, 7, 5], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0, 'intermediate_solutions': [{'tour': [0, 8, 5, 7, 4, 2, 3, 1, 6], 'cur_cost': 986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 5, 3, 2, 4, 7, 1, 6], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 7, 4, 2, 3, 5, 0, 6], 'cur_cost': 1026.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 2, 5, 3, 7, 8, 0], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 2, 4, 6, 1, 3, 7, 8, 0], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 2, 5, 3, 7, 8, 0], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 3, 0, 6, 1, 5, 4, 2, 8], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([3, 4, 0, 1, 6, 7, 8, 2, 5]), 'cur_cost': 870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 3, 4, 0, 6, 7, 8, 2, 5]), 'cur_cost': 1074.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([6, 1, 3, 4, 0, 7, 8, 2, 5]), 'cur_cost': 1003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 3, 4, 6, 7, 8, 2, 5]), 'cur_cost': 1046.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 6, 1, 3, 4, 7, 8, 2, 5]), 'cur_cost': 1083.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 16:59:27,328 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:59:27,329 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,330 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=741.000, 多样性=0.874
2025-08-04 16:59:27,330 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-04 16:59:27,330 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-04 16:59:27,330 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:59:27,330 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09776176762843995, 'best_improvement': -0.0881057268722467}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01666666666666657}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.8888888888888888, 'new_diversity': 0.8888888888888888, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-04 16:59:27,331 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-04 16:59:27,331 - __main__ - INFO - simple1_9 开始进化第 3 代
2025-08-04 16:59:27,332 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-04 16:59:27,332 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,332 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=741.000, 多样性=0.874
2025-08-04 16:59:27,333 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:59:27,333 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.874
2025-08-04 16:59:27,334 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:59:27,334 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.889
2025-08-04 16:59:27,336 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-04 16:59:27,337 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:59:27,337 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-04 16:59:27,337 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-04 16:59:27,343 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 45.383, 聚类评分: 0.000, 覆盖率: 0.003, 收敛趋势: 0.000, 多样性: 0.667
2025-08-04 16:59:27,343 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-04 16:59:27,344 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:59:27,344 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:59:27,347 - visualization.landscape_visualizer - INFO - 插值约束: 125 个点被约束到最小值 680.00
2025-08-04 16:59:27,350 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:59:27,433 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_3_20250804_165927.html
2025-08-04 16:59:27,495 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_3_20250804_165927.html
2025-08-04 16:59:27,496 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-04 16:59:27,496 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-04 16:59:27,496 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1600秒
2025-08-04 16:59:27,497 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 45.383333333333326, 'local_optima_density': 0.25, 'gradient_variance': 42307.85638888888, 'cluster_count': 0}, 'population_state': {'diversity': 0.6666666666666666, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0032, 'fitness_entropy': 0.9731973151785931, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.003)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 45.383)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754297967.343552, 'performance_metrics': {}}}
2025-08-04 16:59:27,497 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:59:27,498 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:59:27,498 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:59:27,498 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:59:27,498 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:59:27,498 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-04 16:59:27,499 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:59:27,499 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,499 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:59:27,499 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-04 16:59:27,500 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,500 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:59:27,500 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 5} (总数: 2, 保护比例: 0.20)
2025-08-04 16:59:27,501 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-04 16:59:27,501 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,501 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,502 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 1029.0
2025-08-04 16:59:27,563 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 16:59:27,563 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 16:59:27,563 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:59:27,564 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,564 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 7, 2, 6, 5, 4, 8, 1], dtype=int64), 'cur_cost': 1029.0}, {'tour': [4, 7, 5, 8, 3, 0, 1, 6, 2], 'cur_cost': 872.0}, {'tour': [1, 7, 5, 6, 0, 4, 3, 8, 2], 'cur_cost': 885.0}, {'tour': [5, 2, 7, 6, 1, 4, 3, 8, 0], 'cur_cost': 1074.0}, {'tour': [4, 7, 0, 3, 2, 5, 1, 8, 6], 'cur_cost': 1255.0}, {'tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0}, {'tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [7, 3, 0, 6, 1, 5, 4, 2, 8], 'cur_cost': 949.0}]
2025-08-04 16:59:27,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 16:59:27,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-04 16:59:27,565 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([0, 3, 7, 2, 6, 5, 4, 8, 1], dtype=int64), 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': array([4, 3, 5, 7, 0, 8, 6, 2, 1]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 4, 3, 5, 0, 8, 6, 2, 1]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 4, 3, 5, 8, 6, 2, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 4, 3, 0, 8, 6, 2, 1]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 4, 3, 8, 6, 2, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,566 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1029.00)
2025-08-04 16:59:27,567 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:59:27,567 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:59:27,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,569 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,570 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,571 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1160.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,571 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 3, 1, 8, 7, 5, 4, 0, 6], 'cur_cost': 1160.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 8, 3, 0, 5, 6, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 3, 0, 1, 6, 2, 8], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,571 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1160.00)
2025-08-04 16:59:27,571 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:59:27,572 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:59:27,572 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,572 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,573 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1033.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,574 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 1, 8, 4, 3, 5, 7, 0, 6], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [1, 7, 0, 6, 5, 4, 3, 8, 2], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 6, 0, 4, 3, 2, 8], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 4, 6, 3, 8, 2], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,574 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1033.00)
2025-08-04 16:59:27,574 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 16:59:27,575 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,575 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,575 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 949.0
2025-08-04 16:59:27,628 - ExploitationExpert - INFO - res_population_num: 2
2025-08-04 16:59:27,628 - ExploitationExpert - INFO - res_population_costs: [680.0, 680]
2025-08-04 16:59:27,629 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-08-04 16:59:27,630 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,630 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 7, 2, 6, 5, 4, 8, 1], dtype=int64), 'cur_cost': 1029.0}, {'tour': [2, 3, 1, 8, 7, 5, 4, 0, 6], 'cur_cost': 1160.0}, {'tour': [2, 1, 8, 4, 3, 5, 7, 0, 6], 'cur_cost': 1033.0}, {'tour': array([4, 1, 6, 0, 2, 7, 5, 8, 3], dtype=int64), 'cur_cost': 949.0}, {'tour': [4, 7, 0, 3, 2, 5, 1, 8, 6], 'cur_cost': 1255.0}, {'tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0}, {'tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [7, 3, 0, 6, 1, 5, 4, 2, 8], 'cur_cost': 949.0}]
2025-08-04 16:59:27,631 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 16:59:27,631 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-04 16:59:27,632 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([4, 1, 6, 0, 2, 7, 5, 8, 3], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([7, 2, 5, 6, 1, 4, 3, 8, 0]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 2, 5, 1, 4, 3, 8, 0]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 7, 2, 5, 4, 3, 8, 0]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 6, 7, 2, 1, 4, 3, 8, 0]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 6, 7, 2, 4, 3, 8, 0]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,633 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 949.00)
2025-08-04 16:59:27,633 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-04 16:59:27,633 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,633 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,634 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 913.0
2025-08-04 16:59:27,694 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:27,694 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:27,694 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:27,695 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,695 - ExploitationExpert - INFO - populations: [{'tour': array([0, 3, 7, 2, 6, 5, 4, 8, 1], dtype=int64), 'cur_cost': 1029.0}, {'tour': [2, 3, 1, 8, 7, 5, 4, 0, 6], 'cur_cost': 1160.0}, {'tour': [2, 1, 8, 4, 3, 5, 7, 0, 6], 'cur_cost': 1033.0}, {'tour': array([4, 1, 6, 0, 2, 7, 5, 8, 3], dtype=int64), 'cur_cost': 949.0}, {'tour': array([2, 1, 4, 7, 5, 0, 6, 3, 8], dtype=int64), 'cur_cost': 913.0}, {'tour': [1, 6, 7, 3, 5, 8, 4, 2, 0], 'cur_cost': 741.0}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0}, {'tour': [5, 3, 2, 1, 7, 6, 8, 4, 0], 'cur_cost': 1003.0}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0}, {'tour': [7, 3, 0, 6, 1, 5, 4, 2, 8], 'cur_cost': 949.0}]
2025-08-04 16:59:27,696 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 16:59:27,696 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-04 16:59:27,697 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([2, 1, 4, 7, 5, 0, 6, 3, 8], dtype=int64), 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': array([0, 7, 4, 3, 2, 5, 1, 8, 6]), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 7, 4, 2, 5, 1, 8, 6]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 7, 4, 5, 1, 8, 6]), 'cur_cost': 1264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 7, 2, 5, 1, 8, 6]), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 3, 0, 7, 5, 1, 8, 6]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,697 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 913.00)
2025-08-04 16:59:27,698 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:59:27,698 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:59:27,698 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,698 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,700 - ExplorationExpert - INFO - 探索路径生成完成，成本: 788.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,700 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 2, 7, 3, 5, 6, 0, 1, 8], 'cur_cost': 788.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 5, 2, 4, 8, 0], 'cur_cost': 825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 5, 3, 8, 4, 2, 0], 'cur_cost': 731.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 3, 5, 4, 2, 0, 8], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,700 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 788.00)
2025-08-04 16:59:27,701 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:59:27,701 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:59:27,701 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,702 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,702 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,702 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,703 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,703 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,703 - ExplorationExpert - INFO - 探索路径生成完成，成本: 777.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,703 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0, 'intermediate_solutions': [{'tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 7, 2, 4, 8, 5, 0], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,704 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 777.00)
2025-08-04 16:59:27,704 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:59:27,704 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:59:27,704 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,705 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,705 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,706 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,706 - ExplorationExpert - INFO - 探索路径生成完成，成本: 941.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,706 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 8, 4, 1, 5, 3, 6, 0, 7], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [5, 3, 6, 1, 7, 2, 8, 4, 0], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 2, 3, 6, 8, 4, 0], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 1, 7, 6, 8, 0, 4], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,706 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 941.00)
2025-08-04 16:59:27,707 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:59:27,707 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:59:27,707 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,707 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,707 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,708 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,708 - ExplorationExpert - INFO - 探索路径生成完成，成本: 812.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,709 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 8, 4, 1, 0, 3, 5, 6, 7], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [5, 8, 4, 2, 0, 6, 7, 3, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 8, 5, 1, 7, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,709 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 812.00)
2025-08-04 16:59:27,709 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-04 16:59:27,709 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-04 16:59:27,709 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,710 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,710 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,711 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1098.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,711 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 8, 4, 5, 1, 3, 7, 0, 6], 'cur_cost': 1098.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 6, 1, 5, 4, 2, 3], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 0, 6, 1, 5, 4, 8, 2], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 1, 5, 4, 2, 8, 3], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,711 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1098.00)
2025-08-04 16:59:27,711 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:59:27,712 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:59:27,713 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 3, 7, 2, 6, 5, 4, 8, 1], dtype=int64), 'cur_cost': 1029.0, 'intermediate_solutions': [{'tour': array([4, 3, 5, 7, 0, 8, 6, 2, 1]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 4, 3, 5, 0, 8, 6, 2, 1]), 'cur_cost': 1134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 4, 3, 5, 8, 6, 2, 1]), 'cur_cost': 970.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 7, 4, 3, 0, 8, 6, 2, 1]), 'cur_cost': 1180.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 0, 7, 4, 3, 8, 6, 2, 1]), 'cur_cost': 1138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 1, 8, 7, 5, 4, 0, 6], 'cur_cost': 1160.0, 'intermediate_solutions': [{'tour': [4, 7, 1, 8, 3, 0, 5, 6, 2], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 7, 3, 0, 1, 6, 2], 'cur_cost': 855.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 5, 3, 0, 1, 6, 2, 8], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 8, 4, 3, 5, 7, 0, 6], 'cur_cost': 1033.0, 'intermediate_solutions': [{'tour': [1, 7, 0, 6, 5, 4, 3, 8, 2], 'cur_cost': 955.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 5, 6, 0, 4, 3, 2, 8], 'cur_cost': 994.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 5, 0, 4, 6, 3, 8, 2], 'cur_cost': 974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 6, 0, 2, 7, 5, 8, 3], dtype=int64), 'cur_cost': 949.0, 'intermediate_solutions': [{'tour': array([7, 2, 5, 6, 1, 4, 3, 8, 0]), 'cur_cost': 1019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 2, 5, 1, 4, 3, 8, 0]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 6, 7, 2, 5, 4, 3, 8, 0]), 'cur_cost': 1024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 6, 7, 2, 1, 4, 3, 8, 0]), 'cur_cost': 990.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 1, 6, 7, 2, 4, 3, 8, 0]), 'cur_cost': 1015.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 4, 7, 5, 0, 6, 3, 8], dtype=int64), 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': array([0, 7, 4, 3, 2, 5, 1, 8, 6]), 'cur_cost': 1200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 7, 4, 2, 5, 1, 8, 6]), 'cur_cost': 1112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 3, 0, 7, 4, 5, 1, 8, 6]), 'cur_cost': 1264.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 7, 2, 5, 1, 8, 6]), 'cur_cost': 1263.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 2, 3, 0, 7, 5, 1, 8, 6]), 'cur_cost': 1114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 7, 3, 5, 6, 0, 1, 8], 'cur_cost': 788.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 5, 2, 4, 8, 0], 'cur_cost': 825.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 7, 5, 3, 8, 4, 2, 0], 'cur_cost': 731.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 7, 3, 5, 4, 2, 0, 8], 'cur_cost': 959.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0, 'intermediate_solutions': [{'tour': [1, 3, 6, 8, 4, 2, 7, 5, 0], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 3, 7, 2, 4, 8, 5, 0], 'cur_cost': 829.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 8, 4, 2, 7, 5, 0], 'cur_cost': 819.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 4, 1, 5, 3, 6, 0, 7], 'cur_cost': 941.0, 'intermediate_solutions': [{'tour': [5, 3, 6, 1, 7, 2, 8, 4, 0], 'cur_cost': 944.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 2, 3, 6, 8, 4, 0], 'cur_cost': 1031.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 1, 7, 6, 8, 0, 4], 'cur_cost': 1059.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 4, 1, 0, 3, 5, 6, 7], 'cur_cost': 812.0, 'intermediate_solutions': [{'tour': [5, 8, 4, 2, 0, 6, 7, 3, 1], 'cur_cost': 945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 4, 8, 5, 1, 7, 3, 6], 'cur_cost': 924.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 2, 0, 1, 7, 3, 6], 'cur_cost': 774.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 4, 5, 1, 3, 7, 0, 6], 'cur_cost': 1098.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 6, 1, 5, 4, 2, 3], 'cur_cost': 1011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 0, 6, 1, 5, 4, 8, 2], 'cur_cost': 1033.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 0, 6, 1, 5, 4, 2, 8, 3], 'cur_cost': 901.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-04 16:59:27,715 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:59:27,715 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,717 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=777.000, 多样性=0.822
2025-08-04 16:59:27,717 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-04 16:59:27,717 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-04 16:59:27,717 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:59:27,718 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.009143301662045163, 'best_improvement': -0.048582995951417005}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.05932203389830505}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:59:27,718 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-04 16:59:27,719 - __main__ - INFO - simple1_9 开始进化第 4 代
2025-08-04 16:59:27,719 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-04 16:59:27,720 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:27,720 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=777.000, 多样性=0.822
2025-08-04 16:59:27,721 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:59:27,721 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.822
2025-08-04 16:59:27,722 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:59:27,722 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 16:59:27,724 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-04 16:59:27,724 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:59:27,724 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 16:59:27,724 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 16:59:27,730 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.385, 适应度梯度: 30.892, 聚类评分: 0.000, 覆盖率: 0.004, 收敛趋势: 0.000, 多样性: 0.572
2025-08-04 16:59:27,730 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-04 16:59:27,731 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:59:27,731 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:59:27,735 - visualization.landscape_visualizer - INFO - 插值约束: 22 个点被约束到最小值 680.00
2025-08-04 16:59:27,740 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:59:27,816 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_4_20250804_165927.html
2025-08-04 16:59:27,852 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_4_20250804_165927.html
2025-08-04 16:59:27,852 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-04 16:59:27,853 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-04 16:59:27,853 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1290秒
2025-08-04 16:59:27,853 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.38461538461538464, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 30.892307692307686, 'local_optima_density': 0.38461538461538464, 'gradient_variance': 26756.662248520704, 'cluster_count': 0}, 'population_state': {'diversity': 0.571992110453649, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0043, 'fitness_entropy': 0.9479479190038742, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.004)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 30.892)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754297967.7305193, 'performance_metrics': {}}}
2025-08-04 16:59:27,854 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:59:27,854 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:59:27,854 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:59:27,854 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:59:27,855 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,855 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-04 16:59:27,855 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,855 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,856 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:59:27,856 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:27,856 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:27,856 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:59:27,857 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 6} (总数: 2, 保护比例: 0.20)
2025-08-04 16:59:27,857 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:59:27,857 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:59:27,857 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,857 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:27,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,858 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 893.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,859 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 8, 4, 6, 7, 5, 3, 1], 'cur_cost': 893.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 2, 6, 4, 5, 8, 1], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 2, 8, 4, 5, 6, 1], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 7, 2, 6, 5, 4, 8, 1], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,859 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 893.00)
2025-08-04 16:59:27,859 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-04 16:59:27,859 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,860 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,860 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 997.0
2025-08-04 16:59:27,922 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:27,922 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:27,922 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:27,923 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,923 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 8, 4, 6, 7, 5, 3, 1], 'cur_cost': 893.0}, {'tour': array([5, 3, 1, 2, 8, 6, 7, 0, 4], dtype=int64), 'cur_cost': 997.0}, {'tour': [2, 1, 8, 4, 3, 5, 7, 0, 6], 'cur_cost': 1033.0}, {'tour': [4, 1, 6, 0, 2, 7, 5, 8, 3], 'cur_cost': 949.0}, {'tour': [2, 1, 4, 7, 5, 0, 6, 3, 8], 'cur_cost': 913.0}, {'tour': [4, 2, 7, 3, 5, 6, 0, 1, 8], 'cur_cost': 788.0}, {'tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0}, {'tour': [2, 8, 4, 1, 5, 3, 6, 0, 7], 'cur_cost': 941.0}, {'tour': [2, 8, 4, 1, 0, 3, 5, 6, 7], 'cur_cost': 812.0}, {'tour': [2, 8, 4, 5, 1, 3, 7, 0, 6], 'cur_cost': 1098.0}]
2025-08-04 16:59:27,924 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒，最大迭代次数: 10
2025-08-04 16:59:27,924 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-04 16:59:27,924 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 3, 1, 2, 8, 6, 7, 0, 4], dtype=int64), 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': array([1, 3, 2, 8, 7, 5, 4, 0, 6]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 3, 2, 7, 5, 4, 0, 6]), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 1, 3, 2, 5, 4, 0, 6]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 1, 3, 7, 5, 4, 0, 6]), 'cur_cost': 1085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 8, 1, 3, 5, 4, 0, 6]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,925 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 997.00)
2025-08-04 16:59:27,925 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-04 16:59:27,925 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:27,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:27,926 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 927.0
2025-08-04 16:59:27,989 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:27,990 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:27,990 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:27,991 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:27,991 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 8, 4, 6, 7, 5, 3, 1], 'cur_cost': 893.0}, {'tour': array([5, 3, 1, 2, 8, 6, 7, 0, 4], dtype=int64), 'cur_cost': 997.0}, {'tour': array([6, 1, 0, 8, 2, 7, 4, 5, 3], dtype=int64), 'cur_cost': 927.0}, {'tour': [4, 1, 6, 0, 2, 7, 5, 8, 3], 'cur_cost': 949.0}, {'tour': [2, 1, 4, 7, 5, 0, 6, 3, 8], 'cur_cost': 913.0}, {'tour': [4, 2, 7, 3, 5, 6, 0, 1, 8], 'cur_cost': 788.0}, {'tour': [2, 0, 1, 7, 3, 5, 6, 8, 4], 'cur_cost': 777.0}, {'tour': [2, 8, 4, 1, 5, 3, 6, 0, 7], 'cur_cost': 941.0}, {'tour': [2, 8, 4, 1, 0, 3, 5, 6, 7], 'cur_cost': 812.0}, {'tour': [2, 8, 4, 5, 1, 3, 7, 0, 6], 'cur_cost': 1098.0}]
2025-08-04 16:59:27,992 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:59:27,992 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-04 16:59:27,992 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([6, 1, 0, 8, 2, 7, 4, 5, 3], dtype=int64), 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': array([8, 1, 2, 4, 3, 5, 7, 0, 6]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 2, 3, 5, 7, 0, 6]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 1, 2, 5, 7, 0, 6]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 8, 1, 3, 5, 7, 0, 6]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 4, 8, 1, 5, 7, 0, 6]), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:27,993 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 927.00)
2025-08-04 16:59:27,993 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-04 16:59:27,993 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-04 16:59:27,994 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,994 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:27,994 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,995 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1046.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,995 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 5, 0, 3, 7, 6, 8, 4, 1], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [6, 1, 4, 0, 2, 7, 5, 8, 3], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 4, 0, 2, 7, 5, 8, 3], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 0, 2, 7, 5, 8, 3], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,996 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1046.00)
2025-08-04 16:59:27,996 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:59:27,996 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:59:27,996 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,997 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:27,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,997 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,998 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:27,998 - ExplorationExpert - INFO - 探索路径生成完成，成本: 744.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:27,998 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 8, 5, 6, 3, 7, 0, 1, 2], 'cur_cost': 744.0, 'intermediate_solutions': [{'tour': [2, 5, 4, 7, 1, 0, 6, 3, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 0, 5, 7, 4, 1, 8], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 5, 0, 6, 1, 3, 8], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:27,998 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 744.00)
2025-08-04 16:59:27,998 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-04 16:59:27,999 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-04 16:59:27,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:27,999 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:28,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,000 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,000 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1048.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,001 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 4, 6, 5, 1, 2, 3, 7], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 3, 5, 1, 0, 6, 8], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 3, 5, 6, 0, 8, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 7, 3, 5, 6, 1, 0, 8], 'cur_cost': 771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,001 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1048.00)
2025-08-04 16:59:28,001 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:59:28,002 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:59:28,002 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,002 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:28,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 910.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,004 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 6, 3, 5, 7, 8, 4], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 7, 1, 0, 6, 8, 4], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 7, 3, 5, 6, 8, 4, 1], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,005 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 910.00)
2025-08-04 16:59:28,005 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:59:28,005 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:59:28,005 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 785.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,007 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 8, 3, 7, 0, 1, 4, 2, 6], 'cur_cost': 785.0, 'intermediate_solutions': [{'tour': [2, 8, 4, 1, 3, 5, 6, 0, 7], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 4, 1, 3, 5, 6, 0, 7], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 4, 1, 5, 7, 3, 6, 0], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,007 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 785.00)
2025-08-04 16:59:28,007 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:59:28,007 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:59:28,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,008 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:28,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,008 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,009 - ExplorationExpert - INFO - 探索路径生成完成，成本: 849.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,009 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [2, 8, 4, 1, 0, 6, 5, 3, 7], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 4, 1, 0, 3, 5, 7, 6], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 1, 0, 4, 3, 5, 6, 7], 'cur_cost': 868.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,009 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 849.00)
2025-08-04 16:59:28,010 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 16:59:28,010 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:28,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:28,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1159.0
2025-08-04 16:59:28,073 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:28,074 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:28,074 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:28,075 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:28,075 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 8, 4, 6, 7, 5, 3, 1], 'cur_cost': 893.0}, {'tour': array([5, 3, 1, 2, 8, 6, 7, 0, 4], dtype=int64), 'cur_cost': 997.0}, {'tour': array([6, 1, 0, 8, 2, 7, 4, 5, 3], dtype=int64), 'cur_cost': 927.0}, {'tour': [2, 5, 0, 3, 7, 6, 8, 4, 1], 'cur_cost': 1046.0}, {'tour': [4, 8, 5, 6, 3, 7, 0, 1, 2], 'cur_cost': 744.0}, {'tour': [0, 8, 4, 6, 5, 1, 2, 3, 7], 'cur_cost': 1048.0}, {'tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0}, {'tour': [5, 8, 3, 7, 0, 1, 4, 2, 6], 'cur_cost': 785.0}, {'tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0}, {'tour': array([0, 7, 4, 3, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1159.0}]
2025-08-04 16:59:28,076 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:59:28,076 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-04 16:59:28,077 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([0, 7, 4, 3, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': array([4, 8, 2, 5, 1, 3, 7, 0, 6]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 8, 2, 1, 3, 7, 0, 6]), 'cur_cost': 951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 4, 8, 2, 3, 7, 0, 6]), 'cur_cost': 1012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 4, 8, 1, 3, 7, 0, 6]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 5, 4, 8, 3, 7, 0, 6]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:28,077 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1159.00)
2025-08-04 16:59:28,078 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:59:28,078 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:59:28,079 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 8, 4, 6, 7, 5, 3, 1], 'cur_cost': 893.0, 'intermediate_solutions': [{'tour': [0, 3, 7, 2, 6, 4, 5, 8, 1], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 7, 2, 8, 4, 5, 6, 1], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 7, 2, 6, 5, 4, 8, 1], 'cur_cost': 1029.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 3, 1, 2, 8, 6, 7, 0, 4], dtype=int64), 'cur_cost': 997.0, 'intermediate_solutions': [{'tour': array([1, 3, 2, 8, 7, 5, 4, 0, 6]), 'cur_cost': 1014.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 3, 2, 7, 5, 4, 0, 6]), 'cur_cost': 1155.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 8, 1, 3, 2, 5, 4, 0, 6]), 'cur_cost': 1154.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 8, 1, 3, 7, 5, 4, 0, 6]), 'cur_cost': 1085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 8, 1, 3, 5, 4, 0, 6]), 'cur_cost': 1132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 1, 0, 8, 2, 7, 4, 5, 3], dtype=int64), 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': array([8, 1, 2, 4, 3, 5, 7, 0, 6]), 'cur_cost': 944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 1, 2, 3, 5, 7, 0, 6]), 'cur_cost': 1021.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 4, 8, 1, 2, 5, 7, 0, 6]), 'cur_cost': 1055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 8, 1, 3, 5, 7, 0, 6]), 'cur_cost': 994.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 3, 4, 8, 1, 5, 7, 0, 6]), 'cur_cost': 1148.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 0, 3, 7, 6, 8, 4, 1], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [6, 1, 4, 0, 2, 7, 5, 8, 3], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 4, 0, 2, 7, 5, 8, 3], 'cur_cost': 916.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 6, 0, 2, 7, 5, 8, 3], 'cur_cost': 949.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 5, 6, 3, 7, 0, 1, 2], 'cur_cost': 744.0, 'intermediate_solutions': [{'tour': [2, 5, 4, 7, 1, 0, 6, 3, 8], 'cur_cost': 952.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 6, 0, 5, 7, 4, 1, 8], 'cur_cost': 1022.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 7, 5, 0, 6, 1, 3, 8], 'cur_cost': 907.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 4, 6, 5, 1, 2, 3, 7], 'cur_cost': 1048.0, 'intermediate_solutions': [{'tour': [4, 2, 7, 3, 5, 1, 0, 6, 8], 'cur_cost': 848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 7, 3, 5, 6, 0, 8, 1], 'cur_cost': 894.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 7, 3, 5, 6, 1, 0, 8], 'cur_cost': 771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0, 'intermediate_solutions': [{'tour': [2, 0, 1, 6, 3, 5, 7, 8, 4], 'cur_cost': 758.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 7, 1, 0, 6, 8, 4], 'cur_cost': 850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 7, 3, 5, 6, 8, 4, 1], 'cur_cost': 883.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 7, 0, 1, 4, 2, 6], 'cur_cost': 785.0, 'intermediate_solutions': [{'tour': [2, 8, 4, 1, 3, 5, 6, 0, 7], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 4, 1, 3, 5, 6, 0, 7], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 4, 1, 5, 7, 3, 6, 0], 'cur_cost': 923.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0, 'intermediate_solutions': [{'tour': [2, 8, 4, 1, 0, 6, 5, 3, 7], 'cur_cost': 764.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 4, 1, 0, 3, 5, 7, 6], 'cur_cost': 872.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 1, 0, 4, 3, 5, 6, 7], 'cur_cost': 868.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 7, 4, 3, 5, 1, 8, 6, 2], dtype=int64), 'cur_cost': 1159.0, 'intermediate_solutions': [{'tour': array([4, 8, 2, 5, 1, 3, 7, 0, 6]), 'cur_cost': 1089.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 8, 2, 1, 3, 7, 0, 6]), 'cur_cost': 951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 5, 4, 8, 2, 3, 7, 0, 6]), 'cur_cost': 1012.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 4, 8, 1, 3, 7, 0, 6]), 'cur_cost': 1181.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 5, 4, 8, 3, 7, 0, 6]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 16:59:28,081 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:59:28,081 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:28,083 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=744.000, 多样性=0.904
2025-08-04 16:59:28,083 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-04 16:59:28,083 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-04 16:59:28,083 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:59:28,084 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.01942682661920413, 'best_improvement': 0.04247104247104247}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.09909909909909911}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.010798698780088415, 'recent_improvements': [0.03074069922222199, -0.09776176762843995, 0.009143301662045163], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:59:28,085 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-04 16:59:28,085 - __main__ - INFO - simple1_9 开始进化第 5 代
2025-08-04 16:59:28,086 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-04 16:59:28,087 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:28,088 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=744.000, 多样性=0.904
2025-08-04 16:59:28,088 - PathExpert - INFO - 开始路径结构分析
2025-08-04 16:59:28,089 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-04 16:59:28,089 - EliteExpert - INFO - 开始精英解分析
2025-08-04 16:59:28,090 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.630
2025-08-04 16:59:28,092 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-04 16:59:28,092 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-04 16:59:28,092 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-04 16:59:28,093 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-04 16:59:28,100 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.308, 适应度梯度: 1.369, 聚类评分: 0.000, 覆盖率: 0.005, 收敛趋势: 0.000, 多样性: 0.609
2025-08-04 16:59:28,100 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-04 16:59:28,100 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-04 16:59:28,101 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple1_9
2025-08-04 16:59:28,128 - visualization.landscape_visualizer - INFO - 插值约束: 117 个点被约束到最小值 680.00
2025-08-04 16:59:28,133 - visualization.landscape_visualizer - INFO - 已添加 5 个精英解标记，坐标系统已统一
2025-08-04 16:59:28,238 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\landscape_simple1_9_iter_5_20250804_165928.html
2025-08-04 16:59:28,291 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple1_9\dashboard_simple1_9_iter_5_20250804_165928.html
2025-08-04 16:59:28,291 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-04 16:59:28,292 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-04 16:59:28,292 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2001秒
2025-08-04 16:59:28,292 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3076923076923077, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 1.3692307692307746, 'local_optima_density': 0.3076923076923077, 'gradient_variance': 30164.44828402367, 'cluster_count': 0}, 'population_state': {'diversity': 0.6094674556213018, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0053, 'fitness_entropy': 0.9549080284177108, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.005)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.369)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754297968.1002018, 'performance_metrics': {}}}
2025-08-04 16:59:28,293 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-04 16:59:28,294 - StrategyExpert - INFO - 开始策略分配分析
2025-08-04 16:59:28,294 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-04 16:59:28,294 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-04 16:59:28,295 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:28,295 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-04 16:59:28,295 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:28,295 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:28,295 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-04 16:59:28,296 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-04 16:59:28,296 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-04 16:59:28,297 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-04 16:59:28,297 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 7} (总数: 2, 保护比例: 0.20)
2025-08-04 16:59:28,297 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-04 16:59:28,298 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-04 16:59:28,298 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,298 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:28,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,300 - ExplorationExpert - INFO - 探索路径生成完成，成本: 857.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,300 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 4, 2, 7, 5, 3, 1], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 8, 4, 6, 1, 3, 5, 7], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 4, 6, 7, 5, 2, 3, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,300 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 857.00)
2025-08-04 16:59:28,300 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-04 16:59:28,301 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-04 16:59:28,301 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,302 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:28,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 966.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,305 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 1, 8, 2, 5, 3, 7, 4], 'cur_cost': 966.0, 'intermediate_solutions': [{'tour': [8, 3, 1, 2, 5, 6, 7, 0, 4], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 1, 2, 8, 7, 6, 0, 4], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 1, 4, 2, 8, 6, 7, 0], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,305 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 966.00)
2025-08-04 16:59:28,305 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-04 16:59:28,306 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-04 16:59:28,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,306 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:28,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,307 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 887.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,308 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 7, 3, 6, 0, 4, 2, 8, 5], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [7, 1, 0, 8, 2, 6, 4, 5, 3], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 5, 4, 7, 2, 8, 0, 3], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 8, 2, 7, 4, 5, 3], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,308 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 887.00)
2025-08-04 16:59:28,308 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-04 16:59:28,309 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:28,309 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:28,309 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 913.0
2025-08-04 16:59:28,382 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:28,382 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:28,382 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:28,383 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:28,383 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0}, {'tour': [0, 6, 1, 8, 2, 5, 3, 7, 4], 'cur_cost': 966.0}, {'tour': [1, 7, 3, 6, 0, 4, 2, 8, 5], 'cur_cost': 887.0}, {'tour': array([2, 1, 6, 5, 8, 4, 3, 7, 0], dtype=int64), 'cur_cost': 913.0}, {'tour': [4, 8, 5, 6, 3, 7, 0, 1, 2], 'cur_cost': 744.0}, {'tour': [0, 8, 4, 6, 5, 1, 2, 3, 7], 'cur_cost': 1048.0}, {'tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0}, {'tour': [5, 8, 3, 7, 0, 1, 4, 2, 6], 'cur_cost': 785.0}, {'tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0}, {'tour': [0, 7, 4, 3, 5, 1, 8, 6, 2], 'cur_cost': 1159.0}]
2025-08-04 16:59:28,385 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒，最大迭代次数: 10
2025-08-04 16:59:28,385 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-04 16:59:28,386 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([2, 1, 6, 5, 8, 4, 3, 7, 0], dtype=int64), 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': array([0, 5, 2, 3, 7, 6, 8, 4, 1]), 'cur_cost': 976.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 2, 7, 6, 8, 4, 1]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 2, 6, 8, 4, 1]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 0, 5, 7, 6, 8, 4, 1]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 3, 0, 5, 6, 8, 4, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:28,387 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 913.00)
2025-08-04 16:59:28,387 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-04 16:59:28,388 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-04 16:59:28,388 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,388 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-08-04 16:59:28,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 861.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,390 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 7, 1, 4, 2, 8, 3, 6], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [4, 8, 5, 6, 7, 3, 0, 1, 2], 'cur_cost': 765.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 3, 7, 0, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 5, 6, 3, 7, 1, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,390 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 861.00)
2025-08-04 16:59:28,390 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-04 16:59:28,390 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:28,391 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:28,391 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 945.0
2025-08-04 16:59:28,474 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:28,474 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:28,474 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:28,475 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:28,476 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0}, {'tour': [0, 6, 1, 8, 2, 5, 3, 7, 4], 'cur_cost': 966.0}, {'tour': [1, 7, 3, 6, 0, 4, 2, 8, 5], 'cur_cost': 887.0}, {'tour': array([2, 1, 6, 5, 8, 4, 3, 7, 0], dtype=int64), 'cur_cost': 913.0}, {'tour': [0, 5, 7, 1, 4, 2, 8, 3, 6], 'cur_cost': 861.0}, {'tour': array([2, 3, 5, 6, 1, 4, 0, 8, 7], dtype=int64), 'cur_cost': 945.0}, {'tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0}, {'tour': [5, 8, 3, 7, 0, 1, 4, 2, 6], 'cur_cost': 785.0}, {'tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0}, {'tour': [0, 7, 4, 3, 5, 1, 8, 6, 2], 'cur_cost': 1159.0}]
2025-08-04 16:59:28,476 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒，最大迭代次数: 10
2025-08-04 16:59:28,477 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-04 16:59:28,477 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 3, 5, 6, 1, 4, 0, 8, 7], dtype=int64), 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': array([4, 8, 0, 6, 5, 1, 2, 3, 7]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 8, 0, 5, 1, 2, 3, 7]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 4, 8, 0, 1, 2, 3, 7]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 8, 5, 1, 2, 3, 7]), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 6, 4, 8, 1, 2, 3, 7]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:28,478 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 945.00)
2025-08-04 16:59:28,478 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-04 16:59:28,478 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-04 16:59:28,478 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,479 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:28,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,479 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,480 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,480 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1095.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,480 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 2, 0, 3, 4, 8, 6, 1, 7], 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 3, 7, 5, 8, 6, 0], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 5, 7, 3, 2, 6], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,481 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1095.00)
2025-08-04 16:59:28,481 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-04 16:59:28,481 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-04 16:59:28,481 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,482 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-08-04 16:59:28,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,482 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,483 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1075.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,483 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 5, 1, 0, 3, 2, 6, 7, 8], 'cur_cost': 1075.0, 'intermediate_solutions': [{'tour': [5, 8, 2, 7, 0, 1, 4, 3, 6], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 7, 0, 4, 1, 2, 6], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 0, 4, 2, 6, 1], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,484 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1075.00)
2025-08-04 16:59:28,484 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-04 16:59:28,484 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-04 16:59:28,484 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-04 16:59:28,485 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-08-04 16:59:28,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,485 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,486 - ExplorationExpert - INFO - 计算路径成本
2025-08-04 16:59:28,486 - ExplorationExpert - INFO - 探索路径生成完成，成本: 763.0, 路径长度: 9, 收集中间解: 3
2025-08-04 16:59:28,486 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [6, 7, 5, 0, 1, 4, 2, 8, 3], 'cur_cost': 763.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 6, 3, 0, 1, 5], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 7, 6, 5, 3, 1, 0], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-04 16:59:28,487 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 763.00)
2025-08-04 16:59:28,487 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-04 16:59:28,487 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-04 16:59:28,487 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-04 16:59:28,488 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 993.0
2025-08-04 16:59:28,554 - ExploitationExpert - INFO - res_population_num: 3
2025-08-04 16:59:28,555 - ExploitationExpert - INFO - res_population_costs: [680.0, 680, 680.0]
2025-08-04 16:59:28,555 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-08-04 16:59:28,556 - ExploitationExpert - INFO - populations_num: 10
2025-08-04 16:59:28,556 - ExploitationExpert - INFO - populations: [{'tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0}, {'tour': [0, 6, 1, 8, 2, 5, 3, 7, 4], 'cur_cost': 966.0}, {'tour': [1, 7, 3, 6, 0, 4, 2, 8, 5], 'cur_cost': 887.0}, {'tour': array([2, 1, 6, 5, 8, 4, 3, 7, 0], dtype=int64), 'cur_cost': 913.0}, {'tour': [0, 5, 7, 1, 4, 2, 8, 3, 6], 'cur_cost': 861.0}, {'tour': array([2, 3, 5, 6, 1, 4, 0, 8, 7], dtype=int64), 'cur_cost': 945.0}, {'tour': [5, 2, 0, 3, 4, 8, 6, 1, 7], 'cur_cost': 1095.0}, {'tour': [4, 5, 1, 0, 3, 2, 6, 7, 8], 'cur_cost': 1075.0}, {'tour': [6, 7, 5, 0, 1, 4, 2, 8, 3], 'cur_cost': 763.0}, {'tour': array([2, 3, 8, 5, 4, 6, 1, 0, 7], dtype=int64), 'cur_cost': 993.0}]
2025-08-04 16:59:28,557 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒，最大迭代次数: 10
2025-08-04 16:59:28,557 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-04 16:59:28,558 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 3, 8, 5, 4, 6, 1, 0, 7], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([4, 7, 0, 3, 5, 1, 8, 6, 2]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 4, 7, 0, 5, 1, 8, 6, 2]), 'cur_cost': 1247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 4, 7, 0, 1, 8, 6, 2]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 4, 7, 5, 1, 8, 6, 2]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 3, 4, 7, 1, 8, 6, 2]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-04 16:59:28,559 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 993.00)
2025-08-04 16:59:28,559 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-04 16:59:28,559 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-04 16:59:28,560 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 7, 0, 3, 5, 8, 4, 2], 'cur_cost': 857.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 4, 2, 7, 5, 3, 1], 'cur_cost': 858.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 8, 4, 6, 1, 3, 5, 7], 'cur_cost': 972.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 4, 6, 7, 5, 2, 3, 1], 'cur_cost': 1063.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 1, 8, 2, 5, 3, 7, 4], 'cur_cost': 966.0, 'intermediate_solutions': [{'tour': [8, 3, 1, 2, 5, 6, 7, 0, 4], 'cur_cost': 969.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 3, 1, 2, 8, 7, 6, 0, 4], 'cur_cost': 956.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 1, 4, 2, 8, 6, 7, 0], 'cur_cost': 922.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 3, 6, 0, 4, 2, 8, 5], 'cur_cost': 887.0, 'intermediate_solutions': [{'tour': [7, 1, 0, 8, 2, 6, 4, 5, 3], 'cur_cost': 1007.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 1, 5, 4, 7, 2, 8, 0, 3], 'cur_cost': 1112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 6, 8, 2, 7, 4, 5, 3], 'cur_cost': 973.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 1, 6, 5, 8, 4, 3, 7, 0], dtype=int64), 'cur_cost': 913.0, 'intermediate_solutions': [{'tour': array([0, 5, 2, 3, 7, 6, 8, 4, 1]), 'cur_cost': 976.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 5, 2, 7, 6, 8, 4, 1]), 'cur_cost': 1143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 3, 0, 5, 2, 6, 8, 4, 1]), 'cur_cost': 1141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 3, 0, 5, 7, 6, 8, 4, 1]), 'cur_cost': 1033.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 7, 3, 0, 5, 6, 8, 4, 1]), 'cur_cost': 992.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 7, 1, 4, 2, 8, 3, 6], 'cur_cost': 861.0, 'intermediate_solutions': [{'tour': [4, 8, 5, 6, 7, 3, 0, 1, 2], 'cur_cost': 765.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 6, 3, 7, 0, 2, 1], 'cur_cost': 880.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 5, 6, 3, 7, 1, 2], 'cur_cost': 869.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 5, 6, 1, 4, 0, 8, 7], dtype=int64), 'cur_cost': 945.0, 'intermediate_solutions': [{'tour': array([4, 8, 0, 6, 5, 1, 2, 3, 7]), 'cur_cost': 1006.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 4, 8, 0, 5, 1, 2, 3, 7]), 'cur_cost': 1103.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 6, 4, 8, 0, 1, 2, 3, 7]), 'cur_cost': 912.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 4, 8, 5, 1, 2, 3, 7]), 'cur_cost': 1037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 6, 4, 8, 1, 2, 3, 7]), 'cur_cost': 1049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 2, 0, 3, 4, 8, 6, 1, 7], 'cur_cost': 1095.0, 'intermediate_solutions': [{'tour': [1, 4, 2, 3, 7, 5, 8, 6, 0], 'cur_cost': 821.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 0, 4, 8, 5, 7, 3, 2, 6], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 2, 3, 7, 5, 8, 4, 0], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 1, 0, 3, 2, 6, 7, 8], 'cur_cost': 1075.0, 'intermediate_solutions': [{'tour': [5, 8, 2, 7, 0, 1, 4, 3, 6], 'cur_cost': 854.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 7, 0, 4, 1, 2, 6], 'cur_cost': 910.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 7, 0, 4, 2, 6, 1], 'cur_cost': 935.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 5, 0, 1, 4, 2, 8, 3], 'cur_cost': 763.0, 'intermediate_solutions': [{'tour': [4, 2, 8, 7, 6, 3, 0, 1, 5], 'cur_cost': 897.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 8, 7, 6, 5, 3, 1, 0], 'cur_cost': 747.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 8, 7, 6, 5, 0, 1, 3], 'cur_cost': 849.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 8, 5, 4, 6, 1, 0, 7], dtype=int64), 'cur_cost': 993.0, 'intermediate_solutions': [{'tour': array([4, 7, 0, 3, 5, 1, 8, 6, 2]), 'cur_cost': 1090.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 4, 7, 0, 5, 1, 8, 6, 2]), 'cur_cost': 1247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 4, 7, 0, 1, 8, 6, 2]), 'cur_cost': 1082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 3, 4, 7, 5, 1, 8, 6, 2]), 'cur_cost': 1208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 5, 3, 4, 7, 1, 8, 6, 2]), 'cur_cost': 1162.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-04 16:59:28,562 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-04 16:59:28,563 - StatsExpert - INFO - 开始统计分析
2025-08-04 16:59:28,564 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=763.000, 多样性=0.894
2025-08-04 16:59:28,564 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-04 16:59:28,564 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-04 16:59:28,565 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-04 16:59:28,565 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.010000988056149412, 'best_improvement': -0.025537634408602152}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0109289617486341}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.058594297123822046, 'recent_improvements': [-0.09776176762843995, 0.009143301662045163, 0.01942682661920413], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-04 16:59:28,566 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-04 16:59:28,567 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-08-04 16:59:28,568 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250804_165928.solution
2025-08-04 16:59:28,580 - __main__ - INFO - 评估统计 - 总次数: 225949.9999998095, 运行时间: 9.26s, 最佳成本: 680.0
2025-08-04 16:59:28,580 - __main__ - INFO - 实例 simple1_9 处理完成
