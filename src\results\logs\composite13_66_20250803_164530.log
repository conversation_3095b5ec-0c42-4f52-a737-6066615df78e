2025-08-03 16:45:30,772 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:45:30,773 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:45:30,775 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:45:30,789 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9903.000, 多样性=0.973
2025-08-03 16:45:30,794 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:45:30,802 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-03 16:45:30,805 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:45:30,807 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:45:30,808 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:45:30,808 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:45:30,808 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:45:31,069 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -4064.960, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:45:31,069 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:45:31,069 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:45:31,138 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:45:31,464 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_164531.html
2025-08-03 16:45:31,539 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_164531.html
2025-08-03 16:45:31,540 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:45:31,540 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:45:31,540 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7328秒
2025-08-03 16:45:31,540 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:45:31,541 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4064.9600000000005, 'local_optima_density': 0.2, 'gradient_variance': 2594130172.3424006, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9713107216099232, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4064.960)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754210731.069228, 'performance_metrics': {}}}
2025-08-03 16:45:31,543 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:45:31,543 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:45:31,543 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:45:31,544 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:45:31,545 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:45:31,545 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:45:31,545 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:45:31,546 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:45:31,547 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:45:31,548 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:45:31,550 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:45:31,551 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1, 2, 4} (总数: 4, 保护比例: 0.20)
2025-08-03 16:45:31,551 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:45:31,552 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:45:31,552 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:31,558 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:31,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:31,733 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12366.0, 路径长度: 66
2025-08-03 16:45:31,733 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}
2025-08-03 16:45:31,733 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 12366.00)
2025-08-03 16:45:31,734 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:45:31,734 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:31,735 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:31,736 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 110821.0
2025-08-03 16:45:33,768 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:45:33,769 - ExploitationExpert - INFO - res_population_costs: [9863.0]
2025-08-03 16:45:33,769 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:45:33,770 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:33,770 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': array([24, 31, 25, 26, 36, 37, 27, 33, 28, 30, 35, 34, 32, 29, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10007.0}, {'tour': array([11,  9,  3,  7,  1,  0, 10,  8,  2,  6,  4,  5, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10255.0}, {'tour': array([32, 28, 30, 35, 34, 26, 25, 31, 33, 29, 24, 37, 36, 27, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9903.0}, {'tour': array([64, 53, 62, 59, 56, 58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10056.0}, {'tour': array([18,  5, 36, 55, 10, 44, 51, 25, 31, 42, 60, 57, 33,  2, 53, 11, 20,
       29,  8, 27, 40, 35, 12,  1,  4,  6, 64, 39, 41,  0, 46, 30, 15, 22,
       52, 38, 34, 19, 54, 28, 32, 21, 43, 45, 56, 65, 16, 17, 58,  3, 47,
        9, 23, 37, 13, 59, 24, 63, 14, 26, 62, 61, 48, 49, 50,  7],
      dtype=int64), 'cur_cost': 102228.0}, {'tour': array([34, 59, 43, 49, 45, 47,  5, 56,  2, 14, 60, 36,  0,  9, 23,  1, 38,
       40, 32, 53, 11, 48,  7, 37, 57, 63, 13, 20, 65,  3, 58, 44, 52,  8,
       17, 27, 22, 10,  4, 46, 50, 31, 33, 26, 29, 30, 64, 28, 24, 19, 21,
       42, 54, 16, 62, 25, 55, 51, 12, 15,  6, 41, 39, 35, 61, 18],
      dtype=int64), 'cur_cost': 99678.0}, {'tour': array([64, 30, 12, 49, 42, 45, 57, 19, 11,  7,  8, 40,  9, 36, 22, 34,  0,
       10,  5, 14, 23, 29,  1, 16, 62, 33, 53, 60, 37, 48, 15,  3, 20, 61,
       24, 56, 26, 35, 25, 13, 18, 63, 27,  4, 51, 31,  2, 39, 17,  6, 58,
       65, 46, 59, 43, 50, 55, 54, 52, 38, 32, 47, 41, 44, 21, 28],
      dtype=int64), 'cur_cost': 101864.0}, {'tour': array([14, 34,  9, 63, 22, 38, 47, 31, 10, 46, 37, 26, 61, 54, 60, 18, 20,
        7, 11, 50, 65,  3, 52, 55, 33, 64,  6, 43, 16, 28, 57, 59, 12, 45,
       19, 39,  5, 32, 41, 56, 27, 44, 48, 17, 35, 15, 29, 24,  1, 58, 23,
        2,  4,  8, 21, 49, 30, 62, 13, 53,  0, 51, 42, 25, 40, 36],
      dtype=int64), 'cur_cost': 100271.0}, {'tour': array([ 6, 15, 44, 10,  1, 46, 56, 60, 18, 11, 43, 58,  0, 12, 13, 57,  7,
        5, 38, 54, 45,  9, 53, 20, 14, 30, 33, 16,  3, 26, 65, 50, 59, 39,
       63, 32, 21, 61, 51, 62, 27, 35,  8, 36,  2, 29, 55, 31, 40, 37, 34,
       23, 19, 28, 47, 49, 25, 17, 42,  4, 48, 41, 52, 24, 22, 64],
      dtype=int64), 'cur_cost': 115517.0}, {'tour': array([32, 54, 41, 27, 15, 52, 16, 14, 36,  3,  8, 33,  2, 53, 18, 49,  0,
       40,  9, 59, 61, 37, 20, 31, 60, 38, 17, 44, 63, 10,  4, 12, 62, 50,
        1, 51, 64, 57, 45, 35, 19, 28,  5, 30, 21, 29, 48, 26, 23, 58, 65,
       11,  6, 46, 42, 56, 39, 25, 47, 24, 43, 22, 13,  7, 55, 34],
      dtype=int64), 'cur_cost': 110956.0}, {'tour': array([ 4, 38, 27, 48, 26, 20, 25, 22,  1, 21, 64, 50, 61, 42, 19, 46, 36,
       59, 54, 47,  2,  6, 49, 28, 34, 40, 14, 33, 16, 63, 30, 24, 55, 12,
        8, 41,  3, 39, 65, 56,  0, 62, 29, 31, 18, 37, 15, 60, 53,  9, 57,
       52, 32, 44, 35, 58, 13,  5, 43, 51, 45, 23,  7, 17, 10, 11],
      dtype=int64), 'cur_cost': 104591.0}, {'tour': array([29, 43, 49, 22, 39, 17, 24, 55, 10, 19, 61, 26, 25, 37,  6, 60, 47,
        5,  3,  2, 38, 48, 53,  8, 36, 13, 56,  0, 34, 57, 23, 62, 59, 30,
       20, 15, 40, 18, 42,  7, 35,  1, 65, 27, 64, 11, 52, 28, 12, 45, 51,
       63, 32, 31, 21,  4, 16, 50, 33, 44, 46, 14, 41, 58, 54,  9],
      dtype=int64), 'cur_cost': 101445.0}, {'tour': array([ 1, 60, 35, 64,  6, 48, 12, 10,  4, 45, 28, 19, 65, 53, 14,  2, 43,
       11, 54, 16, 58, 63, 46, 17, 33, 21,  7, 29, 31, 42, 50, 13, 25, 61,
       26,  8,  3, 52, 62, 36,  9, 30, 24, 51, 39, 34, 20,  5, 57, 32, 44,
       41, 59, 38, 23, 40, 27, 22, 49, 18, 55, 15, 56, 37, 47,  0],
      dtype=int64), 'cur_cost': 105850.0}, {'tour': array([51, 65, 26, 50, 18, 22, 17, 33, 35, 27, 49, 40, 56, 10, 48, 30, 43,
       37, 36, 42, 20,  2, 60, 47, 38, 16, 15, 64, 28, 39,  7, 25,  3,  5,
       14, 55,  4, 62, 29, 61, 41, 45, 23, 32, 24, 59, 21,  8, 52, 44, 34,
       13, 11,  6, 31,  0, 57,  9, 63, 46, 58, 54,  1, 19, 12, 53],
      dtype=int64), 'cur_cost': 100361.0}, {'tour': array([10, 45, 13, 51, 64, 16, 56, 30, 34, 47, 43, 23, 21, 55, 53, 37,  6,
        1, 31, 61, 25, 22, 36, 49, 11,  0, 28, 57,  2,  4, 19,  3, 12, 14,
       58, 48,  9, 44, 52, 17, 46, 33, 26, 27, 39, 41, 35, 40, 15, 42, 18,
       65, 24, 63, 38, 59, 54, 29, 62,  5, 32, 50,  7, 60, 20,  8],
      dtype=int64), 'cur_cost': 110620.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:33,783 - ExploitationExpert - INFO - 局部搜索耗时: 2.05秒
2025-08-03 16:45:33,786 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:45:33,789 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}
2025-08-03 16:45:33,790 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 110821.00)
2025-08-03 16:45:33,791 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:45:33,791 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:45:33,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:33,797 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:33,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:33,798 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12454.0, 路径长度: 66
2025-08-03 16:45:33,799 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}
2025-08-03 16:45:33,800 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12454.00)
2025-08-03 16:45:33,800 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:45:33,801 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:45:33,801 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:33,806 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:33,807 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:33,807 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14632.0, 路径长度: 66
2025-08-03 16:45:33,808 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}
2025-08-03 16:45:33,808 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 14632.00)
2025-08-03 16:45:33,808 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:45:33,809 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:33,809 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:33,809 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 106204.0
2025-08-03 16:45:35,934 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:45:35,935 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0]
2025-08-03 16:45:35,935 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:45:35,936 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:35,936 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': array([64, 53, 62, 59, 56, 58, 60, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10056.0}, {'tour': array([18,  5, 36, 55, 10, 44, 51, 25, 31, 42, 60, 57, 33,  2, 53, 11, 20,
       29,  8, 27, 40, 35, 12,  1,  4,  6, 64, 39, 41,  0, 46, 30, 15, 22,
       52, 38, 34, 19, 54, 28, 32, 21, 43, 45, 56, 65, 16, 17, 58,  3, 47,
        9, 23, 37, 13, 59, 24, 63, 14, 26, 62, 61, 48, 49, 50,  7],
      dtype=int64), 'cur_cost': 102228.0}, {'tour': array([34, 59, 43, 49, 45, 47,  5, 56,  2, 14, 60, 36,  0,  9, 23,  1, 38,
       40, 32, 53, 11, 48,  7, 37, 57, 63, 13, 20, 65,  3, 58, 44, 52,  8,
       17, 27, 22, 10,  4, 46, 50, 31, 33, 26, 29, 30, 64, 28, 24, 19, 21,
       42, 54, 16, 62, 25, 55, 51, 12, 15,  6, 41, 39, 35, 61, 18],
      dtype=int64), 'cur_cost': 99678.0}, {'tour': array([64, 30, 12, 49, 42, 45, 57, 19, 11,  7,  8, 40,  9, 36, 22, 34,  0,
       10,  5, 14, 23, 29,  1, 16, 62, 33, 53, 60, 37, 48, 15,  3, 20, 61,
       24, 56, 26, 35, 25, 13, 18, 63, 27,  4, 51, 31,  2, 39, 17,  6, 58,
       65, 46, 59, 43, 50, 55, 54, 52, 38, 32, 47, 41, 44, 21, 28],
      dtype=int64), 'cur_cost': 101864.0}, {'tour': array([14, 34,  9, 63, 22, 38, 47, 31, 10, 46, 37, 26, 61, 54, 60, 18, 20,
        7, 11, 50, 65,  3, 52, 55, 33, 64,  6, 43, 16, 28, 57, 59, 12, 45,
       19, 39,  5, 32, 41, 56, 27, 44, 48, 17, 35, 15, 29, 24,  1, 58, 23,
        2,  4,  8, 21, 49, 30, 62, 13, 53,  0, 51, 42, 25, 40, 36],
      dtype=int64), 'cur_cost': 100271.0}, {'tour': array([ 6, 15, 44, 10,  1, 46, 56, 60, 18, 11, 43, 58,  0, 12, 13, 57,  7,
        5, 38, 54, 45,  9, 53, 20, 14, 30, 33, 16,  3, 26, 65, 50, 59, 39,
       63, 32, 21, 61, 51, 62, 27, 35,  8, 36,  2, 29, 55, 31, 40, 37, 34,
       23, 19, 28, 47, 49, 25, 17, 42,  4, 48, 41, 52, 24, 22, 64],
      dtype=int64), 'cur_cost': 115517.0}, {'tour': array([32, 54, 41, 27, 15, 52, 16, 14, 36,  3,  8, 33,  2, 53, 18, 49,  0,
       40,  9, 59, 61, 37, 20, 31, 60, 38, 17, 44, 63, 10,  4, 12, 62, 50,
        1, 51, 64, 57, 45, 35, 19, 28,  5, 30, 21, 29, 48, 26, 23, 58, 65,
       11,  6, 46, 42, 56, 39, 25, 47, 24, 43, 22, 13,  7, 55, 34],
      dtype=int64), 'cur_cost': 110956.0}, {'tour': array([ 4, 38, 27, 48, 26, 20, 25, 22,  1, 21, 64, 50, 61, 42, 19, 46, 36,
       59, 54, 47,  2,  6, 49, 28, 34, 40, 14, 33, 16, 63, 30, 24, 55, 12,
        8, 41,  3, 39, 65, 56,  0, 62, 29, 31, 18, 37, 15, 60, 53,  9, 57,
       52, 32, 44, 35, 58, 13,  5, 43, 51, 45, 23,  7, 17, 10, 11],
      dtype=int64), 'cur_cost': 104591.0}, {'tour': array([29, 43, 49, 22, 39, 17, 24, 55, 10, 19, 61, 26, 25, 37,  6, 60, 47,
        5,  3,  2, 38, 48, 53,  8, 36, 13, 56,  0, 34, 57, 23, 62, 59, 30,
       20, 15, 40, 18, 42,  7, 35,  1, 65, 27, 64, 11, 52, 28, 12, 45, 51,
       63, 32, 31, 21,  4, 16, 50, 33, 44, 46, 14, 41, 58, 54,  9],
      dtype=int64), 'cur_cost': 101445.0}, {'tour': array([ 1, 60, 35, 64,  6, 48, 12, 10,  4, 45, 28, 19, 65, 53, 14,  2, 43,
       11, 54, 16, 58, 63, 46, 17, 33, 21,  7, 29, 31, 42, 50, 13, 25, 61,
       26,  8,  3, 52, 62, 36,  9, 30, 24, 51, 39, 34, 20,  5, 57, 32, 44,
       41, 59, 38, 23, 40, 27, 22, 49, 18, 55, 15, 56, 37, 47,  0],
      dtype=int64), 'cur_cost': 105850.0}, {'tour': array([51, 65, 26, 50, 18, 22, 17, 33, 35, 27, 49, 40, 56, 10, 48, 30, 43,
       37, 36, 42, 20,  2, 60, 47, 38, 16, 15, 64, 28, 39,  7, 25,  3,  5,
       14, 55,  4, 62, 29, 61, 41, 45, 23, 32, 24, 59, 21,  8, 52, 44, 34,
       13, 11,  6, 31,  0, 57,  9, 63, 46, 58, 54,  1, 19, 12, 53],
      dtype=int64), 'cur_cost': 100361.0}, {'tour': array([10, 45, 13, 51, 64, 16, 56, 30, 34, 47, 43, 23, 21, 55, 53, 37,  6,
        1, 31, 61, 25, 22, 36, 49, 11,  0, 28, 57,  2,  4, 19,  3, 12, 14,
       58, 48,  9, 44, 52, 17, 46, 33, 26, 27, 39, 41, 35, 40, 15, 42, 18,
       65, 24, 63, 38, 59, 54, 29, 62,  5, 32, 50,  7, 60, 20,  8],
      dtype=int64), 'cur_cost': 110620.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:35,948 - ExploitationExpert - INFO - 局部搜索耗时: 2.14秒
2025-08-03 16:45:35,950 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:45:35,951 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}
2025-08-03 16:45:35,952 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 106204.00)
2025-08-03 16:45:35,952 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:45:35,953 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:45:35,953 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:35,960 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:35,961 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:35,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12359.0, 路径长度: 66
2025-08-03 16:45:35,963 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}
2025-08-03 16:45:35,964 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12359.00)
2025-08-03 16:45:35,964 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:45:35,964 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:45:35,965 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:35,969 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:35,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:35,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12719.0, 路径长度: 66
2025-08-03 16:45:35,970 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}
2025-08-03 16:45:35,970 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12719.00)
2025-08-03 16:45:35,971 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:45:35,971 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:35,971 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:35,971 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109941.0
2025-08-03 16:45:36,651 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 16:45:36,651 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0, 9542.0]
2025-08-03 16:45:36,652 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:45:36,654 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:36,654 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}, {'tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}, {'tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}, {'tour': array([64, 30, 12, 49, 42, 45, 57, 19, 11,  7,  8, 40,  9, 36, 22, 34,  0,
       10,  5, 14, 23, 29,  1, 16, 62, 33, 53, 60, 37, 48, 15,  3, 20, 61,
       24, 56, 26, 35, 25, 13, 18, 63, 27,  4, 51, 31,  2, 39, 17,  6, 58,
       65, 46, 59, 43, 50, 55, 54, 52, 38, 32, 47, 41, 44, 21, 28],
      dtype=int64), 'cur_cost': 101864.0}, {'tour': array([14, 34,  9, 63, 22, 38, 47, 31, 10, 46, 37, 26, 61, 54, 60, 18, 20,
        7, 11, 50, 65,  3, 52, 55, 33, 64,  6, 43, 16, 28, 57, 59, 12, 45,
       19, 39,  5, 32, 41, 56, 27, 44, 48, 17, 35, 15, 29, 24,  1, 58, 23,
        2,  4,  8, 21, 49, 30, 62, 13, 53,  0, 51, 42, 25, 40, 36],
      dtype=int64), 'cur_cost': 100271.0}, {'tour': array([ 6, 15, 44, 10,  1, 46, 56, 60, 18, 11, 43, 58,  0, 12, 13, 57,  7,
        5, 38, 54, 45,  9, 53, 20, 14, 30, 33, 16,  3, 26, 65, 50, 59, 39,
       63, 32, 21, 61, 51, 62, 27, 35,  8, 36,  2, 29, 55, 31, 40, 37, 34,
       23, 19, 28, 47, 49, 25, 17, 42,  4, 48, 41, 52, 24, 22, 64],
      dtype=int64), 'cur_cost': 115517.0}, {'tour': array([32, 54, 41, 27, 15, 52, 16, 14, 36,  3,  8, 33,  2, 53, 18, 49,  0,
       40,  9, 59, 61, 37, 20, 31, 60, 38, 17, 44, 63, 10,  4, 12, 62, 50,
        1, 51, 64, 57, 45, 35, 19, 28,  5, 30, 21, 29, 48, 26, 23, 58, 65,
       11,  6, 46, 42, 56, 39, 25, 47, 24, 43, 22, 13,  7, 55, 34],
      dtype=int64), 'cur_cost': 110956.0}, {'tour': array([ 4, 38, 27, 48, 26, 20, 25, 22,  1, 21, 64, 50, 61, 42, 19, 46, 36,
       59, 54, 47,  2,  6, 49, 28, 34, 40, 14, 33, 16, 63, 30, 24, 55, 12,
        8, 41,  3, 39, 65, 56,  0, 62, 29, 31, 18, 37, 15, 60, 53,  9, 57,
       52, 32, 44, 35, 58, 13,  5, 43, 51, 45, 23,  7, 17, 10, 11],
      dtype=int64), 'cur_cost': 104591.0}, {'tour': array([29, 43, 49, 22, 39, 17, 24, 55, 10, 19, 61, 26, 25, 37,  6, 60, 47,
        5,  3,  2, 38, 48, 53,  8, 36, 13, 56,  0, 34, 57, 23, 62, 59, 30,
       20, 15, 40, 18, 42,  7, 35,  1, 65, 27, 64, 11, 52, 28, 12, 45, 51,
       63, 32, 31, 21,  4, 16, 50, 33, 44, 46, 14, 41, 58, 54,  9],
      dtype=int64), 'cur_cost': 101445.0}, {'tour': array([ 1, 60, 35, 64,  6, 48, 12, 10,  4, 45, 28, 19, 65, 53, 14,  2, 43,
       11, 54, 16, 58, 63, 46, 17, 33, 21,  7, 29, 31, 42, 50, 13, 25, 61,
       26,  8,  3, 52, 62, 36,  9, 30, 24, 51, 39, 34, 20,  5, 57, 32, 44,
       41, 59, 38, 23, 40, 27, 22, 49, 18, 55, 15, 56, 37, 47,  0],
      dtype=int64), 'cur_cost': 105850.0}, {'tour': array([51, 65, 26, 50, 18, 22, 17, 33, 35, 27, 49, 40, 56, 10, 48, 30, 43,
       37, 36, 42, 20,  2, 60, 47, 38, 16, 15, 64, 28, 39,  7, 25,  3,  5,
       14, 55,  4, 62, 29, 61, 41, 45, 23, 32, 24, 59, 21,  8, 52, 44, 34,
       13, 11,  6, 31,  0, 57,  9, 63, 46, 58, 54,  1, 19, 12, 53],
      dtype=int64), 'cur_cost': 100361.0}, {'tour': array([10, 45, 13, 51, 64, 16, 56, 30, 34, 47, 43, 23, 21, 55, 53, 37,  6,
        1, 31, 61, 25, 22, 36, 49, 11,  0, 28, 57,  2,  4, 19,  3, 12, 14,
       58, 48,  9, 44, 52, 17, 46, 33, 26, 27, 39, 41, 35, 40, 15, 42, 18,
       65, 24, 63, 38, 59, 54, 29, 62,  5, 32, 50,  7, 60, 20,  8],
      dtype=int64), 'cur_cost': 110620.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:36,664 - ExploitationExpert - INFO - 局部搜索耗时: 0.69秒
2025-08-03 16:45:36,665 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:45:36,665 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}
2025-08-03 16:45:36,666 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 109941.00)
2025-08-03 16:45:36,666 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:45:36,666 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:45:36,666 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,671 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:36,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12391.0, 路径长度: 66
2025-08-03 16:45:36,672 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}
2025-08-03 16:45:36,672 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12391.00)
2025-08-03 16:45:36,673 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:45:36,673 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:45:36,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,677 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:36,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,678 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12740.0, 路径长度: 66
2025-08-03 16:45:36,678 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}
2025-08-03 16:45:36,680 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12740.00)
2025-08-03 16:45:36,681 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:45:36,682 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:36,683 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:36,685 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 109829.0
2025-08-03 16:45:36,747 - ExploitationExpert - INFO - res_population_num: 4
2025-08-03 16:45:36,749 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0, 9542.0, 9525.0]
2025-08-03 16:45:36,753 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 17, 12, 22, 15, 14, 20,
       21, 13, 23, 16, 18, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:45:36,756 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:36,756 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}, {'tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}, {'tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}, {'tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}, {'tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}, {'tour': array([32, 54, 41, 27, 15, 52, 16, 14, 36,  3,  8, 33,  2, 53, 18, 49,  0,
       40,  9, 59, 61, 37, 20, 31, 60, 38, 17, 44, 63, 10,  4, 12, 62, 50,
        1, 51, 64, 57, 45, 35, 19, 28,  5, 30, 21, 29, 48, 26, 23, 58, 65,
       11,  6, 46, 42, 56, 39, 25, 47, 24, 43, 22, 13,  7, 55, 34],
      dtype=int64), 'cur_cost': 110956.0}, {'tour': array([ 4, 38, 27, 48, 26, 20, 25, 22,  1, 21, 64, 50, 61, 42, 19, 46, 36,
       59, 54, 47,  2,  6, 49, 28, 34, 40, 14, 33, 16, 63, 30, 24, 55, 12,
        8, 41,  3, 39, 65, 56,  0, 62, 29, 31, 18, 37, 15, 60, 53,  9, 57,
       52, 32, 44, 35, 58, 13,  5, 43, 51, 45, 23,  7, 17, 10, 11],
      dtype=int64), 'cur_cost': 104591.0}, {'tour': array([29, 43, 49, 22, 39, 17, 24, 55, 10, 19, 61, 26, 25, 37,  6, 60, 47,
        5,  3,  2, 38, 48, 53,  8, 36, 13, 56,  0, 34, 57, 23, 62, 59, 30,
       20, 15, 40, 18, 42,  7, 35,  1, 65, 27, 64, 11, 52, 28, 12, 45, 51,
       63, 32, 31, 21,  4, 16, 50, 33, 44, 46, 14, 41, 58, 54,  9],
      dtype=int64), 'cur_cost': 101445.0}, {'tour': array([ 1, 60, 35, 64,  6, 48, 12, 10,  4, 45, 28, 19, 65, 53, 14,  2, 43,
       11, 54, 16, 58, 63, 46, 17, 33, 21,  7, 29, 31, 42, 50, 13, 25, 61,
       26,  8,  3, 52, 62, 36,  9, 30, 24, 51, 39, 34, 20,  5, 57, 32, 44,
       41, 59, 38, 23, 40, 27, 22, 49, 18, 55, 15, 56, 37, 47,  0],
      dtype=int64), 'cur_cost': 105850.0}, {'tour': array([51, 65, 26, 50, 18, 22, 17, 33, 35, 27, 49, 40, 56, 10, 48, 30, 43,
       37, 36, 42, 20,  2, 60, 47, 38, 16, 15, 64, 28, 39,  7, 25,  3,  5,
       14, 55,  4, 62, 29, 61, 41, 45, 23, 32, 24, 59, 21,  8, 52, 44, 34,
       13, 11,  6, 31,  0, 57,  9, 63, 46, 58, 54,  1, 19, 12, 53],
      dtype=int64), 'cur_cost': 100361.0}, {'tour': array([10, 45, 13, 51, 64, 16, 56, 30, 34, 47, 43, 23, 21, 55, 53, 37,  6,
        1, 31, 61, 25, 22, 36, 49, 11,  0, 28, 57,  2,  4, 19,  3, 12, 14,
       58, 48,  9, 44, 52, 17, 46, 33, 26, 27, 39, 41, 35, 40, 15, 42, 18,
       65, 24, 63, 38, 59, 54, 29, 62,  5, 32, 50,  7, 60, 20,  8],
      dtype=int64), 'cur_cost': 110620.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:36,765 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:45:36,765 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:45:36,766 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}
2025-08-03 16:45:36,767 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 109829.00)
2025-08-03 16:45:36,767 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:45:36,767 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:45:36,768 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,785 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:45:36,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53259.0, 路径长度: 66
2025-08-03 16:45:36,788 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [51, 15, 35, 11, 3, 62, 65, 56, 59, 54, 12, 23, 30, 5, 58, 63, 61, 8, 6, 21, 25, 36, 14, 22, 9, 16, 18, 13, 10, 64, 4, 57, 53, 2, 52, 40, 39, 46, 41, 45, 38, 19, 37, 31, 32, 26, 17, 7, 20, 0, 55, 1, 27, 34, 49, 47, 42, 48, 43, 50, 33, 29, 24, 28, 44, 60], 'cur_cost': 53259.0}
2025-08-03 16:45:36,789 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 53259.00)
2025-08-03 16:45:36,790 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:45:36,790 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:45:36,791 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,796 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:45:36,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,798 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101876.0, 路径长度: 66
2025-08-03 16:45:36,798 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [21, 3, 5, 2, 15, 25, 62, 28, 64, 31, 65, 12, 13, 27, 48, 45, 63, 43, 16, 30, 24, 26, 58, 7, 49, 6, 52, 29, 50, 46, 14, 18, 11, 56, 33, 4, 51, 38, 23, 17, 40, 44, 41, 20, 35, 57, 37, 34, 36, 60, 42, 32, 0, 9, 39, 61, 19, 1, 59, 10, 53, 8, 55, 22, 54, 47], 'cur_cost': 101876.0}
2025-08-03 16:45:36,799 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 101876.00)
2025-08-03 16:45:36,800 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:45:36,800 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:36,800 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:36,800 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 106930.0
2025-08-03 16:45:36,874 - ExploitationExpert - INFO - res_population_num: 5
2025-08-03 16:45:36,874 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0, 9542.0, 9525.0, 9521]
2025-08-03 16:45:36,875 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 17, 12, 22, 15, 14, 20,
       21, 13, 23, 16, 18, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:45:36,878 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:36,878 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}, {'tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}, {'tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}, {'tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}, {'tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}, {'tour': [51, 15, 35, 11, 3, 62, 65, 56, 59, 54, 12, 23, 30, 5, 58, 63, 61, 8, 6, 21, 25, 36, 14, 22, 9, 16, 18, 13, 10, 64, 4, 57, 53, 2, 52, 40, 39, 46, 41, 45, 38, 19, 37, 31, 32, 26, 17, 7, 20, 0, 55, 1, 27, 34, 49, 47, 42, 48, 43, 50, 33, 29, 24, 28, 44, 60], 'cur_cost': 53259.0}, {'tour': [21, 3, 5, 2, 15, 25, 62, 28, 64, 31, 65, 12, 13, 27, 48, 45, 63, 43, 16, 30, 24, 26, 58, 7, 49, 6, 52, 29, 50, 46, 14, 18, 11, 56, 33, 4, 51, 38, 23, 17, 40, 44, 41, 20, 35, 57, 37, 34, 36, 60, 42, 32, 0, 9, 39, 61, 19, 1, 59, 10, 53, 8, 55, 22, 54, 47], 'cur_cost': 101876.0}, {'tour': array([53, 24, 46, 54, 29,  9, 36,  3, 42, 43, 39, 31, 38, 37, 47, 26, 13,
       41,  7,  0, 19, 51, 15, 57, 21,  5, 49, 17,  6, 55, 35,  1, 65, 50,
       12, 63, 40, 56,  4, 32, 16, 28, 34, 14, 60, 11, 48, 44, 22, 33, 23,
       52, 30, 27, 10,  8, 25,  2, 62, 61, 64, 20, 59, 18, 45, 58],
      dtype=int64), 'cur_cost': 106930.0}, {'tour': array([ 1, 60, 35, 64,  6, 48, 12, 10,  4, 45, 28, 19, 65, 53, 14,  2, 43,
       11, 54, 16, 58, 63, 46, 17, 33, 21,  7, 29, 31, 42, 50, 13, 25, 61,
       26,  8,  3, 52, 62, 36,  9, 30, 24, 51, 39, 34, 20,  5, 57, 32, 44,
       41, 59, 38, 23, 40, 27, 22, 49, 18, 55, 15, 56, 37, 47,  0],
      dtype=int64), 'cur_cost': 105850.0}, {'tour': array([51, 65, 26, 50, 18, 22, 17, 33, 35, 27, 49, 40, 56, 10, 48, 30, 43,
       37, 36, 42, 20,  2, 60, 47, 38, 16, 15, 64, 28, 39,  7, 25,  3,  5,
       14, 55,  4, 62, 29, 61, 41, 45, 23, 32, 24, 59, 21,  8, 52, 44, 34,
       13, 11,  6, 31,  0, 57,  9, 63, 46, 58, 54,  1, 19, 12, 53],
      dtype=int64), 'cur_cost': 100361.0}, {'tour': array([10, 45, 13, 51, 64, 16, 56, 30, 34, 47, 43, 23, 21, 55, 53, 37,  6,
        1, 31, 61, 25, 22, 36, 49, 11,  0, 28, 57,  2,  4, 19,  3, 12, 14,
       58, 48,  9, 44, 52, 17, 46, 33, 26, 27, 39, 41, 35, 40, 15, 42, 18,
       65, 24, 63, 38, 59, 54, 29, 62,  5, 32, 50,  7, 60, 20,  8],
      dtype=int64), 'cur_cost': 110620.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:36,890 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 16:45:36,890 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:45:36,891 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([53, 24, 46, 54, 29,  9, 36,  3, 42, 43, 39, 31, 38, 37, 47, 26, 13,
       41,  7,  0, 19, 51, 15, 57, 21,  5, 49, 17,  6, 55, 35,  1, 65, 50,
       12, 63, 40, 56,  4, 32, 16, 28, 34, 14, 60, 11, 48, 44, 22, 33, 23,
       52, 30, 27, 10,  8, 25,  2, 62, 61, 64, 20, 59, 18, 45, 58],
      dtype=int64), 'cur_cost': 106930.0}
2025-08-03 16:45:36,892 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 106930.00)
2025-08-03 16:45:36,892 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:45:36,892 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:45:36,892 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,898 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:36,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,900 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12852.0, 路径长度: 66
2025-08-03 16:45:36,900 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [0, 12, 5, 10, 8, 2, 6, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}
2025-08-03 16:45:36,901 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 12852.00)
2025-08-03 16:45:36,901 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:45:36,901 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:45:36,901 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:36,905 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:45:36,905 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:36,906 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12738.0, 路径长度: 66
2025-08-03 16:45:36,906 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 14, 1, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12738.0}
2025-08-03 16:45:36,906 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12738.00)
2025-08-03 16:45:36,907 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:45:36,907 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:36,907 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:36,907 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 119441.0
2025-08-03 16:45:36,973 - ExploitationExpert - INFO - res_population_num: 5
2025-08-03 16:45:36,973 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0, 9542.0, 9525.0, 9521]
2025-08-03 16:45:36,973 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 17, 12, 22, 15, 14, 20,
       21, 13, 23, 16, 18, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:45:36,976 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:36,976 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}, {'tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}, {'tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}, {'tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}, {'tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}, {'tour': [51, 15, 35, 11, 3, 62, 65, 56, 59, 54, 12, 23, 30, 5, 58, 63, 61, 8, 6, 21, 25, 36, 14, 22, 9, 16, 18, 13, 10, 64, 4, 57, 53, 2, 52, 40, 39, 46, 41, 45, 38, 19, 37, 31, 32, 26, 17, 7, 20, 0, 55, 1, 27, 34, 49, 47, 42, 48, 43, 50, 33, 29, 24, 28, 44, 60], 'cur_cost': 53259.0}, {'tour': [21, 3, 5, 2, 15, 25, 62, 28, 64, 31, 65, 12, 13, 27, 48, 45, 63, 43, 16, 30, 24, 26, 58, 7, 49, 6, 52, 29, 50, 46, 14, 18, 11, 56, 33, 4, 51, 38, 23, 17, 40, 44, 41, 20, 35, 57, 37, 34, 36, 60, 42, 32, 0, 9, 39, 61, 19, 1, 59, 10, 53, 8, 55, 22, 54, 47], 'cur_cost': 101876.0}, {'tour': array([53, 24, 46, 54, 29,  9, 36,  3, 42, 43, 39, 31, 38, 37, 47, 26, 13,
       41,  7,  0, 19, 51, 15, 57, 21,  5, 49, 17,  6, 55, 35,  1, 65, 50,
       12, 63, 40, 56,  4, 32, 16, 28, 34, 14, 60, 11, 48, 44, 22, 33, 23,
       52, 30, 27, 10,  8, 25,  2, 62, 61, 64, 20, 59, 18, 45, 58],
      dtype=int64), 'cur_cost': 106930.0}, {'tour': [0, 12, 5, 10, 8, 2, 6, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': [0, 14, 1, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12738.0}, {'tour': array([47, 25, 44, 32,  1, 29, 23, 27, 12, 59, 39,  6, 38,  7, 61, 36, 63,
       16, 37, 54,  3, 49, 64, 45, 50, 15, 33,  5, 35, 51, 13, 42, 21,  4,
       57, 17, 28, 14, 62, 48, 41, 65, 18, 53, 26, 30, 60, 20,  8, 34, 22,
       19, 43,  0, 31, 11, 52, 10,  2, 24, 40, 56,  9, 55, 46, 58],
      dtype=int64), 'cur_cost': 119441.0}, {'tour': array([32, 57, 14, 23,  0, 11, 52, 10,  2, 39, 33, 41, 54, 58, 61, 21, 29,
        4, 13,  5, 19, 60,  7, 46, 22, 34, 15, 12, 30, 50, 65, 35, 37, 36,
       51, 56, 40, 20, 31,  3, 49, 28,  1, 42, 59, 55, 47, 64, 27, 43, 44,
       63, 16, 18, 24, 48, 62, 38, 45,  6, 17, 53,  9, 26,  8, 25],
      dtype=int64), 'cur_cost': 107218.0}, {'tour': array([56,  3, 23, 37, 14, 30, 61,  4, 10, 25, 20, 40, 41, 35, 62, 58,  6,
       51, 11, 26,  1, 17, 32, 34, 13, 38, 50, 22, 55, 19, 63, 39, 48, 15,
       57, 27, 52,  9,  2, 36, 60, 64, 31, 47, 44, 16, 21, 46, 42,  8, 49,
       65, 29, 59, 12, 43, 18,  7,  0, 33,  5, 45, 24, 28, 53, 54],
      dtype=int64), 'cur_cost': 99948.0}, {'tour': array([20, 58,  8, 10,  0, 65, 19, 49, 45, 21, 56, 38, 23, 27, 52, 16, 62,
       48, 15, 42, 41, 43, 22, 40, 26, 35, 46, 25,  7,  6, 50, 53, 32, 24,
       55, 61, 51,  3, 39,  4, 44, 17, 30,  1, 12, 47, 36, 64, 54, 14, 63,
       11, 31,  9, 57, 59, 37, 33, 34, 60, 29, 13, 18,  2,  5, 28],
      dtype=int64), 'cur_cost': 101175.0}]
2025-08-03 16:45:36,989 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:45:36,990 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:45:36,990 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([47, 25, 44, 32,  1, 29, 23, 27, 12, 59, 39,  6, 38,  7, 61, 36, 63,
       16, 37, 54,  3, 49, 64, 45, 50, 15, 33,  5, 35, 51, 13, 42, 21,  4,
       57, 17, 28, 14, 62, 48, 41, 65, 18, 53, 26, 30, 60, 20,  8, 34, 22,
       19, 43,  0, 31, 11, 52, 10,  2, 24, 40, 56,  9, 55, 46, 58],
      dtype=int64), 'cur_cost': 119441.0}
2025-08-03 16:45:36,991 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 119441.00)
2025-08-03 16:45:36,991 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:45:36,991 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:45:36,991 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:37,005 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:45:37,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:37,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56359.0, 路径长度: 66
2025-08-03 16:45:37,007 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [4, 55, 8, 63, 20, 29, 32, 30, 31, 13, 25, 37, 26, 23, 18, 28, 9, 11, 59, 10, 52, 53, 54, 17, 3, 6, 15, 0, 60, 19, 22, 34, 35, 43, 48, 38, 51, 44, 16, 27, 21, 49, 46, 14, 36, 47, 50, 24, 33, 39, 45, 5, 2, 58, 61, 65, 64, 62, 1, 56, 7, 57, 40, 42, 41, 12], 'cur_cost': 56359.0}
2025-08-03 16:45:37,007 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 56359.00)
2025-08-03 16:45:37,008 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:45:37,008 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:45:37,008 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:45:37,013 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:45:37,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:45:37,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93315.0, 路径长度: 66
2025-08-03 16:45:37,019 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [13, 21, 2, 8, 59, 10, 37, 26, 58, 60, 14, 24, 65, 63, 7, 1, 9, 44, 25, 36, 28, 39, 47, 50, 32, 43, 12, 62, 55, 38, 45, 35, 56, 49, 18, 5, 0, 3, 22, 29, 48, 53, 15, 27, 46, 61, 17, 11, 19, 64, 34, 16, 41, 54, 57, 42, 51, 4, 23, 33, 40, 20, 52, 30, 31, 6], 'cur_cost': 93315.0}
2025-08-03 16:45:37,020 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 93315.00)
2025-08-03 16:45:37,020 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:45:37,021 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:45:37,021 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:45:37,022 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 111069.0
2025-08-03 16:45:37,088 - ExploitationExpert - INFO - res_population_num: 5
2025-08-03 16:45:37,088 - ExploitationExpert - INFO - res_population_costs: [9863.0, 9582.0, 9542.0, 9525.0, 9521]
2025-08-03 16:45:37,089 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 29, 32, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 19, 17, 14, 15, 22, 12, 18, 16, 23, 13, 20, 21, 42, 41, 50, 51,
       38, 45, 44, 39, 43, 48, 46, 47, 49, 40, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59,
       56, 58, 60, 54, 57, 64, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 17, 12, 22, 15, 14, 20,
       21, 13, 23, 16, 18, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:45:37,091 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:45:37,092 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}, {'tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}, {'tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}, {'tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}, {'tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}, {'tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}, {'tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}, {'tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}, {'tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}, {'tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}, {'tour': [51, 15, 35, 11, 3, 62, 65, 56, 59, 54, 12, 23, 30, 5, 58, 63, 61, 8, 6, 21, 25, 36, 14, 22, 9, 16, 18, 13, 10, 64, 4, 57, 53, 2, 52, 40, 39, 46, 41, 45, 38, 19, 37, 31, 32, 26, 17, 7, 20, 0, 55, 1, 27, 34, 49, 47, 42, 48, 43, 50, 33, 29, 24, 28, 44, 60], 'cur_cost': 53259.0}, {'tour': [21, 3, 5, 2, 15, 25, 62, 28, 64, 31, 65, 12, 13, 27, 48, 45, 63, 43, 16, 30, 24, 26, 58, 7, 49, 6, 52, 29, 50, 46, 14, 18, 11, 56, 33, 4, 51, 38, 23, 17, 40, 44, 41, 20, 35, 57, 37, 34, 36, 60, 42, 32, 0, 9, 39, 61, 19, 1, 59, 10, 53, 8, 55, 22, 54, 47], 'cur_cost': 101876.0}, {'tour': array([53, 24, 46, 54, 29,  9, 36,  3, 42, 43, 39, 31, 38, 37, 47, 26, 13,
       41,  7,  0, 19, 51, 15, 57, 21,  5, 49, 17,  6, 55, 35,  1, 65, 50,
       12, 63, 40, 56,  4, 32, 16, 28, 34, 14, 60, 11, 48, 44, 22, 33, 23,
       52, 30, 27, 10,  8, 25,  2, 62, 61, 64, 20, 59, 18, 45, 58],
      dtype=int64), 'cur_cost': 106930.0}, {'tour': [0, 12, 5, 10, 8, 2, 6, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}, {'tour': [0, 14, 1, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12738.0}, {'tour': array([47, 25, 44, 32,  1, 29, 23, 27, 12, 59, 39,  6, 38,  7, 61, 36, 63,
       16, 37, 54,  3, 49, 64, 45, 50, 15, 33,  5, 35, 51, 13, 42, 21,  4,
       57, 17, 28, 14, 62, 48, 41, 65, 18, 53, 26, 30, 60, 20,  8, 34, 22,
       19, 43,  0, 31, 11, 52, 10,  2, 24, 40, 56,  9, 55, 46, 58],
      dtype=int64), 'cur_cost': 119441.0}, {'tour': [4, 55, 8, 63, 20, 29, 32, 30, 31, 13, 25, 37, 26, 23, 18, 28, 9, 11, 59, 10, 52, 53, 54, 17, 3, 6, 15, 0, 60, 19, 22, 34, 35, 43, 48, 38, 51, 44, 16, 27, 21, 49, 46, 14, 36, 47, 50, 24, 33, 39, 45, 5, 2, 58, 61, 65, 64, 62, 1, 56, 7, 57, 40, 42, 41, 12], 'cur_cost': 56359.0}, {'tour': [13, 21, 2, 8, 59, 10, 37, 26, 58, 60, 14, 24, 65, 63, 7, 1, 9, 44, 25, 36, 28, 39, 47, 50, 32, 43, 12, 62, 55, 38, 45, 35, 56, 49, 18, 5, 0, 3, 22, 29, 48, 53, 15, 27, 46, 61, 17, 11, 19, 64, 34, 16, 41, 54, 57, 42, 51, 4, 23, 33, 40, 20, 52, 30, 31, 6], 'cur_cost': 93315.0}, {'tour': array([14, 16, 49, 61, 65,  0, 47,  9, 33, 62, 35, 28, 32, 53, 19, 64, 17,
       31, 57, 63, 11, 24,  1, 51, 39, 13, 20, 25, 58,  5,  8, 21,  4, 29,
       45, 52, 18, 48, 10, 42, 23, 40,  3, 59, 55, 27, 44, 12, 60, 50,  2,
       22, 43, 46, 26, 56, 15,  6, 41,  7, 34, 37, 54, 30, 36, 38],
      dtype=int64), 'cur_cost': 111069.0}]
2025-08-03 16:45:37,099 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 16:45:37,099 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:45:37,099 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([14, 16, 49, 61, 65,  0, 47,  9, 33, 62, 35, 28, 32, 53, 19, 64, 17,
       31, 57, 63, 11, 24,  1, 51, 39, 13, 20, 25, 58,  5,  8, 21,  4, 29,
       45, 52, 18, 48, 10, 42, 23, 40,  3, 59, 55, 27, 44, 12, 60, 50,  2,
       22, 43, 46, 26, 56, 15,  6, 41,  7, 34, 37, 54, 30, 36, 38],
      dtype=int64), 'cur_cost': 111069.0}
2025-08-03 16:45:37,100 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 111069.00)
2025-08-03 16:45:37,100 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:45:37,100 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:45:37,102 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12366.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 23,  3, 39, 43, 32, 48, 26, 65, 53, 46, 31, 33, 59, 63, 52,  1,
       54, 61, 11, 12,  0, 50,  7, 41, 25,  8, 20, 18, 35, 10, 37, 44, 17,
       42, 15, 60, 24, 40, 64, 29, 38, 62, 34,  9, 16, 58, 28, 14, 22, 56,
       51, 49, 27, 45, 21, 19, 47, 57, 30,  6, 55, 13,  4, 36,  5],
      dtype=int64), 'cur_cost': 110821.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 14, 17, 12, 22, 23, 16, 18, 19, 21, 20, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12454.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 19, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14632.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 51, 46,  8, 43, 47, 55,  3, 29, 37,  5, 24, 28, 44, 27, 36, 48,
       56, 10, 59, 42, 53, 13, 34, 40,  1, 38, 26, 39, 23, 22, 62, 31, 20,
       57, 32, 63, 11,  7, 54, 58, 35, 52, 12, 45, 50, 19, 64, 25,  9, 18,
        6, 21, 65,  2, 15, 30, 17, 60,  0, 49, 41, 33, 14, 16,  4],
      dtype=int64), 'cur_cost': 106204.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 22, 21, 20, 13, 23, 16, 18, 12, 17, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12359.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 8, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12719.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 37, 44, 23,  6, 18, 61, 38,  7, 35, 54, 27, 15, 33, 10, 30,  9,
       60, 32, 46,  2, 52, 57, 49, 11, 47, 20, 29, 26, 39, 56,  0, 65, 34,
       53, 21, 36, 58, 31, 40, 16,  3, 12, 14, 25, 13,  8,  4, 55, 45, 41,
       17, 64, 63,  1, 42, 51,  5, 62, 22, 48, 28, 59, 43, 50, 19],
      dtype=int64), 'cur_cost': 109941.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 3, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 4, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12740.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 41, 17, 45, 19, 12,  6, 55, 50, 48, 65, 32, 29, 61, 57, 37, 25,
       47,  3, 33, 56, 13, 20,  9, 38,  7,  1, 15, 21, 62,  0, 27, 59, 26,
       43, 54, 35, 42, 39, 63, 30, 44, 22, 49, 11,  4, 34, 46, 14, 40, 18,
       36,  5, 64, 10, 53,  8, 51, 16, 31, 58, 24, 23, 28, 52, 60],
      dtype=int64), 'cur_cost': 109829.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [51, 15, 35, 11, 3, 62, 65, 56, 59, 54, 12, 23, 30, 5, 58, 63, 61, 8, 6, 21, 25, 36, 14, 22, 9, 16, 18, 13, 10, 64, 4, 57, 53, 2, 52, 40, 39, 46, 41, 45, 38, 19, 37, 31, 32, 26, 17, 7, 20, 0, 55, 1, 27, 34, 49, 47, 42, 48, 43, 50, 33, 29, 24, 28, 44, 60], 'cur_cost': 53259.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [21, 3, 5, 2, 15, 25, 62, 28, 64, 31, 65, 12, 13, 27, 48, 45, 63, 43, 16, 30, 24, 26, 58, 7, 49, 6, 52, 29, 50, 46, 14, 18, 11, 56, 33, 4, 51, 38, 23, 17, 40, 44, 41, 20, 35, 57, 37, 34, 36, 60, 42, 32, 0, 9, 39, 61, 19, 1, 59, 10, 53, 8, 55, 22, 54, 47], 'cur_cost': 101876.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 24, 46, 54, 29,  9, 36,  3, 42, 43, 39, 31, 38, 37, 47, 26, 13,
       41,  7,  0, 19, 51, 15, 57, 21,  5, 49, 17,  6, 55, 35,  1, 65, 50,
       12, 63, 40, 56,  4, 32, 16, 28, 34, 14, 60, 11, 48, 44, 22, 33, 23,
       52, 30, 27, 10,  8, 25,  2, 62, 61, 64, 20, 59, 18, 45, 58],
      dtype=int64), 'cur_cost': 106930.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 10, 8, 2, 6, 4, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12852.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 9, 11, 7, 3, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12738.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 25, 44, 32,  1, 29, 23, 27, 12, 59, 39,  6, 38,  7, 61, 36, 63,
       16, 37, 54,  3, 49, 64, 45, 50, 15, 33,  5, 35, 51, 13, 42, 21,  4,
       57, 17, 28, 14, 62, 48, 41, 65, 18, 53, 26, 30, 60, 20,  8, 34, 22,
       19, 43,  0, 31, 11, 52, 10,  2, 24, 40, 56,  9, 55, 46, 58],
      dtype=int64), 'cur_cost': 119441.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [4, 55, 8, 63, 20, 29, 32, 30, 31, 13, 25, 37, 26, 23, 18, 28, 9, 11, 59, 10, 52, 53, 54, 17, 3, 6, 15, 0, 60, 19, 22, 34, 35, 43, 48, 38, 51, 44, 16, 27, 21, 49, 46, 14, 36, 47, 50, 24, 33, 39, 45, 5, 2, 58, 61, 65, 64, 62, 1, 56, 7, 57, 40, 42, 41, 12], 'cur_cost': 56359.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [13, 21, 2, 8, 59, 10, 37, 26, 58, 60, 14, 24, 65, 63, 7, 1, 9, 44, 25, 36, 28, 39, 47, 50, 32, 43, 12, 62, 55, 38, 45, 35, 56, 49, 18, 5, 0, 3, 22, 29, 48, 53, 15, 27, 46, 61, 17, 11, 19, 64, 34, 16, 41, 54, 57, 42, 51, 4, 23, 33, 40, 20, 52, 30, 31, 6], 'cur_cost': 93315.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 16, 49, 61, 65,  0, 47,  9, 33, 62, 35, 28, 32, 53, 19, 64, 17,
       31, 57, 63, 11, 24,  1, 51, 39, 13, 20, 25, 58,  5,  8, 21,  4, 29,
       45, 52, 18, 48, 10, 42, 23, 40,  3, 59, 55, 27, 44, 12, 60, 50,  2,
       22, 43, 46, 26, 56, 15,  6, 41,  7, 34, 37, 54, 30, 36, 38],
      dtype=int64), 'cur_cost': 111069.0}}]
2025-08-03 16:45:37,104 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:45:37,105 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:45:37,121 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12359.000, 多样性=0.937
2025-08-03 16:45:37,122 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:45:37,124 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:45:37,124 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:45:37,124 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.022230316242215165, 'best_improvement': -0.24800565485206502}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03721616525944755}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.853030303030303, 'new_diversity': 0.853030303030303, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:45:37,125 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:45:37,128 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:45:37,128 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_164537.solution
2025-08-03 16:45:37,130 - __main__ - INFO - 实例 composite13_66 处理完成
