2025-08-03 16:21:36,267 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 16:21:36,268 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 16:21:36,270 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:21:36,285 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9945.000, 多样性=0.966
2025-08-03 16:21:36,290 - PathExpert - INFO - 开始路径结构分析
2025-08-03 16:21:36,297 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.966
2025-08-03 16:21:36,366 - EliteExpert - INFO - 开始精英解分析
2025-08-03 16:21:36,370 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 16:21:36,370 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 16:21:36,371 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 16:21:36,371 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 16:21:36,655 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -3583.340, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 16:21:36,658 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 16:21:36,659 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 16:21:36,732 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 16:21:37,067 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_162136.html
2025-08-03 16:21:37,112 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_162136.html
2025-08-03 16:21:37,112 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 16:21:37,112 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 16:21:37,113 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7430秒
2025-08-03 16:21:37,114 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 16:21:37,117 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3583.3399999999992, 'local_optima_density': 0.2, 'gradient_variance': 2856810018.1043997, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0019, 'fitness_entropy': 0.8238654610595805, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3583.340)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754209296.658101, 'performance_metrics': {}}}
2025-08-03 16:21:37,120 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 16:21:37,121 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 16:21:37,122 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 16:21:37,123 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:21:37,123 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 16:21:37,124 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:21:37,124 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:21:37,125 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 16:21:37,125 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 16:21:37,126 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 16:21:37,126 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 16:21:37,126 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 3, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 16:21:37,127 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 16:21:37,127 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 16:21:37,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:37,134 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:21:37,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:37,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95333.0, 路径长度: 66
2025-08-03 16:21:37,338 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}
2025-08-03 16:21:37,339 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 95333.00)
2025-08-03 16:21:37,339 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 16:21:37,339 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:37,341 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:37,342 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 109918.0
2025-08-03 16:21:39,492 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 16:21:39,493 - ExploitationExpert - INFO - res_population_costs: [9837.0]
2025-08-03 16:21:39,493 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 16:21:39,494 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:39,494 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': array([ 8,  2,  6,  4,  5,  9, 11,  7,  3,  1,  0, 10, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10213.0}, {'tour': array([49, 40, 43, 48, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9971.0}, {'tour': array([45, 38, 51, 50, 44, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9962.0}, {'tour': array([54, 18,  2, 32, 19, 35, 64, 52, 23, 13, 51, 50,  7,  8, 10, 27, 21,
       58,  9, 28, 24, 37, 63, 62,  3, 36, 33, 61, 59, 15, 49, 65, 31, 40,
       57, 34, 29, 39,  1, 20, 16, 43,  6, 56, 26, 11, 22, 53, 17,  4, 44,
       38, 47, 14, 45, 55,  5, 42, 60,  0, 48, 25, 12, 30, 46, 41],
      dtype=int64), 'cur_cost': 100718.0}, {'tour': array([ 0, 20, 18, 13, 39, 58, 33, 25, 21, 19, 28, 65,  5, 43, 47, 59, 26,
       60, 10, 11, 42, 50, 15, 24, 31, 38, 62, 32,  1,  3, 44,  4, 30, 29,
        8, 40, 22, 57, 16, 37, 41, 53, 17, 51, 12, 27, 48, 36, 46, 49,  7,
       63, 45, 64,  9, 34, 55, 23,  6, 52, 54, 61, 56,  2, 14, 35],
      dtype=int64), 'cur_cost': 102863.0}, {'tour': array([21, 62, 26, 34, 23, 10, 17, 19, 50, 55, 47, 64, 49, 33, 43, 25, 58,
       31, 41, 30,  2, 60, 18, 42, 51, 36, 63,  3, 28, 44,  6, 16,  9, 20,
       59,  4, 24, 29,  5, 61, 11, 35, 39,  8, 32, 22, 38, 57, 46, 52, 53,
       37, 13, 15, 65, 45,  7, 56,  1, 40, 48, 54, 12,  0, 14, 27],
      dtype=int64), 'cur_cost': 115499.0}, {'tour': array([60, 18, 23, 36, 45, 53, 16, 62, 17, 29, 52,  8, 11, 58, 20, 10, 19,
       25, 41, 26, 39, 33, 30,  7, 28,  1, 22, 32,  0, 40,  9, 46, 37, 59,
        2, 12, 35, 34,  4, 31, 64, 50, 48, 55,  3, 42, 65, 56, 43, 49, 13,
       54,  6, 24, 51,  5, 21, 38, 15, 27, 57, 63, 61, 14, 47, 44],
      dtype=int64), 'cur_cost': 108532.0}, {'tour': array([34, 25, 37,  2, 14, 29, 15, 36, 27, 53, 30,  6, 35,  1, 24, 63, 55,
        3, 26, 18, 49, 46, 23, 20, 57,  9, 28, 47, 41, 56, 48, 45, 38, 58,
       12, 52, 11, 43, 10, 32, 39,  8, 60, 59, 22, 33, 16, 61, 31,  5, 17,
       54, 51,  0, 13, 19, 21,  7, 42, 65, 44, 40, 62,  4, 50, 64],
      dtype=int64), 'cur_cost': 107267.0}, {'tour': array([47, 16, 39, 62,  3, 31,  6, 21, 29, 12, 19, 28, 60, 65, 44, 61, 36,
       11, 43, 53,  4, 18, 42, 45, 49, 14, 27, 46, 63, 41, 23,  8,  9, 38,
       17, 54, 32, 13, 37, 20, 58, 30, 51, 50, 10, 64,  7, 25,  0, 56, 55,
       34, 52, 33, 40, 22, 48,  1, 24,  2, 26, 57,  5, 59, 35, 15],
      dtype=int64), 'cur_cost': 113552.0}, {'tour': array([58, 35, 39, 12,  1, 59, 56,  7,  9, 19, 57,  2, 25, 15, 26, 23, 37,
       42, 40, 51, 28, 41, 30, 13,  6, 63, 29, 24, 46, 36, 49, 45, 54, 60,
       48, 22, 20, 14, 43, 21, 38, 55,  3, 65, 27, 53, 11,  8, 47, 61, 33,
       62,  0, 18, 16, 64, 34, 44, 10,  5, 50, 17,  4, 31, 52, 32],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([39,  5, 51,  4, 27,  2, 32, 59, 57, 17, 42, 45, 55, 34, 23, 64, 38,
        7, 62, 63, 13, 40, 48, 35, 44, 52, 56, 15, 24, 30, 29,  0, 21,  6,
       50, 11,  8, 14, 28, 58, 36, 25, 37, 53,  1, 16, 12, 60, 20, 31, 19,
       18, 41, 54, 61, 10, 43, 49,  3, 22, 33, 46, 26, 65,  9, 47],
      dtype=int64), 'cur_cost': 102269.0}, {'tour': array([63, 49, 23, 41, 22, 10, 55, 36, 26, 60, 15, 14, 18, 45, 42, 21,  6,
       11, 47, 13,  1,  9, 24, 65, 35, 61, 64, 39,  2,  3, 56, 46, 44, 54,
       28, 37, 50, 51, 53, 12, 62, 43,  8, 16, 40, 34, 33, 20, 52, 19, 27,
       38, 58,  7, 32,  0, 25, 59, 31, 57,  4, 17,  5, 48, 30, 29],
      dtype=int64), 'cur_cost': 109630.0}, {'tour': array([14, 27, 49, 64, 16, 38, 24, 45, 25,  7, 43, 10,  6, 42,  3, 15, 60,
       34, 41, 39, 13, 46, 61, 12, 33, 56, 32, 57, 21, 29, 62,  1, 19,  4,
       44, 36, 37, 23, 51, 30, 53, 11, 22,  9,  0, 59, 20, 63, 26, 54, 17,
       28, 55,  8, 58, 18, 65, 48, 47,  5, 35, 52, 31, 40, 50,  2],
      dtype=int64), 'cur_cost': 121710.0}, {'tour': array([62, 42, 17, 60, 23, 35, 51,  5, 44, 37, 57, 53, 46, 13, 58, 21, 43,
       26, 50, 59, 11, 16,  1,  6,  4, 64, 38, 39, 30,  0, 28, 65, 15, 47,
       54, 25, 29, 32, 12, 27, 55, 63,  3, 20, 52, 49, 31, 40, 24, 41, 34,
       33, 36,  7, 18, 56,  9,  2, 10, 22,  8, 19, 14, 61, 45, 48],
      dtype=int64), 'cur_cost': 104988.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:39,506 - ExploitationExpert - INFO - 局部搜索耗时: 2.17秒
2025-08-03 16:21:39,506 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 16:21:39,506 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}
2025-08-03 16:21:39,507 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 109918.00)
2025-08-03 16:21:39,507 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 16:21:39,507 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 16:21:39,507 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:39,512 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:39,512 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:39,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12472.0, 路径长度: 66
2025-08-03 16:21:39,518 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}
2025-08-03 16:21:39,520 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12472.00)
2025-08-03 16:21:39,520 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 16:21:39,521 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 16:21:39,522 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:39,528 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:39,528 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:39,528 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14816.0, 路径长度: 66
2025-08-03 16:21:39,529 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}
2025-08-03 16:21:39,530 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 14816.00)
2025-08-03 16:21:39,530 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 16:21:39,531 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:39,531 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:39,533 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 111204.0
2025-08-03 16:21:42,132 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 16:21:42,133 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0]
2025-08-03 16:21:42,134 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:21:42,136 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:42,136 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': array([45, 38, 51, 50, 44, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9962.0}, {'tour': array([54, 18,  2, 32, 19, 35, 64, 52, 23, 13, 51, 50,  7,  8, 10, 27, 21,
       58,  9, 28, 24, 37, 63, 62,  3, 36, 33, 61, 59, 15, 49, 65, 31, 40,
       57, 34, 29, 39,  1, 20, 16, 43,  6, 56, 26, 11, 22, 53, 17,  4, 44,
       38, 47, 14, 45, 55,  5, 42, 60,  0, 48, 25, 12, 30, 46, 41],
      dtype=int64), 'cur_cost': 100718.0}, {'tour': array([ 0, 20, 18, 13, 39, 58, 33, 25, 21, 19, 28, 65,  5, 43, 47, 59, 26,
       60, 10, 11, 42, 50, 15, 24, 31, 38, 62, 32,  1,  3, 44,  4, 30, 29,
        8, 40, 22, 57, 16, 37, 41, 53, 17, 51, 12, 27, 48, 36, 46, 49,  7,
       63, 45, 64,  9, 34, 55, 23,  6, 52, 54, 61, 56,  2, 14, 35],
      dtype=int64), 'cur_cost': 102863.0}, {'tour': array([21, 62, 26, 34, 23, 10, 17, 19, 50, 55, 47, 64, 49, 33, 43, 25, 58,
       31, 41, 30,  2, 60, 18, 42, 51, 36, 63,  3, 28, 44,  6, 16,  9, 20,
       59,  4, 24, 29,  5, 61, 11, 35, 39,  8, 32, 22, 38, 57, 46, 52, 53,
       37, 13, 15, 65, 45,  7, 56,  1, 40, 48, 54, 12,  0, 14, 27],
      dtype=int64), 'cur_cost': 115499.0}, {'tour': array([60, 18, 23, 36, 45, 53, 16, 62, 17, 29, 52,  8, 11, 58, 20, 10, 19,
       25, 41, 26, 39, 33, 30,  7, 28,  1, 22, 32,  0, 40,  9, 46, 37, 59,
        2, 12, 35, 34,  4, 31, 64, 50, 48, 55,  3, 42, 65, 56, 43, 49, 13,
       54,  6, 24, 51,  5, 21, 38, 15, 27, 57, 63, 61, 14, 47, 44],
      dtype=int64), 'cur_cost': 108532.0}, {'tour': array([34, 25, 37,  2, 14, 29, 15, 36, 27, 53, 30,  6, 35,  1, 24, 63, 55,
        3, 26, 18, 49, 46, 23, 20, 57,  9, 28, 47, 41, 56, 48, 45, 38, 58,
       12, 52, 11, 43, 10, 32, 39,  8, 60, 59, 22, 33, 16, 61, 31,  5, 17,
       54, 51,  0, 13, 19, 21,  7, 42, 65, 44, 40, 62,  4, 50, 64],
      dtype=int64), 'cur_cost': 107267.0}, {'tour': array([47, 16, 39, 62,  3, 31,  6, 21, 29, 12, 19, 28, 60, 65, 44, 61, 36,
       11, 43, 53,  4, 18, 42, 45, 49, 14, 27, 46, 63, 41, 23,  8,  9, 38,
       17, 54, 32, 13, 37, 20, 58, 30, 51, 50, 10, 64,  7, 25,  0, 56, 55,
       34, 52, 33, 40, 22, 48,  1, 24,  2, 26, 57,  5, 59, 35, 15],
      dtype=int64), 'cur_cost': 113552.0}, {'tour': array([58, 35, 39, 12,  1, 59, 56,  7,  9, 19, 57,  2, 25, 15, 26, 23, 37,
       42, 40, 51, 28, 41, 30, 13,  6, 63, 29, 24, 46, 36, 49, 45, 54, 60,
       48, 22, 20, 14, 43, 21, 38, 55,  3, 65, 27, 53, 11,  8, 47, 61, 33,
       62,  0, 18, 16, 64, 34, 44, 10,  5, 50, 17,  4, 31, 52, 32],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([39,  5, 51,  4, 27,  2, 32, 59, 57, 17, 42, 45, 55, 34, 23, 64, 38,
        7, 62, 63, 13, 40, 48, 35, 44, 52, 56, 15, 24, 30, 29,  0, 21,  6,
       50, 11,  8, 14, 28, 58, 36, 25, 37, 53,  1, 16, 12, 60, 20, 31, 19,
       18, 41, 54, 61, 10, 43, 49,  3, 22, 33, 46, 26, 65,  9, 47],
      dtype=int64), 'cur_cost': 102269.0}, {'tour': array([63, 49, 23, 41, 22, 10, 55, 36, 26, 60, 15, 14, 18, 45, 42, 21,  6,
       11, 47, 13,  1,  9, 24, 65, 35, 61, 64, 39,  2,  3, 56, 46, 44, 54,
       28, 37, 50, 51, 53, 12, 62, 43,  8, 16, 40, 34, 33, 20, 52, 19, 27,
       38, 58,  7, 32,  0, 25, 59, 31, 57,  4, 17,  5, 48, 30, 29],
      dtype=int64), 'cur_cost': 109630.0}, {'tour': array([14, 27, 49, 64, 16, 38, 24, 45, 25,  7, 43, 10,  6, 42,  3, 15, 60,
       34, 41, 39, 13, 46, 61, 12, 33, 56, 32, 57, 21, 29, 62,  1, 19,  4,
       44, 36, 37, 23, 51, 30, 53, 11, 22,  9,  0, 59, 20, 63, 26, 54, 17,
       28, 55,  8, 58, 18, 65, 48, 47,  5, 35, 52, 31, 40, 50,  2],
      dtype=int64), 'cur_cost': 121710.0}, {'tour': array([62, 42, 17, 60, 23, 35, 51,  5, 44, 37, 57, 53, 46, 13, 58, 21, 43,
       26, 50, 59, 11, 16,  1,  6,  4, 64, 38, 39, 30,  0, 28, 65, 15, 47,
       54, 25, 29, 32, 12, 27, 55, 63,  3, 20, 52, 49, 31, 40, 24, 41, 34,
       33, 36,  7, 18, 56,  9,  2, 10, 22,  8, 19, 14, 61, 45, 48],
      dtype=int64), 'cur_cost': 104988.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:42,145 - ExploitationExpert - INFO - 局部搜索耗时: 2.61秒
2025-08-03 16:21:42,145 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 16:21:42,146 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}
2025-08-03 16:21:42,147 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 111204.00)
2025-08-03 16:21:42,151 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 16:21:42,152 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 16:21:42,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,159 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:42,159 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12725.0, 路径长度: 66
2025-08-03 16:21:42,160 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}
2025-08-03 16:21:42,160 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12725.00)
2025-08-03 16:21:42,160 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 16:21:42,160 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 16:21:42,160 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,166 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:42,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,170 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12344.0, 路径长度: 66
2025-08-03 16:21:42,170 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}
2025-08-03 16:21:42,170 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 12344.00)
2025-08-03 16:21:42,170 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 16:21:42,171 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:42,171 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:42,171 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 113854.0
2025-08-03 16:21:42,803 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 16:21:42,803 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0, 9554.0]
2025-08-03 16:21:42,803 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 65, 63, 52, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-03 16:21:42,805 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:42,805 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}, {'tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}, {'tour': array([21, 62, 26, 34, 23, 10, 17, 19, 50, 55, 47, 64, 49, 33, 43, 25, 58,
       31, 41, 30,  2, 60, 18, 42, 51, 36, 63,  3, 28, 44,  6, 16,  9, 20,
       59,  4, 24, 29,  5, 61, 11, 35, 39,  8, 32, 22, 38, 57, 46, 52, 53,
       37, 13, 15, 65, 45,  7, 56,  1, 40, 48, 54, 12,  0, 14, 27],
      dtype=int64), 'cur_cost': 115499.0}, {'tour': array([60, 18, 23, 36, 45, 53, 16, 62, 17, 29, 52,  8, 11, 58, 20, 10, 19,
       25, 41, 26, 39, 33, 30,  7, 28,  1, 22, 32,  0, 40,  9, 46, 37, 59,
        2, 12, 35, 34,  4, 31, 64, 50, 48, 55,  3, 42, 65, 56, 43, 49, 13,
       54,  6, 24, 51,  5, 21, 38, 15, 27, 57, 63, 61, 14, 47, 44],
      dtype=int64), 'cur_cost': 108532.0}, {'tour': array([34, 25, 37,  2, 14, 29, 15, 36, 27, 53, 30,  6, 35,  1, 24, 63, 55,
        3, 26, 18, 49, 46, 23, 20, 57,  9, 28, 47, 41, 56, 48, 45, 38, 58,
       12, 52, 11, 43, 10, 32, 39,  8, 60, 59, 22, 33, 16, 61, 31,  5, 17,
       54, 51,  0, 13, 19, 21,  7, 42, 65, 44, 40, 62,  4, 50, 64],
      dtype=int64), 'cur_cost': 107267.0}, {'tour': array([47, 16, 39, 62,  3, 31,  6, 21, 29, 12, 19, 28, 60, 65, 44, 61, 36,
       11, 43, 53,  4, 18, 42, 45, 49, 14, 27, 46, 63, 41, 23,  8,  9, 38,
       17, 54, 32, 13, 37, 20, 58, 30, 51, 50, 10, 64,  7, 25,  0, 56, 55,
       34, 52, 33, 40, 22, 48,  1, 24,  2, 26, 57,  5, 59, 35, 15],
      dtype=int64), 'cur_cost': 113552.0}, {'tour': array([58, 35, 39, 12,  1, 59, 56,  7,  9, 19, 57,  2, 25, 15, 26, 23, 37,
       42, 40, 51, 28, 41, 30, 13,  6, 63, 29, 24, 46, 36, 49, 45, 54, 60,
       48, 22, 20, 14, 43, 21, 38, 55,  3, 65, 27, 53, 11,  8, 47, 61, 33,
       62,  0, 18, 16, 64, 34, 44, 10,  5, 50, 17,  4, 31, 52, 32],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([39,  5, 51,  4, 27,  2, 32, 59, 57, 17, 42, 45, 55, 34, 23, 64, 38,
        7, 62, 63, 13, 40, 48, 35, 44, 52, 56, 15, 24, 30, 29,  0, 21,  6,
       50, 11,  8, 14, 28, 58, 36, 25, 37, 53,  1, 16, 12, 60, 20, 31, 19,
       18, 41, 54, 61, 10, 43, 49,  3, 22, 33, 46, 26, 65,  9, 47],
      dtype=int64), 'cur_cost': 102269.0}, {'tour': array([63, 49, 23, 41, 22, 10, 55, 36, 26, 60, 15, 14, 18, 45, 42, 21,  6,
       11, 47, 13,  1,  9, 24, 65, 35, 61, 64, 39,  2,  3, 56, 46, 44, 54,
       28, 37, 50, 51, 53, 12, 62, 43,  8, 16, 40, 34, 33, 20, 52, 19, 27,
       38, 58,  7, 32,  0, 25, 59, 31, 57,  4, 17,  5, 48, 30, 29],
      dtype=int64), 'cur_cost': 109630.0}, {'tour': array([14, 27, 49, 64, 16, 38, 24, 45, 25,  7, 43, 10,  6, 42,  3, 15, 60,
       34, 41, 39, 13, 46, 61, 12, 33, 56, 32, 57, 21, 29, 62,  1, 19,  4,
       44, 36, 37, 23, 51, 30, 53, 11, 22,  9,  0, 59, 20, 63, 26, 54, 17,
       28, 55,  8, 58, 18, 65, 48, 47,  5, 35, 52, 31, 40, 50,  2],
      dtype=int64), 'cur_cost': 121710.0}, {'tour': array([62, 42, 17, 60, 23, 35, 51,  5, 44, 37, 57, 53, 46, 13, 58, 21, 43,
       26, 50, 59, 11, 16,  1,  6,  4, 64, 38, 39, 30,  0, 28, 65, 15, 47,
       54, 25, 29, 32, 12, 27, 55, 63,  3, 20, 52, 49, 31, 40, 24, 41, 34,
       33, 36,  7, 18, 56,  9,  2, 10, 22,  8, 19, 14, 61, 45, 48],
      dtype=int64), 'cur_cost': 104988.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:42,817 - ExploitationExpert - INFO - 局部搜索耗时: 0.64秒
2025-08-03 16:21:42,818 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 16:21:42,819 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}
2025-08-03 16:21:42,820 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 113854.00)
2025-08-03 16:21:42,820 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 16:21:42,820 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 16:21:42,821 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,827 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:42,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,830 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14782.0, 路径长度: 66
2025-08-03 16:21:42,830 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}
2025-08-03 16:21:42,831 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14782.00)
2025-08-03 16:21:42,831 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 16:21:42,831 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 16:21:42,832 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,850 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:21:42,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,854 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62753.0, 路径长度: 66
2025-08-03 16:21:42,855 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}
2025-08-03 16:21:42,858 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 62753.00)
2025-08-03 16:21:42,858 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 16:21:42,859 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:42,860 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:42,863 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 103929.0
2025-08-03 16:21:42,944 - ExploitationExpert - INFO - res_population_num: 10
2025-08-03 16:21:42,944 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0, 9554.0, 9545.0, 9545, 9545, 9545, 9545, 9545, 9534]
2025-08-03 16:21:42,945 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 65, 63, 52, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-08-03 16:21:42,954 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:42,955 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}, {'tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}, {'tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}, {'tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}, {'tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}, {'tour': array([47, 16, 39, 62,  3, 31,  6, 21, 29, 12, 19, 28, 60, 65, 44, 61, 36,
       11, 43, 53,  4, 18, 42, 45, 49, 14, 27, 46, 63, 41, 23,  8,  9, 38,
       17, 54, 32, 13, 37, 20, 58, 30, 51, 50, 10, 64,  7, 25,  0, 56, 55,
       34, 52, 33, 40, 22, 48,  1, 24,  2, 26, 57,  5, 59, 35, 15],
      dtype=int64), 'cur_cost': 113552.0}, {'tour': array([58, 35, 39, 12,  1, 59, 56,  7,  9, 19, 57,  2, 25, 15, 26, 23, 37,
       42, 40, 51, 28, 41, 30, 13,  6, 63, 29, 24, 46, 36, 49, 45, 54, 60,
       48, 22, 20, 14, 43, 21, 38, 55,  3, 65, 27, 53, 11,  8, 47, 61, 33,
       62,  0, 18, 16, 64, 34, 44, 10,  5, 50, 17,  4, 31, 52, 32],
      dtype=int64), 'cur_cost': 104947.0}, {'tour': array([39,  5, 51,  4, 27,  2, 32, 59, 57, 17, 42, 45, 55, 34, 23, 64, 38,
        7, 62, 63, 13, 40, 48, 35, 44, 52, 56, 15, 24, 30, 29,  0, 21,  6,
       50, 11,  8, 14, 28, 58, 36, 25, 37, 53,  1, 16, 12, 60, 20, 31, 19,
       18, 41, 54, 61, 10, 43, 49,  3, 22, 33, 46, 26, 65,  9, 47],
      dtype=int64), 'cur_cost': 102269.0}, {'tour': array([63, 49, 23, 41, 22, 10, 55, 36, 26, 60, 15, 14, 18, 45, 42, 21,  6,
       11, 47, 13,  1,  9, 24, 65, 35, 61, 64, 39,  2,  3, 56, 46, 44, 54,
       28, 37, 50, 51, 53, 12, 62, 43,  8, 16, 40, 34, 33, 20, 52, 19, 27,
       38, 58,  7, 32,  0, 25, 59, 31, 57,  4, 17,  5, 48, 30, 29],
      dtype=int64), 'cur_cost': 109630.0}, {'tour': array([14, 27, 49, 64, 16, 38, 24, 45, 25,  7, 43, 10,  6, 42,  3, 15, 60,
       34, 41, 39, 13, 46, 61, 12, 33, 56, 32, 57, 21, 29, 62,  1, 19,  4,
       44, 36, 37, 23, 51, 30, 53, 11, 22,  9,  0, 59, 20, 63, 26, 54, 17,
       28, 55,  8, 58, 18, 65, 48, 47,  5, 35, 52, 31, 40, 50,  2],
      dtype=int64), 'cur_cost': 121710.0}, {'tour': array([62, 42, 17, 60, 23, 35, 51,  5, 44, 37, 57, 53, 46, 13, 58, 21, 43,
       26, 50, 59, 11, 16,  1,  6,  4, 64, 38, 39, 30,  0, 28, 65, 15, 47,
       54, 25, 29, 32, 12, 27, 55, 63,  3, 20, 52, 49, 31, 40, 24, 41, 34,
       33, 36,  7, 18, 56,  9,  2, 10, 22,  8, 19, 14, 61, 45, 48],
      dtype=int64), 'cur_cost': 104988.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:42,964 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:21:42,965 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 16:21:42,966 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}
2025-08-03 16:21:42,966 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 103929.00)
2025-08-03 16:21:42,967 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 16:21:42,967 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 16:21:42,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,971 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:42,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,972 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12395.0, 路径长度: 66
2025-08-03 16:21:42,972 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 9, 18, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12395.0}
2025-08-03 16:21:42,974 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 12395.00)
2025-08-03 16:21:42,974 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 16:21:42,974 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 16:21:42,975 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:42,992 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:21:42,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:42,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57660.0, 路径长度: 66
2025-08-03 16:21:42,993 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [61, 57, 0, 56, 5, 7, 60, 17, 16, 36, 27, 22, 2, 53, 20, 19, 6, 11, 37, 23, 29, 12, 25, 13, 14, 10, 3, 52, 59, 63, 64, 55, 39, 41, 44, 45, 46, 40, 35, 26, 21, 9, 1, 4, 33, 24, 34, 43, 38, 18, 8, 15, 30, 48, 50, 47, 28, 31, 51, 49, 42, 58, 62, 65, 54, 32], 'cur_cost': 57660.0}
2025-08-03 16:21:42,994 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 57660.00)
2025-08-03 16:21:42,994 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 16:21:42,995 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:42,995 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:42,995 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 92242.0
2025-08-03 16:21:43,088 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 16:21:43,088 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0, 9554.0, 9545.0, 9545, 9545, 9545, 9545, 9545, 9534, 9530]
2025-08-03 16:21:43,089 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 65, 63, 52, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 20, 21, 13, 23, 16, 18,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-08-03 16:21:43,095 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:43,095 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}, {'tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}, {'tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}, {'tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}, {'tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}, {'tour': [0, 9, 18, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12395.0}, {'tour': [61, 57, 0, 56, 5, 7, 60, 17, 16, 36, 27, 22, 2, 53, 20, 19, 6, 11, 37, 23, 29, 12, 25, 13, 14, 10, 3, 52, 59, 63, 64, 55, 39, 41, 44, 45, 46, 40, 35, 26, 21, 9, 1, 4, 33, 24, 34, 43, 38, 18, 8, 15, 30, 48, 50, 47, 28, 31, 51, 49, 42, 58, 62, 65, 54, 32], 'cur_cost': 57660.0}, {'tour': array([ 4, 27, 25, 11, 65, 18,  8, 64, 12, 29, 31, 52, 45, 54, 43, 50, 26,
       30, 35, 49, 13, 39, 32, 63, 58, 42,  9, 23,  3, 19,  1, 20, 53, 17,
        0,  6, 22, 41, 55, 24, 38, 47, 36, 10, 21, 14, 62, 34, 37, 28, 15,
       60, 59, 33, 51, 46, 40, 16, 48, 44,  2,  7, 57,  5, 61, 56],
      dtype=int64), 'cur_cost': 92242.0}, {'tour': array([63, 49, 23, 41, 22, 10, 55, 36, 26, 60, 15, 14, 18, 45, 42, 21,  6,
       11, 47, 13,  1,  9, 24, 65, 35, 61, 64, 39,  2,  3, 56, 46, 44, 54,
       28, 37, 50, 51, 53, 12, 62, 43,  8, 16, 40, 34, 33, 20, 52, 19, 27,
       38, 58,  7, 32,  0, 25, 59, 31, 57,  4, 17,  5, 48, 30, 29],
      dtype=int64), 'cur_cost': 109630.0}, {'tour': array([14, 27, 49, 64, 16, 38, 24, 45, 25,  7, 43, 10,  6, 42,  3, 15, 60,
       34, 41, 39, 13, 46, 61, 12, 33, 56, 32, 57, 21, 29, 62,  1, 19,  4,
       44, 36, 37, 23, 51, 30, 53, 11, 22,  9,  0, 59, 20, 63, 26, 54, 17,
       28, 55,  8, 58, 18, 65, 48, 47,  5, 35, 52, 31, 40, 50,  2],
      dtype=int64), 'cur_cost': 121710.0}, {'tour': array([62, 42, 17, 60, 23, 35, 51,  5, 44, 37, 57, 53, 46, 13, 58, 21, 43,
       26, 50, 59, 11, 16,  1,  6,  4, 64, 38, 39, 30,  0, 28, 65, 15, 47,
       54, 25, 29, 32, 12, 27, 55, 63,  3, 20, 52, 49, 31, 40, 24, 41, 34,
       33, 36,  7, 18, 56,  9,  2, 10, 22,  8, 19, 14, 61, 45, 48],
      dtype=int64), 'cur_cost': 104988.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:43,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-03 16:21:43,106 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 16:21:43,106 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([ 4, 27, 25, 11, 65, 18,  8, 64, 12, 29, 31, 52, 45, 54, 43, 50, 26,
       30, 35, 49, 13, 39, 32, 63, 58, 42,  9, 23,  3, 19,  1, 20, 53, 17,
        0,  6, 22, 41, 55, 24, 38, 47, 36, 10, 21, 14, 62, 34, 37, 28, 15,
       60, 59, 33, 51, 46, 40, 16, 48, 44,  2,  7, 57,  5, 61, 56],
      dtype=int64), 'cur_cost': 92242.0}
2025-08-03 16:21:43,107 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 92242.00)
2025-08-03 16:21:43,107 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 16:21:43,107 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 16:21:43,107 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:43,124 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 16:21:43,125 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:43,126 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62949.0, 路径长度: 66
2025-08-03 16:21:43,126 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [55, 12, 24, 34, 21, 35, 23, 18, 3, 10, 52, 58, 54, 0, 59, 8, 22, 30, 11, 16, 15, 7, 6, 13, 43, 49, 50, 27, 26, 2, 20, 28, 32, 40, 46, 14, 39, 33, 25, 31, 48, 44, 38, 19, 9, 63, 56, 57, 53, 60, 5, 61, 47, 41, 51, 17, 29, 1, 64, 62, 4, 65, 45, 42, 36, 37], 'cur_cost': 62949.0}
2025-08-03 16:21:43,127 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 62949.00)
2025-08-03 16:21:43,127 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 16:21:43,127 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 16:21:43,127 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:43,133 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 16:21:43,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:43,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10306.0, 路径长度: 66
2025-08-03 16:21:43,135 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 11, 7, 1, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10306.0}
2025-08-03 16:21:43,136 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 10306.00)
2025-08-03 16:21:43,136 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 16:21:43,137 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:43,137 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:43,137 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 102847.0
2025-08-03 16:21:43,219 - ExploitationExpert - INFO - res_population_num: 12
2025-08-03 16:21:43,219 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0, 9554.0, 9545.0, 9545, 9545, 9545, 9545, 9545, 9534, 9530, 9521]
2025-08-03 16:21:43,220 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 65, 63, 52, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 20, 21, 13, 23, 16, 18,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 16:21:43,227 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:43,227 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}, {'tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}, {'tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}, {'tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}, {'tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}, {'tour': [0, 9, 18, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12395.0}, {'tour': [61, 57, 0, 56, 5, 7, 60, 17, 16, 36, 27, 22, 2, 53, 20, 19, 6, 11, 37, 23, 29, 12, 25, 13, 14, 10, 3, 52, 59, 63, 64, 55, 39, 41, 44, 45, 46, 40, 35, 26, 21, 9, 1, 4, 33, 24, 34, 43, 38, 18, 8, 15, 30, 48, 50, 47, 28, 31, 51, 49, 42, 58, 62, 65, 54, 32], 'cur_cost': 57660.0}, {'tour': array([ 4, 27, 25, 11, 65, 18,  8, 64, 12, 29, 31, 52, 45, 54, 43, 50, 26,
       30, 35, 49, 13, 39, 32, 63, 58, 42,  9, 23,  3, 19,  1, 20, 53, 17,
        0,  6, 22, 41, 55, 24, 38, 47, 36, 10, 21, 14, 62, 34, 37, 28, 15,
       60, 59, 33, 51, 46, 40, 16, 48, 44,  2,  7, 57,  5, 61, 56],
      dtype=int64), 'cur_cost': 92242.0}, {'tour': [55, 12, 24, 34, 21, 35, 23, 18, 3, 10, 52, 58, 54, 0, 59, 8, 22, 30, 11, 16, 15, 7, 6, 13, 43, 49, 50, 27, 26, 2, 20, 28, 32, 40, 46, 14, 39, 33, 25, 31, 48, 44, 38, 19, 9, 63, 56, 57, 53, 60, 5, 61, 47, 41, 51, 17, 29, 1, 64, 62, 4, 65, 45, 42, 36, 37], 'cur_cost': 62949.0}, {'tour': [0, 11, 7, 1, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10306.0}, {'tour': array([45, 51, 34, 25, 11,  1, 52, 53, 17,  4,  9, 10,  8, 21, 30,  6, 47,
        3, 33, 19, 46, 26, 35, 23, 43, 29, 20, 44, 14,  0, 24, 49, 36, 48,
       54, 31, 56, 42, 61, 55, 27, 63, 65, 41,  2, 18, 64, 16, 60, 62,  5,
       57, 12, 37, 39, 15, 28, 58, 50, 32, 38, 40,  7, 59, 22, 13],
      dtype=int64), 'cur_cost': 102847.0}, {'tour': array([65, 50, 43, 42, 53, 49, 28,  8, 63, 18, 46, 31, 37, 12, 33, 41,  2,
       44,  1, 35, 19, 30, 54, 20,  6, 60, 34, 32, 22,  3, 59, 36, 15, 56,
       11, 45, 26, 38, 13, 17, 47, 48, 16, 23, 61,  0, 27, 64, 52,  9, 55,
        7, 58, 39, 10, 21,  5, 24, 29, 14, 25, 40, 51, 57, 62,  4],
      dtype=int64), 'cur_cost': 99378.0}, {'tour': array([62, 13, 30, 24,  0, 21, 22,  2, 33, 48, 16, 49, 12, 60, 39,  5,  4,
       65, 25, 46, 19, 54, 43, 53, 36, 27, 38,  3,  7, 47, 11, 31, 18, 15,
       41, 42, 58, 64, 63, 23, 29, 17, 50, 28, 20, 10,  1, 55, 26, 61, 52,
       37, 32, 34, 45, 56, 14,  8, 57, 59,  6, 40, 35, 51,  9, 44],
      dtype=int64), 'cur_cost': 103918.0}, {'tour': array([55, 54, 45, 59, 49,  0,  3, 16, 27, 19, 39, 13,  7, 11, 61, 51, 60,
       20, 34,  9, 36, 12, 15,  5,  8, 63, 22, 31, 30, 53, 58, 56, 33, 17,
       47, 52, 50, 14,  1, 62, 23, 10, 41, 65, 40, 28, 32, 37,  4, 25, 44,
       43, 64, 57, 42, 46, 38,  6, 29, 24, 18, 48, 35, 26, 21,  2],
      dtype=int64), 'cur_cost': 95417.0}]
2025-08-03 16:21:43,235 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 16:21:43,235 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 16:21:43,236 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([45, 51, 34, 25, 11,  1, 52, 53, 17,  4,  9, 10,  8, 21, 30,  6, 47,
        3, 33, 19, 46, 26, 35, 23, 43, 29, 20, 44, 14,  0, 24, 49, 36, 48,
       54, 31, 56, 42, 61, 55, 27, 63, 65, 41,  2, 18, 64, 16, 60, 62,  5,
       57, 12, 37, 39, 15, 28, 58, 50, 32, 38, 40,  7, 59, 22, 13],
      dtype=int64), 'cur_cost': 102847.0}
2025-08-03 16:21:43,236 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 102847.00)
2025-08-03 16:21:43,237 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 16:21:43,237 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 16:21:43,237 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:43,243 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:21:43,255 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:43,258 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105189.0, 路径长度: 66
2025-08-03 16:21:43,259 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [19, 18, 16, 56, 14, 4, 8, 10, 27, 25, 39, 36, 60, 57, 24, 26, 9, 55, 47, 3, 15, 20, 43, 2, 46, 61, 54, 28, 44, 22, 1, 45, 34, 33, 7, 40, 37, 58, 21, 53, 35, 52, 62, 17, 49, 51, 59, 64, 11, 13, 29, 30, 63, 50, 65, 0, 42, 32, 41, 12, 23, 38, 48, 6, 31, 5], 'cur_cost': 105189.0}
2025-08-03 16:21:43,260 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 105189.00)
2025-08-03 16:21:43,260 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 16:21:43,261 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 16:21:43,261 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 16:21:43,270 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 16:21:43,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 16:21:43,271 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109111.0, 路径长度: 66
2025-08-03 16:21:43,271 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [19, 16, 3, 49, 5, 31, 6, 35, 20, 52, 53, 36, 56, 58, 57, 21, 9, 55, 25, 47, 33, 65, 43, 1, 15, 14, 2, 42, 7, 29, 60, 62, 11, 59, 48, 64, 22, 18, 51, 63, 13, 54, 39, 41, 38, 26, 50, 45, 8, 32, 4, 10, 46, 0, 12, 27, 28, 40, 44, 24, 61, 30, 23, 34, 17, 37], 'cur_cost': 109111.0}
2025-08-03 16:21:43,272 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 109111.00)
2025-08-03 16:21:43,273 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 16:21:43,273 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 16:21:43,274 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 16:21:43,275 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 106098.0
2025-08-03 16:21:43,395 - ExploitationExpert - INFO - res_population_num: 13
2025-08-03 16:21:43,395 - ExploitationExpert - INFO - res_population_costs: [9837.0, 9594.0, 9554.0, 9545.0, 9545, 9545, 9545, 9545, 9545, 9534, 9530, 9521, 9521]
2025-08-03 16:21:43,395 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  5,  4,  6,  2,  8, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 65, 63, 52, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10, 55, 56, 59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 18, 17, 12, 22, 23, 16, 19,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 36, 26, 25, 37, 24, 31, 33, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 20, 21, 13, 23, 16, 18,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 16:21:43,403 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 16:21:43,404 - ExploitationExpert - INFO - populations: [{'tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}, {'tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}, {'tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}, {'tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}, {'tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}, {'tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}, {'tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}, {'tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}, {'tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}, {'tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}, {'tour': [0, 9, 18, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12395.0}, {'tour': [61, 57, 0, 56, 5, 7, 60, 17, 16, 36, 27, 22, 2, 53, 20, 19, 6, 11, 37, 23, 29, 12, 25, 13, 14, 10, 3, 52, 59, 63, 64, 55, 39, 41, 44, 45, 46, 40, 35, 26, 21, 9, 1, 4, 33, 24, 34, 43, 38, 18, 8, 15, 30, 48, 50, 47, 28, 31, 51, 49, 42, 58, 62, 65, 54, 32], 'cur_cost': 57660.0}, {'tour': array([ 4, 27, 25, 11, 65, 18,  8, 64, 12, 29, 31, 52, 45, 54, 43, 50, 26,
       30, 35, 49, 13, 39, 32, 63, 58, 42,  9, 23,  3, 19,  1, 20, 53, 17,
        0,  6, 22, 41, 55, 24, 38, 47, 36, 10, 21, 14, 62, 34, 37, 28, 15,
       60, 59, 33, 51, 46, 40, 16, 48, 44,  2,  7, 57,  5, 61, 56],
      dtype=int64), 'cur_cost': 92242.0}, {'tour': [55, 12, 24, 34, 21, 35, 23, 18, 3, 10, 52, 58, 54, 0, 59, 8, 22, 30, 11, 16, 15, 7, 6, 13, 43, 49, 50, 27, 26, 2, 20, 28, 32, 40, 46, 14, 39, 33, 25, 31, 48, 44, 38, 19, 9, 63, 56, 57, 53, 60, 5, 61, 47, 41, 51, 17, 29, 1, 64, 62, 4, 65, 45, 42, 36, 37], 'cur_cost': 62949.0}, {'tour': [0, 11, 7, 1, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10306.0}, {'tour': array([45, 51, 34, 25, 11,  1, 52, 53, 17,  4,  9, 10,  8, 21, 30,  6, 47,
        3, 33, 19, 46, 26, 35, 23, 43, 29, 20, 44, 14,  0, 24, 49, 36, 48,
       54, 31, 56, 42, 61, 55, 27, 63, 65, 41,  2, 18, 64, 16, 60, 62,  5,
       57, 12, 37, 39, 15, 28, 58, 50, 32, 38, 40,  7, 59, 22, 13],
      dtype=int64), 'cur_cost': 102847.0}, {'tour': [19, 18, 16, 56, 14, 4, 8, 10, 27, 25, 39, 36, 60, 57, 24, 26, 9, 55, 47, 3, 15, 20, 43, 2, 46, 61, 54, 28, 44, 22, 1, 45, 34, 33, 7, 40, 37, 58, 21, 53, 35, 52, 62, 17, 49, 51, 59, 64, 11, 13, 29, 30, 63, 50, 65, 0, 42, 32, 41, 12, 23, 38, 48, 6, 31, 5], 'cur_cost': 105189.0}, {'tour': [19, 16, 3, 49, 5, 31, 6, 35, 20, 52, 53, 36, 56, 58, 57, 21, 9, 55, 25, 47, 33, 65, 43, 1, 15, 14, 2, 42, 7, 29, 60, 62, 11, 59, 48, 64, 22, 18, 51, 63, 13, 54, 39, 41, 38, 26, 50, 45, 8, 32, 4, 10, 46, 0, 12, 27, 28, 40, 44, 24, 61, 30, 23, 34, 17, 37], 'cur_cost': 109111.0}, {'tour': array([40,  0, 56,  9, 28, 61,  6, 44, 10,  7, 35, 14, 15, 47, 17,  8, 43,
        4, 30, 64, 55,  3, 46, 51, 12, 41, 26, 21, 58, 22,  2, 24, 38, 59,
       13, 37, 62, 20, 50, 16, 65, 33, 54, 63, 57, 39, 19, 27, 29, 31, 18,
       42, 23, 25,  1, 32, 36, 60, 11, 34,  5, 52, 49, 48, 53, 45],
      dtype=int64), 'cur_cost': 106098.0}]
2025-08-03 16:21:43,409 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-03 16:21:43,410 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 16:21:43,410 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([40,  0, 56,  9, 28, 61,  6, 44, 10,  7, 35, 14, 15, 47, 17,  8, 43,
        4, 30, 64, 55,  3, 46, 51, 12, 41, 26, 21, 58, 22,  2, 24, 38, 59,
       13, 37, 62, 20, 50, 16, 65, 33, 54, 63, 57, 39, 19, 27, 29, 31, 18,
       42, 23, 25,  1, 32, 36, 60, 11, 34,  5, 52, 49, 48, 53, 45],
      dtype=int64), 'cur_cost': 106098.0}
2025-08-03 16:21:43,410 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 106098.00)
2025-08-03 16:21:43,411 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 16:21:43,411 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 16:21:43,412 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [29, 32, 24, 16, 27, 56, 49, 43, 33, 50, 40, 62, 35, 38, 52, 41, 39, 12, 5, 23, 7, 19, 17, 0, 45, 34, 21, 4, 13, 9, 1, 44, 26, 25, 53, 55, 47, 37, 57, 65, 63, 58, 3, 15, 51, 20, 48, 14, 2, 10, 46, 64, 30, 42, 61, 54, 18, 28, 60, 8, 6, 59, 11, 31, 22, 36], 'cur_cost': 95333.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([17,  9, 21, 53, 47,  6,  7, 48, 11, 37, 32, 64, 14, 56,  0, 33, 20,
       40, 59, 65, 38, 45, 63, 25, 39, 35, 22, 46,  4, 18, 26,  5, 50, 42,
       43, 44,  8, 41, 16,  1, 36, 19, 52, 57, 23, 49,  3, 60, 24, 27, 61,
       12, 13, 29, 51, 28, 55, 34, 10, 54, 31, 15,  2, 62, 58, 30],
      dtype=int64), 'cur_cost': 109918.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 19, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12472.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 9, 22, 12, 17, 15, 14, 23, 16, 18, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14816.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 42, 17,  8, 59, 45, 20, 60, 24, 14, 41, 61, 62, 16, 12, 58, 21,
        5, 29,  7, 43, 65, 34, 57,  2, 40,  3, 31, 56, 30, 28, 47,  4, 18,
       52, 35, 26,  9, 55, 36,  6, 51,  0, 23,  1, 39, 15, 27, 63, 37, 32,
       25, 22, 11, 10, 33, 54, 64, 49, 50, 19, 46, 53, 13, 48, 44],
      dtype=int64), 'cur_cost': 111204.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 11, 9, 3, 7, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12725.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 13, 18, 16, 23, 22, 12, 17, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  2, 44, 10, 33, 22, 43, 30,  1, 64, 27, 25, 11, 37, 28, 46, 51,
        9, 48, 32, 65, 19, 50, 42, 34,  6, 49, 63, 38, 14, 35, 47, 12, 31,
       24, 61, 40,  8, 18, 29, 55,  0, 36,  5, 15, 54, 41, 53, 39, 60, 13,
       23, 26, 52,  4, 20, 58, 17, 59,  7, 57, 62, 45, 16, 21, 56],
      dtype=int64), 'cur_cost': 113854.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 6, 22, 12, 17, 15, 14, 23, 16, 18, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14782.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [22, 23, 16, 7, 2, 12, 14, 33, 32, 31, 10, 62, 0, 17, 40, 46, 45, 34, 8, 59, 5, 18, 24, 25, 9, 54, 3, 61, 13, 28, 4, 20, 21, 49, 44, 38, 15, 37, 35, 19, 43, 26, 50, 30, 11, 56, 55, 52, 58, 57, 65, 64, 6, 27, 36, 42, 51, 48, 47, 41, 29, 39, 1, 63, 53, 60], 'cur_cost': 62753.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 55, 65, 24, 20, 11,  3, 10, 43, 22, 38, 60, 41, 37, 31, 34, 23,
       61, 25, 39, 15, 42, 29,  0, 58, 57, 45,  7, 56, 54, 28, 51, 36, 53,
       47, 40, 63,  5, 17, 21, 52, 62, 16, 30,  4, 18, 27,  8, 46, 26, 19,
       48, 32, 50,  6, 59, 35, 33, 64, 44, 12,  2,  9, 14, 13, 49],
      dtype=int64), 'cur_cost': 103929.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12395.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [61, 57, 0, 56, 5, 7, 60, 17, 16, 36, 27, 22, 2, 53, 20, 19, 6, 11, 37, 23, 29, 12, 25, 13, 14, 10, 3, 52, 59, 63, 64, 55, 39, 41, 44, 45, 46, 40, 35, 26, 21, 9, 1, 4, 33, 24, 34, 43, 38, 18, 8, 15, 30, 48, 50, 47, 28, 31, 51, 49, 42, 58, 62, 65, 54, 32], 'cur_cost': 57660.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 27, 25, 11, 65, 18,  8, 64, 12, 29, 31, 52, 45, 54, 43, 50, 26,
       30, 35, 49, 13, 39, 32, 63, 58, 42,  9, 23,  3, 19,  1, 20, 53, 17,
        0,  6, 22, 41, 55, 24, 38, 47, 36, 10, 21, 14, 62, 34, 37, 28, 15,
       60, 59, 33, 51, 46, 40, 16, 48, 44,  2,  7, 57,  5, 61, 56],
      dtype=int64), 'cur_cost': 92242.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [55, 12, 24, 34, 21, 35, 23, 18, 3, 10, 52, 58, 54, 0, 59, 8, 22, 30, 11, 16, 15, 7, 6, 13, 43, 49, 50, 27, 26, 2, 20, 28, 32, 40, 46, 14, 39, 33, 25, 31, 48, 44, 38, 19, 9, 63, 56, 57, 53, 60, 5, 61, 47, 41, 51, 17, 29, 1, 64, 62, 4, 65, 45, 42, 36, 37], 'cur_cost': 62949.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 7, 1, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10306.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([45, 51, 34, 25, 11,  1, 52, 53, 17,  4,  9, 10,  8, 21, 30,  6, 47,
        3, 33, 19, 46, 26, 35, 23, 43, 29, 20, 44, 14,  0, 24, 49, 36, 48,
       54, 31, 56, 42, 61, 55, 27, 63, 65, 41,  2, 18, 64, 16, 60, 62,  5,
       57, 12, 37, 39, 15, 28, 58, 50, 32, 38, 40,  7, 59, 22, 13],
      dtype=int64), 'cur_cost': 102847.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [19, 18, 16, 56, 14, 4, 8, 10, 27, 25, 39, 36, 60, 57, 24, 26, 9, 55, 47, 3, 15, 20, 43, 2, 46, 61, 54, 28, 44, 22, 1, 45, 34, 33, 7, 40, 37, 58, 21, 53, 35, 52, 62, 17, 49, 51, 59, 64, 11, 13, 29, 30, 63, 50, 65, 0, 42, 32, 41, 12, 23, 38, 48, 6, 31, 5], 'cur_cost': 105189.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [19, 16, 3, 49, 5, 31, 6, 35, 20, 52, 53, 36, 56, 58, 57, 21, 9, 55, 25, 47, 33, 65, 43, 1, 15, 14, 2, 42, 7, 29, 60, 62, 11, 59, 48, 64, 22, 18, 51, 63, 13, 54, 39, 41, 38, 26, 50, 45, 8, 32, 4, 10, 46, 0, 12, 27, 28, 40, 44, 24, 61, 30, 23, 34, 17, 37], 'cur_cost': 109111.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([40,  0, 56,  9, 28, 61,  6, 44, 10,  7, 35, 14, 15, 47, 17,  8, 43,
        4, 30, 64, 55,  3, 46, 51, 12, 41, 26, 21, 58, 22,  2, 24, 38, 59,
       13, 37, 62, 20, 50, 16, 65, 33, 54, 63, 57, 39, 19, 27, 29, 31, 18,
       42, 23, 25,  1, 32, 36, 60, 11, 34,  5, 52, 49, 48, 53, 45],
      dtype=int64), 'cur_cost': 106098.0}}]
2025-08-03 16:21:43,419 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 16:21:43,421 - StatsExpert - INFO - 开始统计分析
2025-08-03 16:21:43,435 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10306.000, 多样性=0.945
2025-08-03 16:21:43,436 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 16:21:43,436 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 16:21:43,437 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 16:21:43,439 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05842272620989286, 'best_improvement': -0.03629964806435395}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.02162786858180628}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8776223776223776, 'new_diversity': 0.8776223776223776, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 16:21:43,442 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 16:21:43,445 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 16:21:43,445 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_162143.solution
2025-08-03 16:21:43,446 - __main__ - INFO - 实例 composite13_66 处理完成
