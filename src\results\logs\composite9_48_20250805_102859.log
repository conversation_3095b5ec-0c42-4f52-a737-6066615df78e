2025-08-05 10:28:59,916 - __main__ - INFO - composite9_48 开始进化第 1 代
2025-08-05 10:28:59,916 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:59,918 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:59,921 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7023.000, 多样性=0.975
2025-08-05 10:28:59,924 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:59,929 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.975
2025-08-05 10:28:59,932 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:59,935 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:59,935 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:59,935 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:59,935 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:59,960 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -3907.860, 聚类评分: 0.000, 覆盖率: 0.119, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:59,960 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:59,960 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:59,960 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite9_48
2025-08-05 10:28:59,969 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.1%, 梯度: 1429.64 → 1327.95
2025-08-05 10:29:00,103 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\landscape_composite9_48_iter_101_20250805_102900.html
2025-08-05 10:29:00,178 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\dashboard_composite9_48_iter_101_20250805_102900.html
2025-08-05 10:29:00,178 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 101
2025-08-05 10:29:00,178 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:00,178 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2436秒
2025-08-05 10:29:00,178 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 202, 'max_size': 500, 'hits': 0, 'misses': 202, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 670, 'misses': 354, 'hit_rate': 0.654296875, 'evictions': 254, 'ttl': 7200}}
2025-08-05 10:29:00,179 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3907.8599999999997, 'local_optima_density': 0.1, 'gradient_variance': 822091203.8084, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1189, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3907.860)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.119)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360939.9601076, 'performance_metrics': {}}}
2025-08-05 10:29:00,179 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:00,179 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:00,179 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:00,179 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:00,180 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,180 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:00,180 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,180 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,180 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:00,180 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,180 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,181 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:00,181 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:00,181 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:00,181 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:00,181 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,186 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33334.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,187 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,187 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 33334.00)
2025-08-05 10:29:00,187 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:00,187 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:00,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,191 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,192 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,192 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30935.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,192 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,192 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 30935.00)
2025-08-05 10:29:00,192 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:00,193 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:00,193 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,194 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:00,194 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41986.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,195 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 41986.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,195 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 41986.00)
2025-08-05 10:29:00,195 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:00,195 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:00,195 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,199 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,199 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32929.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,199 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 32929.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,200 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 32929.00)
2025-08-05 10:29:00,200 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:00,200 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:00,200 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,201 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:00,202 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,202 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46109.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,202 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 46109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,203 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 46109.00)
2025-08-05 10:29:00,203 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:00,203 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,203 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 50731.0
2025-08-05 10:29:00,215 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:00,215 - ExploitationExpert - INFO - res_population_costs: [6783.0, 6782]
2025-08-05 10:29:00,216 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,217 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,217 - ExploitationExpert - INFO - populations: [{'tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30935.0}, {'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 41986.0}, {'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 32929.0}, {'tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 46109.0}, {'tour': array([39, 46, 29, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38],
      dtype=int64), 'cur_cost': 50731.0}, {'tour': array([20, 21,  7, 35, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61039.0}, {'tour': array([16, 20, 12,  6,  3, 24, 28, 19, 35, 21, 25, 23,  1, 30, 43, 39, 47,
       46,  8, 37, 44, 15, 29, 14, 41,  9, 27,  0,  4, 33, 38,  5, 13, 26,
       22, 11, 31, 45, 40,  7,  2, 34, 10, 18, 17, 32, 36, 42],
      dtype=int64), 'cur_cost': 49596.0}, {'tour': array([14, 31, 38, 24, 40, 17, 28,  1, 43, 22, 45, 33,  7, 13, 42, 41, 15,
       11, 30, 35, 19, 46,  4, 23, 21, 18,  8, 25, 47, 26, 44, 32, 36, 16,
       10,  3,  9, 37, 34,  6,  5, 27, 39,  2, 20, 29,  0, 12],
      dtype=int64), 'cur_cost': 58744.0}, {'tour': array([22, 12,  1, 18,  4,  0, 34,  5, 37, 19, 27, 30, 10, 32, 38, 16, 15,
       31, 47, 11,  2, 42, 33, 24, 40, 35, 46, 25, 39, 13, 21, 17,  8,  9,
       44, 14, 43, 20, 29,  3,  7, 28, 26,  6, 45, 23, 41, 36],
      dtype=int64), 'cur_cost': 54504.0}]
2025-08-05 10:29:00,219 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:00,219 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 261, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 261, 'cache_hits': 0, 'similarity_calculations': 1278, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,219 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([39, 46, 29, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38],
      dtype=int64), 'cur_cost': 50731.0, 'intermediate_solutions': [{'tour': array([25, 21, 11, 40, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 25, 21, 11, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61877.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 40, 25, 21, 11, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 60455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 40, 25, 21, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 60334.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 18, 40, 25, 21, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,220 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 50731.00)
2025-08-05 10:29:00,220 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:00,220 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,220 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,220 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 60960.0
2025-08-05 10:29:00,230 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:00,231 - ExploitationExpert - INFO - res_population_costs: [6783.0, 6782]
2025-08-05 10:29:00,231 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,232 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,232 - ExploitationExpert - INFO - populations: [{'tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30935.0}, {'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 41986.0}, {'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 32929.0}, {'tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 46109.0}, {'tour': array([39, 46, 29, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38],
      dtype=int64), 'cur_cost': 50731.0}, {'tour': array([ 5, 15, 36, 18,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45],
      dtype=int64), 'cur_cost': 60960.0}, {'tour': array([16, 20, 12,  6,  3, 24, 28, 19, 35, 21, 25, 23,  1, 30, 43, 39, 47,
       46,  8, 37, 44, 15, 29, 14, 41,  9, 27,  0,  4, 33, 38,  5, 13, 26,
       22, 11, 31, 45, 40,  7,  2, 34, 10, 18, 17, 32, 36, 42],
      dtype=int64), 'cur_cost': 49596.0}, {'tour': array([14, 31, 38, 24, 40, 17, 28,  1, 43, 22, 45, 33,  7, 13, 42, 41, 15,
       11, 30, 35, 19, 46,  4, 23, 21, 18,  8, 25, 47, 26, 44, 32, 36, 16,
       10,  3,  9, 37, 34,  6,  5, 27, 39,  2, 20, 29,  0, 12],
      dtype=int64), 'cur_cost': 58744.0}, {'tour': array([22, 12,  1, 18,  4,  0, 34,  5, 37, 19, 27, 30, 10, 32, 38, 16, 15,
       31, 47, 11,  2, 42, 33, 24, 40, 35, 46, 25, 39, 13, 21, 17,  8,  9,
       44, 14, 43, 20, 29,  3,  7, 28, 26,  6, 45, 23, 41, 36],
      dtype=int64), 'cur_cost': 54504.0}]
2025-08-05 10:29:00,234 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:00,234 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 262, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 262, 'cache_hits': 0, 'similarity_calculations': 1279, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,235 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 5, 15, 36, 18,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45],
      dtype=int64), 'cur_cost': 60960.0, 'intermediate_solutions': [{'tour': array([ 7, 21, 20, 35, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 62428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35,  7, 21, 20, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 35,  7, 21, 20,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 35,  7, 21, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 60945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 19, 35,  7, 21,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,235 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 60960.00)
2025-08-05 10:29:00,235 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:00,235 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:00,236 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,237 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,237 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,238 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10673.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,238 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10673.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,238 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10673.00)
2025-08-05 10:29:00,238 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:00,238 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:00,238 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,242 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,242 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31675.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,243 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 31675.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,243 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 31675.00)
2025-08-05 10:29:00,243 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:00,243 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:00,243 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,245 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10666.0, 路径长度: 48, 收集中间解: 0
2025-08-05 10:29:00,245 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 10666.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,245 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10666.00)
2025-08-05 10:29:00,245 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:00,245 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:00,247 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30935.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 41986.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 32929.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 46109.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 46, 29, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38],
      dtype=int64), 'cur_cost': 50731.0, 'intermediate_solutions': [{'tour': array([25, 21, 11, 40, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40, 25, 21, 11, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61877.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 40, 25, 21, 11, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 60455.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 40, 25, 21, 18, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 60334.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 18, 40, 25, 21, 41, 16,  6, 32, 24, 33, 10, 35, 27, 42, 28,  1,
       44, 23,  0, 34, 37, 31, 30, 38, 19,  4,  2, 20, 13,  3, 43,  8, 17,
       26, 12, 39,  5, 46, 45, 36, 14, 47,  7, 29,  9, 22, 15],
      dtype=int64), 'cur_cost': 61788.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 15, 36, 18,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45],
      dtype=int64), 'cur_cost': 60960.0, 'intermediate_solutions': [{'tour': array([ 7, 21, 20, 35, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 62428.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([35,  7, 21, 20, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61031.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 35,  7, 21, 20,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61024.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([20, 35,  7, 21, 19,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 60945.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([20, 19, 35,  7, 21,  5, 42, 18, 24,  6, 16,  8, 44, 39, 15, 46, 11,
       37, 45, 33, 32,  9, 12, 36, 17,  1, 31, 10, 30, 27, 28, 29, 25,  3,
       43,  0, 13, 34,  2, 47, 40,  4, 41, 23, 38, 26, 14, 22],
      dtype=int64), 'cur_cost': 61042.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10673.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 31675.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 10666.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:00,247 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:00,247 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,250 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10666.000, 多样性=0.965
2025-08-05 10:29:00,250 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:00,250 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:00,250 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:00,251 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11528243732051202, 'best_improvement': -0.518724191940766}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.010446343779677002}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.038472491358005116, 'recent_improvements': [-0.036831456837985665, -0.02278568513637215, 0.04011352587802456], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 6782, 'new_best_cost': 6782, 'quality_improvement': 0.0, 'old_diversity': 0.10416666666666667, 'new_diversity': 0.10416666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:00,251 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:00,251 - __main__ - INFO - composite9_48 开始进化第 2 代
2025-08-05 10:29:00,251 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:00,251 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,252 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10666.000, 多样性=0.965
2025-08-05 10:29:00,252 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:00,254 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.965
2025-08-05 10:29:00,254 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:00,255 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.104
2025-08-05 10:29:00,257 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:00,257 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:00,257 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:29:00,257 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:29:00,273 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 640.217, 聚类评分: 0.000, 覆盖率: 0.120, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:00,274 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:00,274 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:00,274 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite9_48
2025-08-05 10:29:00,278 - visualization.landscape_visualizer - INFO - 插值约束: 2 个点被约束到最小值 6782.00
2025-08-05 10:29:00,281 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.8%, 梯度: 1773.66 → 1653.02
2025-08-05 10:29:00,427 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\landscape_composite9_48_iter_102_20250805_102900.html
2025-08-05 10:29:00,504 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\dashboard_composite9_48_iter_102_20250805_102900.html
2025-08-05 10:29:00,504 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 102
2025-08-05 10:29:00,504 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:00,505 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2485秒
2025-08-05 10:29:00,505 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 640.2166666666666, 'local_optima_density': 0.25, 'gradient_variance': 181059577.40305555, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.12, 'fitness_entropy': 0.8710490642551529, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.120)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 640.217)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360940.2742918, 'performance_metrics': {}}}
2025-08-05 10:29:00,505 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:00,506 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:00,506 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:00,506 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:00,507 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,507 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:00,507 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,507 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,507 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:00,507 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,507 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,508 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:00,508 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:00,508 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:00,508 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:00,508 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,510 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,511 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,511 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7090.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,511 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7090.0, 'intermediate_solutions': [{'tour': [16, 31, 18, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 14, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33393.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 31, 14, 27, 7, 0, 2, 9, 3, 40, 19, 18, 15, 46, 17, 23, 30, 44, 43, 33, 45, 13, 39, 25, 12, 21, 20, 37, 42, 28, 34, 24, 38, 36, 29, 26, 35, 22, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 35584.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,512 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 7090.00)
2025-08-05 10:29:00,512 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:00,512 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:00,512 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,513 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:00,513 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,513 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,513 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45105.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,514 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [14, 28, 22, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23, 13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21, 1, 36, 6, 0, 3, 9, 8, 2, 42, 11, 7, 5, 47, 18, 46, 40, 4, 31], 'cur_cost': 45105.0, 'intermediate_solutions': [{'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 1, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 46, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 39125.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 43, 42, 26, 37, 46, 47, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 45, 26, 42, 43, 25, 23, 13, 15, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 29100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,514 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 45105.00)
2025-08-05 10:29:00,514 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:00,514 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:00,515 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,517 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,518 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,518 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29335.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,518 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29335.0, 'intermediate_solutions': [{'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 41, 13, 25, 39, 31, 32, 43, 30, 23, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 43318.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 45, 47, 15, 11, 4, 42, 33, 28, 35, 3, 5, 40, 36, 1, 21, 7, 12, 46, 22], 'cur_cost': 41985.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 14, 27, 16, 18, 29, 44, 38, 24, 34, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 43533.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,519 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 29335.00)
2025-08-05 10:29:00,519 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:00,519 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:00,519 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,522 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,523 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32551.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,523 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32551.0, 'intermediate_solutions': [{'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 4, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 35, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 37538.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 43, 11], 'cur_cost': 37036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 31, 15, 29, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 22, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 31498.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,523 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 32551.00)
2025-08-05 10:29:00,523 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:00,523 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:00,523 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,526 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,526 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,526 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,527 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30699.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,527 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 30699.0, 'intermediate_solutions': [{'tour': [40, 27, 24, 29, 35, 16, 18, 14, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 44124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 8, 26, 3, 4, 39, 5, 1, 7, 34, 23, 43], 'cur_cost': 46130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 27, 14, 29, 35, 16, 47, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 47559.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,527 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 30699.00)
2025-08-05 10:29:00,527 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:00,527 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,528 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,528 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 56776.0
2025-08-05 10:29:00,537 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:00,538 - ExploitationExpert - INFO - res_population_costs: [6782, 6783.0]
2025-08-05 10:29:00,538 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,539 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,539 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7090.0}, {'tour': [14, 28, 22, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23, 13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21, 1, 36, 6, 0, 3, 9, 8, 2, 42, 11, 7, 5, 47, 18, 46, 40, 4, 31], 'cur_cost': 45105.0}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29335.0}, {'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32551.0}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 30699.0}, {'tour': array([39,  5, 31, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44],
      dtype=int64), 'cur_cost': 56776.0}, {'tour': [5, 15, 36, 18, 9, 16, 27, 35, 13, 47, 17, 28, 39, 3, 26, 0, 40, 25, 6, 30, 33, 2, 23, 14, 43, 1, 31, 8, 11, 32, 34, 10, 44, 42, 4, 37, 29, 22, 41, 38, 21, 20, 12, 7, 19, 46, 24, 45], 'cur_cost': 60960.0}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10673.0}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 31675.0}, {'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 10666.0}]
2025-08-05 10:29:00,540 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:00,540 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 263, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 263, 'cache_hits': 0, 'similarity_calculations': 1281, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,541 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([39,  5, 31, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44],
      dtype=int64), 'cur_cost': 56776.0, 'intermediate_solutions': [{'tour': array([29, 46, 39, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52610.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 29, 46, 39, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 11, 29, 46, 39, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52774.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 11, 29, 46, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 28, 11, 29, 46, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,541 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 56776.00)
2025-08-05 10:29:00,541 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:00,541 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,541 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,542 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 55775.0
2025-08-05 10:29:00,551 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:00,551 - ExploitationExpert - INFO - res_population_costs: [6782, 6783.0]
2025-08-05 10:29:00,551 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,552 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,553 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7090.0}, {'tour': [14, 28, 22, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23, 13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21, 1, 36, 6, 0, 3, 9, 8, 2, 42, 11, 7, 5, 47, 18, 46, 40, 4, 31], 'cur_cost': 45105.0}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29335.0}, {'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32551.0}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 30699.0}, {'tour': array([39,  5, 31, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44],
      dtype=int64), 'cur_cost': 56776.0}, {'tour': array([43, 17, 26,  4, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13],
      dtype=int64), 'cur_cost': 55775.0}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10673.0}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 31675.0}, {'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 10666.0}]
2025-08-05 10:29:00,554 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:00,554 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 264, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 264, 'cache_hits': 0, 'similarity_calculations': 1284, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,555 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([43, 17, 26,  4, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13],
      dtype=int64), 'cur_cost': 55775.0, 'intermediate_solutions': [{'tour': array([36, 15,  5, 18,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 59201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 36, 15,  5,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 58668.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 18, 36, 15,  5, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 60995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 36, 15,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 60959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  9, 18, 36, 15, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 57272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,555 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 55775.00)
2025-08-05 10:29:00,555 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:00,555 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:00,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,559 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,560 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,561 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27761.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,561 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27761.0, 'intermediate_solutions': [{'tour': [0, 22, 15, 8, 10, 29, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 6, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 15793.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 46, 45, 40, 38, 39, 37, 13, 23, 47, 43, 41, 44, 42, 36], 'cur_cost': 14118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 43, 45, 46, 47, 41, 44, 42, 36], 'cur_cost': 10771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,561 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 27761.00)
2025-08-05 10:29:00,561 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:00,561 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:00,561 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,567 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33273.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,568 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [9, 29, 27, 26, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 33273.0, 'intermediate_solutions': [{'tour': [1, 33, 28, 42, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 32, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 34037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 11, 2, 6, 8, 10, 0, 7, 42, 41, 21, 15, 47, 46, 19], 'cur_cost': 31631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 24, 23, 46, 47, 15, 21, 4, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 34022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,568 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 33273.00)
2025-08-05 10:29:00,568 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:00,568 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:00,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,573 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,573 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,574 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,574 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39171.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,574 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 39171.0, 'intermediate_solutions': [{'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 35, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 22, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 12500.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 9, 6, 5, 8, 10, 1, 7, 44, 37, 39, 41, 43, 47, 46, 45, 38, 40, 42, 36, 33, 35, 27, 32, 25, 34, 28, 31, 24, 29, 30, 26, 22, 20, 16, 2, 11, 4], 'cur_cost': 14402.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 3, 13, 21, 12, 18, 34, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 12505.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,575 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 39171.00)
2025-08-05 10:29:00,575 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:00,575 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:00,577 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7090.0, 'intermediate_solutions': [{'tour': [16, 31, 18, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 14, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33393.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [16, 31, 14, 27, 7, 0, 2, 9, 3, 40, 19, 18, 15, 46, 17, 23, 30, 44, 43, 33, 45, 13, 39, 25, 12, 21, 20, 37, 42, 28, 34, 24, 38, 36, 29, 26, 35, 22, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 35584.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [16, 31, 14, 27, 22, 35, 26, 29, 36, 38, 24, 34, 28, 42, 37, 20, 21, 12, 25, 39, 13, 45, 33, 43, 44, 30, 23, 17, 46, 15, 18, 19, 40, 3, 9, 2, 0, 7, 1, 6, 10, 8, 11, 4, 32, 47, 41, 5], 'cur_cost': 33334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [14, 28, 22, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23, 13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21, 1, 36, 6, 0, 3, 9, 8, 2, 42, 11, 7, 5, 47, 18, 46, 40, 4, 31], 'cur_cost': 45105.0, 'intermediate_solutions': [{'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 1, 37, 26, 42, 43, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 46, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 39125.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 43, 42, 26, 37, 46, 47, 25, 23, 13, 15, 45, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 30933.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [34, 28, 22, 14, 35, 16, 18, 36, 44, 47, 46, 37, 45, 26, 42, 43, 25, 23, 13, 15, 31, 32, 39, 33, 20, 24, 41, 19, 30, 38, 29, 7, 6, 0, 10, 9, 8, 5, 1, 11, 3, 27, 21, 12, 17, 40, 4, 2], 'cur_cost': 29100.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29335.0, 'intermediate_solutions': [{'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 41, 13, 25, 39, 31, 32, 43, 30, 23, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 43318.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 27, 16, 18, 29, 44, 38, 24, 34, 26, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 45, 47, 15, 11, 4, 42, 33, 28, 35, 3, 5, 40, 36, 1, 21, 7, 12, 46, 22], 'cur_cost': 41985.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 14, 27, 16, 18, 29, 44, 38, 24, 34, 37, 20, 23, 13, 25, 39, 31, 32, 43, 30, 41, 17, 19, 6, 0, 9, 2, 8, 10, 21, 1, 36, 40, 5, 3, 35, 28, 33, 42, 4, 11, 15, 47, 45, 7, 12, 46, 22], 'cur_cost': 43533.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32551.0, 'intermediate_solutions': [{'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 4, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 35, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 37538.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 31, 15, 29, 22, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 43, 11], 'cur_cost': 37036.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 31, 15, 29, 37, 44, 39, 47, 41, 24, 28, 30, 13, 17, 26, 21, 32, 25, 14, 35, 16, 22, 34, 46, 36, 33, 19, 38, 12, 23, 45, 6, 10, 5, 4, 0, 9, 2, 7, 8, 27, 42, 20, 18, 3, 1, 11, 43], 'cur_cost': 31498.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 30699.0, 'intermediate_solutions': [{'tour': [40, 27, 24, 29, 35, 16, 18, 14, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 44124.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 27, 14, 29, 35, 16, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 47, 8, 26, 3, 4, 39, 5, 1, 7, 34, 23, 43], 'cur_cost': 46130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 27, 14, 29, 35, 16, 47, 18, 24, 36, 38, 46, 37, 28, 25, 20, 12, 15, 45, 13, 32, 19, 33, 44, 30, 41, 17, 10, 21, 6, 0, 9, 2, 42, 11, 22, 31, 5, 39, 4, 3, 26, 8, 1, 7, 34, 23, 43], 'cur_cost': 47559.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([39,  5, 31, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44],
      dtype=int64), 'cur_cost': 56776.0, 'intermediate_solutions': [{'tour': array([29, 46, 39, 11, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52610.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11, 29, 46, 39, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28, 11, 29, 46, 39, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52774.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 11, 29, 46, 28, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 28, 11, 29, 46, 25,  0,  4, 44, 21, 13, 37, 14,  2,  7,  8, 20,
       24, 27,  9, 32, 47, 31, 18, 15, 40, 12, 42, 41, 45, 23, 10, 16,  3,
       43, 19, 22,  6, 17, 35, 30,  5, 26,  1, 33, 34, 36, 38]), 'cur_cost': 52787.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 17, 26,  4, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13],
      dtype=int64), 'cur_cost': 55775.0, 'intermediate_solutions': [{'tour': array([36, 15,  5, 18,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 59201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 36, 15,  5,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 58668.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 18, 36, 15,  5, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 60995.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 18, 36, 15,  9, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 60959.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  9, 18, 36, 15, 16, 27, 35, 13, 47, 17, 28, 39,  3, 26,  0, 40,
       25,  6, 30, 33,  2, 23, 14, 43,  1, 31,  8, 11, 32, 34, 10, 44, 42,
        4, 37, 29, 22, 41, 38, 21, 20, 12,  7, 19, 46, 24, 45]), 'cur_cost': 57272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27761.0, 'intermediate_solutions': [{'tour': [0, 22, 15, 8, 10, 29, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 6, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 15793.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 46, 45, 40, 38, 39, 37, 13, 23, 47, 43, 41, 44, 42, 36], 'cur_cost': 14118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 15, 8, 10, 6, 5, 4, 9, 2, 11, 3, 1, 7, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 43, 45, 46, 47, 41, 44, 42, 36], 'cur_cost': 10771.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [9, 29, 27, 26, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 33273.0, 'intermediate_solutions': [{'tour': [1, 33, 28, 42, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 46, 47, 15, 21, 41, 32, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 34037.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 4, 24, 23, 11, 2, 6, 8, 10, 0, 7, 42, 41, 21, 15, 47, 46, 19], 'cur_cost': 31631.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 33, 28, 32, 26, 12, 37, 35, 14, 18, 17, 39, 44, 43, 27, 25, 29, 16, 20, 22, 31, 40, 38, 13, 30, 36, 45, 34, 3, 9, 5, 24, 23, 46, 47, 15, 21, 4, 41, 42, 7, 0, 10, 8, 6, 2, 11, 19], 'cur_cost': 34022.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 39171.0, 'intermediate_solutions': [{'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 16, 20, 35, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 22, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 12500.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 3, 13, 21, 12, 18, 19, 23, 15, 17, 9, 6, 5, 8, 10, 1, 7, 44, 37, 39, 41, 43, 47, 46, 45, 38, 40, 42, 36, 33, 35, 27, 32, 25, 34, 28, 31, 24, 29, 30, 26, 22, 20, 16, 2, 11, 4], 'cur_cost': 14402.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 3, 13, 21, 12, 18, 34, 19, 23, 15, 17, 16, 20, 22, 26, 30, 29, 24, 31, 28, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 12505.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:00,577 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:00,577 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,580 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7090.000, 多样性=0.972
2025-08-05 10:29:00,581 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:00,581 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:00,581 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:00,581 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.12745628637036432, 'best_improvement': 0.33527095443465216}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.007677543186180433}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04624837609206994, 'recent_improvements': [-0.02278568513637215, 0.04011352587802456, -0.11528243732051202], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 6782, 'new_best_cost': 6782, 'quality_improvement': 0.0, 'old_diversity': 0.10416666666666667, 'new_diversity': 0.10416666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:29:00,581 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:00,581 - __main__ - INFO - composite9_48 开始进化第 3 代
2025-08-05 10:29:00,581 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:00,581 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,582 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7090.000, 多样性=0.972
2025-08-05 10:29:00,582 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:00,584 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.972
2025-08-05 10:29:00,585 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:00,585 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.104
2025-08-05 10:29:00,587 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:00,587 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:00,587 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:29:00,587 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:29:00,606 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.083, 适应度梯度: 1440.950, 聚类评分: 0.000, 覆盖率: 0.121, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:00,606 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:00,607 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:00,607 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite9_48
2025-08-05 10:29:00,611 - visualization.landscape_visualizer - INFO - 插值约束: 63 个点被约束到最小值 6782.00
2025-08-05 10:29:00,613 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=2.8%, 梯度: 1829.79 → 1778.78
2025-08-05 10:29:00,772 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\landscape_composite9_48_iter_103_20250805_102900.html
2025-08-05 10:29:00,866 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\dashboard_composite9_48_iter_103_20250805_102900.html
2025-08-05 10:29:00,866 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 103
2025-08-05 10:29:00,866 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:00,866 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2793秒
2025-08-05 10:29:00,866 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.08333333333333333, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1440.95, 'local_optima_density': 0.08333333333333333, 'gradient_variance': 269506573.95416665, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1209, 'fitness_entropy': 0.9513282751069652, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.121)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1440.950)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360940.606304, 'performance_metrics': {}}}
2025-08-05 10:29:00,867 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:00,867 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:00,867 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:00,867 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:00,868 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,868 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:00,868 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,868 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,868 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:00,868 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:00,869 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:00,869 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:00,869 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:00,869 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:00,869 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:00,870 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,871 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,872 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,872 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,872 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,872 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8848.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,872 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8848.0, 'intermediate_solutions': [{'tour': [0, 25, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 3, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 9931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 43, 47, 46, 45, 38, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 45, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 8947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,873 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 8848.00)
2025-08-05 10:29:00,873 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:00,873 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,873 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,873 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 59903.0
2025-08-05 10:29:00,883 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:29:00,883 - ExploitationExpert - INFO - res_population_costs: [6782, 6783.0]
2025-08-05 10:29:00,884 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,885 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,885 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8848.0}, {'tour': array([14, 13, 32, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6],
      dtype=int64), 'cur_cost': 59903.0}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29335.0}, {'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32551.0}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 30699.0}, {'tour': [39, 5, 31, 28, 27, 12, 16, 42, 8, 36, 34, 19, 10, 24, 33, 43, 11, 26, 18, 22, 1, 2, 29, 25, 45, 14, 4, 23, 20, 7, 30, 37, 21, 0, 13, 6, 17, 46, 15, 3, 38, 32, 9, 35, 40, 41, 47, 44], 'cur_cost': 56776.0}, {'tour': [43, 17, 26, 4, 39, 35, 37, 0, 15, 23, 36, 3, 40, 14, 18, 41, 7, 16, 5, 10, 28, 38, 24, 6, 9, 1, 33, 8, 30, 21, 45, 22, 29, 47, 27, 44, 20, 42, 46, 19, 12, 32, 31, 11, 2, 34, 25, 13], 'cur_cost': 55775.0}, {'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27761.0}, {'tour': [9, 29, 27, 26, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 33273.0}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 39171.0}]
2025-08-05 10:29:00,885 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:00,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 265, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 265, 'cache_hits': 0, 'similarity_calculations': 1288, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,886 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([14, 13, 32, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6],
      dtype=int64), 'cur_cost': 59903.0, 'intermediate_solutions': [{'tour': array([22, 28, 14, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 22, 28, 14, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 12, 22, 28, 14, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 43341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 12, 22, 28, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 43339.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 29, 12, 22, 28, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,886 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 59903.00)
2025-08-05 10:29:00,886 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:00,887 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:00,887 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,890 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29843.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,891 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 29843.0, 'intermediate_solutions': [{'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 2, 9, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29333.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 32, 47, 25, 42, 45, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 31146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 10, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 31669.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,891 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 29843.00)
2025-08-05 10:29:00,891 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:00,892 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:00,892 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,895 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:00,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,896 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35989.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,896 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35989.0, 'intermediate_solutions': [{'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 43, 42, 27, 34, 13, 36, 6, 5, 1], 'cur_cost': 30506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 2, 4, 10, 3, 0, 9, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32578.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 16, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 34265.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,896 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 35989.00)
2025-08-05 10:29:00,896 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:00,896 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:00,896 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,897 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:00,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,898 - ExplorationExpert - INFO - 探索路径生成完成，成本: 44835.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,898 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 44835.0, 'intermediate_solutions': [{'tour': [13, 16, 27, 17, 23, 3, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 35, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 35391.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 44, 37, 40, 38, 12, 6, 0, 10, 3, 5, 2, 8, 9, 4, 7, 46, 28, 26, 11], 'cur_cost': 34761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 5, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 32971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,898 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 44835.00)
2025-08-05 10:29:00,899 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:00,899 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,899 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,899 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 53031.0
2025-08-05 10:29:00,909 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:00,910 - ExploitationExpert - INFO - res_population_costs: [6782, 6783.0, 6770.0, 6770, 6770, 6770, 6769]
2025-08-05 10:29:00,910 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,912 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,912 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8848.0}, {'tour': array([14, 13, 32, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6],
      dtype=int64), 'cur_cost': 59903.0}, {'tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 29843.0}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35989.0}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 44835.0}, {'tour': array([38, 16,  6, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37],
      dtype=int64), 'cur_cost': 53031.0}, {'tour': [43, 17, 26, 4, 39, 35, 37, 0, 15, 23, 36, 3, 40, 14, 18, 41, 7, 16, 5, 10, 28, 38, 24, 6, 9, 1, 33, 8, 30, 21, 45, 22, 29, 47, 27, 44, 20, 42, 46, 19, 12, 32, 31, 11, 2, 34, 25, 13], 'cur_cost': 55775.0}, {'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27761.0}, {'tour': [9, 29, 27, 26, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 33273.0}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 39171.0}]
2025-08-05 10:29:00,913 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:00,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 266, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 266, 'cache_hits': 0, 'similarity_calculations': 1293, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,914 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([38, 16,  6, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37],
      dtype=int64), 'cur_cost': 53031.0, 'intermediate_solutions': [{'tour': array([31,  5, 39, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 31,  5, 39, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 28, 31,  5, 39, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 28, 31,  5, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 57032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 27, 28, 31,  5, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 56748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,914 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 53031.00)
2025-08-05 10:29:00,915 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:00,915 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:00,915 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:00,915 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 58342.0
2025-08-05 10:29:00,932 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:00,932 - ExploitationExpert - INFO - res_population_costs: [6782, 6783.0, 6770.0, 6770, 6770, 6770, 6769]
2025-08-05 10:29:00,932 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:00,935 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:00,935 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8848.0}, {'tour': array([14, 13, 32, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6],
      dtype=int64), 'cur_cost': 59903.0}, {'tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 29843.0}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35989.0}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 44835.0}, {'tour': array([38, 16,  6, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37],
      dtype=int64), 'cur_cost': 53031.0}, {'tour': array([33, 10, 17, 26, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36],
      dtype=int64), 'cur_cost': 58342.0}, {'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27761.0}, {'tour': [9, 29, 27, 26, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 33273.0}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 39171.0}]
2025-08-05 10:29:00,936 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:00,936 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 267, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 267, 'cache_hits': 0, 'similarity_calculations': 1299, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:00,937 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([33, 10, 17, 26, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36],
      dtype=int64), 'cur_cost': 58342.0, 'intermediate_solutions': [{'tour': array([26, 17, 43,  4, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 26, 17, 43, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 53960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([39,  4, 26, 17, 43, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43,  4, 26, 17, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55727.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 39,  4, 26, 17, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 53965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:00,937 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 58342.00)
2025-08-05 10:29:00,937 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:00,937 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:00,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,939 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:00,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,941 - ExplorationExpert - INFO - 探索路径生成完成，成本: 43668.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,941 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0, 'intermediate_solutions': [{'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 30, 28, 33, 12, 39, 42, 37, 36, 29, 46, 26, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27749.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 4, 22, 32, 17, 20, 16, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 45, 12, 39, 42, 37, 36, 29, 46, 30, 15, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 29497.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,941 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 43668.00)
2025-08-05 10:29:00,941 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:00,942 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:00,942 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,944 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,944 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,945 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10945.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,945 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10945.0, 'intermediate_solutions': [{'tour': [9, 29, 27, 23, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 26, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 34957.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 29, 27, 26, 15, 30, 22, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 34952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 9, 29, 27, 26, 15, 22, 30, 28, 31, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 32946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,945 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 10945.00)
2025-08-05 10:29:00,945 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:00,945 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:00,945 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:00,947 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:00,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,948 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:00,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10776.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:00,949 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10776.0, 'intermediate_solutions': [{'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 14, 3, 11, 4, 0, 5, 1, 22, 47, 41], 'cur_cost': 39194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 8, 7, 40, 16, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 42912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 13, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 37419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:00,949 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 10776.00)
2025-08-05 10:29:00,949 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:00,949 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:00,951 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8848.0, 'intermediate_solutions': [{'tour': [0, 25, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 3, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 9931.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 43, 47, 46, 45, 38, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 7172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 21, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 13, 45, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 4], 'cur_cost': 8947.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 13, 32, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6],
      dtype=int64), 'cur_cost': 59903.0, 'intermediate_solutions': [{'tour': array([22, 28, 14, 12, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45023.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 22, 28, 14, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([29, 12, 22, 28, 14, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 43341.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 12, 22, 28, 29, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 43339.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 29, 12, 22, 28, 19, 44, 16, 17, 26, 37, 15, 30, 25, 43, 39, 23,
       13, 27, 45, 35, 32, 34, 33, 20, 24, 41, 38, 10, 21,  1, 36,  6,  0,
        3,  9,  8,  2, 42, 11,  7,  5, 47, 18, 46, 40,  4, 31]), 'cur_cost': 45105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 29843.0, 'intermediate_solutions': [{'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 2, 9, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 29333.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 32, 47, 25, 42, 45, 1, 9, 2, 10, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 31146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 28, 13, 12, 14, 31, 23, 29, 24, 34, 35, 33, 17, 20, 30, 10, 19, 38, 36, 44, 43, 16, 26, 21, 27, 37, 41, 22, 46, 45, 42, 25, 47, 32, 1, 9, 2, 6, 4, 7, 8, 5, 11, 3, 15, 18, 39, 40], 'cur_cost': 31669.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35989.0, 'intermediate_solutions': [{'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 43, 42, 27, 34, 13, 36, 6, 5, 1], 'cur_cost': 30506.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 16, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 2, 4, 10, 3, 0, 9, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 32578.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 32, 23, 17, 18, 15, 20, 19, 39, 40, 24, 26, 45, 33, 16, 29, 22, 21, 28, 46, 31, 38, 35, 25, 44, 41, 37, 30, 47, 12, 7, 9, 0, 3, 10, 4, 2, 11, 8, 34, 42, 27, 43, 13, 36, 6, 5, 1], 'cur_cost': 34265.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 44835.0, 'intermediate_solutions': [{'tour': [13, 16, 27, 17, 23, 3, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 5, 35, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 35391.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 45, 47, 14, 41, 43, 29, 15, 1, 44, 37, 40, 38, 12, 6, 0, 10, 3, 5, 2, 8, 9, 4, 7, 46, 28, 26, 11], 'cur_cost': 34761.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 16, 27, 17, 23, 35, 33, 20, 36, 34, 18, 19, 24, 22, 42, 39, 31, 30, 32, 25, 21, 5, 45, 47, 14, 41, 43, 29, 15, 1, 7, 4, 9, 8, 2, 3, 10, 0, 6, 12, 38, 40, 37, 44, 46, 28, 26, 11], 'cur_cost': 32971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 16,  6, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37],
      dtype=int64), 'cur_cost': 53031.0, 'intermediate_solutions': [{'tour': array([31,  5, 39, 28, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 31,  5, 39, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58759.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 28, 31,  5, 39, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 58509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([39, 28, 31,  5, 27, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 57032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([39, 27, 28, 31,  5, 12, 16, 42,  8, 36, 34, 19, 10, 24, 33, 43, 11,
       26, 18, 22,  1,  2, 29, 25, 45, 14,  4, 23, 20,  7, 30, 37, 21,  0,
       13,  6, 17, 46, 15,  3, 38, 32,  9, 35, 40, 41, 47, 44]), 'cur_cost': 56748.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 10, 17, 26, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36],
      dtype=int64), 'cur_cost': 58342.0, 'intermediate_solutions': [{'tour': array([26, 17, 43,  4, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 26, 17, 43, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 53960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([39,  4, 26, 17, 43, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55758.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([43,  4, 26, 17, 39, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 55727.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([43, 39,  4, 26, 17, 35, 37,  0, 15, 23, 36,  3, 40, 14, 18, 41,  7,
       16,  5, 10, 28, 38, 24,  6,  9,  1, 33,  8, 30, 21, 45, 22, 29, 47,
       27, 44, 20, 42, 46, 19, 12, 32, 31, 11,  2, 34, 25, 13]), 'cur_cost': 53965.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0, 'intermediate_solutions': [{'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 30, 28, 33, 12, 39, 42, 37, 36, 29, 46, 26, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27749.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 4, 22, 32, 17, 20, 16, 21, 34, 35, 18, 26, 28, 33, 12, 39, 42, 37, 36, 29, 46, 30, 15, 45, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 27741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 4, 22, 16, 20, 17, 32, 21, 34, 35, 18, 26, 28, 33, 45, 12, 39, 42, 37, 36, 29, 46, 30, 15, 38, 44, 41, 47, 23, 25, 31, 13, 27, 14, 19, 3, 7, 0, 8, 6, 1, 2, 5, 11, 9, 24, 40, 43], 'cur_cost': 29497.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10945.0, 'intermediate_solutions': [{'tour': [9, 29, 27, 23, 15, 22, 30, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 26, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 34957.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 29, 27, 26, 15, 30, 22, 28, 31, 14, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 34952.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 9, 29, 27, 26, 15, 22, 30, 28, 31, 24, 17, 34, 25, 38, 13, 21, 23, 12, 32, 40, 44, 45, 35, 33, 43, 20, 37, 36, 7, 1, 5, 11, 3, 6, 2, 8, 16, 46, 42, 18, 19, 47, 41, 10, 0, 4, 39], 'cur_cost': 32946.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10776.0, 'intermediate_solutions': [{'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 14, 3, 11, 4, 0, 5, 1, 22, 47, 41], 'cur_cost': 39194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 13, 44, 18, 8, 7, 40, 16, 6, 10, 2, 9, 28, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 42912.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [46, 34, 35, 24, 12, 26, 19, 37, 30, 15, 25, 32, 21, 39, 43, 27, 20, 31, 17, 38, 36, 44, 18, 16, 40, 7, 8, 6, 10, 2, 9, 28, 13, 45, 29, 23, 42, 33, 22, 3, 11, 4, 0, 5, 1, 14, 47, 41], 'cur_cost': 37419.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:00,952 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:00,952 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,956 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8848.000, 多样性=0.967
2025-08-05 10:29:00,956 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:00,957 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:00,957 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:00,958 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0997461098060808, 'best_improvement': -0.24795486600846262}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0052380952380951815}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04367138024616988, 'recent_improvements': [0.04011352587802456, -0.11528243732051202, 0.12745628637036432], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 6769, 'new_best_cost': 6769, 'quality_improvement': 0.0, 'old_diversity': 0.7093253968253969, 'new_diversity': 0.7093253968253969, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:00,959 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:00,959 - __main__ - INFO - composite9_48 开始进化第 4 代
2025-08-05 10:29:00,959 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:00,959 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:00,960 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8848.000, 多样性=0.967
2025-08-05 10:29:00,960 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:00,963 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.967
2025-08-05 10:29:00,963 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:00,965 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.709
2025-08-05 10:29:00,967 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:00,967 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:00,967 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:29:00,967 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:29:01,004 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -7753.929, 聚类评分: 0.000, 覆盖率: 0.122, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:01,004 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:01,004 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:01,004 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite9_48
2025-08-05 10:29:01,012 - visualization.landscape_visualizer - INFO - 插值约束: 181 个点被约束到最小值 6769.00
2025-08-05 10:29:01,014 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.7%, 梯度: 2922.99 → 2640.46
2025-08-05 10:29:01,154 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\landscape_composite9_48_iter_104_20250805_102901.html
2025-08-05 10:29:01,217 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\dashboard_composite9_48_iter_104_20250805_102901.html
2025-08-05 10:29:01,217 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 104
2025-08-05 10:29:01,217 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:01,218 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2512秒
2025-08-05 10:29:01,218 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7753.929411764705, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 352821414.4232526, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1223, 'fitness_entropy': 0.7180212293982637, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7753.929)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.122)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360941.0040987, 'performance_metrics': {}}}
2025-08-05 10:29:01,218 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:01,218 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:01,218 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:01,218 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:01,219 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,219 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:01,219 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,219 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,219 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:01,220 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,220 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,220 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:01,220 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:01,221 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:01,221 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:01,221 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,224 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:01,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,225 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,225 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35425.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,225 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 35425.0, 'intermediate_solutions': [{'tour': [0, 20, 24, 10, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 13, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 14933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 5, 8, 10, 1, 7, 44, 37, 39, 41, 43, 47, 46, 45, 38, 40, 42, 36, 31, 28, 33, 6, 9, 2, 11, 3, 4], 'cur_cost': 11455.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 24, 11, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 3, 4], 'cur_cost': 11149.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,225 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 35425.00)
2025-08-05 10:29:01,225 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:01,226 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,226 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,226 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 55543.0
2025-08-05 10:29:01,236 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:01,237 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0]
2025-08-05 10:29:01,237 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,239 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,239 - ExploitationExpert - INFO - populations: [{'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 35425.0}, {'tour': array([21, 37, 31, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6],
      dtype=int64), 'cur_cost': 55543.0}, {'tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 29843.0}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35989.0}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 44835.0}, {'tour': [38, 16, 6, 40, 9, 20, 17, 3, 29, 18, 47, 2, 27, 33, 35, 21, 39, 5, 26, 41, 36, 12, 14, 42, 45, 32, 24, 1, 25, 4, 28, 30, 44, 43, 13, 7, 0, 34, 31, 10, 11, 46, 22, 15, 8, 19, 23, 37], 'cur_cost': 53031.0}, {'tour': [33, 10, 17, 26, 25, 24, 19, 32, 8, 23, 21, 46, 4, 13, 47, 18, 34, 11, 44, 0, 35, 45, 38, 28, 43, 22, 16, 6, 2, 29, 42, 3, 9, 5, 14, 15, 40, 30, 39, 1, 27, 37, 31, 41, 20, 7, 12, 36], 'cur_cost': 58342.0}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10945.0}, {'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10776.0}]
2025-08-05 10:29:01,240 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:01,240 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 268, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 268, 'cache_hits': 0, 'similarity_calculations': 1306, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,241 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([21, 37, 31, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6],
      dtype=int64), 'cur_cost': 55543.0, 'intermediate_solutions': [{'tour': array([32, 13, 14, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 58478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 32, 13, 14, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 19, 32, 13, 14,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 19, 32, 13, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 43, 19, 32, 13,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 61412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,241 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 55543.00)
2025-08-05 10:29:01,241 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:01,241 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:01,241 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,243 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,243 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,243 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,243 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,243 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,244 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7072.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,244 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7072.0, 'intermediate_solutions': [{'tour': [6, 0, 33, 28, 9, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 25, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 34697.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 10, 11, 3, 4, 5, 2, 30, 12, 19], 'cur_cost': 29844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 33, 28, 25, 20, 27, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 31304.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,244 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 7072.00)
2025-08-05 10:29:01,244 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:01,244 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:01,244 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,247 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:01,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,248 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,248 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,248 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28383.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,248 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 28383.0, 'intermediate_solutions': [{'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 36, 14, 27, 30, 19, 38, 42, 15, 35, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 34246.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 7, 44], 'cur_cost': 37918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 36, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,248 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 28383.00)
2025-08-05 10:29:01,248 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:01,248 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:01,249 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,250 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:01,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,250 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,251 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49665.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,251 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49665.0, 'intermediate_solutions': [{'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 37, 27, 29, 42, 5, 46, 11, 24, 16, 8, 19, 35, 14, 13], 'cur_cost': 43016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 35, 19, 37, 14, 13], 'cur_cost': 44827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 2, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 47200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,251 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 49665.00)
2025-08-05 10:29:01,251 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:01,251 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,251 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,251 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 56489.0
2025-08-05 10:29:01,264 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:01,264 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0]
2025-08-05 10:29:01,265 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,268 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,268 - ExploitationExpert - INFO - populations: [{'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 35425.0}, {'tour': array([21, 37, 31, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6],
      dtype=int64), 'cur_cost': 55543.0}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7072.0}, {'tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 28383.0}, {'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49665.0}, {'tour': array([ 6,  5, 44,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27],
      dtype=int64), 'cur_cost': 56489.0}, {'tour': [33, 10, 17, 26, 25, 24, 19, 32, 8, 23, 21, 46, 4, 13, 47, 18, 34, 11, 44, 0, 35, 45, 38, 28, 43, 22, 16, 6, 2, 29, 42, 3, 9, 5, 14, 15, 40, 30, 39, 1, 27, 37, 31, 41, 20, 7, 12, 36], 'cur_cost': 58342.0}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10945.0}, {'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10776.0}]
2025-08-05 10:29:01,269 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:01,269 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 269, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 269, 'cache_hits': 0, 'similarity_calculations': 1314, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,270 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 6,  5, 44,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27],
      dtype=int64), 'cur_cost': 56489.0, 'intermediate_solutions': [{'tour': array([ 6, 16, 38, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 53003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40,  6, 16, 38,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 53047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 40,  6, 16, 38, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 54897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 40,  6, 16,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 51145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38,  9, 40,  6, 16, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 51616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,270 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 56489.00)
2025-08-05 10:29:01,270 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:01,270 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,271 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,271 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 57621.0
2025-08-05 10:29:01,286 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:01,287 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0]
2025-08-05 10:29:01,287 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,289 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,290 - ExploitationExpert - INFO - populations: [{'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 35425.0}, {'tour': array([21, 37, 31, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6],
      dtype=int64), 'cur_cost': 55543.0}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7072.0}, {'tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 28383.0}, {'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49665.0}, {'tour': array([ 6,  5, 44,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27],
      dtype=int64), 'cur_cost': 56489.0}, {'tour': array([46,  3,  6, 36, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45],
      dtype=int64), 'cur_cost': 57621.0}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10945.0}, {'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10776.0}]
2025-08-05 10:29:01,291 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:01,291 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 270, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 270, 'cache_hits': 0, 'similarity_calculations': 1323, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,292 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([46,  3,  6, 36, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45],
      dtype=int64), 'cur_cost': 57621.0, 'intermediate_solutions': [{'tour': array([17, 10, 33, 26, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26, 17, 10, 33, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58447.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 26, 17, 10, 33, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58419.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 26, 17, 10, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 25, 26, 17, 10, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,292 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 57621.00)
2025-08-05 10:29:01,292 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:01,292 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:01,293 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,294 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7115.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,295 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 7115.0, 'intermediate_solutions': [{'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 40, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 31, 3, 18], 'cur_cost': 41503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 34, 8, 11, 16, 28, 24, 14, 35, 47, 9, 1, 2, 4, 44, 37, 39, 5, 42, 43, 31, 17, 33, 30, 45, 25, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 40, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 3, 18], 'cur_cost': 41804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,295 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 7115.00)
2025-08-05 10:29:01,296 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:01,296 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:01,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,299 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:01,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,300 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,300 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32922.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,300 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 32922.0, 'intermediate_solutions': [{'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 36, 38, 40, 45, 46, 47, 43, 41, 44, 42, 39], 'cur_cost': 11081.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 47, 46, 45, 40, 38, 39, 37, 23, 19, 18, 21, 14, 15, 22, 43, 41, 44, 42, 36], 'cur_cost': 14359.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 30, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 13096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,300 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 32922.00)
2025-08-05 10:29:01,300 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:01,300 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:01,301 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7567.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,303 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7567.0, 'intermediate_solutions': [{'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 30, 9, 4, 31, 24, 29, 2, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 15998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 37, 13, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 13943.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 11032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,303 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 7567.00)
2025-08-05 10:29:01,303 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:01,303 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:01,305 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 35425.0, 'intermediate_solutions': [{'tour': [0, 20, 24, 10, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 13, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 14933.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 24, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 5, 8, 10, 1, 7, 44, 37, 39, 41, 43, 47, 46, 45, 38, 40, 42, 36, 31, 28, 33, 6, 9, 2, 11, 3, 4], 'cur_cost': 11455.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 24, 11, 13, 21, 12, 18, 19, 14, 16, 17, 22, 15, 23, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 3, 4], 'cur_cost': 11149.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 37, 31, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6],
      dtype=int64), 'cur_cost': 55543.0, 'intermediate_solutions': [{'tour': array([32, 13, 14, 19, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 58478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 32, 13, 14, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59910.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([43, 19, 32, 13, 14,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59905.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 19, 32, 13, 43,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 59828.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 43, 19, 32, 13,  5, 27,  2, 23, 38, 12, 41, 29, 28,  3, 22,  7,
        0, 37, 16,  1, 20, 26, 42,  4, 36, 47, 10, 46, 11, 31,  9, 34, 24,
       17, 35, 33, 40, 44, 15,  8, 25, 30, 18, 21, 45, 39,  6]), 'cur_cost': 61412.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7072.0, 'intermediate_solutions': [{'tour': [6, 0, 33, 28, 9, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 25, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 34697.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 0, 33, 28, 25, 20, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 27, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 10, 11, 3, 4, 5, 2, 30, 12, 19], 'cur_cost': 29844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 33, 28, 25, 20, 27, 22, 14, 32, 16, 18, 29, 35, 45, 38, 41, 34, 26, 36, 43, 23, 37, 17, 42, 44, 31, 21, 13, 40, 15, 47, 46, 39, 24, 7, 9, 8, 1, 2, 5, 4, 3, 11, 10, 30, 12, 19], 'cur_cost': 31304.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 28383.0, 'intermediate_solutions': [{'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 36, 14, 27, 30, 19, 38, 42, 15, 35, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 34246.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 36, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 7, 44], 'cur_cost': 37918.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 33, 12, 22, 29, 28, 37, 25, 34, 20, 36, 26, 17, 21, 32, 18, 40, 41, 31, 39, 13, 45, 35, 14, 27, 30, 19, 38, 42, 15, 1, 0, 9, 2, 5, 3, 6, 11, 4, 10, 8, 24, 16, 46, 43, 47, 44, 7], 'cur_cost': 35999.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49665.0, 'intermediate_solutions': [{'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 37, 27, 29, 42, 5, 46, 11, 24, 16, 8, 19, 35, 14, 13], 'cur_cost': 43016.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 2, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 35, 19, 37, 14, 13], 'cur_cost': 44827.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [33, 21, 18, 25, 32, 20, 17, 22, 23, 26, 30, 41, 34, 28, 2, 45, 47, 31, 1, 38, 40, 15, 0, 43, 39, 44, 7, 4, 12, 6, 3, 9, 10, 36, 8, 27, 29, 42, 5, 46, 11, 24, 16, 37, 19, 35, 14, 13], 'cur_cost': 47200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  5, 44,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27],
      dtype=int64), 'cur_cost': 56489.0, 'intermediate_solutions': [{'tour': array([ 6, 16, 38, 40,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 53003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([40,  6, 16, 38,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 53047.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 40,  6, 16, 38, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 54897.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([38, 40,  6, 16,  9, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 51145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([38,  9, 40,  6, 16, 20, 17,  3, 29, 18, 47,  2, 27, 33, 35, 21, 39,
        5, 26, 41, 36, 12, 14, 42, 45, 32, 24,  1, 25,  4, 28, 30, 44, 43,
       13,  7,  0, 34, 31, 10, 11, 46, 22, 15,  8, 19, 23, 37]), 'cur_cost': 51616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([46,  3,  6, 36, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45],
      dtype=int64), 'cur_cost': 57621.0, 'intermediate_solutions': [{'tour': array([17, 10, 33, 26, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26, 17, 10, 33, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58447.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([25, 26, 17, 10, 33, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58419.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 26, 17, 10, 25, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 25, 26, 17, 10, 24, 19, 32,  8, 23, 21, 46,  4, 13, 47, 18, 34,
       11, 44,  0, 35, 45, 38, 28, 43, 22, 16,  6,  2, 29, 42,  3,  9,  5,
       14, 15, 40, 30, 39,  1, 27, 37, 31, 41, 20,  7, 12, 36]), 'cur_cost': 58328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 7115.0, 'intermediate_solutions': [{'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 25, 45, 30, 33, 17, 40, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 31, 3, 18], 'cur_cost': 41503.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 41, 34, 8, 11, 16, 28, 24, 14, 35, 47, 9, 1, 2, 4, 44, 37, 39, 5, 42, 43, 31, 17, 33, 30, 45, 25, 46, 32, 27, 10, 38, 36, 7, 0, 29, 40, 3, 18], 'cur_cost': 43668.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 20, 13, 21, 12, 19, 26, 22, 15, 23, 40, 41, 25, 45, 30, 33, 17, 31, 43, 42, 5, 39, 37, 44, 4, 2, 1, 9, 47, 35, 14, 24, 28, 16, 11, 8, 34, 46, 32, 27, 10, 38, 36, 7, 0, 29, 3, 18], 'cur_cost': 41804.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 32922.0, 'intermediate_solutions': [{'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 36, 38, 40, 45, 46, 47, 43, 41, 44, 42, 39], 'cur_cost': 11081.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 47, 46, 45, 40, 38, 39, 37, 23, 19, 18, 21, 14, 15, 22, 43, 41, 44, 42, 36], 'cur_cost': 14359.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 12, 2, 9, 8, 10, 6, 5, 4, 7, 1, 3, 11, 31, 24, 29, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 18, 19, 23, 37, 39, 38, 40, 45, 30, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 13096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7567.0, 'intermediate_solutions': [{'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 30, 9, 4, 31, 24, 29, 2, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 15998.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 37, 13, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 13943.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 0, 17, 10, 8, 5, 6, 7, 1, 3, 11, 2, 9, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 33, 28, 20, 16, 14, 21, 12, 18, 19, 23, 15, 22, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 11032.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:01,305 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:01,305 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:01,308 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7072.000, 多样性=0.971
2025-08-05 10:29:01,308 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:01,308 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:01,308 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:01,308 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.09680525143500417, 'best_improvement': 0.2007233273056058}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.003829583532790757}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.007768163757215614, 'recent_improvements': [-0.11528243732051202, 0.12745628637036432, -0.0997461098060808], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 6769, 'new_best_cost': 6769, 'quality_improvement': 0.0, 'old_diversity': 0.7093253968253969, 'new_diversity': 0.7093253968253969, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:01,309 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:01,309 - __main__ - INFO - composite9_48 开始进化第 5 代
2025-08-05 10:29:01,309 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:01,309 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:01,310 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7072.000, 多样性=0.971
2025-08-05 10:29:01,311 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:01,312 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.971
2025-08-05 10:29:01,313 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:01,314 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.709
2025-08-05 10:29:01,315 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:01,315 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:01,315 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:29:01,316 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:29:01,358 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -9235.459, 聚类评分: 0.000, 覆盖率: 0.123, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:01,358 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:01,358 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:01,358 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite9_48
2025-08-05 10:29:01,375 - visualization.landscape_visualizer - INFO - 插值约束: 171 个点被约束到最小值 6769.00
2025-08-05 10:29:01,378 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 2345.65 → 2175.86
2025-08-05 10:29:01,518 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\landscape_composite9_48_iter_105_20250805_102901.html
2025-08-05 10:29:01,612 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite9_48\dashboard_composite9_48_iter_105_20250805_102901.html
2025-08-05 10:29:01,612 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 105
2025-08-05 10:29:01,612 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:01,612 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2970秒
2025-08-05 10:29:01,613 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9235.45882352941, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 316059299.86712795, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1232, 'fitness_entropy': 0.7476722347614896, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9235.459)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.123)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360941.3581905, 'performance_metrics': {}}}
2025-08-05 10:29:01,613 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:01,613 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:01,613 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:01,613 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:01,613 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,613 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:01,614 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:29:01,614 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:01,614 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:01,615 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:01,615 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,616 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 48
2025-08-05 10:29:01,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 44930.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,617 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 11, 31, 15, 36, 18, 33, 23, 20, 19, 16, 12, 25, 37, 29, 24, 38, 42, 13, 45, 47, 30, 8, 40, 44, 14, 46, 7, 3, 41, 43, 39, 1, 10, 27, 9, 32, 35, 6, 0, 2, 5, 28, 17, 4, 22, 34, 26], 'cur_cost': 44930.0, 'intermediate_solutions': [{'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 45, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 38, 17, 0, 2, 5, 18], 'cur_cost': 35400.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 3, 10, 1, 7, 43, 41, 42, 15, 16, 14, 44, 40, 23, 30, 13, 39, 24, 27, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 37760.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 23, 0, 2, 5, 18], 'cur_cost': 33950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,617 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 44930.00)
2025-08-05 10:29:01,617 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:29:01,617 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,617 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,618 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 63854.0
2025-08-05 10:29:01,636 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:29:01,636 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0]
2025-08-05 10:29:01,637 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,641 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,641 - ExploitationExpert - INFO - populations: [{'tour': [21, 11, 31, 15, 36, 18, 33, 23, 20, 19, 16, 12, 25, 37, 29, 24, 38, 42, 13, 45, 47, 30, 8, 40, 44, 14, 46, 7, 3, 41, 43, 39, 1, 10, 27, 9, 32, 35, 6, 0, 2, 5, 28, 17, 4, 22, 34, 26], 'cur_cost': 44930.0}, {'tour': array([27, 16, 43,  9, 37, 18,  1, 34, 25, 41,  2, 22, 44, 39,  5, 20, 46,
       42, 29,  3, 12, 13, 26, 23, 40, 14,  7,  4, 15, 30, 45,  6, 33, 47,
        8, 21, 24, 31,  0, 17, 38, 35, 11, 36, 32, 10, 28, 19],
      dtype=int64), 'cur_cost': 63854.0}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7072.0}, {'tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 28383.0}, {'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49665.0}, {'tour': [6, 5, 44, 7, 13, 15, 17, 4, 19, 0, 37, 40, 46, 26, 2, 22, 47, 8, 1, 14, 36, 31, 3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34, 20, 16, 9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27], 'cur_cost': 56489.0}, {'tour': [46, 3, 6, 36, 30, 5, 22, 9, 1, 38, 14, 23, 20, 32, 40, 0, 25, 2, 17, 27, 42, 8, 11, 44, 21, 47, 18, 41, 34, 7, 13, 10, 39, 33, 43, 12, 26, 31, 29, 24, 19, 16, 4, 15, 37, 35, 28, 45], 'cur_cost': 57621.0}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 7115.0}, {'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 32922.0}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7567.0}]
2025-08-05 10:29:01,642 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:01,642 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 271, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 271, 'cache_hits': 0, 'similarity_calculations': 1333, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,643 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([27, 16, 43,  9, 37, 18,  1, 34, 25, 41,  2, 22, 44, 39,  5, 20, 46,
       42, 29,  3, 12, 13, 26, 23, 40, 14,  7,  4, 15, 30, 45,  6, 33, 47,
        8, 21, 24, 31,  0, 17, 38, 35, 11, 36, 32, 10, 28, 19],
      dtype=int64), 'cur_cost': 63854.0, 'intermediate_solutions': [{'tour': array([31, 37, 21, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 54067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 31, 37, 21, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 55546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 15, 31, 37, 21, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 51895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 15, 31, 37, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 54255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 11, 15, 31, 37, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 55586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,644 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 63854.00)
2025-08-05 10:29:01,644 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:01,644 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:01,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,647 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10712.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,648 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 23, 1, 7, 6, 10, 8, 5, 4, 9, 2, 3, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10712.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 11, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 41, 3], 'cur_cost': 15536.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 43, 47, 46, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 31, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 9529.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,649 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 10712.00)
2025-08-05 10:29:01,649 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:01,649 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:01,649 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,652 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7195.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,654 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 19, 21, 12, 18, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3], 'cur_cost': 7195.0, 'intermediate_solutions': [{'tour': [5, 40, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 11, 38, 36, 37, 47, 4], 'cur_cost': 30231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 7, 0, 10, 1, 39, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 30729.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 11, 29, 20, 6, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 32006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,655 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 7195.00)
2025-08-05 10:29:01,655 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:01,655 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:01,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,659 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,661 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8874.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,661 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 14, 24, 17, 16, 20, 22, 15, 23, 19, 18, 12, 21, 13, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8874.0, 'intermediate_solutions': [{'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 11, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 9, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 23, 31, 20, 34, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 51077.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 27, 14, 18, 19, 1, 47, 40, 3], 'cur_cost': 47825.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,661 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 8874.00)
2025-08-05 10:29:01,661 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:01,661 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,661 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,662 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 64057.0
2025-08-05 10:29:01,682 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:01,682 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0, 6767]
2025-08-05 10:29:01,682 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,685 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,685 - ExploitationExpert - INFO - populations: [{'tour': [21, 11, 31, 15, 36, 18, 33, 23, 20, 19, 16, 12, 25, 37, 29, 24, 38, 42, 13, 45, 47, 30, 8, 40, 44, 14, 46, 7, 3, 41, 43, 39, 1, 10, 27, 9, 32, 35, 6, 0, 2, 5, 28, 17, 4, 22, 34, 26], 'cur_cost': 44930.0}, {'tour': array([27, 16, 43,  9, 37, 18,  1, 34, 25, 41,  2, 22, 44, 39,  5, 20, 46,
       42, 29,  3, 12, 13, 26, 23, 40, 14,  7,  4, 15, 30, 45,  6, 33, 47,
        8, 21, 24, 31,  0, 17, 38, 35, 11, 36, 32, 10, 28, 19],
      dtype=int64), 'cur_cost': 63854.0}, {'tour': [0, 11, 23, 1, 7, 6, 10, 8, 5, 4, 9, 2, 3, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10712.0}, {'tour': [0, 4, 19, 21, 12, 18, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3], 'cur_cost': 7195.0}, {'tour': [0, 14, 24, 17, 16, 20, 22, 15, 23, 19, 18, 12, 21, 13, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8874.0}, {'tour': array([30,  7, 44, 29, 43,  2, 14, 23,  5, 13, 33, 40,  3, 18, 47, 12, 27,
       39, 16,  8, 26, 15, 34,  1, 21, 35, 31, 45, 42, 22, 36, 19, 11, 32,
       38, 20,  9, 46, 37,  0, 24,  4, 25, 41, 10,  6, 17, 28],
      dtype=int64), 'cur_cost': 64057.0}, {'tour': [46, 3, 6, 36, 30, 5, 22, 9, 1, 38, 14, 23, 20, 32, 40, 0, 25, 2, 17, 27, 42, 8, 11, 44, 21, 47, 18, 41, 34, 7, 13, 10, 39, 33, 43, 12, 26, 31, 29, 24, 19, 16, 4, 15, 37, 35, 28, 45], 'cur_cost': 57621.0}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 7115.0}, {'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 32922.0}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7567.0}]
2025-08-05 10:29:01,686 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:01,686 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 272, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 272, 'cache_hits': 0, 'similarity_calculations': 1344, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,687 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([30,  7, 44, 29, 43,  2, 14, 23,  5, 13, 33, 40,  3, 18, 47, 12, 27,
       39, 16,  8, 26, 15, 34,  1, 21, 35, 31, 45, 42, 22, 36, 19, 11, 32,
       38, 20,  9, 46, 37,  0, 24,  4, 25, 41, 10,  6, 17, 28],
      dtype=int64), 'cur_cost': 64057.0, 'intermediate_solutions': [{'tour': array([44,  5,  6,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 54195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 44,  5,  6, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 56495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13,  7, 44,  5,  6, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 57816.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7, 44,  5, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 56534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13,  7, 44,  5, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 60182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,687 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 64057.00)
2025-08-05 10:29:01,687 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:29:01,687 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:01,688 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:01,688 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 51743.0
2025-08-05 10:29:01,703 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:29:01,704 - ExploitationExpert - INFO - res_population_costs: [6769, 6770.0, 6770, 6770, 6770, 6782, 6783.0, 6767]
2025-08-05 10:29:01,704 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 11,  2,  9,  8,  4,  5,  6, 10,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 17, 15, 22, 20,  3],
      dtype=int64), array([ 0,  3, 22, 15, 17, 20, 16, 14, 23, 19, 18, 12, 21, 13, 37, 39, 45,
       40, 38, 46, 41, 43, 47, 44, 42, 36, 33, 35, 27, 34, 25, 32, 26, 30,
       29, 24, 28, 31,  1,  7, 10,  6,  5,  4,  8,  9,  2, 11],
      dtype=int64), array([ 0, 11,  2,  9,  4,  5,  8, 10,  6,  7,  1, 31, 28, 24, 29, 30, 26,
       32, 25, 34, 27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39,
       37, 13, 21, 12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 24, 29, 30, 26, 32, 25, 34,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 45, 38, 40, 42, 44, 47, 43, 41, 46, 39, 37, 13, 21,
       12, 18, 19, 23, 15, 17, 14, 16, 20, 22,  3, 11,  2,  9],
      dtype=int64), array([ 0, 10,  8,  4,  5,  6,  7,  1, 31, 28, 34, 29, 24, 30, 26, 32, 25,
       27, 35, 33, 36, 42, 44, 47, 43, 41, 46, 38, 40, 45, 39, 37, 13, 21,
       12, 18, 19, 23, 14, 16, 20, 17, 15, 22,  3, 11,  2,  9],
      dtype=int64)]
2025-08-05 10:29:01,708 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:01,708 - ExploitationExpert - INFO - populations: [{'tour': [21, 11, 31, 15, 36, 18, 33, 23, 20, 19, 16, 12, 25, 37, 29, 24, 38, 42, 13, 45, 47, 30, 8, 40, 44, 14, 46, 7, 3, 41, 43, 39, 1, 10, 27, 9, 32, 35, 6, 0, 2, 5, 28, 17, 4, 22, 34, 26], 'cur_cost': 44930.0}, {'tour': array([27, 16, 43,  9, 37, 18,  1, 34, 25, 41,  2, 22, 44, 39,  5, 20, 46,
       42, 29,  3, 12, 13, 26, 23, 40, 14,  7,  4, 15, 30, 45,  6, 33, 47,
        8, 21, 24, 31,  0, 17, 38, 35, 11, 36, 32, 10, 28, 19],
      dtype=int64), 'cur_cost': 63854.0}, {'tour': [0, 11, 23, 1, 7, 6, 10, 8, 5, 4, 9, 2, 3, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10712.0}, {'tour': [0, 4, 19, 21, 12, 18, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3], 'cur_cost': 7195.0}, {'tour': [0, 14, 24, 17, 16, 20, 22, 15, 23, 19, 18, 12, 21, 13, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8874.0}, {'tour': array([30,  7, 44, 29, 43,  2, 14, 23,  5, 13, 33, 40,  3, 18, 47, 12, 27,
       39, 16,  8, 26, 15, 34,  1, 21, 35, 31, 45, 42, 22, 36, 19, 11, 32,
       38, 20,  9, 46, 37,  0, 24,  4, 25, 41, 10,  6, 17, 28],
      dtype=int64), 'cur_cost': 64057.0}, {'tour': array([ 1, 25, 21, 31, 39,  8, 14,  3, 35, 40, 46, 38, 12, 18, 19, 30, 10,
        5, 22, 43,  7,  2, 27,  6,  0, 36, 45, 20, 29, 17, 32, 47, 37, 23,
       41, 33, 13, 26, 28, 24, 42, 34,  9,  4, 16, 11, 15, 44],
      dtype=int64), 'cur_cost': 51743.0}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 7115.0}, {'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 32922.0}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7567.0}]
2025-08-05 10:29:01,709 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:01,710 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 273, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 273, 'cache_hits': 0, 'similarity_calculations': 1356, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:01,710 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 1, 25, 21, 31, 39,  8, 14,  3, 35, 40, 46, 38, 12, 18, 19, 30, 10,
        5, 22, 43,  7,  2, 27,  6,  0, 36, 45, 20, 29, 17, 32, 47, 37, 23,
       41, 33, 13, 26, 28, 24, 42, 34,  9,  4, 16, 11, 15, 44],
      dtype=int64), 'cur_cost': 51743.0, 'intermediate_solutions': [{'tour': array([ 6,  3, 46, 36, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 57686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  6,  3, 46, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 57684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 36,  6,  3, 46,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 59449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 36,  6,  3, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 55814.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 30, 36,  6,  3,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 55395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:01,711 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 51743.00)
2025-08-05 10:29:01,711 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:01,711 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:01,711 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,712 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,713 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,713 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7056.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,713 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 16, 23, 19, 18, 12, 21, 13, 14, 17, 22, 20, 15, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 4, 2, 11, 3], 'cur_cost': 7056.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 37, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 13, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 12163.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 2, 9, 4, 5, 6, 10, 1, 3], 'cur_cost': 7135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 6, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 5, 4, 9, 2, 3], 'cur_cost': 11214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,714 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 7056.00)
2025-08-05 10:29:01,714 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:01,714 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:01,714 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,715 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 48
2025-08-05 10:29:01,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,715 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7170.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,716 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 5, 2, 9, 10, 6, 7, 1, 3, 11, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 7170.0, 'intermediate_solutions': [{'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 1, 14, 45, 43, 47, 6, 5, 35, 8, 2, 46], 'cur_cost': 37478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 2, 8, 1, 5, 46], 'cur_cost': 32927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 15, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 33098.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,716 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 7170.00)
2025-08-05 10:29:01,716 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:01,717 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:01,717 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:01,720 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 48
2025-08-05 10:29:01,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,721 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,721 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:01,721 - ExplorationExpert - INFO - 探索路径生成完成，成本: 38104.0, 路径长度: 48, 收集中间解: 3
2025-08-05 10:29:01,721 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [12, 24, 21, 16, 17, 32, 23, 26, 37, 34, 13, 19, 38, 36, 30, 18, 28, 40, 45, 35, 44, 39, 41, 31, 20, 47, 15, 46, 1, 8, 7, 6, 2, 3, 9, 29, 14, 22, 27, 43, 5, 0, 10, 11, 33, 25, 42, 4], 'cur_cost': 38104.0, 'intermediate_solutions': [{'tour': [0, 24, 21, 22, 17, 16, 20, 18, 19, 14, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7637.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 10, 1, 7, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7614.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 2, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 11, 3, 4], 'cur_cost': 10244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:01,721 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 38104.00)
2025-08-05 10:29:01,721 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:01,722 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:01,724 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 11, 31, 15, 36, 18, 33, 23, 20, 19, 16, 12, 25, 37, 29, 24, 38, 42, 13, 45, 47, 30, 8, 40, 44, 14, 46, 7, 3, 41, 43, 39, 1, 10, 27, 9, 32, 35, 6, 0, 2, 5, 28, 17, 4, 22, 34, 26], 'cur_cost': 44930.0, 'intermediate_solutions': [{'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 45, 27, 24, 39, 13, 30, 23, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 38, 17, 0, 2, 5, 18], 'cur_cost': 35400.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 3, 10, 1, 7, 43, 41, 42, 15, 16, 14, 44, 40, 23, 30, 13, 39, 24, 27, 9, 11, 8, 6, 32, 46, 47, 45, 17, 0, 2, 5, 18], 'cur_cost': 37760.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 26, 34, 28, 36, 31, 19, 21, 20, 35, 33, 12, 25, 37, 29, 22, 38, 27, 24, 39, 13, 30, 40, 44, 14, 16, 15, 42, 41, 43, 7, 1, 10, 3, 9, 11, 8, 6, 32, 46, 47, 45, 17, 23, 0, 2, 5, 18], 'cur_cost': 33950.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 16, 43,  9, 37, 18,  1, 34, 25, 41,  2, 22, 44, 39,  5, 20, 46,
       42, 29,  3, 12, 13, 26, 23, 40, 14,  7,  4, 15, 30, 45,  6, 33, 47,
        8, 21, 24, 31,  0, 17, 38, 35, 11, 36, 32, 10, 28, 19],
      dtype=int64), 'cur_cost': 63854.0, 'intermediate_solutions': [{'tour': array([31, 37, 21, 15, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 54067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 31, 37, 21, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 55546.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 15, 31, 37, 21, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 51895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 15, 31, 37, 11, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 54255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 11, 15, 31, 37, 16, 33, 24,  1, 19, 22,  0, 30, 44,  4, 28, 25,
       29, 13, 45, 47, 23,  8,  5, 14, 40, 36,  7,  3, 38, 26, 39, 10, 17,
       27, 41, 32, 35, 42, 46, 12, 43, 18, 20,  2,  9, 34,  6]), 'cur_cost': 55586.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 23, 1, 7, 6, 10, 8, 5, 4, 9, 2, 3, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 10712.0, 'intermediate_solutions': [{'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 11, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 41, 3], 'cur_cost': 15536.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 43, 47, 46, 41, 39, 37, 44, 7, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 7134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 14, 21, 12, 18, 19, 23, 15, 17, 16, 20, 22, 13, 26, 30, 29, 24, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 31, 1, 10, 6, 5, 4, 9, 2, 11, 3], 'cur_cost': 9529.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 19, 21, 12, 18, 14, 16, 17, 22, 20, 15, 23, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3], 'cur_cost': 7195.0, 'intermediate_solutions': [{'tour': [5, 40, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 6, 2, 3, 11, 38, 36, 37, 47, 4], 'cur_cost': 30231.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 11, 29, 20, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 7, 0, 10, 1, 39, 8, 9, 6, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 30729.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 11, 29, 20, 6, 14, 19, 17, 28, 15, 16, 22, 32, 21, 31, 26, 24, 18, 42, 43, 35, 23, 12, 45, 27, 33, 25, 46, 41, 34, 44, 30, 13, 39, 1, 10, 0, 7, 8, 9, 2, 3, 40, 38, 36, 37, 47, 4], 'cur_cost': 32006.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 24, 17, 16, 20, 22, 15, 23, 19, 18, 12, 21, 13, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 8874.0, 'intermediate_solutions': [{'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 11, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 9, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 49683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 23, 31, 20, 34, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 14, 18, 27, 19, 1, 47, 40, 3], 'cur_cost': 51077.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 34, 20, 31, 23, 16, 12, 45, 22, 38, 28, 25, 32, 46, 15, 41, 43, 7, 39, 10, 9, 0, 6, 42, 2, 24, 17, 36, 21, 26, 8, 11, 33, 30, 5, 44, 37, 13, 29, 35, 27, 14, 18, 19, 1, 47, 40, 3], 'cur_cost': 47825.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([30,  7, 44, 29, 43,  2, 14, 23,  5, 13, 33, 40,  3, 18, 47, 12, 27,
       39, 16,  8, 26, 15, 34,  1, 21, 35, 31, 45, 42, 22, 36, 19, 11, 32,
       38, 20,  9, 46, 37,  0, 24,  4, 25, 41, 10,  6, 17, 28],
      dtype=int64), 'cur_cost': 64057.0, 'intermediate_solutions': [{'tour': array([44,  5,  6,  7, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 54195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 44,  5,  6, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 56495.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13,  7, 44,  5,  6, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 57816.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  7, 44,  5, 13, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 56534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 13,  7, 44,  5, 15, 17,  4, 19,  0, 37, 40, 46, 26,  2, 22, 47,
        8,  1, 14, 36, 31,  3, 41, 45, 21, 38, 35, 33, 32, 25, 10, 29, 34,
       20, 16,  9, 39, 24, 18, 43, 12, 28, 11, 30, 23, 42, 27]), 'cur_cost': 60182.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 25, 21, 31, 39,  8, 14,  3, 35, 40, 46, 38, 12, 18, 19, 30, 10,
        5, 22, 43,  7,  2, 27,  6,  0, 36, 45, 20, 29, 17, 32, 47, 37, 23,
       41, 33, 13, 26, 28, 24, 42, 34,  9,  4, 16, 11, 15, 44],
      dtype=int64), 'cur_cost': 51743.0, 'intermediate_solutions': [{'tour': array([ 6,  3, 46, 36, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 57686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  6,  3, 46, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 57684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 36,  6,  3, 46,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 59449.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([46, 36,  6,  3, 30,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 55814.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([46, 30, 36,  6,  3,  5, 22,  9,  1, 38, 14, 23, 20, 32, 40,  0, 25,
        2, 17, 27, 42,  8, 11, 44, 21, 47, 18, 41, 34,  7, 13, 10, 39, 33,
       43, 12, 26, 31, 29, 24, 19, 16,  4, 15, 37, 35, 28, 45]), 'cur_cost': 55395.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 16, 23, 19, 18, 12, 21, 13, 14, 17, 22, 20, 15, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 4, 2, 11, 3], 'cur_cost': 7056.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 37, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 13, 44, 7, 1, 10, 6, 5, 4, 9, 2, 3], 'cur_cost': 12163.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 2, 9, 4, 5, 6, 10, 1, 3], 'cur_cost': 7135.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 12, 18, 19, 14, 16, 17, 22, 20, 15, 23, 21, 13, 26, 30, 29, 24, 31, 28, 34, 25, 32, 27, 35, 33, 36, 42, 40, 38, 45, 6, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 5, 4, 9, 2, 3], 'cur_cost': 11214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 5, 2, 9, 10, 6, 7, 1, 3, 11, 4, 31, 24, 29, 30, 26, 32, 25, 34, 27, 35, 33, 28, 20, 16, 17, 22, 15, 14, 21, 12, 18, 19, 23, 13, 37, 39, 38, 40, 45, 46, 47, 43, 41, 44, 42, 36], 'cur_cost': 7170.0, 'intermediate_solutions': [{'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 1, 14, 45, 43, 47, 6, 5, 35, 8, 2, 46], 'cur_cost': 37478.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 29, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 2, 8, 1, 5, 46], 'cur_cost': 32927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 15, 32, 23, 31, 33, 19, 16, 26, 42, 24, 27, 12, 25, 22, 34, 21, 17, 39, 37, 30, 36, 40, 44, 38, 20, 3, 0, 9, 11, 4, 10, 7, 28, 13, 18, 41, 35, 14, 45, 43, 47, 6, 5, 1, 8, 2, 46], 'cur_cost': 33098.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [12, 24, 21, 16, 17, 32, 23, 26, 37, 34, 13, 19, 38, 36, 30, 18, 28, 40, 45, 35, 44, 39, 41, 31, 20, 47, 15, 46, 1, 8, 7, 6, 2, 3, 9, 29, 14, 22, 27, 43, 5, 0, 10, 11, 33, 25, 42, 4], 'cur_cost': 38104.0, 'intermediate_solutions': [{'tour': [0, 24, 21, 22, 17, 16, 20, 18, 19, 14, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7637.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 10, 1, 7, 8, 5, 6, 9, 2, 11, 3, 4], 'cur_cost': 7614.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 24, 21, 22, 17, 16, 20, 14, 19, 18, 12, 13, 23, 15, 26, 30, 29, 34, 25, 32, 27, 35, 2, 33, 28, 31, 36, 42, 40, 38, 45, 46, 47, 43, 41, 39, 37, 44, 7, 1, 10, 8, 5, 6, 9, 11, 3, 4], 'cur_cost': 10244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:01,724 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:01,724 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:01,727 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=7056.000, 多样性=0.945
2025-08-05 10:29:01,727 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:01,727 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:01,727 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:01,728 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.002821838256659138, 'best_improvement': 0.0022624434389140274}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.026227944682880137}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.015325517467680072, 'recent_improvements': [0.12745628637036432, -0.0997461098060808, 0.09680525143500417], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 6767, 'new_best_cost': 6767, 'quality_improvement': 0.0, 'old_diversity': 0.6659226190476191, 'new_diversity': 0.6659226190476191, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:01,728 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:01,730 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite9_48_solution.json
2025-08-05 10:29:01,730 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite9_48_20250805_102901.solution
2025-08-05 10:29:01,731 - __main__ - INFO - 实例执行完成 - 运行时间: 1.82s, 最佳成本: 6767
2025-08-05 10:29:01,731 - __main__ - INFO - 实例 composite9_48 处理完成
