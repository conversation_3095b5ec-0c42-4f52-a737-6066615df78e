"""
Fitness landscape analysis for the intelligent strategy selection system.

This module implements comprehensive fitness landscape analysis capabilities
to support intelligent strategy selection decisions.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Callable, Any
import numpy as np
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import logging

from .individual_state import IndividualState


@dataclass
class LandscapeFeatures:
    """
    Comprehensive fitness landscape features.
    
    This class encapsulates all landscape characteristics used for
    strategy selection decisions.
    """
    
    # Global landscape features
    global_ruggedness: float = 0.0
    modality: float = 0.0              # Number of local optima indicator
    deceptiveness: float = 0.0         # Misleading gradient indicator
    gradient_strength: float = 0.0     # Overall gradient magnitude
    
    # Local landscape features (per individual)
    local_ruggedness: Dict[int, float] = field(default_factory=dict)
    local_gradients: Dict[int, np.ndarray] = field(default_factory=dict)
    improvement_potentials: Dict[int, float] = field(default_factory=dict)
    
    # Population-level features
    diversity_level: float = 0.0
    convergence_level: float = 0.0
    exploration_coverage: float = 0.0
    
    # Temporal features
    landscape_stability: float = 0.0
    trend_direction: float = 0.0
    
    def get_individual_features(self, individual_id: int) -> Dict[str, float]:
        """Get landscape features for a specific individual."""
        return {
            'global_ruggedness': self.global_ruggedness,
            'modality': self.modality,
            'deceptiveness': self.deceptiveness,
            'gradient_strength': self.gradient_strength,
            'local_ruggedness': self.local_ruggedness.get(individual_id, 0.0),
            'improvement_potential': self.improvement_potentials.get(individual_id, 0.0),
            'diversity_level': self.diversity_level,
            'convergence_level': self.convergence_level,
            'exploration_coverage': self.exploration_coverage,
            'landscape_stability': self.landscape_stability,
            'trend_direction': self.trend_direction
        }


class LandscapeAnalyzer:
    """
    Comprehensive fitness landscape analyzer.
    
    This class provides methods to analyze various aspects of the fitness
    landscape to support intelligent strategy selection.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the landscape analyzer."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configuration parameters
        self.neighborhood_size = self.config.get('neighborhood_size', 20)
        self.gradient_samples = self.config.get('gradient_estimation_samples', 10)
        self.ruggedness_method = self.config.get('ruggedness_calculation_method', 'autocorrelation')
        self.modality_threshold = self.config.get('modality_detection_threshold', 0.1)
        
        # Historical data for temporal analysis
        self.landscape_history = []
        self.fitness_history = []
    
    def analyze_landscape(self, 
                         individual_states: List[IndividualState],
                         fitness_function: Optional[Callable] = None) -> LandscapeFeatures:
        """
        Perform comprehensive landscape analysis.
        
        Args:
            individual_states: List of current individual states
            fitness_function: Optional fitness evaluation function
            
        Returns:
            LandscapeFeatures object with complete analysis
        """
        features = LandscapeFeatures()
        
        if not individual_states:
            return features
        
        # Extract fitness values and solutions
        fitness_values = [state.fitness_value for state in individual_states]
        solutions = [state.current_solution for state in individual_states if state.current_solution]
        
        if not solutions:
            self.logger.warning("No solutions available for landscape analysis")
            return features
        
        # Global landscape analysis
        features.global_ruggedness = self._calculate_global_ruggedness(fitness_values, solutions)
        features.modality = self._estimate_modality(fitness_values, solutions)
        features.deceptiveness = self._calculate_deceptiveness(fitness_values, solutions)
        features.gradient_strength = self._calculate_gradient_strength(fitness_values)
        
        # Local landscape analysis
        for state in individual_states:
            if state.current_solution:
                features.local_ruggedness[state.individual_id] = self._calculate_local_ruggedness(
                    state.current_solution, state.fitness_value, fitness_function
                )
                features.local_gradients[state.individual_id] = self._estimate_local_gradient(
                    state.current_solution, state.fitness_value, fitness_function
                )
                features.improvement_potentials[state.individual_id] = self._estimate_improvement_potential(
                    state.current_solution, state.fitness_value, fitness_function
                )
        
        # Population-level analysis
        features.diversity_level = self._calculate_diversity_level(solutions)
        features.convergence_level = self._calculate_convergence_level(fitness_values)
        features.exploration_coverage = self._estimate_exploration_coverage(solutions)
        
        # Temporal analysis
        if len(self.landscape_history) > 1:
            features.landscape_stability = self._calculate_landscape_stability()
            features.trend_direction = self._calculate_trend_direction(fitness_values)
        
        # Update history
        self.landscape_history.append(features)
        self.fitness_history.append(fitness_values)
        
        # Keep only recent history
        if len(self.landscape_history) > 50:
            self.landscape_history = self.landscape_history[-50:]
            self.fitness_history = self.fitness_history[-50:]
        
        return features
    
    def _calculate_global_ruggedness(self, fitness_values: List[float], 
                                   solutions: List[List[int]]) -> float:
        """Calculate global landscape ruggedness."""
        if len(fitness_values) < 2:
            return 0.0
        
        if self.ruggedness_method == 'autocorrelation':
            return self._ruggedness_autocorrelation(fitness_values, solutions)
        elif self.ruggedness_method == 'variance':
            return self._ruggedness_variance(fitness_values)
        else:
            return self._ruggedness_autocorrelation(fitness_values, solutions)
    
    def _ruggedness_autocorrelation(self, fitness_values: List[float], 
                                  solutions: List[List[int]]) -> float:
        """Calculate ruggedness using autocorrelation method."""
        try:
            # Calculate pairwise distances between solutions
            if len(solutions) < 2:
                return 0.0
            
            # Simple Hamming distance for TSP solutions
            distances = []
            fitness_diffs = []
            
            for i in range(len(solutions)):
                for j in range(i + 1, len(solutions)):
                    # Calculate Hamming distance
                    dist = sum(a != b for a, b in zip(solutions[i], solutions[j]))
                    distances.append(dist)
                    fitness_diffs.append(abs(fitness_values[i] - fitness_values[j]))
            
            if not distances or not fitness_diffs:
                return 0.0
            
            # Calculate correlation between distance and fitness difference
            if len(set(distances)) == 1 or len(set(fitness_diffs)) == 1:
                return 0.0
            
            correlation, _ = stats.pearsonr(distances, fitness_diffs)
            
            # Ruggedness is inverse of correlation (high correlation = smooth landscape)
            ruggedness = 1.0 - abs(correlation) if not np.isnan(correlation) else 0.5
            return max(0.0, min(1.0, ruggedness))
            
        except Exception as e:
            self.logger.warning(f"Error calculating ruggedness: {e}")
            return 0.5
    
    def _ruggedness_variance(self, fitness_values: List[float]) -> float:
        """Calculate ruggedness using fitness variance."""
        if len(fitness_values) < 2:
            return 0.0
        
        variance = np.var(fitness_values)
        mean_fitness = np.mean(fitness_values)
        
        if mean_fitness == 0:
            return 0.0
        
        # Normalize variance by mean fitness
        normalized_variance = variance / (mean_fitness ** 2)
        return min(1.0, normalized_variance)
    
    def _estimate_modality(self, fitness_values: List[float], 
                          solutions: List[List[int]]) -> float:
        """Estimate the number of local optima (modality)."""
        if len(fitness_values) < 3:
            return 0.0
        
        try:
            # Sort fitness values
            sorted_fitness = sorted(fitness_values)
            
            # Count potential local optima using peak detection
            peaks = 0
            for i in range(1, len(sorted_fitness) - 1):
                # Check if this is a local minimum (for minimization)
                if (sorted_fitness[i] < sorted_fitness[i-1] and 
                    sorted_fitness[i] < sorted_fitness[i+1]):
                    peaks += 1
            
            # Normalize by population size
            modality = peaks / len(fitness_values)
            return min(1.0, modality)
            
        except Exception as e:
            self.logger.warning(f"Error estimating modality: {e}")
            return 0.5
    
    def _calculate_deceptiveness(self, fitness_values: List[float], 
                               solutions: List[List[int]]) -> float:
        """Calculate landscape deceptiveness."""
        # Simplified deceptiveness measure
        # High deceptiveness when fitness doesn't correlate well with solution quality
        if len(fitness_values) < 3:
            return 0.0
        
        try:
            # Calculate coefficient of variation
            cv = np.std(fitness_values) / np.mean(fitness_values) if np.mean(fitness_values) != 0 else 0
            
            # High CV might indicate deceptiveness
            deceptiveness = min(1.0, cv)
            return deceptiveness
            
        except Exception as e:
            self.logger.warning(f"Error calculating deceptiveness: {e}")
            return 0.5
    
    def _calculate_gradient_strength(self, fitness_values: List[float]) -> float:
        """Calculate overall gradient strength."""
        if len(fitness_values) < 2:
            return 0.0
        
        # Calculate range of fitness values
        fitness_range = max(fitness_values) - min(fitness_values)
        mean_fitness = np.mean(fitness_values)
        
        if mean_fitness == 0:
            return 0.0
        
        # Normalize gradient strength
        gradient_strength = fitness_range / abs(mean_fitness)
        return min(1.0, gradient_strength)
    
    def _calculate_local_ruggedness(self, solution: List[int], fitness: float,
                                  fitness_function: Optional[Callable]) -> float:
        """Calculate local ruggedness around a specific solution."""
        if not fitness_function:
            return 0.5  # Default value when no fitness function available
        
        try:
            # Generate small neighborhood
            neighborhood_fitness = []
            for _ in range(min(10, self.neighborhood_size)):
                neighbor = self._generate_neighbor(solution)
                neighbor_fitness = fitness_function(neighbor)
                neighborhood_fitness.append(neighbor_fitness)
            
            if not neighborhood_fitness:
                return 0.5
            
            # Calculate local variance
            local_variance = np.var(neighborhood_fitness + [fitness])
            
            # Normalize by fitness magnitude
            if fitness != 0:
                normalized_variance = local_variance / (fitness ** 2)
            else:
                normalized_variance = local_variance
            
            return min(1.0, normalized_variance)
            
        except Exception as e:
            self.logger.warning(f"Error calculating local ruggedness: {e}")
            return 0.5
    
    def _estimate_local_gradient(self, solution: List[int], fitness: float,
                               fitness_function: Optional[Callable]) -> np.ndarray:
        """Estimate local gradient around a solution."""
        if not fitness_function:
            return np.array([0.0])
        
        try:
            gradients = []
            for _ in range(self.gradient_samples):
                neighbor = self._generate_neighbor(solution)
                neighbor_fitness = fitness_function(neighbor)
                gradient = neighbor_fitness - fitness
                gradients.append(gradient)
            
            return np.array(gradients) if gradients else np.array([0.0])
            
        except Exception as e:
            self.logger.warning(f"Error estimating local gradient: {e}")
            return np.array([0.0])
    
    def _estimate_improvement_potential(self, solution: List[int], fitness: float,
                                      fitness_function: Optional[Callable]) -> float:
        """Estimate improvement potential for a solution."""
        if not fitness_function:
            return 0.5
        
        try:
            # Sample neighborhood and count improvements
            improvements = 0
            total_samples = min(20, self.neighborhood_size)
            
            for _ in range(total_samples):
                neighbor = self._generate_neighbor(solution)
                neighbor_fitness = fitness_function(neighbor)
                if neighbor_fitness < fitness:  # Improvement for minimization
                    improvements += 1
            
            return improvements / total_samples if total_samples > 0 else 0.0
            
        except Exception as e:
            self.logger.warning(f"Error estimating improvement potential: {e}")
            return 0.5
    
    def _generate_neighbor(self, solution: List[int]) -> List[int]:
        """Generate a neighbor solution using simple perturbation."""
        if len(solution) < 2:
            return solution.copy()
        
        neighbor = solution.copy()
        
        # Simple 2-opt move
        i, j = np.random.choice(len(solution), 2, replace=False)
        if i > j:
            i, j = j, i
        
        # Reverse the segment
        neighbor[i:j+1] = neighbor[i:j+1][::-1]
        
        return neighbor
    
    def _calculate_diversity_level(self, solutions: List[List[int]]) -> float:
        """Calculate population diversity level."""
        if len(solutions) < 2:
            return 0.0
        
        try:
            # Calculate average pairwise distance
            total_distance = 0
            count = 0
            
            for i in range(len(solutions)):
                for j in range(i + 1, len(solutions)):
                    # Hamming distance
                    distance = sum(a != b for a, b in zip(solutions[i], solutions[j]))
                    total_distance += distance
                    count += 1
            
            if count == 0:
                return 0.0
            
            average_distance = total_distance / count
            max_possible_distance = len(solutions[0]) if solutions else 1
            
            # Normalize diversity
            diversity = average_distance / max_possible_distance
            return min(1.0, diversity)
            
        except Exception as e:
            self.logger.warning(f"Error calculating diversity: {e}")
            return 0.5
    
    def _calculate_convergence_level(self, fitness_values: List[float]) -> float:
        """Calculate population convergence level."""
        if len(fitness_values) < 2:
            return 1.0
        
        # Calculate coefficient of variation
        cv = np.std(fitness_values) / np.mean(fitness_values) if np.mean(fitness_values) != 0 else 0
        
        # High convergence = low variation
        convergence = 1.0 / (1.0 + cv)
        return min(1.0, convergence)
    
    def _estimate_exploration_coverage(self, solutions: List[List[int]]) -> float:
        """Estimate how well the search space has been explored."""
        # Simplified coverage estimation
        if not solutions:
            return 0.0
        
        # This is a placeholder - in practice, this would require
        # more sophisticated analysis of the search space coverage
        diversity = self._calculate_diversity_level(solutions)
        return diversity  # Use diversity as a proxy for coverage
    
    def _calculate_landscape_stability(self) -> float:
        """Calculate landscape stability over time."""
        if len(self.landscape_history) < 2:
            return 1.0
        
        # Compare recent landscape features
        recent_features = self.landscape_history[-5:]  # Last 5 iterations
        
        if len(recent_features) < 2:
            return 1.0
        
        # Calculate stability based on ruggedness variation
        ruggedness_values = [f.global_ruggedness for f in recent_features]
        stability = 1.0 - np.std(ruggedness_values) if len(ruggedness_values) > 1 else 1.0
        
        return max(0.0, min(1.0, stability))
    
    def _calculate_trend_direction(self, current_fitness: List[float]) -> float:
        """Calculate fitness trend direction."""
        if len(self.fitness_history) < 2:
            return 0.0
        
        # Compare current best fitness with historical best
        current_best = min(current_fitness) if current_fitness else float('inf')
        historical_best = [min(f) for f in self.fitness_history[-5:]]  # Last 5 iterations
        
        if len(historical_best) < 2:
            return 0.0
        
        # Calculate trend (negative = improving for minimization)
        trend = (current_best - historical_best[0]) / abs(historical_best[0]) if historical_best[0] != 0 else 0.0
        
        # Normalize to [-1, 1] range
        return max(-1.0, min(1.0, trend))

    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get analysis statistics and performance metrics."""
        return {
            'total_analyses': len(self.landscape_history),
            'ruggedness_method': self.ruggedness_method,
            'analysis_window': self.analysis_window,
            'landscape_history_size': len(self.landscape_history),
            'fitness_history_size': len(self.fitness_history),
            'last_analysis_time': getattr(self, '_last_analysis_time', None),
            'configuration': self.config.copy()
        }
