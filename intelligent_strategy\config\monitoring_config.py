"""
Monitoring configuration classes for the intelligent strategy selection system.

This module defines configuration classes for performance monitoring,
logging, metrics collection, and dashboard settings.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum


class MetricType(Enum):
    """Types of metrics to collect."""
    PERFORMANCE = "performance"
    RESOURCE = "resource"
    STRATEGY = "strategy"
    LLM = "llm"
    SYSTEM = "system"


class LogFormat(Enum):
    """Log output formats."""
    JSON = "json"
    TEXT = "text"
    STRUCTURED = "structured"


@dataclass
class MonitoringConfig:
    """Configuration for performance monitoring."""
    
    # General monitoring settings
    enabled: bool = True
    collection_interval: float = 1.0
    retention_days: int = 30
    
    # Metrics collection
    collect_performance_metrics: bool = True
    collect_resource_metrics: bool = True
    collect_strategy_metrics: bool = True
    collect_llm_metrics: bool = True
    collect_system_metrics: bool = True
    
    # Performance thresholds
    max_execution_time: float = 30.0
    max_memory_usage_mb: int = 1024
    max_cpu_usage_percent: float = 80.0
    min_success_rate: float = 0.95
    
    # Alerting settings
    enable_alerts: bool = True
    alert_threshold_execution_time: float = 10.0
    alert_threshold_memory_usage: int = 512
    alert_threshold_error_rate: float = 0.1
    
    # Storage settings
    metrics_storage_path: str = "metrics/"
    max_storage_size_mb: int = 1024
    compress_old_metrics: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'enabled': self.enabled,
            'collection_interval': self.collection_interval,
            'retention_days': self.retention_days,
            'collect_performance_metrics': self.collect_performance_metrics,
            'collect_resource_metrics': self.collect_resource_metrics,
            'collect_strategy_metrics': self.collect_strategy_metrics,
            'collect_llm_metrics': self.collect_llm_metrics,
            'collect_system_metrics': self.collect_system_metrics,
            'max_execution_time': self.max_execution_time,
            'max_memory_usage_mb': self.max_memory_usage_mb,
            'max_cpu_usage_percent': self.max_cpu_usage_percent,
            'min_success_rate': self.min_success_rate,
            'enable_alerts': self.enable_alerts,
            'alert_threshold_execution_time': self.alert_threshold_execution_time,
            'alert_threshold_memory_usage': self.alert_threshold_memory_usage,
            'alert_threshold_error_rate': self.alert_threshold_error_rate,
            'metrics_storage_path': self.metrics_storage_path,
            'max_storage_size_mb': self.max_storage_size_mb,
            'compress_old_metrics': self.compress_old_metrics
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


@dataclass
class LoggingConfig:
    """Configuration for logging."""
    
    # Basic logging settings
    enabled: bool = True
    log_level: str = "INFO"
    log_format: LogFormat = LogFormat.STRUCTURED
    
    # File logging
    log_to_file: bool = True
    log_file_path: str = "logs/intelligent_strategy.log"
    max_file_size_mb: int = 100
    backup_count: int = 5
    
    # Console logging
    log_to_console: bool = True
    console_log_level: str = "INFO"
    
    # Structured logging
    include_timestamp: bool = True
    include_level: bool = True
    include_module: bool = True
    include_function: bool = True
    include_line_number: bool = False
    
    # Performance logging
    log_execution_times: bool = True
    log_memory_usage: bool = True
    log_strategy_selections: bool = True
    log_llm_interactions: bool = True
    
    # Filtering
    exclude_modules: List[str] = field(default_factory=list)
    include_only_modules: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'enabled': self.enabled,
            'log_level': self.log_level,
            'log_format': self.log_format.value,
            'log_to_file': self.log_to_file,
            'log_file_path': self.log_file_path,
            'max_file_size_mb': self.max_file_size_mb,
            'backup_count': self.backup_count,
            'log_to_console': self.log_to_console,
            'console_log_level': self.console_log_level,
            'include_timestamp': self.include_timestamp,
            'include_level': self.include_level,
            'include_module': self.include_module,
            'include_function': self.include_function,
            'include_line_number': self.include_line_number,
            'log_execution_times': self.log_execution_times,
            'log_memory_usage': self.log_memory_usage,
            'log_strategy_selections': self.log_strategy_selections,
            'log_llm_interactions': self.log_llm_interactions,
            'exclude_modules': self.exclude_modules,
            'include_only_modules': self.include_only_modules
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LoggingConfig':
        """Create from dictionary."""
        config = cls()
        config.enabled = data.get('enabled', config.enabled)
        config.log_level = data.get('log_level', config.log_level)
        config.log_format = LogFormat(data.get('log_format', config.log_format.value))
        config.log_to_file = data.get('log_to_file', config.log_to_file)
        config.log_file_path = data.get('log_file_path', config.log_file_path)
        config.max_file_size_mb = data.get('max_file_size_mb', config.max_file_size_mb)
        config.backup_count = data.get('backup_count', config.backup_count)
        config.log_to_console = data.get('log_to_console', config.log_to_console)
        config.console_log_level = data.get('console_log_level', config.console_log_level)
        config.include_timestamp = data.get('include_timestamp', config.include_timestamp)
        config.include_level = data.get('include_level', config.include_level)
        config.include_module = data.get('include_module', config.include_module)
        config.include_function = data.get('include_function', config.include_function)
        config.include_line_number = data.get('include_line_number', config.include_line_number)
        config.log_execution_times = data.get('log_execution_times', config.log_execution_times)
        config.log_memory_usage = data.get('log_memory_usage', config.log_memory_usage)
        config.log_strategy_selections = data.get('log_strategy_selections', config.log_strategy_selections)
        config.log_llm_interactions = data.get('log_llm_interactions', config.log_llm_interactions)
        config.exclude_modules = data.get('exclude_modules', config.exclude_modules)
        config.include_only_modules = data.get('include_only_modules', config.include_only_modules)
        return config


@dataclass
class DashboardConfig:
    """Configuration for monitoring dashboard."""
    
    # Dashboard settings
    enabled: bool = False
    host: str = "localhost"
    port: int = 8050
    debug: bool = False
    
    # Authentication
    require_authentication: bool = False
    username: Optional[str] = None
    password: Optional[str] = None
    
    # Display settings
    refresh_interval: int = 5  # seconds
    max_data_points: int = 1000
    show_real_time_metrics: bool = True
    show_historical_trends: bool = True
    
    # Chart settings
    default_chart_height: int = 400
    default_chart_width: int = 600
    color_scheme: str = "plotly"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'enabled': self.enabled,
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'require_authentication': self.require_authentication,
            'username': self.username,
            'password': self.password,
            'refresh_interval': self.refresh_interval,
            'max_data_points': self.max_data_points,
            'show_real_time_metrics': self.show_real_time_metrics,
            'show_historical_trends': self.show_historical_trends,
            'default_chart_height': self.default_chart_height,
            'default_chart_width': self.default_chart_width,
            'color_scheme': self.color_scheme
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DashboardConfig':
        """Create from dictionary."""
        config = cls()
        for key, value in data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config


# Default configurations
DEFAULT_MONITORING_CONFIG = MonitoringConfig()
DEFAULT_LOGGING_CONFIG = LoggingConfig()
DEFAULT_DASHBOARD_CONFIG = DashboardConfig()

# Environment-specific configurations
DEVELOPMENT_MONITORING_CONFIG = MonitoringConfig(
    collection_interval=0.5,
    enable_alerts=False
)

PRODUCTION_MONITORING_CONFIG = MonitoringConfig(
    collection_interval=5.0,
    enable_alerts=True,
    retention_days=90
)
