2025-08-05 10:28:29,389 - __main__ - INFO - simple2_10 开始进化第 1 代
2025-08-05 10:28:29,389 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:29,390 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,393 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1359.000, 多样性=0.900
2025-08-05 10:28:29,394 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:29,396 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.900
2025-08-05 10:28:29,429 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:29,431 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:29,431 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:29,432 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:29,432 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:29,438 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: 18.300, 聚类评分: 0.000, 覆盖率: 0.006, 收敛趋势: 0.000, 多样性: 0.900
2025-08-05 10:28:29,439 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:29,439 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:29,439 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 10:28:29,445 - visualization.landscape_visualizer - INFO - 插值约束: 99 个点被约束到最小值 1359.00
2025-08-05 10:28:29,447 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.4%, 梯度: 39.33 → 35.64
2025-08-05 10:28:29,550 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_6_20250805_102829.html
2025-08-05 10:28:29,626 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_6_20250805_102829.html
2025-08-05 10:28:29,626 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 6
2025-08-05 10:28:29,626 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:29,626 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1953秒
2025-08-05 10:28:29,626 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 12, 'max_size': 500, 'hits': 0, 'misses': 12, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 18, 'max_size': 100, 'hits': 37, 'misses': 18, 'hit_rate': 0.6727272727272727, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:29,626 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 18.300000000000004, 'local_optima_density': 0.2, 'gradient_variance': 143344.714, 'cluster_count': 0}, 'population_state': {'diversity': 0.9, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0064, 'fitness_entropy': 0.9695703501901249, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.006)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.900)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 18.300)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360909.4392781, 'performance_metrics': {}}}
2025-08-05 10:28:29,627 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:29,627 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:29,627 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:29,627 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:29,627 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:29,627 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:29,627 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:29,628 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,628 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:29,628 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:29,628 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,628 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:29,628 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:29,629 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:29,629 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:29,629 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,630 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:29,630 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2007.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,630 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 1, 5, 2, 9, 6, 3, 8, 7], 'cur_cost': 2007.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,631 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 2007.00)
2025-08-05 10:28:29,631 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:29,631 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:29,631 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,631 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1509.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,632 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 5, 3, 2, 4, 0, 8, 6, 9, 7], 'cur_cost': 1509.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,632 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1509.00)
2025-08-05 10:28:29,632 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:29,632 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:29,632 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,632 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:29,633 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1885.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,633 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,633 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1885.00)
2025-08-05 10:28:29,633 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:29,633 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:29,633 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,633 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,634 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,634 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2253.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,634 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 6, 0, 5, 3, 8, 4, 2, 9, 7], 'cur_cost': 2253.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,634 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2253.00)
2025-08-05 10:28:29,634 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:29,634 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,634 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,634 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1863.0
2025-08-05 10:28:29,638 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:29,638 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265]
2025-08-05 10:28:29,638 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64)]
2025-08-05 10:28:29,639 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,639 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 1, 5, 2, 9, 6, 3, 8, 7], 'cur_cost': 2007.0}, {'tour': [1, 5, 3, 2, 4, 0, 8, 6, 9, 7], 'cur_cost': 1509.0}, {'tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0}, {'tour': [1, 6, 0, 5, 3, 8, 4, 2, 9, 7], 'cur_cost': 2253.0}, {'tour': array([8, 7, 1, 3, 2, 0, 9, 5, 4, 6], dtype=int64), 'cur_cost': 1863.0}, {'tour': array([6, 8, 1, 5, 9, 4, 3, 2, 0, 7], dtype=int64), 'cur_cost': 1919.0}, {'tour': array([1, 3, 9, 4, 0, 6, 7, 5, 8, 2], dtype=int64), 'cur_cost': 1795.0}, {'tour': array([3, 2, 5, 8, 9, 1, 0, 6, 4, 7], dtype=int64), 'cur_cost': 2072.0}, {'tour': array([1, 2, 6, 0, 8, 4, 5, 9, 7, 3], dtype=int64), 'cur_cost': 1920.0}, {'tour': array([1, 8, 2, 0, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2129.0}]
2025-08-05 10:28:29,640 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,641 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-05 10:28:29,641 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([8, 7, 1, 3, 2, 0, 9, 5, 4, 6], dtype=int64), 'cur_cost': 1863.0, 'intermediate_solutions': [{'tour': array([8, 2, 9, 4, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 2, 9, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2240.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 8, 2, 9, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 4, 8, 2, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 4, 8, 2, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,641 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1863.00)
2025-08-05 10:28:29,641 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:29,641 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:29,642 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,642 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:29,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2077.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,642 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 6, 0, 2, 1, 9, 8, 3, 7, 5], 'cur_cost': 2077.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,642 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 2077.00)
2025-08-05 10:28:29,642 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:29,643 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:29,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,643 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,643 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1817.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,643 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 9, 8, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 1817.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,643 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1817.00)
2025-08-05 10:28:29,644 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:29,644 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:29,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1854.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,644 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 8, 4, 9, 3, 2, 1, 0, 6, 7], 'cur_cost': 1854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,645 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1854.00)
2025-08-05 10:28:29,645 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:29,645 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:29,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,645 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1970.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:29,645 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 9, 1, 2, 7, 8, 6, 0, 3, 5], 'cur_cost': 1970.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,646 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1970.00)
2025-08-05 10:28:29,646 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:29,646 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,646 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,646 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1946.0
2025-08-05 10:28:29,652 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,652 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:29,652 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:29,653 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,653 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 1, 5, 2, 9, 6, 3, 8, 7], 'cur_cost': 2007.0}, {'tour': [1, 5, 3, 2, 4, 0, 8, 6, 9, 7], 'cur_cost': 1509.0}, {'tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0}, {'tour': [1, 6, 0, 5, 3, 8, 4, 2, 9, 7], 'cur_cost': 2253.0}, {'tour': array([8, 7, 1, 3, 2, 0, 9, 5, 4, 6], dtype=int64), 'cur_cost': 1863.0}, {'tour': [4, 6, 0, 2, 1, 9, 8, 3, 7, 5], 'cur_cost': 2077.0}, {'tour': [3, 9, 8, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 1817.0}, {'tour': [5, 8, 4, 9, 3, 2, 1, 0, 6, 7], 'cur_cost': 1854.0}, {'tour': [4, 9, 1, 2, 7, 8, 6, 0, 3, 5], 'cur_cost': 1970.0}, {'tour': array([2, 8, 9, 1, 3, 7, 5, 6, 4, 0], dtype=int64), 'cur_cost': 1946.0}]
2025-08-05 10:28:29,654 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,654 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 79, 'cache_hit_rate': 0.0, 'cache_size': 79}}
2025-08-05 10:28:29,654 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 8, 9, 1, 3, 7, 5, 6, 4, 0], dtype=int64), 'cur_cost': 1946.0, 'intermediate_solutions': [{'tour': array([2, 8, 1, 0, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 8, 1, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 2, 8, 1, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 2, 8, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 1968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 0, 2, 8, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 1909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,654 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1946.00)
2025-08-05 10:28:29,654 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:29,655 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:29,655 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 1, 5, 2, 9, 6, 3, 8, 7], 'cur_cost': 2007.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 3, 2, 4, 0, 8, 6, 9, 7], 'cur_cost': 1509.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 0, 5, 3, 8, 4, 2, 9, 7], 'cur_cost': 2253.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 7, 1, 3, 2, 0, 9, 5, 4, 6], dtype=int64), 'cur_cost': 1863.0, 'intermediate_solutions': [{'tour': array([8, 2, 9, 4, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 8, 2, 9, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2240.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 4, 8, 2, 9, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 4, 8, 2, 5, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 4, 8, 2, 0, 3, 1, 6, 7], dtype=int64), 'cur_cost': 2108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 2, 1, 9, 8, 3, 7, 5], 'cur_cost': 2077.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 8, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 1817.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 4, 9, 3, 2, 1, 0, 6, 7], 'cur_cost': 1854.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 1, 2, 7, 8, 6, 0, 3, 5], 'cur_cost': 1970.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 8, 9, 1, 3, 7, 5, 6, 4, 0], dtype=int64), 'cur_cost': 1946.0, 'intermediate_solutions': [{'tour': array([2, 8, 1, 0, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 8, 1, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2315.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 0, 2, 8, 1, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 2337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 2, 8, 9, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 1968.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 9, 0, 2, 8, 6, 4, 7, 3, 5], dtype=int64), 'cur_cost': 1909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:29,656 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:29,656 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,657 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1509.000, 多样性=0.898
2025-08-05 10:28:29,657 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:29,657 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:29,657 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:29,657 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05268246761380607, 'best_improvement': -0.11037527593818984}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.002469135802469152}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0026423540161693362, 'recent_improvements': [-0.006277916292107011, 0.02498541435152697, -0.00099320825976834], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:29,657 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:29,658 - __main__ - INFO - simple2_10 开始进化第 2 代
2025-08-05 10:28:29,658 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:29,658 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,658 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1509.000, 多样性=0.898
2025-08-05 10:28:29,658 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:29,659 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.898
2025-08-05 10:28:29,659 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:29,660 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 10:28:29,661 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:29,662 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:29,662 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:29,662 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:29,670 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 11.586, 聚类评分: 0.000, 覆盖率: 0.008, 收敛趋势: 0.000, 多样性: 0.625
2025-08-05 10:28:29,671 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:29,671 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:29,671 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 10:28:29,679 - visualization.landscape_visualizer - INFO - 插值约束: 14 个点被约束到最小值 1265.00
2025-08-05 10:28:29,682 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.0%, 梯度: 35.59 → 32.74
2025-08-05 10:28:29,814 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_7_20250805_102829.html
2025-08-05 10:28:29,893 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_7_20250805_102829.html
2025-08-05 10:28:29,893 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 7
2025-08-05 10:28:29,893 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:29,893 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2321秒
2025-08-05 10:28:29,894 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 11.585714285714293, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 96759.57979591838, 'cluster_count': 0}, 'population_state': {'diversity': 0.6248037676609105, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0078, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.008)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 11.586)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360909.6716175, 'performance_metrics': {}}}
2025-08-05 10:28:29,894 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:29,894 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:29,894 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:29,894 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:29,895 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:29,895 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:29,895 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:29,895 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,895 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:29,895 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:29,896 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:29,896 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:29,896 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:29,896 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:29,896 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:29,897 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,897 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,898 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,898 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1708.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,898 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 8, 9, 3, 1, 5, 7, 6, 0, 2], 'cur_cost': 1708.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 5, 2, 4, 6, 3, 8, 7], 'cur_cost': 2148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 1, 5, 2, 9, 6, 7, 8, 3], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 1, 5, 9, 6, 3, 8, 7], 'cur_cost': 2180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,898 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1708.00)
2025-08-05 10:28:29,898 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:29,898 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:29,899 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,899 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,899 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,900 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2196.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,900 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 6, 0, 1, 8, 4, 9, 3, 5, 7], 'cur_cost': 2196.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 2, 5, 0, 8, 6, 9, 7], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 4, 0, 8, 7, 9, 6], 'cur_cost': 1649.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 1, 4, 0, 8, 6, 9, 7], 'cur_cost': 1460.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,900 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 2196.00)
2025-08-05 10:28:29,900 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:29,900 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,902 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1687.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,902 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 0, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [5, 2, 6, 3, 1, 4, 9, 8, 7, 0], 'cur_cost': 2343.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 7, 8, 9, 5, 1, 3, 0], 'cur_cost': 1708.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,902 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1687.00)
2025-08-05 10:28:29,902 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:29,902 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,902 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,902 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 2339.0
2025-08-05 10:28:29,907 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,908 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:29,908 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:29,909 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,909 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 9, 3, 1, 5, 7, 6, 0, 2], 'cur_cost': 1708.0}, {'tour': [2, 6, 0, 1, 8, 4, 9, 3, 5, 7], 'cur_cost': 2196.0}, {'tour': [4, 0, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1687.0}, {'tour': array([1, 6, 0, 2, 3, 9, 7, 4, 5, 8], dtype=int64), 'cur_cost': 2339.0}, {'tour': [8, 7, 1, 3, 2, 0, 9, 5, 4, 6], 'cur_cost': 1863.0}, {'tour': [4, 6, 0, 2, 1, 9, 8, 3, 7, 5], 'cur_cost': 2077.0}, {'tour': [3, 9, 8, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 1817.0}, {'tour': [5, 8, 4, 9, 3, 2, 1, 0, 6, 7], 'cur_cost': 1854.0}, {'tour': [4, 9, 1, 2, 7, 8, 6, 0, 3, 5], 'cur_cost': 1970.0}, {'tour': [2, 8, 9, 1, 3, 7, 5, 6, 4, 0], 'cur_cost': 1946.0}]
2025-08-05 10:28:29,909 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,909 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 81, 'cache_hit_rate': 0.0, 'cache_size': 81}}
2025-08-05 10:28:29,910 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([1, 6, 0, 2, 3, 9, 7, 4, 5, 8], dtype=int64), 'cur_cost': 2339.0, 'intermediate_solutions': [{'tour': array([0, 6, 1, 5, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 6, 1, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 0, 6, 1, 8, 4, 2, 9, 7]), 'cur_cost': 2250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 0, 6, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 5, 0, 6, 8, 4, 2, 9, 7]), 'cur_cost': 1844.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,910 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2339.00)
2025-08-05 10:28:29,910 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:29,910 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:29,910 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,910 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,910 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1744.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,911 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [3, 5, 2, 9, 6, 8, 4, 0, 1, 7], 'cur_cost': 1744.0, 'intermediate_solutions': [{'tour': [8, 7, 1, 3, 2, 0, 6, 5, 4, 9], 'cur_cost': 2144.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 3, 2, 0, 6, 4, 5, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 1, 3, 0, 9, 2, 5, 4, 6], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,911 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1744.00)
2025-08-05 10:28:29,911 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:29,911 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:29,912 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:29,912 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 1881.0
2025-08-05 10:28:29,917 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:29,917 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:29,917 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:29,918 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:29,918 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 9, 3, 1, 5, 7, 6, 0, 2], 'cur_cost': 1708.0}, {'tour': [2, 6, 0, 1, 8, 4, 9, 3, 5, 7], 'cur_cost': 2196.0}, {'tour': [4, 0, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1687.0}, {'tour': array([1, 6, 0, 2, 3, 9, 7, 4, 5, 8], dtype=int64), 'cur_cost': 2339.0}, {'tour': [3, 5, 2, 9, 6, 8, 4, 0, 1, 7], 'cur_cost': 1744.0}, {'tour': array([2, 3, 4, 0, 6, 9, 8, 5, 7, 1], dtype=int64), 'cur_cost': 1881.0}, {'tour': [3, 9, 8, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 1817.0}, {'tour': [5, 8, 4, 9, 3, 2, 1, 0, 6, 7], 'cur_cost': 1854.0}, {'tour': [4, 9, 1, 2, 7, 8, 6, 0, 3, 5], 'cur_cost': 1970.0}, {'tour': [2, 8, 9, 1, 3, 7, 5, 6, 4, 0], 'cur_cost': 1946.0}]
2025-08-05 10:28:29,919 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:29,919 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 84, 'cache_hit_rate': 0.0, 'cache_size': 84}}
2025-08-05 10:28:29,919 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([2, 3, 4, 0, 6, 9, 8, 5, 7, 1], dtype=int64), 'cur_cost': 1881.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 2, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 4, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 0, 6, 4, 9, 8, 3, 7, 5]), 'cur_cost': 1865.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 0, 6, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 2, 0, 6, 9, 8, 3, 7, 5]), 'cur_cost': 2172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:29,920 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1881.00)
2025-08-05 10:28:29,920 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:29,920 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:29,920 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,920 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,920 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,920 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,921 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2348.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,921 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 6, 0, 5, 8, 4, 9, 3, 1, 7], 'cur_cost': 2348.0, 'intermediate_solutions': [{'tour': [8, 9, 3, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 2083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 8, 7, 0, 4, 2, 5, 6, 1], 'cur_cost': 2037.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 9, 5, 2, 4, 0, 6, 1], 'cur_cost': 1832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,921 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 2348.00)
2025-08-05 10:28:29,921 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:29,921 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:29,921 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1839.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,922 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 8, 0, 7, 6, 4, 2, 1, 5, 9], 'cur_cost': 1839.0, 'intermediate_solutions': [{'tour': [5, 6, 4, 9, 3, 2, 1, 0, 8, 7], 'cur_cost': 1873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 4, 8, 5, 2, 1, 0, 6, 7], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 3, 2, 1, 0, 9, 6, 7], 'cur_cost': 1966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,923 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1839.00)
2025-08-05 10:28:29,923 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:29,923 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:29,923 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,923 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:29,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,923 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,924 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1672.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,924 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 5, 3, 2, 0, 8, 4, 6, 9, 7], 'cur_cost': 1672.0, 'intermediate_solutions': [{'tour': [4, 9, 1, 7, 2, 8, 6, 0, 3, 5], 'cur_cost': 2265.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 6, 8, 7, 2, 1, 0, 3, 5], 'cur_cost': 1977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 1, 2, 7, 8, 5, 6, 0, 3], 'cur_cost': 2310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,924 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1672.00)
2025-08-05 10:28:29,924 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:29,924 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:29,924 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:29,925 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2014.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:29,925 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 6, 3, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2014.0, 'intermediate_solutions': [{'tour': [2, 8, 9, 1, 3, 7, 0, 6, 4, 5], 'cur_cost': 2181.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 9, 1, 3, 7, 5, 0, 4, 6], 'cur_cost': 2038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 9, 1, 3, 5, 6, 4, 0], 'cur_cost': 1758.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:29,926 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2014.00)
2025-08-05 10:28:29,926 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:29,926 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:29,927 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 9, 3, 1, 5, 7, 6, 0, 2], 'cur_cost': 1708.0, 'intermediate_solutions': [{'tour': [0, 9, 1, 5, 2, 4, 6, 3, 8, 7], 'cur_cost': 2148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 1, 5, 2, 9, 6, 7, 8, 3], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 4, 1, 5, 9, 6, 3, 8, 7], 'cur_cost': 2180.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 0, 1, 8, 4, 9, 3, 5, 7], 'cur_cost': 2196.0, 'intermediate_solutions': [{'tour': [1, 4, 3, 2, 5, 0, 8, 6, 9, 7], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 4, 0, 8, 7, 9, 6], 'cur_cost': 1649.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 3, 2, 1, 4, 0, 8, 6, 9, 7], 'cur_cost': 1460.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1687.0, 'intermediate_solutions': [{'tour': [5, 2, 6, 3, 1, 4, 9, 8, 7, 0], 'cur_cost': 2343.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 2, 6, 7, 8, 9, 5, 1, 3, 0], 'cur_cost': 1708.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 2, 6, 3, 1, 5, 9, 8, 7, 0], 'cur_cost': 1885.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 6, 0, 2, 3, 9, 7, 4, 5, 8], dtype=int64), 'cur_cost': 2339.0, 'intermediate_solutions': [{'tour': array([0, 6, 1, 5, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 6, 1, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2143.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 5, 0, 6, 1, 8, 4, 2, 9, 7]), 'cur_cost': 2250.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 0, 6, 3, 8, 4, 2, 9, 7]), 'cur_cost': 2164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 5, 0, 6, 8, 4, 2, 9, 7]), 'cur_cost': 1844.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 2, 9, 6, 8, 4, 0, 1, 7], 'cur_cost': 1744.0, 'intermediate_solutions': [{'tour': [8, 7, 1, 3, 2, 0, 6, 5, 4, 9], 'cur_cost': 2144.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 7, 1, 3, 2, 0, 6, 4, 5, 9], 'cur_cost': 1915.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 7, 1, 3, 0, 9, 2, 5, 4, 6], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 3, 4, 0, 6, 9, 8, 5, 7, 1], dtype=int64), 'cur_cost': 1881.0, 'intermediate_solutions': [{'tour': array([0, 6, 4, 2, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2099.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 4, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2102.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 2, 0, 6, 4, 9, 8, 3, 7, 5]), 'cur_cost': 1865.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 0, 6, 1, 9, 8, 3, 7, 5]), 'cur_cost': 2388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 1, 2, 0, 6, 9, 8, 3, 7, 5]), 'cur_cost': 2172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 0, 5, 8, 4, 9, 3, 1, 7], 'cur_cost': 2348.0, 'intermediate_solutions': [{'tour': [8, 9, 3, 7, 5, 2, 4, 0, 6, 1], 'cur_cost': 2083.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 8, 7, 0, 4, 2, 5, 6, 1], 'cur_cost': 2037.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 7, 9, 5, 2, 4, 0, 6, 1], 'cur_cost': 1832.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 0, 7, 6, 4, 2, 1, 5, 9], 'cur_cost': 1839.0, 'intermediate_solutions': [{'tour': [5, 6, 4, 9, 3, 2, 1, 0, 8, 7], 'cur_cost': 1873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 4, 8, 5, 2, 1, 0, 6, 7], 'cur_cost': 1959.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 4, 3, 2, 1, 0, 9, 6, 7], 'cur_cost': 1966.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 3, 2, 0, 8, 4, 6, 9, 7], 'cur_cost': 1672.0, 'intermediate_solutions': [{'tour': [4, 9, 1, 7, 2, 8, 6, 0, 3, 5], 'cur_cost': 2265.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 6, 8, 7, 2, 1, 0, 3, 5], 'cur_cost': 1977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 1, 2, 7, 8, 5, 6, 0, 3], 'cur_cost': 2310.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 3, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2014.0, 'intermediate_solutions': [{'tour': [2, 8, 9, 1, 3, 7, 0, 6, 4, 5], 'cur_cost': 2181.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 9, 1, 3, 7, 5, 0, 4, 6], 'cur_cost': 2038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 7, 9, 1, 3, 5, 6, 4, 0], 'cur_cost': 1758.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:29,927 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:29,927 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,928 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1672.000, 多样性=0.864
2025-08-05 10:28:29,928 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:29,928 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:29,928 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:29,928 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.05435396091446682, 'best_improvement': -0.10801855533465872}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.037128712871286995}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03883394098266652, 'recent_improvements': [0.02498541435152697, -0.00099320825976834, -0.05268246761380607], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:29,929 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:29,929 - __main__ - INFO - simple2_10 开始进化第 3 代
2025-08-05 10:28:29,929 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:29,929 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:29,930 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1672.000, 多样性=0.864
2025-08-05 10:28:29,930 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:29,930 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.864
2025-08-05 10:28:29,930 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:29,931 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 10:28:29,933 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:29,933 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:29,933 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:29,933 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:29,947 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 70.386, 聚类评分: 0.000, 覆盖率: 0.009, 收敛趋势: 0.000, 多样性: 0.619
2025-08-05 10:28:29,947 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:29,947 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:29,947 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 10:28:29,952 - visualization.landscape_visualizer - INFO - 插值约束: 248 个点被约束到最小值 1265.00
2025-08-05 10:28:29,953 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.1%, 梯度: 31.34 → 28.82
2025-08-05 10:28:30,066 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_8_20250805_102829.html
2025-08-05 10:28:30,135 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_8_20250805_102829.html
2025-08-05 10:28:30,135 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 8
2025-08-05 10:28:30,136 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:30,136 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2027秒
2025-08-05 10:28:30,136 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 70.38571428571429, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 57432.619795918356, 'cluster_count': 0}, 'population_state': {'diversity': 0.618524332810047, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0088, 'fitness_entropy': 0.9491132589845683, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.009)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 70.386)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360909.9477265, 'performance_metrics': {}}}
2025-08-05 10:28:30,136 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:30,136 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:30,136 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:30,137 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:30,137 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:30,137 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:30,137 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:30,137 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,138 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:30,138 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:30,138 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,138 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:30,138 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:30,138 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:30,139 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:30,139 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,139 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,140 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,141 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,141 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1706.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,141 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 8, 5, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1706.0, 'intermediate_solutions': [{'tour': [4, 3, 9, 8, 1, 5, 7, 6, 0, 2], 'cur_cost': 2002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 7, 5, 1, 3, 9, 0, 2], 'cur_cost': 1656.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 9, 3, 1, 6, 5, 7, 0, 2], 'cur_cost': 2200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,142 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1706.00)
2025-08-05 10:28:30,142 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:30,142 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,142 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,142 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 2121.0
2025-08-05 10:28:30,148 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,149 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,149 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,150 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,150 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 5, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1706.0}, {'tour': array([8, 5, 6, 0, 4, 2, 7, 1, 3, 9], dtype=int64), 'cur_cost': 2121.0}, {'tour': [4, 0, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1687.0}, {'tour': [1, 6, 0, 2, 3, 9, 7, 4, 5, 8], 'cur_cost': 2339.0}, {'tour': [3, 5, 2, 9, 6, 8, 4, 0, 1, 7], 'cur_cost': 1744.0}, {'tour': [2, 3, 4, 0, 6, 9, 8, 5, 7, 1], 'cur_cost': 1881.0}, {'tour': [2, 6, 0, 5, 8, 4, 9, 3, 1, 7], 'cur_cost': 2348.0}, {'tour': [3, 8, 0, 7, 6, 4, 2, 1, 5, 9], 'cur_cost': 1839.0}, {'tour': [1, 5, 3, 2, 0, 8, 4, 6, 9, 7], 'cur_cost': 1672.0}, {'tour': [1, 6, 3, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2014.0}]
2025-08-05 10:28:30,151 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,151 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 88, 'cache_hit_rate': 0.0, 'cache_size': 88}}
2025-08-05 10:28:30,151 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([8, 5, 6, 0, 4, 2, 7, 1, 3, 9], dtype=int64), 'cur_cost': 2121.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 1, 8, 4, 9, 3, 5, 7]), 'cur_cost': 2092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 0, 6, 2, 8, 4, 9, 3, 5, 7]), 'cur_cost': 2168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 0, 6, 2, 4, 9, 3, 5, 7]), 'cur_cost': 2159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 0, 6, 8, 4, 9, 3, 5, 7]), 'cur_cost': 1762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 1, 0, 6, 4, 9, 3, 5, 7]), 'cur_cost': 2172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,152 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 2121.00)
2025-08-05 10:28:30,152 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:30,152 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:30,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,152 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,152 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,153 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2001.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,153 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 7, 1, 8, 4, 0, 2, 3, 9, 6], 'cur_cost': 2001.0, 'intermediate_solutions': [{'tour': [1, 0, 3, 9, 7, 8, 2, 4, 5, 6], 'cur_cost': 2403.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1744.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 9, 7, 8, 2, 1, 5, 6, 4], 'cur_cost': 1687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,154 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 2001.00)
2025-08-05 10:28:30,154 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:30,154 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,154 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,154 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1915.0
2025-08-05 10:28:30,163 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,163 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,164 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,165 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,165 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 5, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1706.0}, {'tour': array([8, 5, 6, 0, 4, 2, 7, 1, 3, 9], dtype=int64), 'cur_cost': 2121.0}, {'tour': [5, 7, 1, 8, 4, 0, 2, 3, 9, 6], 'cur_cost': 2001.0}, {'tour': array([6, 3, 1, 0, 8, 4, 2, 5, 7, 9], dtype=int64), 'cur_cost': 1915.0}, {'tour': [3, 5, 2, 9, 6, 8, 4, 0, 1, 7], 'cur_cost': 1744.0}, {'tour': [2, 3, 4, 0, 6, 9, 8, 5, 7, 1], 'cur_cost': 1881.0}, {'tour': [2, 6, 0, 5, 8, 4, 9, 3, 1, 7], 'cur_cost': 2348.0}, {'tour': [3, 8, 0, 7, 6, 4, 2, 1, 5, 9], 'cur_cost': 1839.0}, {'tour': [1, 5, 3, 2, 0, 8, 4, 6, 9, 7], 'cur_cost': 1672.0}, {'tour': [1, 6, 3, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2014.0}]
2025-08-05 10:28:30,166 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,166 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 93, 'cache_hit_rate': 0.0, 'cache_size': 93}}
2025-08-05 10:28:30,167 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 3, 1, 0, 8, 4, 2, 5, 7, 9], dtype=int64), 'cur_cost': 1915.0, 'intermediate_solutions': [{'tour': array([0, 6, 1, 2, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 1, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2302.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 0, 6, 1, 9, 7, 4, 5, 8]), 'cur_cost': 2345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 0, 6, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2275.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 2, 0, 6, 9, 7, 4, 5, 8]), 'cur_cost': 2156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,167 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1915.00)
2025-08-05 10:28:30,167 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:30,167 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:30,167 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,168 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,168 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,169 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,169 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,169 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1785.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,169 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0, 'intermediate_solutions': [{'tour': [3, 5, 2, 6, 9, 8, 4, 0, 1, 7], 'cur_cost': 1945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 9, 6, 8, 4, 0, 7, 1], 'cur_cost': 1697.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 9, 6, 8, 0, 4, 1, 7], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,170 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1785.00)
2025-08-05 10:28:30,170 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:30,170 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:30,170 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1962.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,172 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 8, 0, 1, 9, 3, 5, 7, 6, 4], 'cur_cost': 1962.0, 'intermediate_solutions': [{'tour': [2, 3, 4, 0, 9, 6, 8, 5, 7, 1], 'cur_cost': 1829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 4, 0, 6, 8, 9, 5, 7, 1], 'cur_cost': 1624.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 6, 3, 9, 8, 5, 7, 1], 'cur_cost': 1992.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,172 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1962.00)
2025-08-05 10:28:30,172 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:30,172 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,172 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,173 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 2275.0
2025-08-05 10:28:30,180 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,180 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,180 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,181 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,181 - ExploitationExpert - INFO - populations: [{'tour': [6, 8, 5, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1706.0}, {'tour': array([8, 5, 6, 0, 4, 2, 7, 1, 3, 9], dtype=int64), 'cur_cost': 2121.0}, {'tour': [5, 7, 1, 8, 4, 0, 2, 3, 9, 6], 'cur_cost': 2001.0}, {'tour': array([6, 3, 1, 0, 8, 4, 2, 5, 7, 9], dtype=int64), 'cur_cost': 1915.0}, {'tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0}, {'tour': [2, 8, 0, 1, 9, 3, 5, 7, 6, 4], 'cur_cost': 1962.0}, {'tour': array([0, 5, 7, 6, 9, 4, 1, 8, 3, 2], dtype=int64), 'cur_cost': 2275.0}, {'tour': [3, 8, 0, 7, 6, 4, 2, 1, 5, 9], 'cur_cost': 1839.0}, {'tour': [1, 5, 3, 2, 0, 8, 4, 6, 9, 7], 'cur_cost': 1672.0}, {'tour': [1, 6, 3, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2014.0}]
2025-08-05 10:28:30,181 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,181 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 99, 'cache_hit_rate': 0.0, 'cache_size': 99}}
2025-08-05 10:28:30,182 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([0, 5, 7, 6, 9, 4, 1, 8, 3, 2], dtype=int64), 'cur_cost': 2275.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 5, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 6, 2, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 0, 6, 2, 4, 9, 3, 1, 7]), 'cur_cost': 2311.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 0, 6, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 5, 0, 6, 4, 9, 3, 1, 7]), 'cur_cost': 2324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,182 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 2275.00)
2025-08-05 10:28:30,182 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:30,182 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:30,182 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,183 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2047.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,183 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 3, 0, 5, 2, 1, 4, 8, 9, 6], 'cur_cost': 2047.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 3, 6, 4, 2, 1, 5, 9], 'cur_cost': 1768.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 7, 6, 4, 2, 5, 1, 9], 'cur_cost': 1969.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 0, 9, 7, 6, 4, 2, 1, 5], 'cur_cost': 1723.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,183 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 2047.00)
2025-08-05 10:28:30,183 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,184 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,185 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1971.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,185 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 9, 0, 3, 4, 7, 8, 6, 5, 1], 'cur_cost': 1971.0, 'intermediate_solutions': [{'tour': [1, 5, 3, 2, 0, 4, 8, 6, 9, 7], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 9, 6, 4, 8, 0, 7], 'cur_cost': 1844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 2, 8, 4, 6, 9, 7, 0], 'cur_cost': 1778.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,185 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1971.00)
2025-08-05 10:28:30,185 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:30,185 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:30,185 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,185 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,186 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,186 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2314.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,186 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 6, 0, 9, 4, 5, 3, 1, 8, 7], 'cur_cost': 2314.0, 'intermediate_solutions': [{'tour': [3, 6, 1, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2067.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 9, 2, 0, 4, 3, 6, 5], 'cur_cost': 2010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 4, 0, 2, 9, 7, 5, 8], 'cur_cost': 2261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,186 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2314.00)
2025-08-05 10:28:30,187 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:30,187 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:30,188 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 5, 1, 9, 3, 2, 4, 0, 7], 'cur_cost': 1706.0, 'intermediate_solutions': [{'tour': [4, 3, 9, 8, 1, 5, 7, 6, 0, 2], 'cur_cost': 2002.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 6, 7, 5, 1, 3, 9, 0, 2], 'cur_cost': 1656.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 9, 3, 1, 6, 5, 7, 0, 2], 'cur_cost': 2200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 6, 0, 4, 2, 7, 1, 3, 9], dtype=int64), 'cur_cost': 2121.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 1, 8, 4, 9, 3, 5, 7]), 'cur_cost': 2092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 0, 6, 2, 8, 4, 9, 3, 5, 7]), 'cur_cost': 2168.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 0, 6, 2, 4, 9, 3, 5, 7]), 'cur_cost': 2159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 1, 0, 6, 8, 4, 9, 3, 5, 7]), 'cur_cost': 1762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 1, 0, 6, 4, 9, 3, 5, 7]), 'cur_cost': 2172.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 1, 8, 4, 0, 2, 3, 9, 6], 'cur_cost': 2001.0, 'intermediate_solutions': [{'tour': [1, 0, 3, 9, 7, 8, 2, 4, 5, 6], 'cur_cost': 2403.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 3, 9, 7, 8, 2, 1, 5, 6], 'cur_cost': 1744.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 9, 7, 8, 2, 1, 5, 6, 4], 'cur_cost': 1687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 1, 0, 8, 4, 2, 5, 7, 9], dtype=int64), 'cur_cost': 1915.0, 'intermediate_solutions': [{'tour': array([0, 6, 1, 2, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 0, 6, 1, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2302.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 2, 0, 6, 1, 9, 7, 4, 5, 8]), 'cur_cost': 2345.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 2, 0, 6, 3, 9, 7, 4, 5, 8]), 'cur_cost': 2275.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 2, 0, 6, 9, 7, 4, 5, 8]), 'cur_cost': 2156.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0, 'intermediate_solutions': [{'tour': [3, 5, 2, 6, 9, 8, 4, 0, 1, 7], 'cur_cost': 1945.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 2, 9, 6, 8, 4, 0, 7, 1], 'cur_cost': 1697.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 2, 9, 6, 8, 0, 4, 1, 7], 'cur_cost': 1830.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 0, 1, 9, 3, 5, 7, 6, 4], 'cur_cost': 1962.0, 'intermediate_solutions': [{'tour': [2, 3, 4, 0, 9, 6, 8, 5, 7, 1], 'cur_cost': 1829.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 4, 0, 6, 8, 9, 5, 7, 1], 'cur_cost': 1624.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 0, 6, 3, 9, 8, 5, 7, 1], 'cur_cost': 1992.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 5, 7, 6, 9, 4, 1, 8, 3, 2], dtype=int64), 'cur_cost': 2275.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 5, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 0, 6, 2, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 0, 6, 2, 4, 9, 3, 1, 7]), 'cur_cost': 2311.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 5, 0, 6, 8, 4, 9, 3, 1, 7]), 'cur_cost': 2007.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 8, 5, 0, 6, 4, 9, 3, 1, 7]), 'cur_cost': 2324.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 0, 5, 2, 1, 4, 8, 9, 6], 'cur_cost': 2047.0, 'intermediate_solutions': [{'tour': [7, 8, 0, 3, 6, 4, 2, 1, 5, 9], 'cur_cost': 1768.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 0, 7, 6, 4, 2, 5, 1, 9], 'cur_cost': 1969.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 0, 9, 7, 6, 4, 2, 1, 5], 'cur_cost': 1723.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 0, 3, 4, 7, 8, 6, 5, 1], 'cur_cost': 1971.0, 'intermediate_solutions': [{'tour': [1, 5, 3, 2, 0, 4, 8, 6, 9, 7], 'cur_cost': 1439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 9, 6, 4, 8, 0, 7], 'cur_cost': 1844.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 2, 8, 4, 6, 9, 7, 0], 'cur_cost': 1778.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 0, 9, 4, 5, 3, 1, 8, 7], 'cur_cost': 2314.0, 'intermediate_solutions': [{'tour': [3, 6, 1, 4, 0, 2, 9, 7, 8, 5], 'cur_cost': 2067.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 7, 9, 2, 0, 4, 3, 6, 5], 'cur_cost': 2010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 4, 0, 2, 9, 7, 5, 8], 'cur_cost': 2261.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:30,188 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:30,188 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,189 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1706.000, 多样性=0.909
2025-08-05 10:28:30,189 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:30,189 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:30,190 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:30,190 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.013429785229925767, 'best_improvement': -0.02033492822966507}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.051413881748072175}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.026680376327349237, 'recent_improvements': [-0.00099320825976834, -0.05268246761380607, -0.05435396091446682], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:30,190 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:30,190 - __main__ - INFO - simple2_10 开始进化第 4 代
2025-08-05 10:28:30,190 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:30,190 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,191 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1706.000, 多样性=0.909
2025-08-05 10:28:30,191 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:30,191 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.909
2025-08-05 10:28:30,192 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:30,192 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 10:28:30,194 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:30,195 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:30,195 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:30,195 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:30,204 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 29.529, 聚类评分: 0.000, 覆盖率: 0.010, 收敛趋势: 0.000, 多样性: 0.638
2025-08-05 10:28:30,204 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:30,204 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:30,205 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 10:28:30,209 - visualization.landscape_visualizer - INFO - 插值约束: 136 个点被约束到最小值 1265.00
2025-08-05 10:28:30,210 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.2%, 梯度: 44.72 → 41.52
2025-08-05 10:28:30,315 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_9_20250805_102830.html
2025-08-05 10:28:30,366 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_9_20250805_102830.html
2025-08-05 10:28:30,367 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 9
2025-08-05 10:28:30,367 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:30,367 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1732秒
2025-08-05 10:28:30,367 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 29.528571428571418, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 77176.36346938777, 'cluster_count': 0}, 'population_state': {'diversity': 0.6381475667189953, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0098, 'fitness_entropy': 0.9546444483376649, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.010)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 29.529)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360910.2044034, 'performance_metrics': {}}}
2025-08-05 10:28:30,367 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:30,367 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:30,367 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:30,368 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:30,368 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:30,368 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:30,368 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:30,368 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,368 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:30,368 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-05 10:28:30,368 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,368 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:30,369 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:30,369 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:30,369 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:30,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,369 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,370 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1809.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,370 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 4, 7, 5, 1, 2, 9, 8, 6, 0], 'cur_cost': 1809.0, 'intermediate_solutions': [{'tour': [6, 8, 4, 1, 9, 3, 2, 5, 0, 7], 'cur_cost': 1956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 5, 1, 9, 3, 4, 2, 0, 7], 'cur_cost': 2002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 5, 1, 9, 2, 4, 0, 7], 'cur_cost': 2105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,370 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1809.00)
2025-08-05 10:28:30,370 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:30,370 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,370 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,371 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 2205.0
2025-08-05 10:28:30,376 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,377 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,377 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,378 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,378 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 7, 5, 1, 2, 9, 8, 6, 0], 'cur_cost': 1809.0}, {'tour': array([5, 7, 3, 8, 2, 1, 6, 9, 4, 0], dtype=int64), 'cur_cost': 2205.0}, {'tour': [5, 7, 1, 8, 4, 0, 2, 3, 9, 6], 'cur_cost': 2001.0}, {'tour': [6, 3, 1, 0, 8, 4, 2, 5, 7, 9], 'cur_cost': 1915.0}, {'tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0}, {'tour': [2, 8, 0, 1, 9, 3, 5, 7, 6, 4], 'cur_cost': 1962.0}, {'tour': [0, 5, 7, 6, 9, 4, 1, 8, 3, 2], 'cur_cost': 2275.0}, {'tour': [7, 3, 0, 5, 2, 1, 4, 8, 9, 6], 'cur_cost': 2047.0}, {'tour': [2, 9, 0, 3, 4, 7, 8, 6, 5, 1], 'cur_cost': 1971.0}, {'tour': [2, 6, 0, 9, 4, 5, 3, 1, 8, 7], 'cur_cost': 2314.0}]
2025-08-05 10:28:30,379 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,379 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 106, 'cache_hit_rate': 0.0, 'cache_size': 106}}
2025-08-05 10:28:30,379 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([5, 7, 3, 8, 2, 1, 6, 9, 4, 0], dtype=int64), 'cur_cost': 2205.0, 'intermediate_solutions': [{'tour': array([6, 5, 8, 0, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 5, 8, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2304.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 6, 5, 8, 2, 7, 1, 3, 9]), 'cur_cost': 2217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 6, 5, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 0, 6, 5, 2, 7, 1, 3, 9]), 'cur_cost': 1928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,379 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 2205.00)
2025-08-05 10:28:30,379 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:30,380 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:30,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,380 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,380 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,381 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2154.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,381 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 7, 4, 5, 8, 6, 9, 3, 2, 0], 'cur_cost': 2154.0, 'intermediate_solutions': [{'tour': [5, 6, 1, 8, 4, 0, 2, 3, 9, 7], 'cur_cost': 1973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 6, 9, 3, 2, 0, 4, 8], 'cur_cost': 2005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 8, 4, 5, 0, 2, 3, 9, 6], 'cur_cost': 2244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,382 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 2154.00)
2025-08-05 10:28:30,382 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:30,382 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:30,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,382 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1543.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,383 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0, 'intermediate_solutions': [{'tour': [6, 3, 1, 2, 8, 4, 0, 5, 7, 9], 'cur_cost': 1819.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 1, 3, 6, 2, 5, 7, 9], 'cur_cost': 2014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 1, 0, 8, 4, 2, 7, 5, 9], 'cur_cost': 2056.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,383 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1543.00)
2025-08-05 10:28:30,383 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:30,383 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:30,384 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,384 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1728.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,385 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0, 'intermediate_solutions': [{'tour': [4, 9, 5, 3, 7, 8, 6, 2, 0, 1], 'cur_cost': 2006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 6, 8, 7, 3, 5, 0, 2, 1], 'cur_cost': 1935.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,385 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1728.00)
2025-08-05 10:28:30,385 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:30,385 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:30,385 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,385 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,386 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1857.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,386 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 1, 9, 3, 5, 7, 6, 2], 'cur_cost': 1986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 6, 7, 5, 3, 9, 1, 4], 'cur_cost': 2050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 1, 9, 6, 3, 5, 7, 4], 'cur_cost': 2228.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,386 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1857.00)
2025-08-05 10:28:30,386 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:30,387 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,387 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,387 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 2147.0
2025-08-05 10:28:30,392 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,393 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,393 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,394 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,394 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 7, 5, 1, 2, 9, 8, 6, 0], 'cur_cost': 1809.0}, {'tour': array([5, 7, 3, 8, 2, 1, 6, 9, 4, 0], dtype=int64), 'cur_cost': 2205.0}, {'tour': [1, 7, 4, 5, 8, 6, 9, 3, 2, 0], 'cur_cost': 2154.0}, {'tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0}, {'tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0}, {'tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0}, {'tour': array([6, 7, 2, 9, 4, 0, 1, 3, 8, 5], dtype=int64), 'cur_cost': 2147.0}, {'tour': [7, 3, 0, 5, 2, 1, 4, 8, 9, 6], 'cur_cost': 2047.0}, {'tour': [2, 9, 0, 3, 4, 7, 8, 6, 5, 1], 'cur_cost': 1971.0}, {'tour': [2, 6, 0, 9, 4, 5, 3, 1, 8, 7], 'cur_cost': 2314.0}]
2025-08-05 10:28:30,394 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,394 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 114, 'cache_hit_rate': 0.0, 'cache_size': 114}}
2025-08-05 10:28:30,395 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([6, 7, 2, 9, 4, 0, 1, 3, 8, 5], dtype=int64), 'cur_cost': 2147.0, 'intermediate_solutions': [{'tour': array([7, 5, 0, 6, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 5, 0, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 7, 5, 0, 4, 1, 8, 3, 2]), 'cur_cost': 2087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 7, 5, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 6, 7, 5, 4, 1, 8, 3, 2]), 'cur_cost': 2301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,395 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 2147.00)
2025-08-05 10:28:30,395 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:30,395 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:30,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2298.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,396 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [3, 9, 1, 2, 7, 0, 6, 4, 5, 8], 'cur_cost': 2298.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 0, 2, 1, 4, 8, 9, 6], 'cur_cost': 1963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 4, 1, 2, 5, 0, 3, 6], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 0, 2, 1, 4, 8, 9, 6], 'cur_cost': 1963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,397 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 2298.00)
2025-08-05 10:28:30,397 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:30,397 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:30,397 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,397 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,398 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,398 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1842.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,398 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0, 'intermediate_solutions': [{'tour': [2, 9, 0, 6, 4, 7, 8, 3, 5, 1], 'cur_cost': 1835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 0, 3, 4, 6, 8, 7, 5, 1], 'cur_cost': 1741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 7, 0, 3, 4, 8, 6, 5, 1], 'cur_cost': 1866.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,398 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1842.00)
2025-08-05 10:28:30,399 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:30,399 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,399 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,399 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1891.0
2025-08-05 10:28:30,407 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,407 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,407 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,408 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,408 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 7, 5, 1, 2, 9, 8, 6, 0], 'cur_cost': 1809.0}, {'tour': array([5, 7, 3, 8, 2, 1, 6, 9, 4, 0], dtype=int64), 'cur_cost': 2205.0}, {'tour': [1, 7, 4, 5, 8, 6, 9, 3, 2, 0], 'cur_cost': 2154.0}, {'tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0}, {'tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0}, {'tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0}, {'tour': array([6, 7, 2, 9, 4, 0, 1, 3, 8, 5], dtype=int64), 'cur_cost': 2147.0}, {'tour': [3, 9, 1, 2, 7, 0, 6, 4, 5, 8], 'cur_cost': 2298.0}, {'tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0}, {'tour': array([9, 7, 6, 2, 5, 1, 4, 8, 0, 3], dtype=int64), 'cur_cost': 1891.0}]
2025-08-05 10:28:30,408 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,408 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 123, 'cache_hit_rate': 0.0, 'cache_size': 123}}
2025-08-05 10:28:30,409 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([9, 7, 6, 2, 5, 1, 4, 8, 0, 3], dtype=int64), 'cur_cost': 1891.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 9, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 0, 6, 2, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 9, 0, 6, 2, 5, 3, 1, 8, 7]), 'cur_cost': 2157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 9, 0, 6, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 9, 0, 6, 5, 3, 1, 8, 7]), 'cur_cost': 2244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,409 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1891.00)
2025-08-05 10:28:30,409 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:30,409 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:30,410 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 7, 5, 1, 2, 9, 8, 6, 0], 'cur_cost': 1809.0, 'intermediate_solutions': [{'tour': [6, 8, 4, 1, 9, 3, 2, 5, 0, 7], 'cur_cost': 1956.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 8, 5, 1, 9, 3, 4, 2, 0, 7], 'cur_cost': 2002.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 8, 5, 1, 9, 2, 4, 0, 7], 'cur_cost': 2105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([5, 7, 3, 8, 2, 1, 6, 9, 4, 0], dtype=int64), 'cur_cost': 2205.0, 'intermediate_solutions': [{'tour': array([6, 5, 8, 0, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 6, 5, 8, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2304.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 0, 6, 5, 8, 2, 7, 1, 3, 9]), 'cur_cost': 2217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 6, 5, 4, 2, 7, 1, 3, 9]), 'cur_cost': 2297.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 4, 0, 6, 5, 2, 7, 1, 3, 9]), 'cur_cost': 1928.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 4, 5, 8, 6, 9, 3, 2, 0], 'cur_cost': 2154.0, 'intermediate_solutions': [{'tour': [5, 6, 1, 8, 4, 0, 2, 3, 9, 7], 'cur_cost': 1973.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 1, 6, 9, 3, 2, 0, 4, 8], 'cur_cost': 2005.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 8, 4, 5, 0, 2, 3, 9, 6], 'cur_cost': 2244.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0, 'intermediate_solutions': [{'tour': [6, 3, 1, 2, 8, 4, 0, 5, 7, 9], 'cur_cost': 1819.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 0, 1, 3, 6, 2, 5, 7, 9], 'cur_cost': 2014.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 3, 1, 0, 8, 4, 2, 7, 5, 9], 'cur_cost': 2056.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0, 'intermediate_solutions': [{'tour': [4, 9, 5, 3, 7, 8, 6, 2, 0, 1], 'cur_cost': 2006.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 6, 8, 7, 3, 5, 0, 2, 1], 'cur_cost': 1935.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 5, 3, 7, 8, 6, 0, 2, 1], 'cur_cost': 1785.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0, 'intermediate_solutions': [{'tour': [4, 8, 0, 1, 9, 3, 5, 7, 6, 2], 'cur_cost': 1986.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 6, 7, 5, 3, 9, 1, 4], 'cur_cost': 2050.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 1, 9, 6, 3, 5, 7, 4], 'cur_cost': 2228.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 2, 9, 4, 0, 1, 3, 8, 5], dtype=int64), 'cur_cost': 2147.0, 'intermediate_solutions': [{'tour': array([7, 5, 0, 6, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 5, 0, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 7, 5, 0, 4, 1, 8, 3, 2]), 'cur_cost': 2087.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 7, 5, 9, 4, 1, 8, 3, 2]), 'cur_cost': 2125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 6, 7, 5, 4, 1, 8, 3, 2]), 'cur_cost': 2301.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 1, 2, 7, 0, 6, 4, 5, 8], 'cur_cost': 2298.0, 'intermediate_solutions': [{'tour': [7, 3, 5, 0, 2, 1, 4, 8, 9, 6], 'cur_cost': 1963.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 4, 1, 2, 5, 0, 3, 6], 'cur_cost': 2021.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 5, 0, 2, 1, 4, 8, 9, 6], 'cur_cost': 1963.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0, 'intermediate_solutions': [{'tour': [2, 9, 0, 6, 4, 7, 8, 3, 5, 1], 'cur_cost': 1835.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 0, 3, 4, 6, 8, 7, 5, 1], 'cur_cost': 1741.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 7, 0, 3, 4, 8, 6, 5, 1], 'cur_cost': 1866.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 7, 6, 2, 5, 1, 4, 8, 0, 3], dtype=int64), 'cur_cost': 1891.0, 'intermediate_solutions': [{'tour': array([0, 6, 2, 9, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2282.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 0, 6, 2, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2158.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 9, 0, 6, 2, 5, 3, 1, 8, 7]), 'cur_cost': 2157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 9, 0, 6, 4, 5, 3, 1, 8, 7]), 'cur_cost': 2141.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 4, 9, 0, 6, 5, 3, 1, 8, 7]), 'cur_cost': 2244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:30,410 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:30,411 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,412 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1543.000, 多样性=0.893
2025-08-05 10:28:30,412 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:30,412 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:30,412 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:30,412 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04448640679610708, 'best_improvement': 0.0955451348182884}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.017114914425427976}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01962634119194015, 'recent_improvements': [-0.05268246761380607, -0.05435396091446682, -0.013429785229925767], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:30,412 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:30,412 - __main__ - INFO - simple2_10 开始进化第 5 代
2025-08-05 10:28:30,412 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:30,413 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,413 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1543.000, 多样性=0.893
2025-08-05 10:28:30,413 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:30,414 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.893
2025-08-05 10:28:30,414 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:30,414 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 10:28:30,416 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:30,416 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:30,416 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:30,416 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:30,424 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 20.886, 聚类评分: 0.000, 覆盖率: 0.011, 收敛趋势: 0.000, 多样性: 0.635
2025-08-05 10:28:30,425 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:30,425 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:30,425 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple2_10
2025-08-05 10:28:30,429 - visualization.landscape_visualizer - INFO - 插值约束: 47 个点被约束到最小值 1265.00
2025-08-05 10:28:30,431 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.4%, 梯度: 35.89 → 32.89
2025-08-05 10:28:30,585 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\landscape_simple2_10_iter_10_20250805_102830.html
2025-08-05 10:28:30,634 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple2_10\dashboard_simple2_10_iter_10_20250805_102830.html
2025-08-05 10:28:30,634 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 10
2025-08-05 10:28:30,634 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:30,634 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2179秒
2025-08-05 10:28:30,635 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 20.88571428571428, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 50866.72979591837, 'cluster_count': 0}, 'population_state': {'diversity': 0.6350078492935636, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0108, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.011)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 20.886)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360910.4256113, 'performance_metrics': {}}}
2025-08-05 10:28:30,635 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:30,635 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:30,635 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:30,635 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:30,635 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:30,636 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:30,636 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:30,636 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:30,636 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:30,636 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:30,636 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,637 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,638 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1869.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,638 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 8, 1, 4, 0, 6, 7, 9, 3, 5], 'cur_cost': 1869.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 4, 1, 2, 9, 8, 6, 0], 'cur_cost': 1862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 5, 1, 2, 9, 0, 6, 8], 'cur_cost': 1904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 1, 5, 2, 9, 8, 6, 0], 'cur_cost': 1980.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,638 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1869.00)
2025-08-05 10:28:30,638 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:30,638 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,638 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,638 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1721.0
2025-08-05 10:28:30,644 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,644 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,644 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,645 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,645 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 1, 4, 0, 6, 7, 9, 3, 5], 'cur_cost': 1869.0}, {'tour': array([9, 4, 0, 6, 8, 3, 2, 7, 5, 1], dtype=int64), 'cur_cost': 1721.0}, {'tour': [1, 7, 4, 5, 8, 6, 9, 3, 2, 0], 'cur_cost': 2154.0}, {'tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0}, {'tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0}, {'tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0}, {'tour': [6, 7, 2, 9, 4, 0, 1, 3, 8, 5], 'cur_cost': 2147.0}, {'tour': [3, 9, 1, 2, 7, 0, 6, 4, 5, 8], 'cur_cost': 2298.0}, {'tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0}, {'tour': [9, 7, 6, 2, 5, 1, 4, 8, 0, 3], 'cur_cost': 1891.0}]
2025-08-05 10:28:30,645 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,646 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 133, 'cache_hit_rate': 0.0, 'cache_size': 133}}
2025-08-05 10:28:30,646 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([9, 4, 0, 6, 8, 3, 2, 7, 5, 1], dtype=int64), 'cur_cost': 1721.0, 'intermediate_solutions': [{'tour': array([3, 7, 5, 8, 2, 1, 6, 9, 4, 0]), 'cur_cost': 2165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 7, 5, 2, 1, 6, 9, 4, 0]), 'cur_cost': 1993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 3, 7, 5, 1, 6, 9, 4, 0]), 'cur_cost': 2086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 7, 2, 1, 6, 9, 4, 0]), 'cur_cost': 2311.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 8, 3, 7, 1, 6, 9, 4, 0]), 'cur_cost': 2376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,646 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1721.00)
2025-08-05 10:28:30,646 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:30,646 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,646 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,647 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 2307.0
2025-08-05 10:28:30,652 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,652 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,652 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,653 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,653 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 1, 4, 0, 6, 7, 9, 3, 5], 'cur_cost': 1869.0}, {'tour': array([9, 4, 0, 6, 8, 3, 2, 7, 5, 1], dtype=int64), 'cur_cost': 1721.0}, {'tour': array([7, 4, 8, 1, 2, 5, 0, 9, 6, 3], dtype=int64), 'cur_cost': 2307.0}, {'tour': [4, 6, 7, 9, 5, 3, 2, 1, 0, 8], 'cur_cost': 1543.0}, {'tour': [2, 6, 4, 0, 8, 9, 3, 1, 5, 7], 'cur_cost': 1728.0}, {'tour': [6, 4, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1857.0}, {'tour': [6, 7, 2, 9, 4, 0, 1, 3, 8, 5], 'cur_cost': 2147.0}, {'tour': [3, 9, 1, 2, 7, 0, 6, 4, 5, 8], 'cur_cost': 2298.0}, {'tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0}, {'tour': [9, 7, 6, 2, 5, 1, 4, 8, 0, 3], 'cur_cost': 1891.0}]
2025-08-05 10:28:30,654 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,654 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 144, 'cache_hit_rate': 0.0, 'cache_size': 144}}
2025-08-05 10:28:30,654 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([7, 4, 8, 1, 2, 5, 0, 9, 6, 3], dtype=int64), 'cur_cost': 2307.0, 'intermediate_solutions': [{'tour': array([4, 7, 1, 5, 8, 6, 9, 3, 2, 0]), 'cur_cost': 1749.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 7, 1, 8, 6, 9, 3, 2, 0]), 'cur_cost': 2260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 4, 7, 1, 6, 9, 3, 2, 0]), 'cur_cost': 2390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 4, 7, 8, 6, 9, 3, 2, 0]), 'cur_cost': 1829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 4, 7, 6, 9, 3, 2, 0]), 'cur_cost': 2269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,654 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 2307.00)
2025-08-05 10:28:30,654 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:30,654 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,655 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2213.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,656 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 6, 4, 5, 8, 9, 7, 2, 1, 0], 'cur_cost': 2213.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 9, 4, 3, 2, 1, 0, 8], 'cur_cost': 2034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 7, 9, 5, 0, 1, 2, 3, 8], 'cur_cost': 1790.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,656 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 2213.00)
2025-08-05 10:28:30,656 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:30,656 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:30,656 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,656 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1798.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,657 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 8, 7, 6, 0, 2, 3, 1, 5, 4], 'cur_cost': 1798.0, 'intermediate_solutions': [{'tour': [7, 6, 4, 0, 8, 9, 3, 1, 5, 2], 'cur_cost': 1580.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 4, 0, 8, 9, 3, 7, 5, 1], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 4, 0, 8, 1, 9, 3, 5, 7], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,657 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 1798.00)
2025-08-05 10:28:30,657 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:30,657 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,658 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1673.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,659 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 9, 0, 7, 8, 6, 4, 2, 3, 1], 'cur_cost': 1673.0, 'intermediate_solutions': [{'tour': [6, 4, 3, 7, 8, 9, 1, 2, 5, 0], 'cur_cost': 2009.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 4, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 2041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,659 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1673.00)
2025-08-05 10:28:30,659 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:30,659 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:30,659 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,659 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,660 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1898.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,660 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 7, 2, 4, 3, 9, 8, 6, 0, 1], 'cur_cost': 1898.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 3, 4, 0, 1, 9, 8, 5], 'cur_cost': 2100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 9, 4, 0, 1, 3, 5, 8], 'cur_cost': 1774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 2, 9, 0, 1, 4, 3, 8, 5], 'cur_cost': 2527.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,660 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1898.00)
2025-08-05 10:28:30,660 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:30,660 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:30,660 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:30,661 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 2243.0
2025-08-05 10:28:30,666 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:30,666 - ExploitationExpert - INFO - res_population_costs: [1265.0, 1265, 1265, 1265]
2025-08-05 10:28:30,666 - ExploitationExpert - INFO - res_populations: [array([0, 2, 1, 5, 3, 9, 7, 6, 8, 4], dtype=int64), array([0, 4, 6, 8, 7, 9, 3, 5, 1, 2], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 8, 6, 4], dtype=int64), array([0, 2, 3, 1, 5, 9, 7, 6, 8, 4], dtype=int64)]
2025-08-05 10:28:30,667 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:30,667 - ExploitationExpert - INFO - populations: [{'tour': [2, 8, 1, 4, 0, 6, 7, 9, 3, 5], 'cur_cost': 1869.0}, {'tour': array([9, 4, 0, 6, 8, 3, 2, 7, 5, 1], dtype=int64), 'cur_cost': 1721.0}, {'tour': array([7, 4, 8, 1, 2, 5, 0, 9, 6, 3], dtype=int64), 'cur_cost': 2307.0}, {'tour': [3, 6, 4, 5, 8, 9, 7, 2, 1, 0], 'cur_cost': 2213.0}, {'tour': [9, 8, 7, 6, 0, 2, 3, 1, 5, 4], 'cur_cost': 1798.0}, {'tour': [5, 9, 0, 7, 8, 6, 4, 2, 3, 1], 'cur_cost': 1673.0}, {'tour': [5, 7, 2, 4, 3, 9, 8, 6, 0, 1], 'cur_cost': 1898.0}, {'tour': array([3, 6, 2, 7, 1, 5, 9, 4, 8, 0], dtype=int64), 'cur_cost': 2243.0}, {'tour': [1, 9, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 1842.0}, {'tour': [9, 7, 6, 2, 5, 1, 4, 8, 0, 3], 'cur_cost': 1891.0}]
2025-08-05 10:28:30,668 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:30,668 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 156, 'cache_hit_rate': 0.0, 'cache_size': 156}}
2025-08-05 10:28:30,668 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([3, 6, 2, 7, 1, 5, 9, 4, 8, 0], dtype=int64), 'cur_cost': 2243.0, 'intermediate_solutions': [{'tour': array([1, 9, 3, 2, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2361.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 9, 3, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 9, 3, 0, 6, 4, 5, 8]), 'cur_cost': 2145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 9, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 2, 1, 9, 0, 6, 4, 5, 8]), 'cur_cost': 2327.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:30,668 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 2243.00)
2025-08-05 10:28:30,668 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:30,668 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,669 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,670 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2057.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,670 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 7, 2, 9, 4, 8, 6, 3, 1, 5], 'cur_cost': 2057.0, 'intermediate_solutions': [{'tour': [1, 9, 8, 7, 3, 2, 4, 6, 5, 0], 'cur_cost': 2163.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 5, 2, 3, 7, 8, 9, 1, 0], 'cur_cost': 1842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 1, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 2003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,670 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2057.00)
2025-08-05 10:28:30,670 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:30,670 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:30,670 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:30,670 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:30,670 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,671 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:30,671 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1801.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:30,671 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [5, 6, 8, 3, 4, 0, 2, 1, 9, 7], 'cur_cost': 1801.0, 'intermediate_solutions': [{'tour': [9, 7, 6, 8, 5, 1, 4, 2, 0, 3], 'cur_cost': 1866.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 6, 2, 5, 1, 4, 8, 3, 0], 'cur_cost': 2046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 2, 5, 1, 4, 8, 0, 3, 9], 'cur_cost': 1891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:30,671 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1801.00)
2025-08-05 10:28:30,671 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:30,672 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:30,673 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 1, 4, 0, 6, 7, 9, 3, 5], 'cur_cost': 1869.0, 'intermediate_solutions': [{'tour': [3, 5, 7, 4, 1, 2, 9, 8, 6, 0], 'cur_cost': 1862.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 4, 7, 5, 1, 2, 9, 0, 6, 8], 'cur_cost': 1904.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 4, 7, 1, 5, 2, 9, 8, 6, 0], 'cur_cost': 1980.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 4, 0, 6, 8, 3, 2, 7, 5, 1], dtype=int64), 'cur_cost': 1721.0, 'intermediate_solutions': [{'tour': array([3, 7, 5, 8, 2, 1, 6, 9, 4, 0]), 'cur_cost': 2165.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 3, 7, 5, 2, 1, 6, 9, 4, 0]), 'cur_cost': 1993.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([2, 8, 3, 7, 5, 1, 6, 9, 4, 0]), 'cur_cost': 2086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 8, 3, 7, 2, 1, 6, 9, 4, 0]), 'cur_cost': 2311.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 2, 8, 3, 7, 1, 6, 9, 4, 0]), 'cur_cost': 2376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 8, 1, 2, 5, 0, 9, 6, 3], dtype=int64), 'cur_cost': 2307.0, 'intermediate_solutions': [{'tour': array([4, 7, 1, 5, 8, 6, 9, 3, 2, 0]), 'cur_cost': 1749.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 4, 7, 1, 8, 6, 9, 3, 2, 0]), 'cur_cost': 2260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 5, 4, 7, 1, 6, 9, 3, 2, 0]), 'cur_cost': 2390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 5, 4, 7, 8, 6, 9, 3, 2, 0]), 'cur_cost': 1829.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 8, 5, 4, 7, 6, 9, 3, 2, 0]), 'cur_cost': 2269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 5, 8, 9, 7, 2, 1, 0], 'cur_cost': 2213.0, 'intermediate_solutions': [{'tour': [5, 6, 7, 9, 4, 3, 2, 1, 0, 8], 'cur_cost': 2034.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 7, 9, 5, 0, 1, 2, 3, 8], 'cur_cost': 1790.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 9, 5, 3, 2, 1, 0, 8, 7], 'cur_cost': 1727.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 8, 7, 6, 0, 2, 3, 1, 5, 4], 'cur_cost': 1798.0, 'intermediate_solutions': [{'tour': [7, 6, 4, 0, 8, 9, 3, 1, 5, 2], 'cur_cost': 1580.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 4, 0, 8, 9, 3, 7, 5, 1], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 6, 4, 0, 8, 1, 9, 3, 5, 7], 'cur_cost': 2001.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 9, 0, 7, 8, 6, 4, 2, 3, 1], 'cur_cost': 1673.0, 'intermediate_solutions': [{'tour': [6, 4, 3, 7, 8, 9, 1, 2, 5, 0], 'cur_cost': 2009.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 4, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 2041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 9, 7, 8, 3, 1, 2, 5, 0], 'cur_cost': 1671.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 2, 4, 3, 9, 8, 6, 0, 1], 'cur_cost': 1898.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 3, 4, 0, 1, 9, 8, 5], 'cur_cost': 2100.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 2, 9, 4, 0, 1, 3, 5, 8], 'cur_cost': 1774.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 2, 9, 0, 1, 4, 3, 8, 5], 'cur_cost': 2527.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 2, 7, 1, 5, 9, 4, 8, 0], dtype=int64), 'cur_cost': 2243.0, 'intermediate_solutions': [{'tour': array([1, 9, 3, 2, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2361.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 1, 9, 3, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2267.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 2, 1, 9, 3, 0, 6, 4, 5, 8]), 'cur_cost': 2145.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([3, 2, 1, 9, 7, 0, 6, 4, 5, 8]), 'cur_cost': 2096.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([3, 7, 2, 1, 9, 0, 6, 4, 5, 8]), 'cur_cost': 2327.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 2, 9, 4, 8, 6, 3, 1, 5], 'cur_cost': 2057.0, 'intermediate_solutions': [{'tour': [1, 9, 8, 7, 3, 2, 4, 6, 5, 0], 'cur_cost': 2163.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 5, 2, 3, 7, 8, 9, 1, 0], 'cur_cost': 1842.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 1, 8, 7, 3, 2, 5, 6, 4, 0], 'cur_cost': 2003.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 8, 3, 4, 0, 2, 1, 9, 7], 'cur_cost': 1801.0, 'intermediate_solutions': [{'tour': [9, 7, 6, 8, 5, 1, 4, 2, 0, 3], 'cur_cost': 1866.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 7, 6, 2, 5, 1, 4, 8, 3, 0], 'cur_cost': 2046.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 2, 5, 1, 4, 8, 0, 3, 9], 'cur_cost': 1891.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:30,673 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:30,673 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:30,674 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1673.000, 多样性=0.911
2025-08-05 10:28:30,674 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:30,674 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:30,674 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:30,675 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03120368742011402, 'best_improvement': -0.08425145819831498}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.019900497512437935}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.04942018385528695, 'recent_improvements': [-0.05435396091446682, -0.013429785229925767, 0.04448640679610708], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 1265.0, 'new_best_cost': 1265.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:30,675 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:30,677 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple2_10_solution.json
2025-08-05 10:28:30,677 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple2_10_20250805_102830.solution
2025-08-05 10:28:30,677 - __main__ - INFO - 实例执行完成 - 运行时间: 1.29s, 最佳成本: 1265.0
2025-08-05 10:28:30,677 - __main__ - INFO - 实例 simple2_10 处理完成
