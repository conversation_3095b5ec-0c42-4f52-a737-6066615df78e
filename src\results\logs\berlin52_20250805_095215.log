2025-08-05 09:52:15,766 - __main__ - INFO - berlin52 开始进化第 1 代
2025-08-05 09:52:15,767 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:15,768 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:15,771 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9123.000, 多样性=0.982
2025-08-05 09:52:15,774 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:15,777 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.982
2025-08-05 09:52:15,789 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:15,791 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:15,791 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:15,792 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:15,792 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:15,809 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -1619.920, 聚类评分: 0.000, 覆盖率: 0.152, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:15,809 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:15,809 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:15,809 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 09:52:15,814 - visualization.landscape_visualizer - INFO - 插值约束: 171 个点被约束到最小值 9123.00
2025-08-05 09:52:15,921 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_131_20250805_095215.html
2025-08-05 09:52:16,000 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_131_20250805_095215.html
2025-08-05 09:52:16,000 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 131
2025-08-05 09:52:16,001 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:16,001 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2107秒
2025-08-05 09:52:16,001 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 262, 'max_size': 500, 'hits': 0, 'misses': 262, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 862, 'misses': 468, 'hit_rate': 0.6481203007518797, 'evictions': 368, 'ttl': 7200}}
2025-08-05 09:52:16,001 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1619.9199999999998, 'local_optima_density': 0.1, 'gradient_variance': 119291426.4336, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1516, 'fitness_entropy': 0.8427376486136672, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1619.920)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.152)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358735.8091776, 'performance_metrics': {}}}
2025-08-05 09:52:16,002 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:16,002 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:16,002 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:16,002 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:16,004 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,005 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:16,005 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,005 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,005 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:16,006 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,006 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,006 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:16,006 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:16,006 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:16,007 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:16,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,010 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13082.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,011 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13082.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,012 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 13082.00)
2025-08-05 09:52:16,012 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:16,012 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:16,012 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,014 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,014 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,014 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9696.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,015 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9696.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,015 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 9696.00)
2025-08-05 09:52:16,015 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:16,015 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:16,016 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,017 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,018 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,018 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11563.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,018 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 11563.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,018 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 11563.00)
2025-08-05 09:52:16,018 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:16,019 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:16,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,024 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,025 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20345.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,025 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20345.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,025 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 20345.00)
2025-08-05 09:52:16,026 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:16,026 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:16,026 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,028 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9582.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,029 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9582.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,029 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9582.00)
2025-08-05 09:52:16,030 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:16,030 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,030 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,030 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 29975.0
2025-08-05 09:52:16,037 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 09:52:16,038 - ExploitationExpert - INFO - res_population_costs: [8006.0, 7769, 7715]
2025-08-05 09:52:16,038 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64)]
2025-08-05 09:52:16,040 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,040 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13082.0}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9696.0}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 11563.0}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20345.0}, {'tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9582.0}, {'tour': array([50, 45, 11, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48], dtype=int64), 'cur_cost': 29975.0}, {'tour': array([13,  4,  5, 36, 20, 28, 46, 43, 29, 27,  9, 39, 40, 18, 42, 37, 19,
       25, 23, 33, 32, 48, 22,  8, 16, 26, 31, 49, 10, 51,  1, 14, 35,  2,
        7, 24, 45, 47, 30,  6, 12, 21,  0, 15, 34, 11, 38,  3, 44, 50, 41,
       17], dtype=int64), 'cur_cost': 29673.0}, {'tour': array([16, 43,  4, 31,  5, 27,  3, 38, 46, 24, 42, 51, 50, 33, 47, 29,  8,
        0, 34, 19, 20, 39, 10, 23, 32, 18, 40, 12, 37, 22,  9, 41,  2,  1,
       11, 49, 36, 26, 21, 35, 30, 13, 44,  6, 25, 14, 28, 45,  7, 15, 17,
       48], dtype=int64), 'cur_cost': 30689.0}, {'tour': array([14,  4,  8, 42, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31813.0}, {'tour': array([21, 33,  4, 46, 25, 38,  1, 17, 26, 29,  8, 10, 13, 16,  2, 48, 15,
       19,  3, 18, 49, 20, 37,  0,  7, 14, 45, 32, 27, 28, 24, 39, 44, 40,
        9, 36, 22,  6, 30, 47, 43, 23, 51, 12, 42, 41, 35, 31, 50, 34, 11,
        5], dtype=int64), 'cur_cost': 26910.0}]
2025-08-05 09:52:16,043 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,044 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 339, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 339, 'cache_hits': 0, 'similarity_calculations': 1746, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,045 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([50, 45, 11, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48], dtype=int64), 'cur_cost': 29975.0, 'intermediate_solutions': [{'tour': array([46, 47, 49, 33, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 46, 47, 49, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 30955.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 33, 46, 47, 49, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31360.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 33, 46, 47, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 22, 33, 46, 47, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 30946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,045 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 29975.00)
2025-08-05 09:52:16,045 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:16,045 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:16,045 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,047 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29057.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,048 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29057.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,048 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 29057.00)
2025-08-05 09:52:16,048 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:16,048 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:16,048 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,050 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,050 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,050 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12460.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,050 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12460.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,051 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12460.00)
2025-08-05 09:52:16,051 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:16,051 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,051 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,051 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 30534.0
2025-08-05 09:52:16,060 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,060 - ExploitationExpert - INFO - res_population_costs: [8006.0, 7769, 7715, 7542]
2025-08-05 09:52:16,060 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-08-05 09:52:16,063 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,063 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13082.0}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9696.0}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 11563.0}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20345.0}, {'tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9582.0}, {'tour': array([50, 45, 11, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48], dtype=int64), 'cur_cost': 29975.0}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29057.0}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12460.0}, {'tour': array([48, 37, 16, 28, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41], dtype=int64), 'cur_cost': 30534.0}, {'tour': array([21, 33,  4, 46, 25, 38,  1, 17, 26, 29,  8, 10, 13, 16,  2, 48, 15,
       19,  3, 18, 49, 20, 37,  0,  7, 14, 45, 32, 27, 28, 24, 39, 44, 40,
        9, 36, 22,  6, 30, 47, 43, 23, 51, 12, 42, 41, 35, 31, 50, 34, 11,
        5], dtype=int64), 'cur_cost': 26910.0}]
2025-08-05 09:52:16,064 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,065 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 340, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 340, 'cache_hits': 0, 'similarity_calculations': 1747, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,066 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([48, 37, 16, 28, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41], dtype=int64), 'cur_cost': 30534.0, 'intermediate_solutions': [{'tour': array([ 8,  4, 14, 42, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42,  8,  4, 14, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31732.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 42,  8,  4, 14, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 42,  8,  4, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 22, 42,  8,  4, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 32299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,066 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 30534.00)
2025-08-05 09:52:16,066 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:16,066 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:16,066 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,071 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,071 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19041.0, 路径长度: 52, 收集中间解: 0
2025-08-05 09:52:16,071 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,072 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 19041.00)
2025-08-05 09:52:16,072 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:16,072 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:16,076 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13082.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9696.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 11563.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20345.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 9582.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 45, 11, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48], dtype=int64), 'cur_cost': 29975.0, 'intermediate_solutions': [{'tour': array([46, 47, 49, 33, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31029.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 46, 47, 49, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 30955.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 33, 46, 47, 49, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31360.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 33, 46, 47, 22, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 31235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49, 22, 33, 46, 47, 18, 31, 38, 10, 34, 27,  0, 30, 12,  5, 42,  1,
       37, 32, 23, 13, 16, 39,  4, 17,  6,  2,  8, 48,  7, 24, 20, 26, 11,
       43,  3, 19, 51, 35,  9, 15, 50, 25, 40, 29, 28, 21, 36, 14, 44, 41,
       45], dtype=int64), 'cur_cost': 30946.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29057.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12460.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 37, 16, 28, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41], dtype=int64), 'cur_cost': 30534.0, 'intermediate_solutions': [{'tour': array([ 8,  4, 14, 42, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31798.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42,  8,  4, 14, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31732.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 42,  8,  4, 14, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31693.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 42,  8,  4, 22, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 31826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14, 22, 42,  8,  4, 15, 46, 49, 10,  7, 28, 24, 48, 20, 32, 41, 51,
       21,  9, 50, 23,  6, 38,  1,  2,  0, 19, 37, 47, 16, 26, 18, 40, 33,
       44, 11, 39, 43, 35, 27, 25, 31,  5, 45, 29, 13, 30, 34, 12, 36,  3,
       17], dtype=int64), 'cur_cost': 32299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19041.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:16,076 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:16,076 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,080 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9582.000, 多样性=0.970
2025-08-05 09:52:16,081 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:16,081 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:16,081 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:16,081 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07031238523047638, 'best_improvement': -0.05031239723775074}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.012614180078294755}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.051985496449783265, 'recent_improvements': [-0.009206748973533362, -0.03509932635216165, -0.11317774187309988], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 7542, 'new_best_cost': 7542, 'quality_improvement': 0.0, 'old_diversity': 0.7243589743589743, 'new_diversity': 0.7243589743589743, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 09:52:16,081 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:16,081 - __main__ - INFO - berlin52 开始进化第 2 代
2025-08-05 09:52:16,082 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:16,082 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,083 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9582.000, 多样性=0.970
2025-08-05 09:52:16,083 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:16,085 - PathExpert - INFO - 路径结构分析完成: 公共边数量=8, 路径相似性=0.970
2025-08-05 09:52:16,085 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:16,087 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.724
2025-08-05 09:52:16,089 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:16,089 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:16,089 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:16,089 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:16,115 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -1748.029, 聚类评分: 0.000, 覆盖率: 0.153, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:16,115 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:16,115 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:16,116 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 09:52:16,123 - visualization.landscape_visualizer - INFO - 插值约束: 266 个点被约束到最小值 7542.00
2025-08-05 09:52:16,239 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_132_20250805_095216.html
2025-08-05 09:52:16,284 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_132_20250805_095216.html
2025-08-05 09:52:16,285 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 132
2025-08-05 09:52:16,285 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:16,285 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1964秒
2025-08-05 09:52:16,285 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -1748.0285714285712, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 42443693.60489795, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1529, 'fitness_entropy': 0.8932941324021467, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1748.029)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.153)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358736.1156757, 'performance_metrics': {}}}
2025-08-05 09:52:16,285 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:16,286 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:16,286 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:16,286 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:16,286 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,287 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:16,287 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,287 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,287 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:16,287 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,287 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,288 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:16,288 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:16,288 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:16,288 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:16,288 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,290 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,290 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,290 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,291 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9972.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,291 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9972.0, 'intermediate_solutions': [{'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 38, 23, 47, 37, 39, 36, 14, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13422.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 30, 22, 19, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13809.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 3, 13, 12, 26, 27, 50, 25, 46, 11, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13420.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,292 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 9972.00)
2025-08-05 09:52:16,292 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:16,292 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:16,292 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,293 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,293 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,294 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,294 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10036.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,294 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10036.0, 'intermediate_solutions': [{'tour': [0, 2, 18, 4, 14, 5, 23, 22, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 47, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10966.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 28, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 3, 42, 8, 9, 7, 40, 44, 31, 29, 20, 16, 41, 6, 1], 'cur_cost': 10281.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 40, 16, 41, 6, 1], 'cur_cost': 10368.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,294 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 10036.00)
2025-08-05 09:52:16,294 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:16,295 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:16,295 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,296 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20748.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,297 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20748.0, 'intermediate_solutions': [{'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 51, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 9, 10, 41, 6, 1], 'cur_cost': 15859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 25, 26, 27, 11, 50, 32, 42, 8, 9, 7, 40, 44, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 13217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 2, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 13452.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,297 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 20748.00)
2025-08-05 09:52:16,297 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:16,297 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:16,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,301 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21492.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,301 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 25, 46, 13, 26, 2, 8, 1], 'cur_cost': 21492.0, 'intermediate_solutions': [{'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 45, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 6, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 22258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 51, 10, 12, 13, 20, 1, 7, 9], 'cur_cost': 20402.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 16, 30, 29, 6, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,302 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 21492.00)
2025-08-05 09:52:16,302 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 09:52:16,302 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 09:52:16,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,303 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,303 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31098.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,304 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [50, 13, 38, 23, 48, 19, 37, 3, 12, 17, 43, 35, 18, 33, 29, 45, 15, 7, 0, 14, 21, 39, 24, 49, 30, 34, 51, 42, 2, 40, 44, 8, 27, 36, 32, 22, 11, 20, 10, 6, 46, 9, 47, 41, 31, 28, 16, 25, 26, 1, 5, 4], 'cur_cost': 31098.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 32, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 50, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10447.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 11, 27, 26, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 3, 24, 50, 10, 51, 13, 12, 46, 25, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 11, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 27, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10469.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,304 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 31098.00)
2025-08-05 09:52:16,304 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:16,304 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,305 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,305 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 32397.0
2025-08-05 09:52:16,311 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,311 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,311 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,313 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,313 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9972.0}, {'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10036.0}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20748.0}, {'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 25, 46, 13, 26, 2, 8, 1], 'cur_cost': 21492.0}, {'tour': [50, 13, 38, 23, 48, 19, 37, 3, 12, 17, 43, 35, 18, 33, 29, 45, 15, 7, 0, 14, 21, 39, 24, 49, 30, 34, 51, 42, 2, 40, 44, 8, 27, 36, 32, 22, 11, 20, 10, 6, 46, 9, 47, 41, 31, 28, 16, 25, 26, 1, 5, 4], 'cur_cost': 31098.0}, {'tour': array([34, 30, 10, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35], dtype=int64), 'cur_cost': 32397.0}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29057.0}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12460.0}, {'tour': [48, 37, 16, 28, 26, 3, 42, 39, 34, 18, 6, 24, 50, 12, 25, 35, 49, 7, 43, 22, 17, 29, 14, 0, 10, 20, 8, 47, 32, 4, 19, 11, 9, 36, 31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27, 2, 1, 5, 41], 'cur_cost': 30534.0}, {'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19041.0}]
2025-08-05 09:52:16,314 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,314 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 341, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 341, 'cache_hits': 0, 'similarity_calculations': 1749, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,315 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([34, 30, 10, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35], dtype=int64), 'cur_cost': 32397.0, 'intermediate_solutions': [{'tour': array([11, 45, 50, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 11, 45, 50, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 15, 11, 45, 50, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 29903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 15, 11, 45, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 19, 15, 11, 45, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30190.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,315 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 32397.00)
2025-08-05 09:52:16,315 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:16,315 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:16,315 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,319 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,320 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,320 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21948.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,320 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21948.0, 'intermediate_solutions': [{'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 1, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 36, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 27947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 3, 49, 15, 45, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 22, 43, 45, 5, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 28477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,320 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21948.00)
2025-08-05 09:52:16,321 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:16,321 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:16,321 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,322 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,323 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10286.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,323 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10286.0, 'intermediate_solutions': [{'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 51, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 16], 'cur_cost': 15356.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 22, 19, 49, 15, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 22, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 13934.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,324 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10286.00)
2025-08-05 09:52:16,324 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:16,324 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,324 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,324 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 32097.0
2025-08-05 09:52:16,330 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,331 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,331 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,332 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,332 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9972.0}, {'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10036.0}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20748.0}, {'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 25, 46, 13, 26, 2, 8, 1], 'cur_cost': 21492.0}, {'tour': [50, 13, 38, 23, 48, 19, 37, 3, 12, 17, 43, 35, 18, 33, 29, 45, 15, 7, 0, 14, 21, 39, 24, 49, 30, 34, 51, 42, 2, 40, 44, 8, 27, 36, 32, 22, 11, 20, 10, 6, 46, 9, 47, 41, 31, 28, 16, 25, 26, 1, 5, 4], 'cur_cost': 31098.0}, {'tour': array([34, 30, 10, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35], dtype=int64), 'cur_cost': 32397.0}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21948.0}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10286.0}, {'tour': array([16,  6, 14, 17,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42], dtype=int64), 'cur_cost': 32097.0}, {'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19041.0}]
2025-08-05 09:52:16,333 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,334 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 342, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 342, 'cache_hits': 0, 'similarity_calculations': 1752, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,334 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([16,  6, 14, 17,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42], dtype=int64), 'cur_cost': 32097.0, 'intermediate_solutions': [{'tour': array([16, 37, 48, 28, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 16, 37, 48, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 28, 16, 37, 48,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 28, 16, 37, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30737.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 26, 28, 16, 37,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30750.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,335 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 32097.00)
2025-08-05 09:52:16,335 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:16,335 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:16,335 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,336 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,337 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,338 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12103.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,339 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0, 'intermediate_solutions': [{'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 46, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 35, 12, 13, 29], 'cur_cost': 20525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 5, 1, 41, 6, 17, 4, 25, 26, 32, 3, 37, 2, 33, 43, 7, 14, 8, 19, 46, 12, 13, 29], 'cur_cost': 18992.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 36, 24, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 50, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19981.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,339 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12103.00)
2025-08-05 09:52:16,339 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:16,339 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:16,341 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 9972.0, 'intermediate_solutions': [{'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 38, 23, 47, 37, 39, 36, 14, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13422.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 3, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 30, 22, 19, 49, 15, 45, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13809.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 3, 13, 12, 26, 27, 50, 25, 46, 11, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 13420.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10036.0, 'intermediate_solutions': [{'tour': [0, 2, 18, 4, 14, 5, 23, 22, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 47, 30, 17, 21, 48, 31, 44, 40, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10966.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 28, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 3, 42, 8, 9, 7, 40, 44, 31, 29, 20, 16, 41, 6, 1], 'cur_cost': 10281.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 18, 4, 14, 5, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 7, 9, 8, 42, 3, 24, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 28, 29, 20, 40, 16, 41, 6, 1], 'cur_cost': 10368.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20748.0, 'intermediate_solutions': [{'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 44, 40, 7, 51, 8, 42, 32, 50, 11, 27, 26, 25, 46, 12, 9, 10, 41, 6, 1], 'cur_cost': 15859.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 2, 18, 25, 26, 27, 11, 50, 32, 42, 8, 9, 7, 40, 44, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 13217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 14, 15, 49, 19, 22, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 5, 3, 24, 45, 43, 28, 29, 20, 16, 18, 44, 40, 7, 9, 8, 42, 32, 50, 11, 27, 26, 25, 2, 46, 12, 51, 10, 41, 6, 1], 'cur_cost': 13452.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 25, 46, 13, 26, 2, 8, 1], 'cur_cost': 21492.0, 'intermediate_solutions': [{'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 45, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 6, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 22258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 30, 29, 6, 16, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 51, 10, 12, 13, 20, 1, 7, 9], 'cur_cost': 20402.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 34, 43, 21, 33, 31, 19, 48, 18, 16, 30, 29, 6, 22, 14, 23, 11, 25, 47, 3, 50, 4, 45, 27, 36, 37, 35, 15, 0, 38, 24, 5, 40, 17, 28, 49, 42, 2, 44, 41, 8, 39, 26, 46, 10, 51, 12, 13, 20, 1, 7, 9], 'cur_cost': 20352.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [50, 13, 38, 23, 48, 19, 37, 3, 12, 17, 43, 35, 18, 33, 29, 45, 15, 7, 0, 14, 21, 39, 24, 49, 30, 34, 51, 42, 2, 40, 44, 8, 27, 36, 32, 22, 11, 20, 10, 6, 46, 9, 47, 41, 31, 28, 16, 25, 26, 1, 5, 4], 'cur_cost': 31098.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 11, 27, 26, 25, 46, 12, 13, 51, 10, 32, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 50, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10447.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 11, 27, 26, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 3, 24, 50, 10, 51, 13, 12, 46, 25, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 11, 26, 25, 46, 12, 13, 51, 10, 50, 24, 3, 5, 27, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 41, 6, 1, 29, 28], 'cur_cost': 10469.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 30, 10, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35], dtype=int64), 'cur_cost': 32397.0, 'intermediate_solutions': [{'tour': array([11, 45, 50, 15, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30022.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 11, 45, 50, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30209.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([19, 15, 11, 45, 50, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 29903.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 15, 11, 45, 19, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30251.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 19, 15, 11, 45, 34, 22, 26, 40, 33, 23,  5,  4, 36,  2, 14, 20,
        3,  6,  1, 21,  0, 18, 37, 16, 24, 51, 30,  8, 25, 27,  7, 41, 28,
       35, 12, 29,  9, 10, 32, 39, 17, 43, 47, 31, 49, 38, 44, 13, 42, 46,
       48]), 'cur_cost': 30190.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21948.0, 'intermediate_solutions': [{'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 1, 6, 5, 22, 43, 45, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 36, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 27947.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 5, 22, 43, 3, 49, 15, 45, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 29189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 18, 4, 14, 31, 23, 25, 37, 21, 36, 6, 22, 43, 45, 5, 15, 49, 3, 38, 30, 34, 33, 24, 44, 40, 19, 9, 8, 17, 48, 28, 27, 2, 7, 12, 51, 32, 46, 16, 20, 1, 10, 47, 0, 26, 39, 29, 50, 41, 35, 11, 42], 'cur_cost': 28477.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10286.0, 'intermediate_solutions': [{'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 21, 2, 51, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 16], 'cur_cost': 15356.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 22, 19, 49, 15, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 12769.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 10, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 30, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 22, 50, 32, 42, 9, 8, 7, 40, 41, 6, 1, 12, 13, 51], 'cur_cost': 13934.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([16,  6, 14, 17,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42], dtype=int64), 'cur_cost': 32097.0, 'intermediate_solutions': [{'tour': array([16, 37, 48, 28, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 16, 37, 48, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30583.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 28, 16, 37, 48,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([48, 28, 16, 37, 26,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30737.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([48, 26, 28, 16, 37,  3, 42, 39, 34, 18,  6, 24, 50, 12, 25, 35, 49,
        7, 43, 22, 17, 29, 14,  0, 10, 20,  8, 47, 32,  4, 19, 11,  9, 36,
       31, 13, 38, 44, 15, 23, 40, 45, 21, 46, 33, 30, 51, 27,  2,  1,  5,
       41]), 'cur_cost': 30750.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0, 'intermediate_solutions': [{'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 46, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 35, 12, 13, 29], 'cur_cost': 20525.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 36, 24, 50, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 23, 45, 5, 1, 41, 6, 17, 4, 25, 26, 32, 3, 37, 2, 33, 43, 7, 14, 8, 19, 46, 12, 13, 29], 'cur_cost': 18992.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 36, 24, 10, 51, 27, 11, 42, 40, 18, 9, 34, 35, 39, 48, 38, 30, 16, 0, 15, 28, 31, 21, 20, 22, 47, 49, 50, 23, 45, 14, 7, 43, 33, 2, 37, 3, 32, 26, 25, 4, 17, 6, 41, 1, 5, 8, 19, 46, 12, 13, 29], 'cur_cost': 19981.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:16,341 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:16,341 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,345 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9972.000, 多样性=0.953
2025-08-05 09:52:16,345 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:16,345 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:16,345 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:16,345 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06419618337988067, 'best_improvement': -0.040701314965560426}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.018061674008810508}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.05270585579131901, 'recent_improvements': [-0.03509932635216165, -0.11317774187309988, 0.07031238523047638], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 7542, 'new_best_cost': 7542, 'quality_improvement': 0.0, 'old_diversity': 0.7243589743589743, 'new_diversity': 0.7243589743589743, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:16,346 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:16,346 - __main__ - INFO - berlin52 开始进化第 3 代
2025-08-05 09:52:16,346 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:16,346 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,347 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9972.000, 多样性=0.953
2025-08-05 09:52:16,347 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:16,350 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.953
2025-08-05 09:52:16,350 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:16,351 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.724
2025-08-05 09:52:16,354 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:16,354 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:16,354 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:16,355 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:16,384 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: 22.329, 聚类评分: 0.000, 覆盖率: 0.154, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:16,384 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:16,384 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:16,384 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 09:52:16,389 - visualization.landscape_visualizer - INFO - 插值约束: 112 个点被约束到最小值 7542.00
2025-08-05 09:52:16,494 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_133_20250805_095216.html
2025-08-05 09:52:16,546 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_133_20250805_095216.html
2025-08-05 09:52:16,546 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 133
2025-08-05 09:52:16,547 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:16,547 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1930秒
2025-08-05 09:52:16,547 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 22.328571428570417, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 84099524.73489797, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1539, 'fitness_entropy': 0.9795952117099729, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.154)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 22.329)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358736.3845236, 'performance_metrics': {}}}
2025-08-05 09:52:16,547 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:16,547 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:16,548 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:16,548 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:16,548 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,549 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:16,549 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,549 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,549 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:16,549 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,549 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,550 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:16,550 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:16,550 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:16,550 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:16,550 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,553 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20455.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,555 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20455.0, 'intermediate_solutions': [{'tour': [0, 7, 8, 21, 48, 31, 35, 19, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 34, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10864.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10554.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 36, 20, 16, 41, 6, 1], 'cur_cost': 10677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,555 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 20455.00)
2025-08-05 09:52:16,555 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:16,555 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:16,555 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,556 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,556 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,557 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,557 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27004.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,557 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27004.0, 'intermediate_solutions': [{'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 27, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 30, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 12673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 32, 50, 10, 51, 2, 16, 41, 6, 1, 28], 'cur_cost': 11514.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 11, 14, 4, 23, 47, 49, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,558 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 27004.00)
2025-08-05 09:52:16,558 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:16,558 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:16,558 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,562 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20468.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,563 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 33, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20468.0, 'intermediate_solutions': [{'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 50, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 48, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 0, 51, 10, 12, 46, 25, 26, 29, 50, 28, 7, 42, 2, 9, 18, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 21228.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 15, 30, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 21351.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,563 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 20468.00)
2025-08-05 09:52:16,563 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:16,563 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:16,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,564 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,565 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,565 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25580.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,565 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25580.0, 'intermediate_solutions': [{'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 25, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 30, 46, 13, 26, 2, 8, 1], 'cur_cost': 24133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 1, 8, 2, 26, 13, 46, 25, 51, 10, 24, 35, 7], 'cur_cost': 19775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 39, 38, 25, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 46, 13, 26, 2, 8, 1], 'cur_cost': 22431.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,566 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 25580.00)
2025-08-05 09:52:16,566 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:16,566 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,566 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,566 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 27459.0
2025-08-05 09:52:16,572 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,572 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,572 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,574 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,574 - ExploitationExpert - INFO - populations: [{'tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20455.0}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27004.0}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 33, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20468.0}, {'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25580.0}, {'tour': array([15, 46, 51, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25], dtype=int64), 'cur_cost': 27459.0}, {'tour': [34, 30, 10, 22, 7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33, 14, 8, 47, 6, 42, 9, 27, 16, 43, 5, 48, 25, 21, 18, 0, 2, 3, 4, 45, 1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31, 35], 'cur_cost': 32397.0}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21948.0}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10286.0}, {'tour': [16, 6, 14, 17, 5, 34, 18, 47, 9, 13, 3, 24, 11, 45, 7, 46, 19, 39, 38, 4, 12, 21, 50, 15, 0, 22, 43, 36, 20, 37, 44, 2, 51, 1, 10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25, 8, 35, 27, 42], 'cur_cost': 32097.0}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0}]
2025-08-05 09:52:16,574 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,574 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 343, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 343, 'cache_hits': 0, 'similarity_calculations': 1756, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,576 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([15, 46, 51, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25], dtype=int64), 'cur_cost': 27459.0, 'intermediate_solutions': [{'tour': array([38, 13, 50, 23, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 38, 13, 50, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 23, 38, 13, 50, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31435.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 23, 38, 13, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31752.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 48, 23, 38, 13, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31866.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,576 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 27459.00)
2025-08-05 09:52:16,576 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:16,576 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,576 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,576 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 27518.0
2025-08-05 09:52:16,583 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,584 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,584 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,585 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,585 - ExploitationExpert - INFO - populations: [{'tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20455.0}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27004.0}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 33, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20468.0}, {'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25580.0}, {'tour': array([15, 46, 51, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25], dtype=int64), 'cur_cost': 27459.0}, {'tour': array([ 4, 36,  6, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30], dtype=int64), 'cur_cost': 27518.0}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21948.0}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10286.0}, {'tour': [16, 6, 14, 17, 5, 34, 18, 47, 9, 13, 3, 24, 11, 45, 7, 46, 19, 39, 38, 4, 12, 21, 50, 15, 0, 22, 43, 36, 20, 37, 44, 2, 51, 1, 10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25, 8, 35, 27, 42], 'cur_cost': 32097.0}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0}]
2025-08-05 09:52:16,587 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,587 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 344, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 344, 'cache_hits': 0, 'similarity_calculations': 1761, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,588 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 4, 36,  6, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30], dtype=int64), 'cur_cost': 27518.0, 'intermediate_solutions': [{'tour': array([10, 30, 34, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 10, 30, 34,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 22, 10, 30, 34, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 22, 10, 30,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34,  7, 22, 10, 30, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,588 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 27518.00)
2025-08-05 09:52:16,588 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:16,588 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:16,588 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,590 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,591 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,591 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10142.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,591 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10142.0, 'intermediate_solutions': [{'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 1, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 27], 'cur_cost': 23136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 31, 24, 39, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21656.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 16, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 22667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,591 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10142.00)
2025-08-05 09:52:16,591 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:16,592 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:16,592 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,595 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,596 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,596 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19061.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,597 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19061.0, 'intermediate_solutions': [{'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 4, 23, 47, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 40, 7, 8, 9, 42, 13, 12, 18, 44, 32], 'cur_cost': 12537.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 51, 11, 50, 10, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 11002.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,597 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 19061.00)
2025-08-05 09:52:16,597 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:16,597 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,597 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,597 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 31459.0
2025-08-05 09:52:16,603 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,603 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,603 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,605 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,605 - ExploitationExpert - INFO - populations: [{'tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20455.0}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27004.0}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 33, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20468.0}, {'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25580.0}, {'tour': array([15, 46, 51, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25], dtype=int64), 'cur_cost': 27459.0}, {'tour': array([ 4, 36,  6, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30], dtype=int64), 'cur_cost': 27518.0}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10142.0}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19061.0}, {'tour': array([31, 44, 23,  1, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5], dtype=int64), 'cur_cost': 31459.0}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0}]
2025-08-05 09:52:16,606 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,606 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 345, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 345, 'cache_hits': 0, 'similarity_calculations': 1767, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,607 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([31, 44, 23,  1, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5], dtype=int64), 'cur_cost': 31459.0, 'intermediate_solutions': [{'tour': array([14,  6, 16, 17,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 31406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14,  6, 16,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 17, 14,  6, 16, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 31923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 14,  6,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  5, 17, 14,  6, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32929.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,607 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 31459.00)
2025-08-05 09:52:16,607 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:16,607 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:16,607 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14008.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,610 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14008.0, 'intermediate_solutions': [{'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 1, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 49, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 13815.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 40, 16, 1, 7, 9, 8, 42, 32], 'cur_cost': 13808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,610 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 14008.00)
2025-08-05 09:52:16,610 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:16,610 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:16,612 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20455.0, 'intermediate_solutions': [{'tour': [0, 7, 8, 21, 48, 31, 35, 19, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 34, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 20, 16, 41, 6, 1], 'cur_cost': 10864.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10554.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 8, 21, 48, 31, 35, 34, 33, 38, 39, 37, 47, 23, 4, 14, 5, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 2, 18, 44, 40, 9, 42, 32, 50, 11, 27, 26, 25, 46, 12, 13, 51, 10, 28, 29, 36, 20, 16, 41, 6, 1], 'cur_cost': 10677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27004.0, 'intermediate_solutions': [{'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 27, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 30, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 12673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 11, 14, 4, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 32, 50, 10, 51, 2, 16, 41, 6, 1, 28], 'cur_cost': 11514.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 11, 14, 4, 23, 47, 49, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 19, 29, 20, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 5, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 41, 6, 1, 28], 'cur_cost': 10678.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 33, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20468.0, 'intermediate_solutions': [{'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 50, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 48, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 20075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 30, 15, 27, 48, 31, 44, 17, 40, 0, 51, 10, 12, 46, 25, 26, 29, 50, 28, 7, 42, 2, 9, 18, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 21228.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 13, 8, 11, 49, 19, 35, 34, 33, 38, 39, 37, 36, 47, 43, 4, 14, 5, 3, 24, 20, 15, 30, 27, 48, 31, 44, 17, 40, 18, 9, 2, 42, 7, 28, 50, 29, 26, 25, 46, 12, 10, 51, 0, 1, 6, 16, 41, 45, 23, 21, 22], 'cur_cost': 21351.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25580.0, 'intermediate_solutions': [{'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 25, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 30, 46, 13, 26, 2, 8, 1], 'cur_cost': 24133.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 39, 38, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 1, 8, 2, 26, 13, 46, 25, 51, 10, 24, 35, 7], 'cur_cost': 19775.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 39, 38, 25, 23, 21, 47, 37, 3, 33, 17, 43, 31, 18, 16, 29, 41, 45, 49, 0, 14, 15, 28, 36, 44, 30, 48, 34, 42, 9, 40, 11, 50, 27, 12, 5, 22, 4, 20, 19, 6, 7, 35, 24, 10, 51, 46, 13, 26, 2, 8, 1], 'cur_cost': 22431.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 46, 51, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25], dtype=int64), 'cur_cost': 27459.0, 'intermediate_solutions': [{'tour': array([38, 13, 50, 23, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([23, 38, 13, 50, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([48, 23, 38, 13, 50, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31435.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([50, 23, 38, 13, 48, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31752.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([50, 48, 23, 38, 13, 19, 37,  3, 12, 17, 43, 35, 18, 33, 29, 45, 15,
        7,  0, 14, 21, 39, 24, 49, 30, 34, 51, 42,  2, 40, 44,  8, 27, 36,
       32, 22, 11, 20, 10,  6, 46,  9, 47, 41, 31, 28, 16, 25, 26,  1,  5,
        4]), 'cur_cost': 31866.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 36,  6, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30], dtype=int64), 'cur_cost': 27518.0, 'intermediate_solutions': [{'tour': array([10, 30, 34, 22,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32431.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 10, 30, 34,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 7, 22, 10, 30, 34, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32372.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 22, 10, 30,  7, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32272.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34,  7, 22, 10, 30, 15, 49, 26, 12, 20, 11, 17, 51, 23, 38, 44, 33,
       14,  8, 47,  6, 42,  9, 27, 16, 43,  5, 48, 25, 21, 18,  0,  2,  3,
        4, 45,  1, 32, 29, 13, 19, 24, 50, 37, 39, 41, 28, 36, 40, 46, 31,
       35]), 'cur_cost': 32260.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10142.0, 'intermediate_solutions': [{'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 1, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 27], 'cur_cost': 23136.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 31, 24, 39, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 16, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 21656.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 33, 3, 36, 49, 20, 2, 22, 37, 42, 48, 39, 24, 31, 5, 17, 21, 15, 27, 38, 14, 45, 29, 0, 7, 9, 4, 16, 43, 35, 19, 30, 18, 41, 34, 25, 11, 46, 50, 32, 47, 44, 8, 28, 40, 26, 13, 12, 51, 10, 6, 1], 'cur_cost': 22667.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19061.0, 'intermediate_solutions': [{'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 4, 23, 47, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 10360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 11, 50, 10, 51, 40, 7, 8, 9, 42, 13, 12, 18, 44, 32], 'cur_cost': 12537.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 16, 2, 17, 30, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 5, 3, 24, 45, 43, 49, 19, 22, 29, 20, 41, 6, 1, 28, 46, 25, 26, 27, 51, 11, 50, 10, 12, 13, 42, 9, 8, 7, 40, 18, 44, 32], 'cur_cost': 11002.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 44, 23,  1, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5], dtype=int64), 'cur_cost': 31459.0, 'intermediate_solutions': [{'tour': array([14,  6, 16, 17,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 31406.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 14,  6, 16,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 17, 14,  6, 16, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 31923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 14,  6,  5, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16,  5, 17, 14,  6, 34, 18, 47,  9, 13,  3, 24, 11, 45,  7, 46, 19,
       39, 38,  4, 12, 21, 50, 15,  0, 22, 43, 36, 20, 37, 44,  2, 51,  1,
       10, 41, 33, 40, 48, 29, 31, 32, 23, 49, 30, 26, 28, 25,  8, 35, 27,
       42]), 'cur_cost': 32929.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14008.0, 'intermediate_solutions': [{'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 1, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 49, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 13815.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 40, 16, 1, 7, 9, 8, 42, 32], 'cur_cost': 13808.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 21, 12, 26, 27, 25, 46, 13, 51, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 2, 18, 44, 31, 48, 20, 29, 28, 41, 6, 1, 16, 40, 7, 9, 8, 42, 32], 'cur_cost': 12103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:16,612 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:16,612 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,615 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10142.000, 多样性=0.956
2025-08-05 09:52:16,615 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:16,615 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:16,615 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:16,615 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.030616685194940114, 'best_improvement': -0.017047733654231848}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.003140421713772748}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.0244907792466096, 'recent_improvements': [-0.11317774187309988, 0.07031238523047638, -0.06419618337988067], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 7542, 'new_best_cost': 7542, 'quality_improvement': 0.0, 'old_diversity': 0.7243589743589743, 'new_diversity': 0.7243589743589743, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:16,616 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:16,616 - __main__ - INFO - berlin52 开始进化第 4 代
2025-08-05 09:52:16,616 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:16,616 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,617 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10142.000, 多样性=0.956
2025-08-05 09:52:16,617 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:16,619 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.956
2025-08-05 09:52:16,619 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:16,620 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.724
2025-08-05 09:52:16,622 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:16,622 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:16,622 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:16,622 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:16,651 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: 1877.371, 聚类评分: 0.000, 覆盖率: 0.155, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:16,652 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:16,652 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:16,652 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 09:52:16,658 - visualization.landscape_visualizer - INFO - 插值约束: 132 个点被约束到最小值 7542.00
2025-08-05 09:52:16,760 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_134_20250805_095216.html
2025-08-05 09:52:16,810 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_134_20250805_095216.html
2025-08-05 09:52:16,810 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 134
2025-08-05 09:52:16,810 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:16,810 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1886秒
2025-08-05 09:52:16,811 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 1877.3714285714284, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 55447907.32489795, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1546, 'fitness_entropy': 0.9178114620629283, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.155)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1877.371)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358736.651884, 'performance_metrics': {}}}
2025-08-05 09:52:16,811 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:16,811 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:16,811 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:16,811 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:16,812 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,812 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:16,812 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,812 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,812 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:16,812 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:16,813 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:16,813 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:16,813 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:16,813 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:16,813 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:16,813 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,817 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,818 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,819 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20746.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,819 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20746.0, 'intermediate_solutions': [{'tour': [5, 4, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 34, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20520.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 27, 26, 25, 51, 10, 32, 40, 42, 50, 24, 1, 49, 46, 28, 12, 11, 4, 38, 14, 3, 35, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 21, 34, 17, 18, 23, 31, 45, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20735.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,819 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 20746.00)
2025-08-05 09:52:16,819 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:16,819 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:16,819 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,821 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:16,821 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,821 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,821 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,822 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,822 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28028.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,823 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28028.0, 'intermediate_solutions': [{'tour': [5, 39, 17, 14, 23, 31, 35, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 47, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 26966.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 33, 43, 20], 'cur_cost': 26911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 10, 26, 30, 13, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,823 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 28028.00)
2025-08-05 09:52:16,823 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:16,823 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:16,823 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,827 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,829 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21759.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,830 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21759.0, 'intermediate_solutions': [{'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 42, 45, 36, 37, 38, 33, 23, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20519.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 1, 22, 20, 18, 35, 42, 33, 38, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 33, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 21136.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,830 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21759.00)
2025-08-05 09:52:16,830 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:16,830 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:16,830 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,832 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,832 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,834 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10328.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,834 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10328.0, 'intermediate_solutions': [{'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 24, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 37, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25652.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 1, 0, 17, 22], 'cur_cost': 25794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 34, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 3, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,835 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 10328.00)
2025-08-05 09:52:16,835 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:16,835 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,835 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,835 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 31079.0
2025-08-05 09:52:16,846 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,846 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,846 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,847 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,847 - ExploitationExpert - INFO - populations: [{'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20746.0}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28028.0}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21759.0}, {'tour': [0, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10328.0}, {'tour': array([ 6, 27, 15,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39], dtype=int64), 'cur_cost': 31079.0}, {'tour': [4, 36, 6, 18, 2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10, 1, 41, 38, 47, 13, 3, 25, 9, 27, 11, 7, 8, 49, 20, 48, 32, 12, 35, 33, 17, 45, 29, 43, 34, 0, 22, 42, 51, 5, 15, 46, 50, 14, 24, 31, 19, 30], 'cur_cost': 27518.0}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10142.0}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19061.0}, {'tour': [31, 44, 23, 1, 14, 35, 49, 11, 2, 42, 40, 13, 39, 43, 0, 17, 24, 4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15, 3, 8, 36, 12, 9, 16, 50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47, 7, 6, 5], 'cur_cost': 31459.0}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14008.0}]
2025-08-05 09:52:16,848 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,848 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 346, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 346, 'cache_hits': 0, 'similarity_calculations': 1774, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,849 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 6, 27, 15,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39], dtype=int64), 'cur_cost': 31079.0, 'intermediate_solutions': [{'tour': array([51, 46, 15, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 26686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 51, 46, 15, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 36, 51, 46, 15, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 36, 51, 46, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27809.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 35, 36, 51, 46, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27866.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,849 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 31079.00)
2025-08-05 09:52:16,849 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:16,849 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,850 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,850 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 29857.0
2025-08-05 09:52:16,856 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,856 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,856 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,858 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,859 - ExploitationExpert - INFO - populations: [{'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20746.0}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28028.0}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21759.0}, {'tour': [0, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10328.0}, {'tour': array([ 6, 27, 15,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39], dtype=int64), 'cur_cost': 31079.0}, {'tour': array([10,  5, 50, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37], dtype=int64), 'cur_cost': 29857.0}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10142.0}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19061.0}, {'tour': [31, 44, 23, 1, 14, 35, 49, 11, 2, 42, 40, 13, 39, 43, 0, 17, 24, 4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15, 3, 8, 36, 12, 9, 16, 50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47, 7, 6, 5], 'cur_cost': 31459.0}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14008.0}]
2025-08-05 09:52:16,861 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,861 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 347, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 347, 'cache_hits': 0, 'similarity_calculations': 1782, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,861 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([10,  5, 50, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37], dtype=int64), 'cur_cost': 29857.0, 'intermediate_solutions': [{'tour': array([ 6, 36,  4, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18,  6, 36,  4,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 18,  6, 36,  4, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 18,  6, 36,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 28072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  2, 18,  6, 36, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 28017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,861 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 29857.00)
2025-08-05 09:52:16,861 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:16,863 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:16,863 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,868 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,870 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19694.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,870 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19694.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 12, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 39, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 12865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 1, 6, 41, 28, 29, 20, 16, 2, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 24], 'cur_cost': 10824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 48, 43, 45, 15, 49, 22, 30, 17, 21, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,870 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 19694.00)
2025-08-05 09:52:16,870 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:16,870 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:16,871 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,873 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:16,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,874 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,875 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,875 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10873.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,875 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 10873.0, 'intermediate_solutions': [{'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 16, 18, 21, 7, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 14, 29, 33, 3, 43, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 18997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 9, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,876 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 10873.00)
2025-08-05 09:52:16,876 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:16,876 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:16,876 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:16,877 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 30655.0
2025-08-05 09:52:16,885 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:16,885 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:16,885 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:16,888 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:16,888 - ExploitationExpert - INFO - populations: [{'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20746.0}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28028.0}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21759.0}, {'tour': [0, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10328.0}, {'tour': array([ 6, 27, 15,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39], dtype=int64), 'cur_cost': 31079.0}, {'tour': array([10,  5, 50, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37], dtype=int64), 'cur_cost': 29857.0}, {'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19694.0}, {'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 10873.0}, {'tour': array([34,  2, 32, 19, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49], dtype=int64), 'cur_cost': 30655.0}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14008.0}]
2025-08-05 09:52:16,890 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:16,890 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 348, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 348, 'cache_hits': 0, 'similarity_calculations': 1791, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:16,893 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([34,  2, 32, 19, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49], dtype=int64), 'cur_cost': 30655.0, 'intermediate_solutions': [{'tour': array([23, 44, 31,  1, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 23, 44, 31, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  1, 23, 44, 31, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  1, 23, 44, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 14,  1, 23, 44, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:16,893 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 30655.00)
2025-08-05 09:52:16,893 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:16,893 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:16,893 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:16,899 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:16,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,900 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,901 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:16,901 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20515.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:16,901 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 20515.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 2, 7, 8, 42, 32, 40, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 6, 41, 28, 29, 20, 16, 2, 32, 42, 1, 51], 'cur_cost': 15793.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 3, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14685.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:16,903 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 20515.00)
2025-08-05 09:52:16,903 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:16,903 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:16,905 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20746.0, 'intermediate_solutions': [{'tour': [5, 4, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 34, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20520.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 34, 17, 18, 23, 31, 45, 21, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 27, 26, 25, 51, 10, 32, 40, 42, 50, 24, 1, 49, 46, 28, 12, 11, 4, 38, 14, 3, 35, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 21, 34, 17, 18, 23, 31, 45, 20, 6, 16, 33, 15, 19, 43, 37, 39, 44, 47, 0, 22, 30, 2, 29, 35, 3, 14, 38, 4, 11, 12, 28, 46, 49, 1, 24, 50, 42, 40, 32, 10, 51, 25, 26, 27, 36, 48, 9, 8, 7, 41, 13], 'cur_cost': 20735.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28028.0, 'intermediate_solutions': [{'tour': [5, 39, 17, 14, 23, 31, 35, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 47, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 26966.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 13, 10, 26, 30, 36, 46, 9, 8, 33, 43, 20], 'cur_cost': 26911.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 39, 17, 14, 23, 31, 47, 21, 37, 6, 16, 24, 34, 19, 38, 41, 45, 44, 3, 0, 22, 28, 15, 29, 35, 48, 51, 18, 4, 11, 12, 50, 2, 49, 1, 25, 27, 42, 40, 32, 7, 10, 26, 30, 13, 36, 46, 9, 8, 43, 33, 20], 'cur_cost': 27971.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21759.0, 'intermediate_solutions': [{'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 42, 45, 36, 37, 38, 33, 23, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20519.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 1, 22, 20, 18, 35, 42, 33, 38, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 20800.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [48, 0, 17, 43, 28, 19, 31, 15, 47, 24, 23, 45, 36, 37, 38, 42, 35, 18, 20, 22, 1, 40, 3, 39, 11, 25, 49, 29, 2, 7, 8, 44, 4, 34, 27, 50, 14, 21, 41, 46, 26, 10, 32, 33, 9, 5, 30, 6, 16, 12, 13, 51], 'cur_cost': 21136.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10328.0, 'intermediate_solutions': [{'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 24, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 37, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25652.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 34, 3, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 1, 0, 17, 22], 'cur_cost': 25794.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 34, 14, 21, 47, 45, 15, 20, 42, 23, 31, 51, 37, 43, 44, 39, 35, 8, 38, 6, 30, 2, 49, 16, 11, 25, 18, 9, 19, 7, 28, 46, 4, 5, 3, 24, 27, 50, 10, 41, 29, 13, 12, 26, 40, 36, 48, 33, 0, 1, 17, 22], 'cur_cost': 25441.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 27, 15,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39], dtype=int64), 'cur_cost': 31079.0, 'intermediate_solutions': [{'tour': array([51, 46, 15, 36, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 26686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36, 51, 46, 15, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 36, 51, 46, 15, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 36, 51, 46, 35, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27809.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 35, 36, 51, 46, 34,  6, 48, 41, 27,  7, 42, 22, 39, 26, 29,  2,
        5, 40, 23,  4,  3, 50, 14, 32,  8, 19, 20,  1, 13, 28, 49, 38, 18,
       16, 24, 43, 45, 30, 31,  0, 44, 11, 21,  9, 47, 37, 17, 10, 12, 33,
       25]), 'cur_cost': 27866.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  5, 50, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37], dtype=int64), 'cur_cost': 29857.0, 'intermediate_solutions': [{'tour': array([ 6, 36,  4, 18,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18,  6, 36,  4,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27715.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 18,  6, 36,  4, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 27401.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 18,  6, 36,  2, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 28072.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  2, 18,  6, 36, 44, 40, 23, 21, 39, 16, 37, 26, 28, 10,  1, 41,
       38, 47, 13,  3, 25,  9, 27, 11,  7,  8, 49, 20, 48, 32, 12, 35, 33,
       17, 45, 29, 43, 34,  0, 22, 42, 51,  5, 15, 46, 50, 14, 24, 31, 19,
       30]), 'cur_cost': 28017.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19694.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 12, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 39, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 12865.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 3, 1, 6, 41, 28, 29, 20, 16, 2, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 24], 'cur_cost': 10824.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 48, 43, 45, 15, 49, 22, 30, 17, 21, 31, 44, 18, 40, 7, 9, 8, 42, 3, 24, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 10873.0, 'intermediate_solutions': [{'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 43, 3, 33, 29, 14, 40, 39, 16, 18, 21, 7, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19908.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 48, 37, 14, 29, 33, 3, 43, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 9, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 18997.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 15, 24, 45, 28, 35, 22, 49, 19, 23, 34, 44, 31, 0, 47, 5, 50, 26, 25, 27, 13, 38, 2, 36, 4, 9, 48, 37, 43, 3, 33, 29, 14, 40, 39, 7, 18, 21, 16, 20, 6, 30, 17, 8, 42, 32, 51, 12, 10, 46, 41, 1], 'cur_cost': 19838.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([34,  2, 32, 19, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49], dtype=int64), 'cur_cost': 30655.0, 'intermediate_solutions': [{'tour': array([23, 44, 31,  1, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31019.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 23, 44, 31, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  1, 23, 44, 31, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31142.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  1, 23, 44, 14, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31400.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 14,  1, 23, 44, 35, 49, 11,  2, 42, 40, 13, 39, 43,  0, 17, 24,
        4, 37, 21, 18, 28, 20, 48, 33, 29, 46, 15,  3,  8, 36, 12,  9, 16,
       50, 19, 10, 38, 25, 51, 45, 34, 41, 26, 30, 22, 32, 27, 47,  7,  6,
        5]), 'cur_cost': 31646.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 20515.0, 'intermediate_solutions': [{'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 2, 7, 8, 42, 32, 40, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 3, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 6, 41, 28, 29, 20, 16, 2, 32, 42, 1, 51], 'cur_cost': 15793.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 9, 13, 12, 26, 27, 25, 46, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 3, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1, 51], 'cur_cost': 14685.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:16,905 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:16,906 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,910 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10328.000, 多样性=0.968
2025-08-05 09:52:16,911 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:16,911 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:16,911 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:16,911 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0026544078951941945, 'best_improvement': -0.01833957799250641}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012969588550983856}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.050464535212708245, 'recent_improvements': [0.07031238523047638, -0.06419618337988067, -0.030616685194940114], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 7542, 'new_best_cost': 7542, 'quality_improvement': 0.0, 'old_diversity': 0.7243589743589743, 'new_diversity': 0.7243589743589743, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:16,912 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:16,912 - __main__ - INFO - berlin52 开始进化第 5 代
2025-08-05 09:52:16,913 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:16,913 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:16,914 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10328.000, 多样性=0.968
2025-08-05 09:52:16,914 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:16,917 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.968
2025-08-05 09:52:16,917 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:16,919 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.724
2025-08-05 09:52:16,922 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:16,922 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:16,922 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 09:52:16,923 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 09:52:16,965 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.071, 适应度梯度: -844.043, 聚类评分: 0.000, 覆盖率: 0.155, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:16,966 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:16,966 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:16,966 - visualization.landscape_visualizer - INFO - 设置当前实例名: berlin52
2025-08-05 09:52:16,971 - visualization.landscape_visualizer - INFO - 插值约束: 170 个点被约束到最小值 7542.00
2025-08-05 09:52:17,107 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\landscape_berlin52_iter_135_20250805_095217.html
2025-08-05 09:52:17,179 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_berlin52\dashboard_berlin52_iter_135_20250805_095217.html
2025-08-05 09:52:17,180 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 135
2025-08-05 09:52:17,180 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:17,180 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2574秒
2025-08-05 09:52:17,180 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.07142857142857142, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -844.0428571428571, 'local_optima_density': 0.07142857142857142, 'gradient_variance': 50221685.81244897, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1554, 'fitness_entropy': 0.9337851376692216, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -844.043)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.155)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358736.9665794, 'performance_metrics': {}}}
2025-08-05 09:52:17,180 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:17,180 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:17,181 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:17,181 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:17,181 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:17,181 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:17,181 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:17,182 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,182 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:17,182 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-08-05 09:52:17,182 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,182 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:17,182 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:17,183 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:17,183 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:17,183 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,187 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:17,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,187 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,188 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20079.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,188 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [36, 35, 49, 5, 48, 44, 21, 31, 4, 15, 29, 19, 28, 0, 2, 17, 45, 47, 30, 40, 3, 38, 37, 34, 11, 10, 27, 26, 14, 9, 7, 8, 43, 23, 42, 18, 24, 50, 51, 13, 25, 33, 20, 16, 1, 6, 39, 22, 46, 12, 32, 41], 'cur_cost': 20079.0, 'intermediate_solutions': [{'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 27, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 20, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 23659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 8, 41, 16, 7, 31, 38, 9, 42, 1, 20, 0, 25, 50, 24, 47, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 51, 26, 46, 5, 40, 6], 'cur_cost': 20375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,188 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 20079.00)
2025-08-05 09:52:17,188 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:17,188 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:17,188 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,190 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:17,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,190 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,191 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11635.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,191 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 5, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 40, 32, 41, 6, 1], 'cur_cost': 11635.0, 'intermediate_solutions': [{'tour': [5, 18, 8, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 3, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28086.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 51, 11, 32, 48, 40, 41, 10, 27, 17, 38, 2, 28, 12, 20, 29, 25, 50, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 27866.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 11, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,191 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 11635.00)
2025-08-05 09:52:17,191 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:17,192 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:17,192 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,195 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:17,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,196 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,197 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19392.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,197 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [41, 33, 43, 15, 34, 47, 23, 22, 2, 16, 1, 40, 38, 44, 37, 39, 4, 18, 20, 21, 19, 30, 48, 17, 36, 5, 14, 24, 42, 11, 3, 25, 51, 50, 13, 27, 28, 31, 0, 49, 7, 9, 8, 45, 46, 26, 35, 32, 10, 12, 29, 6], 'cur_cost': 19392.0, 'intermediate_solutions': [{'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 32, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 16, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 24520.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 13, 51, 11, 10, 27, 12, 46, 41, 20, 33, 3, 8, 6], 'cur_cost': 20743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 33, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21865.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,197 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 19392.00)
2025-08-05 09:52:17,197 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:17,197 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:17,197 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,198 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:17,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,199 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,200 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25069.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,200 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [36, 33, 49, 5, 34, 44, 23, 35, 2, 15, 29, 19, 28, 16, 47, 17, 45, 14, 30, 21, 3, 43, 37, 0, 11, 10, 27, 26, 42, 9, 7, 8, 51, 50, 46, 25, 24, 31, 40, 13, 6, 18, 12, 32, 1, 20, 39, 22, 48, 41, 38, 4], 'cur_cost': 25069.0, 'intermediate_solutions': [{'tour': [33, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 0, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10484.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 24, 50, 10, 51, 13, 46, 25, 27, 26, 12, 3, 11, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 12, 26, 27, 25, 46, 13, 51, 10, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,200 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 25069.00)
2025-08-05 09:52:17,200 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:17,200 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,200 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,201 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 30852.0
2025-08-05 09:52:17,207 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:17,207 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:17,207 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:17,209 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,209 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 49, 5, 48, 44, 21, 31, 4, 15, 29, 19, 28, 0, 2, 17, 45, 47, 30, 40, 3, 38, 37, 34, 11, 10, 27, 26, 14, 9, 7, 8, 43, 23, 42, 18, 24, 50, 51, 13, 25, 33, 20, 16, 1, 6, 39, 22, 46, 12, 32, 41], 'cur_cost': 20079.0}, {'tour': [0, 7, 5, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 40, 32, 41, 6, 1], 'cur_cost': 11635.0}, {'tour': [41, 33, 43, 15, 34, 47, 23, 22, 2, 16, 1, 40, 38, 44, 37, 39, 4, 18, 20, 21, 19, 30, 48, 17, 36, 5, 14, 24, 42, 11, 3, 25, 51, 50, 13, 27, 28, 31, 0, 49, 7, 9, 8, 45, 46, 26, 35, 32, 10, 12, 29, 6], 'cur_cost': 19392.0}, {'tour': [36, 33, 49, 5, 34, 44, 23, 35, 2, 15, 29, 19, 28, 16, 47, 17, 45, 14, 30, 21, 3, 43, 37, 0, 11, 10, 27, 26, 42, 9, 7, 8, 51, 50, 46, 25, 24, 31, 40, 13, 6, 18, 12, 32, 1, 20, 39, 22, 48, 41, 38, 4], 'cur_cost': 25069.0}, {'tour': array([43, 23, 24, 33,  5,  0, 45,  6, 38,  7, 22,  1, 18, 29, 27, 19, 15,
       13, 26, 49, 39, 20, 47, 44, 16, 31,  9, 50, 32, 40, 35, 36, 12,  2,
       48, 11, 28, 42, 21, 51, 37, 41, 10, 14, 34, 25, 17,  3,  8, 30, 46,
        4], dtype=int64), 'cur_cost': 30852.0}, {'tour': [10, 5, 50, 29, 3, 49, 43, 30, 41, 11, 27, 39, 44, 19, 4, 33, 6, 23, 47, 18, 42, 28, 8, 1, 16, 21, 0, 35, 2, 32, 40, 31, 13, 9, 36, 12, 26, 7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17, 37], 'cur_cost': 29857.0}, {'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19694.0}, {'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 10873.0}, {'tour': [34, 2, 32, 19, 30, 48, 7, 38, 33, 20, 15, 29, 4, 21, 50, 22, 0, 31, 35, 25, 42, 3, 14, 46, 5, 27, 17, 16, 23, 6, 39, 44, 37, 11, 43, 9, 47, 26, 10, 24, 13, 18, 45, 8, 12, 1, 40, 41, 51, 36, 28, 49], 'cur_cost': 30655.0}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 20515.0}]
2025-08-05 09:52:17,210 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:17,210 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 349, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 349, 'cache_hits': 0, 'similarity_calculations': 1801, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,210 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([43, 23, 24, 33,  5,  0, 45,  6, 38,  7, 22,  1, 18, 29, 27, 19, 15,
       13, 26, 49, 39, 20, 47, 44, 16, 31,  9, 50, 32, 40, 35, 36, 12,  2,
       48, 11, 28, 42, 21, 51, 37, 41, 10, 14, 34, 25, 17,  3,  8, 30, 46,
        4], dtype=int64), 'cur_cost': 30852.0, 'intermediate_solutions': [{'tour': array([15, 27,  6,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30792.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 15, 27,  6, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 31082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23,  8, 15, 27,  6, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  8, 15, 27, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 23,  8, 15, 27, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 31101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,211 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 30852.00)
2025-08-05 09:52:17,211 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:17,211 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,211 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,211 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 29798.0
2025-08-05 09:52:17,218 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:17,219 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:17,219 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:17,220 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,220 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 49, 5, 48, 44, 21, 31, 4, 15, 29, 19, 28, 0, 2, 17, 45, 47, 30, 40, 3, 38, 37, 34, 11, 10, 27, 26, 14, 9, 7, 8, 43, 23, 42, 18, 24, 50, 51, 13, 25, 33, 20, 16, 1, 6, 39, 22, 46, 12, 32, 41], 'cur_cost': 20079.0}, {'tour': [0, 7, 5, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 40, 32, 41, 6, 1], 'cur_cost': 11635.0}, {'tour': [41, 33, 43, 15, 34, 47, 23, 22, 2, 16, 1, 40, 38, 44, 37, 39, 4, 18, 20, 21, 19, 30, 48, 17, 36, 5, 14, 24, 42, 11, 3, 25, 51, 50, 13, 27, 28, 31, 0, 49, 7, 9, 8, 45, 46, 26, 35, 32, 10, 12, 29, 6], 'cur_cost': 19392.0}, {'tour': [36, 33, 49, 5, 34, 44, 23, 35, 2, 15, 29, 19, 28, 16, 47, 17, 45, 14, 30, 21, 3, 43, 37, 0, 11, 10, 27, 26, 42, 9, 7, 8, 51, 50, 46, 25, 24, 31, 40, 13, 6, 18, 12, 32, 1, 20, 39, 22, 48, 41, 38, 4], 'cur_cost': 25069.0}, {'tour': array([43, 23, 24, 33,  5,  0, 45,  6, 38,  7, 22,  1, 18, 29, 27, 19, 15,
       13, 26, 49, 39, 20, 47, 44, 16, 31,  9, 50, 32, 40, 35, 36, 12,  2,
       48, 11, 28, 42, 21, 51, 37, 41, 10, 14, 34, 25, 17,  3,  8, 30, 46,
        4], dtype=int64), 'cur_cost': 30852.0}, {'tour': array([34, 49, 13, 19, 12, 46, 28, 50, 22, 47, 31, 40, 10, 35, 41, 30,  7,
        9, 36, 17, 27, 29, 38, 43, 48, 18, 26, 21, 16,  5, 45, 11,  2, 32,
       23,  8, 51, 42, 14, 25,  1, 20, 24, 37,  4,  6, 15,  0, 44,  3, 33,
       39], dtype=int64), 'cur_cost': 29798.0}, {'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19694.0}, {'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 10873.0}, {'tour': [34, 2, 32, 19, 30, 48, 7, 38, 33, 20, 15, 29, 4, 21, 50, 22, 0, 31, 35, 25, 42, 3, 14, 46, 5, 27, 17, 16, 23, 6, 39, 44, 37, 11, 43, 9, 47, 26, 10, 24, 13, 18, 45, 8, 12, 1, 40, 41, 51, 36, 28, 49], 'cur_cost': 30655.0}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 20515.0}]
2025-08-05 09:52:17,222 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:17,222 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 350, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 350, 'cache_hits': 0, 'similarity_calculations': 1812, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,222 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([34, 49, 13, 19, 12, 46, 28, 50, 22, 47, 31, 40, 10, 35, 41, 30,  7,
        9, 36, 17, 27, 29, 38, 43, 48, 18, 26, 21, 16,  5, 45, 11,  2, 32,
       23,  8, 51, 42, 14, 25,  1, 20, 24, 37,  4,  6, 15,  0, 44,  3, 33,
       39], dtype=int64), 'cur_cost': 29798.0, 'intermediate_solutions': [{'tour': array([50,  5, 10, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29805.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 50,  5, 10,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 29, 50,  5, 10, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 29, 50,  5,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  3, 29, 50,  5, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,223 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 29798.00)
2025-08-05 09:52:17,223 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:17,223 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:17,223 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,227 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 52
2025-08-05 09:52:17,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19152.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,229 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [37, 35, 48, 17, 2, 34, 43, 4, 0, 36, 23, 42, 11, 51, 32, 38, 21, 49, 5, 45, 19, 15, 44, 39, 22, 41, 30, 20, 6, 28, 46, 3, 33, 14, 9, 16, 18, 8, 7, 29, 1, 31, 40, 24, 47, 27, 12, 26, 25, 13, 10, 50], 'cur_cost': 19152.0, 'intermediate_solutions': [{'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 41, 22, 38, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 12, 25, 27, 32, 50, 20, 30, 40, 13, 51, 34], 'cur_cost': 21493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 45, 49, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,229 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 19152.00)
2025-08-05 09:52:17,229 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:17,229 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:17,229 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,231 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 52
2025-08-05 09:52:17,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9628.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,232 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 17, 15, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 24, 3, 42, 9, 8, 7, 40, 18, 44, 31, 48, 21, 30, 20, 22, 19, 49, 28, 29, 41, 6, 1, 16, 2, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32], 'cur_cost': 9628.0, 'intermediate_solutions': [{'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 51, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 24, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 13014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 14, 20, 30, 17, 34, 35, 31, 48, 21, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 2, 16, 8, 41, 6, 1, 32], 'cur_cost': 12085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,232 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 9628.00)
2025-08-05 09:52:17,232 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 09:52:17,232 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,232 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,234 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 27032.0
2025-08-05 09:52:17,239 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:17,239 - ExploitationExpert - INFO - res_population_costs: [7542, 7715, 7769, 8006.0]
2025-08-05 09:52:17,239 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 39, 38, 36, 45, 43, 33, 34, 35, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 44,
       18, 40,  7,  8,  9, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11,
       24,  3,  5, 14,  4, 23, 47, 37, 36, 39, 38, 35, 34, 33, 45, 43, 48,
       31], dtype=int64), array([ 0, 21, 17, 30, 22, 19, 49, 15, 28, 29,  1,  6, 41, 20, 16,  2, 40,
        7,  8,  9, 18, 44, 31, 48, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,
        3, 42, 32, 50, 10, 51, 13, 12, 46, 25, 26, 27, 11, 24, 45, 43, 33,
       34], dtype=int64)]
2025-08-05 09:52:17,241 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,241 - ExploitationExpert - INFO - populations: [{'tour': [36, 35, 49, 5, 48, 44, 21, 31, 4, 15, 29, 19, 28, 0, 2, 17, 45, 47, 30, 40, 3, 38, 37, 34, 11, 10, 27, 26, 14, 9, 7, 8, 43, 23, 42, 18, 24, 50, 51, 13, 25, 33, 20, 16, 1, 6, 39, 22, 46, 12, 32, 41], 'cur_cost': 20079.0}, {'tour': [0, 7, 5, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 40, 32, 41, 6, 1], 'cur_cost': 11635.0}, {'tour': [41, 33, 43, 15, 34, 47, 23, 22, 2, 16, 1, 40, 38, 44, 37, 39, 4, 18, 20, 21, 19, 30, 48, 17, 36, 5, 14, 24, 42, 11, 3, 25, 51, 50, 13, 27, 28, 31, 0, 49, 7, 9, 8, 45, 46, 26, 35, 32, 10, 12, 29, 6], 'cur_cost': 19392.0}, {'tour': [36, 33, 49, 5, 34, 44, 23, 35, 2, 15, 29, 19, 28, 16, 47, 17, 45, 14, 30, 21, 3, 43, 37, 0, 11, 10, 27, 26, 42, 9, 7, 8, 51, 50, 46, 25, 24, 31, 40, 13, 6, 18, 12, 32, 1, 20, 39, 22, 48, 41, 38, 4], 'cur_cost': 25069.0}, {'tour': array([43, 23, 24, 33,  5,  0, 45,  6, 38,  7, 22,  1, 18, 29, 27, 19, 15,
       13, 26, 49, 39, 20, 47, 44, 16, 31,  9, 50, 32, 40, 35, 36, 12,  2,
       48, 11, 28, 42, 21, 51, 37, 41, 10, 14, 34, 25, 17,  3,  8, 30, 46,
        4], dtype=int64), 'cur_cost': 30852.0}, {'tour': array([34, 49, 13, 19, 12, 46, 28, 50, 22, 47, 31, 40, 10, 35, 41, 30,  7,
        9, 36, 17, 27, 29, 38, 43, 48, 18, 26, 21, 16,  5, 45, 11,  2, 32,
       23,  8, 51, 42, 14, 25,  1, 20, 24, 37,  4,  6, 15,  0, 44,  3, 33,
       39], dtype=int64), 'cur_cost': 29798.0}, {'tour': [37, 35, 48, 17, 2, 34, 43, 4, 0, 36, 23, 42, 11, 51, 32, 38, 21, 49, 5, 45, 19, 15, 44, 39, 22, 41, 30, 20, 6, 28, 46, 3, 33, 14, 9, 16, 18, 8, 7, 29, 1, 31, 40, 24, 47, 27, 12, 26, 25, 13, 10, 50], 'cur_cost': 19152.0}, {'tour': [0, 17, 15, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 24, 3, 42, 9, 8, 7, 40, 18, 44, 31, 48, 21, 30, 20, 22, 19, 49, 28, 29, 41, 6, 1, 16, 2, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32], 'cur_cost': 9628.0}, {'tour': array([ 6, 44, 47, 46, 25, 26, 10,  3, 37, 34, 41, 21, 20, 18, 28, 33, 14,
       24, 36, 22, 49, 13, 30, 11, 12,  0, 19,  2, 51, 50, 40,  7, 42,  1,
       45, 17,  8, 16, 39,  4,  9, 29, 31, 27, 43, 23, 35,  5, 15, 38, 48,
       32], dtype=int64), 'cur_cost': 27032.0}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 20515.0}]
2025-08-05 09:52:17,242 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:17,243 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 351, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 351, 'cache_hits': 0, 'similarity_calculations': 1824, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,244 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 6, 44, 47, 46, 25, 26, 10,  3, 37, 34, 41, 21, 20, 18, 28, 33, 14,
       24, 36, 22, 49, 13, 30, 11, 12,  0, 19,  2, 51, 50, 40,  7, 42,  1,
       45, 17,  8, 16, 39,  4,  9, 29, 31, 27, 43, 23, 35,  5, 15, 38, 48,
       32], dtype=int64), 'cur_cost': 27032.0, 'intermediate_solutions': [{'tour': array([32,  2, 34, 19, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 32,  2, 34, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30470.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 19, 32,  2, 34, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 19, 32,  2, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 30, 19, 32,  2, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,244 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 27032.00)
2025-08-05 09:52:17,244 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:17,244 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:17,244 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,245 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 52
2025-08-05 09:52:17,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,245 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,246 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,246 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25105.0, 路径长度: 52, 收集中间解: 3
2025-08-05 09:52:17,246 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 18, 47, 48, 31, 4, 33, 38, 39, 37, 0, 2, 23, 20, 45, 30, 44, 34, 36, 22, 14, 17, 3, 25, 29, 28, 42, 27, 26, 50, 51, 11, 10, 9, 8, 32, 46, 6, 35, 19, 21, 12, 41, 1, 43, 49, 15, 16, 13, 40, 5, 24], 'cur_cost': 25105.0, 'intermediate_solutions': [{'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 26, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 41, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 22299.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 6, 40, 10, 36, 15, 50, 13, 46, 51, 27, 5, 11, 32, 8, 7, 35, 19, 16, 9, 42, 45, 26, 12, 25, 49, 1], 'cur_cost': 21639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 24, 49, 25, 12, 33, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 21690.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,247 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 25105.00)
2025-08-05 09:52:17,247 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:17,247 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:17,249 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [36, 35, 49, 5, 48, 44, 21, 31, 4, 15, 29, 19, 28, 0, 2, 17, 45, 47, 30, 40, 3, 38, 37, 34, 11, 10, 27, 26, 14, 9, 7, 8, 43, 23, 42, 18, 24, 50, 51, 13, 25, 33, 20, 16, 1, 6, 39, 22, 46, 12, 32, 41], 'cur_cost': 20079.0, 'intermediate_solutions': [{'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 27, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 20, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 23659.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 8, 41, 16, 7, 31, 38, 9, 42, 1, 20, 0, 25, 50, 24, 47, 48, 32, 11, 27, 10, 13, 12, 26, 46, 51, 5, 40, 6], 'cur_cost': 20850.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 18, 36, 44, 34, 3, 49, 33, 17, 37, 39, 14, 19, 4, 23, 21, 2, 15, 28, 45, 43, 22, 29, 35, 47, 24, 50, 25, 0, 20, 1, 42, 9, 38, 31, 7, 16, 41, 8, 48, 32, 11, 27, 10, 13, 12, 51, 26, 46, 5, 40, 6], 'cur_cost': 20375.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 5, 18, 44, 31, 48, 35, 34, 33, 38, 39, 37, 36, 47, 23, 4, 14, 3, 24, 45, 43, 15, 49, 19, 22, 30, 17, 21, 2, 16, 20, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 40, 32, 41, 6, 1], 'cur_cost': 11635.0, 'intermediate_solutions': [{'tour': [5, 18, 8, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 3, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 11, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28086.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 51, 11, 32, 48, 40, 41, 10, 27, 17, 38, 2, 28, 12, 20, 29, 25, 50, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 27866.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 18, 3, 43, 23, 19, 31, 21, 11, 47, 6, 7, 45, 34, 4, 26, 33, 39, 44, 8, 0, 30, 1, 15, 35, 16, 24, 50, 25, 29, 20, 12, 28, 2, 38, 17, 27, 10, 41, 40, 48, 32, 51, 9, 13, 46, 37, 14, 42, 49, 36, 22], 'cur_cost': 28694.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [41, 33, 43, 15, 34, 47, 23, 22, 2, 16, 1, 40, 38, 44, 37, 39, 4, 18, 20, 21, 19, 30, 48, 17, 36, 5, 14, 24, 42, 11, 3, 25, 51, 50, 13, 27, 28, 31, 0, 49, 7, 9, 8, 45, 46, 26, 35, 32, 10, 12, 29, 6], 'cur_cost': 19392.0, 'intermediate_solutions': [{'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 32, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 16, 26, 25, 3, 33, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 24520.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 13, 51, 11, 10, 27, 12, 46, 41, 20, 33, 3, 8, 6], 'cur_cost': 20743.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [24, 15, 22, 31, 4, 23, 35, 5, 49, 37, 39, 33, 38, 42, 21, 43, 45, 47, 28, 48, 7, 2, 29, 16, 1, 40, 30, 44, 36, 9, 17, 19, 18, 34, 0, 14, 50, 32, 26, 25, 3, 20, 41, 46, 12, 27, 10, 11, 51, 13, 8, 6], 'cur_cost': 21865.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [36, 33, 49, 5, 34, 44, 23, 35, 2, 15, 29, 19, 28, 16, 47, 17, 45, 14, 30, 21, 3, 43, 37, 0, 11, 10, 27, 26, 42, 9, 7, 8, 51, 50, 46, 25, 24, 31, 40, 13, 6, 18, 12, 32, 1, 20, 39, 22, 48, 41, 38, 4], 'cur_cost': 25069.0, 'intermediate_solutions': [{'tour': [33, 11, 3, 12, 26, 27, 25, 46, 13, 51, 10, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 0, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10484.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 43, 33, 34, 35, 38, 36, 39, 37, 47, 23, 14, 4, 5, 24, 50, 10, 51, 13, 46, 25, 27, 26, 12, 3, 11, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10097.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 12, 26, 27, 25, 46, 13, 51, 10, 11, 50, 24, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 15, 49, 19, 22, 30, 17, 21, 48, 31, 44, 18, 40, 7, 9, 8, 42, 32, 2, 16, 20, 29, 28, 41, 6, 1], 'cur_cost': 10065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 23, 24, 33,  5,  0, 45,  6, 38,  7, 22,  1, 18, 29, 27, 19, 15,
       13, 26, 49, 39, 20, 47, 44, 16, 31,  9, 50, 32, 40, 35, 36, 12,  2,
       48, 11, 28, 42, 21, 51, 37, 41, 10, 14, 34, 25, 17,  3,  8, 30, 46,
        4], dtype=int64), 'cur_cost': 30852.0, 'intermediate_solutions': [{'tour': array([15, 27,  6,  8, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30792.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 15, 27,  6, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 31082.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23,  8, 15, 27,  6, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  8, 15, 27, 23, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 30804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 23,  8, 15, 27, 22,  3, 14, 47, 34, 46, 50, 36, 16, 12, 24, 28,
       17, 11, 48, 40, 21, 13,  0, 41, 49, 51, 45,  9, 18, 30,  7, 33,  4,
       25, 20, 38, 43,  2,  1,  5, 35, 42, 29, 19, 32, 10, 37, 44, 31, 26,
       39]), 'cur_cost': 31101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 49, 13, 19, 12, 46, 28, 50, 22, 47, 31, 40, 10, 35, 41, 30,  7,
        9, 36, 17, 27, 29, 38, 43, 48, 18, 26, 21, 16,  5, 45, 11,  2, 32,
       23,  8, 51, 42, 14, 25,  1, 20, 24, 37,  4,  6, 15,  0, 44,  3, 33,
       39], dtype=int64), 'cur_cost': 29798.0, 'intermediate_solutions': [{'tour': array([50,  5, 10, 29,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29805.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 50,  5, 10,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 29, 50,  5, 10, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10, 29, 50,  5,  3, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29762.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  3, 29, 50,  5, 49, 43, 30, 41, 11, 27, 39, 44, 19,  4, 33,  6,
       23, 47, 18, 42, 28,  8,  1, 16, 21,  0, 35,  2, 32, 40, 31, 13,  9,
       36, 12, 26,  7, 14, 51, 15, 45, 20, 22, 38, 24, 34, 25, 48, 46, 17,
       37]), 'cur_cost': 29730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [37, 35, 48, 17, 2, 34, 43, 4, 0, 36, 23, 42, 11, 51, 32, 38, 21, 49, 5, 45, 19, 15, 44, 39, 22, 41, 30, 20, 6, 28, 46, 3, 33, 14, 9, 16, 18, 8, 7, 29, 1, 31, 40, 24, 47, 27, 12, 26, 25, 13, 10, 50], 'cur_cost': 19152.0, 'intermediate_solutions': [{'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 41, 22, 38, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [23, 45, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 49, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 12, 25, 27, 32, 50, 20, 30, 40, 13, 51, 34], 'cur_cost': 21493.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [23, 45, 49, 21, 19, 31, 18, 9, 5, 36, 4, 48, 35, 42, 0, 2, 8, 33, 28, 29, 1, 16, 17, 39, 24, 26, 46, 15, 43, 3, 37, 47, 14, 11, 10, 38, 22, 41, 7, 44, 6, 40, 30, 20, 50, 32, 27, 25, 12, 13, 51, 34], 'cur_cost': 19472.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 15, 5, 4, 14, 23, 47, 37, 39, 36, 38, 35, 34, 33, 43, 45, 24, 3, 42, 9, 8, 7, 40, 18, 44, 31, 48, 21, 30, 20, 22, 19, 49, 28, 29, 41, 6, 1, 16, 2, 11, 27, 26, 25, 46, 12, 13, 51, 10, 50, 32], 'cur_cost': 9628.0, 'intermediate_solutions': [{'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 51, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 24, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 13014.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 14, 20, 30, 17, 34, 35, 31, 48, 21, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 8, 7, 40, 18, 44, 2, 16, 41, 6, 1, 32], 'cur_cost': 11189.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 14, 20, 30, 17, 21, 48, 31, 35, 34, 33, 38, 39, 37, 36, 47, 23, 5, 3, 24, 45, 43, 15, 49, 19, 22, 29, 28, 46, 25, 26, 27, 11, 50, 10, 51, 12, 13, 42, 9, 7, 40, 18, 44, 2, 16, 8, 41, 6, 1, 32], 'cur_cost': 12085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 44, 47, 46, 25, 26, 10,  3, 37, 34, 41, 21, 20, 18, 28, 33, 14,
       24, 36, 22, 49, 13, 30, 11, 12,  0, 19,  2, 51, 50, 40,  7, 42,  1,
       45, 17,  8, 16, 39,  4,  9, 29, 31, 27, 43, 23, 35,  5, 15, 38, 48,
       32], dtype=int64), 'cur_cost': 27032.0, 'intermediate_solutions': [{'tour': array([32,  2, 34, 19, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30649.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 32,  2, 34, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30470.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 19, 32,  2, 34, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30552.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([34, 19, 32,  2, 30, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30516.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([34, 30, 19, 32,  2, 48,  7, 38, 33, 20, 15, 29,  4, 21, 50, 22,  0,
       31, 35, 25, 42,  3, 14, 46,  5, 27, 17, 16, 23,  6, 39, 44, 37, 11,
       43,  9, 47, 26, 10, 24, 13, 18, 45,  8, 12,  1, 40, 41, 51, 36, 28,
       49]), 'cur_cost': 30639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 18, 47, 48, 31, 4, 33, 38, 39, 37, 0, 2, 23, 20, 45, 30, 44, 34, 36, 22, 14, 17, 3, 25, 29, 28, 42, 27, 26, 50, 51, 11, 10, 9, 8, 32, 46, 6, 35, 19, 21, 12, 41, 1, 43, 49, 15, 16, 13, 40, 5, 24], 'cur_cost': 25105.0, 'intermediate_solutions': [{'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 26, 23, 43, 18, 31, 17, 28, 33, 24, 49, 25, 12, 41, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 22299.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 33, 24, 6, 40, 10, 36, 15, 50, 13, 46, 51, 27, 5, 11, 32, 8, 7, 35, 19, 16, 9, 42, 45, 26, 12, 25, 49, 1], 'cur_cost': 21639.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 2, 37, 47, 0, 48, 21, 38, 39, 22, 14, 34, 4, 3, 44, 20, 29, 41, 23, 43, 18, 31, 17, 28, 24, 49, 25, 12, 33, 26, 45, 42, 9, 16, 19, 35, 7, 8, 32, 11, 5, 27, 51, 46, 13, 50, 15, 36, 10, 40, 6, 1], 'cur_cost': 21690.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:17,249 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:17,249 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,252 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9628.000, 多样性=0.967
2025-08-05 09:52:17,252 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:17,252 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:17,252 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:17,253 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.033663977172220576, 'best_improvement': 0.06777691711851278}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0013245033112580444}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03077088774234324, 'recent_improvements': [-0.06419618337988067, -0.030616685194940114, -0.0026544078951941945], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 7542, 'new_best_cost': 7542, 'quality_improvement': 0.0, 'old_diversity': 0.7243589743589743, 'new_diversity': 0.7243589743589743, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:17,253 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:17,256 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\berlin52_solution.json
2025-08-05 09:52:17,256 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\berlin52_20250805_095217.solution
2025-08-05 09:52:17,256 - __main__ - INFO - 实例执行完成 - 运行时间: 1.51s, 最佳成本: 7542
2025-08-05 09:52:17,256 - __main__ - INFO - 实例 berlin52 处理完成
