# -*- coding: utf-8 -*-
"""
增强策略选择提示工程模板

为个体级策略选择提供优化的LLM提示模板，充分利用适应度景观分析和个体状态信息。
"""

import json
import re
from typing import Dict, List, Any, Optional


# 个体级策略选择提示模板
INDIVIDUAL_STRATEGY_PROMPT_TEMPLATE = """## 角色：TSP优化策略选择专家

### 任务概述
基于详细的个体状态分析和适应度景观特征，为TSP优化算法中的每个个体分配最优策略。

### 当前状态 (迭代 {iteration})

#### 景观特征分析
- **全局粗糙度**: {global_ruggedness:.3f} (0-1, 越高表示景观越粗糙，局部最优越多)
- **模态性**: {modality} (单模态/多模态，影响搜索策略选择)
- **欺骗性**: {deceptiveness} (低/中/高，高欺骗性需要更强探索)
- **梯度强度**: {gradient_strength:.3f} (0-1, 越高表示梯度信息越可靠)
- **种群多样性**: {population_diversity:.3f} (0-1, 越高表示种群越多样化)
- **收敛趋势**: {convergence_trend:.3f} (0-1, 越高表示种群越收敛)
- **进化阶段**: {evolution_phase} (exploration/exploitation/balance)
- **困难区域**: {difficult_regions_count} 个 (需要避免的搜索区域)
- **机会区域**: {opportunity_regions_count} 个 (有潜力的搜索区域)

#### 历史反馈
{feedback_summary}

### 个体详细状态分析

种群规模: {population_size}

{individual_details}

### 策略类型详解

#### 探索策略 (Exploration)
1. **strong_exploration**: 强探索策略
   - 适用场景: 严重停滞(>10代)、极低多样性(<0.2)、高欺骗性景观
   - 特点: 大幅度变异、随机重启、区域跳跃
   - 风险: 可能破坏已有好解，计算成本高

2. **balanced_exploration**: 平衡探索策略  
   - 适用场景: 中等停滞(3-10代)、中低多样性(0.2-0.4)、中等粗糙度
   - 特点: 适度变异、区域探索、保持一定随机性
   - 风险: 探索力度可能不足

3. **intelligent_exploration**: 智能探索策略
   - 适用场景: 有潜力的中等个体、机会区域较多、梯度信息可靠
   - 特点: 基于梯度的探索、利用机会区域、自适应步长
   - 风险: 依赖景观分析准确性

#### 开发策略 (Exploitation)
4. **cautious_exploitation**: 谨慎开发策略
   - 适用场景: 较好个体(前30%)、轻微停滞、不确定景观
   - 特点: 小步长局部搜索、保守优化、多样性保护
   - 风险: 改进速度较慢

5. **moderate_exploitation**: 中等开发策略
   - 适用场景: 优秀个体(前20%)、无停滞、中等收敛
   - 特点: 标准局部搜索、邻域优化、精英学习
   - 风险: 可能陷入局部最优

6. **aggressive_exploitation**: 激进开发策略
   - 适用场景: 顶级个体(前10%)、强梯度信息、高收敛阶段
   - 特点: 深度局部搜索、多邻域组合、集中优化
   - 风险: 过度开发，损失多样性

7. **intensive_exploitation**: 集中开发策略
   - 适用场景: 最优个体(前5%)、确定性景观、收敛后期
   - 特点: 精细调优、微调参数、极限优化
   - 风险: 计算资源集中，其他区域探索不足

#### 混合策略 (Hybrid)
8. **adaptive_hybrid**: 自适应混合策略
   - 适用场景: 复杂多模态景观、动态环境、不确定情况
   - 特点: 探索开发动态平衡、自适应参数、多策略融合
   - 风险: 复杂度高，参数调节困难

9. **collaborative_escape**: 协作逃逸策略
   - 适用场景: 集体停滞、所有个体困在局部最优、紧急情况
   - 特点: 种群重组、协作变异、集体跳跃
   - 风险: 破坏性强，需要谨慎使用

### 策略选择原则

#### 核心原则
1. **适应度原则**: 
   - 顶级个体(前10%): 偏向开发策略，保持优势
   - 优秀个体(前30%): 平衡策略，稳步改进  
   - 普通个体(后70%): 偏向探索策略，寻找突破

2. **停滞原则**:
   - 无停滞(0代): 继续当前策略方向
   - 轻微停滞(1-2代): 增加探索成分
   - 中等停滞(3-5代): 转向探索策略
   - 严重停滞(6-10代): 强探索策略
   - 极度停滞(>10代): 重启或协作逃逸

3. **景观适应原则**:
   - 高粗糙度(>0.7): 增强探索，避免局部陷阱
   - 低多样性(<0.3): 强化探索，增加种群多样性
   - 高收敛(>0.7): 精细开发，深度优化
   - 强梯度(>0.6): 利用梯度信息，智能探索

4. **资源分配原则**:
   - 高优先级个体: 分配更多计算资源
   - 平衡探索开发: 确保30-70%的探索开发比例
   - 协调避免冲突: 避免所有个体同时探索或开发

### 输出格式要求

请严格按照以下JSON格式输出策略分配结果：

```json
{{
  "strategy_assignments": [
    {{
      "individual_id": 0,
      "strategy_type": "moderate_exploitation",
      "confidence": 0.85,
      "reasoning": "顶级个体(百分位0.05)且无停滞，景观梯度强(0.75)，适合中等开发策略深度优化",
      "priority": 0.9,
      "expected_improvement": 0.15,
      "parameters": {{
        "local_search_depth": 3,
        "elite_influence": 0.8,
        "perturbation_strength": 0.2
      }}
    }},
    {{
      "individual_id": 1,
      "strategy_type": "balanced_exploration", 
      "confidence": 0.72,
      "reasoning": "中等个体(百分位0.45)存在轻微停滞(3代)，多样性贡献高(0.68)，需要平衡探索保持多样性",
      "priority": 0.6,
      "expected_improvement": 0.08,
      "parameters": {{
        "exploration_radius": 0.2,
        "diversification_strength": 0.6,
        "random_component": 0.3
      }}
    }}
  ],
  "global_analysis": {{
    "exploration_ratio": 0.65,
    "exploitation_ratio": 0.35,
    "key_insights": "种群多样性较低(0.28)且存在多个停滞个体，需要增强探索策略。景观粗糙度中等(0.52)，可以采用智能探索。",
    "risk_assessment": "中等风险：部分个体存在停滞，但顶级个体表现稳定。建议加强探索的同时保护精英解。",
    "coordination_strategy": "采用分层策略：前20%个体专注开发，中间30%平衡策略，后50%强化探索。",
    "resource_allocation": "高优先级个体分配60%计算资源，确保精英解持续优化。"
  }}
}}
```

### 重要提醒
1. 每个个体都必须分配策略，individual_id必须连续从0开始
2. confidence值应在0.1-1.0之间，反映策略选择的确定性
3. reasoning应具体说明选择该策略的关键因素(适应度、停滞、景观特征等)
4. priority值应在0.1-1.0之间，用于资源分配
5. expected_improvement是预期的相对改进幅度
6. parameters根据策略类型提供具体的执行参数

请基于以上分析为每个个体分配最优策略。"""


def generate_individual_strategy_prompt(individual_features: List[Dict], 
                                       landscape_context: Dict,
                                       iteration: int,
                                       feedback_summary: str = "无历史反馈") -> str:
    """
    生成个体级策略选择提示词
    
    Args:
        individual_features: 个体特征列表
        landscape_context: 景观上下文信息
        iteration: 当前迭代次数
        feedback_summary: 反馈摘要
        
    Returns:
        格式化的提示词字符串
    """
    
    # 构建个体详细信息
    individual_details = ""
    for i, features in enumerate(individual_features):
        individual_details += f"""
个体 {features['individual_id']}:
- 适应度: {features['fitness_value']:.2f} (排名: {features['fitness_rank']}, 百分位: {features['fitness_percentile']:.3f})
- 停滞状态: {features['stagnation_duration']}代 ({features['stagnation_level']})
- 多样性贡献: {features['diversity_contribution']:.3f}
- 与最优解距离: {features['distance_to_best']:.2f}
- 近期改进: {features['recent_improvements']}次
- 历史成功策略: {', '.join(features.get('preferred_strategies', ['无']))}
"""
    
    # 格式化提示词
    prompt = INDIVIDUAL_STRATEGY_PROMPT_TEMPLATE.format(
        iteration=iteration,
        global_ruggedness=landscape_context.get('global_ruggedness', 0.5),
        modality=landscape_context.get('modality', '未知'),
        deceptiveness=landscape_context.get('deceptiveness', '未知'),
        gradient_strength=landscape_context.get('gradient_strength', 0.5),
        population_diversity=landscape_context.get('population_diversity', 0.5),
        convergence_trend=landscape_context.get('convergence_trend', 0.5),
        evolution_phase=landscape_context.get('evolution_phase', 'exploration'),
        difficult_regions_count=len(landscape_context.get('difficult_regions', [])),
        opportunity_regions_count=len(landscape_context.get('opportunity_regions', [])),
        feedback_summary=feedback_summary,
        population_size=len(individual_features),
        individual_details=individual_details
    )
    
    return prompt


def parse_strategy_response(response: str, population_size: int) -> Dict[str, Any]:
    """
    解析LLM的策略分配响应
    
    Args:
        response: LLM响应文本
        population_size: 种群大小
        
    Returns:
        解析后的策略分配字典
    """
    try:
        # 提取JSON部分
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_match:
            raise ValueError("未找到JSON格式的响应")
        
        json_str = json_match.group()
        data = json.loads(json_str)
        
        # 验证必要字段
        if 'strategy_assignments' not in data:
            raise ValueError("缺少strategy_assignments字段")
        
        assignments = data['strategy_assignments']
        
        # 验证个体数量
        if len(assignments) != population_size:
            raise ValueError(f"策略分配数量({len(assignments)})与种群大小({population_size})不匹配")
        
        # 验证个体ID连续性
        assigned_ids = {assignment.get('individual_id') for assignment in assignments}
        expected_ids = set(range(population_size))
        if assigned_ids != expected_ids:
            raise ValueError(f"个体ID不连续或缺失: 期望{expected_ids}, 实际{assigned_ids}")
        
        return data
        
    except json.JSONDecodeError as e:
        raise ValueError(f"JSON解析错误: {str(e)}")
    except Exception as e:
        raise ValueError(f"响应解析失败: {str(e)}")


def validate_strategy_assignment(assignment: Dict) -> bool:
    """
    验证单个策略分配的有效性
    
    Args:
        assignment: 策略分配字典
        
    Returns:
        是否有效
    """
    required_fields = ['individual_id', 'strategy_type', 'confidence', 'reasoning']
    
    # 检查必要字段
    for field in required_fields:
        if field not in assignment:
            return False
    
    # 检查置信度范围
    confidence = assignment.get('confidence', 0)
    if not (0.1 <= confidence <= 1.0):
        return False
    
    # 检查优先级范围
    priority = assignment.get('priority', 0.5)
    if not (0.1 <= priority <= 1.0):
        return False
    
    # 检查策略类型
    valid_strategies = [
        'strong_exploration', 'balanced_exploration', 'intelligent_exploration',
        'cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation',
        'intensive_exploitation', 'adaptive_hybrid', 'collaborative_escape'
    ]
    
    if assignment.get('strategy_type') not in valid_strategies:
        return False
    
    return True
