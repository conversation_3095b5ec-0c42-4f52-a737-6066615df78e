# -*- coding: utf-8 -*-
"""
适应度景观3D可视化系统

基于Plotly和Dash实现的实时交互式3D可视化系统，支持动态更新、
交互功能和自动保存。
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output, State
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
import time
import json
import os
import copy
from datetime import datetime
import threading
import queue
from sklearn.decomposition import PCA
from scipy.interpolate import griddata

# 导入增强采样点管理器
from .sampling_point import SamplingPointManager, SamplingPoint, SamplingPointType

logger = logging.getLogger(__name__)


class LandscapeVisualizer:
    """增强的适应度景观3D可视化器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化可视化器

        参数:
            config: 配置参数
        """
        self.config = config or {}

        # 可视化参数
        visualization_config = self.config.get('visualization_config', {})
        self.grid_size = visualization_config.get('grid_size', 50)
        self.update_interval = visualization_config.get('update_interval', 1000)  # ms
        self.auto_save = visualization_config.get('auto_save', True)
        self.save_dir = visualization_config.get('save_dir', 'visualization_output')

        # 增强采样配置
        self.enhanced_sampling_config = visualization_config.get('enhanced_sampling', {})
        self.enhanced_sampling_enabled = self.enhanced_sampling_config.get('enabled', False)

        # 数据存储
        self.landscape_data = {}
        self.history_data = []
        self.current_iteration = 0

        # 增强采样点管理器
        if self.enhanced_sampling_enabled:
            sampling_config = {
                **self.enhanced_sampling_config.get('historical_data_fusion', {}),
                **self.config.get('performance_config', {}).get('sampling_optimization', {})
            }
            self.sampling_manager = SamplingPointManager(sampling_config)
        else:
            self.sampling_manager = None

        # PCA降维器（用于一致的坐标映射）
        self.pca_reducer = None
        self.coordinate_cache = {}

        # Dash应用
        self.app = None
        self.server_thread = None
        self.data_queue = queue.Queue()

        # 当前实例名（用于文件保存）
        self.current_instance_name = None

        # 确保输出目录存在
        os.makedirs(self.save_dir, exist_ok=True)

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"适应度景观3D可视化器初始化完成 (增强采样: {self.enhanced_sampling_enabled})")

    def set_current_instance(self, instance_name: str):
        """
        设置当前处理的实例名称

        参数:
            instance_name: 实例名称
        """
        self.current_instance_name = instance_name
        self.logger.info(f"设置当前实例名: {instance_name}")

    def create_landscape_surface(self, populations: List[List[int]],
                               fitness_values: List[float],
                               analysis_results: Optional[Dict[str, Any]] = None,
                               elite_solutions: List[Dict] = None) -> go.Figure:
        """
        创建适应度景观3D表面图

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            analysis_results: 分析结果
            elite_solutions: 精英解集合

        返回:
            Plotly图形对象
        """
        try:
            # 创建网格数据
            x_grid, y_grid, z_grid = self._create_landscape_grid(
                populations, fitness_values
            )

            # 创建3D表面图
            fig = go.Figure()

            # 添加主要景观表面
            fig.add_trace(go.Surface(
                x=x_grid,
                y=y_grid,
                z=z_grid,
                colorscale='Viridis',
                name='适应度景观',
                showscale=True,
                colorbar=dict(title="适应度值")
            ))

            # 添加种群个体点
            if len(populations) > 0:
                x_pop, y_pop = self._map_populations_to_2d_enhanced(populations)
                fig.add_trace(go.Scatter3d(
                    x=x_pop,
                    y=y_pop,
                    z=fitness_values,
                    mode='markers',
                    marker=dict(
                        size=5,
                        color=fitness_values,
                        colorscale='Reds',
                        showscale=False
                    ),
                    name='种群个体',
                    text=[f'个体{i}: {f:.2f}' for i, f in enumerate(fitness_values)],
                    hovertemplate='%{text}<extra></extra>'
                ))

            # 添加精英解点 - 已禁用特殊显示
            # if elite_solutions:
            #     self._add_elite_solution_markers(fig, elite_solutions)

            # 添加局部最优点
            if analysis_results and 'local_optima' in analysis_results:
                self._add_local_optima_markers(fig, analysis_results, populations, fitness_values)

            # 设置布局
            fig.update_layout(
                title=f'适应度景观3D可视化 - 第{self.current_iteration}代',
                scene=dict(
                    xaxis_title='维度1',
                    yaxis_title='维度2',
                    zaxis_title='适应度值',
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=800,
                height=600
            )

            return fig

        except Exception as e:
            self.logger.error(f"创建景观表面图失败: {e}")
            return self._create_empty_figure("景观表面图创建失败")

    def create_analysis_dashboard(self, analysis_result: Dict[str, Any]) -> go.Figure:
        """
        创建分析仪表板

        参数:
            analysis_result: 分析结果

        返回:
            仪表板图表
        """
        try:
            from plotly.subplots import make_subplots

            # 创建子图布局
            fig = make_subplots(
                rows=2, cols=3,
                subplot_titles=(
                    '局部最优密度', '适应度梯度分布', '聚类分析',
                    '搜索空间覆盖', '收敛趋势', '多样性指标'
                ),
                specs=[
                    [{"type": "indicator"}, {"type": "histogram"}, {"type": "scatter"}],
                    [{"type": "heatmap"}, {"type": "scatter"}, {"type": "bar"}]
                ]
            )

            # 1. 局部最优密度指示器
            local_optima = analysis_result.get('local_optima', {})
            density = local_optima.get('local_optima_density', 0.0)

            fig.add_trace(
                go.Indicator(
                    mode="gauge+number",
                    value=density,
                    title={'text': "局部最优密度"},
                    gauge={
                        'axis': {'range': [None, 1.0]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 0.3], 'color': "lightgray"},
                            {'range': [0.3, 0.7], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 0.8
                        }
                    }
                ),
                row=1, col=1
            )

            # 2. 适应度梯度分布
            gradient_data = analysis_result.get('gradient', {})
            gradient_mean = gradient_data.get('gradient_mean', 0)
            gradient_std = gradient_data.get('gradient_std', 0)

            # 生成示例梯度分布数据
            import numpy as np
            gradient_values = np.random.normal(gradient_mean, gradient_std, 100)

            fig.add_trace(
                go.Histogram(
                    x=gradient_values,
                    nbinsx=20,
                    name="梯度分布"
                ),
                row=1, col=2
            )

            # 3. 聚类分析散点图
            clustering_data = analysis_result.get('clustering', {})
            n_clusters = clustering_data.get('n_clusters', 0)

            # 生成示例聚类数据
            if n_clusters > 0:
                cluster_x = np.random.rand(n_clusters) * 10
                cluster_y = np.random.rand(n_clusters) * 10

                fig.add_trace(
                    go.Scatter(
                        x=cluster_x,
                        y=cluster_y,
                        mode='markers',
                        marker=dict(size=10, color='red'),
                        name="聚类中心"
                    ),
                    row=1, col=3
                )

            # 4. 搜索空间覆盖热图
            coverage_data = analysis_result.get('coverage', {})
            coverage_ratio = coverage_data.get('coverage_ratio', 0)

            # 生成示例覆盖网格
            grid_size = 10
            coverage_grid = np.random.rand(grid_size, grid_size) * coverage_ratio

            fig.add_trace(
                go.Heatmap(
                    z=coverage_grid,
                    colorscale='Viridis',
                    name="覆盖热图"
                ),
                row=2, col=1
            )

            # 5. 收敛趋势
            convergence_data = analysis_result.get('convergence', {})
            trend_slope = convergence_data.get('trend_slope', 0)

            # 生成示例趋势数据
            x_trend = list(range(20))
            y_trend = [i * trend_slope + np.random.normal(0, 0.1) for i in x_trend]

            fig.add_trace(
                go.Scatter(
                    x=x_trend,
                    y=y_trend,
                    mode='lines+markers',
                    name="收敛趋势"
                ),
                row=2, col=2
            )

            # 6. 多样性指标柱状图
            diversity_data = analysis_result.get('diversity', {})
            metrics = ['基因型', '表型', '熵']
            values = [
                diversity_data.get('genotype_diversity', 0),
                diversity_data.get('phenotype_diversity', 0),
                diversity_data.get('entropy_diversity', 0)
            ]

            fig.add_trace(
                go.Bar(
                    x=metrics,
                    y=values,
                    name="多样性指标"
                ),
                row=2, col=3
            )

            # 更新布局
            fig.update_layout(
                height=800,
                title_text="适应度景观分析仪表板",
                showlegend=False
            )

            return fig

        except Exception as e:
            self.logger.error(f"创建分析仪表板失败: {e}")
            return self._create_empty_figure("分析仪表板创建失败")

    def start_dash_app(self, analysis_result: Dict[str, Any], port: int = 8050):
        """
        启动Dash Web应用

        参数:
            analysis_result: 分析结果
            port: 端口号
        """
        try:
            import dash
            from dash import dcc, html, Input, Output

            app = dash.Dash(__name__)

            # 创建初始图表
            landscape_fig = self.create_landscape_surface([], [])
            dashboard_fig = self.create_analysis_dashboard(analysis_result)

            # 定义应用布局
            app.layout = html.Div([
                html.H1("适应度景观分析可视化系统",
                       style={'textAlign': 'center', 'marginBottom': 30}),

                html.Div([
                    html.Div([
                        html.H3("3D适应度景观"),
                        dcc.Graph(id='landscape-graph', figure=landscape_fig)
                    ], style={'width': '48%', 'display': 'inline-block'}),

                    html.Div([
                        html.H3("分析仪表板"),
                        dcc.Graph(id='dashboard-graph', figure=dashboard_fig)
                    ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'})
                ]),

                html.Div([
                    html.H3("控制面板"),
                    html.Button('更新数据', id='update-button', n_clicks=0),
                    html.Button('保存图表', id='save-button', n_clicks=0),
                    html.Div(id='status-output', style={'marginTop': 20})
                ], style={'marginTop': 30, 'textAlign': 'center'})
            ])

            # 定义回调函数
            @app.callback(
                [Output('landscape-graph', 'figure'),
                 Output('dashboard-graph', 'figure'),
                 Output('status-output', 'children')],
                [Input('update-button', 'n_clicks'),
                 Input('save-button', 'n_clicks')]
            )
            def update_graphs(update_clicks, save_clicks):
                ctx = dash.callback_context
                if not ctx.triggered:
                    return landscape_fig, dashboard_fig, "系统就绪"

                button_id = ctx.triggered[0]['prop_id'].split('.')[0]

                if button_id == 'update-button':
                    # 更新图表逻辑
                    new_landscape = self.create_landscape_surface([], [])
                    new_dashboard = self.create_analysis_dashboard(analysis_result)
                    return new_landscape, new_dashboard, f"数据已更新 (点击次数: {update_clicks})"

                elif button_id == 'save-button':
                    # 保存图表逻辑 - 优先使用当前实例名，然后从配置获取，最后使用默认值
                    instance_name = (self.current_instance_name or
                                   self.config.get('current_instance_name', 'default'))
                    self.save_visualization(landscape_fig, f"landscape_{save_clicks}", instance_name)
                    self.save_visualization(dashboard_fig, f"dashboard_{save_clicks}", instance_name)
                    return landscape_fig, dashboard_fig, f"图表已保存 (保存次数: {save_clicks})"

                return landscape_fig, dashboard_fig, "系统就绪"

            # 启动应用
            self.logger.info(f"启动Dash应用，端口: {port}")
            app.run_server(debug=False, port=port, host='127.0.0.1')

        except Exception as e:
            self.logger.error(f"启动Dash应用失败: {e}")

    def save_visualization(self, fig: go.Figure, filename: str, instance_name: str = None):
        """
        保存可视化图表到实例专用文件夹

        参数:
            fig: 图表对象
            filename: 文件名
            instance_name: TSP实例名称，用于创建子文件夹
        """
        try:
            import os

            # 获取文件组织配置
            file_org_config = self.config.get('visualization_config', {}).get('file_organization', {})
            use_instance_folders = file_org_config.get('use_instance_folders', True)
            folder_naming_format = file_org_config.get('folder_naming_format', 'instance_{instance_name}')
            auto_create_folders = file_org_config.get('auto_create_folders', True)

            # 确定保存目录
            if instance_name and use_instance_folders:
                # 为每个TSP实例创建独立的子文件夹
                if '{instance_name}' in folder_naming_format:
                    folder_name = folder_naming_format.format(instance_name=instance_name)
                else:
                    folder_name = f"instance_{instance_name}"
                instance_dir = os.path.join(self.save_dir, folder_name)
                save_path = instance_dir
            else:
                # 如果没有实例名或不使用实例文件夹，使用默认目录
                save_path = self.save_dir

            # 确保保存目录存在
            if auto_create_folders:
                os.makedirs(save_path, exist_ok=True)

            # 保存为HTML
            html_path = os.path.join(save_path, f"{filename}.html")
            fig.write_html(html_path)

            # 保存为PNG（如果kaleido可用）
            try:
                png_path = os.path.join(save_path, f"{filename}.png")
                fig.write_image(png_path)
                self.logger.info(f"图表已保存到实例文件夹: {html_path}, {png_path}")
            except Exception:
                self.logger.info(f"图表已保存到实例文件夹: {html_path}")

        except Exception as e:
            self.logger.error(f"保存可视化图表失败: {e}")

    def update_visualization(self, populations: List[List[int]],
                           fitness_values: List[float],
                           analysis_result: Dict[str, Any],
                           elite_solutions: List[Dict] = None,
                           instance_name: str = None,
                           intermediate_solutions: List[Dict] = None):
        """
        增强的可视化数据更新，支持增量更新和多类型采样点

        参数:
            populations: 种群数据
            fitness_values: 适应度值
            analysis_result: 分析结果
            elite_solutions: 精英解集合
            instance_name: 实例名称
            intermediate_solutions: 中间解集合（新增）
        """
        self.current_iteration += 1

        # 如果启用增强采样，更新采样点管理器
        if self.enhanced_sampling_enabled and self.sampling_manager:
            self._update_sampling_points(populations, fitness_values,
                                       elite_solutions, intermediate_solutions)

        # 增量更新逻辑
        incremental_config = self.enhanced_sampling_config.get('incremental_updates', {})
        should_full_update = self._should_perform_full_update(incremental_config)

        if should_full_update:
            # 执行完整更新
            landscape_fig = self.create_enhanced_landscape_surface(
                populations, fitness_values, analysis_result, elite_solutions)
            self.logger.debug("执行完整景观表面更新")
        else:
            # 执行增量更新
            landscape_fig = self._perform_incremental_landscape_update(
                populations, fitness_values, analysis_result, elite_solutions)
            self.logger.debug("执行增量景观表面更新")

        # 更新分析仪表板（通常较轻量，每次都更新）
        dashboard_fig = self.create_analysis_dashboard(analysis_result)

        # 定期内存清理 - 已禁用
        # 注释掉内存清理调用以保留所有历史数据
        # if (incremental_config.get('enabled', False) and
        #     self.current_iteration % incremental_config.get('memory_cleanup_interval', 10) == 0):
        #     self._perform_memory_cleanup()

        # 自动保存（如果启用）
        if self.auto_save:
            save_interval = self.config.get('save_interval', 1)
            if self.current_iteration % save_interval == 0:
                # 使用传入的实例名，如果没有则使用当前设置的实例名
                effective_instance_name = instance_name or self.current_instance_name
                landscape_filename = self._generate_filename("landscape", effective_instance_name)
                dashboard_filename = self._generate_filename("dashboard", effective_instance_name)

                self.save_visualization(landscape_fig, landscape_filename, effective_instance_name)
                self.save_visualization(dashboard_fig, dashboard_filename, effective_instance_name)

        # 记录统计信息
        if self.enhanced_sampling_enabled and self.sampling_manager:
            stats = self.sampling_manager.get_statistics()
            self.logger.info(f"可视化已更新 (迭代 {self.current_iteration}): "
                           f"总采样点={stats['total_points']}, "
                           f"当前种群={stats['points_by_type'].get('current_population', 0)}, "
                           f"历史精英={stats['points_by_type'].get('historical_elite', 0)}")
        else:
            self.logger.info(f"可视化已更新，当前迭代: {self.current_iteration}")

        return landscape_fig, dashboard_fig

    def _should_perform_full_update(self, incremental_config: Dict[str, Any]) -> bool:
        """
        判断是否应该执行完整更新

        参数:
            incremental_config: 增量更新配置

        返回:
            是否执行完整更新
        """
        if not incremental_config.get('enabled', False):
            return True

        # 强制完整更新的条件
        full_update_interval = incremental_config.get('full_update_interval', 20)
        if self.current_iteration % full_update_interval == 0:
            return True

        # 如果采样点数量变化较大，执行完整更新
        if self.enhanced_sampling_enabled and self.sampling_manager:
            stats = self.sampling_manager.get_statistics()
            total_points = stats['total_points']

            # 检查采样点数量变化
            if not hasattr(self, '_last_total_points'):
                self._last_total_points = total_points
                return True

            point_change_ratio = abs(total_points - self._last_total_points) / max(self._last_total_points, 1)
            change_threshold = incremental_config.get('point_change_threshold', 0.3)

            if point_change_ratio > change_threshold:
                self._last_total_points = total_points
                return True

        return False

    def _perform_incremental_landscape_update(self, populations: List[List[int]],
                                            fitness_values: List[float],
                                            analysis_results: Optional[Dict[str, Any]] = None,
                                            elite_solutions: List[Dict] = None) -> go.Figure:
        """
        执行增量景观更新

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            analysis_results: 分析结果
            elite_solutions: 精英解集合

        返回:
            Plotly图形对象
        """
        try:
            # 如果没有缓存的图形，执行完整更新
            if not hasattr(self, '_cached_landscape_fig') or self._cached_landscape_fig is None:
                return self.create_enhanced_landscape_surface(
                    populations, fitness_values, analysis_results, elite_solutions)

            # 复制缓存的图形
            fig = copy.deepcopy(self._cached_landscape_fig)

            # 更新标题
            fig.update_layout(title=f'增强适应度景观3D可视化 - 第{self.current_iteration}代')

            # 增量更新采样点
            if self.enhanced_sampling_enabled and self.sampling_manager:
                # 移除旧的采样点轨迹
                fig.data = [trace for trace in fig.data if trace.name == '适应度景观']

                # 添加更新的分层采样点
                self._add_layered_sampling_points(fig)
            else:
                # 传统方式的增量更新
                self._update_traditional_points_incremental(fig, populations, fitness_values, elite_solutions)

            # 缓存更新后的图形
            self._cached_landscape_fig = copy.deepcopy(fig)

            return fig

        except Exception as e:
            self.logger.error(f"增量景观更新失败: {e}")
            # 回退到完整更新
            return self.create_enhanced_landscape_surface(
                populations, fitness_values, analysis_results, elite_solutions)

    def _update_traditional_points_incremental(self, fig: go.Figure,
                                             populations: List[List[int]],
                                             fitness_values: List[float],
                                             elite_solutions: List[Dict] = None):
        """
        传统方式的增量点更新

        参数:
            fig: Plotly图形对象
            populations: 种群个体
            fitness_values: 适应度值
            elite_solutions: 精英解集合
        """
        try:
            # 移除旧的点轨迹
            fig.data = [trace for trace in fig.data if trace.name == '适应度景观']

            # 添加新的点
            self._add_traditional_points(fig, populations, fitness_values, elite_solutions)

        except Exception as e:
            self.logger.error(f"传统增量点更新失败: {e}")

    def _update_sampling_points(self, populations: List[List[int]],
                              fitness_values: List[float],
                              elite_solutions: List[Dict] = None,
                              intermediate_solutions: List[Dict] = None):
        """
        更新采样点管理器

        参数:
            populations: 当前种群
            fitness_values: 适应度值
            elite_solutions: 精英解集合
            intermediate_solutions: 中间解集合
        """
        try:
            # 添加当前种群采样点
            if populations and fitness_values:
                self.sampling_manager.add_current_population(
                    populations, fitness_values, self.current_iteration)

            # 添加精英解采样点
            if elite_solutions:
                for elite in elite_solutions:
                    if 'tour' in elite and 'cur_cost' in elite:
                        self.sampling_manager.add_elite_solution(
                            elite['tour'], elite['cur_cost'],
                            self.current_iteration, {'source': 'elite_collection'})

            # 添加中间解采样点
            if intermediate_solutions:
                for intermediate in intermediate_solutions:
                    if 'tour' in intermediate and 'cur_cost' in intermediate:
                        strategy_type = intermediate.get('strategy_type', 'unknown')
                        self.sampling_manager.add_intermediate_solution(
                            intermediate['tour'], intermediate['cur_cost'],
                            self.current_iteration, strategy_type)

        except Exception as e:
            self.logger.error(f"更新采样点失败: {e}")

    def _perform_memory_cleanup(self):
        """
        执行智能内存清理 - 已禁用

        注意：为了保留完整的进化历史轨迹，内存清理功能已被禁用。
        所有采样点、缓存和图形数据将被永久保留。
        """
        # 禁用内存清理机制 - 保留所有历史数据
        # 原有的清理逻辑已被注释掉以确保数据完整性

        try:
            # 仅记录当前状态，不执行任何清理操作
            current_stats = {
                'sampling_points': 0,
                'coordinate_cache_size': len(self.coordinate_cache),
                'has_figure_cache': hasattr(self, '_cached_landscape_fig') and self._cached_landscape_fig is not None
            }

            if self.sampling_manager:
                stats = self.sampling_manager.get_statistics()
                current_stats['sampling_points'] = stats['total_points']

            # 记录禁用状态
            self.logger.debug(f"内存清理已禁用 - 当前状态: 采样点={current_stats['sampling_points']}, "
                            f"坐标缓存={current_stats['coordinate_cache_size']}, "
                            f"图形缓存={'是' if current_stats['has_figure_cache'] else '否'}")

            # 原有清理逻辑已被完全禁用：
            # - 不删除任何采样点
            # - 不清理坐标缓存
            # - 不清理图形缓存
            # - 保留完整的进化历史轨迹

        except Exception as e:
            self.logger.error(f"内存状态检查失败: {e}")

    def _should_clear_figure_cache(self) -> bool:
        """判断是否应该清理图形缓存"""
        try:
            # 检查内存使用情况
            import psutil
            memory_percent = psutil.virtual_memory().percent

            # 如果内存使用超过80%，清理图形缓存
            if memory_percent > 80:
                return True

            # 如果缓存的图形过旧（超过50次迭代），清理缓存
            if (hasattr(self, '_cache_creation_iteration') and
                self.current_iteration - self._cache_creation_iteration > 50):
                return True

            return False

        except ImportError:
            # 如果没有psutil，基于迭代次数判断
            return (hasattr(self, '_cache_creation_iteration') and
                   self.current_iteration - self._cache_creation_iteration > 30)
        except Exception:
            return False

    def _get_memory_usage_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        try:
            stats = {
                'sampling_points': 0,
                'coordinate_cache_size': len(self.coordinate_cache),
                'has_figure_cache': hasattr(self, '_cached_landscape_fig') and self._cached_landscape_fig is not None
            }

            if self.sampling_manager:
                manager_stats = self.sampling_manager.get_statistics()
                stats['sampling_points'] = manager_stats['total_points']
                stats['points_by_type'] = manager_stats['points_by_type']

            try:
                import psutil
                stats['system_memory_percent'] = psutil.virtual_memory().percent
            except ImportError:
                stats['system_memory_percent'] = None

            return stats

        except Exception as e:
            self.logger.error(f"获取内存统计失败: {e}")
            return {}

    def create_enhanced_landscape_surface(self, populations: List[List[int]],
                                        fitness_values: List[float],
                                        analysis_results: Optional[Dict[str, Any]] = None,
                                        elite_solutions: List[Dict] = None) -> go.Figure:
        """
        创建增强的适应度景观3D表面图，支持分层显示

        参数:
            populations: 种群个体
            fitness_values: 适应度值
            analysis_results: 分析结果
            elite_solutions: 精英解集合

        返回:
            Plotly图形对象
        """
        try:
            # 创建网格数据（包含历史数据融合）
            x_grid, y_grid, z_grid = self._create_landscape_grid(
                populations, fitness_values
            )

            # 创建3D表面图
            fig = go.Figure()

            # 添加主要景观表面
            fig.add_trace(go.Surface(
                x=x_grid,
                y=y_grid,
                z=z_grid,
                colorscale='Viridis',
                name='适应度景观',
                showscale=True,
                colorbar=dict(title="适应度值", x=1.02)
            ))

            # 添加分层采样点
            if self.enhanced_sampling_enabled and self.sampling_manager:
                self._add_layered_sampling_points(fig)
            else:
                # 回退到传统显示方式
                self._add_traditional_points(fig, populations, fitness_values, elite_solutions)

            # 设置布局
            fig.update_layout(
                title=f'增强适应度景观3D可视化 - 第{self.current_iteration}代',
                scene=dict(
                    xaxis_title='维度1',
                    yaxis_title='维度2',
                    zaxis_title='适应度值',
                    camera=dict(eye=dict(x=1.5, y=1.5, z=1.5))
                ),
                width=900,
                height=700,
                showlegend=True
            )

            # 缓存图形用于增量更新
            self._cached_landscape_fig = copy.deepcopy(fig)
            self._cache_creation_iteration = self.current_iteration

            return fig

        except Exception as e:
            self.logger.error(f"创建增强景观表面图失败: {e}")
            return self._create_empty_figure("增强景观表面图创建失败")

    def _add_layered_sampling_points(self, fig: go.Figure):
        """
        添加分层采样点显示

        参数:
            fig: Plotly图形对象
        """
        try:
            layered_config = self.enhanced_sampling_config.get('layered_visualization', {})
            if not layered_config.get('enabled', True):
                return

            # 获取所有采样点
            points_by_type = self.sampling_manager.get_points_for_visualization()

            # 获取点样式配置
            point_styles = layered_config.get('point_styles', {})

            # 添加当前种群点
            if (layered_config.get('show_current_population', True) and
                'current_population' in points_by_type):
                self._add_points_by_type(fig, points_by_type['current_population'],
                                       '当前种群', point_styles.get('current_population', {}))

            # 添加历史精英解点
            if (layered_config.get('show_historical_elite', True) and
                'historical_elite' in points_by_type):
                self._add_points_by_type(fig, points_by_type['historical_elite'],
                                       '历史精英解', point_styles.get('historical_elite', {}))

            # 添加中间解点
            if (layered_config.get('show_intermediate_solutions', True) and
                'intermediate_solution' in points_by_type):
                self._add_points_by_type(fig, points_by_type['intermediate_solution'],
                                       '中间解', point_styles.get('intermediate_solutions', {}))

            # 添加局部最优点
            if (layered_config.get('show_local_optima', True) and
                'local_optima' in points_by_type):
                self._add_points_by_type(fig, points_by_type['local_optima'],
                                       '局部最优', point_styles.get('local_optima', {}))

        except Exception as e:
            self.logger.error(f"添加分层采样点失败: {e}")

    def _add_points_by_type(self, fig: go.Figure, points: List[SamplingPoint],
                          name: str, style_config: Dict[str, Any]):
        """
        按类型添加采样点

        参数:
            fig: Plotly图形对象
            points: 采样点列表
            name: 显示名称
            style_config: 样式配置
        """
        try:
            if not points:
                return

            # 提取解和适应度
            solutions = [point.solution for point in points]
            fitness_values = [point.fitness for point in points]

            # 映射到2D坐标
            x_coords, y_coords = self._map_populations_to_2d_enhanced(solutions)

            if not x_coords or not y_coords:
                return

            # 默认样式
            default_style = {
                'color': 'blue',
                'size': 5,
                'symbol': 'circle',
                'opacity': 0.8
            }
            style = {**default_style, **style_config}

            # 创建悬停文本
            hover_text = []
            for i, point in enumerate(points):
                text = (f'{name} {i}<br>'
                       f'适应度: {point.fitness:.2f}<br>'
                       f'迭代: {point.iteration}<br>'
                       f'年龄: {point.age:.1f}s')
                if point.metadata:
                    for key, value in point.metadata.items():
                        text += f'<br>{key}: {value}'
                hover_text.append(text)

            # 添加散点图
            fig.add_trace(go.Scatter3d(
                x=x_coords,
                y=y_coords,
                z=fitness_values,
                mode='markers',
                marker=dict(
                    size=style['size'],
                    color=style['color'],
                    symbol=style['symbol'],
                    opacity=style['opacity'],
                    line=dict(width=1, color='white')
                ),
                name=name,
                text=hover_text,
                hovertemplate='%{text}<extra></extra>'
            ))

        except Exception as e:
            self.logger.error(f"添加{name}采样点失败: {e}")

    def _add_traditional_points(self, fig: go.Figure, populations: List[List[int]],
                              fitness_values: List[float], elite_solutions: List[Dict] = None):
        """
        添加传统方式的采样点（向后兼容）

        参数:
            fig: Plotly图形对象
            populations: 种群个体
            fitness_values: 适应度值
            elite_solutions: 精英解集合
        """
        try:
            # 添加种群个体点
            if len(populations) > 0:
                x_pop, y_pop = self._map_populations_to_2d_enhanced(populations)
                if x_pop and y_pop:
                    fig.add_trace(go.Scatter3d(
                        x=x_pop,
                        y=y_pop,
                        z=fitness_values,
                        mode='markers',
                        marker=dict(
                            size=5,
                            color=fitness_values,
                            colorscale='Reds',
                            showscale=False
                        ),
                        name='种群个体',
                        text=[f'个体{i}: {f:.2f}' for i, f in enumerate(fitness_values)],
                        hovertemplate='%{text}<extra></extra>'
                    ))

            # 添加精英解点 - 已禁用特殊显示
            # if elite_solutions:
            #     self._add_elite_solution_markers(fig, elite_solutions)

        except Exception as e:
            self.logger.error(f"添加传统采样点失败: {e}")

    def _generate_filename(self, file_type: str, instance_name: str = None) -> str:
        """
        生成包含实例名和时间戳的文件名

        参数:
            file_type: 文件类型 ('landscape' 或 'dashboard')
            instance_name: 实例名称

        返回:
            格式化的文件名
        """
        import datetime

        # 获取当前时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 构建文件名 - 优先使用传入的实例名，然后使用当前设置的实例名
        effective_instance_name = instance_name or self.current_instance_name
        if effective_instance_name:
            filename = f"{file_type}_{effective_instance_name}_iter_{self.current_iteration}_{timestamp}"
        else:
            filename = f"{file_type}_iter_{self.current_iteration}_{timestamp}"

        return filename

    def _create_landscape_grid(self, populations: List[List[int]],
                              fitness_values: List[float]) -> tuple:
        """
        创建增强的适应度景观网格数据，支持历史数据融合

        参数:
            populations: 当前种群数据
            fitness_values: 当前适应度值

        返回:
            (x_grid, y_grid, z_grid): 网格数据
        """
        try:
            # 收集所有采样点数据
            all_populations = []
            all_fitness_values = []

            # 添加当前种群数据
            if populations and fitness_values:
                all_populations.extend(populations)
                all_fitness_values.extend(fitness_values)

            # 如果启用增强采样，融合历史数据
            if self.enhanced_sampling_enabled and self.sampling_manager:
                historical_points = self._get_historical_sampling_points()
                if historical_points:
                    hist_populations, hist_fitness = historical_points
                    all_populations.extend(hist_populations)
                    all_fitness_values.extend(hist_fitness)

            if not all_populations or not all_fitness_values:
                # 创建默认网格
                x = np.linspace(0, 10, self.grid_size)
                y = np.linspace(0, 10, self.grid_size)
                x_grid, y_grid = np.meshgrid(x, y)
                z_grid = np.zeros_like(x_grid)
                return x_grid, y_grid, z_grid

            # 获取所有采样点的2D坐标
            x_all, y_all = self._map_populations_to_2d_enhanced(all_populations)

            if not x_all or not y_all:
                # 创建默认网格
                x = np.linspace(0, 10, self.grid_size)
                y = np.linspace(0, 10, self.grid_size)
                x_grid, y_grid = np.meshgrid(x, y)
                z_grid = np.zeros_like(x_grid)
                return x_grid, y_grid, z_grid

            # 确定网格范围
            x_min, x_max = min(x_all), max(x_all)
            y_min, y_max = min(y_all), max(y_all)

            # 扩展范围以获得更好的可视化效果
            x_range = x_max - x_min
            y_range = y_max - y_min
            x_min -= x_range * 0.1
            x_max += x_range * 0.1
            y_min -= y_range * 0.1
            y_max += y_range * 0.1

            # 创建网格
            x = np.linspace(x_min, x_max, self.grid_size)
            y = np.linspace(y_min, y_max, self.grid_size)
            x_grid, y_grid = np.meshgrid(x, y)

            # 使用插值创建表面 - 改进插值方法和边界处理
            points = np.column_stack((x_all, y_all))

            # 尝试使用cubic插值，如果失败则回退到linear
            try:
                z_grid = griddata(
                    points, all_fitness_values, (x_grid, y_grid),
                    method='cubic', fill_value=np.nan
                )
                # 处理NaN值，使用linear插值填充
                nan_mask = np.isnan(z_grid)
                if np.any(nan_mask):
                    z_grid_linear = griddata(
                        points, all_fitness_values, (x_grid, y_grid),
                        method='linear', fill_value=np.mean(all_fitness_values)
                    )
                    z_grid[nan_mask] = z_grid_linear[nan_mask]
            except:
                # 回退到linear插值
                z_grid = griddata(
                    points, all_fitness_values, (x_grid, y_grid),
                    method='linear', fill_value=np.mean(all_fitness_values)
                )

            # 应用插值结果约束，防止出现低于实际最小值的虚假区域
            min_actual_fitness = min(all_fitness_values)
            max_actual_fitness = max(all_fitness_values)

            # 约束插值结果在合理范围内
            z_grid = np.clip(z_grid, min_actual_fitness, max_actual_fitness * 1.1)

            # 记录约束应用情况
            constrained_points = np.sum(z_grid == min_actual_fitness)
            if constrained_points > 0:
                self.logger.info(f"插值约束: {constrained_points} 个点被约束到最小值 {min_actual_fitness:.2f}")

            # 应用表面平滑处理
            z_grid = self._apply_surface_smoothing(z_grid, all_fitness_values)

            return x_grid, y_grid, z_grid

        except Exception as e:
            self.logger.error(f"创建增强景观网格失败: {e}")
            # 返回默认网格
            x = np.linspace(0, 10, self.grid_size)
            y = np.linspace(0, 10, self.grid_size)
            x_grid, y_grid = np.meshgrid(x, y)
            z_grid = np.zeros_like(x_grid)
            return x_grid, y_grid, z_grid

    def _get_historical_sampling_points(self) -> Optional[Tuple[List[List[int]], List[float]]]:
        """
        获取历史采样点数据

        返回:
            (历史种群, 历史适应度值) 或 None
        """
        if not self.sampling_manager:
            return None

        try:
            # 获取历史精英解和中间解
            elite_points = self.sampling_manager.get_points_by_type(SamplingPointType.HISTORICAL_ELITE)
            intermediate_points = self.sampling_manager.get_points_by_type(SamplingPointType.INTERMEDIATE_SOLUTION)

            historical_populations = []
            historical_fitness = []

            # 添加精英解
            for point in elite_points:
                historical_populations.append(point.solution)
                historical_fitness.append(point.fitness)

            # 添加部分中间解（质量筛选）
            config = self.enhanced_sampling_config.get('intermediate_solution_collection', {})
            if config.get('quality_filter_enabled', True):
                # 只保留质量较好的中间解
                min_percentile = config.get('min_quality_percentile', 0.3)
                if intermediate_points:
                    fitness_threshold = np.percentile([p.fitness for p in intermediate_points],
                                                    min_percentile * 100)
                    filtered_intermediate = [p for p in intermediate_points
                                           if p.fitness <= fitness_threshold]
                else:
                    filtered_intermediate = []
            else:
                filtered_intermediate = intermediate_points

            for point in filtered_intermediate:
                historical_populations.append(point.solution)
                historical_fitness.append(point.fitness)

            if historical_populations:
                self.logger.debug(f"融合历史采样点: 精英解={len(elite_points)}, 中间解={len(filtered_intermediate)}")
                return historical_populations, historical_fitness
            else:
                return None

        except Exception as e:
            self.logger.error(f"获取历史采样点失败: {e}")
            return None

    def _map_populations_to_2d_enhanced(self, populations: List[List[int]]) -> tuple:
        """
        增强的种群2D映射，支持一致的坐标系统

        参数:
            populations: 种群数据

        返回:
            (x_coords, y_coords): 2D坐标
        """
        try:
            if not populations:
                return [], []

            # 转换为numpy数组
            pop_array = np.array(populations)

            # 检查样本数量
            if pop_array.shape[0] < 2:
                self.logger.warning(f"样本数量不足({pop_array.shape[0]})，无法进行PCA降维")
                if pop_array.shape[0] == 1:
                    return [0.0], [0.0]
                else:
                    return [], []

            # 确定PCA组件数量
            n_components = min(2, pop_array.shape[1], pop_array.shape[0])

            # 如果没有PCA降维器或需要重新训练
            if self.pca_reducer is None or pop_array.shape[1] != self.pca_reducer.n_features_in_:
                self.pca_reducer = PCA(n_components=n_components)

                # 收集更多数据用于PCA训练，提高稳定性
                training_data = pop_array.copy()

                # 如果有历史数据，融合用于训练
                if self.enhanced_sampling_enabled and self.sampling_manager:
                    historical_points = self._get_historical_sampling_points()
                    if historical_points:
                        hist_populations, _ = historical_points
                        if hist_populations:
                            hist_array = np.array(hist_populations)
                            if hist_array.shape[1] == pop_array.shape[1]:
                                training_data = np.vstack([training_data, hist_array])

                # 使用融合数据训练PCA，提高坐标系统稳定性
                self.pca_reducer.fit(training_data)
                self.coordinate_cache.clear()  # 清空坐标缓存
                self.logger.debug(f"重新训练PCA降维器: {pop_array.shape[1]}D -> {n_components}D (训练样本: {training_data.shape[0]})")

            # 应用PCA降维
            coords_2d = self.pca_reducer.transform(pop_array)

            # 提取坐标
            if coords_2d.shape[1] >= 2:
                x_coords = coords_2d[:, 0].tolist()
                y_coords = coords_2d[:, 1].tolist()
            elif coords_2d.shape[1] == 1:
                x_coords = coords_2d[:, 0].tolist()
                y_coords = [0.0] * len(x_coords)
            else:
                x_coords = [0.0] * len(populations)
                y_coords = [0.0] * len(populations)

            return x_coords, y_coords

        except Exception as e:
            self.logger.error(f"增强种群2D映射失败: {e}")
            return [], []

    def _map_populations_to_2d(self, populations: List[List[int]]) -> tuple:
        """
        将高维种群映射到2D空间用于可视化

        参数:
            populations: 种群数据

        返回:
            (x_coords, y_coords): 2D坐标
        """
        try:
            import numpy as np
            from sklearn.decomposition import PCA

            if not populations:
                return [], []

            # 转换为numpy数组
            pop_array = np.array(populations)

            # 检查样本数量，至少需要2个样本才能进行PCA
            if pop_array.shape[0] < 2:
                self.logger.warning(f"样本数量不足({pop_array.shape[0]})，无法进行PCA降维，使用默认坐标")
                if pop_array.shape[0] == 1:
                    return [0.0], [0.0]
                else:
                    return [], []

            # 使用PCA降维到2D
            if pop_array.shape[1] > 2:
                # 确保n_components不超过样本数量和特征数量的最小值
                n_components = min(2, pop_array.shape[0], pop_array.shape[1])
                pca = PCA(n_components=n_components)
                coords_2d = pca.fit_transform(pop_array)

                if n_components == 2:
                    return coords_2d[:, 0].tolist(), coords_2d[:, 1].tolist()
                elif n_components == 1:
                    return coords_2d[:, 0].tolist(), [0] * len(populations)
                else:
                    return [0] * len(populations), [0] * len(populations)
            elif pop_array.shape[1] == 2:
                return pop_array[:, 0].tolist(), pop_array[:, 1].tolist()
            else:
                # 1D情况
                return pop_array[:, 0].tolist(), [0] * len(populations)

        except Exception as e:
            self.logger.error(f"种群映射到2D失败: {e}")
            return [], []

    def _create_fitness_surface(self, x_range: tuple, y_range: tuple,
                              populations: List[List[int]],
                              fitness_values: List[float]) -> tuple:
        """
        创建适应度表面网格

        参数:
            x_range: X轴范围
            y_range: Y轴范围
            populations: 种群数据
            fitness_values: 适应度值

        返回:
            (x_grid, y_grid, z_grid): 网格数据
        """
        try:
            import numpy as np
            from scipy.interpolate import griddata

            # 创建网格
            x = np.linspace(x_range[0], x_range[1], self.grid_size)
            y = np.linspace(y_range[0], y_range[1], self.grid_size)
            x_grid, y_grid = np.meshgrid(x, y)

            if populations and fitness_values:
                # 获取种群的2D坐标 - 使用增强映射确保坐标系统一致
                x_pop, y_pop = self._map_populations_to_2d_enhanced(populations)

                if x_pop and y_pop:
                    # 使用插值创建表面
                    points = np.column_stack((x_pop, y_pop))
                    z_grid = griddata(
                        points, fitness_values, (x_grid, y_grid),
                        method='cubic', fill_value=np.mean(fitness_values)
                    )

                    # 应用插值结果约束，防止出现低于实际最小值的虚假区域
                    min_actual_fitness = min(fitness_values)
                    max_actual_fitness = max(fitness_values)
                    z_grid = np.clip(z_grid, min_actual_fitness, max_actual_fitness * 1.1)
                else:
                    # 创建示例表面
                    z_grid = self._create_example_surface(x_grid, y_grid)
            else:
                # 创建示例表面
                z_grid = self._create_example_surface(x_grid, y_grid)

            return x_grid, y_grid, z_grid

        except Exception as e:
            self.logger.error(f"创建适应度表面失败: {e}")
            # 返回简单的示例表面
            import numpy as np
            x = np.linspace(x_range[0], x_range[1], self.grid_size)
            y = np.linspace(y_range[0], y_range[1], self.grid_size)
            x_grid, y_grid = np.meshgrid(x, y)
            z_grid = self._create_example_surface(x_grid, y_grid)
            return x_grid, y_grid, z_grid

    def _create_example_surface(self, x_grid, y_grid):
        """创建示例适应度表面"""
        import numpy as np

        # 创建一个有趣的测试函数表面
        z_grid = (
            np.sin(x_grid * 0.5) * np.cos(y_grid * 0.5) * 10 +
            np.exp(-((x_grid - 5)**2 + (y_grid - 5)**2) / 10) * 20 +
            np.random.normal(0, 1, x_grid.shape) * 2
        )

        return z_grid

    def _add_local_optima_markers(self, fig: go.Figure, analysis_results: Dict[str, Any],
                                 populations: List[List[int]], fitness_values: List[float]):
        """
        添加局部最优点标记

        参数:
            fig: 图表对象
            analysis_results: 分析结果
            populations: 种群数据
            fitness_values: 适应度值
        """
        try:
            local_optima_data = analysis_results.get('local_optima', {})
            n_optima = local_optima_data.get('n_local_optima', 0)

            if n_optima > 0 and populations and fitness_values:
                # 获取种群的2D坐标 - 使用增强映射确保坐标系统一致
                x_pop, y_pop = self._map_populations_to_2d_enhanced(populations)

                if x_pop and y_pop:
                    # 找到适应度最高的几个点作为局部最优
                    import numpy as np
                    fitness_array = np.array(fitness_values)
                    top_indices = np.argsort(fitness_array)[-min(n_optima, len(fitness_values)):]

                    optima_x = [x_pop[i] for i in top_indices]
                    optima_y = [y_pop[i] for i in top_indices]
                    optima_z = [fitness_values[i] for i in top_indices]

                    # 添加局部最优点标记
                    fig.add_trace(go.Scatter3d(
                        x=optima_x,
                        y=optima_y,
                        z=optima_z,
                        mode='markers',
                        marker=dict(
                            size=10,
                            color='gold',
                            symbol='diamond',
                            line=dict(color='black', width=2)
                        ),
                        name='局部最优',
                        text=[f'局部最优{i+1}: {z:.2f}' for i, z in enumerate(optima_z)],
                        hovertemplate='%{text}<extra></extra>'
                    ))

        except Exception as e:
            self.logger.error(f"添加局部最优标记失败: {e}")

    # def _add_elite_solution_markers(self, fig: go.Figure, elite_solutions: List[Dict]):
    #     """
    #     添加精英解标记到图表中 - 已禁用
    #
    #     为了统一处理所有个体，不再对精英解进行特殊的视觉突出显示
    #     精英解数据仍然参与3D表面的插值计算，只是不再单独标记
    #
    #     参数:
    #         fig: 图表对象
    #         elite_solutions: 精英解集合
    #     """
    #     pass  # 方法已禁用

    def _create_empty_figure(self, message: str) -> go.Figure:
        """
        创建空的图表（用于错误情况）

        参数:
            message: 错误消息

        返回:
            空图表
        """
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            title="可视化错误",
            xaxis=dict(visible=False),
            yaxis=dict(visible=False)
        )
        return fig

    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return {
            "grid_size": self.grid_size,
            "update_interval": self.update_interval,
            "auto_save": self.auto_save,
            "save_dir": self.save_dir,
            "current_iteration": self.current_iteration
        }

    def set_visualization_config(self, config: Dict[str, Any]):
        """设置可视化配置"""
        self.grid_size = config.get("grid_size", self.grid_size)
        self.update_interval = config.get("update_interval", self.update_interval)
        self.auto_save = config.get("auto_save", self.auto_save)
        self.save_dir = config.get("save_dir", self.save_dir)

        self.logger.info("可视化配置已更新")

    def _apply_surface_smoothing(self, z_grid: np.ndarray, fitness_values: List[float]) -> np.ndarray:
        """
        应用表面平滑处理以改善3D可视化的平滑度，同时保持多模态特征

        参数:
            z_grid: 原始插值网格
            fitness_values: 原始适应度值列表

        返回:
            平滑处理后的网格
        """
        try:
            # 获取平滑配置
            smoothing_config = self.config.get('visualization_config', {}).get('surface_smoothing', {})

            # 检查是否启用平滑
            if not smoothing_config.get('enabled', True):
                return z_grid

            # 获取平滑方法
            smoothing_method = smoothing_config.get('method', 'gaussian_filter')

            # 记录原始表面质量
            original_quality = self._calculate_surface_quality(z_grid)

            # 检查是否启用多模态保持
            preserve_multimodal = smoothing_config.get('preserve_multimodal', True)

            if smoothing_method == 'gaussian_filter':
                if preserve_multimodal:
                    z_grid_smooth = self._apply_multimodal_preserving_smoothing(z_grid, smoothing_config)
                else:
                    z_grid_smooth = self._apply_gaussian_smoothing(z_grid, smoothing_config)
            elif smoothing_method == 'adaptive':
                z_grid_smooth = self._apply_adaptive_smoothing(z_grid, smoothing_config)
            elif smoothing_method == 'edge_preserving':
                z_grid_smooth = self._apply_edge_preserving_smoothing(z_grid, smoothing_config)
            elif smoothing_method == 'rbf':
                z_grid_smooth = self._apply_rbf_smoothing(z_grid, smoothing_config)
            else:
                self.logger.warning(f"未知的平滑方法: {smoothing_method}，使用多模态保持平滑")
                z_grid_smooth = self._apply_multimodal_preserving_smoothing(z_grid, smoothing_config)

            # 重新应用约束以确保平滑后的值仍在合理范围内
            if smoothing_config.get('preserve_constraints', True):
                min_fitness = min(fitness_values)
                max_fitness = max(fitness_values)
                z_grid_smooth = np.clip(z_grid_smooth, min_fitness, max_fitness * 1.1)

            # 记录平滑后的表面质量
            smooth_quality = self._calculate_surface_quality(z_grid_smooth)

            # 记录平滑效果
            improvement = ((original_quality['mean_gradient'] - smooth_quality['mean_gradient']) /
                          original_quality['mean_gradient'] * 100)

            self.logger.info(f"表面平滑完成: 方法={smoothing_method}, "
                           f"平滑度改善={improvement:.1f}%, "
                           f"梯度: {original_quality['mean_gradient']:.2f} → {smooth_quality['mean_gradient']:.2f}")

            return z_grid_smooth

        except Exception as e:
            self.logger.error(f"表面平滑处理失败: {e}")
            return z_grid

    def _apply_gaussian_smoothing(self, z_grid: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """应用高斯滤波平滑"""
        try:
            from scipy.ndimage import gaussian_filter

            sigma = config.get('sigma', 1.5)
            z_grid_smooth = gaussian_filter(z_grid, sigma=sigma)

            self.logger.debug(f"应用高斯滤波平滑 (σ={sigma})")
            return z_grid_smooth

        except ImportError:
            self.logger.error("scipy.ndimage不可用，跳过高斯滤波")
            return z_grid
        except Exception as e:
            self.logger.error(f"高斯滤波失败: {e}")
            return z_grid

    def _apply_multimodal_preserving_smoothing(self, z_grid: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """应用多模态特征保持的平滑处理"""
        try:
            from scipy.ndimage import gaussian_filter, sobel

            # 获取配置参数
            base_sigma = config.get('sigma', 1.0)  # 降低默认σ值以保持特征
            feature_threshold = config.get('feature_threshold', 0.7)  # 特征检测阈值

            # 1. 计算梯度强度以识别重要特征
            grad_x = sobel(z_grid, axis=1)
            grad_y = sobel(z_grid, axis=0)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 2. 识别高梯度区域（重要特征区域）
            gradient_threshold = np.percentile(gradient_magnitude, feature_threshold * 100)
            feature_mask = gradient_magnitude > gradient_threshold

            # 3. 分区域应用不同强度的平滑
            z_grid_smooth = z_grid.copy()

            # 对非特征区域应用标准平滑
            non_feature_mask = ~feature_mask
            if np.any(non_feature_mask):
                smoothed_surface = gaussian_filter(z_grid, sigma=base_sigma)
                z_grid_smooth[non_feature_mask] = smoothed_surface[non_feature_mask]

            # 对特征区域应用轻度平滑
            if np.any(feature_mask):
                light_sigma = base_sigma * 0.5  # 特征区域使用更小的σ值
                light_smoothed = gaussian_filter(z_grid, sigma=light_sigma)
                z_grid_smooth[feature_mask] = light_smoothed[feature_mask]

            feature_ratio = np.sum(feature_mask) / feature_mask.size
            self.logger.debug(f"多模态保持平滑: 特征区域比例={feature_ratio:.3f}, "
                            f"基础σ={base_sigma}, 特征σ={base_sigma*0.5}")

            return z_grid_smooth

        except ImportError:
            self.logger.error("scipy.ndimage不可用，回退到基础高斯滤波")
            return self._apply_gaussian_smoothing(z_grid, config)
        except Exception as e:
            self.logger.error(f"多模态保持平滑失败: {e}")
            return z_grid

    def _apply_adaptive_smoothing(self, z_grid: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """应用自适应平滑处理"""
        try:
            from scipy.ndimage import gaussian_filter, sobel

            # 计算局部复杂度
            grad_x = sobel(z_grid, axis=1)
            grad_y = sobel(z_grid, axis=0)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 使用高斯滤波平滑梯度以获得局部复杂度
            local_complexity = gaussian_filter(gradient_magnitude, sigma=2)

            # 定义复杂度阈值
            complexity_low = np.percentile(local_complexity, 33)
            complexity_high = np.percentile(local_complexity, 67)

            # 创建自适应σ映射
            sigma_base = config.get('sigma', 1.0)
            z_grid_adaptive = z_grid.copy()

            # 低复杂度区域：强平滑
            low_mask = local_complexity < complexity_low
            if np.any(low_mask):
                strong_smooth = gaussian_filter(z_grid, sigma=sigma_base * 1.5)
                z_grid_adaptive[low_mask] = strong_smooth[low_mask]

            # 中等复杂度区域：标准平滑
            medium_mask = (local_complexity >= complexity_low) & (local_complexity <= complexity_high)
            if np.any(medium_mask):
                medium_smooth = gaussian_filter(z_grid, sigma=sigma_base)
                z_grid_adaptive[medium_mask] = medium_smooth[medium_mask]

            # 高复杂度区域：轻度平滑
            high_mask = local_complexity > complexity_high
            if np.any(high_mask):
                light_smooth = gaussian_filter(z_grid, sigma=sigma_base * 0.5)
                z_grid_adaptive[high_mask] = light_smooth[high_mask]

            self.logger.debug(f"自适应平滑: 低复杂度={np.sum(low_mask)}, "
                            f"中等复杂度={np.sum(medium_mask)}, 高复杂度={np.sum(high_mask)}")

            return z_grid_adaptive

        except Exception as e:
            self.logger.error(f"自适应平滑失败: {e}")
            return self._apply_gaussian_smoothing(z_grid, config)

    def _apply_edge_preserving_smoothing(self, z_grid: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """应用边缘保持平滑处理（双边滤波近似）"""
        try:
            from scipy.ndimage import gaussian_filter

            sigma = config.get('sigma', 1.0)
            edge_threshold = config.get('edge_threshold', 0.1)

            # 简化的双边滤波实现
            # 1. 标准高斯平滑
            smoothed = gaussian_filter(z_grid, sigma=sigma)

            # 2. 计算原始和平滑版本的差异
            diff = np.abs(z_grid - smoothed)

            # 3. 在差异较大的地方保持原始值
            edge_mask = diff > (np.std(diff) * edge_threshold)

            result = smoothed.copy()
            result[edge_mask] = z_grid[edge_mask]

            edge_ratio = np.sum(edge_mask) / edge_mask.size
            self.logger.debug(f"边缘保持平滑: 边缘保持比例={edge_ratio:.3f}")

            return result

        except Exception as e:
            self.logger.error(f"边缘保持平滑失败: {e}")
            return self._apply_gaussian_smoothing(z_grid, config)

    def _apply_rbf_smoothing(self, z_grid: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """应用RBF重插值平滑（预留接口）"""
        try:
            # 这里可以实现RBF重插值逻辑
            # 目前回退到多模态保持平滑
            self.logger.debug("RBF平滑暂未实现，回退到多模态保持平滑")
            return self._apply_multimodal_preserving_smoothing(z_grid, config)

        except Exception as e:
            self.logger.error(f"RBF平滑失败: {e}")
            return z_grid

    def _calculate_surface_quality(self, z_grid: np.ndarray) -> Dict[str, float]:
        """计算表面质量指标"""
        try:
            # 计算梯度
            grad_x, grad_y = np.gradient(z_grid)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 计算二阶导数（曲率）
            grad_xx, grad_xy = np.gradient(grad_x)
            grad_yx, grad_yy = np.gradient(grad_y)
            curvature = np.abs(grad_xx) + np.abs(grad_yy)

            return {
                'mean_gradient': np.mean(gradient_magnitude),
                'max_gradient': np.max(gradient_magnitude),
                'std_gradient': np.std(gradient_magnitude),
                'mean_curvature': np.mean(curvature),
                'max_curvature': np.max(curvature),
                'surface_roughness': np.std(z_grid.flatten())
            }

        except Exception as e:
            self.logger.error(f"表面质量计算失败: {e}")
            return {
                'mean_gradient': 0.0,
                'max_gradient': 0.0,
                'std_gradient': 0.0,
                'mean_curvature': 0.0,
                'max_curvature': 0.0,
                'surface_roughness': 0.0
            }