2025-07-31 17:24:24,463 - __main__ - INFO - simple1_9 开始进化第 1 代
2025-07-31 17:24:24,464 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-31 17:24:24,464 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:24:24,464 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=747.0, 多样性=0.744
2025-07-31 17:24:24,465 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:24:24,466 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.122
2025-07-31 17:24:24,466 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:24:24,469 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/2)
2025-07-31 17:24:24,469 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 0, 'total': 2, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753953864.4691138, 'status': 'default_fallback'}}
2025-07-31 17:24:24,469 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:24:24,469 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:24:24,469 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 747.0
  • mean_cost: 950.4
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:24:24,470 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:24:24,470 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:24:25,968 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Diversity is moderate, suggesting exploration. Best individual exploits and the worst two explore."
}
```
2025-07-31 17:24:25,968 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:24:25,968 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:25,968 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:25,968 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Diversity is moderate, suggesting exploration. Best individual exploits and the worst two explore."
}
```
2025-07-31 17:24:25,968 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:24:25,968 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:25,968 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Diversity is moderate, suggesting exploration. Best individual exploits and the worst two explore."
}
```
2025-07-31 17:24:25,969 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:24:25,969 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:24:25,969 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:24:25,969 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:25,970 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:24:25,970 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:26,094 - ExplorationExpert - INFO - 探索路径生成完成，成本: 876.0, 路径长度: 9
2025-07-31 17:24:26,094 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 7, 6, 0, 3, 5, 8, 4, 2], 'cur_cost': 876.0}
2025-07-31 17:24:26,094 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:24:26,094 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:24:26,094 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:26,095 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-07-31 17:24:26,095 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:26,095 - ExplorationExpert - INFO - 探索路径生成完成，成本: 942.0, 路径长度: 9
2025-07-31 17:24:26,095 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 8, 0, 5, 6, 7, 3, 4, 1], 'cur_cost': 942.0}
2025-07-31 17:24:26,095 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:24:26,096 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:24:26,096 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:26,096 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 9
2025-07-31 17:24:26,097 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:26,097 - ExplorationExpert - INFO - 探索路径生成完成，成本: 941.0, 路径长度: 9
2025-07-31 17:24:26,097 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 0, 5, 8, 6, 3, 4, 2, 7], 'cur_cost': 941.0}
2025-07-31 17:24:26,097 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:24:26,098 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:24:26,115 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:24:26,119 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1060.0
2025-07-31 17:24:27,446 - ExploitationExpert - INFO - res_population_num: 1
2025-07-31 17:24:27,446 - ExploitationExpert - INFO - res_population_costs: [680.0]
2025-07-31 17:24:27,446 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64)]
2025-07-31 17:24:27,446 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:24:27,447 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 6, 0, 3, 5, 8, 4, 2], 'cur_cost': 876.0}, {'tour': [2, 8, 0, 5, 6, 7, 3, 4, 1], 'cur_cost': 942.0}, {'tour': [1, 0, 5, 8, 6, 3, 4, 2, 7], 'cur_cost': 941.0}, {'tour': array([6, 3, 4, 8, 7, 1, 0, 5, 2], dtype=int64), 'cur_cost': 1060.0}, {'tour': array([6, 7, 2, 1, 0, 4, 5, 8, 3], dtype=int64), 'cur_cost': 896.0}]
2025-07-31 17:24:27,448 - ExploitationExpert - INFO - 局部搜索耗时: 1.33秒
2025-07-31 17:24:27,448 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-31 17:24:27,448 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([6, 3, 4, 8, 7, 1, 0, 5, 2], dtype=int64), 'cur_cost': 1060.0}
2025-07-31 17:24:27,448 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:24:27,448 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:24:27,448 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:24:27,449 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1116.0
2025-07-31 17:24:29,012 - ExploitationExpert - INFO - res_population_num: 2
2025-07-31 17:24:29,012 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0]
2025-07-31 17:24:29,012 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64)]
2025-07-31 17:24:29,013 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:24:29,013 - ExploitationExpert - INFO - populations: [{'tour': [1, 7, 6, 0, 3, 5, 8, 4, 2], 'cur_cost': 876.0}, {'tour': [2, 8, 0, 5, 6, 7, 3, 4, 1], 'cur_cost': 942.0}, {'tour': [1, 0, 5, 8, 6, 3, 4, 2, 7], 'cur_cost': 941.0}, {'tour': array([6, 3, 4, 8, 7, 1, 0, 5, 2], dtype=int64), 'cur_cost': 1060.0}, {'tour': array([7, 6, 4, 0, 8, 2, 5, 1, 3], dtype=int64), 'cur_cost': 1116.0}]
2025-07-31 17:24:29,014 - ExploitationExpert - INFO - 局部搜索耗时: 1.57秒
2025-07-31 17:24:29,014 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-31 17:24:29,014 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 6, 4, 0, 8, 2, 5, 1, 3], dtype=int64), 'cur_cost': 1116.0}
2025-07-31 17:24:29,015 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 0, 3, 5, 8, 4, 2], 'cur_cost': 876.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 0, 5, 6, 7, 3, 4, 1], 'cur_cost': 942.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 5, 8, 6, 3, 4, 2, 7], 'cur_cost': 941.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 3, 4, 8, 7, 1, 0, 5, 2], dtype=int64), 'cur_cost': 1060.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 6, 4, 0, 8, 2, 5, 1, 3], dtype=int64), 'cur_cost': 1116.0}}]
2025-07-31 17:24:29,015 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:24:29,015 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:24:29,015 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=876.0, 多样性=0.756
2025-07-31 17:24:29,015 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-31 17:24:29,016 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-07-31 17:24:29,016 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:24:29,016 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06510928272976467, 'best_improvement': -0.17269076305220885}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.014925373134328304}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.5555555555555556, 'new_diversity': 0.5555555555555556, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-31 17:24:29,016 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-31 17:24:29,016 - __main__ - INFO - simple1_9 开始进化第 2 代
2025-07-31 17:24:29,016 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-31 17:24:29,016 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:24:29,017 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=876.0, 多样性=0.756
2025-07-31 17:24:29,017 - PathExpert - INFO - 开始路径结构分析
2025-07-31 17:24:29,017 - PathExpert - INFO - 路径结构分析完成: 公共边数量=4, 路径相似性=0.078
2025-07-31 17:24:29,018 - EliteExpert - INFO - 开始精英解分析
2025-07-31 17:24:29,018 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.556
2025-07-31 17:24:29,020 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/2)
2025-07-31 17:24:29,020 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'balance', 'operators': ['mutation', 'crossover'], 'parameters': {}}, 'iteration_info': {'current': 1, 'total': 2, 'progress': 0.5}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1753953869.020756, 'status': 'default_fallback'}}
2025-07-31 17:24:29,020 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-07-31 17:24:29,020 - StrategyExpert - INFO - 开始策略分配分析
2025-07-31 17:24:29,020 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 5 individuals
  • diversity: 0.5
  • best_cost: 876.0
  • mean_cost: 987.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: exploitation
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 2, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-31 17:24:29,021 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-31 17:24:29,021 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-31 17:24:30,359 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Deteriorating cost improvement with high mean cost suggests exploration. Focus on finding promising regions with increased diversity."
}
```
2025-07-31 17:24:30,360 - utils.utils - INFO - 成功从JSON中提取数据
2025-07-31 17:24:30,360 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:30,360 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:30,361 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Deteriorating cost improvement with high mean cost suggests exploration. Focus on finding promising regions with increased diversity."
}
```
2025-07-31 17:24:30,361 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-07-31 17:24:30,361 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-31 17:24:30,361 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit"
  },
  "rationale": "Deteriorating cost improvement with high mean cost suggests exploration. Focus on finding promising regions with increased diversity."
}
```
2025-07-31 17:24:30,361 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-07-31 17:24:30,361 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-31 17:24:30,361 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-31 17:24:30,361 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:30,362 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:24:30,362 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:30,362 - ExplorationExpert - INFO - 探索路径生成完成，成本: 934.0, 路径长度: 9
2025-07-31 17:24:30,362 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 0, 7, 5, 8, 4, 2, 3, 1], 'cur_cost': 934.0}
2025-07-31 17:24:30,362 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-07-31 17:24:30,362 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 9
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 757.0, 路径长度: 9
2025-07-31 17:24:30,363 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 4, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 757.0}
2025-07-31 17:24:30,363 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-31 17:24:30,363 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-31 17:24:30,364 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 9
2025-07-31 17:24:30,364 - ExplorationExpert - INFO - 计算路径成本
2025-07-31 17:24:30,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 893.0, 路径长度: 9
2025-07-31 17:24:30,364 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 8, 1, 4, 7, 3, 5, 6], 'cur_cost': 893.0}
2025-07-31 17:24:30,364 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-31 17:24:30,364 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:24:30,364 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:24:30,364 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 911.0
2025-07-31 17:24:30,689 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:24:30,689 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-07-31 17:24:30,689 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-07-31 17:24:30,690 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:24:30,690 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 7, 5, 8, 4, 2, 3, 1], 'cur_cost': 934.0}, {'tour': [2, 4, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 757.0}, {'tour': [0, 2, 8, 1, 4, 7, 3, 5, 6], 'cur_cost': 893.0}, {'tour': array([0, 6, 3, 8, 5, 1, 2, 4, 7], dtype=int64), 'cur_cost': 911.0}, {'tour': [7, 6, 4, 0, 8, 2, 5, 1, 3], 'cur_cost': 1116.0}]
2025-07-31 17:24:30,691 - ExploitationExpert - INFO - 局部搜索耗时: 0.33秒
2025-07-31 17:24:30,691 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-31 17:24:30,691 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([0, 6, 3, 8, 5, 1, 2, 4, 7], dtype=int64), 'cur_cost': 911.0}
2025-07-31 17:24:30,691 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-07-31 17:24:30,692 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-31 17:24:30,692 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-31 17:24:30,692 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 996.0
2025-07-31 17:24:30,733 - ExploitationExpert - INFO - res_population_num: 3
2025-07-31 17:24:30,733 - ExploitationExpert - INFO - res_population_costs: [680.0, 680.0, 680.0]
2025-07-31 17:24:30,733 - ExploitationExpert - INFO - res_populations: [array([0, 4, 2, 8, 7, 3, 5, 6, 1], dtype=int64), array([0, 1, 4, 2, 8, 3, 5, 6, 7], dtype=int64), array([0, 1, 4, 2, 8, 7, 3, 5, 6], dtype=int64)]
2025-07-31 17:24:30,734 - ExploitationExpert - INFO - populations_num: 5
2025-07-31 17:24:30,734 - ExploitationExpert - INFO - populations: [{'tour': [6, 0, 7, 5, 8, 4, 2, 3, 1], 'cur_cost': 934.0}, {'tour': [2, 4, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 757.0}, {'tour': [0, 2, 8, 1, 4, 7, 3, 5, 6], 'cur_cost': 893.0}, {'tour': array([0, 6, 3, 8, 5, 1, 2, 4, 7], dtype=int64), 'cur_cost': 911.0}, {'tour': array([3, 7, 1, 4, 8, 5, 6, 2, 0], dtype=int64), 'cur_cost': 996.0}]
2025-07-31 17:24:30,735 - ExploitationExpert - INFO - 局部搜索耗时: 0.04秒
2025-07-31 17:24:30,735 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-31 17:24:30,736 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([3, 7, 1, 4, 8, 5, 6, 2, 0], dtype=int64), 'cur_cost': 996.0}
2025-07-31 17:24:30,736 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 0, 7, 5, 8, 4, 2, 3, 1], 'cur_cost': 934.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 8, 5, 3, 6, 7, 0, 1], 'cur_cost': 757.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 8, 1, 4, 7, 3, 5, 6], 'cur_cost': 893.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 3, 8, 5, 1, 2, 4, 7], dtype=int64), 'cur_cost': 911.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 7, 1, 4, 8, 5, 6, 2, 0], dtype=int64), 'cur_cost': 996.0}}]
2025-07-31 17:24:30,736 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-07-31 17:24:30,737 - StatsExpert - INFO - 开始统计分析
2025-07-31 17:24:30,737 - StatsExpert - INFO - 统计分析完成: 种群大小=5, 最优成本=757.0, 多样性=0.711
2025-07-31 17:24:30,737 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-31 17:24:30,737 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-07-31 17:24:30,737 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-31 17:24:30,738 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 2, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.11111374514736648, 'best_improvement': 0.1358447488584475}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.058823529411764795}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 680.0, 'new_best_cost': 680.0, 'quality_improvement': 0.0, 'old_diversity': 0.6296296296296297, 'new_diversity': 0.6296296296296297, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-07-31 17:24:30,738 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-31 17:24:30,741 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_solution.json
2025-07-31 17:24:30,741 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple1_9_20250731_172430.solution
2025-07-31 17:24:30,741 - __main__ - INFO - 实例 simple1_9 处理完成
