# -*- coding: utf-8 -*-
"""
分析工具模块

提供统一的分析工具函数，消除专家模块间的代码冗余。
"""

import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict


class AnalysisUtils:
    """统一的分析工具类"""
    
    @staticmethod
    def calculate_std(values: List[float]) -> float:
        """
        计算标准差
        
        参数:
            values: 数值列表
            
        返回:
            float: 标准差
        """
        if len(values) <= 1:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5
    
    @staticmethod
    def calculate_basic_stats(values: List[float]) -> Dict[str, float]:
        """
        计算基础统计信息
        
        参数:
            values: 数值列表
            
        返回:
            Dict: 包含min, max, mean, std的统计信息
        """
        if not values:
            return {"min": 0.0, "max": 0.0, "mean": 0.0, "std": 0.0}
        
        return {
            "min": min(values),
            "max": max(values),
            "mean": sum(values) / len(values),
            "std": AnalysisUtils.calculate_std(values)
        }
    
    @staticmethod
    def calculate_percentiles(values: List[float], percentiles: List[float]) -> List[float]:
        """
        计算百分位数
        
        参数:
            values: 数值列表
            percentiles: 百分位数列表 (0-100)
            
        返回:
            List[float]: 对应的百分位数值
        """
        if not values:
            return [0.0] * len(percentiles)
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        
        result = []
        for p in percentiles:
            if p <= 0:
                result.append(sorted_values[0])
            elif p >= 100:
                result.append(sorted_values[-1])
            else:
                index = (p / 100) * (n - 1)
                lower_index = int(index)
                upper_index = min(lower_index + 1, n - 1)
                
                if lower_index == upper_index:
                    result.append(sorted_values[lower_index])
                else:
                    # 线性插值
                    weight = index - lower_index
                    value = sorted_values[lower_index] * (1 - weight) + sorted_values[upper_index] * weight
                    result.append(value)
        
        return result


class PathUtils:
    """路径处理工具类"""
    
    @staticmethod
    def extract_tours_safely(populations: List[Dict]) -> List[List[int]]:
        """
        安全地从种群中提取路径数据
        
        参数:
            populations: 种群列表
            
        返回:
            List[List[int]]: 路径列表
        """
        tours = []
        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                if isinstance(tour, np.ndarray):
                    tour = tour.tolist()
                if tour:  # 确保路径非空
                    tours.append(tour)
        return tours
    
    @staticmethod
    def extract_costs_safely(populations: List[Dict]) -> List[float]:
        """
        安全地从种群中提取成本数据
        
        参数:
            populations: 种群列表
            
        返回:
            List[float]: 成本列表
        """
        return [float(ind["cur_cost"]) for ind in populations if "cur_cost" in ind]
    
    @staticmethod
    def validate_tour_data(tour: Any) -> Optional[List[int]]:
        """
        验证和标准化路径数据
        
        参数:
            tour: 路径数据（可能是list或numpy数组）
            
        返回:
            Optional[List[int]]: 标准化的路径，如果无效则返回None
        """
        if tour is None:
            return None
        
        try:
            if isinstance(tour, np.ndarray):
                tour = tour.tolist()
            
            if not isinstance(tour, list) or len(tour) == 0:
                return None
            
            # 确保所有元素都是整数
            return [int(x) for x in tour]
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def extract_edges_from_tour(tour) -> List[Tuple[int, int]]:
        """
        从路径中提取边（支持列表和numpy数组）

        参数:
            tour: 路径（列表或numpy数组）

        返回:
            List[Tuple[int, int]]: 边列表
        """
        # 处理numpy数组
        if hasattr(tour, 'tolist'):
            tour = tour.tolist()

        # 检查有效性
        if not tour or len(tour) < 2:
            return []

        edges = []
        for i in range(len(tour)):
            edge = (int(tour[i]), int(tour[(i + 1) % len(tour)]))
            edges.append(edge)

        return edges
    
    @staticmethod
    def normalize_edges(edges: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """
        标准化边（确保较小的节点在前）
        
        参数:
            edges: 边列表
            
        返回:
            List[Tuple[int, int]]: 标准化的边列表
        """
        return [tuple(sorted(edge)) for edge in edges]


class EdgeAnalysisUtils:
    """边分析工具类"""
    
    @staticmethod
    def calculate_edge_frequency(populations: List[Dict]) -> Dict[str, float]:
        """
        计算边在种群中的出现频率

        参数:
            populations: 种群列表（包含tour字段的字典列表）

        返回:
            Dict[str, float]: 边频率字典
        """
        if not populations:
            return {}

        edge_counts = defaultdict(int)
        total_individuals = len(populations)

        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                edges = PathUtils.extract_edges_from_tour(tour)
                normalized_edges = PathUtils.normalize_edges(edges)

                for edge in normalized_edges:
                    edge_counts[edge] += 1

        # 计算频率并转换为字符串键
        edge_frequency = {}
        for edge, count in edge_counts.items():
            edge_frequency[str(edge)] = count / total_individuals

        return edge_frequency
    
    @staticmethod
    def find_common_edges(populations: List[List[int]], threshold: float = 0.5) -> List[Tuple[int, int]]:
        """
        找出在种群中频繁出现的边
        
        参数:
            populations: 路径列表
            threshold: 频率阈值（0-1）
            
        返回:
            List[Tuple[int, int]]: 高频边列表
        """
        if not populations:
            return []
        
        edge_frequency = EdgeAnalysisUtils.calculate_edge_frequency(populations)
        total_tours = len(populations)
        
        common_edges = []
        for edge, count in edge_frequency.items():
            if count / total_tours >= threshold:
                common_edges.append(edge)
        
        return common_edges
    
    @staticmethod
    def analyze_edge_patterns(populations: List[List[int]]) -> Dict[str, Any]:
        """
        分析边的模式
        
        参数:
            populations: 路径列表
            
        返回:
            Dict[str, Any]: 边模式分析结果
        """
        if not populations:
            return {
                "total_unique_edges": 0,
                "avg_edges_per_tour": 0,
                "edge_frequency_distribution": {},
                "most_common_edges": []
            }
        
        edge_frequency = EdgeAnalysisUtils.calculate_edge_frequency(populations)
        
        # 计算统计信息
        total_unique_edges = len(edge_frequency)
        avg_edges_per_tour = sum(len(tour) for tour in populations) / len(populations)
        
        # 频率分布
        frequency_counts = defaultdict(int)
        for count in edge_frequency.values():
            frequency_counts[count] += 1
        
        # 最常见的边（前10个）
        most_common_edges = sorted(edge_frequency.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "total_unique_edges": total_unique_edges,
            "avg_edges_per_tour": avg_edges_per_tour,
            "edge_frequency_distribution": dict(frequency_counts),
            "most_common_edges": most_common_edges
        }


class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_population_data(populations: List[Dict]) -> bool:
        """
        验证种群数据的有效性
        
        参数:
            populations: 种群数据
            
        返回:
            bool: 数据是否有效
        """
        if not populations or not isinstance(populations, list):
            return False
        
        for individual in populations:
            if not isinstance(individual, dict):
                return False
            
            if "tour" not in individual or "cur_cost" not in individual:
                return False
            
            tour = PathUtils.validate_tour_data(individual["tour"])
            if tour is None:
                return False
            
            try:
                float(individual["cur_cost"])
            except (ValueError, TypeError):
                return False
        
        return True
    
    @staticmethod
    def validate_distance_matrix(distance_matrix: Any, expected_size: int) -> bool:
        """
        验证距离矩阵的有效性
        
        参数:
            distance_matrix: 距离矩阵
            expected_size: 期望的矩阵大小
            
        返回:
            bool: 矩阵是否有效
        """
        if distance_matrix is None:
            return False
        
        try:
            if isinstance(distance_matrix, np.ndarray):
                return (distance_matrix.shape == (expected_size, expected_size) and
                        np.all(distance_matrix >= 0))
            else:
                return False
        except Exception:
            return False
