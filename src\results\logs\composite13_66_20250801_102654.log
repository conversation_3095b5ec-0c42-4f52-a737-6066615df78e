2025-08-01 10:26:54,373 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-01 10:26:54,373 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 10:26:54,373 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:26:54,394 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9989.0, 多样性=0.921
2025-08-01 10:26:54,394 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:26:54,399 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.033
2025-08-01 10:26:54,399 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:26:54,401 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-01 10:26:54,402 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 10:26:54,402 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-01 10:26:54,402 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 10:26:54,613 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 10:26:54,613 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 10:26:54,699 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 10:26:55,031 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_102654.html
2025-08-01 10:26:55,098 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_102654.html
2025-08-01 10:26:55,098 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 10:26:55,098 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 10:26:55,098 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.6973秒
2025-08-01 10:26:55,098 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754015214.613809, 'performance_metrics': {}}}
2025-08-01 10:26:55,098 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:26:55,099 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:26:55,099 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9989.0
  • mean_cost: 78870.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:26:55,099 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:26:55,099 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:26:56,619 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus is on exploration due to unexplored space and low population diversity. Most individuals explore while a few exploit."
}
```
2025-08-01 10:26:56,619 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:26:56,619 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:26:56,619 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:26:56,619 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus is on exploration due to unexplored space and low population diversity. Most individuals explore while a few exploit."
}
```
2025-08-01 10:26:56,620 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:26:56,620 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:26:56,620 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus is on exploration due to unexplored space and low population diversity. Most individuals explore while a few exploit."
}
```
2025-08-01 10:26:56,620 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:26:56,620 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:26:56,620 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:26:56,620 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,623 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 10:26:56,623 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109985.0, 路径长度: 66
2025-08-01 10:26:56,797 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [40, 61, 62, 10, 44, 47, 51, 9, 53, 46, 21, 14, 11, 36, 24, 27, 38, 31, 58, 49, 0, 52, 28, 64, 13, 42, 37, 35, 15, 20, 39, 16, 2, 43, 59, 26, 19, 8, 32, 7, 48, 18, 41, 55, 22, 54, 56, 60, 33, 23, 4, 30, 3, 17, 45, 1, 12, 5, 65, 57, 25, 6, 29, 50, 34, 63], 'cur_cost': 109985.0}
2025-08-01 10:26:56,797 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:26:56,797 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:26:56,797 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,799 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:26:56,799 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12868.0, 路径长度: 66
2025-08-01 10:26:56,800 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}
2025-08-01 10:26:56,800 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:26:56,800 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:26:56,800 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,802 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:26:56,802 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12761.0, 路径长度: 66
2025-08-01 10:26:56,803 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 16, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}
2025-08-01 10:26:56,803 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 10:26:56,803 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 10:26:56,803 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,809 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:26:56,810 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,810 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12377.0, 路径长度: 66
2025-08-01 10:26:56,811 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 11, 18, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}
2025-08-01 10:26:56,811 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 10:26:56,811 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 10:26:56,811 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,819 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:26:56,819 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,819 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61505.0, 路径长度: 66
2025-08-01 10:26:56,819 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [15, 37, 28, 30, 4, 3, 63, 59, 55, 12, 2, 64, 52, 0, 9, 21, 36, 18, 1, 8, 20, 24, 25, 34, 26, 19, 11, 16, 35, 48, 43, 17, 40, 14, 10, 6, 23, 7, 60, 13, 44, 49, 33, 22, 47, 51, 42, 27, 29, 45, 41, 46, 32, 56, 58, 54, 65, 53, 61, 62, 39, 38, 50, 31, 5, 57], 'cur_cost': 61505.0}
2025-08-01 10:26:56,820 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 10:26:56,820 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 10:26:56,820 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,827 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:26:56,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,827 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57222.0, 路径长度: 66
2025-08-01 10:26:56,827 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [15, 34, 32, 7, 55, 65, 62, 59, 11, 53, 6, 61, 56, 4, 63, 19, 31, 1, 14, 25, 21, 35, 24, 9, 60, 64, 2, 10, 37, 29, 3, 58, 20, 27, 40, 47, 45, 51, 49, 46, 18, 22, 23, 16, 0, 26, 33, 5, 8, 13, 43, 41, 50, 42, 44, 17, 28, 48, 39, 38, 36, 30, 12, 52, 57, 54], 'cur_cost': 57222.0}
2025-08-01 10:26:56,828 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:26:56,828 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:26:56,828 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:26:56,830 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:26:56,831 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:26:56,831 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12865.0, 路径长度: 66
2025-08-01 10:26:56,831 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 19, 22, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12865.0}
2025-08-01 10:26:56,831 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 10:26:56,831 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:26:56,834 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:26:56,835 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 116444.0
2025-08-01 10:26:58,573 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 10:26:58,573 - ExploitationExpert - INFO - res_population_costs: [92818.0]
2025-08-01 10:26:58,573 - ExploitationExpert - INFO - res_populations: [array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:26:58,575 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:26:58,575 - ExploitationExpert - INFO - populations: [{'tour': [40, 61, 62, 10, 44, 47, 51, 9, 53, 46, 21, 14, 11, 36, 24, 27, 38, 31, 58, 49, 0, 52, 28, 64, 13, 42, 37, 35, 15, 20, 39, 16, 2, 43, 59, 26, 19, 8, 32, 7, 48, 18, 41, 55, 22, 54, 56, 60, 33, 23, 4, 30, 3, 17, 45, 1, 12, 5, 65, 57, 25, 6, 29, 50, 34, 63], 'cur_cost': 109985.0}, {'tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 16, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}, {'tour': [0, 11, 18, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': [15, 37, 28, 30, 4, 3, 63, 59, 55, 12, 2, 64, 52, 0, 9, 21, 36, 18, 1, 8, 20, 24, 25, 34, 26, 19, 11, 16, 35, 48, 43, 17, 40, 14, 10, 6, 23, 7, 60, 13, 44, 49, 33, 22, 47, 51, 42, 27, 29, 45, 41, 46, 32, 56, 58, 54, 65, 53, 61, 62, 39, 38, 50, 31, 5, 57], 'cur_cost': 61505.0}, {'tour': [15, 34, 32, 7, 55, 65, 62, 59, 11, 53, 6, 61, 56, 4, 63, 19, 31, 1, 14, 25, 21, 35, 24, 9, 60, 64, 2, 10, 37, 29, 3, 58, 20, 27, 40, 47, 45, 51, 49, 46, 18, 22, 23, 16, 0, 26, 33, 5, 8, 13, 43, 41, 50, 42, 44, 17, 28, 48, 39, 38, 36, 30, 12, 52, 57, 54], 'cur_cost': 57222.0}, {'tour': [0, 19, 22, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12865.0}, {'tour': array([27, 53, 47, 24, 12, 59,  4, 42, 19, 61,  9, 25, 37, 39, 11, 10, 15,
       29,  1, 64, 55,  2, 18, 62, 35, 20, 65, 49, 51, 17,  7, 54, 28, 60,
       46, 56, 30, 38, 14, 23, 21, 63, 48, 32,  5, 36, 41, 58, 16,  3, 52,
       34, 22, 26, 57, 31,  6, 33,  0, 50, 43, 44, 13, 45,  8, 40],
      dtype=int64), 'cur_cost': 116444.0}, {'tour': array([37, 32, 13, 53, 22, 25, 60, 20, 58, 17, 59, 55,  1, 43, 38, 40, 56,
       51,  5, 61, 62, 42, 57, 64, 14,  9, 50, 29, 27, 54, 35, 26, 15,  6,
       18, 49, 33, 31, 44, 23, 48,  7, 30, 52, 16, 41,  2, 21, 24, 47, 45,
       65, 36, 11, 46, 12, 34,  3,  8, 10, 19, 63,  0, 39, 28,  4],
      dtype=int64), 'cur_cost': 107712.0}, {'tour': array([18, 61, 13, 44, 32,  5, 14, 39, 11, 48,  3, 41, 20, 47,  7, 22, 40,
       38, 15,  1,  2, 58, 54, 53, 30, 21, 33, 62, 10, 63, 51, 55, 59, 16,
       25, 49,  9, 19, 45, 50,  0, 65, 31, 56, 27, 12, 60, 36,  6, 26, 43,
       42, 57, 29, 17, 64,  8, 37, 46, 35, 28, 23, 34,  4, 24, 52],
      dtype=int64), 'cur_cost': 112346.0}]
2025-08-01 10:26:58,581 - ExploitationExpert - INFO - 局部搜索耗时: 1.75秒
2025-08-01 10:26:58,581 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 10:26:58,581 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([27, 53, 47, 24, 12, 59,  4, 42, 19, 61,  9, 25, 37, 39, 11, 10, 15,
       29,  1, 64, 55,  2, 18, 62, 35, 20, 65, 49, 51, 17,  7, 54, 28, 60,
       46, 56, 30, 38, 14, 23, 21, 63, 48, 32,  5, 36, 41, 58, 16,  3, 52,
       34, 22, 26, 57, 31,  6, 33,  0, 50, 43, 44, 13, 45,  8, 40],
      dtype=int64), 'cur_cost': 116444.0}
2025-08-01 10:26:58,581 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 10:26:58,582 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:26:58,582 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:26:58,582 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106193.0
2025-08-01 10:27:00,469 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 10:27:00,470 - ExploitationExpert - INFO - res_population_costs: [92818.0, 9584.0]
2025-08-01 10:27:00,470 - ExploitationExpert - INFO - res_populations: [array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:00,476 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:00,476 - ExploitationExpert - INFO - populations: [{'tour': [40, 61, 62, 10, 44, 47, 51, 9, 53, 46, 21, 14, 11, 36, 24, 27, 38, 31, 58, 49, 0, 52, 28, 64, 13, 42, 37, 35, 15, 20, 39, 16, 2, 43, 59, 26, 19, 8, 32, 7, 48, 18, 41, 55, 22, 54, 56, 60, 33, 23, 4, 30, 3, 17, 45, 1, 12, 5, 65, 57, 25, 6, 29, 50, 34, 63], 'cur_cost': 109985.0}, {'tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 16, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}, {'tour': [0, 11, 18, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': [15, 37, 28, 30, 4, 3, 63, 59, 55, 12, 2, 64, 52, 0, 9, 21, 36, 18, 1, 8, 20, 24, 25, 34, 26, 19, 11, 16, 35, 48, 43, 17, 40, 14, 10, 6, 23, 7, 60, 13, 44, 49, 33, 22, 47, 51, 42, 27, 29, 45, 41, 46, 32, 56, 58, 54, 65, 53, 61, 62, 39, 38, 50, 31, 5, 57], 'cur_cost': 61505.0}, {'tour': [15, 34, 32, 7, 55, 65, 62, 59, 11, 53, 6, 61, 56, 4, 63, 19, 31, 1, 14, 25, 21, 35, 24, 9, 60, 64, 2, 10, 37, 29, 3, 58, 20, 27, 40, 47, 45, 51, 49, 46, 18, 22, 23, 16, 0, 26, 33, 5, 8, 13, 43, 41, 50, 42, 44, 17, 28, 48, 39, 38, 36, 30, 12, 52, 57, 54], 'cur_cost': 57222.0}, {'tour': [0, 19, 22, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12865.0}, {'tour': array([27, 53, 47, 24, 12, 59,  4, 42, 19, 61,  9, 25, 37, 39, 11, 10, 15,
       29,  1, 64, 55,  2, 18, 62, 35, 20, 65, 49, 51, 17,  7, 54, 28, 60,
       46, 56, 30, 38, 14, 23, 21, 63, 48, 32,  5, 36, 41, 58, 16,  3, 52,
       34, 22, 26, 57, 31,  6, 33,  0, 50, 43, 44, 13, 45,  8, 40],
      dtype=int64), 'cur_cost': 116444.0}, {'tour': array([29, 57, 34, 39,  0, 47, 20, 18, 50, 31, 16, 46, 53, 12, 56, 52, 19,
       45, 44, 65,  3, 28,  4, 23, 51, 17, 63, 38,  9, 26, 36, 25, 30, 54,
        2, 43, 35, 62, 64, 32, 15, 13, 37,  1,  6, 58, 14, 11, 48,  7, 41,
       49, 60, 59, 55, 22, 24,  5,  8, 27, 61, 33, 40, 42, 10, 21],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([18, 61, 13, 44, 32,  5, 14, 39, 11, 48,  3, 41, 20, 47,  7, 22, 40,
       38, 15,  1,  2, 58, 54, 53, 30, 21, 33, 62, 10, 63, 51, 55, 59, 16,
       25, 49,  9, 19, 45, 50,  0, 65, 31, 56, 27, 12, 60, 36,  6, 26, 43,
       42, 57, 29, 17, 64,  8, 37, 46, 35, 28, 23, 34,  4, 24, 52],
      dtype=int64), 'cur_cost': 112346.0}]
2025-08-01 10:27:00,479 - ExploitationExpert - INFO - 局部搜索耗时: 1.90秒
2025-08-01 10:27:00,479 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 10:27:00,479 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([29, 57, 34, 39,  0, 47, 20, 18, 50, 31, 16, 46, 53, 12, 56, 52, 19,
       45, 44, 65,  3, 28,  4, 23, 51, 17, 63, 38,  9, 26, 36, 25, 30, 54,
        2, 43, 35, 62, 64, 32, 15, 13, 37,  1,  6, 58, 14, 11, 48,  7, 41,
       49, 60, 59, 55, 22, 24,  5,  8, 27, 61, 33, 40, 42, 10, 21],
      dtype=int64), 'cur_cost': 106193.0}
2025-08-01 10:27:00,480 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 10:27:00,480 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:00,480 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:00,480 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103449.0
2025-08-01 10:27:00,982 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 10:27:00,982 - ExploitationExpert - INFO - res_population_costs: [92818.0, 9584.0, 9521.0, 9521]
2025-08-01 10:27:00,982 - ExploitationExpert - INFO - res_populations: [array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:00,985 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:00,985 - ExploitationExpert - INFO - populations: [{'tour': [40, 61, 62, 10, 44, 47, 51, 9, 53, 46, 21, 14, 11, 36, 24, 27, 38, 31, 58, 49, 0, 52, 28, 64, 13, 42, 37, 35, 15, 20, 39, 16, 2, 43, 59, 26, 19, 8, 32, 7, 48, 18, 41, 55, 22, 54, 56, 60, 33, 23, 4, 30, 3, 17, 45, 1, 12, 5, 65, 57, 25, 6, 29, 50, 34, 63], 'cur_cost': 109985.0}, {'tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}, {'tour': [0, 16, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}, {'tour': [0, 11, 18, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}, {'tour': [15, 37, 28, 30, 4, 3, 63, 59, 55, 12, 2, 64, 52, 0, 9, 21, 36, 18, 1, 8, 20, 24, 25, 34, 26, 19, 11, 16, 35, 48, 43, 17, 40, 14, 10, 6, 23, 7, 60, 13, 44, 49, 33, 22, 47, 51, 42, 27, 29, 45, 41, 46, 32, 56, 58, 54, 65, 53, 61, 62, 39, 38, 50, 31, 5, 57], 'cur_cost': 61505.0}, {'tour': [15, 34, 32, 7, 55, 65, 62, 59, 11, 53, 6, 61, 56, 4, 63, 19, 31, 1, 14, 25, 21, 35, 24, 9, 60, 64, 2, 10, 37, 29, 3, 58, 20, 27, 40, 47, 45, 51, 49, 46, 18, 22, 23, 16, 0, 26, 33, 5, 8, 13, 43, 41, 50, 42, 44, 17, 28, 48, 39, 38, 36, 30, 12, 52, 57, 54], 'cur_cost': 57222.0}, {'tour': [0, 19, 22, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12865.0}, {'tour': array([27, 53, 47, 24, 12, 59,  4, 42, 19, 61,  9, 25, 37, 39, 11, 10, 15,
       29,  1, 64, 55,  2, 18, 62, 35, 20, 65, 49, 51, 17,  7, 54, 28, 60,
       46, 56, 30, 38, 14, 23, 21, 63, 48, 32,  5, 36, 41, 58, 16,  3, 52,
       34, 22, 26, 57, 31,  6, 33,  0, 50, 43, 44, 13, 45,  8, 40],
      dtype=int64), 'cur_cost': 116444.0}, {'tour': array([29, 57, 34, 39,  0, 47, 20, 18, 50, 31, 16, 46, 53, 12, 56, 52, 19,
       45, 44, 65,  3, 28,  4, 23, 51, 17, 63, 38,  9, 26, 36, 25, 30, 54,
        2, 43, 35, 62, 64, 32, 15, 13, 37,  1,  6, 58, 14, 11, 48,  7, 41,
       49, 60, 59, 55, 22, 24,  5,  8, 27, 61, 33, 40, 42, 10, 21],
      dtype=int64), 'cur_cost': 106193.0}, {'tour': array([51,  6, 63, 55, 37, 20, 58, 28, 32, 25, 11, 56, 61, 12, 22, 31, 40,
       64, 10, 65,  5, 38, 27, 24,  3, 33, 46, 45,  2,  1, 53, 43,  9,  0,
       42, 57, 30,  4, 26, 18, 29, 50, 59, 47, 52, 17,  7, 44, 23, 21, 36,
       16, 39, 54, 15, 48, 49, 14,  8, 35, 34, 60, 19, 13, 62, 41],
      dtype=int64), 'cur_cost': 103449.0}]
2025-08-01 10:27:00,986 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-08-01 10:27:00,987 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 10:27:00,987 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([51,  6, 63, 55, 37, 20, 58, 28, 32, 25, 11, 56, 61, 12, 22, 31, 40,
       64, 10, 65,  5, 38, 27, 24,  3, 33, 46, 45,  2,  1, 53, 43,  9,  0,
       42, 57, 30,  4, 26, 18, 29, 50, 59, 47, 52, 17,  7, 44, 23, 21, 36,
       16, 39, 54, 15, 48, 49, 14,  8, 35, 34, 60, 19, 13, 62, 41],
      dtype=int64), 'cur_cost': 103449.0}
2025-08-01 10:27:00,988 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [40, 61, 62, 10, 44, 47, 51, 9, 53, 46, 21, 14, 11, 36, 24, 27, 38, 31, 58, 49, 0, 52, 28, 64, 13, 42, 37, 35, 15, 20, 39, 16, 2, 43, 59, 26, 19, 8, 32, 7, 48, 18, 41, 55, 22, 54, 56, 60, 33, 23, 4, 30, 3, 17, 45, 1, 12, 5, 65, 57, 25, 6, 29, 50, 34, 63], 'cur_cost': 109985.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12868.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12761.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 18, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12377.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [15, 37, 28, 30, 4, 3, 63, 59, 55, 12, 2, 64, 52, 0, 9, 21, 36, 18, 1, 8, 20, 24, 25, 34, 26, 19, 11, 16, 35, 48, 43, 17, 40, 14, 10, 6, 23, 7, 60, 13, 44, 49, 33, 22, 47, 51, 42, 27, 29, 45, 41, 46, 32, 56, 58, 54, 65, 53, 61, 62, 39, 38, 50, 31, 5, 57], 'cur_cost': 61505.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [15, 34, 32, 7, 55, 65, 62, 59, 11, 53, 6, 61, 56, 4, 63, 19, 31, 1, 14, 25, 21, 35, 24, 9, 60, 64, 2, 10, 37, 29, 3, 58, 20, 27, 40, 47, 45, 51, 49, 46, 18, 22, 23, 16, 0, 26, 33, 5, 8, 13, 43, 41, 50, 42, 44, 17, 28, 48, 39, 38, 36, 30, 12, 52, 57, 54], 'cur_cost': 57222.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 22, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12865.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 53, 47, 24, 12, 59,  4, 42, 19, 61,  9, 25, 37, 39, 11, 10, 15,
       29,  1, 64, 55,  2, 18, 62, 35, 20, 65, 49, 51, 17,  7, 54, 28, 60,
       46, 56, 30, 38, 14, 23, 21, 63, 48, 32,  5, 36, 41, 58, 16,  3, 52,
       34, 22, 26, 57, 31,  6, 33,  0, 50, 43, 44, 13, 45,  8, 40],
      dtype=int64), 'cur_cost': 116444.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 57, 34, 39,  0, 47, 20, 18, 50, 31, 16, 46, 53, 12, 56, 52, 19,
       45, 44, 65,  3, 28,  4, 23, 51, 17, 63, 38,  9, 26, 36, 25, 30, 54,
        2, 43, 35, 62, 64, 32, 15, 13, 37,  1,  6, 58, 14, 11, 48,  7, 41,
       49, 60, 59, 55, 22, 24,  5,  8, 27, 61, 33, 40, 42, 10, 21],
      dtype=int64), 'cur_cost': 106193.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([51,  6, 63, 55, 37, 20, 58, 28, 32, 25, 11, 56, 61, 12, 22, 31, 40,
       64, 10, 65,  5, 38, 27, 24,  3, 33, 46, 45,  2,  1, 53, 43,  9,  0,
       42, 57, 30,  4, 26, 18, 29, 50, 59, 47, 52, 17,  7, 44, 23, 21, 36,
       16, 39, 54, 15, 48, 49, 14,  8, 35, 34, 60, 19, 13, 62, 41],
      dtype=int64), 'cur_cost': 103449.0}}]
2025-08-01 10:27:00,988 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:27:00,988 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:01,005 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12377.0, 多样性=0.858
2025-08-01 10:27:01,005 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 10:27:01,005 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 10:27:01,005 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:27:01,006 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.00021866117425175675, 'best_improvement': -0.2390629692661928}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.06907894736842114}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 9521.0, 'new_best_cost': 9521.0, 'quality_improvement': 0.0, 'old_diversity': 0.9671717171717172, 'new_diversity': 0.9671717171717172, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 10:27:01,006 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 10:27:01,007 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-01 10:27:01,007 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 10:27:01,007 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:01,039 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12377.0, 多样性=0.858
2025-08-01 10:27:01,041 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:27:01,047 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.049
2025-08-01 10:27:01,047 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:27:01,049 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.967
2025-08-01 10:27:01,053 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-01 10:27:01,053 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 10:27:01,054 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-01 10:27:01,054 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-01 10:27:01,104 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:27:01,104 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 10:27:01,104 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-01 10:27:01,119 - visualization.landscape_visualizer - INFO - 已添加 4 个精英解标记
2025-08-01 10:27:01,198 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_102701.html
2025-08-01 10:27:01,266 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_102701.html
2025-08-01 10:27:01,266 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 10:27:01,266 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 10:27:01,267 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2141秒
2025-08-01 10:27:01,267 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754015221.1048265, 'performance_metrics': {}}}
2025-08-01 10:27:01,268 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:27:01,268 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:27:01,268 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12377.0
  • mean_cost: 60566.9
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:27:01,269 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:27:01,269 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:27:02,741 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The landscape focus recommends exploration. With low diversity and unexplored regions, the majority of individuals should explore."
}
```
2025-08-01 10:27:02,742 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:27:02,742 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 10:27:02,742 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 10:27:02,742 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The landscape focus recommends exploration. With low diversity and unexplored regions, the majority of individuals should explore."
}
```
2025-08-01 10:27:02,742 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:27:02,742 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 10:27:02,742 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "The landscape focus recommends exploration. With low diversity and unexplored regions, the majority of individuals should explore."
}
```
2025-08-01 10:27:02,743 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:27:02,743 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:27:02,743 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:27:02,743 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,747 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 10:27:02,748 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,748 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113361.0, 路径长度: 66
2025-08-01 10:27:02,748 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [27, 16, 3, 10, 44, 1, 9, 5, 4, 46, 8, 2, 6, 36, 24, 53, 38, 31, 58, 35, 60, 52, 28, 64, 13, 19, 37, 14, 51, 20, 39, 12, 17, 43, 59, 26, 21, 7, 32, 61, 48, 62, 41, 55, 22, 54, 56, 57, 33, 23, 29, 30, 50, 42, 15, 34, 65, 40, 0, 45, 25, 49, 18, 63, 47, 11], 'cur_cost': 113361.0}
2025-08-01 10:27:02,748 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:27:02,749 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:27:02,749 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,754 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:02,754 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66020.0, 路径长度: 66
2025-08-01 10:27:02,754 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [50, 12, 27, 30, 19, 25, 20, 34, 8, 10, 4, 59, 14, 24, 11, 61, 23, 6, 0, 22, 2, 9, 18, 29, 16, 31, 43, 40, 15, 47, 17, 36, 13, 37, 39, 51, 48, 49, 28, 26, 33, 42, 46, 35, 55, 58, 62, 64, 65, 53, 56, 5, 63, 52, 3, 60, 7, 21, 45, 41, 38, 44, 1, 57, 54, 32], 'cur_cost': 66020.0}
2025-08-01 10:27:02,755 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:27:02,755 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:27:02,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,757 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:02,757 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,757 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12872.0, 路径长度: 66
2025-08-01 10:27:02,757 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}
2025-08-01 10:27:02,758 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 10:27:02,758 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 10:27:02,758 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,759 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 10:27:02,760 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,760 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100611.0, 路径长度: 66
2025-08-01 10:27:02,760 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}
2025-08-01 10:27:02,760 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 10:27:02,760 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 10:27:02,760 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,762 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:02,763 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,763 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12775.0, 路径长度: 66
2025-08-01 10:27:02,763 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}
2025-08-01 10:27:02,763 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 10:27:02,764 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 10:27:02,764 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,770 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:02,771 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,773 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68163.0, 路径长度: 66
2025-08-01 10:27:02,776 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}
2025-08-01 10:27:02,777 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:27:02,777 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:27:02,777 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,782 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:02,783 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,784 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14789.0, 路径长度: 66
2025-08-01 10:27:02,784 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}
2025-08-01 10:27:02,784 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 10:27:02,785 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 10:27:02,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:02,788 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:02,789 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:02,789 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12424.0, 路径长度: 66
2025-08-01 10:27:02,789 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}
2025-08-01 10:27:02,789 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 10:27:02,790 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:02,790 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:02,790 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106131.0
2025-08-01 10:27:02,900 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 10:27:02,901 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9584.0, 92818.0]
2025-08-01 10:27:02,901 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:27:02,903 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:02,903 - ExploitationExpert - INFO - populations: [{'tour': [27, 16, 3, 10, 44, 1, 9, 5, 4, 46, 8, 2, 6, 36, 24, 53, 38, 31, 58, 35, 60, 52, 28, 64, 13, 19, 37, 14, 51, 20, 39, 12, 17, 43, 59, 26, 21, 7, 32, 61, 48, 62, 41, 55, 22, 54, 56, 57, 33, 23, 29, 30, 50, 42, 15, 34, 65, 40, 0, 45, 25, 49, 18, 63, 47, 11], 'cur_cost': 113361.0}, {'tour': [50, 12, 27, 30, 19, 25, 20, 34, 8, 10, 4, 59, 14, 24, 11, 61, 23, 6, 0, 22, 2, 9, 18, 29, 16, 31, 43, 40, 15, 47, 17, 36, 13, 37, 39, 51, 48, 49, 28, 26, 33, 42, 46, 35, 55, 58, 62, 64, 65, 53, 56, 5, 63, 52, 3, 60, 7, 21, 45, 41, 38, 44, 1, 57, 54, 32], 'cur_cost': 66020.0}, {'tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': array([31, 25, 35, 12, 52, 51, 46, 40,  5, 39, 28, 32, 47, 27, 44, 19, 64,
       56, 22,  9,  1, 45,  6, 58, 14, 41, 29, 15, 55, 23, 17,  3, 33, 50,
       26, 43, 61, 13, 53, 18, 16, 62,  0, 37, 65,  7, 11, 20, 36, 60, 21,
       48, 24, 54, 49, 38, 34, 10, 30,  8, 42,  2, 59, 57,  4, 63],
      dtype=int64), 'cur_cost': 106131.0}, {'tour': [51, 6, 63, 55, 37, 20, 58, 28, 32, 25, 11, 56, 61, 12, 22, 31, 40, 64, 10, 65, 5, 38, 27, 24, 3, 33, 46, 45, 2, 1, 53, 43, 9, 0, 42, 57, 30, 4, 26, 18, 29, 50, 59, 47, 52, 17, 7, 44, 23, 21, 36, 16, 39, 54, 15, 48, 49, 14, 8, 35, 34, 60, 19, 13, 62, 41], 'cur_cost': 103449.0}]
2025-08-01 10:27:02,906 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 10:27:02,906 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 10:27:02,908 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([31, 25, 35, 12, 52, 51, 46, 40,  5, 39, 28, 32, 47, 27, 44, 19, 64,
       56, 22,  9,  1, 45,  6, 58, 14, 41, 29, 15, 55, 23, 17,  3, 33, 50,
       26, 43, 61, 13, 53, 18, 16, 62,  0, 37, 65,  7, 11, 20, 36, 60, 21,
       48, 24, 54, 49, 38, 34, 10, 30,  8, 42,  2, 59, 57,  4, 63],
      dtype=int64), 'cur_cost': 106131.0}
2025-08-01 10:27:02,909 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 10:27:02,909 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:02,909 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:02,910 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 96934.0
2025-08-01 10:27:02,987 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 10:27:02,987 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:02,987 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:02,990 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:02,990 - ExploitationExpert - INFO - populations: [{'tour': [27, 16, 3, 10, 44, 1, 9, 5, 4, 46, 8, 2, 6, 36, 24, 53, 38, 31, 58, 35, 60, 52, 28, 64, 13, 19, 37, 14, 51, 20, 39, 12, 17, 43, 59, 26, 21, 7, 32, 61, 48, 62, 41, 55, 22, 54, 56, 57, 33, 23, 29, 30, 50, 42, 15, 34, 65, 40, 0, 45, 25, 49, 18, 63, 47, 11], 'cur_cost': 113361.0}, {'tour': [50, 12, 27, 30, 19, 25, 20, 34, 8, 10, 4, 59, 14, 24, 11, 61, 23, 6, 0, 22, 2, 9, 18, 29, 16, 31, 43, 40, 15, 47, 17, 36, 13, 37, 39, 51, 48, 49, 28, 26, 33, 42, 46, 35, 55, 58, 62, 64, 65, 53, 56, 5, 63, 52, 3, 60, 7, 21, 45, 41, 38, 44, 1, 57, 54, 32], 'cur_cost': 66020.0}, {'tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': array([31, 25, 35, 12, 52, 51, 46, 40,  5, 39, 28, 32, 47, 27, 44, 19, 64,
       56, 22,  9,  1, 45,  6, 58, 14, 41, 29, 15, 55, 23, 17,  3, 33, 50,
       26, 43, 61, 13, 53, 18, 16, 62,  0, 37, 65,  7, 11, 20, 36, 60, 21,
       48, 24, 54, 49, 38, 34, 10, 30,  8, 42,  2, 59, 57,  4, 63],
      dtype=int64), 'cur_cost': 106131.0}, {'tour': array([ 0,  1, 59, 61,  7, 27, 16, 30, 10, 58,  5, 28, 47, 11, 40,  2, 14,
       20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32,  8, 62, 35, 25, 51,
       52,  6, 63, 17, 36, 26, 57, 21, 12, 24, 19,  9, 15, 13, 18, 38, 44,
       31, 49, 39,  4, 54,  3, 43, 23, 41, 55, 65, 22, 48, 64, 60],
      dtype=int64), 'cur_cost': 96934.0}]
2025-08-01 10:27:02,991 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:02,991 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 10:27:02,991 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 0,  1, 59, 61,  7, 27, 16, 30, 10, 58,  5, 28, 47, 11, 40,  2, 14,
       20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32,  8, 62, 35, 25, 51,
       52,  6, 63, 17, 36, 26, 57, 21, 12, 24, 19,  9, 15, 13, 18, 38, 44,
       31, 49, 39,  4, 54,  3, 43, 23, 41, 55, 65, 22, 48, 64, 60],
      dtype=int64), 'cur_cost': 96934.0}
2025-08-01 10:27:02,992 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [27, 16, 3, 10, 44, 1, 9, 5, 4, 46, 8, 2, 6, 36, 24, 53, 38, 31, 58, 35, 60, 52, 28, 64, 13, 19, 37, 14, 51, 20, 39, 12, 17, 43, 59, 26, 21, 7, 32, 61, 48, 62, 41, 55, 22, 54, 56, 57, 33, 23, 29, 30, 50, 42, 15, 34, 65, 40, 0, 45, 25, 49, 18, 63, 47, 11], 'cur_cost': 113361.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [50, 12, 27, 30, 19, 25, 20, 34, 8, 10, 4, 59, 14, 24, 11, 61, 23, 6, 0, 22, 2, 9, 18, 29, 16, 31, 43, 40, 15, 47, 17, 36, 13, 37, 39, 51, 48, 49, 28, 26, 33, 42, 46, 35, 55, 58, 62, 64, 65, 53, 56, 5, 63, 52, 3, 60, 7, 21, 45, 41, 38, 44, 1, 57, 54, 32], 'cur_cost': 66020.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 25, 35, 12, 52, 51, 46, 40,  5, 39, 28, 32, 47, 27, 44, 19, 64,
       56, 22,  9,  1, 45,  6, 58, 14, 41, 29, 15, 55, 23, 17,  3, 33, 50,
       26, 43, 61, 13, 53, 18, 16, 62,  0, 37, 65,  7, 11, 20, 36, 60, 21,
       48, 24, 54, 49, 38, 34, 10, 30,  8, 42,  2, 59, 57,  4, 63],
      dtype=int64), 'cur_cost': 106131.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1, 59, 61,  7, 27, 16, 30, 10, 58,  5, 28, 47, 11, 40,  2, 14,
       20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32,  8, 62, 35, 25, 51,
       52,  6, 63, 17, 36, 26, 57, 21, 12, 24, 19,  9, 15, 13, 18, 38, 44,
       31, 49, 39,  4, 54,  3, 43, 23, 41, 55, 65, 22, 48, 64, 60],
      dtype=int64), 'cur_cost': 96934.0}}]
2025-08-01 10:27:02,992 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:27:02,992 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:03,013 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12424.0, 多样性=0.861
2025-08-01 10:27:03,013 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 10:27:03,013 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 10:27:03,014 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:27:03,014 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 80, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.008434141738008806, 'best_improvement': -0.0037973660822493336}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.003533568904593792}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9521.0, 'new_best_cost': 9521.0, 'quality_improvement': 0.0, 'old_diversity': 0.8464646464646465, 'new_diversity': 0.8464646464646465, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 10:27:03,015 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 10:27:03,016 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-01 10:27:03,016 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 10:27:03,016 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:03,037 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12424.0, 多样性=0.861
2025-08-01 10:27:03,039 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:27:03,044 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.074
2025-08-01 10:27:03,045 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:27:03,046 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.846
2025-08-01 10:27:03,049 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-01 10:27:03,050 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 10:27:03,050 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-01 10:27:03,050 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-01 10:27:03,099 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:27:03,099 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 10:27:03,099 - LandscapeExpert - INFO - 提取到 6 个精英解
2025-08-01 10:27:03,111 - visualization.landscape_visualizer - INFO - 已添加 6 个精英解标记
2025-08-01 10:27:03,230 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_102703.html
2025-08-01 10:27:03,292 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_102703.html
2025-08-01 10:27:03,292 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 10:27:03,292 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 10:27:03,292 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2433秒
2025-08-01 10:27:03,293 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754015223.099508, 'performance_metrics': {}}}
2025-08-01 10:27:03,293 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:27:03,293 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:27:03,294 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12424.0
  • mean_cost: 60408.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 80, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'impro...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:27:03,294 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:27:03,294 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:27:04,822 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Exploitation phase indicated. High mean cost and unexplored space necessitate some exploration. Individuals prioritized based on relative cost."
}
```
2025-08-01 10:27:04,822 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:27:04,823 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 10:27:04,823 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 10:27:04,823 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Exploitation phase indicated. High mean cost and unexplored space necessitate some exploration. Individuals prioritized based on relative cost."
}
```
2025-08-01 10:27:04,823 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:27:04,823 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 10:27:04,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Exploitation phase indicated. High mean cost and unexplored space necessitate some exploration. Individuals prioritized based on relative cost."
}
```
2025-08-01 10:27:04,824 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:27:04,824 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 10:27:04,824 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:04,824 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:04,824 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 112695.0
2025-08-01 10:27:04,909 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 10:27:04,909 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9584.0, 92818.0]
2025-08-01 10:27:04,909 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:27:04,913 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:04,913 - ExploitationExpert - INFO - populations: [{'tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}, {'tour': [50, 12, 27, 30, 19, 25, 20, 34, 8, 10, 4, 59, 14, 24, 11, 61, 23, 6, 0, 22, 2, 9, 18, 29, 16, 31, 43, 40, 15, 47, 17, 36, 13, 37, 39, 51, 48, 49, 28, 26, 33, 42, 46, 35, 55, 58, 62, 64, 65, 53, 56, 5, 63, 52, 3, 60, 7, 21, 45, 41, 38, 44, 1, 57, 54, 32], 'cur_cost': 66020.0}, {'tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': [31, 25, 35, 12, 52, 51, 46, 40, 5, 39, 28, 32, 47, 27, 44, 19, 64, 56, 22, 9, 1, 45, 6, 58, 14, 41, 29, 15, 55, 23, 17, 3, 33, 50, 26, 43, 61, 13, 53, 18, 16, 62, 0, 37, 65, 7, 11, 20, 36, 60, 21, 48, 24, 54, 49, 38, 34, 10, 30, 8, 42, 2, 59, 57, 4, 63], 'cur_cost': 106131.0}, {'tour': [0, 1, 59, 61, 7, 27, 16, 30, 10, 58, 5, 28, 47, 11, 40, 2, 14, 20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32, 8, 62, 35, 25, 51, 52, 6, 63, 17, 36, 26, 57, 21, 12, 24, 19, 9, 15, 13, 18, 38, 44, 31, 49, 39, 4, 54, 3, 43, 23, 41, 55, 65, 22, 48, 64, 60], 'cur_cost': 96934.0}]
2025-08-01 10:27:04,914 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 10:27:04,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 10:27:04,914 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}
2025-08-01 10:27:04,915 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 10:27:04,915 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:04,915 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:04,915 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 113440.0
2025-08-01 10:27:04,990 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 10:27:04,990 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9584.0, 92818.0]
2025-08-01 10:27:04,991 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:27:04,993 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:04,993 - ExploitationExpert - INFO - populations: [{'tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}, {'tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}, {'tour': [0, 3, 19, 2, 8, 5, 4, 6, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12872.0}, {'tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': [31, 25, 35, 12, 52, 51, 46, 40, 5, 39, 28, 32, 47, 27, 44, 19, 64, 56, 22, 9, 1, 45, 6, 58, 14, 41, 29, 15, 55, 23, 17, 3, 33, 50, 26, 43, 61, 13, 53, 18, 16, 62, 0, 37, 65, 7, 11, 20, 36, 60, 21, 48, 24, 54, 49, 38, 34, 10, 30, 8, 42, 2, 59, 57, 4, 63], 'cur_cost': 106131.0}, {'tour': [0, 1, 59, 61, 7, 27, 16, 30, 10, 58, 5, 28, 47, 11, 40, 2, 14, 20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32, 8, 62, 35, 25, 51, 52, 6, 63, 17, 36, 26, 57, 21, 12, 24, 19, 9, 15, 13, 18, 38, 44, 31, 49, 39, 4, 54, 3, 43, 23, 41, 55, 65, 22, 48, 64, 60], 'cur_cost': 96934.0}]
2025-08-01 10:27:04,995 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:04,995 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 10:27:04,995 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}
2025-08-01 10:27:04,995 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 10:27:04,995 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:04,995 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:04,996 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 124803.0
2025-08-01 10:27:05,097 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 10:27:05,098 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9584.0, 92818.0]
2025-08-01 10:27:05,098 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:27:05,101 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:05,101 - ExploitationExpert - INFO - populations: [{'tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}, {'tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}, {'tour': array([11, 28, 38,  4, 41, 65, 29, 49,  0, 30, 25, 59, 34,  9,  1, 24, 57,
       36, 54, 47, 15, 10, 63,  8, 35, 55, 12, 17, 27, 61, 53, 16, 48,  3,
       23, 46, 50, 51, 21,  2, 14, 64, 22, 26, 56, 43,  6, 13, 33, 60, 44,
        7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37,  5, 40, 45],
      dtype=int64), 'cur_cost': 124803.0}, {'tour': [29, 16, 19, 2, 15, 5, 9, 34, 4, 10, 8, 1, 6, 36, 61, 37, 23, 31, 58, 25, 20, 64, 28, 54, 65, 52, 43, 40, 3, 22, 39, 12, 17, 14, 59, 26, 21, 49, 32, 13, 48, 42, 41, 55, 47, 60, 56, 27, 33, 53, 24, 30, 63, 44, 45, 38, 7, 50, 0, 46, 11, 18, 51, 57, 62, 35], 'cur_cost': 100611.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': [31, 25, 35, 12, 52, 51, 46, 40, 5, 39, 28, 32, 47, 27, 44, 19, 64, 56, 22, 9, 1, 45, 6, 58, 14, 41, 29, 15, 55, 23, 17, 3, 33, 50, 26, 43, 61, 13, 53, 18, 16, 62, 0, 37, 65, 7, 11, 20, 36, 60, 21, 48, 24, 54, 49, 38, 34, 10, 30, 8, 42, 2, 59, 57, 4, 63], 'cur_cost': 106131.0}, {'tour': [0, 1, 59, 61, 7, 27, 16, 30, 10, 58, 5, 28, 47, 11, 40, 2, 14, 20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32, 8, 62, 35, 25, 51, 52, 6, 63, 17, 36, 26, 57, 21, 12, 24, 19, 9, 15, 13, 18, 38, 44, 31, 49, 39, 4, 54, 3, 43, 23, 41, 55, 65, 22, 48, 64, 60], 'cur_cost': 96934.0}]
2025-08-01 10:27:05,102 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 10:27:05,102 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 10:27:05,103 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([11, 28, 38,  4, 41, 65, 29, 49,  0, 30, 25, 59, 34,  9,  1, 24, 57,
       36, 54, 47, 15, 10, 63,  8, 35, 55, 12, 17, 27, 61, 53, 16, 48,  3,
       23, 46, 50, 51, 21,  2, 14, 64, 22, 26, 56, 43,  6, 13, 33, 60, 44,
        7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37,  5, 40, 45],
      dtype=int64), 'cur_cost': 124803.0}
2025-08-01 10:27:05,103 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:27:05,103 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:05,103 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:05,103 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 111803.0
2025-08-01 10:27:05,186 - ExploitationExpert - INFO - res_population_num: 6
2025-08-01 10:27:05,186 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9584.0, 92818.0]
2025-08-01 10:27:05,186 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64)]
2025-08-01 10:27:05,190 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:05,190 - ExploitationExpert - INFO - populations: [{'tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}, {'tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}, {'tour': array([11, 28, 38,  4, 41, 65, 29, 49,  0, 30, 25, 59, 34,  9,  1, 24, 57,
       36, 54, 47, 15, 10, 63,  8, 35, 55, 12, 17, 27, 61, 53, 16, 48,  3,
       23, 46, 50, 51, 21,  2, 14, 64, 22, 26, 56, 43,  6, 13, 33, 60, 44,
        7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37,  5, 40, 45],
      dtype=int64), 'cur_cost': 124803.0}, {'tour': array([18, 42, 25,  3, 60, 57, 34,  6, 62, 26, 10, 43,  9, 41, 16, 24, 13,
        4, 59, 15, 63, 48, 23,  0, 46,  1, 17, 54, 45, 52,  8, 61,  2, 35,
        5, 51, 47, 14, 30, 37, 11, 65,  7, 22, 39, 12, 31, 20, 58, 50, 55,
       21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44],
      dtype=int64), 'cur_cost': 111803.0}, {'tour': [0, 2, 17, 11, 9, 3, 7, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12775.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': [31, 25, 35, 12, 52, 51, 46, 40, 5, 39, 28, 32, 47, 27, 44, 19, 64, 56, 22, 9, 1, 45, 6, 58, 14, 41, 29, 15, 55, 23, 17, 3, 33, 50, 26, 43, 61, 13, 53, 18, 16, 62, 0, 37, 65, 7, 11, 20, 36, 60, 21, 48, 24, 54, 49, 38, 34, 10, 30, 8, 42, 2, 59, 57, 4, 63], 'cur_cost': 106131.0}, {'tour': [0, 1, 59, 61, 7, 27, 16, 30, 10, 58, 5, 28, 47, 11, 40, 2, 14, 20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32, 8, 62, 35, 25, 51, 52, 6, 63, 17, 36, 26, 57, 21, 12, 24, 19, 9, 15, 13, 18, 38, 44, 31, 49, 39, 4, 54, 3, 43, 23, 41, 55, 65, 22, 48, 64, 60], 'cur_cost': 96934.0}]
2025-08-01 10:27:05,192 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 10:27:05,192 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 10:27:05,193 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([18, 42, 25,  3, 60, 57, 34,  6, 62, 26, 10, 43,  9, 41, 16, 24, 13,
        4, 59, 15, 63, 48, 23,  0, 46,  1, 17, 54, 45, 52,  8, 61,  2, 35,
        5, 51, 47, 14, 30, 37, 11, 65,  7, 22, 39, 12, 31, 20, 58, 50, 55,
       21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44],
      dtype=int64), 'cur_cost': 111803.0}
2025-08-01 10:27:05,193 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 10:27:05,193 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:05,193 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:05,193 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 95204.0
2025-08-01 10:27:05,268 - ExploitationExpert - INFO - res_population_num: 7
2025-08-01 10:27:05,269 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9584.0, 92818.0, 9521]
2025-08-01 10:27:05,269 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:05,271 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:05,272 - ExploitationExpert - INFO - populations: [{'tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}, {'tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}, {'tour': array([11, 28, 38,  4, 41, 65, 29, 49,  0, 30, 25, 59, 34,  9,  1, 24, 57,
       36, 54, 47, 15, 10, 63,  8, 35, 55, 12, 17, 27, 61, 53, 16, 48,  3,
       23, 46, 50, 51, 21,  2, 14, 64, 22, 26, 56, 43,  6, 13, 33, 60, 44,
        7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37,  5, 40, 45],
      dtype=int64), 'cur_cost': 124803.0}, {'tour': array([18, 42, 25,  3, 60, 57, 34,  6, 62, 26, 10, 43,  9, 41, 16, 24, 13,
        4, 59, 15, 63, 48, 23,  0, 46,  1, 17, 54, 45, 52,  8, 61,  2, 35,
        5, 51, 47, 14, 30, 37, 11, 65,  7, 22, 39, 12, 31, 20, 58, 50, 55,
       21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44],
      dtype=int64), 'cur_cost': 111803.0}, {'tour': array([25, 21, 59,  6, 65, 56, 62, 31, 23, 16, 40, 42,  9, 35, 51, 18, 44,
        3, 11, 48, 55,  1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26,
       12, 49, 60, 10, 17, 14,  7, 24,  4,  8, 61, 22,  2, 15, 20, 45, 58,
       37, 53, 39,  0, 52, 54, 32, 36, 46,  5, 13, 34, 27, 29, 19],
      dtype=int64), 'cur_cost': 95204.0}, {'tour': [23, 11, 12, 31, 4, 54, 7, 14, 22, 21, 28, 20, 8, 60, 55, 16, 37, 3, 18, 17, 43, 47, 34, 26, 5, 58, 56, 2, 27, 10, 63, 6, 64, 65, 59, 15, 1, 29, 40, 45, 50, 36, 19, 24, 46, 42, 13, 48, 38, 51, 33, 32, 9, 52, 61, 57, 53, 62, 39, 49, 44, 35, 30, 25, 0, 41], 'cur_cost': 68163.0}, {'tour': [0, 13, 5, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14789.0}, {'tour': [0, 5, 3, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12424.0}, {'tour': [31, 25, 35, 12, 52, 51, 46, 40, 5, 39, 28, 32, 47, 27, 44, 19, 64, 56, 22, 9, 1, 45, 6, 58, 14, 41, 29, 15, 55, 23, 17, 3, 33, 50, 26, 43, 61, 13, 53, 18, 16, 62, 0, 37, 65, 7, 11, 20, 36, 60, 21, 48, 24, 54, 49, 38, 34, 10, 30, 8, 42, 2, 59, 57, 4, 63], 'cur_cost': 106131.0}, {'tour': [0, 1, 59, 61, 7, 27, 16, 30, 10, 58, 5, 28, 47, 11, 40, 2, 14, 20, 50, 34, 42, 45, 56, 33, 46, 29, 37, 53, 32, 8, 62, 35, 25, 51, 52, 6, 63, 17, 36, 26, 57, 21, 12, 24, 19, 9, 15, 13, 18, 38, 44, 31, 49, 39, 4, 54, 3, 43, 23, 41, 55, 65, 22, 48, 64, 60], 'cur_cost': 96934.0}]
2025-08-01 10:27:05,274 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:05,274 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 10:27:05,274 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([25, 21, 59,  6, 65, 56, 62, 31, 23, 16, 40, 42,  9, 35, 51, 18, 44,
        3, 11, 48, 55,  1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26,
       12, 49, 60, 10, 17, 14,  7, 24,  4,  8, 61, 22,  2, 15, 20, 45, 58,
       37, 53, 39,  0, 52, 54, 32, 36, 46,  5, 13, 34, 27, 29, 19],
      dtype=int64), 'cur_cost': 95204.0}
2025-08-01 10:27:05,275 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 10:27:05,276 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 10:27:05,277 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:05,282 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:05,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:05,283 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12403.0, 路径长度: 66
2025-08-01 10:27:05,283 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}
2025-08-01 10:27:05,283 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:27:05,283 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:27:05,283 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:05,286 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:05,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:05,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12346.0, 路径长度: 66
2025-08-01 10:27:05,287 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}
2025-08-01 10:27:05,287 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 10:27:05,287 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 10:27:05,288 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:05,295 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:05,296 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:05,296 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56736.0, 路径长度: 66
2025-08-01 10:27:05,296 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}
2025-08-01 10:27:05,297 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 10:27:05,297 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 10:27:05,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:05,300 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:05,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:05,301 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12726.0, 路径长度: 66
2025-08-01 10:27:05,301 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}
2025-08-01 10:27:05,301 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-01 10:27:05,301 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-01 10:27:05,301 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:05,310 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:05,312 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:05,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66602.0, 路径长度: 66
2025-08-01 10:27:05,314 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}
2025-08-01 10:27:05,316 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 19, 23, 46, 39, 27, 28, 22, 58, 50, 53, 15,  3, 51,  6, 54, 26,
       56,  1, 40, 65, 63, 20, 10, 11, 64, 31, 37, 59, 44, 57, 35, 49,  7,
       41, 61, 29, 52, 16, 17, 36, 43, 30, 38, 62, 24, 34, 18, 55,  5, 33,
       32, 25,  4, 13, 42, 21,  0, 45,  9, 60, 48, 47,  8, 14, 12],
      dtype=int64), 'cur_cost': 112695.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 14, 47,  7, 17, 24, 32, 63, 55, 31,  0, 13, 45, 51, 21, 11, 46,
        5, 25, 15, 53, 37, 64, 30, 57,  3, 34, 48, 27, 38, 56, 23, 54, 26,
       22, 58, 41, 40, 35,  8, 59, 33, 42, 52, 28, 43, 12, 65, 60,  2,  1,
       10, 44, 61,  6, 62,  4, 36, 19, 18,  9, 39, 16, 50, 20, 49],
      dtype=int64), 'cur_cost': 113440.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 28, 38,  4, 41, 65, 29, 49,  0, 30, 25, 59, 34,  9,  1, 24, 57,
       36, 54, 47, 15, 10, 63,  8, 35, 55, 12, 17, 27, 61, 53, 16, 48,  3,
       23, 46, 50, 51, 21,  2, 14, 64, 22, 26, 56, 43,  6, 13, 33, 60, 44,
        7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37,  5, 40, 45],
      dtype=int64), 'cur_cost': 124803.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 42, 25,  3, 60, 57, 34,  6, 62, 26, 10, 43,  9, 41, 16, 24, 13,
        4, 59, 15, 63, 48, 23,  0, 46,  1, 17, 54, 45, 52,  8, 61,  2, 35,
        5, 51, 47, 14, 30, 37, 11, 65,  7, 22, 39, 12, 31, 20, 58, 50, 55,
       21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44],
      dtype=int64), 'cur_cost': 111803.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 21, 59,  6, 65, 56, 62, 31, 23, 16, 40, 42,  9, 35, 51, 18, 44,
        3, 11, 48, 55,  1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26,
       12, 49, 60, 10, 17, 14,  7, 24,  4,  8, 61, 22,  2, 15, 20, 45, 58,
       37, 53, 39,  0, 52, 54, 32, 36, 46,  5, 13, 34, 27, 29, 19],
      dtype=int64), 'cur_cost': 95204.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}}]
2025-08-01 10:27:05,317 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:27:05,317 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:05,337 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12346.0, 多样性=0.917
2025-08-01 10:27:05,338 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 10:27:05,338 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 10:27:05,339 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:27:05,341 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09483170219743296, 'best_improvement': 0.006278171281390857}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.06572769953051646}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9521.0, 'new_best_cost': 9521.0, 'quality_improvement': 0.0, 'old_diversity': 0.8001443001443002, 'new_diversity': 0.8001443001443002, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:27:05,342 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 10:27:05,343 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-01 10:27:05,343 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-01 10:27:05,343 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:05,365 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12346.0, 多样性=0.917
2025-08-01 10:27:05,365 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:27:05,373 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.029
2025-08-01 10:27:05,376 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:27:05,382 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.800
2025-08-01 10:27:05,386 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-01 10:27:05,387 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 10:27:05,387 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-01 10:27:05,387 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-01 10:27:05,465 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:27:05,465 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-01 10:27:05,465 - LandscapeExpert - INFO - 提取到 7 个精英解
2025-08-01 10:27:05,477 - visualization.landscape_visualizer - INFO - 已添加 7 个精英解标记
2025-08-01 10:27:05,555 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_4_20250801_102705.html
2025-08-01 10:27:05,610 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_4_20250801_102705.html
2025-08-01 10:27:05,612 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-01 10:27:05,612 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-01 10:27:05,612 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2258秒
2025-08-01 10:27:05,613 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754015225.465464, 'performance_metrics': {}}}
2025-08-01 10:27:05,613 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:27:05,613 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:27:05,614 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12346.0
  • mean_cost: 71875.8
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:27:05,614 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:27:05,614 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:27:07,124 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase. Opportunity region suggests exploration, but focus is exploitation. High diversity and cost improvement deterioration requires mostly exploitation."
}
```
2025-08-01 10:27:07,124 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:27:07,125 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:27:07,125 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:27:07,125 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase. Opportunity region suggests exploration, but focus is exploitation. High diversity and cost improvement deterioration requires mostly exploitation."
}
```
2025-08-01 10:27:07,125 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:27:07,125 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-01 10:27:07,125 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Exploitation phase. Opportunity region suggests exploration, but focus is exploitation. High diversity and cost improvement deterioration requires mostly exploitation."
}
```
2025-08-01 10:27:07,126 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:27:07,126 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 10:27:07,126 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,126 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,127 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 104451.0
2025-08-01 10:27:07,214 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 10:27:07,214 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521]
2025-08-01 10:27:07,214 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:07,218 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,218 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': [29, 14, 47, 7, 17, 24, 32, 63, 55, 31, 0, 13, 45, 51, 21, 11, 46, 5, 25, 15, 53, 37, 64, 30, 57, 3, 34, 48, 27, 38, 56, 23, 54, 26, 22, 58, 41, 40, 35, 8, 59, 33, 42, 52, 28, 43, 12, 65, 60, 2, 1, 10, 44, 61, 6, 62, 4, 36, 19, 18, 9, 39, 16, 50, 20, 49], 'cur_cost': 113440.0}, {'tour': [11, 28, 38, 4, 41, 65, 29, 49, 0, 30, 25, 59, 34, 9, 1, 24, 57, 36, 54, 47, 15, 10, 63, 8, 35, 55, 12, 17, 27, 61, 53, 16, 48, 3, 23, 46, 50, 51, 21, 2, 14, 64, 22, 26, 56, 43, 6, 13, 33, 60, 44, 7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37, 5, 40, 45], 'cur_cost': 124803.0}, {'tour': [18, 42, 25, 3, 60, 57, 34, 6, 62, 26, 10, 43, 9, 41, 16, 24, 13, 4, 59, 15, 63, 48, 23, 0, 46, 1, 17, 54, 45, 52, 8, 61, 2, 35, 5, 51, 47, 14, 30, 37, 11, 65, 7, 22, 39, 12, 31, 20, 58, 50, 55, 21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44], 'cur_cost': 111803.0}, {'tour': [25, 21, 59, 6, 65, 56, 62, 31, 23, 16, 40, 42, 9, 35, 51, 18, 44, 3, 11, 48, 55, 1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26, 12, 49, 60, 10, 17, 14, 7, 24, 4, 8, 61, 22, 2, 15, 20, 45, 58, 37, 53, 39, 0, 52, 54, 32, 36, 46, 5, 13, 34, 27, 29, 19], 'cur_cost': 95204.0}, {'tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}, {'tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}]
2025-08-01 10:27:07,219 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 10:27:07,219 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 10:27:07,220 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}
2025-08-01 10:27:07,220 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 10:27:07,220 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,220 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,221 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 104755.0
2025-08-01 10:27:07,332 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 10:27:07,333 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521]
2025-08-01 10:27:07,333 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:07,340 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,341 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}, {'tour': [11, 28, 38, 4, 41, 65, 29, 49, 0, 30, 25, 59, 34, 9, 1, 24, 57, 36, 54, 47, 15, 10, 63, 8, 35, 55, 12, 17, 27, 61, 53, 16, 48, 3, 23, 46, 50, 51, 21, 2, 14, 64, 22, 26, 56, 43, 6, 13, 33, 60, 44, 7, 31, 62, 39, 58, 19, 52, 32, 18, 42, 20, 37, 5, 40, 45], 'cur_cost': 124803.0}, {'tour': [18, 42, 25, 3, 60, 57, 34, 6, 62, 26, 10, 43, 9, 41, 16, 24, 13, 4, 59, 15, 63, 48, 23, 0, 46, 1, 17, 54, 45, 52, 8, 61, 2, 35, 5, 51, 47, 14, 30, 37, 11, 65, 7, 22, 39, 12, 31, 20, 58, 50, 55, 21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44], 'cur_cost': 111803.0}, {'tour': [25, 21, 59, 6, 65, 56, 62, 31, 23, 16, 40, 42, 9, 35, 51, 18, 44, 3, 11, 48, 55, 1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26, 12, 49, 60, 10, 17, 14, 7, 24, 4, 8, 61, 22, 2, 15, 20, 45, 58, 37, 53, 39, 0, 52, 54, 32, 36, 46, 5, 13, 34, 27, 29, 19], 'cur_cost': 95204.0}, {'tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}, {'tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}]
2025-08-01 10:27:07,345 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 10:27:07,346 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 10:27:07,347 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}
2025-08-01 10:27:07,347 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 10:27:07,347 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,348 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,348 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 111428.0
2025-08-01 10:27:07,425 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 10:27:07,425 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521]
2025-08-01 10:27:07,426 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:07,429 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,429 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}, {'tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}, {'tour': [18, 42, 25, 3, 60, 57, 34, 6, 62, 26, 10, 43, 9, 41, 16, 24, 13, 4, 59, 15, 63, 48, 23, 0, 46, 1, 17, 54, 45, 52, 8, 61, 2, 35, 5, 51, 47, 14, 30, 37, 11, 65, 7, 22, 39, 12, 31, 20, 58, 50, 55, 21, 36, 53, 33, 64, 32, 28, 19, 49, 56, 27, 29, 40, 38, 44], 'cur_cost': 111803.0}, {'tour': [25, 21, 59, 6, 65, 56, 62, 31, 23, 16, 40, 42, 9, 35, 51, 18, 44, 3, 11, 48, 55, 1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26, 12, 49, 60, 10, 17, 14, 7, 24, 4, 8, 61, 22, 2, 15, 20, 45, 58, 37, 53, 39, 0, 52, 54, 32, 36, 46, 5, 13, 34, 27, 29, 19], 'cur_cost': 95204.0}, {'tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}, {'tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}]
2025-08-01 10:27:07,430 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:07,430 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 10:27:07,431 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}
2025-08-01 10:27:07,431 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:27:07,431 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,431 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,431 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 91700.0
2025-08-01 10:27:07,516 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 10:27:07,516 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521]
2025-08-01 10:27:07,517 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:07,520 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,520 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}, {'tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}, {'tour': array([42,  6, 29, 60, 56, 20, 61, 10, 57,  5,  1, 26, 36, 13, 12, 28, 33,
       11, 58, 15, 54, 24,  4, 14, 23, 40, 50, 38, 47, 43, 62, 16,  8, 59,
       27, 34, 41, 49, 25, 45, 64, 63,  7, 46, 51, 18, 31, 30, 39, 17,  0,
       44, 35, 48, 21,  9, 65, 19, 53,  2, 37,  3, 32, 52, 55, 22],
      dtype=int64), 'cur_cost': 91700.0}, {'tour': [25, 21, 59, 6, 65, 56, 62, 31, 23, 16, 40, 42, 9, 35, 51, 18, 44, 3, 11, 48, 55, 1, 64, 63, 50, 28, 41, 33, 30, 57, 43, 47, 38, 26, 12, 49, 60, 10, 17, 14, 7, 24, 4, 8, 61, 22, 2, 15, 20, 45, 58, 37, 53, 39, 0, 52, 54, 32, 36, 46, 5, 13, 34, 27, 29, 19], 'cur_cost': 95204.0}, {'tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}, {'tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}]
2025-08-01 10:27:07,522 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 10:27:07,522 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 10:27:07,522 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([42,  6, 29, 60, 56, 20, 61, 10, 57,  5,  1, 26, 36, 13, 12, 28, 33,
       11, 58, 15, 54, 24,  4, 14, 23, 40, 50, 38, 47, 43, 62, 16,  8, 59,
       27, 34, 41, 49, 25, 45, 64, 63,  7, 46, 51, 18, 31, 30, 39, 17,  0,
       44, 35, 48, 21,  9, 65, 19, 53,  2, 37,  3, 32, 52, 55, 22],
      dtype=int64), 'cur_cost': 91700.0}
2025-08-01 10:27:07,523 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 10:27:07,523 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,523 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,523 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99881.0
2025-08-01 10:27:07,593 - ExploitationExpert - INFO - res_population_num: 9
2025-08-01 10:27:07,593 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:07,593 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:07,597 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,597 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}, {'tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}, {'tour': array([42,  6, 29, 60, 56, 20, 61, 10, 57,  5,  1, 26, 36, 13, 12, 28, 33,
       11, 58, 15, 54, 24,  4, 14, 23, 40, 50, 38, 47, 43, 62, 16,  8, 59,
       27, 34, 41, 49, 25, 45, 64, 63,  7, 46, 51, 18, 31, 30, 39, 17,  0,
       44, 35, 48, 21,  9, 65, 19, 53,  2, 37,  3, 32, 52, 55, 22],
      dtype=int64), 'cur_cost': 91700.0}, {'tour': array([50,  1, 11, 23, 38, 42, 52, 48, 43, 24, 25, 30, 62,  9, 22, 45, 14,
        2, 57, 46,  8,  6, 63, 37, 28, 58, 13, 21, 26, 61, 55, 29, 34, 65,
       53, 41, 31, 51, 60, 40, 33, 59, 49, 32,  5, 56, 47, 15, 20, 39, 12,
       44, 19, 18, 10, 17, 64,  0,  3, 36, 35, 54,  4,  7, 27, 16],
      dtype=int64), 'cur_cost': 99881.0}, {'tour': [0, 3, 17, 16, 18, 12, 22, 23, 13, 20, 21, 19, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': [0, 8, 15, 20, 21, 13, 23, 16, 18, 12, 22, 17, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': [13, 37, 28, 19, 25, 36, 31, 33, 26, 18, 16, 1, 61, 52, 64, 7, 15, 6, 10, 63, 5, 58, 62, 55, 22, 32, 21, 17, 14, 29, 49, 41, 47, 20, 35, 2, 3, 57, 4, 60, 9, 65, 54, 39, 40, 45, 12, 27, 8, 0, 34, 23, 48, 50, 51, 43, 46, 42, 38, 30, 44, 11, 59, 56, 53, 24], 'cur_cost': 56736.0}, {'tour': [0, 11, 17, 5, 4, 8, 2, 6, 9, 3, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12726.0}, {'tour': [31, 24, 20, 11, 3, 57, 54, 63, 17, 4, 18, 26, 5, 12, 35, 1, 52, 60, 13, 29, 16, 36, 30, 19, 25, 10, 23, 33, 15, 40, 22, 9, 6, 2, 53, 56, 55, 39, 43, 45, 47, 44, 49, 21, 32, 48, 34, 7, 64, 58, 65, 8, 0, 37, 14, 28, 50, 38, 51, 46, 42, 27, 61, 59, 62, 41], 'cur_cost': 66602.0}]
2025-08-01 10:27:07,599 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:07,599 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 10:27:07,600 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([50,  1, 11, 23, 38, 42, 52, 48, 43, 24, 25, 30, 62,  9, 22, 45, 14,
        2, 57, 46,  8,  6, 63, 37, 28, 58, 13, 21, 26, 61, 55, 29, 34, 65,
       53, 41, 31, 51, 60, 40, 33, 59, 49, 32,  5, 56, 47, 15, 20, 39, 12,
       44, 19, 18, 10, 17, 64,  0,  3, 36, 35, 54,  4,  7, 27, 16],
      dtype=int64), 'cur_cost': 99881.0}
2025-08-01 10:27:07,600 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 10:27:07,600 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 10:27:07,600 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:07,609 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:07,609 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:07,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58722.0, 路径长度: 66
2025-08-01 10:27:07,610 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [34, 18, 26, 11, 3, 60, 62, 58, 1, 8, 64, 65, 4, 53, 56, 55, 13, 27, 30, 20, 15, 33, 6, 52, 10, 37, 14, 23, 32, 19, 29, 2, 63, 0, 36, 35, 9, 7, 24, 17, 43, 47, 46, 49, 42, 21, 31, 22, 48, 39, 41, 44, 16, 45, 25, 28, 38, 51, 40, 50, 12, 5, 59, 61, 57, 54], 'cur_cost': 58722.0}
2025-08-01 10:27:07,610 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:27:07,610 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:27:07,610 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:07,612 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:07,612 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:07,612 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12344.0, 路径长度: 66
2025-08-01 10:27:07,613 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 10, 22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}
2025-08-01 10:27:07,613 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 10:27:07,613 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 10:27:07,613 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:07,615 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:07,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:07,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12863.0, 路径长度: 66
2025-08-01 10:27:07,616 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 13, 15, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}
2025-08-01 10:27:07,616 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 10:27:07,616 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 10:27:07,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:07,618 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 10:27:07,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:07,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109541.0, 路径长度: 66
2025-08-01 10:27:07,619 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}
2025-08-01 10:27:07,619 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 10:27:07,619 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:07,619 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:07,620 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103053.0
2025-08-01 10:27:07,725 - ExploitationExpert - INFO - res_population_num: 10
2025-08-01 10:27:07,726 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521, 9521]
2025-08-01 10:27:07,726 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 10:27:07,731 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:07,731 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}, {'tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}, {'tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}, {'tour': array([42,  6, 29, 60, 56, 20, 61, 10, 57,  5,  1, 26, 36, 13, 12, 28, 33,
       11, 58, 15, 54, 24,  4, 14, 23, 40, 50, 38, 47, 43, 62, 16,  8, 59,
       27, 34, 41, 49, 25, 45, 64, 63,  7, 46, 51, 18, 31, 30, 39, 17,  0,
       44, 35, 48, 21,  9, 65, 19, 53,  2, 37,  3, 32, 52, 55, 22],
      dtype=int64), 'cur_cost': 91700.0}, {'tour': array([50,  1, 11, 23, 38, 42, 52, 48, 43, 24, 25, 30, 62,  9, 22, 45, 14,
        2, 57, 46,  8,  6, 63, 37, 28, 58, 13, 21, 26, 61, 55, 29, 34, 65,
       53, 41, 31, 51, 60, 40, 33, 59, 49, 32,  5, 56, 47, 15, 20, 39, 12,
       44, 19, 18, 10, 17, 64,  0,  3, 36, 35, 54,  4,  7, 27, 16],
      dtype=int64), 'cur_cost': 99881.0}, {'tour': [34, 18, 26, 11, 3, 60, 62, 58, 1, 8, 64, 65, 4, 53, 56, 55, 13, 27, 30, 20, 15, 33, 6, 52, 10, 37, 14, 23, 32, 19, 29, 2, 63, 0, 36, 35, 9, 7, 24, 17, 43, 47, 46, 49, 42, 21, 31, 22, 48, 39, 41, 44, 16, 45, 25, 28, 38, 51, 40, 50, 12, 5, 59, 61, 57, 54], 'cur_cost': 58722.0}, {'tour': [0, 1, 10, 22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': [0, 13, 15, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}, {'tour': array([10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62,  1,  6, 15, 53,  7, 26,
       17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37,  9, 44,  0, 59,  4, 29,
       27, 35, 31, 39, 65, 13,  2, 48, 20, 64, 32, 19, 28, 25, 23, 43,  5,
       57, 14,  3, 46, 50, 34, 40, 18, 54, 51, 21,  8, 30, 56, 24],
      dtype=int64), 'cur_cost': 103053.0}]
2025-08-01 10:27:07,733 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 10:27:07,734 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-01 10:27:07,734 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62,  1,  6, 15, 53,  7, 26,
       17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37,  9, 44,  0, 59,  4, 29,
       27, 35, 31, 39, 65, 13,  2, 48, 20, 64, 32, 19, 28, 25, 23, 43,  5,
       57, 14,  3, 46, 50, 34, 40, 18, 54, 51, 21,  8, 30, 56, 24],
      dtype=int64), 'cur_cost': 103053.0}
2025-08-01 10:27:07,735 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 42, 52, 65, 31, 58, 57, 19, 48, 44, 17, 56, 55, 23,  4,  6, 18,
       33, 37, 45, 24, 59, 30, 51, 46, 26,  5, 38, 47, 49, 64, 54, 16, 25,
       29, 53,  0, 34, 11, 35, 28, 22, 61, 20,  9, 41,  3, 36, 43, 62,  1,
       32, 15, 21,  2,  8, 13, 50, 10, 27, 63, 14, 12, 39, 60, 40],
      dtype=int64), 'cur_cost': 104451.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  3, 26, 40, 36, 55, 10, 46, 11, 47, 64,  1,  2, 34, 20,  4, 16,
       30, 42, 29, 12, 48, 41, 22, 51, 28, 32, 45,  8, 19, 62, 33,  7, 38,
       23, 56, 63, 39, 15, 24, 49, 18, 14, 37, 17, 65, 57, 50, 54, 43,  6,
        0, 44, 60, 25, 31, 35, 53, 52, 59, 58, 13, 27, 61,  5, 21],
      dtype=int64), 'cur_cost': 104755.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 63, 10,  8, 45, 31, 56, 49,  0,  6, 38, 48, 58, 47, 25, 18, 40,
       55, 35, 30, 65, 33, 11, 14, 24, 64, 23, 52, 26, 20, 41, 12, 32, 16,
       19, 27, 34, 53, 22, 28, 50, 57,  4, 13, 54,  1, 17, 29, 36, 43, 15,
       39, 21, 44,  9, 59, 61, 46,  3,  5,  2, 60, 37, 51, 62, 42],
      dtype=int64), 'cur_cost': 111428.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([42,  6, 29, 60, 56, 20, 61, 10, 57,  5,  1, 26, 36, 13, 12, 28, 33,
       11, 58, 15, 54, 24,  4, 14, 23, 40, 50, 38, 47, 43, 62, 16,  8, 59,
       27, 34, 41, 49, 25, 45, 64, 63,  7, 46, 51, 18, 31, 30, 39, 17,  0,
       44, 35, 48, 21,  9, 65, 19, 53,  2, 37,  3, 32, 52, 55, 22],
      dtype=int64), 'cur_cost': 91700.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([50,  1, 11, 23, 38, 42, 52, 48, 43, 24, 25, 30, 62,  9, 22, 45, 14,
        2, 57, 46,  8,  6, 63, 37, 28, 58, 13, 21, 26, 61, 55, 29, 34, 65,
       53, 41, 31, 51, 60, 40, 33, 59, 49, 32,  5, 56, 47, 15, 20, 39, 12,
       44, 19, 18, 10, 17, 64,  0,  3, 36, 35, 54,  4,  7, 27, 16],
      dtype=int64), 'cur_cost': 99881.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [34, 18, 26, 11, 3, 60, 62, 58, 1, 8, 64, 65, 4, 53, 56, 55, 13, 27, 30, 20, 15, 33, 6, 52, 10, 37, 14, 23, 32, 19, 29, 2, 63, 0, 36, 35, 9, 7, 24, 17, 43, 47, 46, 49, 42, 21, 31, 22, 48, 39, 41, 44, 16, 45, 25, 28, 38, 51, 40, 50, 12, 5, 59, 61, 57, 54], 'cur_cost': 58722.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 10, 22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 15, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62,  1,  6, 15, 53,  7, 26,
       17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37,  9, 44,  0, 59,  4, 29,
       27, 35, 31, 39, 65, 13,  2, 48, 20, 64, 32, 19, 28, 25, 23, 43,  5,
       57, 14,  3, 46, 50, 34, 40, 18, 54, 51, 21,  8, 30, 56, 24],
      dtype=int64), 'cur_cost': 103053.0}}]
2025-08-01 10:27:07,735 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:27:07,735 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:07,755 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12344.0, 多样性=0.951
2025-08-01 10:27:07,755 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-01 10:27:07,756 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-01 10:27:07,756 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:27:07,757 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.005952425948987514, 'best_improvement': 0.00016199578810950914}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.036710719530102624}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.0473065205115906, 'recent_improvements': [-0.00021866117425175675, 0.008434141738008806, -0.09483170219743296], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 9521.0, 'new_best_cost': 9521.0, 'quality_improvement': 0.0, 'old_diversity': 0.8269360269360269, 'new_diversity': 0.8269360269360269, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:27:07,758 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-01 10:27:07,758 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-01 10:27:07,759 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-01 10:27:07,759 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:07,781 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12344.0, 多样性=0.951
2025-08-01 10:27:07,782 - PathExpert - INFO - 开始路径结构分析
2025-08-01 10:27:07,787 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.017
2025-08-01 10:27:07,788 - EliteExpert - INFO - 开始精英解分析
2025-08-01 10:27:07,791 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.827
2025-08-01 10:27:07,793 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-01 10:27:07,794 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 10:27:07,794 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-01 10:27:07,794 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-01 10:27:07,864 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 10:27:07,864 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-01 10:27:07,864 - LandscapeExpert - INFO - 提取到 10 个精英解
2025-08-01 10:27:07,873 - visualization.landscape_visualizer - INFO - 已添加 10 个精英解标记
2025-08-01 10:27:07,995 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_5_20250801_102707.html
2025-08-01 10:27:08,063 - visualization.landscape_visualizer - INFO - 图表已保存: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_5_20250801_102707.html
2025-08-01 10:27:08,063 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-01 10:27:08,063 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-01 10:27:08,065 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2716秒
2025-08-01 10:27:08,065 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754015227.8645027, 'performance_metrics': {}}}
2025-08-01 10:27:08,065 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 10:27:08,065 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 10:27:08,065 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12344.0
  • mean_cost: 80873.8
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: convergence
- Landscape focus suggestion: intensification
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 10:27:08,065 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 10:27:08,065 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 10:27:09,636 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase, but unexplored space exists. High explore ratio; balance between exploration and exploitation. Exploitation focused on better individuals."
}
```
2025-08-01 10:27:09,636 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 10:27:09,636 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:27:09,637 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:27:09,637 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase, but unexplored space exists. High explore ratio; balance between exploration and exploitation. Exploitation focused on better individuals."
}
```
2025-08-01 10:27:09,637 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 10:27:09,637 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-08-01 10:27:09,637 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase, but unexplored space exists. High explore ratio; balance between exploration and exploitation. Exploitation focused on better individuals."
}
```
2025-08-01 10:27:09,638 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 10:27:09,638 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 10:27:09,638 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 10:27:09,638 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:09,643 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 10:27:09,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:09,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108895.0, 路径长度: 66
2025-08-01 10:27:09,644 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}
2025-08-01 10:27:09,644 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 10:27:09,644 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 10:27:09,645 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:09,647 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:09,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:09,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14706.0, 路径长度: 66
2025-08-01 10:27:09,648 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}
2025-08-01 10:27:09,648 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 10:27:09,648 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 10:27:09,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:09,650 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:09,651 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:09,651 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12389.0, 路径长度: 66
2025-08-01 10:27:09,651 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}
2025-08-01 10:27:09,651 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 10:27:09,651 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:09,651 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:09,652 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 98483.0
2025-08-01 10:27:09,734 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 10:27:09,734 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:09,735 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:09,742 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:09,742 - ExploitationExpert - INFO - populations: [{'tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}, {'tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}, {'tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}, {'tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}, {'tour': [50, 1, 11, 23, 38, 42, 52, 48, 43, 24, 25, 30, 62, 9, 22, 45, 14, 2, 57, 46, 8, 6, 63, 37, 28, 58, 13, 21, 26, 61, 55, 29, 34, 65, 53, 41, 31, 51, 60, 40, 33, 59, 49, 32, 5, 56, 47, 15, 20, 39, 12, 44, 19, 18, 10, 17, 64, 0, 3, 36, 35, 54, 4, 7, 27, 16], 'cur_cost': 99881.0}, {'tour': [34, 18, 26, 11, 3, 60, 62, 58, 1, 8, 64, 65, 4, 53, 56, 55, 13, 27, 30, 20, 15, 33, 6, 52, 10, 37, 14, 23, 32, 19, 29, 2, 63, 0, 36, 35, 9, 7, 24, 17, 43, 47, 46, 49, 42, 21, 31, 22, 48, 39, 41, 44, 16, 45, 25, 28, 38, 51, 40, 50, 12, 5, 59, 61, 57, 54], 'cur_cost': 58722.0}, {'tour': [0, 1, 10, 22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': [0, 13, 15, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}, {'tour': [10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62, 1, 6, 15, 53, 7, 26, 17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37, 9, 44, 0, 59, 4, 29, 27, 35, 31, 39, 65, 13, 2, 48, 20, 64, 32, 19, 28, 25, 23, 43, 5, 57, 14, 3, 46, 50, 34, 40, 18, 54, 51, 21, 8, 30, 56, 24], 'cur_cost': 103053.0}]
2025-08-01 10:27:09,743 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 10:27:09,743 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-01 10:27:09,744 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}
2025-08-01 10:27:09,744 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 10:27:09,744 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 10:27:09,744 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:09,748 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 10:27:09,748 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:09,749 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12371.0, 路径长度: 66
2025-08-01 10:27:09,749 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}
2025-08-01 10:27:09,749 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 10:27:09,749 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:09,749 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:09,750 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 108143.0
2025-08-01 10:27:09,860 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 10:27:09,860 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:09,861 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:09,868 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:09,868 - ExploitationExpert - INFO - populations: [{'tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}, {'tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}, {'tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}, {'tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}, {'tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}, {'tour': [0, 1, 10, 22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12344.0}, {'tour': [0, 13, 15, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12863.0}, {'tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}, {'tour': [10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62, 1, 6, 15, 53, 7, 26, 17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37, 9, 44, 0, 59, 4, 29, 27, 35, 31, 39, 65, 13, 2, 48, 20, 64, 32, 19, 28, 25, 23, 43, 5, 57, 14, 3, 46, 50, 34, 40, 18, 54, 51, 21, 8, 30, 56, 24], 'cur_cost': 103053.0}]
2025-08-01 10:27:09,869 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 10:27:09,870 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-01 10:27:09,870 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}
2025-08-01 10:27:09,870 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 10:27:09,870 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 10:27:09,871 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 10:27:09,882 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 10:27:09,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 10:27:09,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63567.0, 路径长度: 66
2025-08-01 10:27:09,883 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [57, 61, 58, 59, 17, 26, 35, 12, 16, 24, 11, 6, 22, 15, 34, 7, 21, 4, 64, 9, 65, 39, 45, 50, 44, 38, 42, 43, 27, 36, 18, 3, 60, 56, 2, 55, 0, 54, 13, 29, 23, 31, 25, 33, 20, 32, 5, 63, 19, 30, 28, 8, 14, 40, 51, 48, 37, 49, 46, 41, 10, 1, 53, 62, 52, 47], 'cur_cost': 63567.0}
2025-08-01 10:27:09,883 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 10:27:09,883 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:09,883 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:09,884 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102739.0
2025-08-01 10:27:09,960 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 10:27:09,960 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:09,960 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:09,965 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:09,965 - ExploitationExpert - INFO - populations: [{'tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}, {'tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}, {'tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}, {'tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}, {'tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}, {'tour': [57, 61, 58, 59, 17, 26, 35, 12, 16, 24, 11, 6, 22, 15, 34, 7, 21, 4, 64, 9, 65, 39, 45, 50, 44, 38, 42, 43, 27, 36, 18, 3, 60, 56, 2, 55, 0, 54, 13, 29, 23, 31, 25, 33, 20, 32, 5, 63, 19, 30, 28, 8, 14, 40, 51, 48, 37, 49, 46, 41, 10, 1, 53, 62, 52, 47], 'cur_cost': 63567.0}, {'tour': array([45, 42, 19, 12, 46, 63, 56,  0, 58, 25, 59, 52, 37,  9, 31,  7,  6,
       61, 20, 43, 49, 54, 30, 27, 13,  8, 29, 48, 15, 14, 36, 16, 11, 41,
       33, 39,  2, 50, 53, 22, 24, 34, 35,  4, 17, 65, 26, 28, 55, 57, 38,
       10, 64, 44, 47, 32, 40, 60, 18,  1, 62, 23, 21, 51,  3,  5],
      dtype=int64), 'cur_cost': 102739.0}, {'tour': [14, 6, 58, 38, 40, 26, 23, 30, 32, 47, 64, 12, 13, 37, 48, 28, 3, 44, 19, 17, 50, 27, 61, 65, 5, 39, 15, 24, 20, 56, 43, 49, 54, 63, 2, 0, 18, 25, 45, 62, 22, 34, 4, 36, 10, 51, 21, 16, 42, 29, 1, 60, 41, 55, 9, 59, 11, 35, 57, 31, 53, 46, 7, 8, 33, 52], 'cur_cost': 109541.0}, {'tour': [10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62, 1, 6, 15, 53, 7, 26, 17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37, 9, 44, 0, 59, 4, 29, 27, 35, 31, 39, 65, 13, 2, 48, 20, 64, 32, 19, 28, 25, 23, 43, 5, 57, 14, 3, 46, 50, 34, 40, 18, 54, 51, 21, 8, 30, 56, 24], 'cur_cost': 103053.0}]
2025-08-01 10:27:09,967 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:09,967 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-01 10:27:09,967 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([45, 42, 19, 12, 46, 63, 56,  0, 58, 25, 59, 52, 37,  9, 31,  7,  6,
       61, 20, 43, 49, 54, 30, 27, 13,  8, 29, 48, 15, 14, 36, 16, 11, 41,
       33, 39,  2, 50, 53, 22, 24, 34, 35,  4, 17, 65, 26, 28, 55, 57, 38,
       10, 64, 44, 47, 32, 40, 60, 18,  1, 62, 23, 21, 51,  3,  5],
      dtype=int64), 'cur_cost': 102739.0}
2025-08-01 10:27:09,967 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 10:27:09,968 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:09,968 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:09,968 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 116856.0
2025-08-01 10:27:10,044 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 10:27:10,044 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:10,044 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:10,051 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:10,051 - ExploitationExpert - INFO - populations: [{'tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}, {'tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}, {'tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}, {'tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}, {'tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}, {'tour': [57, 61, 58, 59, 17, 26, 35, 12, 16, 24, 11, 6, 22, 15, 34, 7, 21, 4, 64, 9, 65, 39, 45, 50, 44, 38, 42, 43, 27, 36, 18, 3, 60, 56, 2, 55, 0, 54, 13, 29, 23, 31, 25, 33, 20, 32, 5, 63, 19, 30, 28, 8, 14, 40, 51, 48, 37, 49, 46, 41, 10, 1, 53, 62, 52, 47], 'cur_cost': 63567.0}, {'tour': array([45, 42, 19, 12, 46, 63, 56,  0, 58, 25, 59, 52, 37,  9, 31,  7,  6,
       61, 20, 43, 49, 54, 30, 27, 13,  8, 29, 48, 15, 14, 36, 16, 11, 41,
       33, 39,  2, 50, 53, 22, 24, 34, 35,  4, 17, 65, 26, 28, 55, 57, 38,
       10, 64, 44, 47, 32, 40, 60, 18,  1, 62, 23, 21, 51,  3,  5],
      dtype=int64), 'cur_cost': 102739.0}, {'tour': array([ 4, 60, 29, 49,  0, 30, 16, 48, 54, 10, 33, 21, 15, 53, 32, 46, 34,
       19, 20, 43, 14, 12, 42, 27, 59,  3, 64, 37, 55,  1, 18, 56, 38, 45,
       52,  7, 28, 50,  5, 26, 61, 65,  8, 51, 23, 63, 47, 57,  9, 35, 22,
       62, 41,  6, 36, 44, 17,  2, 39, 31, 40, 11, 58, 13, 25, 24],
      dtype=int64), 'cur_cost': 116856.0}, {'tour': [10, 55, 63, 49, 47, 12, 60, 16, 22, 58, 62, 1, 6, 15, 53, 7, 26, 17, 45, 41, 52, 38, 33, 42, 11, 61, 36, 37, 9, 44, 0, 59, 4, 29, 27, 35, 31, 39, 65, 13, 2, 48, 20, 64, 32, 19, 28, 25, 23, 43, 5, 57, 14, 3, 46, 50, 34, 40, 18, 54, 51, 21, 8, 30, 56, 24], 'cur_cost': 103053.0}]
2025-08-01 10:27:10,053 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-01 10:27:10,053 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-01 10:27:10,054 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 4, 60, 29, 49,  0, 30, 16, 48, 54, 10, 33, 21, 15, 53, 32, 46, 34,
       19, 20, 43, 14, 12, 42, 27, 59,  3, 64, 37, 55,  1, 18, 56, 38, 45,
       52,  7, 28, 50,  5, 26, 61, 65,  8, 51, 23, 63, 47, 57,  9, 35, 22,
       62, 41,  6, 36, 44, 17,  2, 39, 31, 40, 11, 58, 13, 25, 24],
      dtype=int64), 'cur_cost': 116856.0}
2025-08-01 10:27:10,054 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 10:27:10,054 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 10:27:10,054 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 10:27:10,054 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 102397.0
2025-08-01 10:27:10,162 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 10:27:10,162 - ExploitationExpert - INFO - res_population_costs: [9521.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9584.0, 92818.0, 9521, 9521]
2025-08-01 10:27:10,162 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 65,  9, 50, 20, 24, 15, 32,  1, 54, 39, 48, 47, 40, 37, 46,  3,
       19, 38, 41, 61, 29, 18, 60, 58, 57, 62, 11, 33, 34, 51, 14, 42, 56,
       13, 59, 49, 17,  5, 10, 64, 16,  8, 55, 52,  6, 23, 28, 36,  2, 27,
       45, 30,  4,  7, 53, 12, 22, 31, 25, 43, 35, 63, 21, 26, 44],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 10:27:10,168 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 10:27:10,168 - ExploitationExpert - INFO - populations: [{'tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}, {'tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}, {'tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}, {'tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}, {'tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}, {'tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}, {'tour': [57, 61, 58, 59, 17, 26, 35, 12, 16, 24, 11, 6, 22, 15, 34, 7, 21, 4, 64, 9, 65, 39, 45, 50, 44, 38, 42, 43, 27, 36, 18, 3, 60, 56, 2, 55, 0, 54, 13, 29, 23, 31, 25, 33, 20, 32, 5, 63, 19, 30, 28, 8, 14, 40, 51, 48, 37, 49, 46, 41, 10, 1, 53, 62, 52, 47], 'cur_cost': 63567.0}, {'tour': array([45, 42, 19, 12, 46, 63, 56,  0, 58, 25, 59, 52, 37,  9, 31,  7,  6,
       61, 20, 43, 49, 54, 30, 27, 13,  8, 29, 48, 15, 14, 36, 16, 11, 41,
       33, 39,  2, 50, 53, 22, 24, 34, 35,  4, 17, 65, 26, 28, 55, 57, 38,
       10, 64, 44, 47, 32, 40, 60, 18,  1, 62, 23, 21, 51,  3,  5],
      dtype=int64), 'cur_cost': 102739.0}, {'tour': array([ 4, 60, 29, 49,  0, 30, 16, 48, 54, 10, 33, 21, 15, 53, 32, 46, 34,
       19, 20, 43, 14, 12, 42, 27, 59,  3, 64, 37, 55,  1, 18, 56, 38, 45,
       52,  7, 28, 50,  5, 26, 61, 65,  8, 51, 23, 63, 47, 57,  9, 35, 22,
       62, 41,  6, 36, 44, 17,  2, 39, 31, 40, 11, 58, 13, 25, 24],
      dtype=int64), 'cur_cost': 116856.0}, {'tour': array([35,  9, 15, 14,  3, 34, 41, 18, 22, 42, 52, 21,  6,  5, 50, 19, 39,
       28, 56, 58, 13, 12, 24,  7, 54, 36,  8, 64, 49, 29, 30, 40, 62,  4,
       37, 20, 53, 16, 31, 57,  1, 23, 33, 61, 32, 11, 17, 44, 38, 55, 59,
        2, 60, 48, 47, 26, 51, 45, 63,  0, 25, 27, 46, 43, 65, 10],
      dtype=int64), 'cur_cost': 102397.0}]
2025-08-01 10:27:10,171 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 10:27:10,171 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 10:27:10,171 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35,  9, 15, 14,  3, 34, 41, 18, 22, 42, 52, 21,  6,  5, 50, 19, 39,
       28, 56, 58, 13, 12, 24,  7, 54, 36,  8, 64, 49, 29, 30, 40, 62,  4,
       37, 20, 53, 16, 31, 57,  1, 23, 33, 61, 32, 11, 17, 44, 38, 55, 59,
        2, 60, 48, 47, 26, 51, 45, 63,  0, 25, 27, 46, 43, 65, 10],
      dtype=int64), 'cur_cost': 102397.0}
2025-08-01 10:27:10,172 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [42, 63, 29, 8, 45, 31, 57, 49, 11, 6, 17, 56, 55, 47, 20, 18, 33, 2, 37, 30, 12, 59, 41, 22, 46, 28, 23, 52, 14, 43, 64, 54, 7, 38, 19, 53, 0, 34, 25, 35, 50, 62, 61, 13, 9, 65, 3, 36, 39, 24, 15, 60, 44, 21, 10, 5, 51, 40, 1, 27, 58, 4, 32, 16, 48, 26], 'cur_cost': 108895.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 5, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14706.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 44, 50,  0, 63, 64, 55, 36, 21, 39, 33, 29,  3, 37, 42, 11, 25,
       51, 58, 56,  2, 10, 43, 26, 31, 38, 48, 12, 45,  6, 18, 40, 13, 28,
       47, 61, 53,  4, 57, 16, 20, 19, 22, 59, 23,  1, 52, 49, 46, 27, 65,
       32, 60, 15, 62, 30,  9,  5,  8, 34,  7, 54, 17, 24, 14, 41],
      dtype=int64), 'cur_cost': 98483.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12371.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 19,  2, 51, 46, 63, 47, 32, 52, 41, 25, 31,  9, 44, 39, 24, 57,
       37, 29, 11, 61,  1,  3, 33, 10, 17, 45,  0, 64, 62,  4, 40, 56, 55,
       12, 36,  8, 43, 20, 60, 22, 23,  5,  6, 34, 65, 28, 59, 49, 38, 21,
        7, 30, 35, 18, 53, 48, 14, 58, 13, 26, 54, 16, 50, 42, 15],
      dtype=int64), 'cur_cost': 108143.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [57, 61, 58, 59, 17, 26, 35, 12, 16, 24, 11, 6, 22, 15, 34, 7, 21, 4, 64, 9, 65, 39, 45, 50, 44, 38, 42, 43, 27, 36, 18, 3, 60, 56, 2, 55, 0, 54, 13, 29, 23, 31, 25, 33, 20, 32, 5, 63, 19, 30, 28, 8, 14, 40, 51, 48, 37, 49, 46, 41, 10, 1, 53, 62, 52, 47], 'cur_cost': 63567.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([45, 42, 19, 12, 46, 63, 56,  0, 58, 25, 59, 52, 37,  9, 31,  7,  6,
       61, 20, 43, 49, 54, 30, 27, 13,  8, 29, 48, 15, 14, 36, 16, 11, 41,
       33, 39,  2, 50, 53, 22, 24, 34, 35,  4, 17, 65, 26, 28, 55, 57, 38,
       10, 64, 44, 47, 32, 40, 60, 18,  1, 62, 23, 21, 51,  3,  5],
      dtype=int64), 'cur_cost': 102739.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 60, 29, 49,  0, 30, 16, 48, 54, 10, 33, 21, 15, 53, 32, 46, 34,
       19, 20, 43, 14, 12, 42, 27, 59,  3, 64, 37, 55,  1, 18, 56, 38, 45,
       52,  7, 28, 50,  5, 26, 61, 65,  8, 51, 23, 63, 47, 57,  9, 35, 22,
       62, 41,  6, 36, 44, 17,  2, 39, 31, 40, 11, 58, 13, 25, 24],
      dtype=int64), 'cur_cost': 116856.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35,  9, 15, 14,  3, 34, 41, 18, 22, 42, 52, 21,  6,  5, 50, 19, 39,
       28, 56, 58, 13, 12, 24,  7, 54, 36,  8, 64, 49, 29, 30, 40, 62,  4,
       37, 20, 53, 16, 31, 57,  1, 23, 33, 61, 32, 11, 17, 44, 38, 55, 59,
        2, 60, 48, 47, 26, 51, 45, 63,  0, 25, 27, 46, 43, 65, 10],
      dtype=int64), 'cur_cost': 102397.0}}]
2025-08-01 10:27:10,172 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 10:27:10,172 - StatsExpert - INFO - 开始统计分析
2025-08-01 10:27:10,192 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12371.0, 多样性=0.918
2025-08-01 10:27:10,192 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-01 10:27:10,192 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-01 10:27:10,192 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 10:27:10,196 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.011139552549562568, 'best_improvement': -0.002187297472456254}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03470254957507086}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0071932838434981645, 'recent_improvements': [0.008434141738008806, -0.09483170219743296, -0.005952425948987514], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9521.0, 'new_best_cost': 9521.0, 'quality_improvement': 0.0, 'old_diversity': 0.7780073461891645, 'new_diversity': 0.7780073461891645, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 10:27:10,198 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-01 10:27:10,211 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-01 10:27:10,212 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250801_102710.solution
2025-08-01 10:27:10,212 - __main__ - INFO - 实例 composite13_66 处理完成
