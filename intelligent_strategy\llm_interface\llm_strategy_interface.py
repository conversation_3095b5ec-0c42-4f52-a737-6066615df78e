"""
LLM-based strategy interface for the intelligent strategy selection system.

This module implements the interface between the strategy selection system
and Large Language Models for intelligent strategy recommendation.
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import openai

from ..core.individual_state import IndividualState
from ..core.data_structures import StrategyAssignment, StrategyType, ExplorationParameters, ExploitationParameters
from ..core.landscape_analysis import LandscapeFeatures
from ..core.strategy_interfaces import StrategySelectionInterface


@dataclass
class LLMRequest:
    """Request structure for LLM strategy selection."""
    individual_id: int
    landscape_context: Dict[str, Any]
    individual_context: Dict[str, Any]
    population_context: Dict[str, Any]
    historical_performance: Dict[str, Any]


@dataclass
class LLMResponse:
    """Response structure from LLM strategy selection."""
    strategy_type: str
    confidence: float
    reasoning: str
    exploration_params: Optional[Dict[str, float]] = None
    exploitation_params: Optional[Dict[str, float]] = None
    priority: float = 0.5
    time_budget: float = 1.0


class LLMStrategyInterface(StrategySelectionInterface):
    """
    LLM-based strategy selection interface.
    
    This class implements intelligent strategy selection using Large Language Models
    to analyze fitness landscape characteristics and recommend appropriate strategies.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the LLM strategy interface."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # LLM configuration
        self.model_name = config.get('model_name', 'gpt-4')
        self.temperature = config.get('temperature', 0.7)
        self.max_tokens = config.get('max_tokens', 2000)
        self.timeout = config.get('timeout', 30)
        
        # OpenAI client setup
        openai.api_key = config.get('api_key')
        if not openai.api_key:
            self.logger.warning("OpenAI API key not provided. LLM interface will use fallback strategy selection.")
        
        # Strategy selection history
        self.selection_history: List[Dict] = []
        self.confidence_scores: Dict[int, float] = {}
        
        # Performance tracking
        self.llm_call_count = 0
        self.total_llm_time = 0.0
        self.fallback_count = 0
    
    def select_strategies(self, 
                         landscape_analysis: LandscapeFeatures,
                         individual_states: List[IndividualState],
                         population_context: Dict[str, Any]) -> Dict[int, StrategyAssignment]:
        """
        Select strategies using LLM-based analysis.
        
        Args:
            landscape_analysis: Current fitness landscape analysis
            individual_states: List of individual states
            population_context: Additional population-level context
            
        Returns:
            Dictionary mapping individual IDs to strategy assignments
        """
        start_time = time.time()
        assignments = {}
        
        try:
            # Prepare batch request for efficiency
            llm_requests = self._prepare_llm_requests(
                landscape_analysis, individual_states, population_context
            )
            
            # Process requests (batch or individual based on configuration)
            if self.config.get('use_batch_processing', True) and len(llm_requests) > 1:
                llm_responses = self._process_batch_requests(llm_requests)
            else:
                llm_responses = [self._process_single_request(req) for req in llm_requests]
            
            # Convert LLM responses to strategy assignments
            for request, response in zip(llm_requests, llm_responses):
                if response:
                    assignment = self._convert_response_to_assignment(request, response)
                    assignments[request.individual_id] = assignment
                    self.confidence_scores[request.individual_id] = response.confidence
                else:
                    # Fallback strategy selection
                    assignment = self._fallback_strategy_selection(request)
                    assignments[request.individual_id] = assignment
                    self.confidence_scores[request.individual_id] = 0.3  # Low confidence for fallback
                    self.fallback_count += 1
            
            # Record performance metrics
            execution_time = time.time() - start_time
            self.total_llm_time += execution_time
            self.llm_call_count += 1
            
            # Log selection summary
            self.logger.info(f"Selected strategies for {len(assignments)} individuals in {execution_time:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Error in LLM strategy selection: {e}")
            # Fallback to rule-based selection for all individuals
            assignments = self._fallback_strategy_selection_all(individual_states, landscape_analysis)
        
        return assignments
    
    def update_from_feedback(self, feedback) -> None:
        """Update strategy selection based on execution feedback."""
        # Store feedback for future reference
        self.selection_history.append({
            'timestamp': time.time(),
            'feedback': feedback,
            'iteration': feedback.iteration
        })
        
        # Keep only recent history
        if len(self.selection_history) > 100:
            self.selection_history = self.selection_history[-100:]
        
        self.logger.info(f"Updated LLM interface with feedback from iteration {feedback.iteration}")
    
    def get_selection_confidence(self, individual_id: int) -> float:
        """Get confidence level for the last strategy selection."""
        return self.confidence_scores.get(individual_id, 0.5)
    
    def _prepare_llm_requests(self, 
                            landscape_analysis: LandscapeFeatures,
                            individual_states: List[IndividualState],
                            population_context: Dict[str, Any]) -> List[LLMRequest]:
        """Prepare LLM requests for strategy selection."""
        requests = []
        
        for state in individual_states:
            # Prepare landscape context
            landscape_context = landscape_analysis.get_individual_features(state.individual_id)
            
            # Prepare individual context
            individual_context = {
                'fitness_value': state.fitness_value,
                'fitness_rank': state.fitness_rank,
                'fitness_percentile': state.fitness_percentile,
                'stagnation_duration': state.stagnation_duration,
                'stagnation_level': state.stagnation_level.value,
                'performance_trend': state.performance_trend,
                'recent_improvements': state.recent_improvements[-5:],  # Last 5 improvements
                'strategy_success_rate': state.strategy_success_rate,
                'last_strategy_type': state.last_strategy_type,
                'last_strategy_success': state.last_strategy_success,
                'improvement_potential': state.improvement_potential
            }
            
            # Prepare historical performance context
            historical_performance = self._get_historical_performance_context(state.individual_id)
            
            request = LLMRequest(
                individual_id=state.individual_id,
                landscape_context=landscape_context,
                individual_context=individual_context,
                population_context=population_context,
                historical_performance=historical_performance
            )
            
            requests.append(request)
        
        return requests
    
    def _process_single_request(self, request: LLMRequest) -> Optional[LLMResponse]:
        """Process a single LLM request for strategy selection."""
        try:
            # Construct prompt
            prompt = self._construct_strategy_selection_prompt(request)
            
            # Make LLM call
            response = openai.ChatCompletion.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                timeout=self.timeout
            )
            
            # Parse response
            llm_response = self._parse_llm_response(response.choices[0].message.content)
            return llm_response
            
        except Exception as e:
            self.logger.error(f"Error processing LLM request for individual {request.individual_id}: {e}")
            return None
    
    def _process_batch_requests(self, requests: List[LLMRequest]) -> List[Optional[LLMResponse]]:
        """Process multiple LLM requests in batch."""
        # For now, process individually (batch processing would require more complex prompt engineering)
        return [self._process_single_request(req) for req in requests]
    
    def _construct_strategy_selection_prompt(self, request: LLMRequest) -> str:
        """Construct the prompt for LLM strategy selection."""
        prompt = f"""
You are an expert in evolutionary optimization and fitness landscape analysis. 
Your task is to select the most appropriate strategy for an individual in a TSP optimization algorithm.

## Individual Context:
- Individual ID: {request.individual_id}
- Current fitness: {request.individual_context['fitness_value']:.4f}
- Fitness rank: {request.individual_context['fitness_rank']} (percentile: {request.individual_context['fitness_percentile']:.2%})
- Stagnation duration: {request.individual_context['stagnation_duration']} iterations
- Stagnation level: {request.individual_context['stagnation_level']}
- Performance trend: {request.individual_context['performance_trend']:.3f}
- Recent improvements: {request.individual_context['recent_improvements']}
- Strategy success rate: {request.individual_context['strategy_success_rate']:.2%}
- Last strategy: {request.individual_context['last_strategy_type']} (success: {request.individual_context['last_strategy_success']})
- Improvement potential: {request.individual_context['improvement_potential']:.3f}

## Landscape Context:
- Global ruggedness: {request.landscape_context['global_ruggedness']:.3f}
- Modality: {request.landscape_context['modality']:.3f}
- Deceptiveness: {request.landscape_context['deceptiveness']:.3f}
- Gradient strength: {request.landscape_context['gradient_strength']:.3f}
- Local ruggedness: {request.landscape_context['local_ruggedness']:.3f}
- Diversity level: {request.landscape_context['diversity_level']:.3f}
- Convergence level: {request.landscape_context['convergence_level']:.3f}

## Population Context:
- Population size: {request.population_context.get('population_size', 'N/A')}
- Best fitness: {request.population_context.get('best_fitness', 'N/A')}
- Average fitness: {request.population_context.get('average_fitness', 'N/A')}
- Iteration: {request.population_context.get('iteration', 'N/A')}

## Available Strategies:
1. STRONG_EXPLORATION - High-intensity exploration for escaping local optima
2. BALANCED_EXPLORATION - Moderate exploration with some exploitation
3. INTELLIGENT_EXPLORATION - Adaptive exploration based on landscape features
4. CAUTIOUS_EXPLOITATION - Conservative local search
5. MODERATE_EXPLOITATION - Standard local optimization
6. AGGRESSIVE_EXPLOITATION - Intensive local search
7. INTENSIVE_EXPLOITATION - Maximum exploitation of current region
8. HYBRID - Combination of exploration and exploitation

Please respond with a JSON object containing:
{{
    "strategy_type": "selected_strategy_name",
    "confidence": 0.0-1.0,
    "reasoning": "detailed explanation of your choice",
    "exploration_params": {{
        "exploration_intensity": 0.0-1.0,
        "perturbation_strength": 0.0-1.0,
        "diversification_bias": 0.0-1.0,
        "search_radius": 0.0-1.0,
        "mutation_probability": 0.0-1.0,
        "region_focus_weight": 0.0-1.0,
        "novelty_seeking_factor": 0.0-1.0
    }},
    "exploitation_params": {{
        "local_search_intensity": 0.0-1.0,
        "convergence_threshold": 1e-6,
        "elite_guidance_weight": 0.0-1.0,
        "step_size": 0.0-1.0,
        "search_depth": 1-50,
        "patience": 0-20,
        "gradient_following_strength": 0.0-1.0
    }},
    "priority": 0.0-1.0,
    "time_budget": 0.1-5.0
}}

Consider the individual's current state, landscape characteristics, and historical performance to make the best strategy selection.
"""
        return prompt
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for LLM strategy selection."""
        return """
You are an expert AI assistant specializing in evolutionary optimization and fitness landscape analysis.
Your role is to analyze complex optimization scenarios and recommend the most appropriate search strategies.

Key principles:
1. Use exploration strategies when individuals are stagnated or in deceptive regions
2. Use exploitation strategies when individuals are in promising regions with good gradients
3. Consider landscape ruggedness, modality, and deceptiveness in your decisions
4. Balance exploration and exploitation based on population diversity and convergence
5. Adapt strategy parameters based on individual performance history
6. Provide clear reasoning for your recommendations

Always respond with valid JSON format and provide detailed reasoning for your strategy choices.
"""
    
    def _parse_llm_response(self, response_text: str) -> Optional[LLMResponse]:
        """Parse LLM response into structured format."""
        try:
            # Extract JSON from response
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            data = json.loads(response_text)
            
            # Validate required fields
            required_fields = ['strategy_type', 'confidence', 'reasoning']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Create response object
            llm_response = LLMResponse(
                strategy_type=data['strategy_type'],
                confidence=max(0.0, min(1.0, data['confidence'])),
                reasoning=data['reasoning'],
                exploration_params=data.get('exploration_params'),
                exploitation_params=data.get('exploitation_params'),
                priority=max(0.0, min(1.0, data.get('priority', 0.5))),
                time_budget=max(0.1, min(5.0, data.get('time_budget', 1.0)))
            )
            
            return llm_response
            
        except Exception as e:
            self.logger.error(f"Error parsing LLM response: {e}")
            self.logger.debug(f"Response text: {response_text}")
            return None
    
    def _convert_response_to_assignment(self, 
                                      request: LLMRequest,
                                      response: LLMResponse) -> StrategyAssignment:
        """Convert LLM response to strategy assignment."""
        # Parse strategy type
        try:
            strategy_type = StrategyType(response.strategy_type.lower())
        except ValueError:
            self.logger.warning(f"Invalid strategy type: {response.strategy_type}, using BALANCED_EXPLORATION")
            strategy_type = StrategyType.BALANCED_EXPLORATION
        
        # Create parameter objects
        exploration_params = None
        exploitation_params = None
        
        if response.exploration_params:
            try:
                exploration_params = ExplorationParameters(**response.exploration_params)
            except Exception as e:
                self.logger.warning(f"Error creating exploration parameters: {e}")
                exploration_params = ExplorationParameters()  # Use defaults
        
        if response.exploitation_params:
            try:
                # Handle integer parameters
                params = response.exploitation_params.copy()
                if 'search_depth' in params:
                    params['search_depth'] = int(params['search_depth'])
                if 'patience' in params:
                    params['patience'] = int(params['patience'])
                
                exploitation_params = ExploitationParameters(**params)
            except Exception as e:
                self.logger.warning(f"Error creating exploitation parameters: {e}")
                exploitation_params = ExploitationParameters()  # Use defaults
        
        # Create strategy assignment
        assignment = StrategyAssignment(
            individual_id=request.individual_id,
            strategy_type=strategy_type,
            exploration_params=exploration_params,
            exploitation_params=exploitation_params,
            confidence=response.confidence,
            reasoning=response.reasoning,
            priority=response.priority,
            time_budget=response.time_budget,
            metadata={
                'llm_model': self.model_name,
                'selection_time': time.time(),
                'landscape_context': request.landscape_context,
                'individual_context': request.individual_context
            }
        )
        
        return assignment
    
    def _get_historical_performance_context(self, individual_id: int) -> Dict[str, Any]:
        """Get historical performance context for an individual."""
        # Extract relevant historical information from selection history
        individual_history = [
            entry for entry in self.selection_history
            if individual_id in entry.get('feedback', {}).get('execution_results', [])
        ]
        
        if not individual_history:
            return {'has_history': False}
        
        # Aggregate historical performance
        recent_history = individual_history[-5:]  # Last 5 entries
        
        return {
            'has_history': True,
            'recent_entries': len(recent_history),
            'average_success_rate': 0.5,  # Placeholder - would calculate from actual data
            'preferred_strategies': [],    # Placeholder - would extract from history
            'performance_trend': 'stable'  # Placeholder - would calculate trend
        }
    
    def _fallback_strategy_selection(self, request: LLMRequest) -> StrategyAssignment:
        """Fallback strategy selection when LLM is unavailable."""
        individual_context = request.individual_context
        landscape_context = request.landscape_context
        
        # Simple rule-based strategy selection
        if individual_context['stagnation_duration'] > 10:
            strategy_type = StrategyType.STRONG_EXPLORATION
        elif landscape_context['local_ruggedness'] > 0.7:
            strategy_type = StrategyType.INTELLIGENT_EXPLORATION
        elif individual_context['improvement_potential'] > 0.6:
            strategy_type = StrategyType.MODERATE_EXPLOITATION
        else:
            strategy_type = StrategyType.BALANCED_EXPLORATION
        
        return StrategyAssignment(
            individual_id=request.individual_id,
            strategy_type=strategy_type,
            exploration_params=ExplorationParameters(),
            exploitation_params=ExploitationParameters(),
            confidence=0.3,  # Low confidence for rule-based selection
            reasoning="Fallback rule-based selection due to LLM unavailability",
            priority=0.5,
            time_budget=1.0,
            metadata={'fallback': True}
        )
    
    def _fallback_strategy_selection_all(self, 
                                       individual_states: List[IndividualState],
                                       landscape_analysis: LandscapeFeatures) -> Dict[int, StrategyAssignment]:
        """Fallback strategy selection for all individuals."""
        assignments = {}
        
        for state in individual_states:
            # Create minimal request for fallback
            request = LLMRequest(
                individual_id=state.individual_id,
                landscape_context=landscape_analysis.get_individual_features(state.individual_id),
                individual_context={
                    'stagnation_duration': state.stagnation_duration,
                    'improvement_potential': state.improvement_potential
                },
                population_context={},
                historical_performance={}
            )
            
            assignments[state.individual_id] = self._fallback_strategy_selection(request)
        
        return assignments
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """Get LLM interface performance statistics."""
        avg_time = self.total_llm_time / self.llm_call_count if self.llm_call_count > 0 else 0.0
        
        return {
            'total_llm_calls': self.llm_call_count,
            'total_llm_time': self.total_llm_time,
            'average_llm_time': avg_time,
            'fallback_count': self.fallback_count,
            'fallback_rate': self.fallback_count / max(1, self.llm_call_count),
            'model_name': self.model_name,
            'selection_history_length': len(self.selection_history)
        }
