2025-08-05 10:28:34,424 - __main__ - INFO - simple5_12 开始进化第 1 代
2025-08-05 10:28:34,424 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:34,425 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,426 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=786.000, 多样性=0.835
2025-08-05 10:28:34,427 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:34,428 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.835
2025-08-05 10:28:34,429 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:34,431 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:34,431 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:34,431 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:34,431 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:34,436 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -65.480, 聚类评分: 0.000, 覆盖率: 0.023, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:34,436 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:34,437 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:34,437 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple5_12
2025-08-05 10:28:34,441 - visualization.landscape_visualizer - INFO - 插值约束: 50 个点被约束到最小值 786.00
2025-08-05 10:28:34,442 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.1%, 梯度: 23.14 → 21.97
2025-08-05 10:28:34,743 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\landscape_simple5_12_iter_21_20250805_102834.html
2025-08-05 10:28:34,787 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\dashboard_simple5_12_iter_21_20250805_102834.html
2025-08-05 10:28:34,787 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 21
2025-08-05 10:28:34,787 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:34,787 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3556秒
2025-08-05 10:28:34,787 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 42, 'max_size': 500, 'hits': 0, 'misses': 42, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 66, 'max_size': 100, 'hits': 142, 'misses': 66, 'hit_rate': 0.6826923076923077, 'evictions': 0, 'ttl': 7200}}
2025-08-05 10:28:34,788 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -65.47999999999999, 'local_optima_density': 0.3, 'gradient_variance': 96666.22559999999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0231, 'fitness_entropy': 0.9674887648835616, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -65.480)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.023)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360914.4367015, 'performance_metrics': {}}}
2025-08-05 10:28:34,788 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:34,788 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:34,788 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:34,788 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:34,789 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,789 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:34,789 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,789 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,789 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:34,790 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:34,790 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:34,790 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:34,790 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:34,790 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:34,790 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:34,790 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,791 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:34,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1052.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,792 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 7, 1, 9, 11, 3, 6, 5, 2, 10, 4], 'cur_cost': 1052.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,792 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1052.00)
2025-08-05 10:28:34,792 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:34,792 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:34,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,793 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:34,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,793 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1585.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,793 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 7, 11], 'cur_cost': 1585.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,793 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1585.00)
2025-08-05 10:28:34,793 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:34,793 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:34,794 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,794 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:34,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,794 - ExplorationExpert - INFO - 探索路径生成完成，成本: 945.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,794 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 7, 2, 0, 5, 1, 11, 6, 9, 3, 8, 10], 'cur_cost': 945.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,795 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 945.00)
2025-08-05 10:28:34,795 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:34,795 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:34,795 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,795 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:34,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1198.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,796 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 0], 'cur_cost': 1198.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,796 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1198.00)
2025-08-05 10:28:34,796 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:34,796 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,796 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,796 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1669.0
2025-08-05 10:28:34,800 - ExploitationExpert - INFO - res_population_num: 1
2025-08-05 10:28:34,800 - ExploitationExpert - INFO - res_population_costs: [754.0]
2025-08-05 10:28:34,801 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64)]
2025-08-05 10:28:34,801 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,801 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 7, 1, 9, 11, 3, 6, 5, 2, 10, 4], 'cur_cost': 1052.0}, {'tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 7, 11], 'cur_cost': 1585.0}, {'tour': [4, 7, 2, 0, 5, 1, 11, 6, 9, 3, 8, 10], 'cur_cost': 945.0}, {'tour': [1, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 0], 'cur_cost': 1198.0}, {'tour': array([ 9,  4,  3,  8, 10,  1,  7,  6,  2,  0, 11,  5], dtype=int64), 'cur_cost': 1669.0}, {'tour': array([10,  9,  7,  0,  3, 11,  1,  6,  4,  2,  8,  5], dtype=int64), 'cur_cost': 1226.0}, {'tour': array([ 9,  3, 10, 11,  6,  2,  8,  1,  7,  4,  0,  5], dtype=int64), 'cur_cost': 1406.0}, {'tour': array([10,  8,  1,  7,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1555.0}, {'tour': array([ 2,  7,  8,  5,  1,  3,  9,  6, 10,  0,  4, 11], dtype=int64), 'cur_cost': 1265.0}, {'tour': array([ 8,  3,  0,  7,  4,  2, 11,  9, 10,  5,  6,  1], dtype=int64), 'cur_cost': 1413.0}]
2025-08-05 10:28:34,802 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,802 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 53, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 53, 'cache_hits': 0, 'similarity_calculations': 366, 'cache_hit_rate': 0.0, 'cache_size': 366}}
2025-08-05 10:28:34,802 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 9,  4,  3,  8, 10,  1,  7,  6,  2,  0, 11,  5], dtype=int64), 'cur_cost': 1669.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  1,  6,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  1,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1437.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  6,  4,  8,  1,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  6,  4,  8,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1456.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  3,  6,  4,  8,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,802 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1669.00)
2025-08-05 10:28:34,803 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:34,803 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:34,803 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,803 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:34,803 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1140.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,803 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1140.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,804 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1140.00)
2025-08-05 10:28:34,804 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:34,804 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:34,804 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,804 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:34,804 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,805 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1217.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,805 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1217.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,805 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1217.00)
2025-08-05 10:28:34,805 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:34,805 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:34,805 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:34,805 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1626.0
2025-08-05 10:28:34,809 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:34,809 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:34,809 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:34,810 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:34,810 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 7, 1, 9, 11, 3, 6, 5, 2, 10, 4], 'cur_cost': 1052.0}, {'tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 7, 11], 'cur_cost': 1585.0}, {'tour': [4, 7, 2, 0, 5, 1, 11, 6, 9, 3, 8, 10], 'cur_cost': 945.0}, {'tour': [1, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 0], 'cur_cost': 1198.0}, {'tour': array([ 9,  4,  3,  8, 10,  1,  7,  6,  2,  0, 11,  5], dtype=int64), 'cur_cost': 1669.0}, {'tour': [3, 5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1140.0}, {'tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1217.0}, {'tour': array([ 7,  0,  3,  6, 10,  1,  4,  2,  9,  5,  8, 11], dtype=int64), 'cur_cost': 1626.0}, {'tour': array([ 2,  7,  8,  5,  1,  3,  9,  6, 10,  0,  4, 11], dtype=int64), 'cur_cost': 1265.0}, {'tour': array([ 8,  3,  0,  7,  4,  2, 11,  9, 10,  5,  6,  1], dtype=int64), 'cur_cost': 1413.0}]
2025-08-05 10:28:34,811 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:34,811 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 54, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 54, 'cache_hits': 0, 'similarity_calculations': 367, 'cache_hit_rate': 0.0, 'cache_size': 367}}
2025-08-05 10:28:34,811 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7,  0,  3,  6, 10,  1,  4,  2,  9,  5,  8, 11], dtype=int64), 'cur_cost': 1626.0, 'intermediate_solutions': [{'tour': array([ 1,  8, 10,  7,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  1,  8, 10,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  7,  1,  8, 10,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  7,  1,  8,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1577.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  6,  7,  1,  8,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:34,811 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1626.00)
2025-08-05 10:28:34,812 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:34,812 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:34,812 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,812 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:34,812 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,813 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1136.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,813 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 0], 'cur_cost': 1136.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,813 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1136.00)
2025-08-05 10:28:34,813 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:34,813 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:34,813 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:34,813 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:34,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:34,814 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1604.0, 路径长度: 12, 收集中间解: 0
2025-08-05 10:28:34,814 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 1, 7, 2, 5, 11, 3, 10, 0, 6, 8, 9], 'cur_cost': 1604.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:34,814 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1604.00)
2025-08-05 10:28:34,814 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:34,814 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:34,815 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 7, 1, 9, 11, 3, 6, 5, 2, 10, 4], 'cur_cost': 1052.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 7, 11], 'cur_cost': 1585.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 2, 0, 5, 1, 11, 6, 9, 3, 8, 10], 'cur_cost': 945.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 0], 'cur_cost': 1198.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  4,  3,  8, 10,  1,  7,  6,  2,  0, 11,  5], dtype=int64), 'cur_cost': 1669.0, 'intermediate_solutions': [{'tour': array([ 4,  8,  1,  6,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  4,  8,  1,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1437.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  6,  4,  8,  1,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1510.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  6,  4,  8,  3,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1456.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  3,  6,  4,  8,  9,  5,  0,  7,  2, 11, 10], dtype=int64), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1140.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1217.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  0,  3,  6, 10,  1,  4,  2,  9,  5,  8, 11], dtype=int64), 'cur_cost': 1626.0, 'intermediate_solutions': [{'tour': array([ 1,  8, 10,  7,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1444.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  1,  8, 10,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  7,  1,  8, 10,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  7,  1,  8,  6,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1577.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  6,  7,  1,  8,  0,  3,  9,  5,  2,  4, 11], dtype=int64), 'cur_cost': 1396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 0], 'cur_cost': 1136.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 1, 7, 2, 5, 11, 3, 10, 0, 6, 8, 9], 'cur_cost': 1604.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:34,815 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:34,815 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,816 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=945.000, 多样性=0.881
2025-08-05 10:28:34,817 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:34,817 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:34,817 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:34,817 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.12417911661923671, 'best_improvement': -0.20229007633587787}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.05543237250554336}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.028605833970120547, 'recent_improvements': [0.06897869248809659, -0.057596983893113844, 0.011767024547855491], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 754.0, 'new_best_cost': 754.0, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:34,817 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:34,817 - __main__ - INFO - simple5_12 开始进化第 2 代
2025-08-05 10:28:34,818 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:34,818 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:34,818 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=945.000, 多样性=0.881
2025-08-05 10:28:34,818 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:34,819 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.881
2025-08-05 10:28:34,819 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:34,820 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.833
2025-08-05 10:28:34,822 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:34,822 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:34,822 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:34,822 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:34,831 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.250, 适应度梯度: 57.100, 聚类评分: 0.000, 覆盖率: 0.024, 收敛趋势: 0.000, 多样性: 0.900
2025-08-05 10:28:34,831 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:34,831 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:34,831 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple5_12
2025-08-05 10:28:34,836 - visualization.landscape_visualizer - INFO - 插值约束: 404 个点被约束到最小值 754.00
2025-08-05 10:28:34,838 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.2%, 梯度: 36.06 → 34.54
2025-08-05 10:28:34,971 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\landscape_simple5_12_iter_22_20250805_102834.html
2025-08-05 10:28:35,056 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\dashboard_simple5_12_iter_22_20250805_102834.html
2025-08-05 10:28:35,056 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 22
2025-08-05 10:28:35,056 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:35,056 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2335秒
2025-08-05 10:28:35,056 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.25, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 57.099999999999994, 'local_optima_density': 0.25, 'gradient_variance': 129439.45000000001, 'cluster_count': 0}, 'population_state': {'diversity': 0.9002525252525252, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0243, 'fitness_entropy': 0.9111886696810589, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.024)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.900)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 57.100)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360914.8317819, 'performance_metrics': {}}}
2025-08-05 10:28:35,057 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:35,057 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:35,057 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:35,057 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:35,057 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:35,057 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:35,058 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:35,058 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,058 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:35,058 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:35,058 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,058 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:35,059 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:35,059 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:35,059 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:35,059 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,059 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,059 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,060 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,061 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1225.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,061 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 7, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1225.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 3, 9, 11, 1, 6, 5, 2, 10, 4], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 1, 9, 11, 3, 5, 6, 2, 10, 4], 'cur_cost': 1200.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 1, 9, 3, 6, 5, 11, 2, 10, 4], 'cur_cost': 1232.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,061 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1225.00)
2025-08-05 10:28:35,062 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:35,062 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:35,062 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,063 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,064 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,065 - ExplorationExpert - INFO - 探索路径生成完成，成本: 979.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,065 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 6, 4], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [0, 1, 11, 6, 4, 5, 10, 9, 8, 3, 7, 2], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 11, 7], 'cur_cost': 1486.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 6, 4, 5, 10, 9, 1, 8, 3, 7, 11], 'cur_cost': 1392.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,065 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 979.00)
2025-08-05 10:28:35,066 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:35,066 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:35,066 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,066 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:35,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,067 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,067 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1452.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,068 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 4, 7, 0, 6, 5, 11, 10, 2, 8, 9, 3], 'cur_cost': 1452.0, 'intermediate_solutions': [{'tour': [4, 8, 2, 0, 5, 1, 11, 6, 9, 3, 7, 10], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 2, 0, 5, 1, 11, 8, 3, 9, 6, 10], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 5, 1, 11, 6, 9, 3, 8, 0, 10], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,068 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1452.00)
2025-08-05 10:28:35,068 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:35,068 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:35,068 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,069 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:35,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1420.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,070 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 2, 1, 4, 9, 6, 5, 11, 8, 0, 10, 3], 'cur_cost': 1420.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 1], 'cur_cost': 1256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 10, 4, 0, 8, 6, 9, 11, 7], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 10, 4, 7, 11, 9, 6, 2, 8, 0], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,070 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1420.00)
2025-08-05 10:28:35,071 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:35,071 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,071 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,071 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1573.0
2025-08-05 10:28:35,080 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,080 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,080 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,082 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,082 - ExploitationExpert - INFO - populations: [{'tour': [3, 7, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1225.0}, {'tour': [8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 6, 4], 'cur_cost': 979.0}, {'tour': [1, 4, 7, 0, 6, 5, 11, 10, 2, 8, 9, 3], 'cur_cost': 1452.0}, {'tour': [7, 2, 1, 4, 9, 6, 5, 11, 8, 0, 10, 3], 'cur_cost': 1420.0}, {'tour': array([ 7,  2, 11,  8,  9,  3, 10,  6,  5,  0,  4,  1], dtype=int64), 'cur_cost': 1573.0}, {'tour': [3, 5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1140.0}, {'tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1217.0}, {'tour': [7, 0, 3, 6, 10, 1, 4, 2, 9, 5, 8, 11], 'cur_cost': 1626.0}, {'tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 0], 'cur_cost': 1136.0}, {'tour': [4, 1, 7, 2, 5, 11, 3, 10, 0, 6, 8, 9], 'cur_cost': 1604.0}]
2025-08-05 10:28:35,083 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,083 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 55, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 55, 'cache_hits': 0, 'similarity_calculations': 369, 'cache_hit_rate': 0.0, 'cache_size': 369}}
2025-08-05 10:28:35,084 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 7,  2, 11,  8,  9,  3, 10,  6,  5,  0,  4,  1], dtype=int64), 'cur_cost': 1573.0, 'intermediate_solutions': [{'tour': array([ 3,  4,  9,  8, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  3,  4,  9, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  8,  3,  4,  9,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  8,  3,  4, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1599.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 10,  8,  3,  4,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1679.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,084 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1573.00)
2025-08-05 10:28:35,084 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:35,084 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:35,085 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,085 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,086 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,086 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1165.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,086 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 9, 4, 10, 7, 2, 3, 0, 11, 6], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 9, 1, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6, 3], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,086 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1165.00)
2025-08-05 10:28:35,087 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:35,087 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:35,087 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1046.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,089 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [8, 2, 1, 3, 6, 7, 5, 0, 10, 4, 11, 9], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 9, 11], 'cur_cost': 1230.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 2, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1195.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,089 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1046.00)
2025-08-05 10:28:35,089 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:35,089 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,089 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,090 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1327.0
2025-08-05 10:28:35,098 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,098 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,098 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,098 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,098 - ExploitationExpert - INFO - populations: [{'tour': [3, 7, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1225.0}, {'tour': [8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 6, 4], 'cur_cost': 979.0}, {'tour': [1, 4, 7, 0, 6, 5, 11, 10, 2, 8, 9, 3], 'cur_cost': 1452.0}, {'tour': [7, 2, 1, 4, 9, 6, 5, 11, 8, 0, 10, 3], 'cur_cost': 1420.0}, {'tour': array([ 7,  2, 11,  8,  9,  3, 10,  6,  5,  0,  4,  1], dtype=int64), 'cur_cost': 1573.0}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0}, {'tour': array([ 1,  8,  6,  4, 10,  2,  9,  3,  5,  0,  7, 11], dtype=int64), 'cur_cost': 1327.0}, {'tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 0], 'cur_cost': 1136.0}, {'tour': [4, 1, 7, 2, 5, 11, 3, 10, 0, 6, 8, 9], 'cur_cost': 1604.0}]
2025-08-05 10:28:35,100 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,100 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 56, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 56, 'cache_hits': 0, 'similarity_calculations': 372, 'cache_hit_rate': 0.0, 'cache_size': 372}}
2025-08-05 10:28:35,100 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 1,  8,  6,  4, 10,  2,  9,  3,  5,  0,  7, 11], dtype=int64), 'cur_cost': 1327.0, 'intermediate_solutions': [{'tour': array([ 3,  0,  7,  6, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  3,  0,  7, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  6,  3,  0,  7,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  6,  3,  0, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 10,  6,  3,  0,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,100 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1327.00)
2025-08-05 10:28:35,100 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:35,100 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:35,100 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,101 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,101 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,102 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,102 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,102 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1401.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,102 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 8, 10, 2, 9, 11, 3, 7, 5, 4, 6, 0], 'cur_cost': 1401.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 5], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 11, 0, 6, 4, 10, 2, 7, 1, 9], 'cur_cost': 1355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 0, 4, 6], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,102 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1401.00)
2025-08-05 10:28:35,103 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:35,103 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:35,103 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,104 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,104 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,104 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,105 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,105 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,105 - ExplorationExpert - INFO - 探索路径生成完成，成本: 937.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,105 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 5, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [4, 1, 7, 8, 5, 11, 3, 10, 0, 6, 2, 9], 'cur_cost': 1638.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 7, 2, 5, 11, 8, 6, 0, 10, 3, 9], 'cur_cost': 1586.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 7, 2, 5, 11, 3, 0, 6, 8, 9, 10], 'cur_cost': 1534.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,105 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 937.00)
2025-08-05 10:28:35,106 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:35,106 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:35,107 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 7, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1225.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 3, 9, 11, 1, 6, 5, 2, 10, 4], 'cur_cost': 1021.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 1, 9, 11, 3, 5, 6, 2, 10, 4], 'cur_cost': 1200.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 1, 9, 3, 6, 5, 11, 2, 10, 4], 'cur_cost': 1232.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 6, 4], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [0, 1, 11, 6, 4, 5, 10, 9, 8, 3, 7, 2], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 2, 6, 4, 5, 10, 9, 8, 3, 11, 7], 'cur_cost': 1486.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 6, 4, 5, 10, 9, 1, 8, 3, 7, 11], 'cur_cost': 1392.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 0, 6, 5, 11, 10, 2, 8, 9, 3], 'cur_cost': 1452.0, 'intermediate_solutions': [{'tour': [4, 8, 2, 0, 5, 1, 11, 6, 9, 3, 7, 10], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 2, 0, 5, 1, 11, 8, 3, 9, 6, 10], 'cur_cost': 1067.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 7, 2, 5, 1, 11, 6, 9, 3, 8, 0, 10], 'cur_cost': 884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 1, 4, 9, 6, 5, 11, 8, 0, 10, 3], 'cur_cost': 1420.0, 'intermediate_solutions': [{'tour': [0, 5, 3, 2, 10, 4, 7, 11, 9, 6, 8, 1], 'cur_cost': 1256.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 5, 3, 2, 10, 4, 0, 8, 6, 9, 11, 7], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 5, 3, 10, 4, 7, 11, 9, 6, 2, 8, 0], 'cur_cost': 1174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  2, 11,  8,  9,  3, 10,  6,  5,  0,  4,  1], dtype=int64), 'cur_cost': 1573.0, 'intermediate_solutions': [{'tour': array([ 3,  4,  9,  8, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8,  3,  4,  9, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  8,  3,  4,  9,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  8,  3,  4, 10,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1599.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 10,  8,  3,  4,  1,  7,  6,  2,  0, 11,  5]), 'cur_cost': 1679.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0, 'intermediate_solutions': [{'tour': [8, 5, 1, 9, 4, 10, 7, 2, 3, 0, 11, 6], 'cur_cost': 1360.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 9, 1, 4, 10, 7, 2, 8, 0, 11, 6], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 1, 9, 4, 10, 7, 2, 8, 0, 11, 6, 3], 'cur_cost': 1140.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [8, 2, 1, 3, 6, 7, 5, 0, 10, 4, 11, 9], 'cur_cost': 1139.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 2, 1, 3, 6, 7, 5, 10, 0, 4, 9, 11], 'cur_cost': 1230.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 2, 3, 6, 7, 5, 10, 0, 4, 11, 9], 'cur_cost': 1195.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  8,  6,  4, 10,  2,  9,  3,  5,  0,  7, 11], dtype=int64), 'cur_cost': 1327.0, 'intermediate_solutions': [{'tour': array([ 3,  0,  7,  6, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1609.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  3,  0,  7, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1513.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  6,  3,  0,  7,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1618.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  6,  3,  0, 10,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1607.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 10,  6,  3,  0,  1,  4,  2,  9,  5,  8, 11]), 'cur_cost': 1605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 10, 2, 9, 11, 3, 7, 5, 4, 6, 0], 'cur_cost': 1401.0, 'intermediate_solutions': [{'tour': [0, 8, 3, 11, 9, 1, 7, 2, 10, 4, 6, 5], 'cur_cost': 1028.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 3, 11, 0, 6, 4, 10, 2, 7, 1, 9], 'cur_cost': 1355.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 8, 3, 11, 9, 1, 7, 2, 10, 0, 4, 6], 'cur_cost': 1234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7], 'cur_cost': 937.0, 'intermediate_solutions': [{'tour': [4, 1, 7, 8, 5, 11, 3, 10, 0, 6, 2, 9], 'cur_cost': 1638.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 1, 7, 2, 5, 11, 8, 6, 0, 10, 3, 9], 'cur_cost': 1586.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 1, 7, 2, 5, 11, 3, 0, 6, 8, 9, 10], 'cur_cost': 1534.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:35,108 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:35,108 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,110 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=937.000, 多样性=0.889
2025-08-05 10:28:35,111 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:35,111 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:35,111 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:35,111 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03836814175564645, 'best_improvement': 0.008465608465608466}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008403361344537829}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03329106636306143, 'recent_improvements': [-0.057596983893113844, 0.011767024547855491, -0.12417911661923671], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 754.0, 'new_best_cost': 754.0, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:35,112 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:35,112 - __main__ - INFO - simple5_12 开始进化第 3 代
2025-08-05 10:28:35,112 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:35,112 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,113 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=937.000, 多样性=0.889
2025-08-05 10:28:35,113 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:35,114 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.889
2025-08-05 10:28:35,115 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:35,115 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.833
2025-08-05 10:28:35,117 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:35,118 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:35,118 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:35,118 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:35,129 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: 24.783, 聚类评分: 0.000, 覆盖率: 0.025, 收敛趋势: 0.000, 多样性: 0.893
2025-08-05 10:28:35,130 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:35,130 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:35,130 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple5_12
2025-08-05 10:28:35,135 - visualization.landscape_visualizer - INFO - 插值约束: 62 个点被约束到最小值 754.00
2025-08-05 10:28:35,137 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.5%, 梯度: 32.78 → 30.00
2025-08-05 10:28:35,271 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\landscape_simple5_12_iter_23_20250805_102835.html
2025-08-05 10:28:35,331 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\dashboard_simple5_12_iter_23_20250805_102835.html
2025-08-05 10:28:35,331 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 23
2025-08-05 10:28:35,331 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:35,331 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2139秒
2025-08-05 10:28:35,331 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 24.78333333333333, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 67253.07638888889, 'cluster_count': 0}, 'population_state': {'diversity': 0.8926767676767677, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0253, 'fitness_entropy': 0.972765278018163, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.025)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.893)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 24.783)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360915.1304274, 'performance_metrics': {}}}
2025-08-05 10:28:35,331 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:35,331 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:35,331 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:35,332 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:35,332 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,332 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:35,332 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,332 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,332 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:35,332 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,332 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,333 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:35,333 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:35,333 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:35,333 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:35,333 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,334 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,335 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1012.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,335 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 8, 0], 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': [3, 7, 2, 4, 5, 1, 11, 6, 9, 8, 0, 10], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 2, 4, 10, 0, 8, 9, 1, 11, 6, 5], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,335 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1012.00)
2025-08-05 10:28:35,335 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:35,335 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:35,335 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,335 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,335 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,336 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1277.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,336 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 10, 0], 'cur_cost': 1277.0, 'intermediate_solutions': [{'tour': [8, 3, 2, 10, 7, 5, 0, 11, 1, 9, 6, 4], 'cur_cost': 1190.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 2, 10, 7, 5, 3, 4, 6, 9, 1, 11], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 4], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,336 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 1277.00)
2025-08-05 10:28:35,336 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:35,336 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,336 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,337 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1393.0
2025-08-05 10:28:35,342 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,342 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,342 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,342 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,342 - ExploitationExpert - INFO - populations: [{'tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 8, 0], 'cur_cost': 1012.0}, {'tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 10, 0], 'cur_cost': 1277.0}, {'tour': array([ 2,  9,  3,  1,  5,  7,  8,  4,  0, 11,  6, 10], dtype=int64), 'cur_cost': 1393.0}, {'tour': [7, 2, 1, 4, 9, 6, 5, 11, 8, 0, 10, 3], 'cur_cost': 1420.0}, {'tour': [7, 2, 11, 8, 9, 3, 10, 6, 5, 0, 4, 1], 'cur_cost': 1573.0}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0}, {'tour': [1, 8, 6, 4, 10, 2, 9, 3, 5, 0, 7, 11], 'cur_cost': 1327.0}, {'tour': [1, 8, 10, 2, 9, 11, 3, 7, 5, 4, 6, 0], 'cur_cost': 1401.0}, {'tour': [4, 5, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7], 'cur_cost': 937.0}]
2025-08-05 10:28:35,343 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,343 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 57, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 57, 'cache_hits': 0, 'similarity_calculations': 376, 'cache_hit_rate': 0.0, 'cache_size': 376}}
2025-08-05 10:28:35,343 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 2,  9,  3,  1,  5,  7,  8,  4,  0, 11,  6, 10], dtype=int64), 'cur_cost': 1393.0, 'intermediate_solutions': [{'tour': array([ 7,  4,  1,  0,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  7,  4,  1,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  0,  7,  4,  1,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  0,  7,  4,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  6,  0,  7,  4,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1330.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,344 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1393.00)
2025-08-05 10:28:35,344 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:35,344 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,344 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,344 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1442.0
2025-08-05 10:28:35,348 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,348 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,348 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,349 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,349 - ExploitationExpert - INFO - populations: [{'tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 8, 0], 'cur_cost': 1012.0}, {'tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 10, 0], 'cur_cost': 1277.0}, {'tour': array([ 2,  9,  3,  1,  5,  7,  8,  4,  0, 11,  6, 10], dtype=int64), 'cur_cost': 1393.0}, {'tour': array([ 0,  1,  6,  5, 10,  9,  4,  8,  7,  3, 11,  2], dtype=int64), 'cur_cost': 1442.0}, {'tour': [7, 2, 11, 8, 9, 3, 10, 6, 5, 0, 4, 1], 'cur_cost': 1573.0}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0}, {'tour': [1, 8, 6, 4, 10, 2, 9, 3, 5, 0, 7, 11], 'cur_cost': 1327.0}, {'tour': [1, 8, 10, 2, 9, 11, 3, 7, 5, 4, 6, 0], 'cur_cost': 1401.0}, {'tour': [4, 5, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7], 'cur_cost': 937.0}]
2025-08-05 10:28:35,350 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,350 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 58, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 58, 'cache_hits': 0, 'similarity_calculations': 381, 'cache_hit_rate': 0.0, 'cache_size': 381}}
2025-08-05 10:28:35,350 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 0,  1,  6,  5, 10,  9,  4,  8,  7,  3, 11,  2], dtype=int64), 'cur_cost': 1442.0, 'intermediate_solutions': [{'tour': array([ 1,  2,  7,  4,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  1,  2,  7,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  4,  1,  2,  7,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  4,  1,  2,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1438.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  9,  4,  1,  2,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,350 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1442.00)
2025-08-05 10:28:35,351 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:35,351 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,351 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,351 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1438.0
2025-08-05 10:28:35,356 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,356 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,356 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,357 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,357 - ExploitationExpert - INFO - populations: [{'tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 8, 0], 'cur_cost': 1012.0}, {'tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 10, 0], 'cur_cost': 1277.0}, {'tour': array([ 2,  9,  3,  1,  5,  7,  8,  4,  0, 11,  6, 10], dtype=int64), 'cur_cost': 1393.0}, {'tour': array([ 0,  1,  6,  5, 10,  9,  4,  8,  7,  3, 11,  2], dtype=int64), 'cur_cost': 1442.0}, {'tour': array([ 2,  1,  8,  0,  4, 10,  6,  5, 11,  3,  7,  9], dtype=int64), 'cur_cost': 1438.0}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 6, 10], 'cur_cost': 1165.0}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1046.0}, {'tour': [1, 8, 6, 4, 10, 2, 9, 3, 5, 0, 7, 11], 'cur_cost': 1327.0}, {'tour': [1, 8, 10, 2, 9, 11, 3, 7, 5, 4, 6, 0], 'cur_cost': 1401.0}, {'tour': [4, 5, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7], 'cur_cost': 937.0}]
2025-08-05 10:28:35,358 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,358 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 59, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 59, 'cache_hits': 0, 'similarity_calculations': 387, 'cache_hit_rate': 0.0, 'cache_size': 387}}
2025-08-05 10:28:35,358 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2,  1,  8,  0,  4, 10,  6,  5, 11,  3,  7,  9], dtype=int64), 'cur_cost': 1438.0, 'intermediate_solutions': [{'tour': array([11,  2,  7,  8,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1474.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 11,  2,  7,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  8, 11,  2,  7,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  8, 11,  2,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  9,  8, 11,  2,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,359 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1438.00)
2025-08-05 10:28:35,359 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:35,359 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:35,359 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,359 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:35,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,359 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,360 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,360 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1139.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,360 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 3], 'cur_cost': 1139.0, 'intermediate_solutions': [{'tour': [0, 2, 11, 4, 8, 5, 9, 1, 3, 7, 6, 10], 'cur_cost': 1384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 10, 6], 'cur_cost': 1408.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 11, 6, 4, 8, 5, 7, 1, 3, 9, 10], 'cur_cost': 1212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,360 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1139.00)
2025-08-05 10:28:35,360 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:35,360 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:35,360 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,361 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1099.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,361 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 2, 3, 9, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1099.0, 'intermediate_solutions': [{'tour': [1, 3, 2, 8, 0, 5, 4, 9, 11, 6, 7, 10], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 10, 9, 6, 11], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,362 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1099.00)
2025-08-05 10:28:35,362 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:35,362 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:35,362 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,362 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1204.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,363 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 10, 8, 9], 'cur_cost': 1204.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 4, 10, 6, 9, 3, 5, 0, 7, 11], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 10, 4, 6, 9, 3, 5, 0, 7, 11], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 6, 10, 2, 9, 4, 3, 5, 0, 7, 11], 'cur_cost': 1561.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,364 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1204.00)
2025-08-05 10:28:35,364 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:35,364 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:35,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,364 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 944.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,365 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 7, 10, 8, 0, 2, 3, 11, 6, 9, 1, 4], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [1, 8, 10, 0, 9, 11, 3, 7, 5, 4, 6, 2], 'cur_cost': 1382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 2, 9, 11, 0, 6, 4, 5, 7, 3], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 10, 2, 9, 3, 7, 11, 5, 4, 6, 0], 'cur_cost': 1533.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,366 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 944.00)
2025-08-05 10:28:35,366 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:35,366 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:35,366 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,366 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1142.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,367 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 8, 2, 3, 6, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1142.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 8, 2, 1, 10, 3, 11, 6, 9, 7], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 8, 2, 10, 1, 3, 7, 9, 6, 11], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7, 5], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,367 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 1142.00)
2025-08-05 10:28:35,368 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:35,368 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:35,369 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 8, 0], 'cur_cost': 1012.0, 'intermediate_solutions': [{'tour': [3, 7, 2, 4, 5, 1, 11, 6, 9, 8, 0, 10], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 7, 2, 4, 10, 0, 8, 9, 1, 11, 6, 5], 'cur_cost': 1161.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 2, 4, 5, 6, 11, 1, 9, 8, 0, 10], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 10, 0], 'cur_cost': 1277.0, 'intermediate_solutions': [{'tour': [8, 3, 2, 10, 7, 5, 0, 11, 1, 9, 6, 4], 'cur_cost': 1190.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 2, 10, 7, 5, 3, 4, 6, 9, 1, 11], 'cur_cost': 1068.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 8, 0, 2, 10, 7, 5, 3, 11, 1, 9, 4], 'cur_cost': 1143.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  9,  3,  1,  5,  7,  8,  4,  0, 11,  6, 10], dtype=int64), 'cur_cost': 1393.0, 'intermediate_solutions': [{'tour': array([ 7,  4,  1,  0,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1547.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  7,  4,  1,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1421.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  0,  7,  4,  1,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1478.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1,  0,  7,  4,  6,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  6,  0,  7,  4,  5, 11, 10,  2,  8,  9,  3]), 'cur_cost': 1330.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  6,  5, 10,  9,  4,  8,  7,  3, 11,  2], dtype=int64), 'cur_cost': 1442.0, 'intermediate_solutions': [{'tour': array([ 1,  2,  7,  4,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  1,  2,  7,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  4,  1,  2,  7,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  4,  1,  2,  9,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1438.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  9,  4,  1,  2,  6,  5, 11,  8,  0, 10,  3]), 'cur_cost': 1620.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  1,  8,  0,  4, 10,  6,  5, 11,  3,  7,  9], dtype=int64), 'cur_cost': 1438.0, 'intermediate_solutions': [{'tour': array([11,  2,  7,  8,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1474.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 11,  2,  7,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1578.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  8, 11,  2,  7,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1589.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  8, 11,  2,  9,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7,  9,  8, 11,  2,  3, 10,  6,  5,  0,  4,  1]), 'cur_cost': 1687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 3], 'cur_cost': 1139.0, 'intermediate_solutions': [{'tour': [0, 2, 11, 4, 8, 5, 9, 1, 3, 7, 6, 10], 'cur_cost': 1384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 11, 4, 8, 5, 7, 1, 3, 9, 10, 6], 'cur_cost': 1408.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 11, 6, 4, 8, 5, 7, 1, 3, 9, 10], 'cur_cost': 1212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 2, 3, 9, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1099.0, 'intermediate_solutions': [{'tour': [1, 3, 2, 8, 0, 5, 4, 9, 11, 6, 7, 10], 'cur_cost': 1058.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 8, 0, 5, 4, 7, 10, 9, 6, 11], 'cur_cost': 927.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 1, 2, 8, 0, 5, 4, 7, 11, 6, 9, 10], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 10, 8, 9], 'cur_cost': 1204.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 4, 10, 6, 9, 3, 5, 0, 7, 11], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 10, 4, 6, 9, 3, 5, 0, 7, 11], 'cur_cost': 1049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 6, 10, 2, 9, 4, 3, 5, 0, 7, 11], 'cur_cost': 1561.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 10, 8, 0, 2, 3, 11, 6, 9, 1, 4], 'cur_cost': 944.0, 'intermediate_solutions': [{'tour': [1, 8, 10, 0, 9, 11, 3, 7, 5, 4, 6, 2], 'cur_cost': 1382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 2, 9, 11, 0, 6, 4, 5, 7, 3], 'cur_cost': 1405.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 8, 10, 2, 9, 3, 7, 11, 5, 4, 6, 0], 'cur_cost': 1533.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 2, 3, 6, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1142.0, 'intermediate_solutions': [{'tour': [4, 5, 0, 8, 2, 1, 10, 3, 11, 6, 9, 7], 'cur_cost': 1076.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 5, 0, 8, 2, 10, 1, 3, 7, 9, 6, 11], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 8, 2, 10, 1, 3, 11, 6, 9, 7, 5], 'cur_cost': 990.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:35,369 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:35,369 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,370 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=944.000, 多样性=0.904
2025-08-05 10:28:35,370 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:35,371 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:35,371 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:35,371 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03684674797197012, 'best_improvement': -0.007470651013874066}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01666666666666644}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01330055860389548, 'recent_improvements': [0.011767024547855491, -0.12417911661923671, 0.03836814175564645], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 754.0, 'new_best_cost': 754.0, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:35,371 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:35,371 - __main__ - INFO - simple5_12 开始进化第 4 代
2025-08-05 10:28:35,371 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:35,372 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,372 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=944.000, 多样性=0.904
2025-08-05 10:28:35,372 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:35,373 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.904
2025-08-05 10:28:35,374 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:35,374 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.833
2025-08-05 10:28:35,376 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:35,376 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:35,377 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:35,377 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:35,384 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.167, 适应度梯度: -32.117, 聚类评分: 0.000, 覆盖率: 0.026, 收敛趋势: 0.000, 多样性: 0.904
2025-08-05 10:28:35,384 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:35,385 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:35,385 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple5_12
2025-08-05 10:28:35,388 - visualization.landscape_visualizer - INFO - 插值约束: 64 个点被约束到最小值 754.00
2025-08-05 10:28:35,390 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.2%, 梯度: 25.66 → 24.08
2025-08-05 10:28:35,524 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\landscape_simple5_12_iter_24_20250805_102835.html
2025-08-05 10:28:35,584 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\dashboard_simple5_12_iter_24_20250805_102835.html
2025-08-05 10:28:35,584 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 24
2025-08-05 10:28:35,584 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:35,585 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2091秒
2025-08-05 10:28:35,585 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.16666666666666666, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -32.11666666666667, 'local_optima_density': 0.16666666666666666, 'gradient_variance': 51691.50972222222, 'cluster_count': 0}, 'population_state': {'diversity': 0.904040404040404, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0263, 'fitness_entropy': 0.9353340267248303, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -32.117)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.026)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.904)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360915.3847888, 'performance_metrics': {}}}
2025-08-05 10:28:35,585 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:35,585 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:35,585 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:35,585 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:35,586 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,586 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:35,586 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,586 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,586 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:35,586 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,586 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,587 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:35,587 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 0} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:35,587 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:35,587 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:35,587 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,587 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,587 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,588 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,588 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1294.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,588 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 5, 1, 10, 4, 9, 11, 3, 7, 8, 0, 6], 'cur_cost': 1294.0, 'intermediate_solutions': [{'tour': [5, 10, 4, 2, 7, 11, 3, 8, 1, 6, 9, 0], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 0, 8], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 2, 7, 11, 3, 9, 4, 1, 6, 8, 0], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,588 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 1294.00)
2025-08-05 10:28:35,589 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,590 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1431.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,590 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 11, 0, 6, 3, 1, 7, 2, 8, 10, 4, 9], 'cur_cost': 1431.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 4, 5, 9, 11, 1, 7, 6, 10, 0], 'cur_cost': 1295.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 0, 10], 'cur_cost': 1410.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 11, 2, 6, 5, 9, 1, 7, 4, 10, 0], 'cur_cost': 1502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,590 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1431.00)
2025-08-05 10:28:35,590 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:35,590 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,590 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,590 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 1249.0
2025-08-05 10:28:35,596 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,596 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,596 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,597 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,597 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 1, 10, 4, 9, 11, 3, 7, 8, 0, 6], 'cur_cost': 1294.0}, {'tour': [5, 11, 0, 6, 3, 1, 7, 2, 8, 10, 4, 9], 'cur_cost': 1431.0}, {'tour': array([ 5, 10,  1,  0,  8,  7,  3,  9,  4, 11,  6,  2], dtype=int64), 'cur_cost': 1249.0}, {'tour': [0, 1, 6, 5, 10, 9, 4, 8, 7, 3, 11, 2], 'cur_cost': 1442.0}, {'tour': [2, 1, 8, 0, 4, 10, 6, 5, 11, 3, 7, 9], 'cur_cost': 1438.0}, {'tour': [5, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 3], 'cur_cost': 1139.0}, {'tour': [0, 10, 2, 3, 9, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1099.0}, {'tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 10, 8, 9], 'cur_cost': 1204.0}, {'tour': [5, 7, 10, 8, 0, 2, 3, 11, 6, 9, 1, 4], 'cur_cost': 944.0}, {'tour': [1, 8, 2, 3, 6, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1142.0}]
2025-08-05 10:28:35,597 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,597 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 60, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 60, 'cache_hits': 0, 'similarity_calculations': 394, 'cache_hit_rate': 0.0, 'cache_size': 394}}
2025-08-05 10:28:35,598 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 5, 10,  1,  0,  8,  7,  3,  9,  4, 11,  6,  2], dtype=int64), 'cur_cost': 1249.0, 'intermediate_solutions': [{'tour': array([ 3,  9,  2,  1,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  3,  9,  2,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  1,  3,  9,  2,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  1,  3,  9,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  1,  3,  9,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1352.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,598 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1249.00)
2025-08-05 10:28:35,598 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:35,598 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,598 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,598 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1509.0
2025-08-05 10:28:35,603 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,603 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,603 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,604 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,604 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 1, 10, 4, 9, 11, 3, 7, 8, 0, 6], 'cur_cost': 1294.0}, {'tour': [5, 11, 0, 6, 3, 1, 7, 2, 8, 10, 4, 9], 'cur_cost': 1431.0}, {'tour': array([ 5, 10,  1,  0,  8,  7,  3,  9,  4, 11,  6,  2], dtype=int64), 'cur_cost': 1249.0}, {'tour': array([ 4, 10,  1,  0, 11,  9,  8,  7,  3,  2,  6,  5], dtype=int64), 'cur_cost': 1509.0}, {'tour': [2, 1, 8, 0, 4, 10, 6, 5, 11, 3, 7, 9], 'cur_cost': 1438.0}, {'tour': [5, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 3], 'cur_cost': 1139.0}, {'tour': [0, 10, 2, 3, 9, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1099.0}, {'tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 10, 8, 9], 'cur_cost': 1204.0}, {'tour': [5, 7, 10, 8, 0, 2, 3, 11, 6, 9, 1, 4], 'cur_cost': 944.0}, {'tour': [1, 8, 2, 3, 6, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1142.0}]
2025-08-05 10:28:35,605 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,605 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 61, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 61, 'cache_hits': 0, 'similarity_calculations': 402, 'cache_hit_rate': 0.0, 'cache_size': 402}}
2025-08-05 10:28:35,605 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 4, 10,  1,  0, 11,  9,  8,  7,  3,  2,  6,  5], dtype=int64), 'cur_cost': 1509.0, 'intermediate_solutions': [{'tour': array([ 6,  1,  0,  5, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  6,  1,  0, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  6,  1,  0,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  6,  1, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 10,  5,  6,  1,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,606 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1509.00)
2025-08-05 10:28:35,606 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:35,606 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,606 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,606 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1499.0
2025-08-05 10:28:35,611 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,611 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,611 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,612 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,612 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 1, 10, 4, 9, 11, 3, 7, 8, 0, 6], 'cur_cost': 1294.0}, {'tour': [5, 11, 0, 6, 3, 1, 7, 2, 8, 10, 4, 9], 'cur_cost': 1431.0}, {'tour': array([ 5, 10,  1,  0,  8,  7,  3,  9,  4, 11,  6,  2], dtype=int64), 'cur_cost': 1249.0}, {'tour': array([ 4, 10,  1,  0, 11,  9,  8,  7,  3,  2,  6,  5], dtype=int64), 'cur_cost': 1509.0}, {'tour': array([ 2,  1,  7, 10,  9,  0,  3,  6,  4, 11,  8,  5], dtype=int64), 'cur_cost': 1499.0}, {'tour': [5, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 3], 'cur_cost': 1139.0}, {'tour': [0, 10, 2, 3, 9, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1099.0}, {'tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 10, 8, 9], 'cur_cost': 1204.0}, {'tour': [5, 7, 10, 8, 0, 2, 3, 11, 6, 9, 1, 4], 'cur_cost': 944.0}, {'tour': [1, 8, 2, 3, 6, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1142.0}]
2025-08-05 10:28:35,613 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,614 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 62, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 62, 'cache_hits': 0, 'similarity_calculations': 411, 'cache_hit_rate': 0.0, 'cache_size': 411}}
2025-08-05 10:28:35,614 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2,  1,  7, 10,  9,  0,  3,  6,  4, 11,  8,  5], dtype=int64), 'cur_cost': 1499.0, 'intermediate_solutions': [{'tour': array([ 8,  1,  2,  0,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  8,  1,  2,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  0,  8,  1,  2, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1482.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  0,  8,  1,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  4,  0,  8,  1, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,614 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1499.00)
2025-08-05 10:28:35,614 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:35,614 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:35,614 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,615 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1181.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,616 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 9, 1, 5, 7, 4, 10, 8, 0, 3, 11, 6], 'cur_cost': 1181.0, 'intermediate_solutions': [{'tour': [3, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 5], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 4, 6, 7, 2, 11, 9, 0, 1, 8, 3], 'cur_cost': 1370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 4, 6, 7, 2, 8, 11, 9, 1, 0, 3], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,616 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 1181.00)
2025-08-05 10:28:35,616 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:35,616 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:35,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,616 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1233.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,617 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 6, 8, 3, 5, 2, 7, 4, 10, 0, 11, 9], 'cur_cost': 1233.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 3, 9, 11, 10, 7, 5, 4, 6, 8], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 2, 9, 3, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 2, 3, 9, 11, 1, 7, 4, 6, 8], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,617 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 1233.00)
2025-08-05 10:28:35,617 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:35,617 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,618 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1184.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,619 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 11, 6, 10], 'cur_cost': 1184.0, 'intermediate_solutions': [{'tour': [2, 5, 0, 3, 7, 11, 1, 6, 4, 10, 8, 9], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 9, 8, 10], 'cur_cost': 1248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 3, 6, 11, 7, 1, 4, 10, 8, 9], 'cur_cost': 1307.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,619 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1184.00)
2025-08-05 10:28:35,619 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:35,619 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:35,619 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,620 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1171.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,620 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 9, 6], 'cur_cost': 1171.0, 'intermediate_solutions': [{'tour': [5, 7, 10, 8, 0, 11, 3, 2, 6, 9, 1, 4], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 10, 6, 11, 3, 2, 0, 8, 9, 1, 4], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 10, 8, 0, 2, 11, 3, 6, 9, 1, 4], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,620 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1171.00)
2025-08-05 10:28:35,621 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,622 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1200.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,622 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [11, 7, 5, 4, 2, 8, 1, 9, 6, 3, 0, 10], 'cur_cost': 1200.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 3, 6, 7, 9, 11, 5, 10, 4, 0], 'cur_cost': 1290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 5, 7, 9, 11, 6, 3, 10, 4, 0], 'cur_cost': 1192.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 8, 2, 3, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,622 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1200.00)
2025-08-05 10:28:35,622 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:35,622 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:35,623 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 1, 10, 4, 9, 11, 3, 7, 8, 0, 6], 'cur_cost': 1294.0, 'intermediate_solutions': [{'tour': [5, 10, 4, 2, 7, 11, 3, 8, 1, 6, 9, 0], 'cur_cost': 1138.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 4, 2, 7, 11, 3, 9, 1, 6, 0, 8], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 2, 7, 11, 3, 9, 4, 1, 6, 8, 0], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 11, 0, 6, 3, 1, 7, 2, 8, 10, 4, 9], 'cur_cost': 1431.0, 'intermediate_solutions': [{'tour': [3, 8, 2, 4, 5, 9, 11, 1, 7, 6, 10, 0], 'cur_cost': 1295.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 6, 5, 9, 11, 1, 7, 4, 0, 10], 'cur_cost': 1410.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 8, 11, 2, 6, 5, 9, 1, 7, 4, 10, 0], 'cur_cost': 1502.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 10,  1,  0,  8,  7,  3,  9,  4, 11,  6,  2], dtype=int64), 'cur_cost': 1249.0, 'intermediate_solutions': [{'tour': array([ 3,  9,  2,  1,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1532.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  3,  9,  2,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1369.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5,  1,  3,  9,  2,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1356.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  1,  3,  9,  5,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1393.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  5,  1,  3,  9,  7,  8,  4,  0, 11,  6, 10]), 'cur_cost': 1352.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 10,  1,  0, 11,  9,  8,  7,  3,  2,  6,  5], dtype=int64), 'cur_cost': 1509.0, 'intermediate_solutions': [{'tour': array([ 6,  1,  0,  5, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1484.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  6,  1,  0, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1514.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  6,  1,  0,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  5,  6,  1, 10,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 10,  5,  6,  1,  9,  4,  8,  7,  3, 11,  2]), 'cur_cost': 1291.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  1,  7, 10,  9,  0,  3,  6,  4, 11,  8,  5], dtype=int64), 'cur_cost': 1499.0, 'intermediate_solutions': [{'tour': array([ 8,  1,  2,  0,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1468.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  8,  1,  2,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  0,  8,  1,  2, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1482.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  0,  8,  1,  4, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  4,  0,  8,  1, 10,  6,  5, 11,  3,  7,  9]), 'cur_cost': 1540.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 1, 5, 7, 4, 10, 8, 0, 3, 11, 6], 'cur_cost': 1181.0, 'intermediate_solutions': [{'tour': [3, 10, 4, 6, 7, 2, 11, 9, 1, 0, 8, 5], 'cur_cost': 1221.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 4, 6, 7, 2, 11, 9, 0, 1, 8, 3], 'cur_cost': 1370.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 4, 6, 7, 2, 8, 11, 9, 1, 0, 3], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 6, 8, 3, 5, 2, 7, 4, 10, 0, 11, 9], 'cur_cost': 1233.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 3, 9, 11, 10, 7, 5, 4, 6, 8], 'cur_cost': 1258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 2, 9, 3, 11, 1, 7, 5, 4, 6, 8], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 2, 3, 9, 11, 1, 7, 4, 6, 8], 'cur_cost': 1050.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 11, 6, 10], 'cur_cost': 1184.0, 'intermediate_solutions': [{'tour': [2, 5, 0, 3, 7, 11, 1, 6, 4, 10, 8, 9], 'cur_cost': 1278.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 0, 3, 6, 11, 1, 7, 4, 9, 8, 10], 'cur_cost': 1248.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 0, 3, 6, 11, 7, 1, 4, 10, 8, 9], 'cur_cost': 1307.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 9, 6], 'cur_cost': 1171.0, 'intermediate_solutions': [{'tour': [5, 7, 10, 8, 0, 11, 3, 2, 6, 9, 1, 4], 'cur_cost': 1183.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 10, 6, 11, 3, 2, 0, 8, 9, 1, 4], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 10, 8, 0, 2, 11, 3, 6, 9, 1, 4], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [11, 7, 5, 4, 2, 8, 1, 9, 6, 3, 0, 10], 'cur_cost': 1200.0, 'intermediate_solutions': [{'tour': [1, 8, 2, 3, 6, 7, 9, 11, 5, 10, 4, 0], 'cur_cost': 1290.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 2, 5, 7, 9, 11, 6, 3, 10, 4, 0], 'cur_cost': 1192.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 8, 2, 3, 11, 9, 7, 5, 10, 4, 0], 'cur_cost': 1191.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:35,624 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:35,624 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,625 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1171.000, 多样性=0.913
2025-08-05 10:28:35,625 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:35,625 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:35,625 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:35,625 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11938174269352904, 'best_improvement': -0.24046610169491525}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.010245901639344433}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.08051293229560341, 'recent_improvements': [-0.12417911661923671, 0.03836814175564645, 0.03684674797197012], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 754.0, 'new_best_cost': 754.0, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:35,625 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:35,626 - __main__ - INFO - simple5_12 开始进化第 5 代
2025-08-05 10:28:35,626 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:35,626 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,626 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1171.000, 多样性=0.913
2025-08-05 10:28:35,627 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:35,627 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.913
2025-08-05 10:28:35,627 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:35,628 - EliteExpert - INFO - 精英解分析完成: 精英解数量=2, 多样性=0.833
2025-08-05 10:28:35,629 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:35,629 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:35,629 - LandscapeExpert - INFO - 添加精英解数据: 2个精英解
2025-08-05 10:28:35,630 - LandscapeExpert - INFO - 数据提取成功: 12个路径, 12个适应度值
2025-08-05 10:28:35,636 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.333, 适应度梯度: 53.417, 聚类评分: 0.000, 覆盖率: 0.027, 收敛趋势: 0.000, 多样性: 0.920
2025-08-05 10:28:35,636 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:35,636 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:35,636 - visualization.landscape_visualizer - INFO - 设置当前实例名: simple5_12
2025-08-05 10:28:35,640 - visualization.landscape_visualizer - INFO - 插值约束: 194 个点被约束到最小值 754.00
2025-08-05 10:28:35,642 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.4%, 梯度: 25.13 → 23.78
2025-08-05 10:28:35,756 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\landscape_simple5_12_iter_25_20250805_102835.html
2025-08-05 10:28:35,820 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_simple5_12\dashboard_simple5_12_iter_25_20250805_102835.html
2025-08-05 10:28:35,820 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 25
2025-08-05 10:28:35,821 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:35,821 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1916秒
2025-08-05 10:28:35,821 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3333333333333333, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 53.416666666666664, 'local_optima_density': 0.3333333333333333, 'gradient_variance': 56257.08305555555, 'cluster_count': 0}, 'population_state': {'diversity': 0.9204545454545454, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0273, 'fitness_entropy': 0.9355245321275765, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.027)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.920)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 53.417)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360915.6368597, 'performance_metrics': {}}}
2025-08-05 10:28:35,821 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:35,821 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:35,822 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:35,822 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:35,822 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,822 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:35,823 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,823 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,823 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:35,823 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:35,823 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:35,824 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:35,824 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:35,824 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:35,824 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:35,824 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,825 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,825 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,826 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1071.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,827 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 8, 2, 7, 10, 1, 3, 11, 6, 9, 4], 'cur_cost': 1071.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 10, 4, 9, 0, 3, 7, 8, 11, 6], 'cur_cost': 1489.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 1, 10, 4, 7, 3, 11, 9, 8, 0, 6], 'cur_cost': 1291.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 1, 10, 4, 9, 11, 0, 3, 7, 8, 6], 'cur_cost': 1497.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,827 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1071.00)
2025-08-05 10:28:35,827 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:35,827 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,828 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,828 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 1248.0
2025-08-05 10:28:35,837 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,837 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,837 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,838 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,838 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 2, 7, 10, 1, 3, 11, 6, 9, 4], 'cur_cost': 1071.0}, {'tour': array([ 1,  6,  8,  3,  9, 11,  4,  0,  7, 10,  5,  2], dtype=int64), 'cur_cost': 1248.0}, {'tour': [5, 10, 1, 0, 8, 7, 3, 9, 4, 11, 6, 2], 'cur_cost': 1249.0}, {'tour': [4, 10, 1, 0, 11, 9, 8, 7, 3, 2, 6, 5], 'cur_cost': 1509.0}, {'tour': [2, 1, 7, 10, 9, 0, 3, 6, 4, 11, 8, 5], 'cur_cost': 1499.0}, {'tour': [2, 9, 1, 5, 7, 4, 10, 8, 0, 3, 11, 6], 'cur_cost': 1181.0}, {'tour': [1, 6, 8, 3, 5, 2, 7, 4, 10, 0, 11, 9], 'cur_cost': 1233.0}, {'tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 11, 6, 10], 'cur_cost': 1184.0}, {'tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 9, 6], 'cur_cost': 1171.0}, {'tour': [11, 7, 5, 4, 2, 8, 1, 9, 6, 3, 0, 10], 'cur_cost': 1200.0}]
2025-08-05 10:28:35,839 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,839 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 63, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 63, 'cache_hits': 0, 'similarity_calculations': 421, 'cache_hit_rate': 0.0, 'cache_size': 421}}
2025-08-05 10:28:35,840 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 1,  6,  8,  3,  9, 11,  4,  0,  7, 10,  5,  2], dtype=int64), 'cur_cost': 1248.0, 'intermediate_solutions': [{'tour': array([ 0, 11,  5,  6,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  0, 11,  5,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  6,  0, 11,  5,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  6,  0, 11,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  3,  6,  0, 11,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,840 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1248.00)
2025-08-05 10:28:35,840 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:35,840 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:35,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,841 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 12
2025-08-05 10:28:35,841 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,842 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,842 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1525.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,842 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 1, 7, 10, 8, 3, 0, 6, 9, 2, 4, 11], 'cur_cost': 1525.0, 'intermediate_solutions': [{'tour': [5, 10, 1, 0, 8, 7, 3, 9, 6, 11, 4, 2], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 2, 6, 11, 4, 9, 3, 7, 8, 0, 1], 'cur_cost': 1273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 1, 0, 8, 7, 3, 9, 4, 5, 11, 6, 2], 'cur_cost': 1337.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,843 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1525.00)
2025-08-05 10:28:35,843 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:35,843 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,843 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,843 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 1535.0
2025-08-05 10:28:35,849 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,850 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,850 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,850 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,851 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 2, 7, 10, 1, 3, 11, 6, 9, 4], 'cur_cost': 1071.0}, {'tour': array([ 1,  6,  8,  3,  9, 11,  4,  0,  7, 10,  5,  2], dtype=int64), 'cur_cost': 1248.0}, {'tour': [5, 1, 7, 10, 8, 3, 0, 6, 9, 2, 4, 11], 'cur_cost': 1525.0}, {'tour': array([11,  5,  2,  4,  8,  9,  1, 10,  3,  7,  0,  6], dtype=int64), 'cur_cost': 1535.0}, {'tour': [2, 1, 7, 10, 9, 0, 3, 6, 4, 11, 8, 5], 'cur_cost': 1499.0}, {'tour': [2, 9, 1, 5, 7, 4, 10, 8, 0, 3, 11, 6], 'cur_cost': 1181.0}, {'tour': [1, 6, 8, 3, 5, 2, 7, 4, 10, 0, 11, 9], 'cur_cost': 1233.0}, {'tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 11, 6, 10], 'cur_cost': 1184.0}, {'tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 9, 6], 'cur_cost': 1171.0}, {'tour': [11, 7, 5, 4, 2, 8, 1, 9, 6, 3, 0, 10], 'cur_cost': 1200.0}]
2025-08-05 10:28:35,851 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,851 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 64, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 64, 'cache_hits': 0, 'similarity_calculations': 432, 'cache_hit_rate': 0.0, 'cache_size': 432}}
2025-08-05 10:28:35,852 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([11,  5,  2,  4,  8,  9,  1, 10,  3,  7,  0,  6], dtype=int64), 'cur_cost': 1535.0, 'intermediate_solutions': [{'tour': array([ 1, 10,  4,  0, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  1, 10,  4, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1463.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  0,  1, 10,  4,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1680.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  1, 10, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 11,  0,  1, 10,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,852 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1535.00)
2025-08-05 10:28:35,853 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:35,853 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:35,853 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:35,853 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 1271.0
2025-08-05 10:28:35,859 - ExploitationExpert - INFO - res_population_num: 2
2025-08-05 10:28:35,859 - ExploitationExpert - INFO - res_population_costs: [754.0, 754.0]
2025-08-05 10:28:35,859 - ExploitationExpert - INFO - res_populations: [array([ 0,  5, 10,  4,  6,  9, 11,  1,  3,  7,  2,  8], dtype=int64), array([ 0,  2,  5, 10,  4,  7,  1, 11,  6,  9,  3,  8], dtype=int64)]
2025-08-05 10:28:35,859 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:35,860 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 8, 2, 7, 10, 1, 3, 11, 6, 9, 4], 'cur_cost': 1071.0}, {'tour': array([ 1,  6,  8,  3,  9, 11,  4,  0,  7, 10,  5,  2], dtype=int64), 'cur_cost': 1248.0}, {'tour': [5, 1, 7, 10, 8, 3, 0, 6, 9, 2, 4, 11], 'cur_cost': 1525.0}, {'tour': array([11,  5,  2,  4,  8,  9,  1, 10,  3,  7,  0,  6], dtype=int64), 'cur_cost': 1535.0}, {'tour': array([ 9,  2,  0,  8,  7,  1,  6,  4,  3, 11,  5, 10], dtype=int64), 'cur_cost': 1271.0}, {'tour': [2, 9, 1, 5, 7, 4, 10, 8, 0, 3, 11, 6], 'cur_cost': 1181.0}, {'tour': [1, 6, 8, 3, 5, 2, 7, 4, 10, 0, 11, 9], 'cur_cost': 1233.0}, {'tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 11, 6, 10], 'cur_cost': 1184.0}, {'tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 9, 6], 'cur_cost': 1171.0}, {'tour': [11, 7, 5, 4, 2, 8, 1, 9, 6, 3, 0, 10], 'cur_cost': 1200.0}]
2025-08-05 10:28:35,860 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:35,860 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 65, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 65, 'cache_hits': 0, 'similarity_calculations': 444, 'cache_hit_rate': 0.0, 'cache_size': 444}}
2025-08-05 10:28:35,861 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 9,  2,  0,  8,  7,  1,  6,  4,  3, 11,  5, 10], dtype=int64), 'cur_cost': 1271.0, 'intermediate_solutions': [{'tour': array([ 7,  1,  2, 10,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  7,  1,  2,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 10,  7,  1,  2,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1476.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10,  7,  1,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  9, 10,  7,  1,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1503.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:35,861 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 1271.00)
2025-08-05 10:28:35,861 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:35,861 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:35,861 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,862 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1209.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,863 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 2, 10, 3, 6, 11, 1, 7, 8, 0, 9], 'cur_cost': 1209.0, 'intermediate_solutions': [{'tour': [2, 9, 1, 6, 7, 4, 10, 8, 0, 3, 11, 5], 'cur_cost': 1169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 1, 5, 7, 3, 0, 8, 10, 4, 11, 6], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 1, 7, 4, 10, 8, 0, 3, 11, 5, 6], 'cur_cost': 1325.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,863 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 1209.00)
2025-08-05 10:28:35,863 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:35,863 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:35,863 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,864 - ExplorationExpert - INFO - 探索路径生成完成，成本: 980.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,865 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 4, 7, 1, 3, 2, 5, 10, 0, 8, 11, 9], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 5, 2, 8, 4, 10, 0, 11, 9], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 3, 5, 4, 7, 2, 10, 0, 11, 9], 'cur_cost': 1312.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 8, 3, 11, 5, 2, 7, 4, 10, 0, 9], 'cur_cost': 1277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,865 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 980.00)
2025-08-05 10:28:35,865 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:35,865 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:35,865 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,866 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 12
2025-08-05 10:28:35,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1107.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,867 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [10, 4, 7, 11, 3, 2, 0, 5, 8, 1, 6, 9], 'cur_cost': 1107.0, 'intermediate_solutions': [{'tour': [2, 4, 3, 9, 0, 8, 5, 7, 6, 11, 1, 10], 'cur_cost': 1215.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 10, 6, 11], 'cur_cost': 1330.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 3, 9, 5, 0, 8, 7, 1, 11, 6, 10], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,868 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1107.00)
2025-08-05 10:28:35,868 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:35,868 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:35,868 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,868 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,869 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,869 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1369.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,869 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [2, 10, 3, 4, 5, 6, 11, 1, 7, 8, 0, 9], 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': [1, 2, 11, 0, 10, 5, 7, 4, 8, 3, 9, 6], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 6, 9], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 8, 11, 0, 10, 4, 7, 5, 3, 9, 6], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,870 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 1369.00)
2025-08-05 10:28:35,870 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:35,870 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:35,870 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 12
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:35,871 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1091.0, 路径长度: 12, 收集中间解: 3
2025-08-05 10:28:35,872 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 8, 7, 3, 5, 2, 0, 10, 4, 1, 11, 9], 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': [11, 7, 5, 4, 2, 6, 1, 9, 8, 3, 0, 10], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 7, 5, 4, 2, 8, 1, 9, 0, 3, 6, 10], 'cur_cost': 1394.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 7, 5, 9, 4, 2, 8, 1, 6, 3, 0, 10], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:35,872 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 1091.00)
2025-08-05 10:28:35,872 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:35,872 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:35,874 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 2, 7, 10, 1, 3, 11, 6, 9, 4], 'cur_cost': 1071.0, 'intermediate_solutions': [{'tour': [2, 5, 1, 10, 4, 9, 0, 3, 7, 8, 11, 6], 'cur_cost': 1489.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 1, 10, 4, 7, 3, 11, 9, 8, 0, 6], 'cur_cost': 1291.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 1, 10, 4, 9, 11, 0, 3, 7, 8, 6], 'cur_cost': 1497.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  6,  8,  3,  9, 11,  4,  0,  7, 10,  5,  2], dtype=int64), 'cur_cost': 1248.0, 'intermediate_solutions': [{'tour': array([ 0, 11,  5,  6,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 6,  0, 11,  5,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  6,  0, 11,  5,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1402.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  6,  0, 11,  3,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5,  3,  6,  0, 11,  1,  7,  2,  8, 10,  4,  9]), 'cur_cost': 1422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 1, 7, 10, 8, 3, 0, 6, 9, 2, 4, 11], 'cur_cost': 1525.0, 'intermediate_solutions': [{'tour': [5, 10, 1, 0, 8, 7, 3, 9, 6, 11, 4, 2], 'cur_cost': 1072.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 2, 6, 11, 4, 9, 3, 7, 8, 0, 1], 'cur_cost': 1273.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 1, 0, 8, 7, 3, 9, 4, 5, 11, 6, 2], 'cur_cost': 1337.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([11,  5,  2,  4,  8,  9,  1, 10,  3,  7,  0,  6], dtype=int64), 'cur_cost': 1535.0, 'intermediate_solutions': [{'tour': array([ 1, 10,  4,  0, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  1, 10,  4, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1463.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11,  0,  1, 10,  4,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1680.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  0,  1, 10, 11,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 11,  0,  1, 10,  9,  8,  7,  3,  2,  6,  5]), 'cur_cost': 1730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  2,  0,  8,  7,  1,  6,  4,  3, 11,  5, 10], dtype=int64), 'cur_cost': 1271.0, 'intermediate_solutions': [{'tour': array([ 7,  1,  2, 10,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  7,  1,  2,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1475.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 10,  7,  1,  2,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1476.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 10,  7,  1,  9,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1358.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  9, 10,  7,  1,  0,  3,  6,  4, 11,  8,  5]), 'cur_cost': 1503.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 2, 10, 3, 6, 11, 1, 7, 8, 0, 9], 'cur_cost': 1209.0, 'intermediate_solutions': [{'tour': [2, 9, 1, 6, 7, 4, 10, 8, 0, 3, 11, 5], 'cur_cost': 1169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 9, 1, 5, 7, 3, 0, 8, 10, 4, 11, 6], 'cur_cost': 1262.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 1, 7, 4, 10, 8, 0, 3, 11, 5, 6], 'cur_cost': 1325.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 4, 7, 1, 3, 2, 5, 10, 0, 8, 11, 9], 'cur_cost': 980.0, 'intermediate_solutions': [{'tour': [1, 6, 7, 3, 5, 2, 8, 4, 10, 0, 11, 9], 'cur_cost': 1193.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 6, 8, 3, 5, 4, 7, 2, 10, 0, 11, 9], 'cur_cost': 1312.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 8, 3, 11, 5, 2, 7, 4, 10, 0, 9], 'cur_cost': 1277.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [10, 4, 7, 11, 3, 2, 0, 5, 8, 1, 6, 9], 'cur_cost': 1107.0, 'intermediate_solutions': [{'tour': [2, 4, 3, 9, 0, 8, 5, 7, 6, 11, 1, 10], 'cur_cost': 1215.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 4, 3, 9, 0, 8, 5, 7, 1, 10, 6, 11], 'cur_cost': 1330.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 3, 9, 5, 0, 8, 7, 1, 11, 6, 10], 'cur_cost': 1196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [2, 10, 3, 4, 5, 6, 11, 1, 7, 8, 0, 9], 'cur_cost': 1369.0, 'intermediate_solutions': [{'tour': [1, 2, 11, 0, 10, 5, 7, 4, 8, 3, 9, 6], 'cur_cost': 1242.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 11, 0, 10, 4, 7, 5, 8, 3, 6, 9], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 8, 11, 0, 10, 4, 7, 5, 3, 9, 6], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 8, 7, 3, 5, 2, 0, 10, 4, 1, 11, 9], 'cur_cost': 1091.0, 'intermediate_solutions': [{'tour': [11, 7, 5, 4, 2, 6, 1, 9, 8, 3, 0, 10], 'cur_cost': 1431.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 7, 5, 4, 2, 8, 1, 9, 0, 3, 6, 10], 'cur_cost': 1394.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 7, 5, 9, 4, 2, 8, 1, 6, 3, 0, 10], 'cur_cost': 1436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:35,874 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:35,874 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:35,875 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=980.000, 多样性=0.893
2025-08-05 10:28:35,875 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:35,876 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:35,876 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:35,876 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06265339774476171, 'best_improvement': 0.16310845431255339}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022312373225152133}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07887494222458774, 'recent_improvements': [0.03836814175564645, 0.03684674797197012, -0.11938174269352904], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 2, 'new_count': 2, 'count_change': 0, 'old_best_cost': 754.0, 'new_best_cost': 754.0, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:35,876 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:35,878 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple5_12_solution.json
2025-08-05 10:28:35,878 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\simple5_12_20250805_102835.solution
2025-08-05 10:28:35,878 - __main__ - INFO - 实例执行完成 - 运行时间: 1.46s, 最佳成本: 754.0
2025-08-05 10:28:35,879 - __main__ - INFO - 实例 simple5_12 处理完成
