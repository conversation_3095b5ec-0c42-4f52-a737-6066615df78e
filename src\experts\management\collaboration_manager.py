# -*- coding: utf-8 -*-
"""
专家协作管理器模块

包含ExpertCollaborationManager类，负责管理专家间的交互和协作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
import copy
import logging
import random
import math
from experts.base.expert_base import ExpertBase
from experts.evolution.exploration_expert import ExplorationExpert
from experts.evolution.exploitation_expert import ExploitationExpert
from experts.evolution.assessment_expert import EvolutionAssessmentExpert
from experts.analysis.landscape_expert import LandscapeExpert
from experts.strategy.strategy_expert import StrategyExpert
from experts.strategy.enhanced_strategy_expert import EnhancedStrategyExpert
from experts.analysis.stats_expert import StatsExpert
from experts.analysis.path_expert import PathExpert
from experts.analysis.elite_expert import EliteExpert
from core.algorithms.gls_evol_enhanced import tour_cost
from utils import utils
from utils.analysis_cache import population_cache, get_all_cache_stats


class FitnessBasedSelectionStrategy:
    """适应度导向的选择策略类"""

    def __init__(self, elite_protection_rate=0.2, base_acceptance_rate=0.8):
        """
        初始化选择策略

        参数:
            elite_protection_rate: 精英保护比例 (默认20%)
            base_acceptance_rate: 基础接受概率 (默认80%)
        """
        self.elite_protection_rate = elite_protection_rate
        self.base_acceptance_rate = base_acceptance_rate
        self.logger = logging.getLogger(__name__)

    def adaptive_replacement_decision(self, old_individual, new_individual,
                                    iteration, total_iterations, is_elite=False, landscape_features=None):
        """
        自适应替换决策 - 增强版本，支持景观特征

        参数:
            old_individual: 原个体
            new_individual: 新个体
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            is_elite: 是否为精英个体
            landscape_features: 景观特征字典

        返回:
            bool: 是否接受新个体
        """
        old_cost = float(old_individual.get("cur_cost", float('inf')))
        new_cost = float(new_individual.get("cur_cost", float('inf')))

        # 如果新个体更优，直接接受
        if new_cost < old_cost:
            self.logger.debug(f"新个体更优 (成本: {new_cost:.2f} < {old_cost:.2f})，直接接受")
            return True

        # 精英保护机制：精英个体只接受更优解
        if is_elite:
            self.logger.debug(f"精英个体保护，拒绝劣质解 (成本: {new_cost:.2f} >= {old_cost:.2f})")
            return False

        # 计算接受概率
        acceptance_prob = self._calculate_acceptance_probability(
            old_cost, new_cost, iteration, total_iterations, landscape_features)

        # 随机决策
        accept = random.random() < acceptance_prob

        self.logger.debug(f"概率接受决策: 适应度差距={new_cost - old_cost:.2f}, "
                         f"接受概率={acceptance_prob:.3f}, 结果={'接受' if accept else '拒绝'}")

        return accept

    def _calculate_acceptance_probability(self, current_cost, new_cost,
                                        iteration, total_iterations, landscape_features=None):
        """景观感知的接受概率计算"""

        # 基础参数
        progress = iteration / max(total_iterations, 1)
        cost_diff = new_cost - current_cost

        # 景观自适应温度
        base_temperature = 1000 * (1 - progress) ** 1.5

        if landscape_features:
            # 景观特征调整
            ruggedness = landscape_features.get('ruggedness', 0)
            coverage = landscape_features.get('coverage', 0)
            diversity = landscape_features.get('diversity', 1.0)

            temperature_modifier = 1.0
            if ruggedness > 0.3:
                temperature_modifier *= 1.2  # 崎岖景观提高温度
            if coverage < 0.01:
                temperature_modifier *= 1.15  # 低覆盖率提高温度
            if diversity < 0.8:
                temperature_modifier *= 1.1  # 低多样性时增加接受概率

            temperature = base_temperature * temperature_modifier
        else:
            temperature = base_temperature

        # 计算接受概率
        if cost_diff <= 0:
            return 1.0

        return math.exp(-cost_diff / temperature) if temperature > 0 else 0

    def identify_elite_individuals(self, populations, landscape_features=None):
        """
        识别精英个体 - 增强版本，支持动态精英保护

        参数:
            populations: 当前种群
            landscape_features: 景观特征字典

        返回:
            set: 精英个体的索引集合
        """
        if not populations:
            return set()

        # 按适应度排序
        sorted_indices = sorted(range(len(populations)),
                              key=lambda i: float(populations[i].get("cur_cost", float('inf'))))

        # 动态保护比例
        base_protection_ratio = self.elite_protection_rate

        if landscape_features:
            diversity = landscape_features.get('diversity', 1.0)
            if diversity < 0.8:
                # 低多样性时增加保护比例
                protection_ratio = min(0.3, base_protection_ratio * 1.2)
            else:
                protection_ratio = base_protection_ratio
        else:
            protection_ratio = base_protection_ratio

        # 选择精英个体
        elite_count = max(1, int(len(populations) * protection_ratio))
        elite_indices = set(sorted_indices[:elite_count])

        self.logger.info(f"识别精英个体: {elite_indices} (总数: {elite_count}, 保护比例: {protection_ratio:.2f})")
        return elite_indices

    def _is_elite_individual(self, individual_idx, current_cost, population_costs, landscape_features=None):
        """动态精英保护判断"""

        # 基础精英保护
        sorted_costs = sorted(population_costs)

        # 动态保护比例
        base_protection_ratio = self.elite_protection_rate

        if landscape_features:
            diversity = landscape_features.get('diversity', 1.0)
            if diversity < 0.8:
                # 低多样性时增加保护比例
                protection_ratio = min(0.3, base_protection_ratio * 1.2)
            else:
                protection_ratio = base_protection_ratio
        else:
            protection_ratio = base_protection_ratio

        elite_threshold_idx = int(len(sorted_costs) * protection_ratio)
        return current_cost <= sorted_costs[elite_threshold_idx]


class ExpertCollaborationManager:
    """专家协作管理器，管理专家间的交互
    已完全优化专家与LLM的交互方式：
    1. 景观分析专家已升级为算法化实现，完全去除LLM依赖
    2. 统计分析、路径结构和精英解专家使用算法实现，不与LLM交互
    3. 探索专家使用纯算法实现，不依赖LLM生成多样化路径
    4. 利用专家完全使用局部搜索和扰动代码，不使用LLM
    5. 评估专家使用纯算法实现，基于数学指标和统计分析进行评估
    6. 策略专家可选择性使用LLM或算法实现
    """
    
    def __init__(self, interface_llm=None, config=None):
        self.config = config or {}
        # 保留interface_llm参数用于兼容性，但不再使用
        self.interface_llm = interface_llm
        if interface_llm is not None:
            self.logger = logging.getLogger(__name__)
            self.logger.info("注意：协作管理器已升级，LandscapeExpert不再依赖LLM")
        else:
            self.logger = logging.getLogger(__name__)

        self.experts = {}
        self._initialize_experts()
        
        # 添加专家间数据共享存储
        self.shared_data = {
            "high_quality_edges": [],
            "difficult_regions": [],
            "opportunity_regions": [],
            "elite_features": {},
            "population_diversity": 0.0,
            "convergence_level": 0.0
        }

        # 初始化适应度导向的选择策略
        self.selection_strategy = FitnessBasedSelectionStrategy(
            elite_protection_rate=0.2,  # 保护前20%的精英个体
            base_acceptance_rate=0.8     # 基础接受概率80%
        )
    
    def _initialize_experts(self):
        """初始化所有专家模块"""
        # 这些专家使用算法实现，不与LLM交互
        self.experts['stats'] = StatsExpert()
        self.experts['path'] = PathExpert()
        self.experts['elite'] = EliteExpert()

        # 景观分析专家现在使用算法实现，不再依赖LLM
        self.experts['landscape'] = LandscapeExpert(
            interface_llm=self.interface_llm,  # 保持兼容性，但内部不使用
            config=self.config.get('landscape_config', {})
        )

        # 策略专家配置 - 支持LLM和算法化两种模式
        strategy_config = self.config.get('strategy_config', {})
        enable_llm = strategy_config.get('enable_llm_reasoning', False)

        if enable_llm and self.interface_llm is not None:
            # 使用增强策略专家（支持LLM）
            self.logger.info("启用LLM增强策略选择")
            self.experts['strategy'] = EnhancedStrategyExpert(
                interface_llm=self.interface_llm,
                config=strategy_config
            )
        else:
            # 使用传统策略专家（算法化）
            if enable_llm and self.interface_llm is None:
                self.logger.warning("LLM已启用但接口不可用，回退到算法化策略选择")
            else:
                self.logger.info("使用算法化策略选择")
            self.experts['strategy'] = StrategyExpert(self.interface_llm)

        # 这些专家使用纯算法实现，不依赖LLM
        self.experts['exploration'] = ExplorationExpert()  # 纯算法实现
        self.experts['exploitation'] = ExploitationExpert()  # 纯算法实现
        self.experts['assessment'] = EvolutionAssessmentExpert()  # 纯算法实现
    
    def update_shared_data(self, key, value):
        """更新专家间共享数据"""
        if key in self.shared_data:
            self.shared_data[key] = value
            self.logger.info(f"更新共享数据: {key}")
    
    def run_analysis_phase(self, populations, res_populations, distance_matrix, iteration, total_iterations=10, coordinates=None, instance_name=None):
        """运行分析阶段，包括统计、路径和精英分析（支持缓存优化）"""
        self.logger.info(f"--- Running Analysis Phase (Iteration {iteration}) ---")

        # 引入空间统计计算函数
        from experts.prompts.experts_prompt import compute_spatial_stats

        # 尝试从缓存获取统计分析结果
        cached_stats = population_cache.get_population_stats(populations)
        if cached_stats is not None:
            self.logger.info("使用缓存的统计分析结果")
            stats_analysis = cached_stats
        else:
            # 运行统计分析专家
            stats_analysis = self.experts["stats"].analyze(populations)
            population_cache.cache_population_stats(populations, stats_analysis)

        stats_report = self.experts["stats"].generate_report(stats_analysis, coordinates, distance_matrix, instance_name)

        # 尝试从缓存获取路径分析结果
        cached_path = population_cache.get_path_analysis(populations)
        if cached_path is not None:
            self.logger.info("使用缓存的路径分析结果")
            path_analysis = cached_path
        else:
            # 运行路径分析专家
            path_analysis = self.experts["path"].analyze(populations, distance_matrix)
            population_cache.cache_path_analysis(populations, path_analysis)

        path_report = self.experts["path"].generate_report(path_analysis, instance_name)

        # 精英解分析（通常不缓存，因为精英解变化频繁）
        elite_solutions = sorted(res_populations, key=lambda p: p['cur_cost']) if res_populations else []
        elite_analysis = self.experts["elite"].analyze(elite_solutions, populations, distance_matrix)
        elite_report = self.experts["elite"].generate_report(elite_analysis, instance_name)

        # 传递真实种群数据给景观分析专家
        self.experts["landscape"]._current_populations = populations
        self.experts["landscape"]._current_res_populations = res_populations

        # 运行景观分析专家
        landscape_report = self.experts["landscape"].analyze(
            stats_report, path_report, elite_report, iteration, total_iterations,
            history_data={'spatial_stats': compute_spatial_stats(coordinates, distance_matrix)}
        )

        self.update_shared_data('landscape_report', landscape_report)

        # 记录缓存统计信息
        if iteration % 5 == 0:  # 每5代记录一次缓存统计
            cache_stats = get_all_cache_stats()
            self.logger.info(f"缓存统计: {cache_stats}")

        return landscape_report, stats_report
    
    def run_strategy_phase(self, landscape_report, populations, iteration, strategy_feedback=None):
        """运行策略分配阶段"""
        self.logger.info("开始策略分配阶段")
        
        # 使用优化后的策略专家
        strategy_result = self.experts['strategy'].analyze(
            landscape_report=landscape_report,
            populations=populations,
            iteration=iteration,
            strategy_feedback=strategy_feedback
        )
        
        # 记录策略分配报告
        strategy_selection, strategy_response = strategy_result
        self.logger.info(f"策略分配报告: {strategy_selection}")
        self.logger.info(f"策略分配完整报告: {strategy_response}")
        
        self.logger.info("策略分配阶段完成")
        return strategy_result

    def run_evolution_phase(self, populations, strategies, landscape_report, distance_matrix,
                           res_populations=None, iteration=0, total_iterations=10):
        """运行进化阶段，生成新路径并应用适应度导向的选择策略"""
        self.logger.info("开始进化阶段")

        # 提取景观特征
        landscape_features = self._extract_landscape_features_from_report(landscape_report)

        # 识别精英个体 - 传递景观特征
        elite_indices = self.selection_strategy.identify_elite_individuals(populations, landscape_features)

        new_populations = []
        evolution_reports = []
        selection_stats = {"accepted": 0, "rejected": 0, "elite_protected": 0}

        for i, individual in enumerate(populations):
            # 生成新路径
            new_individual = None

            # 将共享数据传递给专家
            if strategies[i] == 'explore':
                self.logger.info(f"为个体 {i} 生成探索路径")
                new_path_data = self.experts['exploration'].generate_path(
                    individual=individual,
                    landscape_report=landscape_report,  # 包含了共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    evo_populations=None,
                    res_populations=res_populations
                )

                # 记录探索路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "explore", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 探索路径生成报告: {new_path_data}")

                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)

                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }

            else:  # exploit
                self.logger.info(f"为个体 {i} 生成利用路径")

                # 传递共享数据给利用专家
                exploitation_landscape_report = {
                    "high_quality_edges": self.shared_data["high_quality_edges"],
                    "fixed_nodes": self.shared_data.get("elite_features", {}).get("fixed_nodes", []),
                    "low_quality_regions": self.shared_data["difficult_regions"]
                }

                new_path_data = self.experts['exploitation'].generate_path(
                    individual=individual,
                    landscape_report=exploitation_landscape_report,  # 使用共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    res_populations=res_populations  # 传递精英解集合
                )

                # 记录利用路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "exploit", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 利用路径生成报告: {new_path_data}")

                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)

                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }

            # 应用适应度导向的选择策略
            if new_individual is not None:
                is_elite = i in elite_indices
                should_accept = self.selection_strategy.adaptive_replacement_decision(
                    old_individual=individual,
                    new_individual=new_individual,
                    iteration=iteration,
                    total_iterations=total_iterations,
                    is_elite=is_elite,
                    landscape_features=landscape_features  # 传递景观特征
                )

                if should_accept:
                    new_populations.append(new_individual)
                    selection_stats["accepted"] += 1
                    self.logger.info(f"个体 {i} 接受新路径 (成本: {new_individual['cur_cost']:.2f})")
                else:
                    new_populations.append(copy.deepcopy(individual))
                    selection_stats["rejected"] += 1
                    if is_elite:
                        selection_stats["elite_protected"] += 1
                    self.logger.info(f"个体 {i} 保留原路径 (成本: {individual['cur_cost']:.2f})")
            else:
                # 如果生成失败，保留原个体
                new_populations.append(copy.deepcopy(individual))
                self.logger.warning(f"个体 {i} 路径生成失败，保留原个体")

        # 记录选择策略统计信息
        self.logger.info(f"适应度导向选择统计: {selection_stats}")
        self.logger.info(f"接受率: {selection_stats['accepted']}/{len(populations)} "
                        f"({selection_stats['accepted']/len(populations)*100:.1f}%)")

        # 记录整体进化报告
        self.logger.info(f"进化阶段报告汇总: {evolution_reports}")
        self.logger.info("进化阶段完成")
        return new_populations

    def _extract_landscape_features_from_report(self, landscape_report):
        """从景观报告中提取特征用于选择策略"""
        features = {}

        if not landscape_report:
            return features

        # 提取关键景观特征
        if 'local_optima_density' in landscape_report:
            features['ruggedness'] = landscape_report['local_optima_density']

        if 'gradient_strength' in landscape_report:
            features['gradient_strength'] = landscape_report['gradient_strength']

        if 'coverage' in landscape_report:
            features['coverage'] = landscape_report['coverage']

        if 'diversity_preservation' in landscape_report:
            features['diversity'] = landscape_report['diversity_preservation']

        if 'evolution_phase' in landscape_report:
            features['evolution_phase'] = landscape_report['evolution_phase']

        return features

    def run_assessment_phase(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """运行评估阶段"""
        self.logger.info(f"--- Running Assessment Phase (Iteration {iteration}) ---")

        assessment_report = self.experts["assessment"].evaluate(
            old_stats_report, new_stats_report, strategies, iteration, total_iterations,
            old_res_populations=old_res_populations, new_res_populations=new_res_populations
        )

        # 更新共享数据
        self.update_shared_data('assessment_report', assessment_report)
        return assessment_report
