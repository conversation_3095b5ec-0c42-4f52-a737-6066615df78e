2025-08-01 17:43:32,841 - main - INFO - composite13_66 开始进化第 1 代
2025-08-01 17:43:32,842 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-01 17:43:32,844 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:32,849 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10014.000, 多样性=0.967
2025-08-01 17:43:32,852 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:43:32,856 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.967
2025-08-01 17:43:32,858 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:43:32,860 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-01 17:43:32,861 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:43:32,861 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-01 17:43:32,862 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-01 17:43:33,143 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-01 17:43:33,144 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 17:43:33,222 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 17:43:33,630 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250801_174333.html
2025-08-01 17:43:33,708 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250801_174333.html
2025-08-01 17:43:33,709 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-01 17:43:33,709 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-01 17:43:33,710 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.8506秒
2025-08-01 17:43:33,711 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-01 17:43:33,713 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041413.1431146, 'performance_metrics': {}}}
2025-08-01 17:43:33,714 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:43:33,715 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:43:33,716 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 10014.0
  • mean_cost: 73248.0
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:43:33,719 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:43:33,720 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:43:35,623 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "The search space has unexplored regions, and population diversity is currently low. Increase exploration to seek new opportunities."
}
```
2025-08-01 17:43:35,625 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:43:35,625 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 17:43:35,626 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 17:43:35,626 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "The search space has unexplored regions, and population diversity is currently low. Increase exploration to seek new opportunities."
}
```
2025-08-01 17:43:35,627 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:43:35,628 - main - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-08-01 17:43:35,628 - main - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "The search space has unexplored regions, and population diversity is currently low. Increase exploration to seek new opportunities."
}
```
2025-08-01 17:43:35,629 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:43:35,630 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 17:43:35,630 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 17:43:35,630 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:35,641 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:35,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:35,844 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62037.0, 路径长度: 66
2025-08-01 17:43:35,846 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}
2025-08-01 17:43:35,846 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 17:43:35,847 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 17:43:35,847 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:35,849 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:35,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:35,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12792.0, 路径长度: 66
2025-08-01 17:43:35,850 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}
2025-08-01 17:43:35,851 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:43:35,851 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:43:35,852 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:35,854 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:35,854 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:35,855 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12431.0, 路径长度: 66
2025-08-01 17:43:35,855 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}
2025-08-01 17:43:35,856 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 17:43:35,856 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 17:43:35,856 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:35,858 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:35,859 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:35,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14878.0, 路径长度: 66
2025-08-01 17:43:35,860 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}
2025-08-01 17:43:35,860 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 17:43:35,861 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 17:43:35,861 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:35,864 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:35,864 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:35,865 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10444.0, 路径长度: 66
2025-08-01 17:43:35,865 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}
2025-08-01 17:43:35,866 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:43:35,866 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:35,868 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:35,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 105684.0
2025-08-01 17:43:38,361 - ExploitationExpert - INFO - res_population_num: 1
2025-08-01 17:43:38,361 - ExploitationExpert - INFO - res_population_costs: [82731.0]
2025-08-01 17:43:38,362 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:38,363 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:38,363 - ExploitationExpert - INFO - populations: [{'tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}, {'tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}, {'tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}, {'tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}, {'tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}, {'tour': array([48, 14, 12, 44, 51, 29,  7, 65, 39,  4, 24, 49, 34, 11, 47, 13, 10,
        2, 23, 31, 57, 36, 52, 20, 28, 15, 22, 55, 59, 18, 16, 62, 61, 38,
        8, 40,  3, 33,  6, 43, 35, 58, 42,  9, 45, 53, 56, 60, 54, 19, 46,
        5, 21, 25, 26,  0, 32, 17,  1, 41, 37, 30, 64, 27, 50, 63],
      dtype=int64), 'cur_cost': 114390.0}, {'tour': array([19, 59,  1,  9, 46, 48,  2, 63, 27, 42, 17, 24, 43, 23,  7,  3, 36,
       16, 14, 11, 37, 47, 20, 21, 22, 55, 31, 13, 18, 25,  0, 53, 65, 64,
       39, 60, 35, 34,  6, 45, 41,  4, 57, 54, 15, 29, 10, 44,  5, 12, 26,
       58, 30, 28, 50, 51, 62, 56, 32,  8, 61, 49, 40, 38, 52, 33],
      dtype=int64), 'cur_cost': 97725.0}, {'tour': array([12, 62,  9, 23, 30, 49, 48,  6, 14, 37, 25, 54, 32, 35, 22, 56, 20,
       45, 47, 29,  3, 52, 53, 27, 19, 24, 51, 65, 41, 59, 38, 64, 58, 40,
       18, 60, 44, 10, 46, 17, 13, 36, 16,  4, 42, 61, 11, 39, 28,  5, 31,
       26,  2,  8,  0, 55, 15, 34,  7, 57, 63, 21, 33,  1, 43, 50],
      dtype=int64), 'cur_cost': 104514.0}, {'tour': array([19, 23, 34, 26,  9, 46, 18,  2, 49, 14, 30, 65, 53, 50, 54, 58, 60,
       57,  8, 45, 51, 15, 61, 64, 63, 38, 24, 37, 47,  3, 55,  0,  7, 17,
       10, 13, 41, 16,  4, 62, 29, 40, 44, 25, 39, 59,  1, 32, 31, 52, 43,
       27, 35, 20, 33, 48, 22,  5, 42, 11, 12, 28, 56, 21,  6, 36],
      dtype=int64), 'cur_cost': 97249.0}]
2025-08-01 17:43:38,371 - ExploitationExpert - INFO - 局部搜索耗时: 2.50秒
2025-08-01 17:43:38,374 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-01 17:43:38,375 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}
2025-08-01 17:43:38,376 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:43:38,376 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:38,377 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:38,378 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 105881.0
2025-08-01 17:43:41,008 - ExploitationExpert - INFO - res_population_num: 2
2025-08-01 17:43:41,008 - ExploitationExpert - INFO - res_population_costs: [82731.0, 9563.0]
2025-08-01 17:43:41,009 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:41,011 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:41,012 - ExploitationExpert - INFO - populations: [{'tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}, {'tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}, {'tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}, {'tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}, {'tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}, {'tour': array([48, 12,  5,  7, 11, 25,  2, 34, 31, 57, 15, 35, 41, 37,  6,  3, 54,
       60, 58, 50,  9, 10, 16, 51, 43,  8, 56, 65, 23,  0, 33, 52, 13, 64,
       30, 21, 17, 20,  1, 40, 61, 18, 59, 27, 47, 39, 32, 38, 19, 55, 46,
       22,  4, 14, 29, 62, 36, 24, 53, 26, 42, 63, 44, 45, 49, 28],
      dtype=int64), 'cur_cost': 105881.0}, {'tour': array([19, 59,  1,  9, 46, 48,  2, 63, 27, 42, 17, 24, 43, 23,  7,  3, 36,
       16, 14, 11, 37, 47, 20, 21, 22, 55, 31, 13, 18, 25,  0, 53, 65, 64,
       39, 60, 35, 34,  6, 45, 41,  4, 57, 54, 15, 29, 10, 44,  5, 12, 26,
       58, 30, 28, 50, 51, 62, 56, 32,  8, 61, 49, 40, 38, 52, 33],
      dtype=int64), 'cur_cost': 97725.0}, {'tour': array([12, 62,  9, 23, 30, 49, 48,  6, 14, 37, 25, 54, 32, 35, 22, 56, 20,
       45, 47, 29,  3, 52, 53, 27, 19, 24, 51, 65, 41, 59, 38, 64, 58, 40,
       18, 60, 44, 10, 46, 17, 13, 36, 16,  4, 42, 61, 11, 39, 28,  5, 31,
       26,  2,  8,  0, 55, 15, 34,  7, 57, 63, 21, 33,  1, 43, 50],
      dtype=int64), 'cur_cost': 104514.0}, {'tour': array([19, 23, 34, 26,  9, 46, 18,  2, 49, 14, 30, 65, 53, 50, 54, 58, 60,
       57,  8, 45, 51, 15, 61, 64, 63, 38, 24, 37, 47,  3, 55,  0,  7, 17,
       10, 13, 41, 16,  4, 62, 29, 40, 44, 25, 39, 59,  1, 32, 31, 52, 43,
       27, 35, 20, 33, 48, 22,  5, 42, 11, 12, 28, 56, 21,  6, 36],
      dtype=int64), 'cur_cost': 97249.0}]
2025-08-01 17:43:41,019 - ExploitationExpert - INFO - 局部搜索耗时: 2.64秒
2025-08-01 17:43:41,020 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-01 17:43:41,021 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([48, 12,  5,  7, 11, 25,  2, 34, 31, 57, 15, 35, 41, 37,  6,  3, 54,
       60, 58, 50,  9, 10, 16, 51, 43,  8, 56, 65, 23,  0, 33, 52, 13, 64,
       30, 21, 17, 20,  1, 40, 61, 18, 59, 27, 47, 39, 32, 38, 19, 55, 46,
       22,  4, 14, 29, 62, 36, 24, 53, 26, 42, 63, 44, 45, 49, 28],
      dtype=int64), 'cur_cost': 105881.0}
2025-08-01 17:43:41,022 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:43:41,022 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:41,022 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:41,023 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 91316.0
2025-08-01 17:43:41,873 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 17:43:41,875 - ExploitationExpert - INFO - res_population_costs: [82731.0, 9563.0, 9525.0]
2025-08-01 17:43:41,875 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:41,880 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:41,881 - ExploitationExpert - INFO - populations: [{'tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}, {'tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}, {'tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}, {'tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}, {'tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}, {'tour': array([48, 12,  5,  7, 11, 25,  2, 34, 31, 57, 15, 35, 41, 37,  6,  3, 54,
       60, 58, 50,  9, 10, 16, 51, 43,  8, 56, 65, 23,  0, 33, 52, 13, 64,
       30, 21, 17, 20,  1, 40, 61, 18, 59, 27, 47, 39, 32, 38, 19, 55, 46,
       22,  4, 14, 29, 62, 36, 24, 53, 26, 42, 63, 44, 45, 49, 28],
      dtype=int64), 'cur_cost': 105881.0}, {'tour': array([ 1, 29, 47, 43, 52, 58, 18, 63, 61,  0, 35, 23, 15, 14, 45, 39, 21,
       36, 49, 50,  2, 10,  6, 57,  9, 26, 28, 64, 25, 13,  5, 27, 44, 53,
       40, 22, 12, 16, 24, 30,  3, 60, 62, 20, 48, 19, 55, 59, 31,  4, 56,
       33, 37,  7, 32, 42,  8, 54, 38, 51, 17, 34, 11, 46, 41, 65],
      dtype=int64), 'cur_cost': 91316.0}, {'tour': array([12, 62,  9, 23, 30, 49, 48,  6, 14, 37, 25, 54, 32, 35, 22, 56, 20,
       45, 47, 29,  3, 52, 53, 27, 19, 24, 51, 65, 41, 59, 38, 64, 58, 40,
       18, 60, 44, 10, 46, 17, 13, 36, 16,  4, 42, 61, 11, 39, 28,  5, 31,
       26,  2,  8,  0, 55, 15, 34,  7, 57, 63, 21, 33,  1, 43, 50],
      dtype=int64), 'cur_cost': 104514.0}, {'tour': array([19, 23, 34, 26,  9, 46, 18,  2, 49, 14, 30, 65, 53, 50, 54, 58, 60,
       57,  8, 45, 51, 15, 61, 64, 63, 38, 24, 37, 47,  3, 55,  0,  7, 17,
       10, 13, 41, 16,  4, 62, 29, 40, 44, 25, 39, 59,  1, 32, 31, 52, 43,
       27, 35, 20, 33, 48, 22,  5, 42, 11, 12, 28, 56, 21,  6, 36],
      dtype=int64), 'cur_cost': 97249.0}]
2025-08-01 17:43:41,888 - ExploitationExpert - INFO - 局部搜索耗时: 0.87秒
2025-08-01 17:43:41,889 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-01 17:43:41,890 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 1, 29, 47, 43, 52, 58, 18, 63, 61,  0, 35, 23, 15, 14, 45, 39, 21,
       36, 49, 50,  2, 10,  6, 57,  9, 26, 28, 64, 25, 13,  5, 27, 44, 53,
       40, 22, 12, 16, 24, 30,  3, 60, 62, 20, 48, 19, 55, 59, 31,  4, 56,
       33, 37,  7, 32, 42,  8, 54, 38, 51, 17, 34, 11, 46, 41, 65],
      dtype=int64), 'cur_cost': 91316.0}
2025-08-01 17:43:41,890 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:43:41,891 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:41,891 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:41,892 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 103849.0
2025-08-01 17:43:41,980 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 17:43:41,981 - ExploitationExpert - INFO - res_population_costs: [82731.0, 9563.0, 9525.0]
2025-08-01 17:43:41,981 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:41,984 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:41,984 - ExploitationExpert - INFO - populations: [{'tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}, {'tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}, {'tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}, {'tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}, {'tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}, {'tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}, {'tour': array([48, 12,  5,  7, 11, 25,  2, 34, 31, 57, 15, 35, 41, 37,  6,  3, 54,
       60, 58, 50,  9, 10, 16, 51, 43,  8, 56, 65, 23,  0, 33, 52, 13, 64,
       30, 21, 17, 20,  1, 40, 61, 18, 59, 27, 47, 39, 32, 38, 19, 55, 46,
       22,  4, 14, 29, 62, 36, 24, 53, 26, 42, 63, 44, 45, 49, 28],
      dtype=int64), 'cur_cost': 105881.0}, {'tour': array([ 1, 29, 47, 43, 52, 58, 18, 63, 61,  0, 35, 23, 15, 14, 45, 39, 21,
       36, 49, 50,  2, 10,  6, 57,  9, 26, 28, 64, 25, 13,  5, 27, 44, 53,
       40, 22, 12, 16, 24, 30,  3, 60, 62, 20, 48, 19, 55, 59, 31,  4, 56,
       33, 37,  7, 32, 42,  8, 54, 38, 51, 17, 34, 11, 46, 41, 65],
      dtype=int64), 'cur_cost': 91316.0}, {'tour': array([59,  4,  1, 34, 35, 20, 42, 49,  8, 39, 44, 28, 10,  7, 47, 23, 61,
        5, 38, 58, 25, 22, 63, 13, 18, 19,  0, 37, 24, 50, 14, 52, 15, 12,
       60, 41, 64, 46, 62, 29, 40, 30, 27, 48, 33, 31,  3, 54, 36, 51, 56,
       55, 45,  6, 16,  2, 11, 43,  9, 32, 26, 21, 57, 17, 65, 53],
      dtype=int64), 'cur_cost': 103849.0}, {'tour': array([19, 23, 34, 26,  9, 46, 18,  2, 49, 14, 30, 65, 53, 50, 54, 58, 60,
       57,  8, 45, 51, 15, 61, 64, 63, 38, 24, 37, 47,  3, 55,  0,  7, 17,
       10, 13, 41, 16,  4, 62, 29, 40, 44, 25, 39, 59,  1, 32, 31, 52, 43,
       27, 35, 20, 33, 48, 22,  5, 42, 11, 12, 28, 56, 21,  6, 36],
      dtype=int64), 'cur_cost': 97249.0}]
2025-08-01 17:43:41,989 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:43:41,990 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-01 17:43:41,991 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([59,  4,  1, 34, 35, 20, 42, 49,  8, 39, 44, 28, 10,  7, 47, 23, 61,
        5, 38, 58, 25, 22, 63, 13, 18, 19,  0, 37, 24, 50, 14, 52, 15, 12,
       60, 41, 64, 46, 62, 29, 40, 30, 27, 48, 33, 31,  3, 54, 36, 51, 56,
       55, 45,  6, 16,  2, 11, 43,  9, 32, 26, 21, 57, 17, 65, 53],
      dtype=int64), 'cur_cost': 103849.0}
2025-08-01 17:43:41,991 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-01 17:43:41,991 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-01 17:43:41,992 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:41,994 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:41,995 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:41,995 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12416.0, 路径长度: 66
2025-08-01 17:43:41,996 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 2, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12416.0}
2025-08-01 17:43:41,997 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [47, 16, 4, 17, 30, 21, 27, 20, 31, 29, 37, 18, 7, 55, 58, 60, 52, 65, 5, 12, 3, 6, 15, 33, 8, 63, 14, 10, 23, 22, 19, 2, 24, 35, 9, 36, 26, 1, 32, 49, 51, 46, 38, 40, 48, 41, 50, 34, 42, 28, 0, 53, 62, 54, 57, 64, 61, 11, 13, 43, 45, 44, 39, 25, 56, 59], 'cur_cost': 62037.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 12, 4, 5, 8, 2, 6, 9, 11, 1, 3, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12792.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 5, 4, 8, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12431.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 1, 13, 20, 21, 19, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14878.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 10, 6, 2, 8, 5, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10444.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 37, 48, 44,  2,  0, 35, 65, 33, 51, 54, 59, 47, 50, 20, 29, 57,
       64, 31, 62, 40, 14, 43, 16, 46,  8, 12,  5,  3, 39, 58, 18, 45, 10,
       41, 15, 60, 23, 42, 11,  9,  6, 36,  7, 13, 19, 17, 25, 49, 63, 53,
       55, 34,  1,  4, 52, 26, 56, 21, 28, 38, 61, 24, 22, 32, 27],
      dtype=int64), 'cur_cost': 105684.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 12,  5,  7, 11, 25,  2, 34, 31, 57, 15, 35, 41, 37,  6,  3, 54,
       60, 58, 50,  9, 10, 16, 51, 43,  8, 56, 65, 23,  0, 33, 52, 13, 64,
       30, 21, 17, 20,  1, 40, 61, 18, 59, 27, 47, 39, 32, 38, 19, 55, 46,
       22,  4, 14, 29, 62, 36, 24, 53, 26, 42, 63, 44, 45, 49, 28],
      dtype=int64), 'cur_cost': 105881.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 29, 47, 43, 52, 58, 18, 63, 61,  0, 35, 23, 15, 14, 45, 39, 21,
       36, 49, 50,  2, 10,  6, 57,  9, 26, 28, 64, 25, 13,  5, 27, 44, 53,
       40, 22, 12, 16, 24, 30,  3, 60, 62, 20, 48, 19, 55, 59, 31,  4, 56,
       33, 37,  7, 32, 42,  8, 54, 38, 51, 17, 34, 11, 46, 41, 65],
      dtype=int64), 'cur_cost': 91316.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([59,  4,  1, 34, 35, 20, 42, 49,  8, 39, 44, 28, 10,  7, 47, 23, 61,
        5, 38, 58, 25, 22, 63, 13, 18, 19,  0, 37, 24, 50, 14, 52, 15, 12,
       60, 41, 64, 46, 62, 29, 40, 30, 27, 48, 33, 31,  3, 54, 36, 51, 56,
       55, 45,  6, 16,  2, 11, 43,  9, 32, 26, 21, 57, 17, 65, 53],
      dtype=int64), 'cur_cost': 103849.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12416.0}}]
2025-08-01 17:43:42,000 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:43:42,000 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:42,006 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10444.000, 多样性=0.928
2025-08-01 17:43:42,007 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-01 17:43:42,007 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-01 17:43:42,008 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:43:42,008 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10183921191208027, 'best_improvement': -0.04293988416217296}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0407239819004523}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 9525.0, 'new_best_cost': 9525.0, 'quality_improvement': 0.0, 'old_diversity': 0.9545454545454546, 'new_diversity': 0.9545454545454546, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 17:43:42,009 - main - INFO - --- Finished Evolution Iteration 1 ---
2025-08-01 17:43:42,010 - main - INFO - composite13_66 开始进化第 2 代
2025-08-01 17:43:42,010 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-01 17:43:42,011 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:42,013 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10444.000, 多样性=0.928
2025-08-01 17:43:42,013 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:43:42,016 - PathExpert - INFO - 路径结构分析完成: 公共边数量=14, 路径相似性=0.928
2025-08-01 17:43:42,017 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:43:42,018 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.955
2025-08-01 17:43:42,021 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-01 17:43:42,021 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:43:42,021 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-01 17:43:42,021 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-01 17:43:42,057 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:43:42,057 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-01 17:43:42,058 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-01 17:43:42,067 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-01 17:43:42,162 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_2_20250801_174342.html
2025-08-01 17:43:42,222 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_2_20250801_174342.html
2025-08-01 17:43:42,223 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 2
2025-08-01 17:43:42,223 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-01 17:43:42,223 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2025秒
2025-08-01 17:43:42,224 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041422.0579636, 'performance_metrics': {}}}
2025-08-01 17:43:42,225 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:43:42,225 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:43:42,225 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 10444.0
  • mean_cost: 53172.8
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:43:42,229 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:43:42,229 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:43:43,819 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed.  Recommended focus on exploration, opportunity regions available, and low diversity favors extensive search."
}
```
2025-08-01 17:43:43,819 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:43:43,820 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 17:43:43,820 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 17:43:43,821 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed.  Recommended focus on exploration, opportunity regions available, and low diversity favors extensive search."
}
```
2025-08-01 17:43:43,822 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:43:43,822 - main - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-01 17:43:43,823 - main - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.80,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration needed.  Recommended focus on exploration, opportunity regions available, and low diversity favors extensive search."
}
```
2025-08-01 17:43:43,824 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:43:43,824 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 17:43:43,825 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 17:43:43,825 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,832 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:43,833 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,833 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62337.0, 路径长度: 66
2025-08-01 17:43:43,834 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 5, 55, 22, 31, 26, 19, 1, 54, 21, 30, 3, 16, 23, 36, 27, 20, 13, 17, 40, 42, 14, 29, 2, 7, 62, 64, 15, 34, 10, 65, 4, 56, 58, 18, 25, 48, 50, 41, 46, 45, 39, 33, 49, 43, 12, 35, 44, 24, 38, 8, 9, 52, 57, 59, 60, 63, 53, 61, 47, 51, 37, 28, 32, 11], 'cur_cost': 62337.0}
2025-08-01 17:43:43,834 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 17:43:43,834 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 17:43:43,835 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,843 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:43,844 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,845 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62621.0, 路径长度: 66
2025-08-01 17:43:43,846 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [65, 7, 64, 17, 36, 24, 34, 19, 16, 37, 28, 32, 13, 21, 3, 62, 11, 8, 12, 35, 4, 56, 59, 9, 6, 14, 27, 31, 2, 5, 57, 52, 54, 53, 63, 39, 47, 23, 0, 60, 1, 33, 43, 41, 51, 50, 46, 22, 25, 10, 61, 20, 49, 48, 38, 42, 18, 29, 40, 15, 30, 45, 26, 44, 58, 55], 'cur_cost': 62621.0}
2025-08-01 17:43:43,847 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:43:43,848 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:43:43,848 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,851 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 17:43:43,851 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,852 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88700.0, 路径长度: 66
2025-08-01 17:43:43,852 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}
2025-08-01 17:43:43,853 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 17:43:43,853 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 17:43:43,853 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,859 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:43,860 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,860 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57468.0, 路径长度: 66
2025-08-01 17:43:43,861 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}
2025-08-01 17:43:43,862 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 17:43:43,863 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 17:43:43,863 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,866 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:43,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12347.0, 路径长度: 66
2025-08-01 17:43:43,867 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}
2025-08-01 17:43:43,868 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 17:43:43,868 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 17:43:43,868 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,878 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:43,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,881 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52224.0, 路径长度: 66
2025-08-01 17:43:43,881 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}
2025-08-01 17:43:43,882 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 17:43:43,883 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 17:43:43,883 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,885 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:43,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12909.0, 路径长度: 66
2025-08-01 17:43:43,887 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}
2025-08-01 17:43:43,887 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 17:43:43,888 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 17:43:43,888 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:43,890 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:43,890 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:43,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12821.0, 路径长度: 66
2025-08-01 17:43:43,891 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}
2025-08-01 17:43:43,892 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:43:43,892 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:43,892 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:43,893 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 115270.0
2025-08-01 17:43:43,976 - ExploitationExpert - INFO - res_population_num: 3
2025-08-01 17:43:43,976 - ExploitationExpert - INFO - res_population_costs: [9525.0, 9563.0, 82731.0]
2025-08-01 17:43:43,977 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:43,980 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:43,980 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 5, 55, 22, 31, 26, 19, 1, 54, 21, 30, 3, 16, 23, 36, 27, 20, 13, 17, 40, 42, 14, 29, 2, 7, 62, 64, 15, 34, 10, 65, 4, 56, 58, 18, 25, 48, 50, 41, 46, 45, 39, 33, 49, 43, 12, 35, 44, 24, 38, 8, 9, 52, 57, 59, 60, 63, 53, 61, 47, 51, 37, 28, 32, 11], 'cur_cost': 62337.0}, {'tour': [65, 7, 64, 17, 36, 24, 34, 19, 16, 37, 28, 32, 13, 21, 3, 62, 11, 8, 12, 35, 4, 56, 59, 9, 6, 14, 27, 31, 2, 5, 57, 52, 54, 53, 63, 39, 47, 23, 0, 60, 1, 33, 43, 41, 51, 50, 46, 22, 25, 10, 61, 20, 49, 48, 38, 42, 18, 29, 40, 15, 30, 45, 26, 44, 58, 55], 'cur_cost': 62621.0}, {'tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}, {'tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': array([38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16,  7, 65, 37, 57,  3,  1,
       59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54,  6, 20, 52, 35, 44,
       39, 47, 22, 42,  4,  8,  5, 25, 26,  9, 45, 13, 31, 56, 49, 11, 28,
       55, 51, 23,  0, 12, 58, 19, 40, 24, 33, 41,  2, 32, 17, 27],
      dtype=int64), 'cur_cost': 115270.0}, {'tour': [0, 2, 19, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12416.0}]
2025-08-01 17:43:43,984 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-01 17:43:43,984 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-01 17:43:43,985 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16,  7, 65, 37, 57,  3,  1,
       59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54,  6, 20, 52, 35, 44,
       39, 47, 22, 42,  4,  8,  5, 25, 26,  9, 45, 13, 31, 56, 49, 11, 28,
       55, 51, 23,  0, 12, 58, 19, 40, 24, 33, 41,  2, 32, 17, 27],
      dtype=int64), 'cur_cost': 115270.0}
2025-08-01 17:43:43,986 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:43:43,986 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:43,986 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:43,987 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 100569.0
2025-08-01 17:43:44,071 - ExploitationExpert - INFO - res_population_num: 4
2025-08-01 17:43:44,073 - ExploitationExpert - INFO - res_population_costs: [9525.0, 9563.0, 82731.0, 9521]
2025-08-01 17:43:44,074 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:44,079 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:44,080 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 5, 55, 22, 31, 26, 19, 1, 54, 21, 30, 3, 16, 23, 36, 27, 20, 13, 17, 40, 42, 14, 29, 2, 7, 62, 64, 15, 34, 10, 65, 4, 56, 58, 18, 25, 48, 50, 41, 46, 45, 39, 33, 49, 43, 12, 35, 44, 24, 38, 8, 9, 52, 57, 59, 60, 63, 53, 61, 47, 51, 37, 28, 32, 11], 'cur_cost': 62337.0}, {'tour': [65, 7, 64, 17, 36, 24, 34, 19, 16, 37, 28, 32, 13, 21, 3, 62, 11, 8, 12, 35, 4, 56, 59, 9, 6, 14, 27, 31, 2, 5, 57, 52, 54, 53, 63, 39, 47, 23, 0, 60, 1, 33, 43, 41, 51, 50, 46, 22, 25, 10, 61, 20, 49, 48, 38, 42, 18, 29, 40, 15, 30, 45, 26, 44, 58, 55], 'cur_cost': 62621.0}, {'tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}, {'tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': array([38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16,  7, 65, 37, 57,  3,  1,
       59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54,  6, 20, 52, 35, 44,
       39, 47, 22, 42,  4,  8,  5, 25, 26,  9, 45, 13, 31, 56, 49, 11, 28,
       55, 51, 23,  0, 12, 58, 19, 40, 24, 33, 41,  2, 32, 17, 27],
      dtype=int64), 'cur_cost': 115270.0}, {'tour': array([35, 19, 17, 32, 30, 59, 37, 36, 12, 63,  1, 25,  5, 61, 44,  8, 62,
       58, 47,  7, 57, 23, 14, 42, 10, 51,  4,  9, 43,  6, 38, 48,  0, 60,
        3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65,  2, 24, 64, 31,
       49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27],
      dtype=int64), 'cur_cost': 100569.0}]
2025-08-01 17:43:44,085 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:43:44,085 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-01 17:43:44,086 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 19, 17, 32, 30, 59, 37, 36, 12, 63,  1, 25,  5, 61, 44,  8, 62,
       58, 47,  7, 57, 23, 14, 42, 10, 51,  4,  9, 43,  6, 38, 48,  0, 60,
        3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65,  2, 24, 64, 31,
       49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27],
      dtype=int64), 'cur_cost': 100569.0}
2025-08-01 17:43:44,088 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 5, 55, 22, 31, 26, 19, 1, 54, 21, 30, 3, 16, 23, 36, 27, 20, 13, 17, 40, 42, 14, 29, 2, 7, 62, 64, 15, 34, 10, 65, 4, 56, 58, 18, 25, 48, 50, 41, 46, 45, 39, 33, 49, 43, 12, 35, 44, 24, 38, 8, 9, 52, 57, 59, 60, 63, 53, 61, 47, 51, 37, 28, 32, 11], 'cur_cost': 62337.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [65, 7, 64, 17, 36, 24, 34, 19, 16, 37, 28, 32, 13, 21, 3, 62, 11, 8, 12, 35, 4, 56, 59, 9, 6, 14, 27, 31, 2, 5, 57, 52, 54, 53, 63, 39, 47, 23, 0, 60, 1, 33, 43, 41, 51, 50, 46, 22, 25, 10, 61, 20, 49, 48, 38, 42, 18, 29, 40, 15, 30, 45, 26, 44, 58, 55], 'cur_cost': 62621.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16,  7, 65, 37, 57,  3,  1,
       59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54,  6, 20, 52, 35, 44,
       39, 47, 22, 42,  4,  8,  5, 25, 26,  9, 45, 13, 31, 56, 49, 11, 28,
       55, 51, 23,  0, 12, 58, 19, 40, 24, 33, 41,  2, 32, 17, 27],
      dtype=int64), 'cur_cost': 115270.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 19, 17, 32, 30, 59, 37, 36, 12, 63,  1, 25,  5, 61, 44,  8, 62,
       58, 47,  7, 57, 23, 14, 42, 10, 51,  4,  9, 43,  6, 38, 48,  0, 60,
        3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65,  2, 24, 64, 31,
       49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27],
      dtype=int64), 'cur_cost': 100569.0}}]
2025-08-01 17:43:44,092 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:43:44,092 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:44,096 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12347.000, 多样性=0.954
2025-08-01 17:43:44,097 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-01 17:43:44,097 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-01 17:43:44,098 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:43:44,098 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.11884214474616413, 'best_improvement': -0.18220988127154347}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.028301886792452772}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8737373737373737, 'new_diversity': 0.8737373737373737, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-01 17:43:44,100 - main - INFO - --- Finished Evolution Iteration 2 ---
2025-08-01 17:43:44,100 - main - INFO - composite13_66 开始进化第 3 代
2025-08-01 17:43:44,100 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-01 17:43:44,101 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:44,102 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12347.000, 多样性=0.954
2025-08-01 17:43:44,103 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:43:44,111 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.954
2025-08-01 17:43:44,113 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:43:44,116 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.874
2025-08-01 17:43:44,119 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-01 17:43:44,120 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:43:44,120 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-01 17:43:44,120 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-01 17:43:44,169 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:43:44,171 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-01 17:43:44,173 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-01 17:43:44,187 - visualization.landscape_visualizer - INFO - 已添加 4 个精英解标记
2025-08-01 17:43:44,301 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_3_20250801_174344.html
2025-08-01 17:43:44,363 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_3_20250801_174344.html
2025-08-01 17:43:44,364 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 3
2025-08-01 17:43:44,364 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-01 17:43:44,365 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2456秒
2025-08-01 17:43:44,365 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041424.1716294, 'performance_metrics': {}}}
2025-08-01 17:43:44,366 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:43:44,366 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:43:44,367 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12347.0
  • mean_cost: 57726.6
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:43:44,371 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:43:44,374 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:43:46,027 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Landscape suggests exploitation, but unexplored space exists.  Slightly exploit, using a 60/40 ratio."
}
```
2025-08-01 17:43:46,028 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:43:46,028 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 17:43:46,029 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 17:43:46,029 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Landscape suggests exploitation, but unexplored space exists.  Slightly exploit, using a 60/40 ratio."
}
```
2025-08-01 17:43:46,030 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:43:46,030 - main - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-01 17:43:46,031 - main - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.40,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Landscape suggests exploitation, but unexplored space exists.  Slightly exploit, using a 60/40 ratio."
}
```
2025-08-01 17:43:46,032 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:43:46,033 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:43:46,033 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:46,033 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:46,034 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 92417.0
2025-08-01 17:43:46,125 - ExploitationExpert - INFO - res_population_num: 5
2025-08-01 17:43:46,125 - ExploitationExpert - INFO - res_population_costs: [9521, 9525.0, 9563.0, 82731.0, 9521]
2025-08-01 17:43:46,125 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:46,131 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:46,132 - ExploitationExpert - INFO - populations: [{'tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}, {'tour': [65, 7, 64, 17, 36, 24, 34, 19, 16, 37, 28, 32, 13, 21, 3, 62, 11, 8, 12, 35, 4, 56, 59, 9, 6, 14, 27, 31, 2, 5, 57, 52, 54, 53, 63, 39, 47, 23, 0, 60, 1, 33, 43, 41, 51, 50, 46, 22, 25, 10, 61, 20, 49, 48, 38, 42, 18, 29, 40, 15, 30, 45, 26, 44, 58, 55], 'cur_cost': 62621.0}, {'tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}, {'tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': [38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16, 7, 65, 37, 57, 3, 1, 59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54, 6, 20, 52, 35, 44, 39, 47, 22, 42, 4, 8, 5, 25, 26, 9, 45, 13, 31, 56, 49, 11, 28, 55, 51, 23, 0, 12, 58, 19, 40, 24, 33, 41, 2, 32, 17, 27], 'cur_cost': 115270.0}, {'tour': [35, 19, 17, 32, 30, 59, 37, 36, 12, 63, 1, 25, 5, 61, 44, 8, 62, 58, 47, 7, 57, 23, 14, 42, 10, 51, 4, 9, 43, 6, 38, 48, 0, 60, 3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65, 2, 24, 64, 31, 49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27], 'cur_cost': 100569.0}]
2025-08-01 17:43:46,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-01 17:43:46,138 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-01 17:43:46,142 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}
2025-08-01 17:43:46,143 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:43:46,143 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:46,143 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:46,144 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 106251.0
2025-08-01 17:43:46,240 - ExploitationExpert - INFO - res_population_num: 7
2025-08-01 17:43:46,243 - ExploitationExpert - INFO - res_population_costs: [9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521]
2025-08-01 17:43:46,244 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:46,255 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:46,256 - ExploitationExpert - INFO - populations: [{'tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}, {'tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}, {'tour': [65, 7, 64, 17, 36, 21, 34, 18, 16, 37, 23, 32, 3, 61, 53, 27, 11, 8, 13, 60, 4, 56, 14, 29, 6, 31, 62, 15, 2, 9, 57, 52, 54, 10, 58, 39, 25, 48, 0, 41, 46, 45, 43, 33, 49, 50, 12, 35, 44, 63, 38, 20, 40, 1, 47, 59, 26, 24, 30, 28, 42, 51, 19, 22, 5, 55], 'cur_cost': 88700.0}, {'tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': [38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16, 7, 65, 37, 57, 3, 1, 59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54, 6, 20, 52, 35, 44, 39, 47, 22, 42, 4, 8, 5, 25, 26, 9, 45, 13, 31, 56, 49, 11, 28, 55, 51, 23, 0, 12, 58, 19, 40, 24, 33, 41, 2, 32, 17, 27], 'cur_cost': 115270.0}, {'tour': [35, 19, 17, 32, 30, 59, 37, 36, 12, 63, 1, 25, 5, 61, 44, 8, 62, 58, 47, 7, 57, 23, 14, 42, 10, 51, 4, 9, 43, 6, 38, 48, 0, 60, 3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65, 2, 24, 64, 31, 49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27], 'cur_cost': 100569.0}]
2025-08-01 17:43:46,261 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:43:46,263 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-08-01 17:43:46,267 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}
2025-08-01 17:43:46,270 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-01 17:43:46,271 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:46,271 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:46,274 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 110581.0
2025-08-01 17:43:46,398 - ExploitationExpert - INFO - res_population_num: 8
2025-08-01 17:43:46,398 - ExploitationExpert - INFO - res_population_costs: [9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521, 9521]
2025-08-01 17:43:46,399 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:46,410 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:46,411 - ExploitationExpert - INFO - populations: [{'tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}, {'tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}, {'tour': array([35, 63, 19, 42, 58, 21, 52,  1, 50,  4, 27, 31, 11, 46, 28, 61, 23,
       37, 34, 44, 56, 60,  6, 18, 45, 59, 62, 24,  7, 13, 54, 22, 10, 47,
       12, 36,  8, 30, 33, 64, 26, 17,  5,  0, 14, 39, 65,  2, 49, 25, 43,
       38, 57, 41,  9, 15, 53, 55, 48, 40, 32, 51, 20, 16,  3, 29],
      dtype=int64), 'cur_cost': 110581.0}, {'tour': [7, 61, 53, 15, 2, 22, 4, 23, 17, 21, 32, 24, 5, 16, 27, 14, 30, 36, 11, 8, 12, 37, 35, 25, 1, 54, 62, 9, 59, 63, 19, 31, 0, 26, 29, 18, 39, 50, 45, 46, 43, 13, 34, 20, 41, 38, 49, 47, 28, 44, 48, 40, 51, 33, 10, 3, 55, 65, 56, 60, 57, 64, 58, 52, 6, 42], 'cur_cost': 57468.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': [38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16, 7, 65, 37, 57, 3, 1, 59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54, 6, 20, 52, 35, 44, 39, 47, 22, 42, 4, 8, 5, 25, 26, 9, 45, 13, 31, 56, 49, 11, 28, 55, 51, 23, 0, 12, 58, 19, 40, 24, 33, 41, 2, 32, 17, 27], 'cur_cost': 115270.0}, {'tour': [35, 19, 17, 32, 30, 59, 37, 36, 12, 63, 1, 25, 5, 61, 44, 8, 62, 58, 47, 7, 57, 23, 14, 42, 10, 51, 4, 9, 43, 6, 38, 48, 0, 60, 3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65, 2, 24, 64, 31, 49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27], 'cur_cost': 100569.0}]
2025-08-01 17:43:46,417 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-01 17:43:46,417 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-08-01 17:43:46,418 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([35, 63, 19, 42, 58, 21, 52,  1, 50,  4, 27, 31, 11, 46, 28, 61, 23,
       37, 34, 44, 56, 60,  6, 18, 45, 59, 62, 24,  7, 13, 54, 22, 10, 47,
       12, 36,  8, 30, 33, 64, 26, 17,  5,  0, 14, 39, 65,  2, 49, 25, 43,
       38, 57, 41,  9, 15, 53, 55, 48, 40, 32, 51, 20, 16,  3, 29],
      dtype=int64), 'cur_cost': 110581.0}
2025-08-01 17:43:46,418 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:43:46,419 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:46,419 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:46,419 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 108151.0
2025-08-01 17:43:46,513 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:43:46,514 - ExploitationExpert - INFO - res_population_costs: [9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:43:46,515 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:46,524 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:46,524 - ExploitationExpert - INFO - populations: [{'tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}, {'tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}, {'tour': array([35, 63, 19, 42, 58, 21, 52,  1, 50,  4, 27, 31, 11, 46, 28, 61, 23,
       37, 34, 44, 56, 60,  6, 18, 45, 59, 62, 24,  7, 13, 54, 22, 10, 47,
       12, 36,  8, 30, 33, 64, 26, 17,  5,  0, 14, 39, 65,  2, 49, 25, 43,
       38, 57, 41,  9, 15, 53, 55, 48, 40, 32, 51, 20, 16,  3, 29],
      dtype=int64), 'cur_cost': 110581.0}, {'tour': array([50,  0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43,  7, 46, 18, 60,
       45, 24,  3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57,
        8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33,  4, 27, 25, 26,
        6,  9, 19, 34, 55,  1, 53, 32, 41, 21,  2, 14,  5, 17, 31],
      dtype=int64), 'cur_cost': 108151.0}, {'tour': [0, 4, 12, 13, 20, 21, 19, 16, 18, 23, 22, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12347.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': [38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16, 7, 65, 37, 57, 3, 1, 59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54, 6, 20, 52, 35, 44, 39, 47, 22, 42, 4, 8, 5, 25, 26, 9, 45, 13, 31, 56, 49, 11, 28, 55, 51, 23, 0, 12, 58, 19, 40, 24, 33, 41, 2, 32, 17, 27], 'cur_cost': 115270.0}, {'tour': [35, 19, 17, 32, 30, 59, 37, 36, 12, 63, 1, 25, 5, 61, 44, 8, 62, 58, 47, 7, 57, 23, 14, 42, 10, 51, 4, 9, 43, 6, 38, 48, 0, 60, 3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65, 2, 24, 64, 31, 49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27], 'cur_cost': 100569.0}]
2025-08-01 17:43:46,529 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:43:46,530 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-08-01 17:43:46,530 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([50,  0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43,  7, 46, 18, 60,
       45, 24,  3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57,
        8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33,  4, 27, 25, 26,
        6,  9, 19, 34, 55,  1, 53, 32, 41, 21,  2, 14,  5, 17, 31],
      dtype=int64), 'cur_cost': 108151.0}
2025-08-01 17:43:46,531 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:43:46,532 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:46,532 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:46,532 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 111115.0
2025-08-01 17:43:46,629 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:43:46,630 - ExploitationExpert - INFO - res_population_costs: [9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-01 17:43:46,630 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-01 17:43:46,641 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:46,641 - ExploitationExpert - INFO - populations: [{'tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}, {'tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}, {'tour': array([35, 63, 19, 42, 58, 21, 52,  1, 50,  4, 27, 31, 11, 46, 28, 61, 23,
       37, 34, 44, 56, 60,  6, 18, 45, 59, 62, 24,  7, 13, 54, 22, 10, 47,
       12, 36,  8, 30, 33, 64, 26, 17,  5,  0, 14, 39, 65,  2, 49, 25, 43,
       38, 57, 41,  9, 15, 53, 55, 48, 40, 32, 51, 20, 16,  3, 29],
      dtype=int64), 'cur_cost': 110581.0}, {'tour': array([50,  0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43,  7, 46, 18, 60,
       45, 24,  3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57,
        8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33,  4, 27, 25, 26,
        6,  9, 19, 34, 55,  1, 53, 32, 41, 21,  2, 14,  5, 17, 31],
      dtype=int64), 'cur_cost': 108151.0}, {'tour': array([54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50,  0, 17,
       36, 64,  8, 31, 16, 14, 21, 18, 33, 56, 15,  3, 53, 38, 45, 20,  4,
       52, 48, 30, 28, 13,  5, 42, 60, 19,  1, 39, 27, 65, 44, 10, 11, 58,
        6, 22, 49, 32, 63, 47,  2, 25, 29, 55,  9, 57, 24, 41,  7],
      dtype=int64), 'cur_cost': 111115.0}, {'tour': [44, 15, 1, 9, 6, 60, 7, 4, 61, 52, 58, 8, 5, 20, 18, 17, 25, 12, 24, 23, 2, 62, 56, 64, 39, 14, 22, 16, 28, 36, 35, 3, 11, 26, 33, 19, 37, 32, 31, 47, 51, 50, 21, 34, 48, 45, 13, 46, 43, 41, 49, 29, 40, 30, 10, 63, 65, 53, 55, 59, 57, 54, 0, 27, 42, 38], 'cur_cost': 52224.0}, {'tour': [0, 20, 13, 5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12909.0}, {'tour': [0, 9, 18, 5, 4, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12821.0}, {'tour': [38, 30, 21, 53, 48, 10, 14, 46, 29, 61, 16, 7, 65, 37, 57, 3, 1, 59, 15, 50, 43, 64, 34, 63, 62, 18, 60, 36, 54, 6, 20, 52, 35, 44, 39, 47, 22, 42, 4, 8, 5, 25, 26, 9, 45, 13, 31, 56, 49, 11, 28, 55, 51, 23, 0, 12, 58, 19, 40, 24, 33, 41, 2, 32, 17, 27], 'cur_cost': 115270.0}, {'tour': [35, 19, 17, 32, 30, 59, 37, 36, 12, 63, 1, 25, 5, 61, 44, 8, 62, 58, 47, 7, 57, 23, 14, 42, 10, 51, 4, 9, 43, 6, 38, 48, 0, 60, 3, 18, 39, 28, 29, 16, 56, 52, 55, 20, 40, 41, 65, 2, 24, 64, 31, 49, 53, 34, 54, 13, 46, 45, 26, 21, 15, 22, 11, 50, 33, 27], 'cur_cost': 100569.0}]
2025-08-01 17:43:46,648 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:43:46,648 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-08-01 17:43:46,648 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50,  0, 17,
       36, 64,  8, 31, 16, 14, 21, 18, 33, 56, 15,  3, 53, 38, 45, 20,  4,
       52, 48, 30, 28, 13,  5, 42, 60, 19,  1, 39, 27, 65, 44, 10, 11, 58,
        6, 22, 49, 32, 63, 47,  2, 25, 29, 55,  9, 57, 24, 41,  7],
      dtype=int64), 'cur_cost': 111115.0}
2025-08-01 17:43:46,649 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 17:43:46,649 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 17:43:46,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:46,657 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:46,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:46,658 - ExplorationExpert - INFO - 探索路径生成完成，成本: 70408.0, 路径长度: 66
2025-08-01 17:43:46,658 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [34, 30, 12, 36, 8, 59, 7, 6, 60, 10, 22, 11, 64, 9, 2, 62, 18, 14, 29, 35, 33, 27, 23, 4, 15, 28, 32, 0, 13, 1, 61, 63, 21, 3, 24, 46, 50, 49, 48, 20, 31, 40, 16, 37, 17, 19, 43, 41, 47, 44, 5, 56, 65, 55, 39, 38, 26, 51, 58, 57, 52, 53, 45, 42, 25, 54], 'cur_cost': 70408.0}
2025-08-01 17:43:46,659 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-01 17:43:46,660 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-01 17:43:46,660 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:46,663 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:46,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:46,664 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12887.0, 路径长度: 66
2025-08-01 17:43:46,664 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}
2025-08-01 17:43:46,665 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-01 17:43:46,665 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-01 17:43:46,665 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:46,667 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 17:43:46,668 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:46,668 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103454.0, 路径长度: 66
2025-08-01 17:43:46,668 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}
2025-08-01 17:43:46,669 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-01 17:43:46,669 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-01 17:43:46,670 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:46,675 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:46,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:46,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12931.0, 路径长度: 66
2025-08-01 17:43:46,678 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}
2025-08-01 17:43:46,680 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-01 17:43:46,680 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-01 17:43:46,680 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:46,683 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:46,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:46,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12828.0, 路径长度: 66
2025-08-01 17:43:46,684 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}
2025-08-01 17:43:46,685 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 47, 11, 42, 63,  7, 64, 41, 54,  6, 56,  0, 27, 29, 26, 20, 49,
       45, 60, 18, 44, 50, 30, 34, 32, 19, 15, 51, 37, 31, 46, 22, 43, 35,
        2,  3, 14, 16, 53, 48, 23, 10, 28, 38, 33, 39, 57,  8, 40, 25,  9,
       61, 65,  1,  5, 17, 52, 21, 62, 58, 12, 59, 55,  4, 13, 24],
      dtype=int64), 'cur_cost': 92417.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 41, 22, 47, 62, 49, 12, 11,  1, 13, 14, 59, 25, 26, 30, 50, 46,
       20, 53,  9, 15, 17,  0, 45,  8, 19, 43, 31, 57, 48, 21, 35, 18,  3,
       63,  6,  5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58,
       16,  2, 36, 56, 40, 34, 27,  7, 52, 33, 23, 37, 39, 38,  4],
      dtype=int64), 'cur_cost': 106251.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 63, 19, 42, 58, 21, 52,  1, 50,  4, 27, 31, 11, 46, 28, 61, 23,
       37, 34, 44, 56, 60,  6, 18, 45, 59, 62, 24,  7, 13, 54, 22, 10, 47,
       12, 36,  8, 30, 33, 64, 26, 17,  5,  0, 14, 39, 65,  2, 49, 25, 43,
       38, 57, 41,  9, 15, 53, 55, 48, 40, 32, 51, 20, 16,  3, 29],
      dtype=int64), 'cur_cost': 110581.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([50,  0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43,  7, 46, 18, 60,
       45, 24,  3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57,
        8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33,  4, 27, 25, 26,
        6,  9, 19, 34, 55,  1, 53, 32, 41, 21,  2, 14,  5, 17, 31],
      dtype=int64), 'cur_cost': 108151.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50,  0, 17,
       36, 64,  8, 31, 16, 14, 21, 18, 33, 56, 15,  3, 53, 38, 45, 20,  4,
       52, 48, 30, 28, 13,  5, 42, 60, 19,  1, 39, 27, 65, 44, 10, 11, 58,
        6, 22, 49, 32, 63, 47,  2, 25, 29, 55,  9, 57, 24, 41,  7],
      dtype=int64), 'cur_cost': 111115.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [34, 30, 12, 36, 8, 59, 7, 6, 60, 10, 22, 11, 64, 9, 2, 62, 18, 14, 29, 35, 33, 27, 23, 4, 15, 28, 32, 0, 13, 1, 61, 63, 21, 3, 24, 46, 50, 49, 48, 20, 31, 40, 16, 37, 17, 19, 43, 41, 47, 44, 5, 56, 65, 55, 39, 38, 26, 51, 58, 57, 52, 53, 45, 42, 25, 54], 'cur_cost': 70408.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}}]
2025-08-01 17:43:46,688 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:43:46,688 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:46,692 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12828.000, 多样性=0.932
2025-08-01 17:43:46,692 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-01 17:43:46,692 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-01 17:43:46,693 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:43:46,695 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09552929175547335, 'best_improvement': -0.038956831619016766}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.023641496118560222}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7815426997245178, 'new_diversity': 0.7815426997245178, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:43:46,698 - main - INFO - --- Finished Evolution Iteration 3 ---
2025-08-01 17:43:46,698 - main - INFO - composite13_66 开始进化第 4 代
2025-08-01 17:43:46,699 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-01 17:43:46,699 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:46,700 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12828.000, 多样性=0.932
2025-08-01 17:43:46,700 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:43:46,706 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.932
2025-08-01 17:43:46,707 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:43:46,715 - EliteExpert - INFO - 精英解分析完成: 精英解数量=11, 多样性=0.782
2025-08-01 17:43:46,718 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-01 17:43:46,718 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:43:46,718 - LandscapeExpert - INFO - 添加精英解数据: 11个精英解
2025-08-01 17:43:46,718 - LandscapeExpert - INFO - 数据提取成功: 21个路径, 21个适应度值
2025-08-01 17:43:46,811 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:43:46,813 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-01 17:43:46,814 - LandscapeExpert - INFO - 提取到 11 个精英解
2025-08-01 17:43:46,822 - visualization.landscape_visualizer - INFO - 已添加 11 个精英解标记
2025-08-01 17:43:46,910 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_4_20250801_174346.html
2025-08-01 17:43:46,960 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_4_20250801_174346.html
2025-08-01 17:43:46,960 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 4
2025-08-01 17:43:46,960 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-01 17:43:46,961 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2421秒
2025-08-01 17:43:46,962 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041426.813949, 'performance_metrics': {}}}
2025-08-01 17:43:46,963 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:43:46,963 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:43:46,964 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12828.0
  • mean_cost: 74102.3
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: exploitation
- Landscape focus suggestion: exploitation
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 40, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:43:46,967 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:43:46,967 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:43:48,983 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, low diversity, and unexplored space. Balance exploration with exploitation, favoring exploitation."
}
```
2025-08-01 17:43:48,985 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:43:48,986 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:48,986 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:48,987 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, low diversity, and unexplored space. Balance exploration with exploitation, favoring exploitation."
}
```
2025-08-01 17:43:48,988 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:43:48,988 - main - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:48,989 - main - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Exploitation phase, low diversity, and unexplored space. Balance exploration with exploitation, favoring exploitation."
}
```
2025-08-01 17:43:48,990 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:43:48,991 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-01 17:43:48,991 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:48,991 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:48,992 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 107376.0
2025-08-01 17:43:49,097 - ExploitationExpert - INFO - res_population_num: 11
2025-08-01 17:43:49,097 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:49,097 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:49,110 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,111 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': [10, 41, 22, 47, 62, 49, 12, 11, 1, 13, 14, 59, 25, 26, 30, 50, 46, 20, 53, 9, 15, 17, 0, 45, 8, 19, 43, 31, 57, 48, 21, 35, 18, 3, 63, 6, 5, 65, 54, 32, 51, 60, 28, 29, 61, 44, 55, 24, 42, 64, 58, 16, 2, 36, 56, 40, 34, 27, 7, 52, 33, 23, 37, 39, 38, 4], 'cur_cost': 106251.0}, {'tour': [35, 63, 19, 42, 58, 21, 52, 1, 50, 4, 27, 31, 11, 46, 28, 61, 23, 37, 34, 44, 56, 60, 6, 18, 45, 59, 62, 24, 7, 13, 54, 22, 10, 47, 12, 36, 8, 30, 33, 64, 26, 17, 5, 0, 14, 39, 65, 2, 49, 25, 43, 38, 57, 41, 9, 15, 53, 55, 48, 40, 32, 51, 20, 16, 3, 29], 'cur_cost': 110581.0}, {'tour': [50, 0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43, 7, 46, 18, 60, 45, 24, 3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57, 8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33, 4, 27, 25, 26, 6, 9, 19, 34, 55, 1, 53, 32, 41, 21, 2, 14, 5, 17, 31], 'cur_cost': 108151.0}, {'tour': [54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50, 0, 17, 36, 64, 8, 31, 16, 14, 21, 18, 33, 56, 15, 3, 53, 38, 45, 20, 4, 52, 48, 30, 28, 13, 5, 42, 60, 19, 1, 39, 27, 65, 44, 10, 11, 58, 6, 22, 49, 32, 63, 47, 2, 25, 29, 55, 9, 57, 24, 41, 7], 'cur_cost': 111115.0}, {'tour': [34, 30, 12, 36, 8, 59, 7, 6, 60, 10, 22, 11, 64, 9, 2, 62, 18, 14, 29, 35, 33, 27, 23, 4, 15, 28, 32, 0, 13, 1, 61, 63, 21, 3, 24, 46, 50, 49, 48, 20, 31, 40, 16, 37, 17, 19, 43, 41, 47, 44, 5, 56, 65, 55, 39, 38, 26, 51, 58, 57, 52, 53, 45, 42, 25, 54], 'cur_cost': 70408.0}, {'tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}, {'tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,116 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:43:49,116 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-08-01 17:43:49,117 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}
2025-08-01 17:43:49,118 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-01 17:43:49,118 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,118 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,119 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 94701.0
2025-08-01 17:43:49,235 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:43:49,236 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521]
2025-08-01 17:43:49,236 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,249 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,249 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [35, 63, 19, 42, 58, 21, 52, 1, 50, 4, 27, 31, 11, 46, 28, 61, 23, 37, 34, 44, 56, 60, 6, 18, 45, 59, 62, 24, 7, 13, 54, 22, 10, 47, 12, 36, 8, 30, 33, 64, 26, 17, 5, 0, 14, 39, 65, 2, 49, 25, 43, 38, 57, 41, 9, 15, 53, 55, 48, 40, 32, 51, 20, 16, 3, 29], 'cur_cost': 110581.0}, {'tour': [50, 0, 54, 20, 61, 13, 49, 30, 56, 23, 62, 58, 43, 7, 46, 18, 60, 45, 24, 3, 64, 52, 65, 51, 15, 59, 48, 36, 28, 16, 44, 10, 39, 57, 8, 63, 37, 38, 29, 12, 22, 42, 11, 47, 40, 35, 33, 4, 27, 25, 26, 6, 9, 19, 34, 55, 1, 53, 32, 41, 21, 2, 14, 5, 17, 31], 'cur_cost': 108151.0}, {'tour': [54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50, 0, 17, 36, 64, 8, 31, 16, 14, 21, 18, 33, 56, 15, 3, 53, 38, 45, 20, 4, 52, 48, 30, 28, 13, 5, 42, 60, 19, 1, 39, 27, 65, 44, 10, 11, 58, 6, 22, 49, 32, 63, 47, 2, 25, 29, 55, 9, 57, 24, 41, 7], 'cur_cost': 111115.0}, {'tour': [34, 30, 12, 36, 8, 59, 7, 6, 60, 10, 22, 11, 64, 9, 2, 62, 18, 14, 29, 35, 33, 27, 23, 4, 15, 28, 32, 0, 13, 1, 61, 63, 21, 3, 24, 46, 50, 49, 48, 20, 31, 40, 16, 37, 17, 19, 43, 41, 47, 44, 5, 56, 65, 55, 39, 38, 26, 51, 58, 57, 52, 53, 45, 42, 25, 54], 'cur_cost': 70408.0}, {'tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}, {'tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,253 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:43:49,253 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-08-01 17:43:49,254 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}
2025-08-01 17:43:49,254 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:43:49,255 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:43:49,255 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:49,263 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:49,263 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:49,264 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59120.0, 路径长度: 66
2025-08-01 17:43:49,264 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}
2025-08-01 17:43:49,265 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-01 17:43:49,265 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,265 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,266 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103594.0
2025-08-01 17:43:49,363 - ExploitationExpert - INFO - res_population_num: 12
2025-08-01 17:43:49,364 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521]
2025-08-01 17:43:49,364 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,377 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,377 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [54, 23, 43, 51, 26, 12, 46, 34, 61, 40, 62, 59, 35, 37, 50, 0, 17, 36, 64, 8, 31, 16, 14, 21, 18, 33, 56, 15, 3, 53, 38, 45, 20, 4, 52, 48, 30, 28, 13, 5, 42, 60, 19, 1, 39, 27, 65, 44, 10, 11, 58, 6, 22, 49, 32, 63, 47, 2, 25, 29, 55, 9, 57, 24, 41, 7], 'cur_cost': 111115.0}, {'tour': [34, 30, 12, 36, 8, 59, 7, 6, 60, 10, 22, 11, 64, 9, 2, 62, 18, 14, 29, 35, 33, 27, 23, 4, 15, 28, 32, 0, 13, 1, 61, 63, 21, 3, 24, 46, 50, 49, 48, 20, 31, 40, 16, 37, 17, 19, 43, 41, 47, 44, 5, 56, 65, 55, 39, 38, 26, 51, 58, 57, 52, 53, 45, 42, 25, 54], 'cur_cost': 70408.0}, {'tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}, {'tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,383 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:43:49,384 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-08-01 17:43:49,385 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}
2025-08-01 17:43:49,385 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-01 17:43:49,385 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-01 17:43:49,386 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:49,392 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:49,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:49,393 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61186.0, 路径长度: 66
2025-08-01 17:43:49,393 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}
2025-08-01 17:43:49,394 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-01 17:43:49,394 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,394 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,395 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 114726.0
2025-08-01 17:43:49,485 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:43:49,486 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521, 9521]
2025-08-01 17:43:49,486 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,495 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,495 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}, {'tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}, {'tour': [0, 5, 13, 1, 7, 3, 9, 11, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12887.0}, {'tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,506 - ExploitationExpert - INFO - 局部搜索耗时: 0.11秒
2025-08-01 17:43:49,510 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-08-01 17:43:49,512 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}
2025-08-01 17:43:49,514 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:43:49,514 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,515 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,516 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 112237.0
2025-08-01 17:43:49,644 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:43:49,645 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521, 9521]
2025-08-01 17:43:49,645 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,654 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,655 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}, {'tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}, {'tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}, {'tour': [24, 62, 34, 39, 13, 4, 0, 42, 28, 50, 36, 15, 59, 32, 2, 27, 58, 57, 35, 10, 44, 22, 43, 16, 18, 49, 48, 12, 21, 8, 53, 5, 37, 56, 46, 14, 25, 40, 26, 45, 17, 7, 11, 30, 33, 23, 3, 63, 60, 41, 65, 51, 31, 52, 61, 9, 6, 38, 47, 1, 55, 20, 19, 54, 64, 29], 'cur_cost': 103454.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,659 - ExploitationExpert - INFO - 局部搜索耗时: 0.14秒
2025-08-01 17:43:49,660 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-08-01 17:43:49,660 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}
2025-08-01 17:43:49,661 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:43:49,661 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,662 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,662 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108049.0
2025-08-01 17:43:49,761 - ExploitationExpert - INFO - res_population_num: 13
2025-08-01 17:43:49,762 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521, 9521]
2025-08-01 17:43:49,762 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,777 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,778 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}, {'tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}, {'tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}, {'tour': array([ 3,  5, 40, 16, 63, 44,  0, 57, 53, 24, 46,  2, 13, 22,  6, 12, 23,
       25, 28, 20, 49, 47, 34, 11, 55, 26, 21,  7, 56, 38, 10, 43, 33, 29,
       45, 51, 18, 52, 17, 65,  8, 42,  9, 62, 64, 35, 27, 50, 39, 19, 61,
       41, 59, 32,  4, 48, 14,  1, 60, 15, 36, 54, 30, 58, 31, 37],
      dtype=int64), 'cur_cost': 108049.0}, {'tour': [0, 21, 2, 9, 11, 7, 3, 1, 5, 4, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12931.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,785 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-08-01 17:43:49,786 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-08-01 17:43:49,786 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 3,  5, 40, 16, 63, 44,  0, 57, 53, 24, 46,  2, 13, 22,  6, 12, 23,
       25, 28, 20, 49, 47, 34, 11, 55, 26, 21,  7, 56, 38, 10, 43, 33, 29,
       45, 51, 18, 52, 17, 65,  8, 42,  9, 62, 64, 35, 27, 50, 39, 19, 61,
       41, 59, 32,  4, 48, 14,  1, 60, 15, 36, 54, 30, 58, 31, 37],
      dtype=int64), 'cur_cost': 108049.0}
2025-08-01 17:43:49,787 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:43:49,787 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,787 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,788 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 117845.0
2025-08-01 17:43:49,885 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:49,885 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521]
2025-08-01 17:43:49,886 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:49,897 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:49,897 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}, {'tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}, {'tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}, {'tour': array([ 3,  5, 40, 16, 63, 44,  0, 57, 53, 24, 46,  2, 13, 22,  6, 12, 23,
       25, 28, 20, 49, 47, 34, 11, 55, 26, 21,  7, 56, 38, 10, 43, 33, 29,
       45, 51, 18, 52, 17, 65,  8, 42,  9, 62, 64, 35, 27, 50, 39, 19, 61,
       41, 59, 32,  4, 48, 14,  1, 60, 15, 36, 54, 30, 58, 31, 37],
      dtype=int64), 'cur_cost': 108049.0}, {'tour': array([17, 25, 51, 36, 60, 32,  8, 29, 47, 15,  3, 35, 21, 30, 56, 28,  2,
        7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26,  1, 40,
       63, 12, 48, 42, 18, 34, 62,  4, 65, 24, 41,  5, 19, 20, 53, 58, 22,
       10, 33, 64, 39, 16, 50, 13, 44,  9, 55, 27, 61, 31,  6,  0],
      dtype=int64), 'cur_cost': 117845.0}, {'tour': [0, 20, 11, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12828.0}]
2025-08-01 17:43:49,934 - ExploitationExpert - INFO - 局部搜索耗时: 0.15秒
2025-08-01 17:43:49,934 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-08-01 17:43:49,935 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([17, 25, 51, 36, 60, 32,  8, 29, 47, 15,  3, 35, 21, 30, 56, 28,  2,
        7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26,  1, 40,
       63, 12, 48, 42, 18, 34, 62,  4, 65, 24, 41,  5, 19, 20, 53, 58, 22,
       10, 33, 64, 39, 16, 50, 13, 44,  9, 55, 27, 61, 31,  6,  0],
      dtype=int64), 'cur_cost': 117845.0}
2025-08-01 17:43:49,935 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:43:49,936 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:49,937 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:49,939 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110294.0
2025-08-01 17:43:50,061 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:50,062 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0, 9521, 9521, 9521]
2025-08-01 17:43:50,062 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-01 17:43:50,074 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:50,075 - ExploitationExpert - INFO - populations: [{'tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}, {'tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}, {'tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}, {'tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}, {'tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}, {'tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}, {'tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}, {'tour': array([ 3,  5, 40, 16, 63, 44,  0, 57, 53, 24, 46,  2, 13, 22,  6, 12, 23,
       25, 28, 20, 49, 47, 34, 11, 55, 26, 21,  7, 56, 38, 10, 43, 33, 29,
       45, 51, 18, 52, 17, 65,  8, 42,  9, 62, 64, 35, 27, 50, 39, 19, 61,
       41, 59, 32,  4, 48, 14,  1, 60, 15, 36, 54, 30, 58, 31, 37],
      dtype=int64), 'cur_cost': 108049.0}, {'tour': array([17, 25, 51, 36, 60, 32,  8, 29, 47, 15,  3, 35, 21, 30, 56, 28,  2,
        7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26,  1, 40,
       63, 12, 48, 42, 18, 34, 62,  4, 65, 24, 41,  5, 19, 20, 53, 58, 22,
       10, 33, 64, 39, 16, 50, 13, 44,  9, 55, 27, 61, 31,  6,  0],
      dtype=int64), 'cur_cost': 117845.0}, {'tour': array([59, 19,  0, 44, 58,  3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37,
       23, 29, 16,  7, 11, 61, 33, 60, 26,  9, 32, 50, 63, 38,  1,  2, 30,
       18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14,  8, 62, 35,
       24,  4, 15,  5, 57, 46, 34, 25, 43,  6, 39, 41, 54, 45, 40],
      dtype=int64), 'cur_cost': 110294.0}]
2025-08-01 17:43:50,084 - ExploitationExpert - INFO - 局部搜索耗时: 0.15秒
2025-08-01 17:43:50,084 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-08-01 17:43:50,085 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([59, 19,  0, 44, 58,  3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37,
       23, 29, 16,  7, 11, 61, 33, 60, 26,  9, 32, 50, 63, 38,  1,  2, 30,
       18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14,  8, 62, 35,
       24,  4, 15,  5, 57, 46, 34, 25, 43,  6, 39, 41, 54, 45, 40],
      dtype=int64), 'cur_cost': 110294.0}
2025-08-01 17:43:50,086 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 31, 15,  2, 26, 64, 20, 50, 49, 46, 30, 39, 33, 16, 56, 44,  9,
       53, 57,  0, 27, 65,  1, 41,  4, 23, 47, 51, 62, 17, 10,  5, 24,  3,
       42,  8, 52, 60, 32, 14, 36, 55, 25, 40, 11, 18, 19, 45, 28, 59, 34,
       12, 58, 63, 38, 61,  7, 21, 22, 29, 37, 13,  6, 54, 35, 43],
      dtype=int64), 'cur_cost': 107376.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([64, 28, 30, 43, 22,  2, 34,  0, 55, 60, 11, 10, 14, 56, 57, 51, 20,
       13, 32, 40, 31,  9,  7,  1, 58, 46, 23, 29, 19, 33, 17, 26, 18, 16,
       21, 65,  8, 38, 41, 53, 47, 25, 15, 12, 42, 50,  6,  5, 39,  3, 45,
       37, 36, 61, 59,  4, 48, 27, 35, 49, 54, 62, 63, 44, 52, 24],
      dtype=int64), 'cur_cost': 94701.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [56, 54, 9, 65, 64, 57, 55, 62, 2, 15, 19, 17, 14, 5, 0, 22, 16, 27, 11, 7, 52, 13, 32, 6, 58, 23, 3, 20, 34, 18, 28, 26, 29, 40, 51, 21, 30, 43, 50, 38, 47, 46, 36, 4, 10, 8, 53, 63, 44, 42, 35, 1, 61, 60, 59, 41, 12, 24, 31, 33, 25, 49, 39, 45, 48, 37], 'cur_cost': 59120.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 38, 53, 26, 39, 24,  8, 45, 27, 19, 37, 10, 25, 31,  1, 40, 14,
       58, 64, 44, 21, 30, 32, 35, 16, 29, 48,  7, 28,  2, 52, 59,  3, 63,
        4, 49, 46,  6, 62, 22, 36, 12, 50, 60, 57, 13,  9, 55, 34, 20,  0,
       65, 17, 18, 15, 41,  5, 47, 61, 43, 56, 11, 54, 51, 42, 23],
      dtype=int64), 'cur_cost': 103594.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 22, 3, 8, 62, 20, 23, 32, 27, 12, 26, 34, 29, 9, 5, 63, 11, 18, 25, 43, 35, 40, 41, 42, 50, 13, 28, 19, 15, 21, 36, 14, 24, 2, 0, 64, 6, 56, 59, 60, 16, 37, 1, 54, 39, 51, 44, 46, 49, 38, 45, 31, 48, 47, 33, 4, 58, 57, 61, 55, 53, 52, 10, 7, 17, 30], 'cur_cost': 61186.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 61, 48, 37, 18, 59, 38,  4, 34, 35, 23, 14,  6,  7,  8, 28, 16,
       12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20,  9, 10, 46, 49, 22, 47,
       21,  3, 31, 57, 19, 24,  5, 36, 39, 53, 25,  1, 33, 44, 45, 64, 30,
       55, 42, 52, 27, 26, 40,  2, 58,  0, 60, 43, 17, 11, 62, 50],
      dtype=int64), 'cur_cost': 114726.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 17, 63,  9,  0, 13, 60, 15,  8, 42, 55, 30, 40, 59, 19, 61, 27,
       53, 18, 37,  6,  3,  2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57,  5,
       24, 52, 11, 48, 31, 36,  7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51,
       26, 20, 32, 10, 56, 12, 54,  1, 28, 45, 39, 29, 35, 38, 65],
      dtype=int64), 'cur_cost': 112237.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3,  5, 40, 16, 63, 44,  0, 57, 53, 24, 46,  2, 13, 22,  6, 12, 23,
       25, 28, 20, 49, 47, 34, 11, 55, 26, 21,  7, 56, 38, 10, 43, 33, 29,
       45, 51, 18, 52, 17, 65,  8, 42,  9, 62, 64, 35, 27, 50, 39, 19, 61,
       41, 59, 32,  4, 48, 14,  1, 60, 15, 36, 54, 30, 58, 31, 37],
      dtype=int64), 'cur_cost': 108049.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 25, 51, 36, 60, 32,  8, 29, 47, 15,  3, 35, 21, 30, 56, 28,  2,
        7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26,  1, 40,
       63, 12, 48, 42, 18, 34, 62,  4, 65, 24, 41,  5, 19, 20, 53, 58, 22,
       10, 33, 64, 39, 16, 50, 13, 44,  9, 55, 27, 61, 31,  6,  0],
      dtype=int64), 'cur_cost': 117845.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 19,  0, 44, 58,  3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37,
       23, 29, 16,  7, 11, 61, 33, 60, 26,  9, 32, 50, 63, 38,  1,  2, 30,
       18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14,  8, 62, 35,
       24,  4, 15,  5, 57, 46, 34, 25, 43,  6, 39, 41, 54, 45, 40],
      dtype=int64), 'cur_cost': 110294.0}}]
2025-08-01 17:43:50,090 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:43:50,091 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:50,094 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=59120.000, 多样性=0.984
2025-08-01 17:43:50,095 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-01 17:43:50,095 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-01 17:43:50,097 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:43:50,098 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -1.3346835289197105, 'best_improvement': -3.6086685375740566}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.055655945066859135}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.09868425183377681, 'recent_improvements': [0.10183921191208027, -0.11884214474616413, -0.09552929175547335], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7878787878787878, 'new_diversity': 0.7878787878787878, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:43:50,101 - main - INFO - --- Finished Evolution Iteration 4 ---
2025-08-01 17:43:50,101 - main - INFO - composite13_66 开始进化第 5 代
2025-08-01 17:43:50,102 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-01 17:43:50,102 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:50,107 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=59120.000, 多样性=0.984
2025-08-01 17:43:50,107 - PathExpert - INFO - 开始路径结构分析
2025-08-01 17:43:50,114 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.984
2025-08-01 17:43:50,114 - EliteExpert - INFO - 开始精英解分析
2025-08-01 17:43:50,124 - EliteExpert - INFO - 精英解分析完成: 精英解数量=14, 多样性=0.788
2025-08-01 17:43:50,125 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-01 17:43:50,125 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-01 17:43:50,125 - LandscapeExpert - INFO - 添加精英解数据: 14个精英解
2025-08-01 17:43:50,125 - LandscapeExpert - INFO - 数据提取成功: 24个路径, 24个适应度值
2025-08-01 17:43:50,242 - core.algorithms.fitness_landscape_analyzer - ERROR - 景观分析失败: '>' not supported between instances of 'str' and 'int'
2025-08-01 17:43:50,243 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-01 17:43:50,243 - LandscapeExpert - INFO - 提取到 14 个精英解
2025-08-01 17:43:50,254 - visualization.landscape_visualizer - INFO - 已添加 14 个精英解标记
2025-08-01 17:43:50,354 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_5_20250801_174350.html
2025-08-01 17:43:50,404 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_5_20250801_174350.html
2025-08-01 17:43:50,407 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 5
2025-08-01 17:43:50,408 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-01 17:43:50,409 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2831秒
2025-08-01 17:43:50,409 - main - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.5, 'local_optima_density': 0.5}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 0.5, 'coverage': 0.5}, 'difficult_regions': [], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754041430.2431607, 'performance_metrics': {}}}
2025-08-01 17:43:50,411 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-01 17:43:50,411 - StrategyExpert - INFO - 开始策略分配分析
2025-08-01 17:43:50,412 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 59120.0
  • mean_cost: 98912.8
- Search-space: ruggedness 0.5, modality uni-modal, deceptiveness low
- Evolution phase: convergence
- Landscape focus suggestion: intensification
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [{"type": "unexplored_space", "potential": "high", "description": "未充分探索的搜索空间", "recommendation": "增加探索性操作"}]
- Last-iteration feedback: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-08-01 17:43:50,416 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-08-01 17:43:50,417 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-08-01 17:43:51,992 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase with low diversity and unexplored space calls for increased exploration. Favoring some exploit for local improvements."
}
```
2025-08-01 17:43:51,992 - utils.utils - INFO - 成功从JSON中提取数据
2025-08-01 17:43:51,993 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:51,993 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:51,993 - experts.management.collaboration_manager - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase with low diversity and unexplored space calls for increased exploration. Favoring some exploit for local improvements."
}
```
2025-08-01 17:43:51,995 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-01 17:43:51,996 - main - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-08-01 17:43:51,997 - main - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Convergence phase with low diversity and unexplored space calls for increased exploration. Favoring some exploit for local improvements."
}
```
2025-08-01 17:43:51,999 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-01 17:43:51,999 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-01 17:43:51,999 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-01 17:43:51,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:52,008 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:52,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:52,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59863.0, 路径长度: 66
2025-08-01 17:43:52,010 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}
2025-08-01 17:43:52,011 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-01 17:43:52,011 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-01 17:43:52,012 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:52,015 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-01 17:43:52,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:52,017 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12388.0, 路径长度: 66
2025-08-01 17:43:52,017 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}
2025-08-01 17:43:52,018 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-01 17:43:52,018 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-01 17:43:52,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:52,022 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 17:43:52,022 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:52,023 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100789.0, 路径长度: 66
2025-08-01 17:43:52,023 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}
2025-08-01 17:43:52,023 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-01 17:43:52,024 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-01 17:43:52,024 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:52,031 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-01 17:43:52,032 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:52,032 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57823.0, 路径长度: 66
2025-08-01 17:43:52,033 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}
2025-08-01 17:43:52,033 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-01 17:43:52,034 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:52,034 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:52,035 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 123298.0
2025-08-01 17:43:52,149 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:52,150 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:52,151 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:52,162 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:52,163 - ExploitationExpert - INFO - populations: [{'tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}, {'tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}, {'tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}, {'tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}, {'tour': [54, 61, 48, 37, 18, 59, 38, 4, 34, 35, 23, 14, 6, 7, 8, 28, 16, 12, 32, 63, 41, 13, 65, 29, 15, 51, 56, 20, 9, 10, 46, 49, 22, 47, 21, 3, 31, 57, 19, 24, 5, 36, 39, 53, 25, 1, 33, 44, 45, 64, 30, 55, 42, 52, 27, 26, 40, 2, 58, 0, 60, 43, 17, 11, 62, 50], 'cur_cost': 114726.0}, {'tour': [4, 17, 63, 9, 0, 13, 60, 15, 8, 42, 55, 30, 40, 59, 19, 61, 27, 53, 18, 37, 6, 3, 2, 58, 41, 16, 23, 64, 14, 49, 34, 44, 57, 5, 24, 52, 11, 48, 31, 36, 7, 33, 47, 25, 22, 50, 21, 46, 43, 62, 51, 26, 20, 32, 10, 56, 12, 54, 1, 28, 45, 39, 29, 35, 38, 65], 'cur_cost': 112237.0}, {'tour': [3, 5, 40, 16, 63, 44, 0, 57, 53, 24, 46, 2, 13, 22, 6, 12, 23, 25, 28, 20, 49, 47, 34, 11, 55, 26, 21, 7, 56, 38, 10, 43, 33, 29, 45, 51, 18, 52, 17, 65, 8, 42, 9, 62, 64, 35, 27, 50, 39, 19, 61, 41, 59, 32, 4, 48, 14, 1, 60, 15, 36, 54, 30, 58, 31, 37], 'cur_cost': 108049.0}, {'tour': [17, 25, 51, 36, 60, 32, 8, 29, 47, 15, 3, 35, 21, 30, 56, 28, 2, 7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26, 1, 40, 63, 12, 48, 42, 18, 34, 62, 4, 65, 24, 41, 5, 19, 20, 53, 58, 22, 10, 33, 64, 39, 16, 50, 13, 44, 9, 55, 27, 61, 31, 6, 0], 'cur_cost': 117845.0}, {'tour': [59, 19, 0, 44, 58, 3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37, 23, 29, 16, 7, 11, 61, 33, 60, 26, 9, 32, 50, 63, 38, 1, 2, 30, 18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14, 8, 62, 35, 24, 4, 15, 5, 57, 46, 34, 25, 43, 6, 39, 41, 54, 45, 40], 'cur_cost': 110294.0}]
2025-08-01 17:43:52,167 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:43:52,167 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-08-01 17:43:52,168 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}
2025-08-01 17:43:52,168 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-01 17:43:52,169 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-01 17:43:52,169 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-01 17:43:52,175 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-01 17:43:52,176 - ExplorationExpert - INFO - 计算路径成本
2025-08-01 17:43:52,177 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95053.0, 路径长度: 66
2025-08-01 17:43:52,177 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}
2025-08-01 17:43:52,179 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-01 17:43:52,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:52,180 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:52,181 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104957.0
2025-08-01 17:43:52,289 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:52,289 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:52,290 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:52,301 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:52,302 - ExploitationExpert - INFO - populations: [{'tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}, {'tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}, {'tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}, {'tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}, {'tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}, {'tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}, {'tour': [3, 5, 40, 16, 63, 44, 0, 57, 53, 24, 46, 2, 13, 22, 6, 12, 23, 25, 28, 20, 49, 47, 34, 11, 55, 26, 21, 7, 56, 38, 10, 43, 33, 29, 45, 51, 18, 52, 17, 65, 8, 42, 9, 62, 64, 35, 27, 50, 39, 19, 61, 41, 59, 32, 4, 48, 14, 1, 60, 15, 36, 54, 30, 58, 31, 37], 'cur_cost': 108049.0}, {'tour': [17, 25, 51, 36, 60, 32, 8, 29, 47, 15, 3, 35, 21, 30, 56, 28, 2, 7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26, 1, 40, 63, 12, 48, 42, 18, 34, 62, 4, 65, 24, 41, 5, 19, 20, 53, 58, 22, 10, 33, 64, 39, 16, 50, 13, 44, 9, 55, 27, 61, 31, 6, 0], 'cur_cost': 117845.0}, {'tour': [59, 19, 0, 44, 58, 3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37, 23, 29, 16, 7, 11, 61, 33, 60, 26, 9, 32, 50, 63, 38, 1, 2, 30, 18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14, 8, 62, 35, 24, 4, 15, 5, 57, 46, 34, 25, 43, 6, 39, 41, 54, 45, 40], 'cur_cost': 110294.0}]
2025-08-01 17:43:52,310 - ExploitationExpert - INFO - 局部搜索耗时: 0.13秒
2025-08-01 17:43:52,311 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:43:52,313 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}
2025-08-01 17:43:52,314 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-01 17:43:52,315 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:52,315 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:52,316 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 112071.0
2025-08-01 17:43:53,553 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:53,554 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:53,554 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:53,567 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:53,568 - ExploitationExpert - INFO - populations: [{'tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}, {'tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}, {'tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}, {'tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}, {'tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}, {'tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}, {'tour': array([30, 55, 57, 42, 50, 33,  8,  5, 58, 17, 22,  4, 40, 16,  3, 63, 56,
       51, 10, 12, 13,  6, 59, 27, 52, 21, 44, 48, 26, 18, 14,  9, 38, 65,
       46, 23, 31,  0,  2, 49, 20, 11, 37, 41, 53, 29, 64, 34, 32, 60, 19,
       28, 54, 43, 36, 61,  1, 62, 45, 24, 25, 39, 35, 47, 15,  7],
      dtype=int64), 'cur_cost': 112071.0}, {'tour': [17, 25, 51, 36, 60, 32, 8, 29, 47, 15, 3, 35, 21, 30, 56, 28, 2, 7, 43, 46, 57, 11, 49, 38, 23, 45, 54, 14, 37, 59, 52, 26, 1, 40, 63, 12, 48, 42, 18, 34, 62, 4, 65, 24, 41, 5, 19, 20, 53, 58, 22, 10, 33, 64, 39, 16, 50, 13, 44, 9, 55, 27, 61, 31, 6, 0], 'cur_cost': 117845.0}, {'tour': [59, 19, 0, 44, 58, 3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37, 23, 29, 16, 7, 11, 61, 33, 60, 26, 9, 32, 50, 63, 38, 1, 2, 30, 18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14, 8, 62, 35, 24, 4, 15, 5, 57, 46, 34, 25, 43, 6, 39, 41, 54, 45, 40], 'cur_cost': 110294.0}]
2025-08-01 17:43:53,585 - ExploitationExpert - INFO - 局部搜索耗时: 1.27秒
2025-08-01 17:43:53,586 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:43:53,587 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([30, 55, 57, 42, 50, 33,  8,  5, 58, 17, 22,  4, 40, 16,  3, 63, 56,
       51, 10, 12, 13,  6, 59, 27, 52, 21, 44, 48, 26, 18, 14,  9, 38, 65,
       46, 23, 31,  0,  2, 49, 20, 11, 37, 41, 53, 29, 64, 34, 32, 60, 19,
       28, 54, 43, 36, 61,  1, 62, 45, 24, 25, 39, 35, 47, 15,  7],
      dtype=int64), 'cur_cost': 112071.0}
2025-08-01 17:43:53,588 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-01 17:43:53,589 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:53,589 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:53,590 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108276.0
2025-08-01 17:43:53,746 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:53,747 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:53,747 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:53,758 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:53,758 - ExploitationExpert - INFO - populations: [{'tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}, {'tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}, {'tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}, {'tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}, {'tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}, {'tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}, {'tour': array([30, 55, 57, 42, 50, 33,  8,  5, 58, 17, 22,  4, 40, 16,  3, 63, 56,
       51, 10, 12, 13,  6, 59, 27, 52, 21, 44, 48, 26, 18, 14,  9, 38, 65,
       46, 23, 31,  0,  2, 49, 20, 11, 37, 41, 53, 29, 64, 34, 32, 60, 19,
       28, 54, 43, 36, 61,  1, 62, 45, 24, 25, 39, 35, 47, 15,  7],
      dtype=int64), 'cur_cost': 112071.0}, {'tour': array([61, 10,  6,  8, 57, 20, 24, 45, 54, 38, 17, 26,  0, 59, 44, 62, 12,
       19,  9, 28, 29, 35, 49, 14, 39, 18, 41, 52,  5, 42, 36, 43, 31, 37,
       22, 48, 30,  1,  7, 55, 15, 58, 33, 46, 25, 60,  3, 32, 56, 16, 23,
        2,  4, 63, 13, 40, 27, 47, 53, 51, 50, 64, 34, 65, 21, 11],
      dtype=int64), 'cur_cost': 108276.0}, {'tour': [59, 19, 0, 44, 58, 3, 22, 17, 42, 10, 55, 56, 48, 52, 49, 20, 37, 23, 29, 16, 7, 11, 61, 33, 60, 26, 9, 32, 50, 63, 38, 1, 2, 30, 18, 31, 27, 64, 53, 36, 21, 47, 13, 65, 28, 51, 12, 14, 8, 62, 35, 24, 4, 15, 5, 57, 46, 34, 25, 43, 6, 39, 41, 54, 45, 40], 'cur_cost': 110294.0}]
2025-08-01 17:43:53,765 - ExploitationExpert - INFO - 局部搜索耗时: 0.17秒
2025-08-01 17:43:53,765 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:43:53,766 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([61, 10,  6,  8, 57, 20, 24, 45, 54, 38, 17, 26,  0, 59, 44, 62, 12,
       19,  9, 28, 29, 35, 49, 14, 39, 18, 41, 52,  5, 42, 36, 43, 31, 37,
       22, 48, 30,  1,  7, 55, 15, 58, 33, 46, 25, 60,  3, 32, 56, 16, 23,
        2,  4, 63, 13, 40, 27, 47, 53, 51, 50, 64, 34, 65, 21, 11],
      dtype=int64), 'cur_cost': 108276.0}
2025-08-01 17:43:53,766 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-01 17:43:53,767 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-01 17:43:53,767 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-01 17:43:53,768 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 106751.0
2025-08-01 17:43:53,896 - ExploitationExpert - INFO - res_population_num: 14
2025-08-01 17:43:53,896 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525.0, 9563.0, 82731.0]
2025-08-01 17:43:53,897 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 18, 16, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 32, 29, 20, 16, 46, 65, 59, 28, 15, 64, 25, 35, 63,  5, 39, 47,
       51,  6, 58, 54, 57, 62, 50, 49, 22, 36, 56, 44, 18,  7, 17, 30, 34,
       42, 31, 13, 23, 53, 21, 14,  9, 27, 37, 19, 24, 48, 41, 40, 60, 12,
       45, 52, 55, 43, 38, 26, 61,  3,  1,  2,  4,  8, 11, 33, 10],
      dtype=int64)]
2025-08-01 17:43:53,910 - ExploitationExpert - INFO - populations_num: 10
2025-08-01 17:43:53,912 - ExploitationExpert - INFO - populations: [{'tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}, {'tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}, {'tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}, {'tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}, {'tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}, {'tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}, {'tour': array([30, 55, 57, 42, 50, 33,  8,  5, 58, 17, 22,  4, 40, 16,  3, 63, 56,
       51, 10, 12, 13,  6, 59, 27, 52, 21, 44, 48, 26, 18, 14,  9, 38, 65,
       46, 23, 31,  0,  2, 49, 20, 11, 37, 41, 53, 29, 64, 34, 32, 60, 19,
       28, 54, 43, 36, 61,  1, 62, 45, 24, 25, 39, 35, 47, 15,  7],
      dtype=int64), 'cur_cost': 112071.0}, {'tour': array([61, 10,  6,  8, 57, 20, 24, 45, 54, 38, 17, 26,  0, 59, 44, 62, 12,
       19,  9, 28, 29, 35, 49, 14, 39, 18, 41, 52,  5, 42, 36, 43, 31, 37,
       22, 48, 30,  1,  7, 55, 15, 58, 33, 46, 25, 60,  3, 32, 56, 16, 23,
        2,  4, 63, 13, 40, 27, 47, 53, 51, 50, 64, 34, 65, 21, 11],
      dtype=int64), 'cur_cost': 108276.0}, {'tour': array([52, 57, 43, 24,  1,  8, 37, 54, 25, 12, 44, 21, 29, 48, 56, 13, 22,
       30, 61, 16, 27, 59, 42,  7, 17, 40, 46, 31, 18, 64, 49, 50, 35, 33,
        0,  6, 38,  2, 41, 62, 63, 14, 34, 26, 60, 58, 36, 19, 32, 23, 55,
       45, 53, 65, 15,  4, 39, 10,  5, 20,  3,  9, 11, 51, 47, 28],
      dtype=int64), 'cur_cost': 106751.0}]
2025-08-01 17:43:53,919 - ExploitationExpert - INFO - 局部搜索耗时: 0.15秒
2025-08-01 17:43:53,919 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-08-01 17:43:53,920 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([52, 57, 43, 24,  1,  8, 37, 54, 25, 12, 44, 21, 29, 48, 56, 13, 22,
       30, 61, 16, 27, 59, 42,  7, 17, 40, 46, 31, 18, 64, 49, 50, 35, 33,
        0,  6, 38,  2, 41, 62, 63, 14, 34, 26, 60, 58, 36, 19, 32, 23, 55,
       45, 53, 65, 15,  4, 39, 10,  5, 20,  3,  9, 11, 51, 47, 28],
      dtype=int64), 'cur_cost': 106751.0}
2025-08-01 17:43:53,921 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 35, 25, 31, 37, 16, 14, 6, 12, 23, 18, 34, 24, 4, 3, 7, 62, 2, 56, 58, 13, 19, 5, 1, 61, 8, 9, 0, 53, 59, 65, 10, 33, 17, 32, 43, 38, 20, 11, 29, 40, 46, 50, 22, 36, 15, 27, 39, 42, 41, 47, 26, 45, 30, 48, 28, 44, 55, 64, 57, 60, 52, 54, 63, 49, 51], 'cur_cost': 59863.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [21, 6, 2, 26, 64, 16, 14, 62, 18, 19, 22, 17, 25, 5, 3, 7, 11, 27, 56, 36, 13, 28, 41, 34, 61, 8, 24, 0, 53, 10, 65, 59, 29, 9, 32, 4, 38, 20, 55, 60, 40, 37, 1, 54, 58, 15, 44, 39, 42, 35, 47, 63, 45, 30, 48, 57, 51, 50, 31, 46, 43, 52, 12, 33, 49, 23], 'cur_cost': 100789.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [37, 34, 1, 2, 62, 6, 9, 0, 18, 19, 24, 36, 16, 12, 40, 50, 22, 29, 27, 4, 57, 54, 61, 11, 55, 56, 3, 64, 63, 7, 21, 14, 26, 32, 30, 10, 65, 49, 44, 41, 47, 38, 15, 20, 33, 35, 31, 8, 13, 39, 43, 17, 46, 25, 42, 23, 45, 51, 28, 58, 60, 59, 52, 53, 5, 48], 'cur_cost': 57823.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 41, 24, 65, 56,  2, 63, 12, 55, 28, 58, 43, 11, 25, 31, 23,  9,
       19,  7, 29, 27, 17, 39, 37, 10, 57, 35, 52, 32, 22, 46, 60, 30,  3,
       20, 45, 64, 47,  5, 14, 62, 38, 36, 54, 34, 48,  4, 42, 21, 15,  8,
       33, 51, 50,  1, 44, 16, 40, 13,  6, 53,  0, 18, 61, 49, 26],
      dtype=int64), 'cur_cost': 123298.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 31, 64, 6, 9, 16, 12, 18, 17, 14, 5, 8, 27, 11, 2, 58, 35, 19, 30, 56, 3, 29, 63, 34, 59, 26, 32, 43, 65, 49, 44, 61, 53, 46, 50, 25, 36, 60, 57, 13, 39, 52, 15, 45, 38, 40, 55, 48, 21, 47, 54, 24, 62, 42, 23, 22, 51, 41, 7, 10, 4, 33, 28, 37, 20], 'cur_cost': 95053.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 32, 46,  1, 54, 64, 26, 14,  0, 19, 24, 11, 53, 20, 50, 31, 49,
       34, 37, 15, 41, 44, 59, 56, 58,  7, 57, 30, 52, 13, 38, 23, 36,  9,
       65, 18, 39, 21, 60, 47, 10, 35,  5, 61, 40, 22, 43, 29, 55,  6, 12,
       51, 33,  3, 48,  4, 16, 45, 28, 17, 42, 62,  8, 63,  2, 27],
      dtype=int64), 'cur_cost': 104957.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 55, 57, 42, 50, 33,  8,  5, 58, 17, 22,  4, 40, 16,  3, 63, 56,
       51, 10, 12, 13,  6, 59, 27, 52, 21, 44, 48, 26, 18, 14,  9, 38, 65,
       46, 23, 31,  0,  2, 49, 20, 11, 37, 41, 53, 29, 64, 34, 32, 60, 19,
       28, 54, 43, 36, 61,  1, 62, 45, 24, 25, 39, 35, 47, 15,  7],
      dtype=int64), 'cur_cost': 112071.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 10,  6,  8, 57, 20, 24, 45, 54, 38, 17, 26,  0, 59, 44, 62, 12,
       19,  9, 28, 29, 35, 49, 14, 39, 18, 41, 52,  5, 42, 36, 43, 31, 37,
       22, 48, 30,  1,  7, 55, 15, 58, 33, 46, 25, 60,  3, 32, 56, 16, 23,
        2,  4, 63, 13, 40, 27, 47, 53, 51, 50, 64, 34, 65, 21, 11],
      dtype=int64), 'cur_cost': 108276.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 57, 43, 24,  1,  8, 37, 54, 25, 12, 44, 21, 29, 48, 56, 13, 22,
       30, 61, 16, 27, 59, 42,  7, 17, 40, 46, 31, 18, 64, 49, 50, 35, 33,
        0,  6, 38,  2, 41, 62, 63, 14, 34, 26, 60, 58, 36, 19, 32, 23, 55,
       45, 53, 65, 15,  4, 39, 10,  5, 20,  3,  9, 11, 51, 47, 28],
      dtype=int64), 'cur_cost': 106751.0}}]
2025-08-01 17:43:53,925 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-01 17:43:53,925 - StatsExpert - INFO - 开始统计分析
2025-08-01 17:43:53,929 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12388.000, 多样性=0.969
2025-08-01 17:43:53,930 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-01 17:43:53,930 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-01 17:43:53,930 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-01 17:43:53,933 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.2844106556905753, 'best_improvement': 0.7904600811907984}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.015063334474494948}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.6079206920867731, 'recent_improvements': [-0.11884214474616413, -0.09552929175547335, -1.3346835289197105], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 14, 'new_count': 14, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.7878787878787878, 'new_diversity': 0.7878787878787878, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-01 17:43:53,936 - main - INFO - --- Finished Evolution Iteration 5 ---
2025-08-01 17:43:53,954 - main - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-01 17:43:53,954 - main - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250801_174353.solution
2025-08-01 17:43:53,954 - main - INFO - 实例 composite13_66 处理完成
