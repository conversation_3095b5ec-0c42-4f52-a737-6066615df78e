2025-08-05 10:29:03,470 - __main__ - INFO - composite11_59 开始进化第 1 代
2025-08-05 10:29:03,470 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:03,471 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,474 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=25045.000, 多样性=0.945
2025-08-05 10:29:03,476 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:03,479 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.945
2025-08-05 10:29:03,481 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:03,484 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:03,484 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:03,484 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:03,484 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:03,504 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -39031.520, 聚类评分: 0.000, 覆盖率: 0.129, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:03,504 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:03,505 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:03,505 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 10:29:03,511 - visualization.landscape_visualizer - INFO - 插值约束: 3 个点被约束到最小值 25045.00
2025-08-05 10:29:03,512 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.3%, 梯度: 7867.93 → 7217.98
2025-08-05 10:29:03,611 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_111_20250805_102903.html
2025-08-05 10:29:03,669 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_111_20250805_102903.html
2025-08-05 10:29:03,669 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 111
2025-08-05 10:29:03,669 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:03,669 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1854秒
2025-08-05 10:29:03,669 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 222, 'max_size': 500, 'hits': 0, 'misses': 222, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 742, 'misses': 384, 'hit_rate': 0.6589698046181173, 'evictions': 284, 'ttl': 7200}}
2025-08-05 10:29:03,669 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -39031.52, 'local_optima_density': 0.1, 'gradient_variance': 13230454076.1776, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1289, 'fitness_entropy': 0.9372305632161296, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -39031.520)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.129)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360943.5043304, 'performance_metrics': {}}}
2025-08-05 10:29:03,670 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:03,670 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:03,670 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:03,670 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:03,672 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,672 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:03,672 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,673 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,673 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:03,673 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:29:03,673 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,673 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:03,673 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:03,674 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:03,674 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:03,674 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,677 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,678 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32636.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,678 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32636.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,679 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 32636.00)
2025-08-05 10:29:03,679 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:03,679 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:03,679 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,683 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 10:29:03,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 131725.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,684 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 131725.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,684 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 131725.00)
2025-08-05 10:29:03,684 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:03,685 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:03,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,688 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27056.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,689 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27056.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,689 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 27056.00)
2025-08-05 10:29:03,689 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:03,690 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:03,690 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,692 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27054.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,693 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27054.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,694 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 27054.00)
2025-08-05 10:29:03,694 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:03,694 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:03,694 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,697 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,697 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,698 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32574.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,698 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 32574.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,698 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 32574.00)
2025-08-05 10:29:03,698 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:03,698 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,698 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,699 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 214776.0
2025-08-05 10:29:03,709 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,709 - ExploitationExpert - INFO - res_population_costs: [24533.0, 24494, 24488, 24455]
2025-08-05 10:29:03,709 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64)]
2025-08-05 10:29:03,711 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,711 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32636.0}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 131725.0}, {'tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27056.0}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27054.0}, {'tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 32574.0}, {'tour': array([20, 50, 57, 16, 45, 28, 12,  5, 56,  9, 11, 54, 18, 19, 51, 15,  0,
       29, 27, 41,  1, 39, 38, 46, 52, 34,  8, 49, 42, 40,  2, 25,  7, 32,
       24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36,  6,
        4,  3, 37, 35, 31, 44, 14, 26], dtype=int64), 'cur_cost': 214776.0}, {'tour': array([55, 38, 51, 34, 12, 28, 11, 43, 24, 46, 48, 39, 58, 42, 26, 45, 13,
       15, 16, 20, 23, 57, 25, 29,  0, 47, 21,  5,  2,  9, 41, 56, 27, 18,
       19, 50, 44, 14,  1, 54,  3,  7,  6, 49, 17, 36, 53,  4, 32, 30, 35,
       31, 40, 52, 10, 33, 37, 22,  8], dtype=int64), 'cur_cost': 259757.0}, {'tour': array([17,  3, 53, 21, 35, 54, 15,  5, 42, 14, 39, 16, 58, 33, 27, 13, 22,
        7, 49, 20,  4, 46, 12, 55, 48, 56, 44, 23, 26, 25, 47, 43, 41,  0,
       50, 38, 37, 57, 51, 19, 11, 52, 45, 31, 29, 28,  9, 32, 18,  6,  8,
       10, 36,  1, 34, 40, 24,  2, 30], dtype=int64), 'cur_cost': 230109.0}, {'tour': array([34, 26, 45, 51, 37, 55, 20, 32,  5, 17,  9,  2,  3, 18, 39, 33, 57,
       28, 48, 54,  7,  0, 43, 56, 35,  1, 23, 24,  4, 44, 30, 27, 49, 21,
       31,  8, 14,  6, 42, 22, 19, 36, 16, 29, 10, 53, 50, 40, 15, 47, 12,
       13, 52, 41, 46, 25, 58, 11, 38], dtype=int64), 'cur_cost': 257091.0}, {'tour': array([30, 48, 36, 51, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 273344.0}]
2025-08-05 10:29:03,714 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:03,715 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 287, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 287, 'cache_hits': 0, 'similarity_calculations': 1434, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,716 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([20, 50, 57, 16, 45, 28, 12,  5, 56,  9, 11, 54, 18, 19, 51, 15,  0,
       29, 27, 41,  1, 39, 38, 46, 52, 34,  8, 49, 42, 40,  2, 25,  7, 32,
       24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36,  6,
        4,  3, 37, 35, 31, 44, 14, 26], dtype=int64), 'cur_cost': 214776.0, 'intermediate_solutions': [{'tour': array([11, 22, 35, 33, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 282592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 11, 22, 35, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 278329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 33, 11, 22, 35, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 278322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([35, 33, 11, 22, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 270529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([35, 49, 33, 11, 22, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 270864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,717 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 214776.00)
2025-08-05 10:29:03,717 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:03,717 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:03,717 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,726 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 10:29:03,726 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,727 - ExplorationExpert - INFO - 探索路径生成完成，成本: 142711.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,727 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [25, 5, 28, 23, 3, 10, 2, 21, 30, 42, 26, 34, 44, 32, 24, 38, 0, 19, 33, 39, 4, 8, 31, 36, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 142711.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,727 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 142711.00)
2025-08-05 10:29:03,727 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:03,727 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:03,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,729 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,729 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,730 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32630.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,730 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32630.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,730 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 32630.00)
2025-08-05 10:29:03,730 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:03,730 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:03,730 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,732 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 10:29:03,732 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,732 - ExplorationExpert - INFO - 探索路径生成完成，成本: 218727.0, 路径长度: 59, 收集中间解: 0
2025-08-05 10:29:03,733 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [17, 13, 8, 22, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53, 33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10, 9, 30, 21, 48, 15, 52, 56, 0, 35, 49, 23, 14, 2, 41, 58, 45, 25, 5, 6, 18, 4, 3, 16, 20, 7, 1], 'cur_cost': 218727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,733 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 218727.00)
2025-08-05 10:29:03,733 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:03,733 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,734 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,734 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 225708.0
2025-08-05 10:29:03,748 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:03,748 - ExploitationExpert - INFO - res_population_costs: [24533.0, 24494, 24488, 24455]
2025-08-05 10:29:03,748 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64)]
2025-08-05 10:29:03,750 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:03,750 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32636.0}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 131725.0}, {'tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27056.0}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27054.0}, {'tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 32574.0}, {'tour': array([20, 50, 57, 16, 45, 28, 12,  5, 56,  9, 11, 54, 18, 19, 51, 15,  0,
       29, 27, 41,  1, 39, 38, 46, 52, 34,  8, 49, 42, 40,  2, 25,  7, 32,
       24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36,  6,
        4,  3, 37, 35, 31, 44, 14, 26], dtype=int64), 'cur_cost': 214776.0}, {'tour': [25, 5, 28, 23, 3, 10, 2, 21, 30, 42, 26, 34, 44, 32, 24, 38, 0, 19, 33, 39, 4, 8, 31, 36, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 142711.0}, {'tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32630.0}, {'tour': [17, 13, 8, 22, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53, 33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10, 9, 30, 21, 48, 15, 52, 56, 0, 35, 49, 23, 14, 2, 41, 58, 45, 25, 5, 6, 18, 4, 3, 16, 20, 7, 1], 'cur_cost': 218727.0}, {'tour': array([31, 45, 46,  9,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51], dtype=int64), 'cur_cost': 225708.0}]
2025-08-05 10:29:03,752 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:03,752 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 288, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 288, 'cache_hits': 0, 'similarity_calculations': 1435, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:03,753 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([31, 45, 46,  9,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51], dtype=int64), 'cur_cost': 225708.0, 'intermediate_solutions': [{'tour': array([36, 48, 30, 51, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 270115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 36, 48, 30, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 266932.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 51, 36, 48, 30, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 273397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 51, 36, 48, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 273344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 33, 51, 36, 48, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 266569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:03,753 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 225708.00)
2025-08-05 10:29:03,753 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:03,753 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:03,755 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32636.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 131725.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27056.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27054.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 32574.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 50, 57, 16, 45, 28, 12,  5, 56,  9, 11, 54, 18, 19, 51, 15,  0,
       29, 27, 41,  1, 39, 38, 46, 52, 34,  8, 49, 42, 40,  2, 25,  7, 32,
       24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36,  6,
        4,  3, 37, 35, 31, 44, 14, 26], dtype=int64), 'cur_cost': 214776.0, 'intermediate_solutions': [{'tour': array([11, 22, 35, 33, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 282592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33, 11, 22, 35, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 278329.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 33, 11, 22, 35, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 278322.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([35, 33, 11, 22, 49, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 270529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([35, 49, 33, 11, 22, 30, 17,  5,  3, 34, 58,  1, 21, 12, 26, 32, 52,
       24,  6, 51,  8, 45, 47, 25,  4, 29,  7, 50, 28, 53, 13, 42, 37, 40,
       20, 16,  9, 56, 27, 15, 41, 14, 18, 44, 57, 36, 48,  0, 54, 43, 19,
       38, 10, 23, 55, 46, 31,  2, 39], dtype=int64), 'cur_cost': 270864.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [25, 5, 28, 23, 3, 10, 2, 21, 30, 42, 26, 34, 44, 32, 24, 38, 0, 19, 33, 39, 4, 8, 31, 36, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 142711.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32630.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [17, 13, 8, 22, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53, 33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10, 9, 30, 21, 48, 15, 52, 56, 0, 35, 49, 23, 14, 2, 41, 58, 45, 25, 5, 6, 18, 4, 3, 16, 20, 7, 1], 'cur_cost': 218727.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([31, 45, 46,  9,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51], dtype=int64), 'cur_cost': 225708.0, 'intermediate_solutions': [{'tour': array([36, 48, 30, 51, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 270115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 36, 48, 30, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 266932.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([33, 51, 36, 48, 30, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 273397.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 51, 36, 48, 33, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 273344.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 33, 51, 36, 48, 22, 54, 31, 10, 17,  7, 55, 43, 15, 41, 14, 46,
        6, 25,  9, 53, 34,  3, 42,  0,  4, 37, 56, 45, 35, 47, 52,  2, 21,
       38, 44, 11,  8, 28, 23, 18, 40, 29, 24, 57, 26,  5, 16, 12, 49, 20,
       58, 13, 50,  1, 32, 27, 19, 39], dtype=int64), 'cur_cost': 266569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:03,755 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:03,755 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,760 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27054.000, 多样性=0.942
2025-08-05 10:29:03,760 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:03,760 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:03,760 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:03,760 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.17312795971049535, 'best_improvement': -0.08021561189858255}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0035856573705178715}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.16920092412652876, 'recent_improvements': [-0.2764316519785348, 0.11152101591674986, 0.06197019627452271], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 24455, 'new_best_cost': 24455, 'quality_improvement': 0.0, 'old_diversity': 0.7062146892655367, 'new_diversity': 0.7062146892655367, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:03,761 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:03,761 - __main__ - INFO - composite11_59 开始进化第 2 代
2025-08-05 10:29:03,761 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:03,761 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:03,762 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27054.000, 多样性=0.942
2025-08-05 10:29:03,762 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:03,765 - PathExpert - INFO - 路径结构分析完成: 公共边数量=21, 路径相似性=0.942
2025-08-05 10:29:03,766 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:03,767 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.706
2025-08-05 10:29:03,770 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:03,770 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:03,770 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:03,770 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:03,809 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -33866.443, 聚类评分: 0.000, 覆盖率: 0.130, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:03,809 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:03,810 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:03,810 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 10:29:03,814 - visualization.landscape_visualizer - INFO - 插值约束: 61 个点被约束到最小值 24455.00
2025-08-05 10:29:03,815 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.6%, 梯度: 6147.97 → 5680.32
2025-08-05 10:29:03,910 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_112_20250805_102903.html
2025-08-05 10:29:03,961 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_112_20250805_102903.html
2025-08-05 10:29:03,961 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 112
2025-08-05 10:29:03,961 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:03,961 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1905秒
2025-08-05 10:29:03,961 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -33866.442857142865, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 4480752571.915306, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1302, 'fitness_entropy': 0.8120406807941002, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -33866.443)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.130)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360943.80969, 'performance_metrics': {}}}
2025-08-05 10:29:03,962 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:03,962 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:03,962 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:03,962 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:03,963 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:03,963 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:03,963 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:03,963 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:03,963 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:03,963 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:03,963 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,965 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,965 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,966 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,966 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27048.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,966 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27048.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 2, 3, 7, 4, 9, 10, 6, 5, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 8, 7, 6, 10, 9, 4, 5, 3, 2, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 38278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 29, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 47249.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,967 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 27048.00)
2025-08-05 10:29:03,967 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:03,967 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:03,967 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,969 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,969 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27116.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,970 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27116.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 20, 55, 57, 49, 54, 47, 50, 25, 52, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 147173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 2, 17, 23, 45, 58, 41, 13, 18, 14, 33, 44, 35, 0, 12, 22, 15, 34, 21, 30, 9, 10, 31, 11, 16, 19, 3, 5, 56, 7], 'cur_cost': 133852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7, 47], 'cur_cost': 139379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,970 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 27116.00)
2025-08-05 10:29:03,970 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:03,970 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:03,971 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,972 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,973 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32464.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,973 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32464.0, 'intermediate_solutions': [{'tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 25, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 17, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47505.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 5, 8, 7, 1, 6, 19, 20, 16, 17, 15, 11, 12, 21, 22, 18, 13, 14, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27016.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 0, 54, 50, 47], 'cur_cost': 32524.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,974 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 32464.00)
2025-08-05 10:29:03,974 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:03,974 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:03,974 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,975 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,976 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,976 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32496.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,976 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32496.0, 'intermediate_solutions': [{'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 3, 15, 19, 6, 1, 7, 8, 5, 11, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42346.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 45, 42, 36, 43, 37, 44, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 9, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,977 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 32496.00)
2025-08-05 10:29:03,977 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:03,977 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:03,977 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,981 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 10:29:03,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,982 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,982 - ExplorationExpert - INFO - 探索路径生成完成，成本: 183259.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,982 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [51, 53, 48, 49, 6, 21, 2, 18, 13, 17, 7, 11, 27, 4, 26, 24, 44, 30, 32, 37, 8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28, 35, 29, 0, 14, 9, 50, 54, 55, 52, 3, 57, 19, 12, 5, 1, 56, 20, 58, 43, 41, 38, 47, 22, 15, 40], 'cur_cost': 183259.0, 'intermediate_solutions': [{'tour': [0, 43, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 14, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 55735.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 2, 6, 1, 7, 8, 5, 3, 4, 9, 10, 18, 14, 0, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 40331.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 23, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 47182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,982 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 183259.00)
2025-08-05 10:29:03,982 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:03,982 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:03,982 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,986 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 10:29:03,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,987 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,987 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157571.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,987 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157571.0, 'intermediate_solutions': [{'tour': [20, 50, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 15, 51, 0, 29, 27, 41, 1, 39, 38, 46, 52, 34, 8, 49, 42, 40, 2, 25, 7, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 209382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 50, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 51, 15, 0, 29, 27, 41, 7, 25, 2, 40, 42, 49, 8, 34, 52, 46, 38, 39, 1, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 214758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 50, 29, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 51, 15, 0, 27, 41, 1, 39, 38, 46, 52, 34, 8, 49, 42, 40, 2, 25, 7, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 229280.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,988 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 157571.00)
2025-08-05 10:29:03,988 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:03,988 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:03,988 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,989 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,990 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32419.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,990 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32419.0, 'intermediate_solutions': [{'tour': [42, 5, 28, 23, 3, 10, 2, 21, 30, 25, 26, 34, 44, 32, 24, 38, 0, 19, 33, 39, 4, 8, 31, 36, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 138277.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 5, 22, 47, 55, 11, 13, 16, 12, 15, 9, 1, 49, 51, 56, 54, 57, 58, 52, 53, 36, 31, 8, 4, 39, 33, 19, 0, 38, 24, 32, 44, 34, 26, 42, 30, 21, 2, 10, 3, 23, 28, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 142737.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 5, 28, 23, 3, 10, 2, 21, 30, 42, 26, 34, 44, 32, 38, 0, 19, 33, 39, 4, 8, 31, 36, 24, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 149341.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,991 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 32419.00)
2025-08-05 10:29:03,991 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:03,991 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:03,991 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:03,992 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:03,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,993 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:03,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27086.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:03,994 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27086.0, 'intermediate_solutions': [{'tour': [0, 16, 7, 2, 3, 11, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 5, 12, 21, 19], 'cur_cost': 47873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 51, 57, 55, 56, 52, 58, 48, 53, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32736.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 48, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 47194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:03,994 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 27086.00)
2025-08-05 10:29:03,994 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:03,994 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:03,994 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:03,994 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 244722.0
2025-08-05 10:29:04,000 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:04,000 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0]
2025-08-05 10:29:04,000 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 10:29:04,001 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,001 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27048.0}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27116.0}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32464.0}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32496.0}, {'tour': [51, 53, 48, 49, 6, 21, 2, 18, 13, 17, 7, 11, 27, 4, 26, 24, 44, 30, 32, 37, 8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28, 35, 29, 0, 14, 9, 50, 54, 55, 52, 3, 57, 19, 12, 5, 1, 56, 20, 58, 43, 41, 38, 47, 22, 15, 40], 'cur_cost': 183259.0}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157571.0}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32419.0}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27086.0}, {'tour': array([ 7, 50, 27, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46], dtype=int64), 'cur_cost': 244722.0}, {'tour': [31, 45, 46, 9, 4, 11, 1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26, 54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58, 8, 18, 50, 41, 36, 49, 15, 2, 5, 28, 35, 57, 48, 39, 25, 22, 34, 3, 16, 17, 32, 19, 6, 7, 0, 14, 38, 51], 'cur_cost': 225708.0}]
2025-08-05 10:29:04,003 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,003 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 289, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 289, 'cache_hits': 0, 'similarity_calculations': 1437, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,004 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 7, 50, 27, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46], dtype=int64), 'cur_cost': 244722.0, 'intermediate_solutions': [{'tour': array([ 8, 13, 17, 22, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 211189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22,  8, 13, 17, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 218691.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 22,  8, 13, 17, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 223518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 22,  8, 13, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 218707.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 32, 22,  8, 13, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 228951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,004 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 244722.00)
2025-08-05 10:29:04,004 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:04,005 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,005 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,005 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 262202.0
2025-08-05 10:29:04,015 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:04,016 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0]
2025-08-05 10:29:04,016 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 10:29:04,018 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,018 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27048.0}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27116.0}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32464.0}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32496.0}, {'tour': [51, 53, 48, 49, 6, 21, 2, 18, 13, 17, 7, 11, 27, 4, 26, 24, 44, 30, 32, 37, 8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28, 35, 29, 0, 14, 9, 50, 54, 55, 52, 3, 57, 19, 12, 5, 1, 56, 20, 58, 43, 41, 38, 47, 22, 15, 40], 'cur_cost': 183259.0}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157571.0}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32419.0}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27086.0}, {'tour': array([ 7, 50, 27, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46], dtype=int64), 'cur_cost': 244722.0}, {'tour': array([54,  4, 32, 43, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19], dtype=int64), 'cur_cost': 262202.0}]
2025-08-05 10:29:04,019 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,019 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 290, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 290, 'cache_hits': 0, 'similarity_calculations': 1440, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,020 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([54,  4, 32, 43, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19], dtype=int64), 'cur_cost': 262202.0, 'intermediate_solutions': [{'tour': array([46, 45, 31,  9,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 223611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 46, 45, 31,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 225708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  9, 46, 45, 31, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 223520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  9, 46, 45,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 228088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31,  4,  9, 46, 45, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 228101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,021 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 262202.00)
2025-08-05 10:29:04,021 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:04,021 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:04,024 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27048.0, 'intermediate_solutions': [{'tour': [0, 1, 15, 2, 3, 7, 4, 9, 10, 6, 5, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32707.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 15, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 8, 7, 6, 10, 9, 4, 5, 3, 2, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 38278.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 15, 2, 3, 5, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 29, 47, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 47249.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27116.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 20, 55, 57, 49, 54, 47, 50, 25, 52, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7], 'cur_cost': 147173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 47, 50, 25, 20, 26, 51, 2, 17, 23, 45, 58, 41, 13, 18, 14, 33, 44, 35, 0, 12, 22, 15, 34, 21, 30, 9, 10, 31, 11, 16, 19, 3, 5, 56, 7], 'cur_cost': 133852.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 6, 8, 28, 32, 29, 24, 27, 46, 37, 43, 36, 42, 38, 40, 39, 53, 48, 52, 55, 57, 49, 54, 50, 25, 20, 26, 51, 19, 16, 11, 31, 10, 9, 30, 21, 34, 15, 22, 12, 0, 35, 44, 33, 14, 18, 13, 41, 58, 45, 23, 17, 2, 3, 5, 56, 7, 47], 'cur_cost': 139379.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32464.0, 'intermediate_solutions': [{'tour': [0, 14, 13, 18, 22, 21, 12, 11, 15, 25, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 17, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47505.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 5, 8, 7, 1, 6, 19, 20, 16, 17, 15, 11, 12, 21, 22, 18, 13, 14, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27016.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 13, 18, 22, 21, 12, 11, 15, 17, 16, 20, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 0, 54, 50, 47], 'cur_cost': 32524.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32496.0, 'intermediate_solutions': [{'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 3, 15, 19, 6, 1, 7, 8, 5, 11, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 42346.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 45, 42, 36, 43, 37, 44, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 19, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 9, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33943.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [51, 53, 48, 49, 6, 21, 2, 18, 13, 17, 7, 11, 27, 4, 26, 24, 44, 30, 32, 37, 8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28, 35, 29, 0, 14, 9, 50, 54, 55, 52, 3, 57, 19, 12, 5, 1, 56, 20, 58, 43, 41, 38, 47, 22, 15, 40], 'cur_cost': 183259.0, 'intermediate_solutions': [{'tour': [0, 43, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 14, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 55735.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 2, 6, 1, 7, 8, 5, 3, 4, 9, 10, 18, 14, 0, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 40331.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 18, 10, 9, 4, 3, 5, 8, 7, 1, 6, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 23, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 21, 12, 11, 15, 17, 16, 20, 19], 'cur_cost': 47182.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157571.0, 'intermediate_solutions': [{'tour': [20, 50, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 15, 51, 0, 29, 27, 41, 1, 39, 38, 46, 52, 34, 8, 49, 42, 40, 2, 25, 7, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 209382.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 50, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 51, 15, 0, 29, 27, 41, 7, 25, 2, 40, 42, 49, 8, 34, 52, 46, 38, 39, 1, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 214758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 50, 29, 57, 16, 45, 28, 12, 5, 56, 9, 11, 54, 18, 19, 51, 15, 0, 27, 41, 1, 39, 38, 46, 52, 34, 8, 49, 42, 40, 2, 25, 7, 32, 24, 47, 22, 13, 53, 58, 48, 33, 10, 21, 17, 55, 30, 23, 43, 36, 6, 4, 3, 37, 35, 31, 44, 14, 26], 'cur_cost': 229280.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32419.0, 'intermediate_solutions': [{'tour': [42, 5, 28, 23, 3, 10, 2, 21, 30, 25, 26, 34, 44, 32, 24, 38, 0, 19, 33, 39, 4, 8, 31, 36, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 138277.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [25, 5, 22, 47, 55, 11, 13, 16, 12, 15, 9, 1, 49, 51, 56, 54, 57, 58, 52, 53, 36, 31, 8, 4, 39, 33, 19, 0, 38, 24, 32, 44, 34, 26, 42, 30, 21, 2, 10, 3, 23, 28, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 142737.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [25, 5, 28, 23, 3, 10, 2, 21, 30, 42, 26, 34, 44, 32, 38, 0, 19, 33, 39, 4, 8, 31, 36, 24, 53, 52, 58, 57, 54, 56, 51, 49, 1, 9, 15, 12, 16, 13, 11, 55, 47, 22, 6, 7, 17, 20, 48, 18, 50, 14, 46, 35, 37, 45, 41, 43, 40, 29, 27], 'cur_cost': 149341.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27086.0, 'intermediate_solutions': [{'tour': [0, 16, 7, 2, 3, 11, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 5, 12, 21, 19], 'cur_cost': 47873.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 51, 57, 55, 56, 52, 58, 48, 53, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 32736.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 7, 2, 3, 5, 4, 9, 10, 6, 1, 8, 28, 48, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 17, 15, 11, 12, 21, 19], 'cur_cost': 47194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 50, 27, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46], dtype=int64), 'cur_cost': 244722.0, 'intermediate_solutions': [{'tour': array([ 8, 13, 17, 22, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 211189.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22,  8, 13, 17, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 218691.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 22,  8, 13, 17, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 223518.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([17, 22,  8, 13, 32, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 218707.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([17, 32, 22,  8, 13, 29, 11, 27, 12, 37, 43, 36, 28, 38, 40, 39, 53,
       33, 24, 55, 57, 34, 54, 47, 50, 46, 44, 26, 51, 19, 42, 31, 10,  9,
       30, 21, 48, 15, 52, 56,  0, 35, 49, 23, 14,  2, 41, 58, 45, 25,  5,
        6, 18,  4,  3, 16, 20,  7,  1]), 'cur_cost': 228951.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([54,  4, 32, 43, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19], dtype=int64), 'cur_cost': 262202.0, 'intermediate_solutions': [{'tour': array([46, 45, 31,  9,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 223611.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 46, 45, 31,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 225708.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  9, 46, 45, 31, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 223520.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  9, 46, 45,  4, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 228088.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31,  4,  9, 46, 45, 11,  1, 47, 53, 12, 27, 13, 30, 10, 40, 44, 26,
       54, 56, 33, 42, 29, 23, 37, 55, 43, 24, 20, 21, 52, 58,  8, 18, 50,
       41, 36, 49, 15,  2,  5, 28, 35, 57, 48, 39, 25, 22, 34,  3, 16, 17,
       32, 19,  6,  7,  0, 14, 38, 51]), 'cur_cost': 228101.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:04,025 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:04,025 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,032 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27048.000, 多样性=0.907
2025-08-05 10:29:04,032 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:04,032 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:04,032 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:04,033 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03563996655105198, 'best_improvement': 0.00022177866489243733}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.037584966013594616}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.030803471896872752, 'recent_improvements': [0.11152101591674986, 0.06197019627452271, 0.17312795971049535], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 24455, 'new_best_cost': 24455, 'quality_improvement': 0.0, 'old_diversity': 0.7062146892655367, 'new_diversity': 0.7062146892655367, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:04,033 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:04,033 - __main__ - INFO - composite11_59 开始进化第 3 代
2025-08-05 10:29:04,033 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:04,033 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,034 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=27048.000, 多样性=0.907
2025-08-05 10:29:04,035 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:04,038 - PathExpert - INFO - 路径结构分析完成: 公共边数量=42, 路径相似性=0.907
2025-08-05 10:29:04,038 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:04,040 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.706
2025-08-05 10:29:04,041 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:04,041 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:04,042 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:04,042 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:04,089 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -25654.514, 聚类评分: 0.000, 覆盖率: 0.131, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:04,089 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:04,089 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:04,089 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 10:29:04,096 - visualization.landscape_visualizer - INFO - 插值约束: 125 个点被约束到最小值 24455.00
2025-08-05 10:29:04,098 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.9%, 梯度: 8458.92 → 7789.20
2025-08-05 10:29:04,215 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_113_20250805_102904.html
2025-08-05 10:29:04,291 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_113_20250805_102904.html
2025-08-05 10:29:04,291 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 113
2025-08-05 10:29:04,291 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:04,291 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2494秒
2025-08-05 10:29:04,291 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -25654.51428571429, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 7341788291.786939, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1311, 'fitness_entropy': 0.6458459985690297, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -25654.514)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.131)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360944.0894938, 'performance_metrics': {}}}
2025-08-05 10:29:04,291 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:04,291 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:04,292 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:04,292 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:04,292 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,292 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:04,292 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,292 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:04,292 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:04,293 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,293 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:04,293 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:04,293 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:04,293 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:04,293 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:04,293 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 119419.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,296 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 119419.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 50, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 32, 47], 'cur_cost': 56246.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 26, 28, 10, 2, 3, 9, 4, 8, 7, 1, 6, 19, 21, 11, 15, 17, 16, 20, 14, 22, 13, 18, 12, 5, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 5, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,296 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 119419.00)
2025-08-05 10:29:04,296 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:04,296 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:04,296 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,298 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,299 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24958.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,299 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24958.0, 'intermediate_solutions': [{'tour': [0, 9, 20, 13, 22, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27135.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 53, 39, 40, 38, 41, 45, 42, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 38375.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 46, 23, 33, 35, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,299 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 24958.00)
2025-08-05 10:29:04,299 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:04,299 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:04,299 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,301 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27144.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,302 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27144.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 1, 7, 8, 5, 3, 2, 17, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 15, 16, 20, 14, 22, 18, 13, 47, 11, 12, 21, 19], 'cur_cost': 43630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 14, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 40104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,302 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 27144.00)
2025-08-05 10:29:04,302 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:04,302 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:04,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,304 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,305 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32484.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,305 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32484.0, 'intermediate_solutions': [{'tour': [38, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 0, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 48126.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 43831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 24, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 47082.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,305 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 32484.00)
2025-08-05 10:29:04,305 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:04,305 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,305 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,306 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 247695.0
2025-08-05 10:29:04,312 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:04,313 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0]
2025-08-05 10:29:04,313 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 10:29:04,315 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,315 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 119419.0}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24958.0}, {'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27144.0}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32484.0}, {'tour': array([44, 57, 14, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20], dtype=int64), 'cur_cost': 247695.0}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157571.0}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 32419.0}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27086.0}, {'tour': [7, 50, 27, 29, 26, 48, 37, 45, 8, 42, 32, 54, 35, 40, 28, 43, 6, 12, 44, 21, 17, 36, 23, 51, 52, 2, 0, 31, 18, 53, 49, 56, 39, 11, 25, 3, 15, 58, 55, 4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33, 5, 13, 34, 1, 9, 16, 57, 46], 'cur_cost': 244722.0}, {'tour': [54, 4, 32, 43, 36, 0, 53, 45, 31, 3, 5, 56, 42, 28, 14, 11, 30, 50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58, 1, 8, 49, 26, 21, 38, 18, 51, 22, 6, 47, 7, 27, 33, 12, 16, 15, 24, 57, 9, 40, 44, 2, 46, 20, 37, 19], 'cur_cost': 262202.0}]
2025-08-05 10:29:04,315 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,316 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 291, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 291, 'cache_hits': 0, 'similarity_calculations': 1444, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,316 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([44, 57, 14, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20], dtype=int64), 'cur_cost': 247695.0, 'intermediate_solutions': [{'tour': array([48, 53, 51, 49,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49, 48, 53, 51,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183256.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 49, 48, 53, 51, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183275.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 49, 48, 53,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  6, 49, 48, 53, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 188901.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,317 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 247695.00)
2025-08-05 10:29:04,317 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:04,317 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:04,317 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,318 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 10:29:04,318 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,319 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,319 - ExplorationExpert - INFO - 探索路径生成完成，成本: 144730.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,319 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144730.0, 'intermediate_solutions': [{'tour': [49, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 51, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157587.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 39, 31, 42, 44, 46, 35, 1, 55, 52, 21, 48, 20, 56, 22, 49, 12, 8, 24, 37, 45, 32, 13, 10, 28, 33, 30, 5, 23, 25, 29, 34, 26, 16, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 165261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 54, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 11], 'cur_cost': 165202.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,320 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 144730.00)
2025-08-05 10:29:04,320 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:04,320 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:04,320 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,322 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 59
2025-08-05 10:29:04,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,323 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162692.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,324 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162692.0, 'intermediate_solutions': [{'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 50, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 38, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 54919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 15, 17, 16, 20, 14, 22, 18, 11, 21, 19], 'cur_cost': 32551.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 38, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 42301.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,324 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 162692.00)
2025-08-05 10:29:04,324 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:04,324 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:04,324 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,327 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24873.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,328 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24873.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 30, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 20, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47619.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 41677.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 5, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 29179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,328 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 24873.00)
2025-08-05 10:29:04,328 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:04,328 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,328 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,329 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 267655.0
2025-08-05 10:29:04,338 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:04,339 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0]
2025-08-05 10:29:04,339 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 10:29:04,341 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,341 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 119419.0}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24958.0}, {'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27144.0}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32484.0}, {'tour': array([44, 57, 14, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20], dtype=int64), 'cur_cost': 247695.0}, {'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144730.0}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162692.0}, {'tour': [0, 2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24873.0}, {'tour': array([55, 38, 11, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25], dtype=int64), 'cur_cost': 267655.0}, {'tour': [54, 4, 32, 43, 36, 0, 53, 45, 31, 3, 5, 56, 42, 28, 14, 11, 30, 50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58, 1, 8, 49, 26, 21, 38, 18, 51, 22, 6, 47, 7, 27, 33, 12, 16, 15, 24, 57, 9, 40, 44, 2, 46, 20, 37, 19], 'cur_cost': 262202.0}]
2025-08-05 10:29:04,342 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,342 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 292, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 292, 'cache_hits': 0, 'similarity_calculations': 1449, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,343 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([55, 38, 11, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25], dtype=int64), 'cur_cost': 267655.0, 'intermediate_solutions': [{'tour': array([27, 50,  7, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 249161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 27, 50,  7, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 249100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 29, 27, 50,  7, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 242248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 29, 27, 50, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 251594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 26, 29, 27, 50, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 237059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,343 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 267655.00)
2025-08-05 10:29:04,343 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:04,343 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,344 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,344 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 238969.0
2025-08-05 10:29:04,350 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:29:04,350 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0]
2025-08-05 10:29:04,350 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64)]
2025-08-05 10:29:04,353 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,353 - ExploitationExpert - INFO - populations: [{'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 119419.0}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24958.0}, {'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27144.0}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32484.0}, {'tour': array([44, 57, 14, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20], dtype=int64), 'cur_cost': 247695.0}, {'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144730.0}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162692.0}, {'tour': [0, 2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24873.0}, {'tour': array([55, 38, 11, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25], dtype=int64), 'cur_cost': 267655.0}, {'tour': array([30, 27, 29, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18], dtype=int64), 'cur_cost': 238969.0}]
2025-08-05 10:29:04,354 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,354 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 293, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 293, 'cache_hits': 0, 'similarity_calculations': 1455, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,355 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([30, 27, 29, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18], dtype=int64), 'cur_cost': 238969.0, 'intermediate_solutions': [{'tour': array([32,  4, 54, 43, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 262429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 32,  4, 54, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 269962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([36, 43, 32,  4, 54,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 264316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 43, 32,  4, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 267885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 36, 43, 32,  4,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 260119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,355 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 238969.00)
2025-08-05 10:29:04,355 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:04,355 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:04,358 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 119419.0, 'intermediate_solutions': [{'tour': [0, 5, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 50, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 32, 47], 'cur_cost': 56246.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 26, 28, 10, 2, 3, 9, 4, 8, 7, 1, 6, 19, 21, 11, 15, 17, 16, 20, 14, 22, 13, 18, 12, 5, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33835.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 18, 13, 22, 14, 20, 16, 17, 15, 11, 21, 19, 6, 1, 7, 8, 4, 9, 3, 2, 10, 28, 26, 30, 25, 5, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 33954.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24958.0, 'intermediate_solutions': [{'tour': [0, 9, 20, 13, 22, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27135.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 53, 39, 40, 38, 41, 45, 42, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 38375.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 20, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 14, 6, 1, 7, 8, 5, 3, 2, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 46, 23, 33, 35, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 37017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27144.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 1, 7, 8, 5, 3, 2, 17, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 32456.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 15, 16, 20, 14, 22, 18, 13, 47, 11, 12, 21, 19], 'cur_cost': 43630.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 17, 1, 7, 8, 5, 3, 2, 14, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 40104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 32484.0, 'intermediate_solutions': [{'tour': [38, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 0, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 48126.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 43831.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 17, 8, 5, 3, 2, 9, 4, 1, 7, 6, 10, 28, 26, 30, 25, 32, 29, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 24, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 15, 11, 12, 21, 19], 'cur_cost': 47082.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 57, 14, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20], dtype=int64), 'cur_cost': 247695.0, 'intermediate_solutions': [{'tour': array([48, 53, 51, 49,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183177.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([49, 48, 53, 51,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183256.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 49, 48, 53, 51, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183275.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 49, 48, 53,  6, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 183232.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  6, 49, 48, 53, 21,  2, 18, 13, 17,  7, 11, 27,  4, 26, 24, 44,
       30, 32, 37,  8, 25, 33, 42, 36, 31, 45, 39, 10, 16, 34, 46, 23, 28,
       35, 29,  0, 14,  9, 50, 54, 55, 52,  3, 57, 19, 12,  5,  1, 56, 20,
       58, 43, 41, 38, 47, 22, 15, 40]), 'cur_cost': 188901.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144730.0, 'intermediate_solutions': [{'tour': [49, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 51, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 157587.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 7, 19, 17, 39, 31, 42, 44, 46, 35, 1, 55, 52, 21, 48, 20, 56, 22, 49, 12, 8, 24, 37, 45, 32, 13, 10, 28, 33, 30, 5, 23, 25, 29, 34, 26, 16, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 54, 11], 'cur_cost': 165261.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 57, 47, 6, 9, 18, 0, 3, 2, 27, 4, 54, 7, 19, 17, 16, 26, 34, 29, 25, 23, 5, 30, 33, 28, 10, 13, 32, 45, 37, 24, 8, 12, 49, 22, 56, 20, 48, 21, 52, 55, 1, 35, 46, 44, 42, 31, 39, 50, 58, 14, 15, 43, 38, 36, 41, 40, 53, 11], 'cur_cost': 165202.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162692.0, 'intermediate_solutions': [{'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 50, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 38, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 54919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 15, 17, 16, 20, 14, 22, 18, 11, 21, 19], 'cur_cost': 32551.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 12, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 38, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 21, 19], 'cur_cost': 42301.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24873.0, 'intermediate_solutions': [{'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 30, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 20, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 47619.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 5, 8, 2, 10, 28, 26, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 41677.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 7, 22, 13, 18, 19, 21, 12, 11, 15, 17, 16, 20, 14, 6, 4, 9, 3, 8, 2, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 5, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 29179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 38, 11, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25], dtype=int64), 'cur_cost': 267655.0, 'intermediate_solutions': [{'tour': array([27, 50,  7, 29, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 249161.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 27, 50,  7, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 249100.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 29, 27, 50,  7, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 242248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7, 29, 27, 50, 26, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 251594.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 26, 29, 27, 50, 48, 37, 45,  8, 42, 32, 54, 35, 40, 28, 43,  6,
       12, 44, 21, 17, 36, 23, 51, 52,  2,  0, 31, 18, 53, 49, 56, 39, 11,
       25,  3, 15, 58, 55,  4, 22, 14, 47, 24, 20, 38, 10, 41, 30, 19, 33,
        5, 13, 34,  1,  9, 16, 57, 46]), 'cur_cost': 237059.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 27, 29, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18], dtype=int64), 'cur_cost': 238969.0, 'intermediate_solutions': [{'tour': array([32,  4, 54, 43, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 262429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43, 32,  4, 54, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 269962.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([36, 43, 32,  4, 54,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 264316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([54, 43, 32,  4, 36,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 267885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([54, 36, 43, 32,  4,  0, 53, 45, 31,  3,  5, 56, 42, 28, 14, 11, 30,
       50, 39, 10, 52, 29, 48, 55, 35, 17, 34, 13, 41, 25, 23, 58,  1,  8,
       49, 26, 21, 38, 18, 51, 22,  6, 47,  7, 27, 33, 12, 16, 15, 24, 57,
        9, 40, 44,  2, 46, 20, 37, 19]), 'cur_cost': 260119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:04,358 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:04,358 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,361 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24873.000, 多样性=0.961
2025-08-05 10:29:04,361 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:04,361 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:04,361 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:04,361 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.06594273388969356, 'best_improvement': 0.0804125998225377}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.059825508932280634}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04880508141278735, 'recent_improvements': [0.06197019627452271, 0.17312795971049535, -0.03563996655105198], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 24455, 'new_best_cost': 24455, 'quality_improvement': 0.0, 'old_diversity': 0.7062146892655367, 'new_diversity': 0.7062146892655367, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:04,362 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:04,362 - __main__ - INFO - composite11_59 开始进化第 4 代
2025-08-05 10:29:04,362 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:04,362 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,363 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24873.000, 多样性=0.961
2025-08-05 10:29:04,363 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:04,365 - PathExpert - INFO - 路径结构分析完成: 公共边数量=18, 路径相似性=0.961
2025-08-05 10:29:04,366 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:04,367 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.706
2025-08-05 10:29:04,369 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:04,369 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:04,370 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:29:04,370 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:29:04,410 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.143, 适应度梯度: -26458.300, 聚类评分: 0.000, 覆盖率: 0.132, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:04,410 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:04,410 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:04,410 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 10:29:04,416 - visualization.landscape_visualizer - INFO - 插值约束: 73 个点被约束到最小值 24455.00
2025-08-05 10:29:04,417 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.0%, 梯度: 8832.32 → 8038.95
2025-08-05 10:29:04,554 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\landscape_composite11_59_iter_114_20250805_102904.html
2025-08-05 10:29:04,638 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite11_59\dashboard_composite11_59_iter_114_20250805_102904.html
2025-08-05 10:29:04,638 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 114
2025-08-05 10:29:04,638 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:04,638 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2696秒
2025-08-05 10:29:04,639 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.14285714285714285, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -26458.3, 'local_optima_density': 0.14285714285714285, 'gradient_variance': 4374157648.775714, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1318, 'fitness_entropy': 0.754445012014942, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -26458.300)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.132)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360944.4101658, 'performance_metrics': {}}}
2025-08-05 10:29:04,639 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:04,639 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:04,640 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:04,640 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:04,641 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,641 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:04,641 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,641 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:04,642 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:04,642 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:29:04,642 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:04,642 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:04,642 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:04,642 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:04,643 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:04,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,645 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27065.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,647 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27065.0, 'intermediate_solutions': [{'tour': [7, 5, 48, 1, 6, 22, 14, 20, 33, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 16, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 127322.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 50, 44, 11, 17, 37, 31, 26, 24], 'cur_cost': 127117.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 12, 17, 11, 44, 50, 37, 31, 13, 26, 24], 'cur_cost': 129479.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,647 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 27065.00)
2025-08-05 10:29:04,647 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:04,647 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:04,648 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,652 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 10:29:04,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,652 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,653 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 191093.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,653 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [46, 28, 7, 22, 23, 6, 17, 30, 1, 34, 44, 43, 41, 24, 8, 3, 21, 27, 45, 33, 29, 25, 26, 35, 9, 13, 5, 53, 49, 10, 19, 54, 47, 56, 2, 4, 20, 18, 57, 12, 14, 0, 16, 55, 11, 52, 58, 48, 51, 37, 36, 38, 42, 32, 39, 31, 40, 50, 15], 'cur_cost': 191093.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 22, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 31, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 45289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 25023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 14, 20, 16, 17, 15, 11, 12, 21, 19, 22], 'cur_cost': 25058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,653 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 191093.00)
2025-08-05 10:29:04,653 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:04,653 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:04,653 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,656 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32641.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,657 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 18, 15, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32641.0, 'intermediate_solutions': [{'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 53, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 33, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 41652.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 5, 8, 7, 1, 6, 14, 20, 17, 15, 11, 12, 19, 18, 13, 22, 21, 16, 0, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 55, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 57, 51, 49, 54, 50, 47], 'cur_cost': 34931.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,657 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 32641.00)
2025-08-05 10:29:04,658 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:04,658 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:04,658 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,659 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,660 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,660 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27098.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,660 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 15, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27098.0, 'intermediate_solutions': [{'tour': [16, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 0, 17, 15, 11, 12, 19], 'cur_cost': 32673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 19, 12, 11, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43], 'cur_cost': 40257.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 42, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 42370.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,661 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 27098.00)
2025-08-05 10:29:04,661 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:04,661 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,661 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,661 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 246038.0
2025-08-05 10:29:04,669 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:04,669 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0, 24455]
2025-08-05 10:29:04,669 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 22, 13, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64)]
2025-08-05 10:29:04,671 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,671 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27065.0}, {'tour': [46, 28, 7, 22, 23, 6, 17, 30, 1, 34, 44, 43, 41, 24, 8, 3, 21, 27, 45, 33, 29, 25, 26, 35, 9, 13, 5, 53, 49, 10, 19, 54, 47, 56, 2, 4, 20, 18, 57, 12, 14, 0, 16, 55, 11, 52, 58, 48, 51, 37, 36, 38, 42, 32, 39, 31, 40, 50, 15], 'cur_cost': 191093.0}, {'tour': [0, 18, 15, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32641.0}, {'tour': [0, 4, 15, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27098.0}, {'tour': array([18, 37, 43, 15, 19, 34, 50,  0, 35, 39, 38, 16, 44, 23, 24, 30, 51,
       40,  9, 27, 10,  8, 53, 17, 13, 31, 52,  7, 20, 28,  1, 11, 42,  2,
       12, 58,  5, 56, 14, 46, 47,  6, 25, 21, 57, 22, 41, 49,  3, 26,  4,
       29, 33, 32, 48, 55, 36, 45, 54], dtype=int64), 'cur_cost': 246038.0}, {'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144730.0}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162692.0}, {'tour': [0, 2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24873.0}, {'tour': [55, 38, 11, 34, 2, 26, 43, 49, 7, 14, 57, 30, 35, 8, 41, 3, 46, 45, 51, 19, 50, 13, 23, 1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22, 12, 52, 10, 4, 53, 24, 29, 16, 32, 56, 48, 0, 40, 20, 5, 39, 28, 21, 47, 6, 33, 54, 9, 17, 25], 'cur_cost': 267655.0}, {'tour': [30, 27, 29, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15, 5, 7, 51, 20, 21, 6, 1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34, 9, 32, 54, 4, 16, 42, 52, 13, 0, 53, 17, 46, 39, 26, 38, 45, 50, 3, 37, 8, 2, 49, 47, 57, 40, 28, 18], 'cur_cost': 238969.0}]
2025-08-05 10:29:04,672 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,672 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 294, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 294, 'cache_hits': 0, 'similarity_calculations': 1462, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,673 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([18, 37, 43, 15, 19, 34, 50,  0, 35, 39, 38, 16, 44, 23, 24, 30, 51,
       40,  9, 27, 10,  8, 53, 17, 13, 31, 52,  7, 20, 28,  1, 11, 42,  2,
       12, 58,  5, 56, 14, 46, 47,  6, 25, 21, 57, 22, 41, 49,  3, 26,  4,
       29, 33, 32, 48, 55, 36, 45, 54], dtype=int64), 'cur_cost': 246038.0, 'intermediate_solutions': [{'tour': array([14, 57, 44, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 14, 57, 44, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 243479.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 24, 14, 57, 44, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239862.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44, 24, 14, 57, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239627.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 55, 24, 14, 57, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 247696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,674 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 246038.00)
2025-08-05 10:29:04,674 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:04,674 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:04,674 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,676 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24991.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,677 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 5, 2, 3, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24991.0, 'intermediate_solutions': [{'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 21, 28, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144684.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 48, 1, 6, 22, 14, 52, 9, 58, 0, 53, 56, 40, 38, 41, 45, 43, 36, 39, 37, 13, 46, 35, 33, 23, 34, 27, 29, 32, 8, 30, 25, 19, 21, 28, 17, 2, 11, 20, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 155814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 27, 8, 32, 29, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144754.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,677 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 24991.00)
2025-08-05 10:29:04,678 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:04,678 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:04,678 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,683 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 59
2025-08-05 10:29:04,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157052.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,684 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [48, 21, 34, 8, 19, 2, 0, 12, 22, 16, 33, 42, 36, 35, 43, 38, 40, 4, 30, 24, 26, 3, 13, 7, 25, 44, 27, 37, 6, 10, 20, 11, 23, 17, 28, 41, 45, 9, 49, 56, 57, 54, 52, 51, 58, 18, 14, 29, 31, 5, 1, 50, 53, 46, 39, 55, 47, 15, 32], 'cur_cost': 157052.0, 'intermediate_solutions': [{'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 23, 32, 29, 24, 31, 27, 25, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162821.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 50, 51, 49, 57, 52, 58, 18, 0, 53, 38, 40, 45, 41, 8, 43, 36, 39, 46, 33, 35, 34, 23, 27, 31, 24, 29, 32, 25, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 177260.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 17, 26, 6, 44, 10, 55, 5, 19, 54], 'cur_cost': 158504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,684 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 157052.00)
2025-08-05 10:29:04,684 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:29:04,684 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:29:04,684 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:04,686 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 59
2025-08-05 10:29:04,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,687 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:04,687 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32555.0, 路径长度: 59, 收集中间解: 3
2025-08-05 10:29:04,687 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 18, 1, 7, 8, 5, 3, 2, 9, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 32555.0, 'intermediate_solutions': [{'tour': [0, 56, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 2, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 40384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 9, 12, 11, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 3, 5, 8, 7, 1, 6, 21, 19], 'cur_cost': 32387.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 0, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32560.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:04,688 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 32555.00)
2025-08-05 10:29:04,688 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:29:04,688 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,688 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,688 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 231857.0
2025-08-05 10:29:04,694 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:04,695 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0, 24455]
2025-08-05 10:29:04,695 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 22, 13, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64)]
2025-08-05 10:29:04,697 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,697 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27065.0}, {'tour': [46, 28, 7, 22, 23, 6, 17, 30, 1, 34, 44, 43, 41, 24, 8, 3, 21, 27, 45, 33, 29, 25, 26, 35, 9, 13, 5, 53, 49, 10, 19, 54, 47, 56, 2, 4, 20, 18, 57, 12, 14, 0, 16, 55, 11, 52, 58, 48, 51, 37, 36, 38, 42, 32, 39, 31, 40, 50, 15], 'cur_cost': 191093.0}, {'tour': [0, 18, 15, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32641.0}, {'tour': [0, 4, 15, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27098.0}, {'tour': array([18, 37, 43, 15, 19, 34, 50,  0, 35, 39, 38, 16, 44, 23, 24, 30, 51,
       40,  9, 27, 10,  8, 53, 17, 13, 31, 52,  7, 20, 28,  1, 11, 42,  2,
       12, 58,  5, 56, 14, 46, 47,  6, 25, 21, 57, 22, 41, 49,  3, 26,  4,
       29, 33, 32, 48, 55, 36, 45, 54], dtype=int64), 'cur_cost': 246038.0}, {'tour': [0, 1, 5, 2, 3, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24991.0}, {'tour': [48, 21, 34, 8, 19, 2, 0, 12, 22, 16, 33, 42, 36, 35, 43, 38, 40, 4, 30, 24, 26, 3, 13, 7, 25, 44, 27, 37, 6, 10, 20, 11, 23, 17, 28, 41, 45, 9, 49, 56, 57, 54, 52, 51, 58, 18, 14, 29, 31, 5, 1, 50, 53, 46, 39, 55, 47, 15, 32], 'cur_cost': 157052.0}, {'tour': [0, 20, 18, 1, 7, 8, 5, 3, 2, 9, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 32555.0}, {'tour': array([25, 38, 13, 17, 10, 27, 52, 54,  4, 19,  1, 32, 48, 24, 43, 34, 40,
       28, 41, 56, 16,  6,  9,  3, 50, 47, 31,  2, 42, 22, 18, 11, 30,  7,
       12, 15, 14, 20, 37, 51, 44, 46, 57, 21, 45, 29,  0, 49, 55,  5, 23,
       58,  8, 35, 36, 26, 53, 39, 33], dtype=int64), 'cur_cost': 231857.0}, {'tour': [30, 27, 29, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15, 5, 7, 51, 20, 21, 6, 1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34, 9, 32, 54, 4, 16, 42, 52, 13, 0, 53, 17, 46, 39, 26, 38, 45, 50, 3, 37, 8, 2, 49, 47, 57, 40, 28, 18], 'cur_cost': 238969.0}]
2025-08-05 10:29:04,698 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,698 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 295, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 295, 'cache_hits': 0, 'similarity_calculations': 1470, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,699 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([25, 38, 13, 17, 10, 27, 52, 54,  4, 19,  1, 32, 48, 24, 43, 34, 40,
       28, 41, 56, 16,  6,  9,  3, 50, 47, 31,  2, 42, 22, 18, 11, 30,  7,
       12, 15, 14, 20, 37, 51, 44, 46, 57, 21, 45, 29,  0, 49, 55,  5, 23,
       58,  8, 35, 36, 26, 53, 39, 33], dtype=int64), 'cur_cost': 231857.0, 'intermediate_solutions': [{'tour': array([11, 38, 55, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34, 11, 38, 55,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 260821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 34, 11, 38, 55, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267655.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 34, 11, 38,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 269769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55,  2, 34, 11, 38, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,699 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 231857.00)
2025-08-05 10:29:04,699 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:04,699 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:04,699 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:04,700 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 263488.0
2025-08-05 10:29:04,709 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:04,709 - ExploitationExpert - INFO - res_population_costs: [24455, 24488, 24494, 24533.0, 24455]
2025-08-05 10:29:04,709 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21, 12, 11,
       27, 34, 23, 31, 33, 28, 26, 30, 25, 29, 32, 24, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 18, 13, 22, 14, 20, 16, 15, 17, 21,
       12, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10,  6,  1,  7, 19, 21, 12, 17, 22, 18, 13, 14, 20, 16,
       15, 11, 27, 34, 23, 31, 33, 26, 28, 30, 25, 29, 32, 24, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 22, 13, 14, 20, 16, 15, 17, 21, 12, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64)]
2025-08-05 10:29:04,711 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:04,712 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27065.0}, {'tour': [46, 28, 7, 22, 23, 6, 17, 30, 1, 34, 44, 43, 41, 24, 8, 3, 21, 27, 45, 33, 29, 25, 26, 35, 9, 13, 5, 53, 49, 10, 19, 54, 47, 56, 2, 4, 20, 18, 57, 12, 14, 0, 16, 55, 11, 52, 58, 48, 51, 37, 36, 38, 42, 32, 39, 31, 40, 50, 15], 'cur_cost': 191093.0}, {'tour': [0, 18, 15, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32641.0}, {'tour': [0, 4, 15, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27098.0}, {'tour': array([18, 37, 43, 15, 19, 34, 50,  0, 35, 39, 38, 16, 44, 23, 24, 30, 51,
       40,  9, 27, 10,  8, 53, 17, 13, 31, 52,  7, 20, 28,  1, 11, 42,  2,
       12, 58,  5, 56, 14, 46, 47,  6, 25, 21, 57, 22, 41, 49,  3, 26,  4,
       29, 33, 32, 48, 55, 36, 45, 54], dtype=int64), 'cur_cost': 246038.0}, {'tour': [0, 1, 5, 2, 3, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24991.0}, {'tour': [48, 21, 34, 8, 19, 2, 0, 12, 22, 16, 33, 42, 36, 35, 43, 38, 40, 4, 30, 24, 26, 3, 13, 7, 25, 44, 27, 37, 6, 10, 20, 11, 23, 17, 28, 41, 45, 9, 49, 56, 57, 54, 52, 51, 58, 18, 14, 29, 31, 5, 1, 50, 53, 46, 39, 55, 47, 15, 32], 'cur_cost': 157052.0}, {'tour': [0, 20, 18, 1, 7, 8, 5, 3, 2, 9, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 32555.0}, {'tour': array([25, 38, 13, 17, 10, 27, 52, 54,  4, 19,  1, 32, 48, 24, 43, 34, 40,
       28, 41, 56, 16,  6,  9,  3, 50, 47, 31,  2, 42, 22, 18, 11, 30,  7,
       12, 15, 14, 20, 37, 51, 44, 46, 57, 21, 45, 29,  0, 49, 55,  5, 23,
       58,  8, 35, 36, 26, 53, 39, 33], dtype=int64), 'cur_cost': 231857.0}, {'tour': array([52, 25, 51, 53, 54, 26,  0, 40, 27,  7, 50, 11, 23, 13, 10, 44, 56,
       38, 29, 45, 49,  1,  9, 57, 14, 17, 16, 37, 24, 39, 20, 43, 15, 12,
       34, 41, 30,  8, 46, 58, 35, 36,  3, 22, 28, 19, 42,  6, 47,  5, 33,
       48,  4, 31, 21, 18,  2, 55, 32], dtype=int64), 'cur_cost': 263488.0}]
2025-08-05 10:29:04,713 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:04,713 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 296, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 296, 'cache_hits': 0, 'similarity_calculations': 1479, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:04,714 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([52, 25, 51, 53, 54, 26,  0, 40, 27,  7, 50, 11, 23, 13, 10, 44, 56,
       38, 29, 45, 49,  1,  9, 57, 14, 17, 16, 37, 24, 39, 20, 43, 15, 12,
       34, 41, 30,  8, 46, 58, 35, 36,  3, 22, 28, 19, 42,  6, 47,  5, 33,
       48,  4, 31, 21, 18,  2, 55, 32], dtype=int64), 'cur_cost': 263488.0, 'intermediate_solutions': [{'tour': array([29, 27, 30, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 29, 27, 30, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 228900.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 12, 29, 27, 30, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 12, 29, 27, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 31, 12, 29, 27, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:04,714 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 263488.00)
2025-08-05 10:29:04,714 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:04,714 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:04,717 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 16, 20, 14, 13, 18, 22, 21, 12, 11, 15, 17, 19, 6, 1, 7, 8, 5, 3, 2, 9, 4, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27065.0, 'intermediate_solutions': [{'tour': [7, 5, 48, 1, 6, 22, 14, 20, 33, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 16, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 17, 11, 44, 50, 37, 31, 26, 24], 'cur_cost': 127322.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 13, 12, 50, 44, 11, 17, 37, 31, 26, 24], 'cur_cost': 127117.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 48, 1, 6, 22, 14, 20, 16, 27, 4, 10, 21, 19, 28, 30, 25, 32, 29, 3, 2, 36, 34, 23, 33, 35, 46, 39, 18, 43, 8, 42, 45, 41, 38, 40, 0, 53, 9, 58, 52, 56, 55, 57, 51, 49, 54, 47, 15, 12, 17, 11, 44, 50, 37, 31, 13, 26, 24], 'cur_cost': 129479.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [46, 28, 7, 22, 23, 6, 17, 30, 1, 34, 44, 43, 41, 24, 8, 3, 21, 27, 45, 33, 29, 25, 26, 35, 9, 13, 5, 53, 49, 10, 19, 54, 47, 56, 2, 4, 20, 18, 57, 12, 14, 0, 16, 55, 11, 52, 58, 48, 51, 37, 36, 38, 42, 32, 39, 31, 40, 50, 15], 'cur_cost': 191093.0, 'intermediate_solutions': [{'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 22, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 31, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 45289.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 25023.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 1, 6, 4, 9, 3, 5, 8, 7, 2, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 14, 20, 16, 17, 15, 11, 12, 21, 19, 22], 'cur_cost': 25058.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 15, 2, 3, 5, 4, 9, 10, 6, 1, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 20, 16, 17, 21, 12, 11, 19], 'cur_cost': 32641.0, 'intermediate_solutions': [{'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 53, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 33, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 41652.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 9, 2, 3, 5, 8, 7, 1, 6, 14, 20, 17, 15, 11, 12, 19, 18, 13, 22, 21, 16, 0, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27172.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 21, 22, 13, 18, 19, 12, 11, 15, 17, 20, 14, 6, 1, 7, 8, 5, 3, 2, 9, 4, 55, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 57, 51, 49, 54, 50, 47], 'cur_cost': 34931.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 15, 13, 18, 22, 14, 20, 16, 17, 21, 12, 11, 19, 6, 1, 7, 8, 5, 3, 2, 9, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47], 'cur_cost': 27098.0, 'intermediate_solutions': [{'tour': [16, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 0, 17, 15, 11, 12, 19], 'cur_cost': 32673.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 19, 12, 11, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43], 'cur_cost': 40257.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 9, 4, 3, 5, 8, 7, 1, 6, 10, 28, 26, 30, 25, 42, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 19], 'cur_cost': 42370.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 37, 43, 15, 19, 34, 50,  0, 35, 39, 38, 16, 44, 23, 24, 30, 51,
       40,  9, 27, 10,  8, 53, 17, 13, 31, 52,  7, 20, 28,  1, 11, 42,  2,
       12, 58,  5, 56, 14, 46, 47,  6, 25, 21, 57, 22, 41, 49,  3, 26,  4,
       29, 33, 32, 48, 55, 36, 45, 54], dtype=int64), 'cur_cost': 246038.0, 'intermediate_solutions': [{'tour': array([14, 57, 44, 24, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239684.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([24, 14, 57, 44, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 243479.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([55, 24, 14, 57, 44, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239862.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44, 24, 14, 57, 55, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 239627.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 55, 24, 14, 57, 41, 50, 34, 31, 15, 11, 37, 58, 22, 39, 47, 46,
       35, 52, 49, 54,  4, 28, 26,  2,  5, 43,  7, 53, 18, 36,  9,  1,  3,
       25, 48, 16, 19,  0, 12, 29, 51, 17, 42, 38,  8, 30, 33, 10, 40, 13,
       32, 56, 21,  6, 45, 27, 23, 20]), 'cur_cost': 247696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 5, 2, 3, 4, 9, 10, 6, 7, 8, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 24991.0, 'intermediate_solutions': [{'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 21, 28, 19, 25, 30, 8, 32, 29, 27, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144684.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 48, 1, 6, 22, 14, 52, 9, 58, 0, 53, 56, 40, 38, 41, 45, 43, 36, 39, 37, 13, 46, 35, 33, 23, 34, 27, 29, 32, 8, 30, 25, 19, 21, 28, 17, 2, 11, 20, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 155814.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 48, 1, 6, 22, 14, 20, 11, 2, 17, 28, 21, 19, 25, 30, 27, 8, 32, 29, 34, 23, 33, 35, 46, 13, 37, 39, 36, 43, 45, 41, 38, 40, 56, 53, 0, 58, 9, 52, 57, 55, 49, 51, 50, 54, 16, 18, 24, 15, 26, 3, 4, 42, 10, 31, 7, 47, 12, 44], 'cur_cost': 144754.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [48, 21, 34, 8, 19, 2, 0, 12, 22, 16, 33, 42, 36, 35, 43, 38, 40, 4, 30, 24, 26, 3, 13, 7, 25, 44, 27, 37, 6, 10, 20, 11, 23, 17, 28, 41, 45, 9, 49, 56, 57, 54, 52, 51, 58, 18, 14, 29, 31, 5, 1, 50, 53, 46, 39, 55, 47, 15, 32], 'cur_cost': 157052.0, 'intermediate_solutions': [{'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 23, 32, 29, 24, 31, 27, 25, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 162821.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 50, 51, 49, 57, 52, 58, 18, 0, 53, 38, 40, 45, 41, 8, 43, 36, 39, 46, 33, 35, 34, 23, 27, 31, 24, 29, 32, 25, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 26, 6, 44, 17, 10, 55, 5, 19, 54], 'cur_cost': 177260.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 2, 1, 9, 3, 12, 16, 7, 4, 28, 14, 30, 25, 32, 29, 24, 31, 27, 23, 34, 35, 33, 46, 39, 36, 43, 8, 41, 45, 40, 38, 53, 0, 18, 58, 52, 57, 49, 51, 50, 47, 13, 22, 42, 20, 11, 56, 15, 37, 48, 17, 26, 6, 44, 10, 55, 5, 19, 54], 'cur_cost': 158504.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 18, 1, 7, 8, 5, 3, 2, 9, 4, 6, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 54, 50, 47, 13, 22, 14, 21, 12, 11, 15, 17, 16, 19], 'cur_cost': 32555.0, 'intermediate_solutions': [{'tour': [0, 56, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 2, 55, 57, 51, 49, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 40384.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 9, 12, 11, 15, 17, 16, 20, 14, 22, 18, 13, 47, 50, 54, 49, 51, 57, 55, 56, 52, 58, 48, 53, 39, 40, 38, 41, 45, 42, 36, 43, 37, 44, 46, 35, 33, 23, 34, 27, 31, 24, 29, 32, 25, 30, 26, 28, 10, 4, 3, 5, 8, 7, 1, 6, 21, 19], 'cur_cost': 32387.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 6, 1, 7, 8, 5, 3, 4, 10, 28, 26, 30, 25, 32, 29, 24, 31, 27, 34, 23, 33, 35, 46, 44, 37, 43, 36, 42, 45, 41, 38, 40, 39, 53, 48, 58, 52, 56, 55, 57, 51, 49, 0, 54, 50, 47, 13, 18, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19], 'cur_cost': 32560.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 38, 13, 17, 10, 27, 52, 54,  4, 19,  1, 32, 48, 24, 43, 34, 40,
       28, 41, 56, 16,  6,  9,  3, 50, 47, 31,  2, 42, 22, 18, 11, 30,  7,
       12, 15, 14, 20, 37, 51, 44, 46, 57, 21, 45, 29,  0, 49, 55,  5, 23,
       58,  8, 35, 36, 26, 53, 39, 33], dtype=int64), 'cur_cost': 231857.0, 'intermediate_solutions': [{'tour': array([11, 38, 55, 34,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34, 11, 38, 55,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 260821.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 34, 11, 38, 55, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267655.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([55, 34, 11, 38,  2, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 269769.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([55,  2, 34, 11, 38, 26, 43, 49,  7, 14, 57, 30, 35,  8, 41,  3, 46,
       45, 51, 19, 50, 13, 23,  1, 18, 37, 27, 58, 44, 31, 36, 42, 15, 22,
       12, 52, 10,  4, 53, 24, 29, 16, 32, 56, 48,  0, 40, 20,  5, 39, 28,
       21, 47,  6, 33, 54,  9, 17, 25]), 'cur_cost': 267411.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 25, 51, 53, 54, 26,  0, 40, 27,  7, 50, 11, 23, 13, 10, 44, 56,
       38, 29, 45, 49,  1,  9, 57, 14, 17, 16, 37, 24, 39, 20, 43, 15, 12,
       34, 41, 30,  8, 46, 58, 35, 36,  3, 22, 28, 19, 42,  6, 47,  5, 33,
       48,  4, 31, 21, 18,  2, 55, 32], dtype=int64), 'cur_cost': 263488.0, 'intermediate_solutions': [{'tour': array([29, 27, 30, 12, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 29, 27, 30, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 228900.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([31, 12, 29, 27, 30, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 12, 29, 27, 31, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238969.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 31, 12, 29, 27, 56, 36, 33, 41, 10, 43, 14, 55, 23, 15,  5,  7,
       51, 20, 21,  6,  1, 58, 11, 24, 22, 19, 44, 35, 48, 25, 34,  9, 32,
       54,  4, 16, 42, 52, 13,  0, 53, 17, 46, 39, 26, 38, 45, 50,  3, 37,
        8,  2, 49, 47, 57, 40, 28, 18]), 'cur_cost': 238960.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:04,717 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:04,718 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,721 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24991.000, 多样性=0.946
2025-08-05 10:29:04,721 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:04,721 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:04,721 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:04,722 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.018262874788902355, 'best_improvement': -0.0047441000281429665}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.015680125441003463}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.11953534680009446, 'recent_improvements': [0.17312795971049535, -0.03563996655105198, -0.06594273388969356], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 24455, 'new_best_cost': 24455, 'quality_improvement': 0.0, 'old_diversity': 0.6508474576271186, 'new_diversity': 0.6508474576271186, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:04,722 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:04,722 - __main__ - INFO - composite11_59 开始进化第 5 代
2025-08-05 10:29:04,722 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:04,722 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:04,723 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=24991.000, 多样性=0.946
2025-08-05 10:29:04,723 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:04,726 - PathExpert - INFO - 路径结构分析完成: 公共边数量=9, 路径相似性=0.946
2025-08-05 10:29:04,726 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:04,728 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.651
2025-08-05 10:29:04,729 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:04,730 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:04,730 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:04,730 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:04,769 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.133, 适应度梯度: -13892.440, 聚类评分: 0.000, 覆盖率: 0.133, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:04,769 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:04,769 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:04,769 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite11_59
2025-08-05 10:29:04,777 - visualization.landscape_visualizer - INFO - 插值约束: 60 个点被约束到最小值 24455.00
2025-08-05 10:29:04,779 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.4%, 梯度: 9921.41 → 8987.39
