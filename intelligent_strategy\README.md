# Intelligent Strategy Selection System

A fitness landscape-driven strategy selection system for the EoH-TSP-Solver framework, implementing intelligent exploration-exploitation balance through LLM-based reasoning and adaptive strategy selection.

## Project Structure

```
src/intelligent_strategy/
├── __init__.py                 # Main module initialization
├── system.py                   # Main system interface
├── core/                       # Core framework components
│   ├── __init__.py
│   ├── individual_state.py     # Individual state modeling
│   ├── landscape_analysis.py   # Fitness landscape analysis
│   ├── strategy_interfaces.py  # Core interfaces
│   └── data_structures.py      # Common data structures
├── strategies/                 # Strategy implementations
│   ├── __init__.py
│   ├── exploration/            # Exploration strategies
│   │   ├── __init__.py
│   │   ├── strong_exploration.py
│   │   ├── balanced_exploration.py
│   │   └── intelligent_exploration.py
│   ├── exploitation/           # Exploitation strategies
│   │   ├── __init__.py
│   │   ├── local_search.py
│   │   ├── elite_guidance.py
│   │   └── escape_mechanisms.py
│   └── coordination/           # Coordination mechanisms
│       ├── __init__.py
│       └── collaborative_escape.py
├── llm_interface/              # LLM integration
│   ├── __init__.py
│   ├── llm_strategy_interface.py
│   ├── prompt_templates.py
│   ├── response_parser.py
│   └── providers/              # LLM provider implementations
│       ├── __init__.py
│       ├── openai_provider.py
│       └── base_provider.py
├── monitoring/                 # Performance monitoring
│   ├── __init__.py
│   ├── performance_monitor.py
│   ├── resource_manager.py
│   ├── memory_optimizer.py
│   ├── dashboard.py
│   └── logger.py
├── integration/                # EoH-TSP-Solver integration
│   ├── __init__.py
│   ├── eoh_adapter.py
│   ├── data_flow.py
│   └── compatibility.py
├── config/                     # Configuration management
│   ├── __init__.py
│   ├── system_config.py
│   ├── strategy_config.py
│   ├── llm_config.py
│   └── monitoring_config.py
└── utils/                      # Utility functions
    ├── __init__.py
    ├── cache_manager.py
    ├── parallel_optimizer.py
    ├── validation.py
    └── metrics_calculator.py
```

## Development Phases

### Phase 1: Basic Framework Implementation ✅
- [x] Project structure creation
- [ ] Core data structures (IndividualState, LandscapeFeatures)
- [ ] Individual state monitoring system
- [ ] LLM interface development
- [ ] Basic strategy selection interface

### Phase 2: Core Algorithm Implementation
- [ ] Exploration strategy algorithms
- [ ] Exploitation strategy algorithms  
- [ ] Coordination mechanisms
- [ ] Strategy execution framework

### Phase 3: System Integration and Optimization
- [ ] EoH-TSP-Solver integration
- [ ] Performance optimizations
- [ ] Caching and parallel processing
- [ ] Resource management

### Phase 4: Validation and Deployment
- [ ] Functional validation tests
- [ ] Performance benchmarking
- [ ] Production deployment setup
- [ ] Monitoring dashboard

## Key Features

- **🧠 Intelligent Strategy Selection**: LLM-based multi-dimensional strategy reasoning
- **🎯 Individual-Level Control**: Personalized strategy parameters for each individual
- **🔄 Adaptive Adjustment**: Real-time strategy optimization based on execution feedback
- **🤝 Collaborative Exploration**: Intelligent coordination between individuals
- **📊 Comprehensive Monitoring**: Detailed execution tracking and performance evaluation

## Configuration

The system uses YAML configuration files located in `config/intelligent_strategy_config.yaml`. Key configuration sections:

- `system`: Core system parameters
- `llm_interface`: LLM provider and model settings
- `exploration_strategy`: Exploration algorithm parameters
- `exploitation_strategy`: Exploitation algorithm parameters
- `performance_monitoring`: Monitoring and logging settings

## Usage

```python
from intelligent_strategy import IntelligentStrategySystem
from intelligent_strategy.config import SystemConfig

# Initialize system
config = SystemConfig.from_yaml('config/intelligent_strategy_config.yaml')
strategy_system = IntelligentStrategySystem(config)

# Integrate with EoH-TSP-Solver
solver.set_strategy_system(strategy_system)

# Run optimization
result = solver.solve(problem_instance)
```

## Performance Targets

Based on design document specifications:
- **Convergence Speed**: 25-40% improvement
- **Solution Quality**: 15-30% improvement  
- **Algorithm Robustness**: Significant enhancement
- **Parameter Sensitivity**: Major reduction

## Dependencies

See `requirements_intelligent_strategy.txt` for complete dependency list.

## License

This project is part of the EoH-TSP-Solver framework.
