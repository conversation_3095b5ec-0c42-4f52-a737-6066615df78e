2025-08-05 10:28:42,910 - __main__ - INFO - geometry5_10 开始进化第 1 代
2025-08-05 10:28:42,911 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:42,912 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,913 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=86.000, 多样性=0.869
2025-08-05 10:28:42,915 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:42,916 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.869
2025-08-05 10:28:42,917 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:42,919 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:42,919 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:42,919 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:42,920 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:42,925 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -6.400, 聚类评分: 0.000, 覆盖率: 0.059, 收敛趋势: 0.000, 多样性: 0.869
2025-08-05 10:28:42,926 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:42,926 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:42,926 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry5_10
2025-08-05 10:28:42,932 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 1.87 → 1.71
2025-08-05 10:28:43,068 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\landscape_geometry5_10_iter_51_20250805_102842.html
2025-08-05 10:28:43,137 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\dashboard_geometry5_10_iter_51_20250805_102842.html
2025-08-05 10:28:43,137 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 51
2025-08-05 10:28:43,137 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:43,138 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2187秒
2025-08-05 10:28:43,138 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 102, 'max_size': 500, 'hits': 0, 'misses': 102, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 350, 'misses': 164, 'hit_rate': 0.6809338521400778, 'evictions': 64, 'ttl': 7200}}
2025-08-05 10:28:43,138 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6.4, 'local_optima_density': 0.2, 'gradient_variance': 402.84000000000003, 'cluster_count': 0}, 'population_state': {'diversity': 0.8688888888888888, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0586, 'fitness_entropy': 0.9756149631508355, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6.400)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.059)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.869)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360922.926702, 'performance_metrics': {}}}
2025-08-05 10:28:43,138 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:43,139 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:43,139 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:43,139 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:43,139 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:43,139 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:43,140 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:43,140 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,140 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:43,140 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:43,140 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,141 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:43,141 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:43,141 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:43,141 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:43,141 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,142 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,142 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,142 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,142 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 107.00)
2025-08-05 10:28:43,143 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:43,143 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:43,143 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,143 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,144 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,144 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 8, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,144 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 101.00)
2025-08-05 10:28:43,144 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:43,144 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:43,144 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,145 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,145 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,145 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [8, 9, 3, 6, 0, 5, 4, 7, 2, 1], 'cur_cost': 104.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,145 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 104.00)
2025-08-05 10:28:43,145 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:43,145 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:43,146 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,146 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,147 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 7, 4, 3, 9, 0, 1, 6, 8, 5], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,147 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 101.00)
2025-08-05 10:28:43,147 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:43,147 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:43,147 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,147 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,147 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,148 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,148 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 3, 2, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 117.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,148 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 117.00)
2025-08-05 10:28:43,148 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:43,148 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:43,148 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,149 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,149 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,149 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,149 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 2, 6, 1, 7, 9, 8, 5, 4, 0], 'cur_cost': 105.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,149 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 105.00)
2025-08-05 10:28:43,149 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:43,149 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,149 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,150 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 114.0
2025-08-05 10:28:43,155 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:43,156 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78]
2025-08-05 10:28:43,156 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64)]
2025-08-05 10:28:43,157 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,157 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0}, {'tour': [5, 8, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 101.0}, {'tour': [8, 9, 3, 6, 0, 5, 4, 7, 2, 1], 'cur_cost': 104.0}, {'tour': [2, 7, 4, 3, 9, 0, 1, 6, 8, 5], 'cur_cost': 101.0}, {'tour': [1, 3, 2, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 117.0}, {'tour': [3, 2, 6, 1, 7, 9, 8, 5, 4, 0], 'cur_cost': 105.0}, {'tour': array([7, 1, 9, 8, 5, 3, 2, 4, 0, 6], dtype=int64), 'cur_cost': 114.0}, {'tour': array([1, 9, 5, 2, 0, 7, 6, 4, 3, 8], dtype=int64), 'cur_cost': 125.0}, {'tour': array([1, 5, 2, 0, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 139.0}, {'tour': array([2, 7, 5, 6, 0, 3, 8, 4, 1, 9], dtype=int64), 'cur_cost': 128.0}]
2025-08-05 10:28:43,158 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,158 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 131, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 131, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,159 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([7, 1, 9, 8, 5, 3, 2, 4, 0, 6], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([1, 9, 5, 4, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 9, 5, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 1, 9, 5, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 1, 9, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 4, 1, 9, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,159 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 114.00)
2025-08-05 10:28:43,159 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:43,159 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:43,159 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,159 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,160 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 129.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,160 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 8, 0, 3, 9, 2, 1, 7, 5, 6], 'cur_cost': 129.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,160 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 129.00)
2025-08-05 10:28:43,160 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:43,160 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,160 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,161 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 120.0
2025-08-05 10:28:43,167 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:43,167 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78]
2025-08-05 10:28:43,167 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64)]
2025-08-05 10:28:43,168 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,168 - ExploitationExpert - INFO - populations: [{'tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0}, {'tour': [5, 8, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 101.0}, {'tour': [8, 9, 3, 6, 0, 5, 4, 7, 2, 1], 'cur_cost': 104.0}, {'tour': [2, 7, 4, 3, 9, 0, 1, 6, 8, 5], 'cur_cost': 101.0}, {'tour': [1, 3, 2, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 117.0}, {'tour': [3, 2, 6, 1, 7, 9, 8, 5, 4, 0], 'cur_cost': 105.0}, {'tour': array([7, 1, 9, 8, 5, 3, 2, 4, 0, 6], dtype=int64), 'cur_cost': 114.0}, {'tour': [4, 8, 0, 3, 9, 2, 1, 7, 5, 6], 'cur_cost': 129.0}, {'tour': array([0, 4, 7, 6, 3, 1, 9, 8, 5, 2], dtype=int64), 'cur_cost': 120.0}, {'tour': array([2, 7, 5, 6, 0, 3, 8, 4, 1, 9], dtype=int64), 'cur_cost': 128.0}]
2025-08-05 10:28:43,169 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,169 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 132, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 132, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,170 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([0, 4, 7, 6, 3, 1, 9, 8, 5, 2], dtype=int64), 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 0, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 5, 1, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 2, 5, 1, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 2, 5, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 2, 5, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,170 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 120.00)
2025-08-05 10:28:43,170 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:43,170 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:43,170 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,170 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,171 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,171 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:43,171 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 8, 1, 5, 3, 4, 9, 0, 6, 2], 'cur_cost': 111.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,171 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 111.00)
2025-08-05 10:28:43,171 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:43,171 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:43,172 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [8, 9, 3, 6, 0, 5, 4, 7, 2, 1], 'cur_cost': 104.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 7, 4, 3, 9, 0, 1, 6, 8, 5], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 2, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 117.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 2, 6, 1, 7, 9, 8, 5, 4, 0], 'cur_cost': 105.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 1, 9, 8, 5, 3, 2, 4, 0, 6], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([1, 9, 5, 4, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 1, 9, 5, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 1, 9, 5, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([5, 4, 1, 9, 7, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([5, 7, 4, 1, 9, 6, 3, 0, 8, 2], dtype=int64), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 0, 3, 9, 2, 1, 7, 5, 6], 'cur_cost': 129.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 4, 7, 6, 3, 1, 9, 8, 5, 2], dtype=int64), 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': array([2, 5, 1, 0, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 2, 5, 1, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 0, 2, 5, 1, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 0, 2, 5, 3, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 3, 0, 2, 5, 8, 6, 9, 7, 4], dtype=int64), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 8, 1, 5, 3, 4, 9, 0, 6, 2], 'cur_cost': 111.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:43,172 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:43,172 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,173 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=101.000, 多样性=0.936
2025-08-05 10:28:43,174 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:43,174 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:43,174 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:43,174 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.019503547122206023, 'best_improvement': -0.1744186046511628}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.07672634271099756}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.01464444552323906, 'recent_improvements': [-0.03156439332198039, -0.0372807580083116, -0.0022755022755022677], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 78.0, 'new_best_cost': 78.0, 'quality_improvement': 0.0, 'old_diversity': 0.5666666666666667, 'new_diversity': 0.5666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:43,174 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:43,174 - __main__ - INFO - geometry5_10 开始进化第 2 代
2025-08-05 10:28:43,174 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:43,175 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,175 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=101.000, 多样性=0.936
2025-08-05 10:28:43,175 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:43,176 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.936
2025-08-05 10:28:43,176 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:43,177 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.567
2025-08-05 10:28:43,178 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:43,178 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:43,178 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:43,178 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:43,186 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.429, 适应度梯度: 1.471, 聚类评分: 0.000, 覆盖率: 0.060, 收敛趋势: 0.000, 多样性: 0.651
2025-08-05 10:28:43,186 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:43,186 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:43,186 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry5_10
2025-08-05 10:28:43,190 - visualization.landscape_visualizer - INFO - 插值约束: 83 个点被约束到最小值 78.00
2025-08-05 10:28:43,191 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.5%, 梯度: 1.74 → 1.61
2025-08-05 10:28:43,299 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\landscape_geometry5_10_iter_52_20250805_102843.html
2025-08-05 10:28:43,383 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\dashboard_geometry5_10_iter_52_20250805_102843.html
2025-08-05 10:28:43,383 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 52
2025-08-05 10:28:43,383 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:43,384 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2058秒
2025-08-05 10:28:43,384 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.42857142857142855, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 1.4714285714285718, 'local_optima_density': 0.42857142857142855, 'gradient_variance': 325.3177551020408, 'cluster_count': 0}, 'population_state': {'diversity': 0.6507064364207221, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0597, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.060)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 1.471)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360923.1863625, 'performance_metrics': {}}}
2025-08-05 10:28:43,384 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:43,384 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:43,384 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:43,385 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:43,385 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,385 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:43,385 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,386 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,386 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:43,386 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,386 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,386 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:43,386 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:43,387 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:43,387 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:43,387 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,387 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,388 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,389 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 9, 3, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [4, 6, 8, 9, 1, 5, 0, 3, 2, 7], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 8, 9, 3, 5, 0, 1, 7, 2], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,389 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 102.00)
2025-08-05 10:28:43,389 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:43,389 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:43,389 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,390 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,390 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,391 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,391 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [5, 9, 0, 7, 3, 2, 6, 8, 1, 4], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 0, 7, 3, 2, 6, 9, 4, 1], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,391 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 108.00)
2025-08-05 10:28:43,391 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:43,392 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:43,392 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,392 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,393 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,393 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,393 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 3, 4, 7, 0, 5, 8, 2, 6, 9], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [8, 9, 3, 6, 0, 5, 7, 4, 2, 1], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 9, 3, 6, 0, 4, 5, 7, 2, 1], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 3, 6, 8, 0, 5, 4, 7, 2, 1], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,393 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 103.00)
2025-08-05 10:28:43,394 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,395 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,395 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,395 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 7, 9, 1, 6, 8, 4, 3, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [2, 7, 4, 3, 8, 0, 1, 6, 9, 5], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 3, 6, 1, 0, 9, 8, 5], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 7, 4, 3, 0, 1, 6, 8, 5], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,395 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 102.00)
2025-08-05 10:28:43,395 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:43,395 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:43,395 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,396 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,397 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 8, 2, 4, 7, 3, 6, 9, 0, 5], 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': [1, 3, 2, 9, 0, 8, 7, 6, 5, 4], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 9, 5, 4, 0, 6, 7, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 3, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,398 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 112.00)
2025-08-05 10:28:43,398 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:43,398 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:43,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,398 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,399 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,400 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [1, 2, 4, 5, 6, 7, 3, 8, 9, 0], 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': [3, 2, 6, 8, 7, 9, 1, 5, 4, 0], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 6, 1, 7, 9, 4, 5, 8, 0], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 6, 1, 7, 5, 9, 8, 4, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,400 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 97.00)
2025-08-05 10:28:43,400 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:43,400 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:43,400 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,401 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,402 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,402 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 0, 8, 9, 2, 6, 7, 3, 4, 5], 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': [7, 3, 9, 8, 5, 1, 2, 4, 0, 6], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 9, 8, 5, 0, 4, 2, 3, 6], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 9, 4, 8, 5, 3, 2, 0, 6], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,402 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 97.00)
2025-08-05 10:28:43,402 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:43,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,403 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 114.0
2025-08-05 10:28:43,410 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:28:43,410 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78, 78.0, 78, 78, 78]
2025-08-05 10:28:43,410 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64), array([0, 5, 8, 4, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 1, 6, 9], dtype=int64), array([0, 1, 6, 2, 7, 3, 4, 5, 8, 9], dtype=int64)]
2025-08-05 10:28:43,411 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,411 - ExploitationExpert - INFO - populations: [{'tour': [4, 9, 3, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0}, {'tour': [1, 3, 4, 7, 0, 5, 8, 2, 6, 9], 'cur_cost': 103.0}, {'tour': [0, 2, 7, 9, 1, 6, 8, 4, 3, 5], 'cur_cost': 102.0}, {'tour': [1, 8, 2, 4, 7, 3, 6, 9, 0, 5], 'cur_cost': 112.0}, {'tour': [1, 2, 4, 5, 6, 7, 3, 8, 9, 0], 'cur_cost': 97.0}, {'tour': [1, 0, 8, 9, 2, 6, 7, 3, 4, 5], 'cur_cost': 97.0}, {'tour': array([4, 1, 3, 2, 9, 5, 0, 6, 8, 7], dtype=int64), 'cur_cost': 114.0}, {'tour': [0, 4, 7, 6, 3, 1, 9, 8, 5, 2], 'cur_cost': 120.0}, {'tour': [7, 8, 1, 5, 3, 4, 9, 0, 6, 2], 'cur_cost': 111.0}]
2025-08-05 10:28:43,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,412 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 133, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 133, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,412 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([4, 1, 3, 2, 9, 5, 0, 6, 8, 7], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([0, 8, 4, 3, 9, 2, 1, 7, 5, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 8, 4, 9, 2, 1, 7, 5, 6]), 'cur_cost': 125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 3, 0, 8, 4, 2, 1, 7, 5, 6]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 8, 9, 2, 1, 7, 5, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 3, 0, 8, 2, 1, 7, 5, 6]), 'cur_cost': 136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,413 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 114.00)
2025-08-05 10:28:43,413 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:43,413 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,413 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,413 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 115.0
2025-08-05 10:28:43,420 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:43,420 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78, 78.0, 78, 78, 78, 78, 78]
2025-08-05 10:28:43,421 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64), array([0, 5, 8, 4, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 1, 6, 9], dtype=int64), array([0, 1, 6, 2, 7, 3, 4, 5, 8, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 6, 1, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 6, 1, 9], dtype=int64)]
2025-08-05 10:28:43,422 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,422 - ExploitationExpert - INFO - populations: [{'tour': [4, 9, 3, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0}, {'tour': [1, 3, 4, 7, 0, 5, 8, 2, 6, 9], 'cur_cost': 103.0}, {'tour': [0, 2, 7, 9, 1, 6, 8, 4, 3, 5], 'cur_cost': 102.0}, {'tour': [1, 8, 2, 4, 7, 3, 6, 9, 0, 5], 'cur_cost': 112.0}, {'tour': [1, 2, 4, 5, 6, 7, 3, 8, 9, 0], 'cur_cost': 97.0}, {'tour': [1, 0, 8, 9, 2, 6, 7, 3, 4, 5], 'cur_cost': 97.0}, {'tour': array([4, 1, 3, 2, 9, 5, 0, 6, 8, 7], dtype=int64), 'cur_cost': 114.0}, {'tour': array([8, 5, 7, 6, 0, 9, 4, 2, 3, 1], dtype=int64), 'cur_cost': 115.0}, {'tour': [7, 8, 1, 5, 3, 4, 9, 0, 6, 2], 'cur_cost': 111.0}]
2025-08-05 10:28:43,423 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,423 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 134, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 134, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,423 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([8, 5, 7, 6, 0, 9, 4, 2, 3, 1], dtype=int64), 'cur_cost': 115.0, 'intermediate_solutions': [{'tour': array([7, 4, 0, 6, 3, 1, 9, 8, 5, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 4, 0, 3, 1, 9, 8, 5, 2]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 7, 4, 0, 1, 9, 8, 5, 2]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 7, 4, 3, 1, 9, 8, 5, 2]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 6, 7, 4, 1, 9, 8, 5, 2]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,424 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 115.00)
2025-08-05 10:28:43,424 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:43,424 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:43,424 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,424 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,424 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,425 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,425 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 9, 8, 4, 5, 0, 2, 6, 1, 3], 'cur_cost': 95.0, 'intermediate_solutions': [{'tour': [7, 8, 1, 5, 3, 4, 9, 2, 6, 0], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 5, 3, 4, 9, 2, 6, 0], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 1, 2, 5, 3, 4, 9, 0, 6], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,425 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 95.00)
2025-08-05 10:28:43,425 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:43,425 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:43,426 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 3, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [4, 6, 8, 9, 1, 5, 0, 3, 2, 7], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 8, 9, 3, 5, 0, 1, 7, 2], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 6, 8, 9, 3, 5, 0, 1, 2, 7], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [5, 9, 0, 7, 3, 2, 6, 8, 1, 4], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 8, 0, 7, 3, 2, 6, 9, 4, 1], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 5, 0, 7, 3, 2, 6, 9, 1, 4], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 4, 7, 0, 5, 8, 2, 6, 9], 'cur_cost': 103.0, 'intermediate_solutions': [{'tour': [8, 9, 3, 6, 0, 5, 7, 4, 2, 1], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 9, 3, 6, 0, 4, 5, 7, 2, 1], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 3, 6, 8, 0, 5, 4, 7, 2, 1], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 7, 9, 1, 6, 8, 4, 3, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [2, 7, 4, 3, 8, 0, 1, 6, 9, 5], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 4, 3, 6, 1, 0, 9, 8, 5], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 9, 7, 4, 3, 0, 1, 6, 8, 5], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 2, 4, 7, 3, 6, 9, 0, 5], 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': [1, 3, 2, 9, 0, 8, 7, 6, 5, 4], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 2, 9, 5, 4, 0, 6, 7, 8], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 3, 9, 5, 8, 7, 6, 0, 4], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 4, 5, 6, 7, 3, 8, 9, 0], 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': [3, 2, 6, 8, 7, 9, 1, 5, 4, 0], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 2, 6, 1, 7, 9, 4, 5, 8, 0], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 2, 6, 1, 7, 5, 9, 8, 4, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 8, 9, 2, 6, 7, 3, 4, 5], 'cur_cost': 97.0, 'intermediate_solutions': [{'tour': [7, 3, 9, 8, 5, 1, 2, 4, 0, 6], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 1, 9, 8, 5, 0, 4, 2, 3, 6], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 1, 9, 4, 8, 5, 3, 2, 0, 6], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([4, 1, 3, 2, 9, 5, 0, 6, 8, 7], dtype=int64), 'cur_cost': 114.0, 'intermediate_solutions': [{'tour': array([0, 8, 4, 3, 9, 2, 1, 7, 5, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 0, 8, 4, 9, 2, 1, 7, 5, 6]), 'cur_cost': 125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 3, 0, 8, 4, 2, 1, 7, 5, 6]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 3, 0, 8, 9, 2, 1, 7, 5, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 3, 0, 8, 2, 1, 7, 5, 6]), 'cur_cost': 136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 5, 7, 6, 0, 9, 4, 2, 3, 1], dtype=int64), 'cur_cost': 115.0, 'intermediate_solutions': [{'tour': array([7, 4, 0, 6, 3, 1, 9, 8, 5, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 4, 0, 3, 1, 9, 8, 5, 2]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([3, 6, 7, 4, 0, 1, 9, 8, 5, 2]), 'cur_cost': 106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 6, 7, 4, 3, 1, 9, 8, 5, 2]), 'cur_cost': 113.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 3, 6, 7, 4, 1, 9, 8, 5, 2]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 8, 4, 5, 0, 2, 6, 1, 3], 'cur_cost': 95.0, 'intermediate_solutions': [{'tour': [7, 8, 1, 5, 3, 4, 9, 2, 6, 0], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 8, 1, 5, 3, 4, 9, 2, 6, 0], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 1, 2, 5, 3, 4, 9, 0, 6], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:43,427 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:43,427 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,428 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=95.000, 多样性=0.907
2025-08-05 10:28:43,428 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:43,428 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:43,428 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:43,429 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07521424023627855, 'best_improvement': 0.0594059405940594}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.030878859857482024}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008888605443052788, 'recent_improvements': [-0.0372807580083116, -0.0022755022755022677, -0.019503547122206023], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 78.0, 'new_best_cost': 78.0, 'quality_improvement': 0.0, 'old_diversity': 0.6244444444444445, 'new_diversity': 0.6244444444444445, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:43,429 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:43,429 - __main__ - INFO - geometry5_10 开始进化第 3 代
2025-08-05 10:28:43,429 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:43,429 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,430 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=95.000, 多样性=0.907
2025-08-05 10:28:43,430 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:43,430 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.907
2025-08-05 10:28:43,431 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:43,432 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.624
2025-08-05 10:28:43,433 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:43,434 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:43,434 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:43,434 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:43,449 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.500, 适应度梯度: -5.850, 聚类评分: 0.000, 覆盖率: 0.061, 收敛趋势: 0.000, 多样性: 0.417
2025-08-05 10:28:43,449 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:43,450 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:43,450 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry5_10
2025-08-05 10:28:43,458 - visualization.landscape_visualizer - INFO - 插值约束: 301 个点被约束到最小值 78.00
2025-08-05 10:28:43,461 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.3%, 梯度: 1.66 → 1.54
2025-08-05 10:28:43,563 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\landscape_geometry5_10_iter_53_20250805_102843.html
2025-08-05 10:28:43,611 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry5_10\dashboard_geometry5_10_iter_53_20250805_102843.html
2025-08-05 10:28:43,611 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 53
2025-08-05 10:28:43,611 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:43,611 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1782秒
2025-08-05 10:28:43,611 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -5.85, 'local_optima_density': 0.5, 'gradient_variance': 117.6875, 'cluster_count': 0}, 'population_state': {'diversity': 0.41657894736842105, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0613, 'fitness_entropy': 0.8067719731506842, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5.850)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.061)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360923.4491177, 'performance_metrics': {}}}
2025-08-05 10:28:43,612 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:43,612 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:43,612 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:43,612 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:43,612 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,612 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:43,613 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,613 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,613 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:43,613 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:43,613 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:43,614 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:43,614 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:43,614 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:43,614 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:43,614 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,615 - ExplorationExpert - INFO - 探索路径生成完成，成本: 124.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,615 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 3, 0, 1, 7, 9, 4, 8, 6, 5], 'cur_cost': 124.0, 'intermediate_solutions': [{'tour': [4, 3, 9, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 7, 3, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 1, 3, 7, 0, 6, 2, 8, 5], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,616 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 124.00)
2025-08-05 10:28:43,616 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:43,616 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:43,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,616 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,617 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 0, 2, 7, 1, 3, 6, 9, 8, 5], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 1, 5, 9, 7, 6, 3, 4], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 4, 3], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,617 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 113.00)
2025-08-05 10:28:43,617 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:43,617 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,618 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 121.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,619 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 3, 5, 6, 7, 2, 4, 9, 8], 'cur_cost': 121.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 7, 0, 5, 8, 6, 2, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 4, 7, 0, 5, 9, 6, 2, 8], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 4, 7, 0, 5, 8, 2, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,619 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 121.00)
2025-08-05 10:28:43,619 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:43,619 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:43,619 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,620 - ExplorationExpert - INFO - 探索路径生成完成，成本: 120.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,621 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 3, 0, 1, 2, 5, 7, 9, 6, 4], 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': [8, 2, 7, 9, 1, 6, 0, 4, 3, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 9, 5, 3, 4, 8, 6, 1], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 9, 1, 6, 5, 8, 4, 3], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,621 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 120.00)
2025-08-05 10:28:43,621 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:43,621 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,621 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,621 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 120.0
2025-08-05 10:28:43,633 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:43,633 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78, 78.0, 78, 78, 78, 78, 78]
2025-08-05 10:28:43,633 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64), array([0, 5, 8, 4, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 1, 6, 9], dtype=int64), array([0, 1, 6, 2, 7, 3, 4, 5, 8, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 6, 1, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 6, 1, 9], dtype=int64)]
2025-08-05 10:28:43,636 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,636 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 0, 1, 7, 9, 4, 8, 6, 5], 'cur_cost': 124.0}, {'tour': [4, 0, 2, 7, 1, 3, 6, 9, 8, 5], 'cur_cost': 113.0}, {'tour': [0, 1, 3, 5, 6, 7, 2, 4, 9, 8], 'cur_cost': 121.0}, {'tour': [8, 3, 0, 1, 2, 5, 7, 9, 6, 4], 'cur_cost': 120.0}, {'tour': array([7, 4, 3, 6, 8, 2, 5, 1, 9, 0], dtype=int64), 'cur_cost': 120.0}, {'tour': [1, 2, 4, 5, 6, 7, 3, 8, 9, 0], 'cur_cost': 97.0}, {'tour': [1, 0, 8, 9, 2, 6, 7, 3, 4, 5], 'cur_cost': 97.0}, {'tour': [4, 1, 3, 2, 9, 5, 0, 6, 8, 7], 'cur_cost': 114.0}, {'tour': [8, 5, 7, 6, 0, 9, 4, 2, 3, 1], 'cur_cost': 115.0}, {'tour': [7, 9, 8, 4, 5, 0, 2, 6, 1, 3], 'cur_cost': 95.0}]
2025-08-05 10:28:43,636 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,637 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 135, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 135, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,637 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([7, 4, 3, 6, 8, 2, 5, 1, 9, 0], dtype=int64), 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': array([2, 8, 1, 4, 7, 3, 6, 9, 0, 5]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 2, 8, 1, 7, 3, 6, 9, 0, 5]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 2, 8, 1, 3, 6, 9, 0, 5]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 2, 8, 7, 3, 6, 9, 0, 5]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 4, 2, 8, 3, 6, 9, 0, 5]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,637 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 120.00)
2025-08-05 10:28:43,637 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:43,638 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:43,638 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,638 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,639 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,639 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,639 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 6, 9, 0, 1, 8, 5, 4, 7, 3], 'cur_cost': 85.0, 'intermediate_solutions': [{'tour': [1, 9, 4, 5, 6, 7, 3, 8, 2, 0], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 9, 8, 3, 7, 6, 5, 0], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 5, 6, 7, 8, 9, 0, 3], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,640 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 85.00)
2025-08-05 10:28:43,640 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:43,640 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:43,640 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,640 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:43,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,641 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,641 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 9, 5, 4, 8, 6, 1, 0, 2, 3], 'cur_cost': 92.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 4, 2, 6, 7, 3, 9, 5], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 6, 2, 9, 8, 0, 4, 5], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 8, 9, 2, 6, 7, 4, 5], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,641 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 92.00)
2025-08-05 10:28:43,641 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:43,641 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,641 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,642 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111.0
2025-08-05 10:28:43,648 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:28:43,648 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78, 78.0, 78, 78, 78, 78, 78, 78.0, 78]
2025-08-05 10:28:43,648 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64), array([0, 5, 8, 4, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 1, 6, 9], dtype=int64), array([0, 1, 6, 2, 7, 3, 4, 5, 8, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 6, 1, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 6, 1, 9], dtype=int64), array([0, 9, 8, 5, 4, 3, 7, 6, 2, 1], dtype=int64), array([0, 9, 5, 8, 4, 3, 7, 2, 6, 1], dtype=int64)]
2025-08-05 10:28:43,650 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,651 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 0, 1, 7, 9, 4, 8, 6, 5], 'cur_cost': 124.0}, {'tour': [4, 0, 2, 7, 1, 3, 6, 9, 8, 5], 'cur_cost': 113.0}, {'tour': [0, 1, 3, 5, 6, 7, 2, 4, 9, 8], 'cur_cost': 121.0}, {'tour': [8, 3, 0, 1, 2, 5, 7, 9, 6, 4], 'cur_cost': 120.0}, {'tour': array([7, 4, 3, 6, 8, 2, 5, 1, 9, 0], dtype=int64), 'cur_cost': 120.0}, {'tour': [2, 6, 9, 0, 1, 8, 5, 4, 7, 3], 'cur_cost': 85.0}, {'tour': [7, 9, 5, 4, 8, 6, 1, 0, 2, 3], 'cur_cost': 92.0}, {'tour': array([0, 6, 5, 4, 1, 7, 3, 2, 8, 9], dtype=int64), 'cur_cost': 111.0}, {'tour': [8, 5, 7, 6, 0, 9, 4, 2, 3, 1], 'cur_cost': 115.0}, {'tour': [7, 9, 8, 4, 5, 0, 2, 6, 1, 3], 'cur_cost': 95.0}]
2025-08-05 10:28:43,651 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,651 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 136, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 136, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,652 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([0, 6, 5, 4, 1, 7, 3, 2, 8, 9], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([3, 1, 4, 2, 9, 5, 0, 6, 8, 7]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 3, 1, 4, 9, 5, 0, 6, 8, 7]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 3, 1, 4, 5, 0, 6, 8, 7]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 3, 1, 9, 5, 0, 6, 8, 7]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 2, 3, 1, 5, 0, 6, 8, 7]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,652 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 111.00)
2025-08-05 10:28:43,652 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:43,652 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:43,652 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:43,652 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 119.0
2025-08-05 10:28:43,659 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:43,659 - ExploitationExpert - INFO - res_population_costs: [78.0, 78, 78, 78, 78.0, 78, 78, 78, 78, 78, 78.0, 78, 78]
2025-08-05 10:28:43,659 - ExploitationExpert - INFO - res_populations: [array([0, 9, 5, 4, 8, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 8, 5, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 9, 5, 8, 4, 7, 3, 2, 6, 1], dtype=int64), array([0, 1, 2, 6, 7, 3, 4, 8, 5, 9], dtype=int64), array([0, 5, 8, 4, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 1, 6, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 1, 6, 9], dtype=int64), array([0, 1, 6, 2, 7, 3, 4, 5, 8, 9], dtype=int64), array([0, 5, 8, 4, 3, 7, 2, 6, 1, 9], dtype=int64), array([0, 5, 4, 8, 7, 3, 2, 6, 1, 9], dtype=int64), array([0, 9, 8, 5, 4, 3, 7, 6, 2, 1], dtype=int64), array([0, 9, 5, 8, 4, 3, 7, 2, 6, 1], dtype=int64), array([0, 9, 1, 2, 6, 7, 3, 4, 8, 5], dtype=int64)]
2025-08-05 10:28:43,661 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:43,661 - ExploitationExpert - INFO - populations: [{'tour': [2, 3, 0, 1, 7, 9, 4, 8, 6, 5], 'cur_cost': 124.0}, {'tour': [4, 0, 2, 7, 1, 3, 6, 9, 8, 5], 'cur_cost': 113.0}, {'tour': [0, 1, 3, 5, 6, 7, 2, 4, 9, 8], 'cur_cost': 121.0}, {'tour': [8, 3, 0, 1, 2, 5, 7, 9, 6, 4], 'cur_cost': 120.0}, {'tour': array([7, 4, 3, 6, 8, 2, 5, 1, 9, 0], dtype=int64), 'cur_cost': 120.0}, {'tour': [2, 6, 9, 0, 1, 8, 5, 4, 7, 3], 'cur_cost': 85.0}, {'tour': [7, 9, 5, 4, 8, 6, 1, 0, 2, 3], 'cur_cost': 92.0}, {'tour': array([0, 6, 5, 4, 1, 7, 3, 2, 8, 9], dtype=int64), 'cur_cost': 111.0}, {'tour': array([9, 0, 1, 4, 8, 2, 5, 6, 7, 3], dtype=int64), 'cur_cost': 119.0}, {'tour': [7, 9, 8, 4, 5, 0, 2, 6, 1, 3], 'cur_cost': 95.0}]
2025-08-05 10:28:43,662 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:43,662 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 137, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 137, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:43,662 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([9, 0, 1, 4, 8, 2, 5, 6, 7, 3], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 6, 0, 9, 4, 2, 3, 1]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 5, 8, 0, 9, 4, 2, 3, 1]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 7, 5, 8, 9, 4, 2, 3, 1]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 7, 5, 0, 9, 4, 2, 3, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 7, 5, 9, 4, 2, 3, 1]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:43,663 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 119.00)
2025-08-05 10:28:43,663 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:43,663 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:43,663 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:43,663 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:43,663 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,664 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:43,664 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:43,664 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 7, 1, 5, 8, 9, 6, 2, 3, 4], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [7, 9, 8, 4, 5, 0, 2, 1, 6, 3], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 4, 5, 0, 2, 1, 6, 3], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 8, 4, 0, 2, 6, 1, 5, 3], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:43,664 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 108.00)
2025-08-05 10:28:43,665 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:43,665 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:43,666 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 0, 1, 7, 9, 4, 8, 6, 5], 'cur_cost': 124.0, 'intermediate_solutions': [{'tour': [4, 3, 9, 7, 0, 1, 6, 2, 8, 5], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 9, 7, 3, 0, 1, 6, 2, 8, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 9, 1, 3, 7, 0, 6, 2, 8, 5], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 2, 7, 1, 3, 6, 9, 8, 5], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [2, 8, 0, 1, 5, 9, 7, 6, 3, 4], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 4, 3], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 0, 1, 5, 9, 6, 7, 3, 4], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 5, 6, 7, 2, 4, 9, 8], 'cur_cost': 121.0, 'intermediate_solutions': [{'tour': [1, 3, 4, 7, 0, 5, 8, 6, 2, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 4, 7, 0, 5, 9, 6, 2, 8], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 3, 4, 7, 0, 5, 8, 2, 9], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 3, 0, 1, 2, 5, 7, 9, 6, 4], 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': [8, 2, 7, 9, 1, 6, 0, 4, 3, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 7, 9, 5, 3, 4, 8, 6, 1], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 7, 9, 1, 6, 5, 8, 4, 3], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([7, 4, 3, 6, 8, 2, 5, 1, 9, 0], dtype=int64), 'cur_cost': 120.0, 'intermediate_solutions': [{'tour': array([2, 8, 1, 4, 7, 3, 6, 9, 0, 5]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 2, 8, 1, 7, 3, 6, 9, 0, 5]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 4, 2, 8, 1, 3, 6, 9, 0, 5]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 4, 2, 8, 7, 3, 6, 9, 0, 5]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 4, 2, 8, 3, 6, 9, 0, 5]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 6, 9, 0, 1, 8, 5, 4, 7, 3], 'cur_cost': 85.0, 'intermediate_solutions': [{'tour': [1, 9, 4, 5, 6, 7, 3, 8, 2, 0], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 4, 9, 8, 3, 7, 6, 5, 0], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 5, 6, 7, 8, 9, 0, 3], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 5, 4, 8, 6, 1, 0, 2, 3], 'cur_cost': 92.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 4, 2, 6, 7, 3, 9, 5], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 6, 2, 9, 8, 0, 4, 5], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 3, 8, 9, 2, 6, 7, 4, 5], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 6, 5, 4, 1, 7, 3, 2, 8, 9], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([3, 1, 4, 2, 9, 5, 0, 6, 8, 7]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 3, 1, 4, 9, 5, 0, 6, 8, 7]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 2, 3, 1, 4, 5, 0, 6, 8, 7]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([4, 2, 3, 1, 9, 5, 0, 6, 8, 7]), 'cur_cost': 107.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([4, 9, 2, 3, 1, 5, 0, 6, 8, 7]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 0, 1, 4, 8, 2, 5, 6, 7, 3], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([7, 5, 8, 6, 0, 9, 4, 2, 3, 1]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 7, 5, 8, 0, 9, 4, 2, 3, 1]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 6, 7, 5, 8, 9, 4, 2, 3, 1]), 'cur_cost': 111.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 7, 5, 0, 9, 4, 2, 3, 1]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 0, 6, 7, 5, 9, 4, 2, 3, 1]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 1, 5, 8, 9, 6, 2, 3, 4], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [7, 9, 8, 4, 5, 0, 2, 1, 6, 3], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 9, 8, 4, 5, 0, 2, 1, 6, 3], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 9, 8, 4, 0, 2, 6, 1, 5, 3], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:43,667 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:43,667 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,669 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=85.000, 多样性=0.900
2025-08-05 10:28:43,669 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:43,669 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:43,669 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:43,670 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.012689827335136253, 'best_improvement': 0.10526315789473684}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.007352941176470878}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03874487125589041, 'recent_improvements': [-0.0022755022755022677, -0.019503547122206023, 0.07521424023627855], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 13, 'new_count': 13, 'count_change': 0, 'old_best_cost': 78.0, 'new_best_cost': 78.0, 'quality_improvement': 0.0, 'old_diversity': 0.6551282051282051, 'new_diversity': 0.6551282051282051, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:43,671 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:43,671 - __main__ - INFO - geometry5_10 开始进化第 4 代
2025-08-05 10:28:43,671 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:43,672 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:43,673 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=85.000, 多样性=0.900
2025-08-05 10:28:43,673 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:43,674 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.900
2025-08-05 10:28:43,674 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:43,677 - EliteExpert - INFO - 精英解分析完成: 精英解数量=13, 多样性=0.655
2025-08-05 10:28:43,679 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:43,679 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:43,679 - LandscapeExpert - INFO - 添加精英解数据: 13个精英解
2025-08-05 10:28:43,679 - LandscapeExpert - INFO - 数据提取成功: 23个路径, 23个适应度值
2025-08-05 10:28:43,699 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.565, 适应度梯度: -7.348, 聚类评分: 0.000, 覆盖率: 0.062, 收敛趋势: 0.000, 多样性: 0.353
2025-08-05 10:28:43,699 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:43,699 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:43,699 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry5_10
2025-08-05 10:28:43,705 - visualization.landscape_visualizer - INFO - 插值约束: 416 个点被约束到最小值 78.00
2025-08-05 10:28:43,708 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.2%, 梯度: 1.86 → 1.76
