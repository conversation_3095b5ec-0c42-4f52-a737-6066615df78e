"""
Exploration strategy implementations for the intelligent strategy selection system.

This module implements various exploration strategies designed to help individuals
escape local optima and explore new regions of the search space.
"""

import random
import time
import numpy as np
from typing import List, Dict, Optional, Any, Callable
from abc import ABC, abstractmethod
import logging

from ..core.individual_state import IndividualState, IndividualContext
from ..core.data_structures import StrategyAssignment, ExecutionResult, ExplorationParameters, StrategyType, ExecutionStatus
from ..core.strategy_interfaces import StrategyExecutionInterface


class ExplorationStrategy(ABC):
    """
    Abstract base class for exploration strategies.
    
    All exploration strategies must implement the execute method
    to perform exploration operations on individuals.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the exploration strategy."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.execution_count = 0
        self.success_count = 0
    
    @abstractmethod
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """
        Execute the exploration strategy.
        
        Args:
            assignment: Strategy assignment with parameters
            context: Individual context for execution
            fitness_function: Fitness evaluation function
            
        Returns:
            Execution result
        """
        pass
    
    def get_success_rate(self) -> float:
        """Get the success rate of this strategy."""
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count


class StrongExplorationStrategy(ExplorationStrategy):
    """
    Strong exploration strategy for escaping deep local optima.
    
    This strategy uses high-intensity perturbations and large search radius
    to help individuals escape from stagnation and explore distant regions.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute strong exploration strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploration parameters
            params = assignment.exploration_params or ExplorationParameters()
            
            # Adjust parameters for strong exploration
            exploration_intensity = max(0.7, params.exploration_intensity)
            perturbation_strength = max(0.8, params.perturbation_strength)
            search_radius = max(0.5, params.search_radius)
            
            # Perform strong exploration
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Multiple large perturbations
            num_perturbations = int(10 * exploration_intensity)
            
            for i in range(num_perturbations):
                # Generate large perturbation
                perturbed_solution = self._generate_strong_perturbation(
                    best_solution, perturbation_strength, search_radius
                )
                
                # Evaluate fitness
                new_fitness = fitness_function(perturbed_solution)
                operations_count += 1
                
                # Update best if improved
                if new_fitness < best_fitness:  # Minimization
                    best_solution = perturbed_solution.copy()
                    best_fitness = new_fitness
                
                # Early termination if significant improvement
                improvement = context.current_fitness - new_fitness
                if improvement > 0.01 * abs(context.current_fitness):
                    break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=num_perturbations,
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'perturbations_performed': num_perturbations,
                    'exploration_intensity': exploration_intensity,
                    'perturbation_strength': perturbation_strength
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in strong exploration strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _generate_strong_perturbation(self, 
                                    solution: List[int],
                                    perturbation_strength: float,
                                    search_radius: float) -> List[int]:
        """Generate a strong perturbation of the solution."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Number of changes based on perturbation strength
        num_changes = max(1, int(n * perturbation_strength * search_radius))
        
        # Perform multiple random swaps/reversals
        for _ in range(num_changes):
            if random.random() < 0.7:  # 70% chance of reversal, 30% chance of swap
                # Random reversal (2-opt style)
                i, j = sorted(random.sample(range(n), 2))
                perturbed[i:j+1] = perturbed[i:j+1][::-1]
            else:
                # Random swap
                i, j = random.sample(range(n), 2)
                perturbed[i], perturbed[j] = perturbed[j], perturbed[i]
        
        return perturbed


class BalancedExplorationStrategy(ExplorationStrategy):
    """
    Balanced exploration strategy with controlled perturbations.
    
    This strategy provides moderate exploration while maintaining some
    exploitation characteristics for balanced search behavior.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute balanced exploration strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploration parameters
            params = assignment.exploration_params or ExplorationParameters()
            
            # Perform balanced exploration
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Adaptive number of perturbations
            num_perturbations = max(5, int(15 * params.exploration_intensity))
            
            for i in range(num_perturbations):
                # Generate balanced perturbation
                if i < num_perturbations // 2:
                    # First half: moderate perturbations
                    perturbed_solution = self._generate_moderate_perturbation(
                        best_solution, params
                    )
                else:
                    # Second half: local improvements
                    perturbed_solution = self._generate_local_perturbation(
                        best_solution, params
                    )
                
                # Evaluate fitness
                new_fitness = fitness_function(perturbed_solution)
                operations_count += 1
                
                # Update best if improved
                if new_fitness < best_fitness:
                    best_solution = perturbed_solution.copy()
                    best_fitness = new_fitness
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=num_perturbations,
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'perturbations_performed': num_perturbations
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in balanced exploration strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _generate_moderate_perturbation(self, 
                                      solution: List[int],
                                      params: ExplorationParameters) -> List[int]:
        """Generate a moderate perturbation."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Moderate number of changes
        num_changes = max(1, int(n * params.perturbation_strength * 0.3))
        
        for _ in range(num_changes):
            if random.random() < params.diversification_bias:
                # Diversification move
                i, j = sorted(random.sample(range(n), 2))
                perturbed[i:j+1] = perturbed[i:j+1][::-1]
            else:
                # Local move
                i = random.randint(0, n-1)
                j = (i + random.randint(1, min(5, n-1))) % n
                perturbed[i], perturbed[j] = perturbed[j], perturbed[i]
        
        return perturbed
    
    def _generate_local_perturbation(self, 
                                   solution: List[int],
                                   params: ExplorationParameters) -> List[int]:
        """Generate a local perturbation."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Small local changes
        if random.random() < params.mutation_probability:
            # Single swap
            i, j = random.sample(range(n), 2)
            perturbed[i], perturbed[j] = perturbed[j], perturbed[i]
        else:
            # Small reversal
            i = random.randint(0, n-3)
            j = i + random.randint(1, min(3, n-i-1))
            perturbed[i:j+1] = perturbed[i:j+1][::-1]
        
        return perturbed


class IntelligentExplorationStrategy(ExplorationStrategy):
    """
    Intelligent exploration strategy with adaptive behavior.
    
    This strategy adapts its exploration behavior based on landscape
    characteristics and individual state information.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute intelligent exploration strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Get exploration parameters
            params = assignment.exploration_params or ExplorationParameters()
            
            # Adapt parameters based on context
            adapted_params = self._adapt_parameters(params, context)
            
            # Perform intelligent exploration
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            operations_count = 0
            
            # Multi-phase exploration
            phases = self._plan_exploration_phases(adapted_params, context)
            
            for phase_name, phase_params in phases.items():
                phase_result = self._execute_exploration_phase(
                    best_solution, best_fitness, phase_params, fitness_function
                )
                
                if phase_result['improved']:
                    best_solution = phase_result['solution']
                    best_fitness = phase_result['fitness']
                
                operations_count += phase_result['operations']
                
                # Early termination if significant improvement
                improvement = context.current_fitness - best_fitness
                if improvement > 0.05 * abs(context.current_fitness):
                    break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=len(phases),
                operations_count=operations_count,
                metadata={
                    'parameters': params.to_dict(),
                    'adapted_parameters': adapted_params.to_dict(),
                    'phases_executed': list(phases.keys())
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in intelligent exploration strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _adapt_parameters(self, 
                         params: ExplorationParameters,
                         context: IndividualContext) -> ExplorationParameters:
        """Adapt exploration parameters based on context."""
        # Create adapted parameters
        adapted = ExplorationParameters(
            exploration_intensity=params.exploration_intensity,
            perturbation_strength=params.perturbation_strength,
            diversification_bias=params.diversification_bias,
            search_radius=params.search_radius,
            mutation_probability=params.mutation_probability,
            region_focus_weight=params.region_focus_weight,
            novelty_seeking_factor=params.novelty_seeking_factor
        )
        
        # Adapt based on stagnation level
        stagnation_multiplier = {
            'NONE': 1.0,
            'LOW': 1.2,
            'MODERATE': 1.5,
            'HIGH': 2.0,
            'CRITICAL': 2.5
        }.get(context.individual_state.stagnation_level.value, 1.0)
        
        adapted.exploration_intensity = min(1.0, adapted.exploration_intensity * stagnation_multiplier)
        adapted.perturbation_strength = min(1.0, adapted.perturbation_strength * stagnation_multiplier)
        
        # Adapt based on local ruggedness
        if context.local_ruggedness > 0.7:
            # High ruggedness - increase diversification
            adapted.diversification_bias = min(1.0, adapted.diversification_bias * 1.3)
            adapted.search_radius = min(1.0, adapted.search_radius * 1.2)
        
        # Adapt based on gradient strength
        if context.gradient_strength < 0.3:
            # Weak gradients - increase novelty seeking
            adapted.novelty_seeking_factor = min(1.0, adapted.novelty_seeking_factor * 1.4)
        
        return adapted
    
    def _plan_exploration_phases(self, 
                               params: ExplorationParameters,
                               context: IndividualContext) -> Dict[str, Dict]:
        """Plan multi-phase exploration strategy."""
        phases = {}
        
        # Phase 1: Diversification
        if params.diversification_bias > 0.5:
            phases['diversification'] = {
                'type': 'diversification',
                'intensity': params.diversification_bias,
                'operations': max(3, int(8 * params.exploration_intensity))
            }
        
        # Phase 2: Novelty seeking
        if params.novelty_seeking_factor > 0.6:
            phases['novelty_seeking'] = {
                'type': 'novelty',
                'intensity': params.novelty_seeking_factor,
                'operations': max(2, int(5 * params.exploration_intensity))
            }
        
        # Phase 3: Adaptive perturbation
        phases['adaptive_perturbation'] = {
            'type': 'adaptive',
            'intensity': params.perturbation_strength,
            'operations': max(5, int(10 * params.exploration_intensity))
        }
        
        return phases
    
    def _execute_exploration_phase(self, 
                                 current_solution: List[int],
                                 current_fitness: float,
                                 phase_params: Dict,
                                 fitness_function: Callable) -> Dict[str, Any]:
        """Execute a specific exploration phase."""
        best_solution = current_solution.copy()
        best_fitness = current_fitness
        operations = 0
        
        phase_type = phase_params['type']
        intensity = phase_params['intensity']
        max_operations = phase_params['operations']
        
        for _ in range(max_operations):
            if phase_type == 'diversification':
                perturbed = self._diversification_move(current_solution, intensity)
            elif phase_type == 'novelty':
                perturbed = self._novelty_seeking_move(current_solution, intensity)
            else:  # adaptive
                perturbed = self._adaptive_perturbation_move(current_solution, intensity)
            
            new_fitness = fitness_function(perturbed)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = perturbed.copy()
                best_fitness = new_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < current_fitness,
            'operations': operations
        }
    
    def _diversification_move(self, solution: List[int], intensity: float) -> List[int]:
        """Generate a diversification move."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Large-scale rearrangement
        num_segments = max(2, int(4 * intensity))
        segment_size = n // num_segments
        
        # Shuffle segments
        segments = []
        for i in range(0, n, segment_size):
            segments.append(perturbed[i:i+segment_size])
        
        random.shuffle(segments)
        
        # Reconstruct solution
        result = []
        for segment in segments:
            result.extend(segment)
        
        return result[:n]  # Ensure correct length
    
    def _novelty_seeking_move(self, solution: List[int], intensity: float) -> List[int]:
        """Generate a novelty-seeking move."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Random insertion moves for novelty
        num_moves = max(1, int(n * intensity * 0.2))
        
        for _ in range(num_moves):
            # Remove element and insert elsewhere
            remove_idx = random.randint(0, n-1)
            element = perturbed.pop(remove_idx)
            insert_idx = random.randint(0, len(perturbed))
            perturbed.insert(insert_idx, element)
        
        return perturbed
    
    def _adaptive_perturbation_move(self, solution: List[int], intensity: float) -> List[int]:
        """Generate an adaptive perturbation move."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Adaptive 2-opt with variable range
        range_size = max(2, int(n * intensity * 0.3))
        
        start_idx = random.randint(0, n - range_size)
        end_idx = start_idx + random.randint(2, range_size)
        end_idx = min(end_idx, n - 1)
        
        # Reverse the selected range
        perturbed[start_idx:end_idx+1] = perturbed[start_idx:end_idx+1][::-1]
        
        return perturbed
