"""
Comprehensive integration tests for the Intelligent Strategy Selection System.

This module contains tests to validate the integration between the intelligent
strategy system and the EoH-TSP-Solver framework.
"""

import unittest
import numpy as np
import time
from typing import List, Dict, Any
import sys
import os

# Add the src directory to the path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from intelligent_strategy.integration import (
    get_eoh_integrator, 
    integrate_with_eoh_evolution,
    EoHStrategyAdapter,
    EoHSystemIntegrator
)
from intelligent_strategy.core.data_structures import StrategyType, StrategyAssignment
from intelligent_strategy.core.individual_state import IndividualState, StagnationLevel
from intelligent_strategy.system import IntelligentStrategySystem


class TestEoHIntegration(unittest.TestCase):
    """Test cases for EoH-TSP-Solver integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'enabled': True,
            'integration_mode': 'full',
            'llm_interface': {
                'provider': 'mock',
                'fallback_enabled': True
            },
            'landscape_analysis': {
                'enable_caching': False
            }
        }
        
        # Create test TSP instance
        self.n_cities = 10
        self.pop_size = 5
        self.distance_matrix = self._create_test_distance_matrix()
        self.populations = self._create_test_population()
        self.landscape_report = self._create_test_landscape_report()
        self.strategies = self._create_test_strategies()
    
    def _create_test_distance_matrix(self) -> np.ndarray:
        """Create a test distance matrix."""
        # Create symmetric distance matrix
        matrix = np.random.rand(self.n_cities, self.n_cities) * 100
        matrix = (matrix + matrix.T) / 2  # Make symmetric
        np.fill_diagonal(matrix, 0)  # Zero diagonal
        return matrix
    
    def _create_test_population(self) -> List[Dict]:
        """Create a test population."""
        population = []
        for i in range(self.pop_size):
            tour = np.random.permutation(self.n_cities).astype(np.int64)
            cost = np.random.uniform(100, 500)
            population.append({
                "tour": tour,
                "cur_cost": cost
            })
        return population
    
    def _create_test_landscape_report(self) -> Dict[str, Any]:
        """Create a test landscape report."""
        return {
            'ruggedness': {
                'autocorrelation': 0.6,
                'variance': 0.4
            },
            'modality': {
                'estimated_peaks': 5,
                'peak_distribution': 'scattered'
            },
            'diversity': {
                'diversity_index': 0.7,
                'population_spread': 0.5
            }
        }
    
    def _create_test_strategies(self) -> Dict[int, str]:
        """Create test strategy assignments."""
        return {i: 'exploration' for i in range(self.pop_size)}
    
    def test_integrator_initialization(self):
        """Test integrator initialization."""
        integrator = get_eoh_integrator(self.config)
        
        self.assertIsInstance(integrator, EoHSystemIntegrator)
        self.assertTrue(integrator.is_enabled)
        self.assertEqual(integrator.integration_mode, 'full')
        self.assertIsInstance(integrator.adapter, EoHStrategyAdapter)
    
    def test_basic_integration_workflow(self):
        """Test basic integration workflow."""
        # Perform integration
        updated_populations = integrate_with_eoh_evolution(
            populations=self.populations,
            strategies=self.strategies,
            landscape_report=self.landscape_report,
            distance_matrix=self.distance_matrix,
            iteration=0,
            res_populations=self.populations[:2],
            config=self.config
        )
        
        # Validate results
        self.assertIsInstance(updated_populations, list)
        self.assertEqual(len(updated_populations), len(self.populations))
        
        # Check that each individual has required fields
        for individual in updated_populations:
            self.assertIn('tour', individual)
            self.assertIn('cur_cost', individual)
            self.assertIsInstance(individual['tour'], np.ndarray)
            self.assertIsInstance(individual['cur_cost'], (int, float))
    
    def test_strategy_selection(self):
        """Test intelligent strategy selection."""
        system = IntelligentStrategySystem(self.config)
        
        # Select strategies
        assignments = system.select_strategies(
            population=self.populations,
            iteration=0,
            landscape_report=self.landscape_report,
            distance_matrix=self.distance_matrix
        )
        
        # Validate assignments
        self.assertIsInstance(assignments, dict)
        self.assertEqual(len(assignments), len(self.populations))
        
        for individual_id, assignment in assignments.items():
            self.assertIsInstance(assignment, StrategyAssignment)
            self.assertIsInstance(assignment.strategy_type, StrategyType)
            self.assertIsInstance(assignment.confidence, float)
            self.assertGreaterEqual(assignment.confidence, 0.0)
            self.assertLessEqual(assignment.confidence, 1.0)
    
    def test_strategy_execution(self):
        """Test strategy execution."""
        system = IntelligentStrategySystem(self.config)
        
        # Create test assignments
        assignments = {
            i: StrategyAssignment(
                individual_id=i,
                strategy_type=StrategyType.BALANCED_EXPLORATION,
                confidence=0.7,
                reasoning="Test assignment"
            ) for i in range(len(self.populations))
        }
        
        # Execute strategies
        results = system.execute_strategies(
            strategy_assignments=assignments,
            population=self.populations,
            distance_matrix=self.distance_matrix
        )
        
        # Validate results
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), len(assignments))
        
        for result in results:
            self.assertIsInstance(result.individual_id, int)
            self.assertIsInstance(result.strategy_type, StrategyType)
            self.assertIsInstance(result.success, bool)
            self.assertIsInstance(result.execution_time, float)
            self.assertGreaterEqual(result.execution_time, 0.0)
    
    def test_individual_state_monitoring(self):
        """Test individual state monitoring."""
        integrator = get_eoh_integrator(self.config)
        adapter = integrator.adapter
        
        # Convert population and update states
        eoh_individuals = adapter._convert_population_to_internal(self.populations)
        adapter._update_individual_states(eoh_individuals, self.distance_matrix, 0)
        
        # Validate individual states
        self.assertEqual(len(adapter.individual_states), len(self.populations))
        
        for individual_id, state in adapter.individual_states.items():
            self.assertIsInstance(state, IndividualState)
            self.assertEqual(state.individual_id, individual_id)
            self.assertIsInstance(state.fitness_value, float)
            self.assertIsInstance(state.stagnation_level, StagnationLevel)
    
    def test_landscape_analysis(self):
        """Test landscape analysis enhancement."""
        integrator = get_eoh_integrator(self.config)
        adapter = integrator.adapter
        
        # Convert population
        eoh_individuals = adapter._convert_population_to_internal(self.populations)
        
        # Analyze landscape
        landscape_features = adapter._analyze_landscape(
            eoh_individuals, self.distance_matrix, self.landscape_report
        )
        
        # Validate landscape features
        self.assertIsNotNone(landscape_features)
        self.assertIsInstance(landscape_features.global_ruggedness, float)
        self.assertIsInstance(landscape_features.modality, int)
        self.assertIsInstance(landscape_features.population_diversity, float)
    
    def test_collaborative_escape(self):
        """Test collaborative escape coordination."""
        integrator = get_eoh_integrator(self.config)
        escape_coordinator = integrator.adapter.escape_coordinator
        
        # Create stagnated individual
        stagnated_state = IndividualState(
            individual_id=1,
            fitness_value=400.0,
            fitness_rank=4,
            fitness_percentile=0.8,
            stagnation_duration=8,
            stagnation_level=StagnationLevel.HIGH
        )
        
        # Request collaboration
        collaboration_info = escape_coordinator.request_collaborative_escape(
            individual_id="1",
            individual_state=stagnated_state,
            current_fitness=400.0,
            solution_features={'tour_length': 10, 'avg_edge_length': 5.0}
        )
        
        # Validate collaboration (may be None if no collaboration needed)
        if collaboration_info:
            self.assertIn('group_id', collaboration_info)
            self.assertIn('coordination_strategy', collaboration_info)
            self.assertIn('recommended_strategy', collaboration_info)
    
    def test_strategy_coordination(self):
        """Test strategy execution coordination."""
        integrator = get_eoh_integrator(self.config)
        strategy_coordinator = integrator.adapter.strategy_coordinator
        
        # Request strategy execution
        decision = strategy_coordinator.request_strategy_execution(
            individual_id="test_individual",
            strategy_type=StrategyType.MODERATE_EXPLOITATION,
            priority=0.6,
            resource_requirements={'cpu': 0.2, 'memory': 0.1}
        )
        
        # Validate decision
        self.assertIn('decision', decision)
        self.assertIn(decision['decision'], ['execute_immediately', 'queued', 'delay'])
    
    def test_performance_monitoring(self):
        """Test performance monitoring."""
        integrator = get_eoh_integrator(self.config)
        
        # Perform some integration to generate data
        integrate_with_eoh_evolution(
            populations=self.populations,
            strategies=self.strategies,
            landscape_report=self.landscape_report,
            distance_matrix=self.distance_matrix,
            iteration=0,
            res_populations=self.populations[:2],
            config=self.config
        )
        
        # Get statistics
        stats = integrator.get_system_status()
        
        # Validate statistics
        self.assertIn('enabled', stats)
        self.assertIn('integration_mode', stats)
        self.assertIn('adapter_statistics', stats)
        
        adapter_stats = stats['adapter_statistics']
        self.assertIn('integration_stats', adapter_stats)
        self.assertIn('total_strategy_selections', adapter_stats['integration_stats'])
    
    def test_error_handling(self):
        """Test error handling and fallback behavior."""
        # Test with invalid configuration
        invalid_config = {
            'enabled': True,
            'integration_mode': 'invalid_mode'
        }
        
        # Should not raise exception, should use fallback
        try:
            updated_populations = integrate_with_eoh_evolution(
                populations=self.populations,
                strategies=self.strategies,
                landscape_report=self.landscape_report,
                distance_matrix=self.distance_matrix,
                iteration=0,
                res_populations=self.populations[:2],
                config=invalid_config
            )
            
            # Should return original population on error
            self.assertEqual(len(updated_populations), len(self.populations))
            
        except Exception as e:
            self.fail(f"Integration should handle errors gracefully, but raised: {e}")
    
    def test_disabled_integration(self):
        """Test behavior when integration is disabled."""
        disabled_config = {
            'enabled': False
        }
        
        # Should return original population unchanged
        updated_populations = integrate_with_eoh_evolution(
            populations=self.populations,
            strategies=self.strategies,
            landscape_report=self.landscape_report,
            distance_matrix=self.distance_matrix,
            iteration=0,
            res_populations=self.populations[:2],
            config=disabled_config
        )
        
        # Should be identical to input
        self.assertEqual(len(updated_populations), len(self.populations))
        for i, (original, updated) in enumerate(zip(self.populations, updated_populations)):
            np.testing.assert_array_equal(original['tour'], updated['tour'])
            self.assertEqual(original['cur_cost'], updated['cur_cost'])
    
    def test_performance_benchmarks(self):
        """Test performance benchmarks."""
        # Measure integration time
        start_time = time.time()
        
        for _ in range(5):  # Run multiple iterations
            integrate_with_eoh_evolution(
                populations=self.populations,
                strategies=self.strategies,
                landscape_report=self.landscape_report,
                distance_matrix=self.distance_matrix,
                iteration=0,
                res_populations=self.populations[:2],
                config=self.config
            )
        
        total_time = time.time() - start_time
        avg_time_per_iteration = total_time / 5
        
        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(avg_time_per_iteration, 2.0, 
                       f"Integration too slow: {avg_time_per_iteration:.3f}s per iteration")
        
        print(f"Performance: {avg_time_per_iteration:.3f}s per iteration")


class TestSystemComponents(unittest.TestCase):
    """Test individual system components."""
    
    def test_strategy_factory(self):
        """Test strategy factory functionality."""
        from intelligent_strategy.strategies import get_strategy_factory
        
        factory = get_strategy_factory()
        
        # Test strategy creation
        for strategy_type in StrategyType:
            strategy = factory.create_strategy(strategy_type)
            self.assertIsNotNone(strategy)
        
        # Test strategy statistics
        stats = factory.get_strategy_statistics()
        self.assertIn('total_strategies', stats)
        self.assertGreater(stats['total_strategies'], 0)
    
    def test_llm_interface_fallback(self):
        """Test LLM interface fallback behavior."""
        from intelligent_strategy.llm_interface import LLMStrategyInterface
        
        # Configure with mock provider
        config = {
            'provider': 'mock',
            'fallback_enabled': True
        }
        
        llm_interface = LLMStrategyInterface(config)
        
        # Should not raise exception even with mock provider
        try:
            # This should use fallback strategy selection
            assignments = llm_interface.select_strategies(
                landscape_analysis=None,
                individual_states=[],
                population_context={}
            )
            self.assertIsInstance(assignments, dict)
        except Exception as e:
            self.fail(f"LLM interface should handle fallback gracefully: {e}")


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
