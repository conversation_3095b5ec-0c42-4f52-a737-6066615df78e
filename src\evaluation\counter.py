"""
核心评估计数器实现

包含线程安全的评估计数器和Numba兼容的高性能计数器。
"""

import time
import threading
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import numpy as np

try:
    from numba import jit, types
    from numba.typed import Dict as NumbaDict
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    jit = lambda *args, **kwargs: lambda f: f

from .types import (
    EvaluationType, AlgorithmPhase, EvaluationComplexity,
    EvaluationRecord, EvaluationStatistics
)

class EvaluationCounter:
    """线程安全的评估计数器"""
    
    def __init__(self, 
                 enable_detailed_logging: bool = False,
                 max_records: int = 10000,
                 batch_size: int = 100):
        """
        初始化评估计数器
        
        Args:
            enable_detailed_logging: 是否启用详细记录
            max_records: 最大记录数量
            batch_size: 批量处理大小
        """
        self._lock = threading.RLock()
        self._enable_detailed_logging = enable_detailed_logging
        self._max_records = max_records
        self._batch_size = batch_size
        
        # 基础统计
        self._statistics = EvaluationStatistics()
        
        # 详细记录
        self._detailed_records: deque = deque(maxlen=max_records)
        self._batch_buffer: List[EvaluationRecord] = []
        
        # 当前阶段追踪
        self._current_phase = AlgorithmPhase.INITIALIZATION
        self._current_iteration: Optional[int] = None
        
        # 性能优化
        self._last_flush_time = time.time()
        self._flush_interval = 1.0  # 1秒刷新一次
    
    def increment(self, 
                  evaluation_type: EvaluationType,
                  count: int = 1,
                  cost_value: Optional[float] = None,
                  improvement: Optional[float] = None,
                  complexity: EvaluationComplexity = EvaluationComplexity.O_N,
                  metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        增加评估计数
        
        Args:
            evaluation_type: 评估类型
            count: 评估次数
            cost_value: 成本值
            improvement: 改进量
            complexity: 复杂度
            metadata: 元数据
        """
        with self._lock:
            current_time = time.time()
            thread_id = threading.get_ident()
            
            # 更新基础统计
            self._statistics.total_count += count
            self._statistics.last_update_time = current_time
            self._statistics.by_type[evaluation_type] += count
            self._statistics.by_phase[self._current_phase] += count
            self._statistics.by_complexity[complexity] += count
            
            # 更新线程统计
            if thread_id not in self._statistics.by_thread:
                self._statistics.by_thread[thread_id] = 0
            self._statistics.by_thread[thread_id] += count
            
            # 更新成本和改进统计
            if cost_value is not None:
                self._statistics.update_cost_statistics(cost_value)
            
            if improvement is not None:
                self._statistics.update_improvement_statistics(improvement)
            
            # 详细记录
            if self._enable_detailed_logging:
                record = EvaluationRecord(
                    timestamp=current_time,
                    evaluation_type=evaluation_type,
                    algorithm_phase=self._current_phase,
                    thread_id=thread_id,
                    iteration=self._current_iteration,
                    count=count,
                    cost_value=cost_value,
                    improvement=improvement,
                    complexity=complexity,
                    metadata=metadata or {}
                )
                
                self._batch_buffer.append(record)
                
                # 批量刷新
                if (len(self._batch_buffer) >= self._batch_size or 
                    current_time - self._last_flush_time > self._flush_interval):
                    self._flush_batch_updates()
    
    def _flush_batch_updates(self) -> None:
        """刷新批量更新"""
        if self._batch_buffer:
            self._detailed_records.extend(self._batch_buffer)
            self._batch_buffer.clear()
            self._last_flush_time = time.time()
    
    def set_current_phase(self, phase: AlgorithmPhase, iteration: Optional[int] = None) -> None:
        """设置当前算法阶段"""
        with self._lock:
            self._current_phase = phase
            self._current_iteration = iteration
    
    def get_statistics(self) -> EvaluationStatistics:
        """获取统计信息"""
        with self._lock:
            # 确保批量更新已刷新
            self._flush_batch_updates()
            return self._statistics
    
    def get_detailed_records(self, 
                           evaluation_type: Optional[EvaluationType] = None,
                           phase: Optional[AlgorithmPhase] = None,
                           limit: Optional[int] = None) -> List[EvaluationRecord]:
        """获取详细记录"""
        with self._lock:
            self._flush_batch_updates()
            
            records = list(self._detailed_records)
            
            # 过滤
            if evaluation_type is not None:
                records = [r for r in records if r.evaluation_type == evaluation_type]
            
            if phase is not None:
                records = [r for r in records if r.algorithm_phase == phase]
            
            # 限制数量
            if limit is not None:
                records = records[-limit:]
            
            return records
    
    def reset(self) -> None:
        """重置计数器"""
        with self._lock:
            self._statistics = EvaluationStatistics()
            self._detailed_records.clear()
            self._batch_buffer.clear()
            self._current_phase = AlgorithmPhase.INITIALIZATION
            self._current_iteration = None
    
    def get_memory_usage_mb(self) -> float:
        """获取内存使用量（MB）"""
        import sys
        with self._lock:
            total_size = sys.getsizeof(self._statistics)
            total_size += sys.getsizeof(self._detailed_records)
            total_size += sum(sys.getsizeof(record) for record in self._detailed_records)
            total_size += sys.getsizeof(self._batch_buffer)
            total_size += sum(sys.getsizeof(record) for record in self._batch_buffer)
            
            return total_size / (1024 * 1024)

if NUMBA_AVAILABLE:
    @jit(nopython=True)
    def _numba_increment_counter(counters: np.ndarray, index: int, count: int) -> None:
        """Numba优化的计数器增量函数"""
        counters[index] += count
    
    @jit(nopython=True)
    def _numba_get_total(counters: np.ndarray) -> int:
        """Numba优化的总计获取函数"""
        return np.sum(counters)

class NumbaCounterWrapper:
    """Numba兼容的高性能计数器包装器"""
    
    def __init__(self):
        """初始化Numba计数器"""
        if not NUMBA_AVAILABLE:
            raise RuntimeError("Numba不可用，无法使用NumbaCounterWrapper")
        
        # 创建计数器数组
        self._eval_type_count = len(EvaluationType)
        self._phase_count = len(AlgorithmPhase)
        self._complexity_count = len(EvaluationComplexity)
        
        self._type_counters = np.zeros(self._eval_type_count, dtype=np.int64)
        self._phase_counters = np.zeros(self._phase_count, dtype=np.int64)
        self._complexity_counters = np.zeros(self._complexity_count, dtype=np.int64)
        self._total_counter = np.array([0], dtype=np.int64)
        
        # 创建映射
        self._type_to_index = {eval_type: i for i, eval_type in enumerate(EvaluationType)}
        self._phase_to_index = {phase: i for i, phase in enumerate(AlgorithmPhase)}
        self._complexity_to_index = {comp: i for i, comp in enumerate(EvaluationComplexity)}
        
        self._current_phase_index = 0  # 默认为INITIALIZATION
        
        # 线程锁
        self._lock = threading.Lock()
    
    def increment(self, 
                  evaluation_type: EvaluationType,
                  count: int = 1,
                  complexity: EvaluationComplexity = EvaluationComplexity.O_N) -> None:
        """增加评估计数"""
        with self._lock:
            type_index = self._type_to_index[evaluation_type]
            complexity_index = self._complexity_to_index[complexity]
            
            _numba_increment_counter(self._type_counters, type_index, count)
            _numba_increment_counter(self._phase_counters, self._current_phase_index, count)
            _numba_increment_counter(self._complexity_counters, complexity_index, count)
            _numba_increment_counter(self._total_counter, 0, count)
    
    def set_current_phase(self, phase: AlgorithmPhase) -> None:
        """设置当前阶段"""
        with self._lock:
            self._current_phase_index = self._phase_to_index[phase]
    
    def get_total_count(self) -> int:
        """获取总计数"""
        return int(_numba_get_total(self._total_counter))
    
    def get_type_count(self, evaluation_type: EvaluationType) -> int:
        """获取指定类型的计数"""
        index = self._type_to_index[evaluation_type]
        return int(self._type_counters[index])
    
    def get_phase_count(self, phase: AlgorithmPhase) -> int:
        """获取指定阶段的计数"""
        index = self._phase_to_index[phase]
        return int(self._phase_counters[index])
    
    def get_statistics(self) -> EvaluationStatistics:
        """获取统计信息"""
        with self._lock:
            stats = EvaluationStatistics()
            stats.total_count = self.get_total_count()
            stats.start_time = getattr(self, '_start_time', time.time())
            stats.last_update = time.time()

            # 按类型统计
            for eval_type in EvaluationType:
                stats.by_type[eval_type] = self.get_type_count(eval_type)

            # 按阶段统计
            for phase in AlgorithmPhase:
                stats.by_phase[phase] = self.get_phase_count(phase)

            # 按复杂度统计
            for i, complexity in enumerate(EvaluationComplexity):
                stats.by_complexity[complexity] = int(self._complexity_counters[i])

            return stats

    def get_memory_usage_mb(self) -> float:
        """获取内存使用量（MB）"""
        # NumbaCounterWrapper的内存使用很小
        return 0.01

    def get_detailed_records(self, limit: Optional[int] = None) -> List:
        """获取详细记录（NumbaCounterWrapper不支持详细记录）"""
        return []
    
    def reset(self) -> None:
        """重置计数器"""
        with self._lock:
            self._type_counters.fill(0)
            self._phase_counters.fill(0)
            self._complexity_counters.fill(0)
            self._total_counter.fill(0)
            self._current_phase_index = 0
            self._start_time = time.time()
