2025-08-05 10:28:44,309 - __main__ - INFO - geometry6_15 开始进化第 1 代
2025-08-05 10:28:44,309 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:44,310 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:44,311 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=148.000, 多样性=0.942
2025-08-05 10:28:44,312 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:44,313 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.942
2025-08-05 10:28:44,314 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:44,316 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:44,316 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:44,316 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:44,316 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:44,322 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -6.460, 聚类评分: 0.000, 覆盖率: 0.064, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:44,323 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:44,323 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:44,323 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry6_15
2025-08-05 10:28:44,329 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.4%, 梯度: 2.62 → 2.40
2025-08-05 10:28:44,424 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\landscape_geometry6_15_iter_56_20250805_102844.html
2025-08-05 10:28:44,496 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\dashboard_geometry6_15_iter_56_20250805_102844.html
2025-08-05 10:28:44,496 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 56
2025-08-05 10:28:44,497 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:44,497 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1810秒
2025-08-05 10:28:44,498 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 112, 'max_size': 500, 'hits': 0, 'misses': 112, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 383, 'misses': 182, 'hit_rate': 0.6778761061946903, 'evictions': 82, 'ttl': 7200}}
2025-08-05 10:28:44,499 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -6.459999999999999, 'local_optima_density': 0.2, 'gradient_variance': 1395.5524, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0643, 'fitness_entropy': 0.9756149631508355, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -6.460)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.064)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360924.3239532, 'performance_metrics': {}}}
2025-08-05 10:28:44,499 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:44,499 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:44,500 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:44,500 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:44,500 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,501 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:44,501 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,501 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:44,501 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:44,501 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,501 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:44,501 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:44,502 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:44,502 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:44,502 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:44,502 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,503 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,503 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,503 - ExplorationExpert - INFO - 探索路径生成完成，成本: 156.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,504 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 14, 9, 8, 13, 12, 4], 'cur_cost': 156.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,504 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 156.00)
2025-08-05 10:28:44,504 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:44,504 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:44,505 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,505 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,506 - ExplorationExpert - INFO - 探索路径生成完成，成本: 206.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,506 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 14, 5, 10, 3], 'cur_cost': 206.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,506 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 206.00)
2025-08-05 10:28:44,507 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:44,507 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:44,507 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,508 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:44,509 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,509 - ExplorationExpert - INFO - 探索路径生成完成，成本: 182.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,509 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 182.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,509 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 182.00)
2025-08-05 10:28:44,510 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:44,510 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:44,510 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,511 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:44,512 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,512 - ExplorationExpert - INFO - 探索路径生成完成，成本: 192.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,512 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [6, 7, 8, 13, 5, 14, 10, 9, 11, 3, 12, 0, 1, 2, 4], 'cur_cost': 192.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,512 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 192.00)
2025-08-05 10:28:44,512 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:44,513 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:44,513 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,514 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,514 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 176.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,515 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 1], 'cur_cost': 176.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,515 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 176.00)
2025-08-05 10:28:44,515 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:44,515 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:44,515 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:44,515 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 218.0
2025-08-05 10:28:44,526 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:44,526 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130]
2025-08-05 10:28:44,526 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64)]
2025-08-05 10:28:44,529 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:44,529 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 14, 9, 8, 13, 12, 4], 'cur_cost': 156.0}, {'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 14, 5, 10, 3], 'cur_cost': 206.0}, {'tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 182.0}, {'tour': [6, 7, 8, 13, 5, 14, 10, 9, 11, 3, 12, 0, 1, 2, 4], 'cur_cost': 192.0}, {'tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 1], 'cur_cost': 176.0}, {'tour': array([ 2, 11, 12, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1],
      dtype=int64), 'cur_cost': 218.0}, {'tour': array([ 5,  4,  0,  3, 12,  1,  2, 14, 10, 11,  6,  9,  7, 13,  8],
      dtype=int64), 'cur_cost': 204.0}, {'tour': array([ 2,  6,  9, 13,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 242.0}, {'tour': array([12,  6,  7,  4,  1,  0,  8,  3, 13, 10,  2, 11, 14,  5,  9],
      dtype=int64), 'cur_cost': 227.0}, {'tour': array([ 5,  3,  4,  8,  7, 10,  6, 11,  1,  2,  0, 13, 14, 12,  9],
      dtype=int64), 'cur_cost': 212.0}]
2025-08-05 10:28:44,532 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:44,532 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 144, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 144, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:44,533 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 2, 11, 12, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1],
      dtype=int64), 'cur_cost': 218.0, 'intermediate_solutions': [{'tour': array([12,  5, 11,  9,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 12,  5, 11,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9, 12,  5, 11,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  9, 12,  5,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  0,  9, 12,  5,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:44,533 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 218.00)
2025-08-05 10:28:44,533 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:44,533 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:44,533 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,534 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,535 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 201.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,535 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 2, 3], 'cur_cost': 201.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,535 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 201.00)
2025-08-05 10:28:44,536 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:44,536 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:44,536 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:44,536 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 210.0
2025-08-05 10:28:44,556 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:44,557 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130]
2025-08-05 10:28:44,557 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64)]
2025-08-05 10:28:44,562 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:44,563 - ExploitationExpert - INFO - populations: [{'tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 14, 9, 8, 13, 12, 4], 'cur_cost': 156.0}, {'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 14, 5, 10, 3], 'cur_cost': 206.0}, {'tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 182.0}, {'tour': [6, 7, 8, 13, 5, 14, 10, 9, 11, 3, 12, 0, 1, 2, 4], 'cur_cost': 192.0}, {'tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 1], 'cur_cost': 176.0}, {'tour': array([ 2, 11, 12, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1],
      dtype=int64), 'cur_cost': 218.0}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 2, 3], 'cur_cost': 201.0}, {'tour': array([14,  6,  3, 12,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2],
      dtype=int64), 'cur_cost': 210.0}, {'tour': array([12,  6,  7,  4,  1,  0,  8,  3, 13, 10,  2, 11, 14,  5,  9],
      dtype=int64), 'cur_cost': 227.0}, {'tour': array([ 5,  3,  4,  8,  7, 10,  6, 11,  1,  2,  0, 13, 14, 12,  9],
      dtype=int64), 'cur_cost': 212.0}]
2025-08-05 10:28:44,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:44,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 145, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 145, 'cache_hits': 0, 'similarity_calculations': 577, 'cache_hit_rate': 0.0, 'cache_size': 577}}
2025-08-05 10:28:44,567 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([14,  6,  3, 12,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2],
      dtype=int64), 'cur_cost': 210.0, 'intermediate_solutions': [{'tour': array([ 9,  6,  2, 13,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  9,  6,  2,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 13,  9,  6,  2,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 13,  9,  6,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  1, 13,  9,  6,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:44,567 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 210.00)
2025-08-05 10:28:44,567 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:44,567 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:44,567 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,568 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:44,569 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 172.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,569 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,570 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 172.00)
2025-08-05 10:28:44,570 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:44,570 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:44,570 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,571 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:44,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 202.0, 路径长度: 15, 收集中间解: 0
2025-08-05 10:28:44,572 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [14, 7, 5, 10, 6, 11, 3, 9, 12, 13, 8, 2, 1, 4, 0], 'cur_cost': 202.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,572 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 202.00)
2025-08-05 10:28:44,572 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:44,573 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:44,575 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 14, 9, 8, 13, 12, 4], 'cur_cost': 156.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 14, 5, 10, 3], 'cur_cost': 206.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 182.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 8, 13, 5, 14, 10, 9, 11, 3, 12, 0, 1, 2, 4], 'cur_cost': 192.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 1], 'cur_cost': 176.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 11, 12, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1],
      dtype=int64), 'cur_cost': 218.0, 'intermediate_solutions': [{'tour': array([12,  5, 11,  9,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 12,  5, 11,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0,  9, 12,  5, 11,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11,  9, 12,  5,  0,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11,  0,  9, 12,  5,  6,  8,  1,  2,  3, 13, 14,  4,  7, 10],
      dtype=int64), 'cur_cost': 227.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 2, 3], 'cur_cost': 201.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  6,  3, 12,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2],
      dtype=int64), 'cur_cost': 210.0, 'intermediate_solutions': [{'tour': array([ 9,  6,  2, 13,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  9,  6,  2,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 13,  9,  6,  2,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 13,  9,  6,  1,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 255.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  1, 13,  9,  6,  3, 12,  5,  0,  8, 10,  4,  7, 14, 11],
      dtype=int64), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [14, 7, 5, 10, 6, 11, 3, 9, 12, 13, 8, 2, 1, 4, 0], 'cur_cost': 202.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:44,575 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:44,575 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:44,577 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=156.000, 多样性=0.905
2025-08-05 10:28:44,578 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:44,578 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:44,578 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:44,579 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.025166415500132416, 'best_improvement': -0.05405405405405406}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.039308176100628874}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.03329304542764679, 'recent_improvements': [-0.012689827335136253, -0.05898128778799003, 0.05389626352015733], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7555555555555556, 'new_diversity': 0.7555555555555556, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:44,580 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:44,580 - __main__ - INFO - geometry6_15 开始进化第 2 代
2025-08-05 10:28:44,580 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:44,580 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:44,581 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=156.000, 多样性=0.905
2025-08-05 10:28:44,581 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:44,583 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.905
2025-08-05 10:28:44,583 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:44,586 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.756
2025-08-05 10:28:44,591 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:44,591 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:44,591 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:44,591 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:44,626 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.500, 适应度梯度: -8.040, 聚类评分: 0.000, 覆盖率: 0.066, 收敛趋势: 0.000, 多样性: 0.658
2025-08-05 10:28:44,626 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:44,626 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:44,627 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry6_15
2025-08-05 10:28:44,630 - visualization.landscape_visualizer - INFO - 插值约束: 291 个点被约束到最小值 130.00
2025-08-05 10:28:44,632 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.9%, 梯度: 5.11 → 4.75
2025-08-05 10:28:44,729 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\landscape_geometry6_15_iter_57_20250805_102844.html
2025-08-05 10:28:44,786 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\dashboard_geometry6_15_iter_57_20250805_102844.html
2025-08-05 10:28:44,787 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 57
2025-08-05 10:28:44,787 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:44,787 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1959秒
2025-08-05 10:28:44,787 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -8.04, 'local_optima_density': 0.5, 'gradient_variance': 650.9904000000001, 'cluster_count': 0}, 'population_state': {'diversity': 0.6578947368421052, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0661, 'fitness_entropy': 0.7919268258058498, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -8.040)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.066)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360924.62649, 'performance_metrics': {}}}
2025-08-05 10:28:44,787 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:44,787 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:44,787 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:44,788 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:44,788 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,788 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:44,788 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,789 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:44,789 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:44,789 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:44,789 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:44,789 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:44,789 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 8} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:44,789 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:44,790 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:44,790 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,790 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,790 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,791 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 168.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,791 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 4, 0], 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 8, 9, 14, 13, 12, 4], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 1, 5, 10, 6, 13, 8, 9, 14, 2, 3, 11, 12, 4], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 10, 13, 6, 11, 3, 2, 14, 9, 8, 12, 4], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,792 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 168.00)
2025-08-05 10:28:44,792 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:44,792 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:44,792 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,792 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,793 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,793 - ExplorationExpert - INFO - 探索路径生成完成，成本: 188.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,793 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 10, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 3, 4], 'cur_cost': 188.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 9, 13, 14, 5, 10, 3], 'cur_cost': 215.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 3, 10, 5, 14], 'cur_cost': 211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 1, 11, 4, 12, 7, 8, 2, 13, 9, 14, 5, 10, 3], 'cur_cost': 199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,793 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 188.00)
2025-08-05 10:28:44,794 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:44,794 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:44,794 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,794 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:44,794 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 探索路径生成完成，成本: 198.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,795 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 8, 6, 5, 14, 13, 7, 11, 3, 2, 10, 12, 0, 9, 1], 'cur_cost': 198.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 12, 6, 2, 14, 5, 10, 9, 11, 13, 1, 8, 3], 'cur_cost': 187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 3, 2], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 7, 12, 6, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,795 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 198.00)
2025-08-05 10:28:44,795 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:44,795 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 172.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,796 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 8, 1, 6, 11, 7, 12, 4, 3, 10, 9, 14, 2, 13], 'cur_cost': 172.0, 'intermediate_solutions': [{'tour': [11, 7, 8, 13, 5, 14, 10, 9, 6, 3, 12, 0, 1, 2, 4], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 13, 5, 4, 2, 1, 0, 12, 3, 11, 9, 10, 14], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 13, 3, 5, 14, 10, 9, 11, 12, 0, 1, 2, 4], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,797 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 172.00)
2025-08-05 10:28:44,797 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:44,797 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:44,797 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,797 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,797 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,798 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,798 - ExplorationExpert - INFO - 探索路径生成完成，成本: 164.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,798 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 10, 6, 11, 3, 2, 4], 'cur_cost': 164.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 14], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 12, 7, 13, 4, 3, 1], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 2, 6, 5, 10, 9, 8, 13, 7, 11, 12, 4, 3, 1], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,798 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 164.00)
2025-08-05 10:28:44,798 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:44,798 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:44,798 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:44,799 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 243.0
2025-08-05 10:28:44,807 - ExploitationExpert - INFO - res_population_num: 14
2025-08-05 10:28:44,807 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:44,807 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64)]
2025-08-05 10:28:44,810 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:44,810 - ExploitationExpert - INFO - populations: [{'tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 4, 0], 'cur_cost': 168.0}, {'tour': [1, 10, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 3, 4], 'cur_cost': 188.0}, {'tour': [4, 8, 6, 5, 14, 13, 7, 11, 3, 2, 10, 12, 0, 9, 1], 'cur_cost': 198.0}, {'tour': [0, 5, 8, 1, 6, 11, 7, 12, 4, 3, 10, 9, 14, 2, 13], 'cur_cost': 172.0}, {'tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 10, 6, 11, 3, 2, 4], 'cur_cost': 164.0}, {'tour': array([ 6,  0,  5, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1],
      dtype=int64), 'cur_cost': 243.0}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 2, 3], 'cur_cost': 201.0}, {'tour': [14, 6, 3, 12, 5, 11, 7, 10, 0, 13, 4, 8, 9, 1, 2], 'cur_cost': 210.0}, {'tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0}, {'tour': [14, 7, 5, 10, 6, 11, 3, 9, 12, 13, 8, 2, 1, 4, 0], 'cur_cost': 202.0}]
2025-08-05 10:28:44,810 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:44,810 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 146, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 146, 'cache_hits': 0, 'similarity_calculations': 579, 'cache_hit_rate': 0.0, 'cache_size': 579}}
2025-08-05 10:28:44,811 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 6,  0,  5, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1],
      dtype=int64), 'cur_cost': 243.0, 'intermediate_solutions': [{'tour': array([12, 11,  2, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 12, 11,  2,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 13, 12, 11,  2,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 13, 12, 11,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  9, 13, 12, 11,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:44,811 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 243.00)
2025-08-05 10:28:44,811 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:44,811 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:44,811 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,811 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,812 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,812 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,812 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,812 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,812 - ExplorationExpert - INFO - 探索路径生成完成，成本: 167.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,812 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [1, 7, 2, 5, 6, 11, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 167.0, 'intermediate_solutions': [{'tour': [1, 13, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 4, 2, 3], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 3, 2], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 8, 6, 7, 12, 13, 2, 3], 'cur_cost': 212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,812 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 167.00)
2025-08-05 10:28:44,812 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:44,813 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:44,813 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:44,813 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 236.0
2025-08-05 10:28:44,820 - ExploitationExpert - INFO - res_population_num: 19
2025-08-05 10:28:44,820 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130]
2025-08-05 10:28:44,821 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64)]
2025-08-05 10:28:44,824 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:44,824 - ExploitationExpert - INFO - populations: [{'tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 4, 0], 'cur_cost': 168.0}, {'tour': [1, 10, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 3, 4], 'cur_cost': 188.0}, {'tour': [4, 8, 6, 5, 14, 13, 7, 11, 3, 2, 10, 12, 0, 9, 1], 'cur_cost': 198.0}, {'tour': [0, 5, 8, 1, 6, 11, 7, 12, 4, 3, 10, 9, 14, 2, 13], 'cur_cost': 172.0}, {'tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 10, 6, 11, 3, 2, 4], 'cur_cost': 164.0}, {'tour': array([ 6,  0,  5, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1],
      dtype=int64), 'cur_cost': 243.0}, {'tour': [1, 7, 2, 5, 6, 11, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 167.0}, {'tour': array([ 2, 10,  4,  9,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6],
      dtype=int64), 'cur_cost': 236.0}, {'tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0}, {'tour': [14, 7, 5, 10, 6, 11, 3, 9, 12, 13, 8, 2, 1, 4, 0], 'cur_cost': 202.0}]
2025-08-05 10:28:44,825 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:44,825 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 147, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 147, 'cache_hits': 0, 'similarity_calculations': 582, 'cache_hit_rate': 0.0, 'cache_size': 582}}
2025-08-05 10:28:44,826 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 2, 10,  4,  9,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6],
      dtype=int64), 'cur_cost': 236.0, 'intermediate_solutions': [{'tour': array([ 3,  6, 14, 12,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  3,  6, 14,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 220.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 12,  3,  6, 14, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 12,  3,  6,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  5, 12,  3,  6, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:44,826 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 236.00)
2025-08-05 10:28:44,826 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:44,826 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:44,826 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,826 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:44,826 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,827 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,827 - ExplorationExpert - INFO - 探索路径生成完成，成本: 152.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,827 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 13, 0, 7, 11, 6, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 152.0, 'intermediate_solutions': [{'tour': [4, 7, 12, 6, 11, 5, 14, 8, 3, 1, 10, 9, 2, 13, 0], 'cur_cost': 198.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 0, 13], 'cur_cost': 183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 7, 12, 6, 11, 5, 14, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,827 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 152.00)
2025-08-05 10:28:44,827 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:44,827 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:44,828 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:44,828 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:44,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,828 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,829 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:44,829 - ExplorationExpert - INFO - 探索路径生成完成，成本: 186.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:44,829 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 2, 13, 0, 1], 'cur_cost': 186.0, 'intermediate_solutions': [{'tour': [14, 7, 5, 10, 6, 11, 3, 2, 12, 13, 8, 9, 1, 4, 0], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 7, 5, 0, 4, 1, 2, 8, 13, 12, 9, 3, 11, 6, 10], 'cur_cost': 209.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 7, 5, 10, 4, 6, 11, 3, 9, 12, 13, 8, 2, 1, 0], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:44,829 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 186.00)
2025-08-05 10:28:44,829 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:44,829 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:44,830 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 4, 0], 'cur_cost': 168.0, 'intermediate_solutions': [{'tour': [0, 7, 1, 5, 10, 6, 11, 3, 2, 8, 9, 14, 13, 12, 4], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 1, 5, 10, 6, 13, 8, 9, 14, 2, 3, 11, 12, 4], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 1, 5, 10, 13, 6, 11, 3, 2, 14, 9, 8, 12, 4], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 10, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 3, 4], 'cur_cost': 188.0, 'intermediate_solutions': [{'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 9, 13, 14, 5, 10, 3], 'cur_cost': 215.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 1, 11, 2, 4, 12, 7, 8, 13, 9, 3, 10, 5, 14], 'cur_cost': 211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 1, 11, 4, 12, 7, 8, 2, 13, 9, 14, 5, 10, 3], 'cur_cost': 199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 6, 5, 14, 13, 7, 11, 3, 2, 10, 12, 0, 9, 1], 'cur_cost': 198.0, 'intermediate_solutions': [{'tour': [4, 0, 7, 12, 6, 2, 14, 5, 10, 9, 11, 13, 1, 8, 3], 'cur_cost': 187.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 0, 7, 12, 6, 8, 14, 5, 10, 9, 11, 13, 1, 3, 2], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 0, 7, 12, 6, 14, 5, 10, 9, 11, 13, 1, 2, 3], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 8, 1, 6, 11, 7, 12, 4, 3, 10, 9, 14, 2, 13], 'cur_cost': 172.0, 'intermediate_solutions': [{'tour': [11, 7, 8, 13, 5, 14, 10, 9, 6, 3, 12, 0, 1, 2, 4], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 8, 13, 5, 4, 2, 1, 0, 12, 3, 11, 9, 10, 14], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 7, 8, 13, 3, 5, 14, 10, 9, 11, 12, 0, 1, 2, 4], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 10, 6, 11, 3, 2, 4], 'cur_cost': 164.0, 'intermediate_solutions': [{'tour': [0, 1, 2, 6, 11, 5, 10, 9, 8, 13, 7, 12, 4, 3, 14], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 2, 6, 11, 5, 10, 9, 8, 12, 7, 13, 4, 3, 1], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 2, 6, 5, 10, 9, 8, 13, 7, 11, 12, 4, 3, 1], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  0,  5, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1],
      dtype=int64), 'cur_cost': 243.0, 'intermediate_solutions': [{'tour': array([12, 11,  2, 13,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13, 12, 11,  2,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 13, 12, 11,  2,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 217.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 13, 12, 11,  9,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  9, 13, 12, 11,  5, 10,  4,  6,  0,  7, 14,  8,  3,  1]), 'cur_cost': 212.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 2, 5, 6, 11, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 167.0, 'intermediate_solutions': [{'tour': [1, 13, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 4, 2, 3], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 6, 7, 12, 8, 13, 3, 2], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 0, 11, 9, 14, 5, 10, 8, 6, 7, 12, 13, 2, 3], 'cur_cost': 212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 10,  4,  9,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6],
      dtype=int64), 'cur_cost': 236.0, 'intermediate_solutions': [{'tour': array([ 3,  6, 14, 12,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 215.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  3,  6, 14,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 220.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 12,  3,  6, 14, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 12,  3,  6,  5, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  5, 12,  3,  6, 11,  7, 10,  0, 13,  4,  8,  9,  1,  2]), 'cur_cost': 200.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 13, 0, 7, 11, 6, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 152.0, 'intermediate_solutions': [{'tour': [4, 7, 12, 6, 11, 5, 14, 8, 3, 1, 10, 9, 2, 13, 0], 'cur_cost': 198.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 7, 12, 6, 11, 5, 14, 8, 9, 1, 10, 3, 2, 0, 13], 'cur_cost': 183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 4, 7, 12, 6, 11, 5, 14, 9, 1, 10, 3, 2, 13, 0], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 2, 13, 0, 1], 'cur_cost': 186.0, 'intermediate_solutions': [{'tour': [14, 7, 5, 10, 6, 11, 3, 2, 12, 13, 8, 9, 1, 4, 0], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 7, 5, 0, 4, 1, 2, 8, 13, 12, 9, 3, 11, 6, 10], 'cur_cost': 209.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 7, 5, 10, 4, 6, 11, 3, 9, 12, 13, 8, 2, 1, 0], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:44,830 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:44,831 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:44,832 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=152.000, 多样性=0.924
2025-08-05 10:28:44,832 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:44,832 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:44,832 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:44,833 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.022542650590174976, 'best_improvement': 0.02564102564102564}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.021276595744681027}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.042073851644061216, 'recent_improvements': [-0.05898128778799003, 0.05389626352015733, 0.025166415500132416], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 19, 'new_count': 19, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7434697855750488, 'new_diversity': 0.7434697855750488, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:44,835 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:44,835 - __main__ - INFO - geometry6_15 开始进化第 3 代
2025-08-05 10:28:44,835 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:44,835 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:44,836 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=152.000, 多样性=0.924
2025-08-05 10:28:44,836 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:44,837 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.924
2025-08-05 10:28:44,837 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:44,843 - EliteExpert - INFO - 精英解分析完成: 精英解数量=19, 多样性=0.743
2025-08-05 10:28:44,846 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:44,846 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:44,846 - LandscapeExpert - INFO - 添加精英解数据: 19个精英解
2025-08-05 10:28:44,846 - LandscapeExpert - INFO - 数据提取成功: 29个路径, 29个适应度值
2025-08-05 10:28:44,899 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.655, 适应度梯度: -11.476, 聚类评分: 0.000, 覆盖率: 0.068, 收敛趋势: 0.000, 多样性: 0.435
2025-08-05 10:28:44,900 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:44,900 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:44,900 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry6_15
2025-08-05 10:28:44,910 - visualization.landscape_visualizer - INFO - 插值约束: 237 个点被约束到最小值 130.00
2025-08-05 10:28:44,912 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.8%, 梯度: 3.34 → 3.07
2025-08-05 10:28:45,045 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\landscape_geometry6_15_iter_58_20250805_102844.html
2025-08-05 10:28:45,091 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\dashboard_geometry6_15_iter_58_20250805_102844.html
2025-08-05 10:28:45,092 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 58
2025-08-05 10:28:45,092 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:45,092 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2460秒
2025-08-05 10:28:45,092 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6551724137931034, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -11.475862068965515, 'local_optima_density': 0.6551724137931034, 'gradient_variance': 963.9211414982167, 'cluster_count': 0}, 'population_state': {'diversity': 0.43528112790895196, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.068, 'fitness_entropy': 0.6162485807286918, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -11.476)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.068)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360924.9003565, 'performance_metrics': {}}}
2025-08-05 10:28:45,092 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:45,093 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:45,093 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:45,093 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:45,093 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:45,093 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:45,093 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:45,094 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,094 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:45,094 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:28:45,094 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,094 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:45,094 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:45,095 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:45,095 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:45,095 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,095 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,096 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,096 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,096 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,096 - ExplorationExpert - INFO - 探索路径生成完成，成本: 192.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,096 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 4], 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': [1, 14, 12, 8, 13, 3, 6, 11, 5, 10, 9, 2, 7, 4, 0], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 0, 4], 'cur_cost': 190.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 14, 12, 8, 13, 3, 7, 6, 11, 5, 10, 9, 2, 4, 0], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,096 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 192.00)
2025-08-05 10:28:45,096 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:45,096 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:45,097 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,098 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,099 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,099 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,099 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [10, 11, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 14, 1], 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': [1, 10, 0, 5, 2, 11, 9, 8, 13, 7, 12, 6, 14, 3, 4], 'cur_cost': 230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 0, 5, 2, 14, 9, 6, 12, 7, 13, 8, 11, 3, 4], 'cur_cost': 202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 3, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 4], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,099 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 170.00)
2025-08-05 10:28:45,099 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:45,099 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,099 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,100 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 245.0
2025-08-05 10:28:45,111 - ExploitationExpert - INFO - res_population_num: 21
2025-08-05 10:28:45,111 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130]
2025-08-05 10:28:45,111 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64)]
2025-08-05 10:28:45,116 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,116 - ExploitationExpert - INFO - populations: [{'tour': [1, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 4], 'cur_cost': 192.0}, {'tour': [10, 11, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 14, 1], 'cur_cost': 170.0}, {'tour': array([ 1,  5,  4, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6],
      dtype=int64), 'cur_cost': 245.0}, {'tour': [0, 5, 8, 1, 6, 11, 7, 12, 4, 3, 10, 9, 14, 2, 13], 'cur_cost': 172.0}, {'tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 10, 6, 11, 3, 2, 4], 'cur_cost': 164.0}, {'tour': [6, 0, 5, 14, 3, 12, 2, 7, 9, 13, 8, 10, 11, 4, 1], 'cur_cost': 243.0}, {'tour': [1, 7, 2, 5, 6, 11, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 167.0}, {'tour': [2, 10, 4, 9, 3, 1, 12, 5, 8, 11, 0, 13, 14, 7, 6], 'cur_cost': 236.0}, {'tour': [1, 13, 0, 7, 11, 6, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 152.0}, {'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 2, 13, 0, 1], 'cur_cost': 186.0}]
2025-08-05 10:28:45,116 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,116 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 148, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 148, 'cache_hits': 0, 'similarity_calculations': 586, 'cache_hit_rate': 0.0, 'cache_size': 586}}
2025-08-05 10:28:45,117 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 1,  5,  4, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6],
      dtype=int64), 'cur_cost': 245.0, 'intermediate_solutions': [{'tour': array([ 6,  8,  4,  5, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  6,  8,  4, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  5,  6,  8,  4, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  6,  8, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 14,  5,  6,  8, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,117 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 245.00)
2025-08-05 10:28:45,117 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:45,117 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:45,118 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,118 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,118 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 探索路径生成完成，成本: 194.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,119 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 194.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 1, 2, 11, 7, 12, 4, 3, 10, 9, 14, 6, 13], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 10, 3, 4, 12, 7, 11, 6, 1, 9, 14, 2, 13], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 8, 1, 6, 11, 7, 12, 4, 3, 10, 14, 2, 13], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,119 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 194.00)
2025-08-05 10:28:45,119 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:45,119 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 193.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,121 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 193.0, 'intermediate_solutions': [{'tour': [0, 12, 7, 1, 8, 13, 9, 5, 14, 10, 6, 11, 3, 2, 4], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 2, 3, 11, 6, 10, 4], 'cur_cost': 163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 7, 1, 8, 4, 13, 9, 14, 5, 10, 6, 11, 3, 2], 'cur_cost': 179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,121 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 193.00)
2025-08-05 10:28:45,121 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:45,121 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,121 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,121 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 247.0
2025-08-05 10:28:45,132 - ExploitationExpert - INFO - res_population_num: 24
2025-08-05 10:28:45,133 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130]
2025-08-05 10:28:45,133 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64)]
2025-08-05 10:28:45,140 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,140 - ExploitationExpert - INFO - populations: [{'tour': [1, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 4], 'cur_cost': 192.0}, {'tour': [10, 11, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 14, 1], 'cur_cost': 170.0}, {'tour': array([ 1,  5,  4, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6],
      dtype=int64), 'cur_cost': 245.0}, {'tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 194.0}, {'tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 193.0}, {'tour': array([ 9,  6,  4,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0],
      dtype=int64), 'cur_cost': 247.0}, {'tour': [1, 7, 2, 5, 6, 11, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 167.0}, {'tour': [2, 10, 4, 9, 3, 1, 12, 5, 8, 11, 0, 13, 14, 7, 6], 'cur_cost': 236.0}, {'tour': [1, 13, 0, 7, 11, 6, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 152.0}, {'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 2, 13, 0, 1], 'cur_cost': 186.0}]
2025-08-05 10:28:45,141 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,141 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 149, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 149, 'cache_hits': 0, 'similarity_calculations': 591, 'cache_hit_rate': 0.0, 'cache_size': 591}}
2025-08-05 10:28:45,141 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 9,  6,  4,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0],
      dtype=int64), 'cur_cost': 247.0, 'intermediate_solutions': [{'tour': array([ 5,  0,  6, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  5,  0,  6,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 14,  5,  0,  6, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 241.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 14,  5,  0,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3, 14,  5,  0, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 226.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,141 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 247.00)
2025-08-05 10:28:45,142 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:45,142 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:45,142 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,142 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:45,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,143 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 224.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,143 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [10, 13, 6, 0, 9, 8, 14, 3, 11, 12, 4, 2, 1, 5, 7], 'cur_cost': 224.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 5, 8, 11, 3, 10, 9, 14, 6, 13, 0, 12, 4], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 2, 5, 6, 11, 10, 3, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 11, 2, 5, 6, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,143 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 224.00)
2025-08-05 10:28:45,143 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:45,143 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,144 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,144 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 219.0
2025-08-05 10:28:45,154 - ExploitationExpert - INFO - res_population_num: 24
2025-08-05 10:28:45,154 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130]
2025-08-05 10:28:45,155 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64)]
2025-08-05 10:28:45,159 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,160 - ExploitationExpert - INFO - populations: [{'tour': [1, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 4], 'cur_cost': 192.0}, {'tour': [10, 11, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 14, 1], 'cur_cost': 170.0}, {'tour': array([ 1,  5,  4, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6],
      dtype=int64), 'cur_cost': 245.0}, {'tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 194.0}, {'tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 193.0}, {'tour': array([ 9,  6,  4,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0],
      dtype=int64), 'cur_cost': 247.0}, {'tour': [10, 13, 6, 0, 9, 8, 14, 3, 11, 12, 4, 2, 1, 5, 7], 'cur_cost': 224.0}, {'tour': array([14,  3,  0,  1,  4, 12,  6,  7,  5, 11,  9,  8,  2, 10, 13],
      dtype=int64), 'cur_cost': 219.0}, {'tour': [1, 13, 0, 7, 11, 6, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 152.0}, {'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 2, 13, 0, 1], 'cur_cost': 186.0}]
2025-08-05 10:28:45,160 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,161 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 150, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 150, 'cache_hits': 0, 'similarity_calculations': 597, 'cache_hit_rate': 0.0, 'cache_size': 597}}
2025-08-05 10:28:45,161 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([14,  3,  0,  1,  4, 12,  6,  7,  5, 11,  9,  8,  2, 10, 13],
      dtype=int64), 'cur_cost': 219.0, 'intermediate_solutions': [{'tour': array([ 4, 10,  2,  9,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  4, 10,  2,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  9,  4, 10,  2,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9,  4, 10,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 230.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  3,  9,  4, 10,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,161 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 219.00)
2025-08-05 10:28:45,161 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,162 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,163 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,163 - ExplorationExpert - INFO - 探索路径生成完成，成本: 149.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,163 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 8, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 149.0, 'intermediate_solutions': [{'tour': [1, 13, 0, 7, 11, 6, 8, 10, 9, 14, 5, 12, 4, 3, 2], 'cur_cost': 169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 0, 7, 11, 4, 12, 8, 14, 9, 10, 5, 6, 3, 2], 'cur_cost': 151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 13, 0, 7, 11, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,163 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 149.00)
2025-08-05 10:28:45,163 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:45,163 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:45,163 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 189.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,165 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 9, 13, 8, 6, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 189.0, 'intermediate_solutions': [{'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 1, 13, 0, 2], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 3, 4, 14, 10, 11, 12, 8, 9, 5, 2, 13, 0, 1], 'cur_cost': 195.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 5, 2, 13, 9, 0, 1], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,165 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 189.00)
2025-08-05 10:28:45,165 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:45,165 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:45,166 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 4], 'cur_cost': 192.0, 'intermediate_solutions': [{'tour': [1, 14, 12, 8, 13, 3, 6, 11, 5, 10, 9, 2, 7, 4, 0], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 14, 12, 8, 13, 7, 6, 11, 5, 10, 9, 2, 3, 0, 4], 'cur_cost': 190.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 14, 12, 8, 13, 3, 7, 6, 11, 5, 10, 9, 2, 4, 0], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [10, 11, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 14, 1], 'cur_cost': 170.0, 'intermediate_solutions': [{'tour': [1, 10, 0, 5, 2, 11, 9, 8, 13, 7, 12, 6, 14, 3, 4], 'cur_cost': 230.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 0, 5, 2, 14, 9, 6, 12, 7, 13, 8, 11, 3, 4], 'cur_cost': 202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 3, 0, 5, 2, 14, 9, 8, 13, 7, 12, 6, 11, 4], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1,  5,  4, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6],
      dtype=int64), 'cur_cost': 245.0, 'intermediate_solutions': [{'tour': array([ 6,  8,  4,  5, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 201.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  6,  8,  4, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 202.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  5,  6,  8,  4, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 187.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4,  5,  6,  8, 14, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4, 14,  5,  6,  8, 13,  7, 11,  3,  2, 10, 12,  0,  9,  1]), 'cur_cost': 198.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 194.0, 'intermediate_solutions': [{'tour': [0, 5, 8, 1, 2, 11, 7, 12, 4, 3, 10, 9, 14, 6, 13], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 8, 10, 3, 4, 12, 7, 11, 6, 1, 9, 14, 2, 13], 'cur_cost': 176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 9, 8, 1, 6, 11, 7, 12, 4, 3, 10, 14, 2, 13], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 193.0, 'intermediate_solutions': [{'tour': [0, 12, 7, 1, 8, 13, 9, 5, 14, 10, 6, 11, 3, 2, 4], 'cur_cost': 173.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 7, 1, 8, 13, 9, 14, 5, 2, 3, 11, 6, 10, 4], 'cur_cost': 163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 7, 1, 8, 4, 13, 9, 14, 5, 10, 6, 11, 3, 2], 'cur_cost': 179.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9,  6,  4,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0],
      dtype=int64), 'cur_cost': 247.0, 'intermediate_solutions': [{'tour': array([ 5,  0,  6, 14,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14,  5,  0,  6,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 14,  5,  0,  6, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 241.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 14,  5,  0,  3, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 247.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3, 14,  5,  0, 12,  2,  7,  9, 13,  8, 10, 11,  4,  1]), 'cur_cost': 226.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [10, 13, 6, 0, 9, 8, 14, 3, 11, 12, 4, 2, 1, 5, 7], 'cur_cost': 224.0, 'intermediate_solutions': [{'tour': [1, 7, 2, 5, 8, 11, 3, 10, 9, 14, 6, 13, 0, 12, 4], 'cur_cost': 194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 2, 5, 6, 11, 10, 3, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 11, 2, 5, 6, 3, 10, 9, 14, 8, 13, 0, 12, 4], 'cur_cost': 174.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  3,  0,  1,  4, 12,  6,  7,  5, 11,  9,  8,  2, 10, 13],
      dtype=int64), 'cur_cost': 219.0, 'intermediate_solutions': [{'tour': array([ 4, 10,  2,  9,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 221.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9,  4, 10,  2,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  9,  4, 10,  2,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2,  9,  4, 10,  3,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 230.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2,  3,  9,  4, 10,  1, 12,  5,  8, 11,  0, 13, 14,  7,  6]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 149.0, 'intermediate_solutions': [{'tour': [1, 13, 0, 7, 11, 6, 8, 10, 9, 14, 5, 12, 4, 3, 2], 'cur_cost': 169.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 0, 7, 11, 4, 12, 8, 14, 9, 10, 5, 6, 3, 2], 'cur_cost': 151.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 1, 13, 0, 7, 11, 5, 10, 9, 14, 8, 12, 4, 3, 2], 'cur_cost': 171.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 9, 13, 8, 6, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 189.0, 'intermediate_solutions': [{'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 9, 5, 1, 13, 0, 2], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 3, 4, 14, 10, 11, 12, 8, 9, 5, 2, 13, 0, 1], 'cur_cost': 195.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 3, 4, 8, 12, 11, 10, 14, 5, 2, 13, 9, 0, 1], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:45,166 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:45,166 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,168 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=149.000, 多样性=0.919
2025-08-05 10:28:45,168 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:45,168 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:45,168 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:45,170 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.02523317227325704, 'best_improvement': 0.019736842105263157}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00641025641025637}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.03821945705516615, 'recent_improvements': [0.05389626352015733, 0.025166415500132416, -0.022542650590174976], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 24, 'new_count': 24, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7417874396135266, 'new_diversity': 0.7417874396135266, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:45,173 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:45,173 - __main__ - INFO - geometry6_15 开始进化第 4 代
2025-08-05 10:28:45,173 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:45,173 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,174 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=149.000, 多样性=0.919
2025-08-05 10:28:45,174 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:45,175 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.919
2025-08-05 10:28:45,175 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:45,181 - EliteExpert - INFO - 精英解分析完成: 精英解数量=24, 多样性=0.742
2025-08-05 10:28:45,183 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:45,183 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:45,183 - LandscapeExpert - INFO - 添加精英解数据: 24个精英解
2025-08-05 10:28:45,183 - LandscapeExpert - INFO - 数据提取成功: 34个路径, 34个适应度值
2025-08-05 10:28:45,236 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.706, 适应度梯度: -14.353, 聚类评分: 0.000, 覆盖率: 0.070, 收敛趋势: 0.000, 多样性: 0.366
2025-08-05 10:28:45,237 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:45,237 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:45,237 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry6_15
2025-08-05 10:28:45,246 - visualization.landscape_visualizer - INFO - 插值约束: 253 个点被约束到最小值 130.00
2025-08-05 10:28:45,248 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.6%, 梯度: 6.51 → 6.01
2025-08-05 10:28:45,381 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\landscape_geometry6_15_iter_59_20250805_102845.html
2025-08-05 10:28:45,445 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\dashboard_geometry6_15_iter_59_20250805_102845.html
2025-08-05 10:28:45,445 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 59
2025-08-05 10:28:45,445 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:45,445 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2619秒
2025-08-05 10:28:45,446 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7058823529411765, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -14.352941176470589, 'local_optima_density': 0.7058823529411765, 'gradient_variance': 941.8707266435986, 'cluster_count': 0}, 'population_state': {'diversity': 0.36615287826360493, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0695, 'fitness_entropy': 0.5545793529000247, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.706)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14.353)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.070)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360925.2372591, 'performance_metrics': {}}}
2025-08-05 10:28:45,446 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:45,446 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:45,446 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:45,446 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:45,447 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,447 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:45,447 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,447 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,447 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:45,447 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,447 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,448 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:45,448 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:45,448 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:45,448 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:45,448 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,448 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,449 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,449 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,449 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 13, 2, 5, 1, 7, 12, 6, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [4, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 1], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 2, 6, 11, 3, 10, 5, 9, 14, 0, 7, 12, 8, 4], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 13, 2, 6, 0, 9, 5, 10, 3, 11, 7, 14, 12, 8, 4], 'cur_cost': 213.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,449 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 174.00)
2025-08-05 10:28:45,449 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,450 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,451 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,451 - ExplorationExpert - INFO - 探索路径生成完成，成本: 188.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,451 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 188.0, 'intermediate_solutions': [{'tour': [10, 14, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 11, 1], 'cur_cost': 200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 14, 5, 2, 3, 6, 8, 9, 13, 7, 12, 0, 4, 11, 1], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 11, 4, 0, 12, 7, 13, 9, 1, 8, 6, 3, 2, 5, 14], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,451 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 188.00)
2025-08-05 10:28:45,451 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:45,451 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,451 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,451 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 213.0
2025-08-05 10:28:45,462 - ExploitationExpert - INFO - res_population_num: 26
2025-08-05 10:28:45,462 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:45,463 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64)]
2025-08-05 10:28:45,467 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,467 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 2, 5, 1, 7, 12, 6, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 174.0}, {'tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 188.0}, {'tour': array([ 8, 10,  7,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11],
      dtype=int64), 'cur_cost': 213.0}, {'tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 194.0}, {'tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 193.0}, {'tour': [9, 6, 4, 1, 3, 13, 7, 5, 8, 11, 12, 2, 14, 10, 0], 'cur_cost': 247.0}, {'tour': [10, 13, 6, 0, 9, 8, 14, 3, 11, 12, 4, 2, 1, 5, 7], 'cur_cost': 224.0}, {'tour': [14, 3, 0, 1, 4, 12, 6, 7, 5, 11, 9, 8, 2, 10, 13], 'cur_cost': 219.0}, {'tour': [1, 8, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 149.0}, {'tour': [0, 1, 9, 13, 8, 6, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 189.0}]
2025-08-05 10:28:45,468 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,468 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 151, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 151, 'cache_hits': 0, 'similarity_calculations': 604, 'cache_hit_rate': 0.0, 'cache_size': 604}}
2025-08-05 10:28:45,469 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 8, 10,  7,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11],
      dtype=int64), 'cur_cost': 213.0, 'intermediate_solutions': [{'tour': array([ 4,  5,  1, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4,  5,  1,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 12,  4,  5,  1, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12,  4,  5,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  2, 12,  4,  5, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,469 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 213.00)
2025-08-05 10:28:45,469 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:45,469 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:45,469 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,470 - ExplorationExpert - INFO - 探索路径生成完成，成本: 241.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,470 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 2, 7, 5, 10, 9, 6, 4, 14, 11, 8, 0, 1, 3, 13], 'cur_cost': 241.0, 'intermediate_solutions': [{'tour': [1, 2, 13, 8, 0, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 201.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 3, 14], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 13, 3, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,471 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 241.00)
2025-08-05 10:28:45,471 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,471 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,472 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,472 - ExplorationExpert - INFO - 探索路径生成完成，成本: 184.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,472 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0, 'intermediate_solutions': [{'tour': [1, 8, 5, 0, 3, 11, 6, 10, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 4, 12, 7, 13, 2], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 189.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,472 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 184.00)
2025-08-05 10:28:45,472 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:45,472 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,472 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,473 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 221.0
2025-08-05 10:28:45,485 - ExploitationExpert - INFO - res_population_num: 32
2025-08-05 10:28:45,485 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130]
2025-08-05 10:28:45,485 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4,  6,  5,  9,  8,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  5, 10,  3,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4,  7, 12],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64)]
2025-08-05 10:28:45,495 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,495 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 2, 5, 1, 7, 12, 6, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 174.0}, {'tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 188.0}, {'tour': array([ 8, 10,  7,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11],
      dtype=int64), 'cur_cost': 213.0}, {'tour': [12, 2, 7, 5, 10, 9, 6, 4, 14, 11, 8, 0, 1, 3, 13], 'cur_cost': 241.0}, {'tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0}, {'tour': array([ 4,  8,  9, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7],
      dtype=int64), 'cur_cost': 221.0}, {'tour': [10, 13, 6, 0, 9, 8, 14, 3, 11, 12, 4, 2, 1, 5, 7], 'cur_cost': 224.0}, {'tour': [14, 3, 0, 1, 4, 12, 6, 7, 5, 11, 9, 8, 2, 10, 13], 'cur_cost': 219.0}, {'tour': [1, 8, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 149.0}, {'tour': [0, 1, 9, 13, 8, 6, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 189.0}]
2025-08-05 10:28:45,496 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,497 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 152, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 152, 'cache_hits': 0, 'similarity_calculations': 612, 'cache_hit_rate': 0.0, 'cache_size': 612}}
2025-08-05 10:28:45,497 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 4,  8,  9, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7],
      dtype=int64), 'cur_cost': 221.0, 'intermediate_solutions': [{'tour': array([ 4,  6,  9,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  4,  6,  9,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  1,  4,  6,  9, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 243.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  1,  4,  6,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  3,  1,  4,  6, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,497 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 221.00)
2025-08-05 10:28:45,498 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:45,498 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,498 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,498 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 209.0
2025-08-05 10:28:45,509 - ExploitationExpert - INFO - res_population_num: 36
2025-08-05 10:28:45,509 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130]
2025-08-05 10:28:45,509 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4,  6,  5,  9,  8,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  5, 10,  3,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4,  7, 12],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  5,  3, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64)]
2025-08-05 10:28:45,516 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,517 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 2, 5, 1, 7, 12, 6, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 174.0}, {'tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 188.0}, {'tour': array([ 8, 10,  7,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11],
      dtype=int64), 'cur_cost': 213.0}, {'tour': [12, 2, 7, 5, 10, 9, 6, 4, 14, 11, 8, 0, 1, 3, 13], 'cur_cost': 241.0}, {'tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0}, {'tour': array([ 4,  8,  9, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7],
      dtype=int64), 'cur_cost': 221.0}, {'tour': array([10,  5,  2,  4, 13,  7, 14, 11,  9,  8,  1,  0, 12,  3,  6],
      dtype=int64), 'cur_cost': 209.0}, {'tour': [14, 3, 0, 1, 4, 12, 6, 7, 5, 11, 9, 8, 2, 10, 13], 'cur_cost': 219.0}, {'tour': [1, 8, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 149.0}, {'tour': [0, 1, 9, 13, 8, 6, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 189.0}]
2025-08-05 10:28:45,517 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,517 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 153, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 153, 'cache_hits': 0, 'similarity_calculations': 621, 'cache_hit_rate': 0.0, 'cache_size': 621}}
2025-08-05 10:28:45,518 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([10,  5,  2,  4, 13,  7, 14, 11,  9,  8,  1,  0, 12,  3,  6],
      dtype=int64), 'cur_cost': 209.0, 'intermediate_solutions': [{'tour': array([ 6, 13, 10,  0,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  6, 13, 10,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  0,  6, 13, 10,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  0,  6, 13,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  9,  0,  6, 13,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,518 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 209.00)
2025-08-05 10:28:45,518 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:45,519 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:45,519 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,519 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,519 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,520 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,520 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,520 - ExplorationExpert - INFO - 探索路径生成完成，成本: 207.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,520 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 10, 0, 11, 2, 6, 5, 9, 14, 8, 13, 7, 12, 4, 3], 'cur_cost': 207.0, 'intermediate_solutions': [{'tour': [14, 3, 0, 1, 4, 12, 10, 7, 5, 11, 9, 8, 2, 6, 13], 'cur_cost': 238.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 11, 5, 7, 6, 12, 4, 1, 0, 3, 9, 8, 2, 10, 13], 'cur_cost': 220.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 3, 0, 10, 1, 4, 12, 6, 7, 5, 11, 9, 8, 2, 13], 'cur_cost': 239.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,520 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 207.00)
2025-08-05 10:28:45,520 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:45,521 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:45,521 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,521 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,521 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,522 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 186.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,522 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 13, 11], 'cur_cost': 186.0, 'intermediate_solutions': [{'tour': [11, 8, 0, 7, 1, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 14, 9, 10, 5, 6, 11, 7, 0, 2, 3, 4, 12, 13], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 8, 13], 'cur_cost': 147.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,522 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 186.00)
2025-08-05 10:28:45,522 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,524 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,524 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 4, 6, 0, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [0, 1, 9, 13, 12, 6, 8, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 8, 13, 9, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 5, 9, 13, 8, 6, 12, 4, 11, 10, 14, 7, 3, 2], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,524 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 174.00)
2025-08-05 10:28:45,524 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:45,524 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:45,525 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 2, 5, 1, 7, 12, 6, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [4, 13, 2, 6, 0, 14, 9, 5, 10, 3, 11, 7, 12, 8, 1], 'cur_cost': 196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 13, 2, 6, 11, 3, 10, 5, 9, 14, 0, 7, 12, 8, 4], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 13, 2, 6, 0, 9, 5, 10, 3, 11, 7, 14, 12, 8, 4], 'cur_cost': 213.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 188.0, 'intermediate_solutions': [{'tour': [10, 14, 4, 0, 12, 7, 13, 9, 8, 6, 3, 2, 5, 11, 1], 'cur_cost': 200.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 14, 5, 2, 3, 6, 8, 9, 13, 7, 12, 0, 4, 11, 1], 'cur_cost': 185.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 11, 4, 0, 12, 7, 13, 9, 1, 8, 6, 3, 2, 5, 14], 'cur_cost': 168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 10,  7,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11],
      dtype=int64), 'cur_cost': 213.0, 'intermediate_solutions': [{'tour': array([ 4,  5,  1, 12,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 246.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12,  4,  5,  1,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 2, 12,  4,  5,  1, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 242.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 12,  4,  5,  2, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1,  2, 12,  4,  5, 14, 11,  0, 10,  8,  7,  3, 13,  9,  6]), 'cur_cost': 245.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 2, 7, 5, 10, 9, 6, 4, 14, 11, 8, 0, 1, 3, 13], 'cur_cost': 241.0, 'intermediate_solutions': [{'tour': [1, 2, 13, 8, 0, 9, 10, 6, 5, 11, 4, 7, 12, 14, 3], 'cur_cost': 201.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 2, 13, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 3, 14], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 13, 3, 0, 8, 9, 10, 6, 5, 11, 4, 7, 12, 14], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0, 'intermediate_solutions': [{'tour': [1, 8, 5, 0, 3, 11, 6, 10, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 10, 0, 3, 11, 6, 5, 9, 14, 4, 12, 7, 13, 2], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 1, 10, 0, 3, 11, 6, 5, 9, 14, 2, 13, 7, 12, 4], 'cur_cost': 189.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4,  8,  9, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7],
      dtype=int64), 'cur_cost': 221.0, 'intermediate_solutions': [{'tour': array([ 4,  6,  9,  1,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 229.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1,  4,  6,  9,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 235.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  1,  4,  6,  9, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 243.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9,  1,  4,  6,  3, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9,  3,  1,  4,  6, 13,  7,  5,  8, 11, 12,  2, 14, 10,  0]), 'cur_cost': 244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([10,  5,  2,  4, 13,  7, 14, 11,  9,  8,  1,  0, 12,  3,  6],
      dtype=int64), 'cur_cost': 209.0, 'intermediate_solutions': [{'tour': array([ 6, 13, 10,  0,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 223.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  6, 13, 10,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9,  0,  6, 13, 10,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 228.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([10,  0,  6, 13,  9,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([10,  9,  0,  6, 13,  8, 14,  3, 11, 12,  4,  2,  1,  5,  7]), 'cur_cost': 214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 10, 0, 11, 2, 6, 5, 9, 14, 8, 13, 7, 12, 4, 3], 'cur_cost': 207.0, 'intermediate_solutions': [{'tour': [14, 3, 0, 1, 4, 12, 10, 7, 5, 11, 9, 8, 2, 6, 13], 'cur_cost': 238.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 11, 5, 7, 6, 12, 4, 1, 0, 3, 9, 8, 2, 10, 13], 'cur_cost': 220.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 3, 0, 10, 1, 4, 12, 6, 7, 5, 11, 9, 8, 2, 13], 'cur_cost': 239.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 13, 11], 'cur_cost': 186.0, 'intermediate_solutions': [{'tour': [11, 8, 0, 7, 1, 6, 5, 10, 9, 14, 2, 3, 4, 12, 13], 'cur_cost': 184.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 8, 14, 9, 10, 5, 6, 11, 7, 0, 2, 3, 4, 12, 13], 'cur_cost': 166.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 7, 11, 6, 5, 10, 9, 14, 2, 3, 4, 12, 8, 13], 'cur_cost': 147.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 6, 0, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [0, 1, 9, 13, 12, 6, 8, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 203.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 6, 8, 13, 9, 12, 4, 11, 10, 14, 5, 7, 3, 2], 'cur_cost': 202.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 5, 9, 13, 8, 6, 12, 4, 11, 10, 14, 7, 3, 2], 'cur_cost': 197.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:45,526 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:45,526 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,527 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=174.000, 多样性=0.936
2025-08-05 10:28:45,527 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:45,527 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:45,527 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:45,532 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0437099136266997, 'best_improvement': -0.16778523489932887}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0193548387096773}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.025199793886694726, 'recent_improvements': [0.025166415500132416, -0.022542650590174976, -0.02523317227325704], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 36, 'new_count': 36, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7554497354497355, 'new_diversity': 0.7554497354497355, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:45,537 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:45,537 - __main__ - INFO - geometry6_15 开始进化第 5 代
2025-08-05 10:28:45,537 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:45,537 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,538 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=174.000, 多样性=0.936
2025-08-05 10:28:45,538 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:45,539 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.936
2025-08-05 10:28:45,539 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:45,552 - EliteExpert - INFO - 精英解分析完成: 精英解数量=36, 多样性=0.755
2025-08-05 10:28:45,553 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:45,553 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:45,553 - LandscapeExpert - INFO - 添加精英解数据: 36个精英解
2025-08-05 10:28:45,553 - LandscapeExpert - INFO - 数据提取成功: 46个路径, 46个适应度值
2025-08-05 10:28:45,678 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.783, 适应度梯度: -10.200, 聚类评分: 0.000, 覆盖率: 0.072, 收敛趋势: 0.000, 多样性: 0.269
2025-08-05 10:28:45,679 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:45,679 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:45,679 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry6_15
2025-08-05 10:28:45,690 - visualization.landscape_visualizer - INFO - 插值约束: 289 个点被约束到最小值 130.00
2025-08-05 10:28:45,694 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=11.4%, 梯度: 4.17 → 3.69
2025-08-05 10:28:45,813 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\landscape_geometry6_15_iter_60_20250805_102845.html
2025-08-05 10:28:45,862 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry6_15\dashboard_geometry6_15_iter_60_20250805_102845.html
2025-08-05 10:28:45,862 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 60
2025-08-05 10:28:45,863 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:45,863 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.3102秒
2025-08-05 10:28:45,863 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.782608695652174, 'modality': 'multi-modal', 'deceptiveness': 'high', 'gradient_strength': -10.200000000000001, 'local_optima_density': 0.782608695652174, 'gradient_variance': 653.3530434782609, 'cluster_count': 0}, 'population_state': {'diversity': 0.2694391934467549, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0717, 'fitness_entropy': 0.44903297048332863, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'high_local_optima', 'severity': 'high', 'description': '高局部最优密度区域 (密度: 0.783)', 'recommendation': '增加扰动强度'}, {'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -10.200)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.072)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization', 'diversification'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50, 'diversification_strength': 0.6}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360925.6796234, 'performance_metrics': {}}}
2025-08-05 10:28:45,863 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:45,863 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:45,863 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:45,863 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:45,863 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,864 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:45,864 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:45,864 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:45,864 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:45,864 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:45,864 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,865 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 15
2025-08-05 10:28:45,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,865 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,866 - ExplorationExpert - INFO - 探索路径生成完成，成本: 166.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,866 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [13, 0, 12, 6, 11, 3, 9, 10, 5, 2, 7, 4, 8, 14, 1], 'cur_cost': 166.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 5, 10, 7, 12, 6, 11, 3, 1, 9, 14, 8, 4], 'cur_cost': 178.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 5, 1, 7, 12, 6, 3, 11, 10, 9, 14, 8, 4], 'cur_cost': 183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 5, 1, 7, 12, 6, 2, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 190.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,866 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 166.00)
2025-08-05 10:28:45,866 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:45,866 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:45,867 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,867 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:45,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,867 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,868 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,868 - ExplorationExpert - INFO - 探索路径生成完成，成本: 220.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,868 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [13, 2, 12, 6, 11, 9, 1, 10, 5, 7, 0, 4, 8, 14, 3], 'cur_cost': 220.0, 'intermediate_solutions': [{'tour': [9, 12, 11, 5, 4, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 12, 1, 8, 2, 5, 4, 11, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 14, 0, 6, 10, 3], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,868 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 220.00)
2025-08-05 10:28:45,868 - experts.management.collaboration_manager - INFO - 为个体 2 生成利用路径
2025-08-05 10:28:45,868 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,868 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 181.0
2025-08-05 10:28:45,881 - ExploitationExpert - INFO - res_population_num: 41
2025-08-05 10:28:45,881 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130.0, 130, 130, 130, 130]
2025-08-05 10:28:45,881 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4,  6,  5,  9,  8,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  5, 10,  3,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4,  7, 12],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  5,  3, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3, 11,  6,  4, 12,  7,  8],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3,  6, 11,  4, 12,  7,  8],
      dtype=int64)]
2025-08-05 10:28:45,889 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,889 - ExploitationExpert - INFO - populations: [{'tour': [13, 0, 12, 6, 11, 3, 9, 10, 5, 2, 7, 4, 8, 14, 1], 'cur_cost': 166.0}, {'tour': [13, 2, 12, 6, 11, 9, 1, 10, 5, 7, 0, 4, 8, 14, 3], 'cur_cost': 220.0}, {'tour': array([12,  3,  6, 11,  5,  1,  0,  4,  7, 13, 14, 10,  2,  9,  8],
      dtype=int64), 'cur_cost': 181.0}, {'tour': [12, 2, 7, 5, 10, 9, 6, 4, 14, 11, 8, 0, 1, 3, 13], 'cur_cost': 241.0}, {'tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0}, {'tour': [4, 8, 9, 10, 6, 13, 2, 12, 0, 3, 5, 11, 1, 14, 7], 'cur_cost': 221.0}, {'tour': [10, 5, 2, 4, 13, 7, 14, 11, 9, 8, 1, 0, 12, 3, 6], 'cur_cost': 209.0}, {'tour': [1, 10, 0, 11, 2, 6, 5, 9, 14, 8, 13, 7, 12, 4, 3], 'cur_cost': 207.0}, {'tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 13, 11], 'cur_cost': 186.0}, {'tour': [1, 4, 6, 0, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 174.0}]
2025-08-05 10:28:45,890 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,890 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 154, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 154, 'cache_hits': 0, 'similarity_calculations': 631, 'cache_hit_rate': 0.0, 'cache_size': 631}}
2025-08-05 10:28:45,890 - experts.management.collaboration_manager - INFO - 个体 2 利用路径生成报告: {'new_tour': array([12,  3,  6, 11,  5,  1,  0,  4,  7, 13, 14, 10,  2,  9,  8],
      dtype=int64), 'cur_cost': 181.0, 'intermediate_solutions': [{'tour': array([ 7, 10,  8,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  7, 10,  8, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  4,  7, 10,  8,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  4,  7, 10, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 12,  4,  7, 10,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,890 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 181.00)
2025-08-05 10:28:45,891 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:45,891 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,891 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,891 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 212.0
2025-08-05 10:28:45,901 - ExploitationExpert - INFO - res_population_num: 43
2025-08-05 10:28:45,901 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:45,901 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4,  6,  5,  9,  8,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  5, 10,  3,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4,  7, 12],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  5,  3, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3, 11,  6,  4, 12,  7,  8],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3,  6, 11,  4, 12,  7,  8],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  2, 10,  5,  9, 14,  1,  8, 13],
      dtype=int64)]
2025-08-05 10:28:45,909 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,909 - ExploitationExpert - INFO - populations: [{'tour': [13, 0, 12, 6, 11, 3, 9, 10, 5, 2, 7, 4, 8, 14, 1], 'cur_cost': 166.0}, {'tour': [13, 2, 12, 6, 11, 9, 1, 10, 5, 7, 0, 4, 8, 14, 3], 'cur_cost': 220.0}, {'tour': array([12,  3,  6, 11,  5,  1,  0,  4,  7, 13, 14, 10,  2,  9,  8],
      dtype=int64), 'cur_cost': 181.0}, {'tour': array([ 5,  1,  6,  4,  9, 11, 14, 10,  2,  8, 13,  7, 12,  0,  3],
      dtype=int64), 'cur_cost': 212.0}, {'tour': [10, 3, 2, 8, 6, 4, 12, 11, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 184.0}, {'tour': [4, 8, 9, 10, 6, 13, 2, 12, 0, 3, 5, 11, 1, 14, 7], 'cur_cost': 221.0}, {'tour': [10, 5, 2, 4, 13, 7, 14, 11, 9, 8, 1, 0, 12, 3, 6], 'cur_cost': 209.0}, {'tour': [1, 10, 0, 11, 2, 6, 5, 9, 14, 8, 13, 7, 12, 4, 3], 'cur_cost': 207.0}, {'tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 13, 11], 'cur_cost': 186.0}, {'tour': [1, 4, 6, 0, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 174.0}]
2025-08-05 10:28:45,910 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,910 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 155, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 155, 'cache_hits': 0, 'similarity_calculations': 642, 'cache_hit_rate': 0.0, 'cache_size': 642}}
2025-08-05 10:28:45,910 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 5,  1,  6,  4,  9, 11, 14, 10,  2,  8, 13,  7, 12,  0,  3],
      dtype=int64), 'cur_cost': 212.0, 'intermediate_solutions': [{'tour': array([ 7,  2, 12,  5, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7,  2, 12, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  7,  2, 12,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  5,  7,  2, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 10,  5,  7,  2,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,911 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 212.00)
2025-08-05 10:28:45,911 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:45,911 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:45,911 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,911 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:45,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,911 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,912 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,912 - ExplorationExpert - INFO - 探索路径生成完成，成本: 227.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,912 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [9, 8, 2, 0, 7, 13, 3, 6, 14, 10, 12, 4, 5, 11, 1], 'cur_cost': 227.0, 'intermediate_solutions': [{'tour': [10, 3, 5, 8, 6, 4, 12, 11, 7, 9, 14, 2, 13, 0, 1], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 3, 2, 13, 5, 14, 9, 7, 11, 12, 4, 6, 8, 0, 1], 'cur_cost': 188.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 3, 2, 8, 6, 11, 4, 12, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,912 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 227.00)
2025-08-05 10:28:45,912 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:28:45,912 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:45,912 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:45,913 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 214.0
2025-08-05 10:28:45,924 - ExploitationExpert - INFO - res_population_num: 48
2025-08-05 10:28:45,924 - ExploitationExpert - INFO - res_population_costs: [130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130.0, 130, 130, 130, 130.0, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130]
2025-08-05 10:28:45,925 - ExploitationExpert - INFO - res_populations: [array([ 0, 12,  4, 11,  6,  7,  8,  9,  5, 10,  3,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  1, 14,  2,  9,  8,  7,  6,  5, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  3,  6,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3,  6,  7,  8,  9,  5, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  5, 10,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 13,  1, 14,  9,  8,  7,  6,  5, 10,  2,  3, 11,  4, 12],
      dtype=int64), array([ 0, 12,  4,  7,  8,  9,  5,  6, 11,  3, 10,  2, 14,  1, 13],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2, 14,  1,  9,  5,  6,  7,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2, 10,  5,  3, 11,  4,  6,  7, 12],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  2,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7,  6,  5,  9,  8, 13,  1, 14,  2, 10,  3, 11,  4, 12],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2, 10,  5,  3, 11,  6,  7,  4, 12],
      dtype=int64), array([ 0, 12,  4, 11,  3, 10,  2,  5,  6,  7,  8,  9, 14,  1, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  6,  4, 11,  3,  5, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4, 12,  7,  6,  5,  9,  8],
      dtype=int64), array([ 0,  7,  8, 13,  1,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12],
      dtype=int64), array([ 0, 13,  1, 14,  2, 10,  3, 11,  4,  6,  5,  9,  8,  7, 12],
      dtype=int64), array([ 0,  7, 12,  4,  6, 11,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  5, 10,  3,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0,  8, 13,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4,  7, 12],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1, 14,  9,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  5,  3, 10,  2,  9, 14,  1, 13,  8],
      dtype=int64), array([ 0, 13,  1,  8,  9, 14,  2,  5, 10,  3,  6, 11,  4, 12,  7],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 12,  7,  4, 11,  6,  3, 10,  5,  2, 14,  9,  1,  8, 13],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3, 11,  6,  4, 12,  7,  8],
      dtype=int64), array([ 0, 13,  1, 14,  9,  2, 10,  5,  3,  6, 11,  4, 12,  7,  8],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3,  5, 10,  2,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  3,  2, 10,  5,  9, 14,  1,  8, 13],
      dtype=int64), array([ 0,  8,  7, 12,  4,  6, 11,  3,  5, 10,  2, 14,  9,  1, 13],
      dtype=int64), array([ 0, 12,  4,  7,  6, 11,  3, 10,  5,  2, 14,  9,  8,  1, 13],
      dtype=int64), array([ 0, 13,  8,  9,  1, 14,  2,  5, 10,  3, 11,  6,  4, 12,  7],
      dtype=int64), array([ 0,  7, 12,  4, 11,  6,  5, 10,  3,  2, 14,  1,  9,  8, 13],
      dtype=int64), array([ 0, 13,  8,  1,  9, 14,  2,  3, 10,  5,  6, 11,  4, 12,  7],
      dtype=int64)]
2025-08-05 10:28:45,934 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:45,934 - ExploitationExpert - INFO - populations: [{'tour': [13, 0, 12, 6, 11, 3, 9, 10, 5, 2, 7, 4, 8, 14, 1], 'cur_cost': 166.0}, {'tour': [13, 2, 12, 6, 11, 9, 1, 10, 5, 7, 0, 4, 8, 14, 3], 'cur_cost': 220.0}, {'tour': array([12,  3,  6, 11,  5,  1,  0,  4,  7, 13, 14, 10,  2,  9,  8],
      dtype=int64), 'cur_cost': 181.0}, {'tour': array([ 5,  1,  6,  4,  9, 11, 14, 10,  2,  8, 13,  7, 12,  0,  3],
      dtype=int64), 'cur_cost': 212.0}, {'tour': [9, 8, 2, 0, 7, 13, 3, 6, 14, 10, 12, 4, 5, 11, 1], 'cur_cost': 227.0}, {'tour': array([ 7,  0, 14,  8, 12,  4, 13, 10, 11,  9,  3,  6,  1,  2,  5],
      dtype=int64), 'cur_cost': 214.0}, {'tour': [10, 5, 2, 4, 13, 7, 14, 11, 9, 8, 1, 0, 12, 3, 6], 'cur_cost': 209.0}, {'tour': [1, 10, 0, 11, 2, 6, 5, 9, 14, 8, 13, 7, 12, 4, 3], 'cur_cost': 207.0}, {'tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 13, 11], 'cur_cost': 186.0}, {'tour': [1, 4, 6, 0, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 174.0}]
2025-08-05 10:28:45,935 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:45,935 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 156, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 156, 'cache_hits': 0, 'similarity_calculations': 654, 'cache_hit_rate': 0.0, 'cache_size': 654}}
2025-08-05 10:28:45,935 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 7,  0, 14,  8, 12,  4, 13, 10, 11,  9,  3,  6,  1,  2,  5],
      dtype=int64), 'cur_cost': 214.0, 'intermediate_solutions': [{'tour': array([ 9,  8,  4, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  9,  8,  4,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 10,  9,  8,  4, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  9,  8,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6, 10,  9,  8, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:45,935 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 214.00)
2025-08-05 10:28:45,936 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:45,936 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:45,936 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,936 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 202.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,937 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 14, 2, 4, 13, 8, 7, 12, 6, 11, 5, 10, 9, 1, 3], 'cur_cost': 202.0, 'intermediate_solutions': [{'tour': [10, 5, 2, 3, 13, 7, 14, 11, 9, 8, 1, 0, 12, 4, 6], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 5, 2, 4, 13, 7, 14, 11, 12, 0, 1, 8, 9, 3, 6], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 5, 2, 0, 4, 13, 7, 14, 11, 9, 8, 1, 12, 3, 6], 'cur_cost': 219.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,937 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 202.00)
2025-08-05 10:28:45,937 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:45,937 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:45,937 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,938 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,938 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,939 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,939 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,939 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 13, 0, 6, 2, 7, 12, 8, 9, 14, 5, 10, 3, 11, 4], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [1, 10, 0, 11, 2, 6, 5, 12, 14, 8, 13, 7, 9, 4, 3], 'cur_cost': 244.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 0, 11, 2, 8, 14, 9, 5, 6, 13, 7, 12, 4, 3], 'cur_cost': 216.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 8, 0, 11, 2, 6, 5, 9, 14, 13, 7, 12, 4, 3], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,939 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 174.00)
2025-08-05 10:28:45,939 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:45,939 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:45,939 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,940 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 15
2025-08-05 10:28:45,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,940 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,941 - ExplorationExpert - INFO - 探索路径生成完成，成本: 231.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,941 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [13, 9, 2, 0, 10, 11, 7, 1, 12, 14, 8, 3, 5, 6, 4], 'cur_cost': 231.0, 'intermediate_solutions': [{'tour': [7, 14, 10, 3, 9, 1, 0, 12, 4, 5, 8, 6, 2, 13, 11], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 11, 13], 'cur_cost': 187.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 10, 9, 1, 0, 12, 4, 5, 8, 3, 14, 2, 13, 11], 'cur_cost': 200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,941 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 231.00)
2025-08-05 10:28:45,941 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:45,941 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:45,941 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 15
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:45,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 193.0, 路径长度: 15, 收集中间解: 3
2025-08-05 10:28:45,943 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 8, 2, 12, 0, 11, 6, 5, 10, 9, 14, 13, 7, 4, 3], 'cur_cost': 193.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 0, 12, 10, 8, 13, 9, 14, 5, 7, 2, 3, 11], 'cur_cost': 210.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 6, 0, 11, 3, 2, 10, 5, 14, 9, 13, 8, 7, 12], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 4, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:45,943 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 193.00)
2025-08-05 10:28:45,943 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:45,943 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:45,944 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [13, 0, 12, 6, 11, 3, 9, 10, 5, 2, 7, 4, 8, 14, 1], 'cur_cost': 166.0, 'intermediate_solutions': [{'tour': [0, 13, 2, 5, 10, 7, 12, 6, 11, 3, 1, 9, 14, 8, 4], 'cur_cost': 178.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 2, 5, 1, 7, 12, 6, 3, 11, 10, 9, 14, 8, 4], 'cur_cost': 183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 5, 1, 7, 12, 6, 2, 11, 3, 10, 9, 14, 8, 4], 'cur_cost': 190.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [13, 2, 12, 6, 11, 9, 1, 10, 5, 7, 0, 4, 8, 14, 3], 'cur_cost': 220.0, 'intermediate_solutions': [{'tour': [9, 12, 11, 5, 4, 2, 8, 1, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 12, 1, 8, 2, 5, 4, 11, 13, 7, 0, 6, 10, 3, 14], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 12, 11, 4, 5, 2, 8, 1, 13, 7, 14, 0, 6, 10, 3], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([12,  3,  6, 11,  5,  1,  0,  4,  7, 13, 14, 10,  2,  9,  8],
      dtype=int64), 'cur_cost': 181.0, 'intermediate_solutions': [{'tour': array([ 7, 10,  8,  4, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 214.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  7, 10,  8, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([12,  4,  7, 10,  8,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 211.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  4,  7, 10, 12,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8, 12,  4,  7, 10,  3,  1, 13,  0,  5,  2, 14,  6,  9, 11]), 'cur_cost': 199.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  1,  6,  4,  9, 11, 14, 10,  2,  8, 13,  7, 12,  0,  3],
      dtype=int64), 'cur_cost': 212.0, 'intermediate_solutions': [{'tour': array([ 7,  2, 12,  5, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5,  7,  2, 12, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 257.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  5,  7,  2, 12,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 252.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12,  5,  7,  2, 10,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 236.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12, 10,  5,  7,  2,  9,  6,  4, 14, 11,  8,  0,  1,  3, 13]), 'cur_cost': 237.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [9, 8, 2, 0, 7, 13, 3, 6, 14, 10, 12, 4, 5, 11, 1], 'cur_cost': 227.0, 'intermediate_solutions': [{'tour': [10, 3, 5, 8, 6, 4, 12, 11, 7, 9, 14, 2, 13, 0, 1], 'cur_cost': 177.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 3, 2, 13, 5, 14, 9, 7, 11, 12, 4, 6, 8, 0, 1], 'cur_cost': 188.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 3, 2, 8, 6, 11, 4, 12, 7, 9, 14, 5, 13, 0, 1], 'cur_cost': 170.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7,  0, 14,  8, 12,  4, 13, 10, 11,  9,  3,  6,  1,  2,  5],
      dtype=int64), 'cur_cost': 214.0, 'intermediate_solutions': [{'tour': array([ 9,  8,  4, 10,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 231.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([10,  9,  8,  4,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 10,  9,  8,  4, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 222.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 4, 10,  9,  8,  6, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 225.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 4,  6, 10,  9,  8, 13,  2, 12,  0,  3,  5, 11,  1, 14,  7]), 'cur_cost': 206.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 2, 4, 13, 8, 7, 12, 6, 11, 5, 10, 9, 1, 3], 'cur_cost': 202.0, 'intermediate_solutions': [{'tour': [10, 5, 2, 3, 13, 7, 14, 11, 9, 8, 1, 0, 12, 4, 6], 'cur_cost': 191.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [10, 5, 2, 4, 13, 7, 14, 11, 12, 0, 1, 8, 9, 3, 6], 'cur_cost': 204.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [10, 5, 2, 0, 4, 13, 7, 14, 11, 9, 8, 1, 12, 3, 6], 'cur_cost': 219.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 13, 0, 6, 2, 7, 12, 8, 9, 14, 5, 10, 3, 11, 4], 'cur_cost': 174.0, 'intermediate_solutions': [{'tour': [1, 10, 0, 11, 2, 6, 5, 12, 14, 8, 13, 7, 9, 4, 3], 'cur_cost': 244.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 10, 0, 11, 2, 8, 14, 9, 5, 6, 13, 7, 12, 4, 3], 'cur_cost': 216.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 10, 8, 0, 11, 2, 6, 5, 9, 14, 13, 7, 12, 4, 3], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [13, 9, 2, 0, 10, 11, 7, 1, 12, 14, 8, 3, 5, 6, 4], 'cur_cost': 231.0, 'intermediate_solutions': [{'tour': [7, 14, 10, 3, 9, 1, 0, 12, 4, 5, 8, 6, 2, 13, 11], 'cur_cost': 205.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 6, 10, 3, 9, 1, 0, 12, 4, 5, 8, 14, 2, 11, 13], 'cur_cost': 187.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 6, 10, 9, 1, 0, 12, 4, 5, 8, 3, 14, 2, 13, 11], 'cur_cost': 200.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 2, 12, 0, 11, 6, 5, 10, 9, 14, 13, 7, 4, 3], 'cur_cost': 193.0, 'intermediate_solutions': [{'tour': [1, 4, 6, 0, 12, 10, 8, 13, 9, 14, 5, 7, 2, 3, 11], 'cur_cost': 210.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 6, 0, 11, 3, 2, 10, 5, 14, 9, 13, 8, 7, 12], 'cur_cost': 180.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 4, 12, 7, 8, 13, 9, 14, 5, 10, 2, 3, 11], 'cur_cost': 172.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:45,945 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:45,945 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:45,946 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=166.000, 多样性=0.921
2025-08-05 10:28:45,946 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:45,946 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:45,946 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:45,955 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.025317837171330654, 'best_improvement': 0.04597701149425287}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01582278481012684}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.01058363151826236, 'recent_improvements': [-0.022542650590174976, -0.02523317227325704, -0.0437099136266997], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 48, 'new_count': 48, 'count_change': 0, 'old_best_cost': 130.0, 'new_best_cost': 130.0, 'quality_improvement': 0.0, 'old_diversity': 0.7462765957446809, 'new_diversity': 0.7462765957446809, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:45,965 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:45,972 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry6_15_solution.json
2025-08-05 10:28:45,972 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry6_15_20250805_102845.solution
2025-08-05 10:28:45,972 - __main__ - INFO - 实例执行完成 - 运行时间: 1.66s, 最佳成本: 130.0
2025-08-05 10:28:45,972 - __main__ - INFO - 实例 geometry6_15 处理完成
