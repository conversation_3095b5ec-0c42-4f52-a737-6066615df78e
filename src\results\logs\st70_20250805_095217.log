2025-08-05 09:52:17,260 - __main__ - INFO - st70 开始进化第 1 代
2025-08-05 09:52:17,260 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 09:52:17,262 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,266 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=812.000, 多样性=0.981
2025-08-05 09:52:17,269 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:17,273 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.981
2025-08-05 09:52:17,274 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:17,277 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 09:52:17,277 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:17,277 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 09:52:17,277 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 09:52:17,297 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -318.120, 聚类评分: 0.000, 覆盖率: 0.156, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:17,297 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 09:52:17,297 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 09:52:17,298 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 09:52:17,304 - visualization.landscape_visualizer - INFO - 插值约束: 14 个点被约束到最小值 812.00
2025-08-05 09:52:17,423 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_136_20250805_095217.html
2025-08-05 09:52:17,484 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_136_20250805_095217.html
2025-08-05 09:52:17,484 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 136
2025-08-05 09:52:17,484 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 09:52:17,484 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2071秒
2025-08-05 09:52:17,484 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 272, 'max_size': 500, 'hits': 0, 'misses': 272, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 899, 'misses': 482, 'hit_rate': 0.6509775524981897, 'evictions': 382, 'ttl': 7200}}
2025-08-05 09:52:17,485 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -318.11999999999995, 'local_optima_density': 0.2, 'gradient_variance': 2291007.1776, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1564, 'fitness_entropy': 0.9911594714322186, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -318.120)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.156)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358737.2976708, 'performance_metrics': {}}}
2025-08-05 09:52:17,485 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:17,485 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:17,485 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:17,485 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:17,487 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,487 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 09:52:17,487 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,487 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,487 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:17,488 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,488 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,488 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:17,488 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:17,488 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:17,488 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:17,488 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,498 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,498 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,498 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2447.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,498 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2447.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,499 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2447.00)
2025-08-05 09:52:17,499 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:17,499 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:17,499 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,502 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,502 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,502 - ExplorationExpert - INFO - 探索路径生成完成，成本: 982.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,502 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,503 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 982.00)
2025-08-05 09:52:17,503 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:17,503 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:17,503 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,506 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,506 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,506 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1018.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,506 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1018.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,507 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 1018.00)
2025-08-05 09:52:17,507 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:17,507 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:17,507 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,510 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,510 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,510 - ExplorationExpert - INFO - 探索路径生成完成，成本: 968.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,511 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 968.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,511 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 968.00)
2025-08-05 09:52:17,511 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:17,511 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,511 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,512 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3699.0
2025-08-05 09:52:17,523 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 09:52:17,523 - ExploitationExpert - INFO - res_population_costs: [705.0, 696, 682, 681]
2025-08-05 09:52:17,524 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-08-05 09:52:17,525 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,526 - ExploitationExpert - INFO - populations: [{'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2447.0}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1018.0}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 968.0}, {'tour': array([ 9, 64, 50, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45], dtype=int64), 'cur_cost': 3699.0}, {'tour': array([61, 19, 18, 31, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3754.0}, {'tour': array([35, 10, 34, 44, 53,  9, 30,  1, 40, 54, 18,  6, 58, 12, 51,  2, 65,
       39, 48, 67, 38, 49, 47, 27, 46,  7, 41, 52, 36, 60, 22,  8, 16, 15,
       24, 59, 57, 55, 29, 17,  5, 42, 56, 62, 11, 13, 68, 25, 43, 28, 69,
       66, 19, 63, 23, 37, 21,  0, 20, 31,  4,  3, 33, 45, 26, 32, 50, 14,
       61, 64], dtype=int64), 'cur_cost': 3547.0}, {'tour': array([31, 29,  2, 66, 25, 16, 46, 63, 28, 12, 34, 36, 35,  4, 43,  1, 51,
       47, 57, 65, 60, 33, 64, 55, 67, 18, 54, 10, 21, 58,  0, 44, 40, 26,
       32, 49, 42, 53, 17, 24, 39, 52, 13, 27, 45, 50, 68, 23,  5, 14, 19,
       30, 20, 48, 38,  3, 37,  7, 22, 15,  9, 62, 56,  6, 11, 69, 41, 61,
       59,  8], dtype=int64), 'cur_cost': 3317.0}, {'tour': array([11, 39, 64, 58, 57,  3,  0, 59, 30, 43, 18, 44, 24, 36, 27, 45, 12,
       65, 37, 14, 49, 67,  5, 32, 61, 48, 26,  7, 62, 23, 54, 47,  6, 53,
       22, 29, 21, 51, 33, 17, 28,  1,  8, 34,  2, 25, 31, 46, 68,  9, 55,
       15, 69, 52, 56, 40, 50, 10, 66, 38, 60, 35, 13,  4, 16, 42, 19, 20,
       41, 63], dtype=int64), 'cur_cost': 3501.0}, {'tour': array([21, 40, 34, 53, 11, 43, 66, 58, 12, 69, 62, 41, 32, 15,  0,  9, 17,
       24, 19, 44,  6,  3, 56, 38, 54, 42, 29, 61, 63, 50, 18, 22, 68, 57,
        5, 36, 13, 45, 67, 30,  4, 60, 26,  8, 65,  2, 47, 64,  7, 39, 28,
       16, 33, 25, 37, 31, 14, 20, 51, 10, 27, 59, 49, 52, 48, 23,  1, 55,
       35, 46], dtype=int64), 'cur_cost': 3355.0}]
2025-08-05 09:52:17,528 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:17,529 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 352, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 352, 'cache_hits': 0, 'similarity_calculations': 1824, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,530 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 9, 64, 50, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45], dtype=int64), 'cur_cost': 3699.0, 'intermediate_solutions': [{'tour': array([42, 48, 37, 64, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([64, 42, 48, 37, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([54, 64, 42, 48, 37, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([37, 64, 42, 48, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([37, 54, 64, 42, 48, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,530 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3699.00)
2025-08-05 09:52:17,530 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:17,530 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,530 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,531 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3438.0
2025-08-05 09:52:17,542 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:17,542 - ExploitationExpert - INFO - res_population_costs: [705.0, 696, 682, 681, 680, 679]
2025-08-05 09:52:17,542 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-08-05 09:52:17,545 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,545 - ExploitationExpert - INFO - populations: [{'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2447.0}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1018.0}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 968.0}, {'tour': array([ 9, 64, 50, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45], dtype=int64), 'cur_cost': 3699.0}, {'tour': array([ 9, 68, 29, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47], dtype=int64), 'cur_cost': 3438.0}, {'tour': array([35, 10, 34, 44, 53,  9, 30,  1, 40, 54, 18,  6, 58, 12, 51,  2, 65,
       39, 48, 67, 38, 49, 47, 27, 46,  7, 41, 52, 36, 60, 22,  8, 16, 15,
       24, 59, 57, 55, 29, 17,  5, 42, 56, 62, 11, 13, 68, 25, 43, 28, 69,
       66, 19, 63, 23, 37, 21,  0, 20, 31,  4,  3, 33, 45, 26, 32, 50, 14,
       61, 64], dtype=int64), 'cur_cost': 3547.0}, {'tour': array([31, 29,  2, 66, 25, 16, 46, 63, 28, 12, 34, 36, 35,  4, 43,  1, 51,
       47, 57, 65, 60, 33, 64, 55, 67, 18, 54, 10, 21, 58,  0, 44, 40, 26,
       32, 49, 42, 53, 17, 24, 39, 52, 13, 27, 45, 50, 68, 23,  5, 14, 19,
       30, 20, 48, 38,  3, 37,  7, 22, 15,  9, 62, 56,  6, 11, 69, 41, 61,
       59,  8], dtype=int64), 'cur_cost': 3317.0}, {'tour': array([11, 39, 64, 58, 57,  3,  0, 59, 30, 43, 18, 44, 24, 36, 27, 45, 12,
       65, 37, 14, 49, 67,  5, 32, 61, 48, 26,  7, 62, 23, 54, 47,  6, 53,
       22, 29, 21, 51, 33, 17, 28,  1,  8, 34,  2, 25, 31, 46, 68,  9, 55,
       15, 69, 52, 56, 40, 50, 10, 66, 38, 60, 35, 13,  4, 16, 42, 19, 20,
       41, 63], dtype=int64), 'cur_cost': 3501.0}, {'tour': array([21, 40, 34, 53, 11, 43, 66, 58, 12, 69, 62, 41, 32, 15,  0,  9, 17,
       24, 19, 44,  6,  3, 56, 38, 54, 42, 29, 61, 63, 50, 18, 22, 68, 57,
        5, 36, 13, 45, 67, 30,  4, 60, 26,  8, 65,  2, 47, 64,  7, 39, 28,
       16, 33, 25, 37, 31, 14, 20, 51, 10, 27, 59, 49, 52, 48, 23,  1, 55,
       35, 46], dtype=int64), 'cur_cost': 3355.0}]
2025-08-05 09:52:17,550 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:17,550 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 353, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 353, 'cache_hits': 0, 'similarity_calculations': 1825, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,552 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 9, 68, 29, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47], dtype=int64), 'cur_cost': 3438.0, 'intermediate_solutions': [{'tour': array([18, 19, 61, 31, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3846.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 18, 19, 61, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 31, 18, 19, 61, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([61, 31, 18, 19, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3779.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([61, 35, 31, 18, 19, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3713.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,552 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 3438.00)
2025-08-05 09:52:17,552 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:17,553 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:17,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,567 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2535.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,568 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2535.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,568 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 2535.00)
2025-08-05 09:52:17,568 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:17,568 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:17,568 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,571 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,572 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1007.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,572 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1007.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,572 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1007.00)
2025-08-05 09:52:17,572 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:17,573 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:17,573 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,581 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,581 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,581 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2635.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,581 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2635.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,582 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2635.00)
2025-08-05 09:52:17,582 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:17,582 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:17,582 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,589 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,589 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2357.0, 路径长度: 70, 收集中间解: 0
2025-08-05 09:52:17,590 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2357.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,590 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2357.00)
2025-08-05 09:52:17,590 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:17,590 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:17,592 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2447.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1018.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 968.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 64, 50, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45], dtype=int64), 'cur_cost': 3699.0, 'intermediate_solutions': [{'tour': array([42, 48, 37, 64, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3677.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([64, 42, 48, 37, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([54, 64, 42, 48, 37, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3663.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([37, 64, 42, 48, 54, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3500.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([37, 54, 64, 42, 48, 11,  8, 67, 51, 44,  9,  2, 43, 12, 18, 23, 60,
       20, 39,  3, 25,  1, 36, 22, 32, 57,  5, 31, 30, 50, 33, 53, 28, 52,
       66, 41, 16, 29, 49, 45, 63, 47, 38, 40, 56, 10, 62, 15, 27, 13, 24,
       58, 55, 61, 17, 34, 59,  7,  0, 14, 68, 26, 21, 65, 19,  4, 35,  6,
       46, 69], dtype=int64), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 68, 29, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47], dtype=int64), 'cur_cost': 3438.0, 'intermediate_solutions': [{'tour': array([18, 19, 61, 31, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3846.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 18, 19, 61, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3789.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 31, 18, 19, 61, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3730.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([61, 31, 18, 19, 35, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3779.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([61, 35, 31, 18, 19, 44, 33, 65, 64, 32, 43, 53, 21, 63,  1, 48, 34,
       24,  9, 67, 40, 57, 52, 47, 28, 54, 13, 66, 25, 10, 58, 62,  0, 68,
       27,  7, 37, 42, 69,  4, 22,  8, 56,  3, 26,  2, 45, 14, 12, 39, 49,
       23, 16, 55,  6, 36, 41, 38, 60, 17, 51, 29,  5, 50, 30, 46, 11, 59,
       15, 20], dtype=int64), 'cur_cost': 3713.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2535.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1007.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2635.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2357.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:17,592 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:17,592 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,596 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=968.000, 多样性=0.982
2025-08-05 09:52:17,597 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 09:52:17,597 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 09:52:17,597 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:17,597 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.014289762844720577, 'best_improvement': -0.1921182266009852}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0006472491909383052}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.032140331183580347, 'recent_improvements': [-0.030616685194940114, -0.0026544078951941945, 0.033663977172220576], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 679, 'new_best_cost': 679, 'quality_improvement': 0.0, 'old_diversity': 0.8466666666666667, 'new_diversity': 0.8466666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 09:52:17,598 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 09:52:17,598 - __main__ - INFO - st70 开始进化第 2 代
2025-08-05 09:52:17,598 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 09:52:17,599 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,599 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=968.000, 多样性=0.982
2025-08-05 09:52:17,600 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:17,602 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.982
2025-08-05 09:52:17,603 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:17,605 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.847
2025-08-05 09:52:17,608 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 09:52:17,608 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:17,608 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:17,608 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:17,673 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.062, 适应度梯度: -359.075, 聚类评分: 0.000, 覆盖率: 0.158, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:17,674 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 09:52:17,674 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:17,674 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 09:52:17,679 - visualization.landscape_visualizer - INFO - 插值约束: 107 个点被约束到最小值 679.00
2025-08-05 09:52:17,803 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_137_20250805_095217.html
2025-08-05 09:52:17,872 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_137_20250805_095217.html
2025-08-05 09:52:17,872 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 137
2025-08-05 09:52:17,872 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 09:52:17,872 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2644秒
2025-08-05 09:52:17,873 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.0625, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -359.07500000000005, 'local_optima_density': 0.0625, 'gradient_variance': 737228.489375, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1578, 'fitness_entropy': 0.9042492561839792, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -359.075)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.158)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358737.6733923, 'performance_metrics': {}}}
2025-08-05 09:52:17,873 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:17,873 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:17,873 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:17,873 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:17,873 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,874 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 09:52:17,874 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,874 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,874 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:17,874 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore']
2025-08-05 09:52:17,874 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:17,875 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:17,875 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:17,875 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:17,875 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:17,875 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,881 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2223.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,883 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2223.0, 'intermediate_solutions': [{'tour': [42, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 40, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2440.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 60, 29, 1, 18, 68, 34, 58, 64, 15, 59, 9, 21, 41, 3, 32, 38, 43, 8, 31, 2, 7, 56, 12, 22, 28, 69, 23, 25, 26, 24, 61, 44, 11, 10, 57, 0, 35, 63, 55, 46, 53, 39, 27, 42, 20, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2465.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 25, 23, 69, 26, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2640.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,884 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 2223.00)
2025-08-05 09:52:17,884 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:17,884 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:17,884 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,886 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,886 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,887 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,887 - ExplorationExpert - INFO - 探索路径生成完成，成本: 983.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,888 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [35, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 0, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 64, 55, 66, 47, 61, 53, 32, 33, 20, 11, 59, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 48, 31, 2, 7, 27, 25, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,888 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 983.00)
2025-08-05 09:52:17,888 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:17,888 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:17,888 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,891 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,891 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,892 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1100.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,892 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1100.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 7, 37, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 27, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63, 35], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 10, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,892 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 1100.00)
2025-08-05 09:52:17,892 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:17,892 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:17,893 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,895 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,896 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1063.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,896 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [0, 15, 2, 46, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 6, 22, 35, 63, 31, 48, 54], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 49, 50, 64, 55, 10, 47, 66, 32, 53, 61, 26, 45, 24, 44, 38, 60, 39, 8, 67, 43, 29, 19, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 14, 48, 54], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,897 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 1063.00)
2025-08-05 09:52:17,897 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:17,897 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,897 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,897 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3682.0
2025-08-05 09:52:17,908 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:17,908 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:17,908 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:17,911 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,911 - ExploitationExpert - INFO - populations: [{'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2223.0}, {'tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 983.0}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1100.0}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1063.0}, {'tour': array([51, 15, 38, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49], dtype=int64), 'cur_cost': 3682.0}, {'tour': [9, 68, 29, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32, 43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14, 5, 3, 41, 20, 34, 46, 0, 26, 4, 50, 42, 12, 65, 36, 1, 30, 63, 38, 8, 21, 10, 69, 24, 39, 16, 13, 58, 31, 6, 2, 11, 23, 27, 66, 35, 62, 25, 7, 48, 22, 47], 'cur_cost': 3438.0}, {'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2535.0}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1007.0}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2635.0}, {'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2357.0}]
2025-08-05 09:52:17,913 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:17,913 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 354, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 354, 'cache_hits': 0, 'similarity_calculations': 1827, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,914 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([51, 15, 38, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49], dtype=int64), 'cur_cost': 3682.0, 'intermediate_solutions': [{'tour': array([50, 64,  9, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([63, 50, 64,  9, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3714.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 63, 50, 64,  9, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3739.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 63, 50, 64, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3712.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 35, 63, 50, 64, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3740.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,914 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3682.00)
2025-08-05 09:52:17,915 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:17,915 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:17,915 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:17,915 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3620.0
2025-08-05 09:52:17,926 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:17,926 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:17,926 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:17,930 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:17,930 - ExploitationExpert - INFO - populations: [{'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2223.0}, {'tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 983.0}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1100.0}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1063.0}, {'tour': array([51, 15, 38, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49], dtype=int64), 'cur_cost': 3682.0}, {'tour': array([23, 37, 17, 27,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40], dtype=int64), 'cur_cost': 3620.0}, {'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2535.0}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1007.0}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2635.0}, {'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2357.0}]
2025-08-05 09:52:17,931 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:17,931 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 355, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 355, 'cache_hits': 0, 'similarity_calculations': 1830, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:17,932 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([23, 37, 17, 27,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40], dtype=int64), 'cur_cost': 3620.0, 'intermediate_solutions': [{'tour': array([29, 68,  9, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 29, 68,  9, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 51, 29, 68,  9, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 51, 29, 68, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 49, 51, 29, 68, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:17,932 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 3620.00)
2025-08-05 09:52:17,932 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:17,932 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:17,933 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,936 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,936 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,937 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 927.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,937 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 64, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 22, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2587.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 6, 19, 29, 58, 61], 'cur_cost': 2508.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 54, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,937 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 927.00)
2025-08-05 09:52:17,938 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:17,938 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:17,938 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,941 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,941 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 953.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,942 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [0, 14, 50, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 7, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 43, 67, 42, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 33, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,942 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 953.00)
2025-08-05 09:52:17,942 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:17,942 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:17,942 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,945 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:17,945 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,946 - ExplorationExpert - INFO - 探索路径生成完成，成本: 998.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,947 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 998.0, 'intermediate_solutions': [{'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 43, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 6, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2599.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 60, 48, 54, 29, 43, 18, 30, 56, 69, 50, 39, 55, 32, 37, 34, 21, 27, 6, 26, 24, 41, 53, 63, 35], 'cur_cost': 2658.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 15, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2630.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,947 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 998.00)
2025-08-05 09:52:17,947 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 09:52:17,947 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 09:52:17,947 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:17,955 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:17,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,956 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,957 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:17,957 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2374.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:17,957 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [6, 21, 51, 9, 32, 57, 22, 69, 68, 58, 40, 67, 2, 5, 1, 13, 11, 26, 45, 8, 42, 43, 52, 16, 19, 18, 4, 55, 15, 64, 59, 47, 61, 66, 39, 50, 53, 65, 36, 63, 46, 0, 37, 56, 62, 31, 20, 33, 10, 3, 7, 48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44, 24, 30], 'cur_cost': 2374.0, 'intermediate_solutions': [{'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 63, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 20, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2479.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 16, 29, 48, 7, 1, 21, 36, 50, 57, 9, 23, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 62, 69, 5, 14, 24, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:17,957 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 2374.00)
2025-08-05 09:52:17,958 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:17,958 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:17,960 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2223.0, 'intermediate_solutions': [{'tour': [42, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 40, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 26, 25, 23, 69, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2440.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 60, 29, 1, 18, 68, 34, 58, 64, 15, 59, 9, 21, 41, 3, 32, 38, 43, 8, 31, 2, 7, 56, 12, 22, 28, 69, 23, 25, 26, 24, 61, 44, 11, 10, 57, 0, 35, 63, 55, 46, 53, 39, 27, 42, 20, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2465.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 67, 16, 49, 65, 17, 4, 62, 51, 36, 52, 37, 30, 5, 13, 20, 42, 27, 39, 53, 46, 55, 63, 35, 0, 57, 10, 11, 44, 61, 24, 25, 23, 69, 26, 28, 22, 12, 56, 7, 2, 31, 8, 43, 38, 32, 3, 41, 21, 9, 59, 15, 64, 58, 34, 68, 18, 1, 29, 60, 45, 33, 47, 6, 54, 19, 14, 48, 50, 66], 'cur_cost': 2640.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 983.0, 'intermediate_solutions': [{'tour': [35, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 0, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 982.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 64, 55, 66, 47, 61, 53, 32, 33, 20, 11, 59, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1006.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 1, 16, 42, 40, 5, 41, 3, 6, 48, 31, 2, 7, 27, 25, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1100.0, 'intermediate_solutions': [{'tour': [0, 1, 8, 7, 37, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 27, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1250.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 63, 35], 'cur_cost': 983.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 8, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 3, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 10, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1088.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [0, 15, 2, 46, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 6, 22, 35, 63, 31, 48, 54], 'cur_cost': 1157.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 25, 7, 27, 13, 49, 50, 64, 55, 10, 47, 66, 32, 53, 61, 26, 45, 24, 44, 38, 60, 39, 8, 67, 43, 29, 19, 57, 36, 46, 22, 35, 63, 31, 48, 54], 'cur_cost': 1106.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 2, 6, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 23, 18, 25, 7, 27, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 31, 14, 48, 54], 'cur_cost': 1023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([51, 15, 38, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49], dtype=int64), 'cur_cost': 3682.0, 'intermediate_solutions': [{'tour': array([50, 64,  9, 63, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([63, 50, 64,  9, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3714.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 63, 50, 64,  9, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3739.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 63, 50, 64, 35, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3712.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 35, 63, 50, 64, 23, 47, 51, 41, 52,  4, 34, 44, 46, 10, 12, 31,
       57, 19, 25,  5, 33,  0, 48, 55,  8, 17, 20, 60, 68, 15, 36, 49, 26,
        7, 32, 18,  3,  6, 54, 30, 53, 16, 27, 59, 65, 39, 37, 43, 40, 29,
        1, 69, 61, 28, 56, 11, 67, 22,  2, 13, 21, 14, 24, 42, 62, 58, 38,
       66, 45]), 'cur_cost': 3740.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([23, 37, 17, 27,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40], dtype=int64), 'cur_cost': 3620.0, 'intermediate_solutions': [{'tour': array([29, 68,  9, 51, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3424.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 29, 68,  9, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 51, 29, 68,  9, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 9, 51, 29, 68, 49, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3429.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 9, 49, 51, 29, 68, 40, 15, 18, 45, 55, 33, 61, 28, 64, 44, 59, 32,
       43, 54, 57, 53, 19, 67, 60, 56, 37, 17, 52, 14,  5,  3, 41, 20, 34,
       46,  0, 26,  4, 50, 42, 12, 65, 36,  1, 30, 63, 38,  8, 21, 10, 69,
       24, 39, 16, 13, 58, 31,  6,  2, 11, 23, 27, 66, 35, 62, 25,  7, 48,
       22, 47]), 'cur_cost': 3418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 927.0, 'intermediate_solutions': [{'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 64, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 22, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2587.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 6, 19, 29, 58, 61], 'cur_cost': 2508.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [40, 52, 17, 7, 45, 5, 42, 39, 53, 59, 11, 8, 31, 48, 16, 27, 60, 10, 49, 4, 65, 12, 23, 34, 14, 41, 51, 50, 15, 0, 54, 68, 22, 37, 36, 55, 46, 30, 3, 18, 2, 43, 25, 20, 44, 47, 57, 35, 56, 67, 24, 13, 38, 32, 63, 66, 26, 33, 9, 64, 28, 69, 62, 21, 1, 58, 29, 19, 6, 61], 'cur_cost': 2584.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 953.0, 'intermediate_solutions': [{'tour': [0, 14, 50, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 7, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1206.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 43, 67, 42, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1041.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 7, 5, 40, 41, 17, 3, 1, 6, 31, 2, 27, 25, 48, 54, 18, 23, 33, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 67, 43, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1130.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 998.0, 'intermediate_solutions': [{'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 26, 43, 27, 21, 34, 37, 32, 55, 39, 50, 69, 56, 30, 18, 6, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2599.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 15, 11, 45, 13, 7, 3, 14, 17, 19, 60, 48, 54, 29, 43, 18, 30, 56, 69, 50, 39, 55, 32, 37, 34, 21, 27, 6, 26, 24, 41, 53, 63, 35], 'cur_cost': 2658.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [38, 8, 33, 4, 59, 22, 0, 64, 42, 61, 5, 40, 51, 52, 16, 44, 47, 67, 31, 62, 36, 10, 66, 20, 2, 25, 58, 46, 23, 65, 1, 9, 12, 28, 57, 68, 49, 11, 45, 13, 7, 3, 14, 17, 19, 26, 6, 27, 21, 34, 15, 37, 32, 55, 39, 50, 69, 56, 30, 18, 43, 29, 54, 48, 60, 24, 41, 53, 63, 35], 'cur_cost': 2630.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [6, 21, 51, 9, 32, 57, 22, 69, 68, 58, 40, 67, 2, 5, 1, 13, 11, 26, 45, 8, 42, 43, 52, 16, 19, 18, 4, 55, 15, 64, 59, 47, 61, 66, 39, 50, 53, 65, 36, 63, 46, 0, 37, 56, 62, 31, 20, 33, 10, 3, 7, 48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44, 24, 30], 'cur_cost': 2374.0, 'intermediate_solutions': [{'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 63, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 20, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2479.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 62, 69, 5, 14, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 24, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 16, 29, 48, 7, 1, 21, 36, 50, 57, 9, 23, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2380.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 62, 69, 5, 14, 24, 35, 56, 22, 15, 68, 17, 67, 13, 40, 65, 52, 46, 37, 59, 4, 10, 11, 41, 20, 42, 44, 53, 38, 8, 66, 0, 58, 28, 18, 31, 26, 45, 60, 19, 2, 25, 27, 33, 61, 47, 32, 51, 64, 63, 30, 23, 9, 57, 50, 36, 21, 1, 7, 48, 29, 16, 3, 43, 6, 34, 54, 39, 55, 49], 'cur_cost': 2495.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 09:52:17,960 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:17,960 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,964 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=927.000, 多样性=0.949
2025-08-05 09:52:17,965 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 09:52:17,965 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 09:52:17,965 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:17,966 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06565198369887189, 'best_improvement': 0.042355371900826444}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.03331177231565305}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008472085369957388, 'recent_improvements': [-0.0026544078951941945, 0.033663977172220576, 0.014289762844720577], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 679, 'new_best_cost': 679, 'quality_improvement': 0.0, 'old_diversity': 0.8466666666666667, 'new_diversity': 0.8466666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 09:52:17,966 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 09:52:17,967 - __main__ - INFO - st70 开始进化第 3 代
2025-08-05 09:52:17,967 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 09:52:17,967 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:17,968 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=927.000, 多样性=0.949
2025-08-05 09:52:17,968 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:17,971 - PathExpert - INFO - 路径结构分析完成: 公共边数量=30, 路径相似性=0.949
2025-08-05 09:52:17,972 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:17,973 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.847
2025-08-05 09:52:17,976 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 09:52:17,976 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:17,976 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:17,976 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:18,025 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.188, 适应度梯度: -248.988, 聚类评分: 0.000, 覆盖率: 0.159, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:18,027 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 09:52:18,027 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:18,027 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 09:52:18,032 - visualization.landscape_visualizer - INFO - 插值约束: 224 个点被约束到最小值 679.00
2025-08-05 09:52:18,159 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_138_20250805_095218.html
2025-08-05 09:52:18,216 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_138_20250805_095218.html
2025-08-05 09:52:18,216 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 138
2025-08-05 09:52:18,216 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 09:52:18,216 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2396秒
2025-08-05 09:52:18,216 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1875, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -248.9875, 'local_optima_density': 0.1875, 'gradient_variance': 937699.3573437501, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1585, 'fitness_entropy': 0.875, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -248.988)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.159)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358738.0259945, 'performance_metrics': {}}}
2025-08-05 09:52:18,217 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:18,217 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:18,217 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:18,217 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:18,217 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,217 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 09:52:18,218 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,218 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,218 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:18,218 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,218 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,218 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:18,219 - experts.management.collaboration_manager - INFO - 识别精英个体: {6, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:18,219 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:18,219 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:18,219 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,222 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,223 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,223 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,224 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1013.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,225 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 40, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 15, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 32, 5, 21, 62, 7, 42, 18, 48, 67, 8, 39, 40, 31, 45, 13, 29, 27, 3, 53, 61, 47, 10, 51, 59, 24, 20, 64, 49, 34, 12, 15, 66, 55, 22, 35, 57, 9, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2233.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 3, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2290.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,225 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1013.00)
2025-08-05 09:52:18,225 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:18,225 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:18,225 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,228 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,228 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,229 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 979.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,229 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [0, 5, 10, 19, 13, 29, 25, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 43, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1082.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 35, 22, 15, 46, 36, 57, 49, 50, 64, 55, 47, 66, 32, 53, 61, 26, 45, 24, 44, 38, 60, 39, 8, 40, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 63], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 19, 13, 29, 43, 10, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,230 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 979.00)
2025-08-05 09:52:18,230 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:18,230 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:18,230 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,232 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,232 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,233 - ExplorationExpert - INFO - 探索路径生成完成，成本: 923.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,233 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 47, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 12, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1381.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 50, 51, 59, 11, 33, 20, 16, 42, 41, 40, 5, 65, 52, 4, 9, 49, 57, 36, 15, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 45, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,233 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 923.00)
2025-08-05 09:52:18,233 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:18,233 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:18,233 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,235 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 09:52:18,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,235 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,236 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,236 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,236 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2636.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,236 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2636.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 39, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 41, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 15, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 40, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,236 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 2636.00)
2025-08-05 09:52:18,236 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:18,237 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,237 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,237 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3549.0
2025-08-05 09:52:18,248 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:18,248 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:18,248 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:18,251 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,251 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1013.0}, {'tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 979.0}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 923.0}, {'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2636.0}, {'tour': array([30, 38,  8, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37], dtype=int64), 'cur_cost': 3549.0}, {'tour': [23, 37, 17, 27, 5, 15, 60, 31, 11, 19, 52, 47, 28, 66, 3, 30, 12, 42, 35, 4, 24, 58, 46, 65, 1, 51, 55, 61, 43, 32, 36, 68, 0, 48, 57, 64, 2, 49, 29, 21, 14, 8, 25, 54, 33, 9, 67, 41, 45, 22, 69, 20, 59, 16, 10, 6, 38, 53, 34, 18, 63, 56, 50, 62, 13, 7, 39, 44, 26, 40], 'cur_cost': 3620.0}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 927.0}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 953.0}, {'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 998.0}, {'tour': [6, 21, 51, 9, 32, 57, 22, 69, 68, 58, 40, 67, 2, 5, 1, 13, 11, 26, 45, 8, 42, 43, 52, 16, 19, 18, 4, 55, 15, 64, 59, 47, 61, 66, 39, 50, 53, 65, 36, 63, 46, 0, 37, 56, 62, 31, 20, 33, 10, 3, 7, 48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44, 24, 30], 'cur_cost': 2374.0}]
2025-08-05 09:52:18,252 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:18,252 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 356, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 356, 'cache_hits': 0, 'similarity_calculations': 1834, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,253 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([30, 38,  8, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37], dtype=int64), 'cur_cost': 3549.0, 'intermediate_solutions': [{'tour': array([38, 15, 51, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 38, 15, 51,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3692.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 56, 38, 15, 51, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 56, 38, 15,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  8, 56, 38, 15, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,254 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3549.00)
2025-08-05 09:52:18,254 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:18,254 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,254 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,254 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3629.0
2025-08-05 09:52:18,268 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:18,268 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:18,268 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:18,271 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,271 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1013.0}, {'tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 979.0}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 923.0}, {'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2636.0}, {'tour': array([30, 38,  8, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37], dtype=int64), 'cur_cost': 3549.0}, {'tour': array([49, 36, 12, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28], dtype=int64), 'cur_cost': 3629.0}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 927.0}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 953.0}, {'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 998.0}, {'tour': [6, 21, 51, 9, 32, 57, 22, 69, 68, 58, 40, 67, 2, 5, 1, 13, 11, 26, 45, 8, 42, 43, 52, 16, 19, 18, 4, 55, 15, 64, 59, 47, 61, 66, 39, 50, 53, 65, 36, 63, 46, 0, 37, 56, 62, 31, 20, 33, 10, 3, 7, 48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44, 24, 30], 'cur_cost': 2374.0}]
2025-08-05 09:52:18,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:18,274 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 357, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 357, 'cache_hits': 0, 'similarity_calculations': 1839, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,274 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([49, 36, 12, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28], dtype=int64), 'cur_cost': 3629.0, 'intermediate_solutions': [{'tour': array([17, 37, 23, 27,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 17, 37, 23,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 27, 17, 37, 23, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3588.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 27, 17, 37,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23,  5, 27, 17, 37, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,275 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 3629.00)
2025-08-05 09:52:18,275 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:18,275 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:18,275 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,278 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,278 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,279 - ExplorationExpert - INFO - 探索路径生成完成，成本: 981.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,279 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 981.0, 'intermediate_solutions': [{'tour': [0, 5, 15, 4, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 9, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 52, 13, 10, 63], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 21, 62, 65, 37, 56, 14, 23, 1, 6, 58, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,279 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 981.00)
2025-08-05 09:52:18,279 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:18,279 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:18,279 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,281 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,282 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,282 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1080.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,283 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1080.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 69, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 4, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 26, 8, 60, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 15, 46, 22, 23, 14, 56, 34, 69, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 58, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,283 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 1080.00)
2025-08-05 09:52:18,283 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:18,283 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:18,283 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,286 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,286 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,286 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,286 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,287 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 991.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,287 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 22, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 31, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 1170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 3, 17, 41, 5, 35, 63], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 21, 3, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 35, 63], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,287 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 991.00)
2025-08-05 09:52:18,287 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:18,287 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,287 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,288 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3662.0
2025-08-05 09:52:18,297 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:18,297 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:18,298 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:18,300 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,300 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1013.0}, {'tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 979.0}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 923.0}, {'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2636.0}, {'tour': array([30, 38,  8, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37], dtype=int64), 'cur_cost': 3549.0}, {'tour': array([49, 36, 12, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28], dtype=int64), 'cur_cost': 3629.0}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 981.0}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1080.0}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0}, {'tour': array([52, 16, 54, 57, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65], dtype=int64), 'cur_cost': 3662.0}]
2025-08-05 09:52:18,302 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:18,302 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 358, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 358, 'cache_hits': 0, 'similarity_calculations': 1845, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,303 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([52, 16, 54, 57, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65], dtype=int64), 'cur_cost': 3662.0, 'intermediate_solutions': [{'tour': array([51, 21,  6,  9, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2416.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 51, 21,  6, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32,  9, 51, 21,  6, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  9, 51, 21, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 32,  9, 51, 21, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,303 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3662.00)
2025-08-05 09:52:18,303 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:18,303 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:18,306 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 40, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 3, 27, 29, 13, 45, 31, 15, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 32, 5, 21, 62, 7, 42, 18, 48, 67, 8, 39, 40, 31, 45, 13, 29, 27, 3, 53, 61, 47, 10, 51, 59, 24, 20, 64, 49, 34, 12, 15, 66, 55, 22, 35, 57, 9, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2233.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [54, 1, 2, 14, 4, 16, 33, 11, 41, 56, 28, 68, 17, 9, 57, 35, 22, 55, 66, 15, 3, 12, 34, 49, 64, 20, 24, 59, 51, 10, 47, 61, 53, 27, 29, 13, 45, 31, 40, 39, 8, 67, 48, 18, 42, 7, 62, 21, 5, 32, 50, 36, 46, 30, 65, 69, 52, 0, 63, 60, 44, 26, 38, 19, 25, 43, 23, 6, 58, 37], 'cur_cost': 2290.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 979.0, 'intermediate_solutions': [{'tour': [0, 5, 10, 19, 13, 29, 25, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 43, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1082.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 10, 19, 13, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 35, 22, 15, 46, 36, 57, 49, 50, 64, 55, 47, 66, 32, 53, 61, 26, 45, 24, 44, 38, 60, 39, 8, 40, 41, 17, 3, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 63], 'cur_cost': 1018.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 19, 13, 29, 43, 10, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 923.0, 'intermediate_solutions': [{'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 47, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 12, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1381.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 50, 51, 59, 11, 33, 20, 16, 42, 41, 40, 5, 65, 52, 4, 9, 49, 57, 36, 15, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1163.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 10, 17, 3, 1, 6, 31, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 45, 4, 52, 65, 5, 40, 41, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1212.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2636.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 39, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 41, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1236.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 15, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1156.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 9, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 40, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 35, 63], 'cur_cost': 1168.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 38,  8, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37], dtype=int64), 'cur_cost': 3549.0, 'intermediate_solutions': [{'tour': array([38, 15, 51, 56,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([56, 38, 15, 51,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3692.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 56, 38, 15, 51, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3710.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([51, 56, 38, 15,  8, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3703.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([51,  8, 56, 38, 15, 11, 58, 40, 48, 47, 62, 16, 35,  9, 13, 30, 55,
       53, 32, 39,  7,  5, 54, 60, 36, 59, 46, 10, 44, 69, 27, 45, 21,  6,
       63, 64, 67, 41,  1, 52, 57, 68, 34, 61, 50,  0, 37, 66, 14,  4, 26,
       24, 25, 20, 31, 22, 29, 42, 18, 43, 12,  2,  3, 23, 28, 65, 19, 33,
       17, 49]), 'cur_cost': 3711.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 36, 12, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28], dtype=int64), 'cur_cost': 3629.0, 'intermediate_solutions': [{'tour': array([17, 37, 23, 27,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([27, 17, 37, 23,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 5, 27, 17, 37, 23, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3588.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([23, 27, 17, 37,  5, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([23,  5, 27, 17, 37, 15, 60, 31, 11, 19, 52, 47, 28, 66,  3, 30, 12,
       42, 35,  4, 24, 58, 46, 65,  1, 51, 55, 61, 43, 32, 36, 68,  0, 48,
       57, 64,  2, 49, 29, 21, 14,  8, 25, 54, 33,  9, 67, 41, 45, 22, 69,
       20, 59, 16, 10,  6, 38, 53, 34, 18, 63, 56, 50, 62, 13,  7, 39, 44,
       26, 40]), 'cur_cost': 3598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 981.0, 'intermediate_solutions': [{'tour': [0, 5, 15, 4, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 9, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 1053.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 58, 21, 62, 65, 37, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 52, 13, 10, 63], 'cur_cost': 1020.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 15, 4, 9, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 28, 12, 30, 68, 69, 34, 21, 62, 65, 37, 56, 14, 23, 1, 6, 58, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 52, 10, 63], 'cur_cost': 1008.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1080.0, 'intermediate_solutions': [{'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 69, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 4, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1101.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 26, 8, 60, 39, 24, 44, 38, 61, 32, 53, 66, 47, 10, 64, 55, 50, 49, 57, 36, 15, 46, 22, 23, 14, 56, 34, 69, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1146.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 3, 7, 27, 25, 48, 54, 18, 6, 31, 2, 58, 41, 17, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 35, 63], 'cur_cost': 1035.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0, 'intermediate_solutions': [{'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 22, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 31, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 35, 63], 'cur_cost': 1170.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 1, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 3, 17, 41, 5, 35, 63], 'cur_cost': 1010.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 1, 21, 3, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 18, 6, 31, 2, 7, 27, 25, 48, 54, 19, 29, 43, 67, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 35, 63], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 16, 54, 57, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65], dtype=int64), 'cur_cost': 3662.0, 'intermediate_solutions': [{'tour': array([51, 21,  6,  9, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2416.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 51, 21,  6, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2405.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32,  9, 51, 21,  6, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2409.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6,  9, 51, 21, 32, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6, 32,  9, 51, 21, 57, 22, 69, 68, 58, 40, 67,  2,  5,  1, 13, 11,
       26, 45,  8, 42, 43, 52, 16, 19, 18,  4, 55, 15, 64, 59, 47, 61, 66,
       39, 50, 53, 65, 36, 63, 46,  0, 37, 56, 62, 31, 20, 33, 10,  3,  7,
       48, 25, 17, 23, 14, 12, 41, 54, 29, 27, 38, 49, 35, 34, 28, 60, 44,
       24, 30]), 'cur_cost': 2390.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:18,307 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:18,307 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:18,310 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=923.000, 多样性=0.960
2025-08-05 09:52:18,311 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 09:52:18,311 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 09:52:18,311 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:18,311 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.025020135266392435, 'best_improvement': 0.004314994606256742}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011709601873536373}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.015994003263325656, 'recent_improvements': [0.033663977172220576, 0.014289762844720577, 0.06565198369887189], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 679, 'new_best_cost': 679, 'quality_improvement': 0.0, 'old_diversity': 0.8466666666666667, 'new_diversity': 0.8466666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:18,312 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 09:52:18,312 - __main__ - INFO - st70 开始进化第 4 代
2025-08-05 09:52:18,312 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 09:52:18,313 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:18,313 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=923.000, 多样性=0.960
2025-08-05 09:52:18,314 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:18,317 - PathExpert - INFO - 路径结构分析完成: 公共边数量=30, 路径相似性=0.960
2025-08-05 09:52:18,317 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:18,319 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.847
2025-08-05 09:52:18,321 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 09:52:18,322 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:18,322 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 09:52:18,322 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 09:52:18,389 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.062, 适应度梯度: -340.462, 聚类评分: 0.000, 覆盖率: 0.159, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:18,389 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 09:52:18,389 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:18,389 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 09:52:18,405 - visualization.landscape_visualizer - INFO - 插值约束: 475 个点被约束到最小值 679.00
2025-08-05 09:52:18,514 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_139_20250805_095218.html
2025-08-05 09:52:18,601 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_139_20250805_095218.html
2025-08-05 09:52:18,601 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 139
2025-08-05 09:52:18,601 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 09:52:18,601 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2797秒
2025-08-05 09:52:18,602 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.0625, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -340.4625, 'local_optima_density': 0.0625, 'gradient_variance': 1211704.26859375, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.159, 'fitness_entropy': 0.8744996115311403, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -340.462)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.159)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358738.389429, 'performance_metrics': {}}}
2025-08-05 09:52:18,602 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:18,602 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:18,602 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:18,602 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:18,604 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,604 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 09:52:18,604 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,604 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,604 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:18,604 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,604 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,605 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:18,605 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:18,605 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:18,605 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:18,605 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,609 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,610 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,610 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,611 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,612 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1063.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,613 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 45, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 12, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1352.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 43, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1094.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 6, 21, 62, 58, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63, 37], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,613 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 1063.00)
2025-08-05 09:52:18,614 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:18,614 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:18,614 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,616 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 70
2025-08-05 09:52:18,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,616 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,617 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3119.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,617 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3119.0, 'intermediate_solutions': [{'tour': [0, 21, 2, 9, 4, 52, 1, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 65, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 65, 52, 4, 9, 2, 21, 0, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 35, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,618 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 3119.00)
2025-08-05 09:52:18,618 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:18,618 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:18,618 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,624 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:18,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,624 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,625 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,625 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2607.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,625 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2607.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 51, 59, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 63, 66, 47, 10, 64, 55, 50, 35], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 1, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,625 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 2607.00)
2025-08-05 09:52:18,625 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:18,626 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:18,626 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,628 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,628 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1013.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,630 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 61, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 54, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2832.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 20, 21, 63, 6, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2618.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 15, 64, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2676.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,630 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 1013.00)
2025-08-05 09:52:18,630 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:18,631 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,631 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,631 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3617.0
2025-08-05 09:52:18,642 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:18,643 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:18,643 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:18,646 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,646 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1063.0}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3119.0}, {'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2607.0}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1013.0}, {'tour': array([45,  3, 24, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60], dtype=int64), 'cur_cost': 3617.0}, {'tour': [49, 36, 12, 51, 6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24, 1, 9, 16, 44, 4, 30, 7, 54, 60, 47, 29, 57, 27, 3, 10, 19, 32, 41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65, 0, 15, 59, 5, 64, 20, 2, 39, 8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34, 17, 28], 'cur_cost': 3629.0}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 981.0}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1080.0}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0}, {'tour': [52, 16, 54, 57, 21, 19, 58, 26, 31, 32, 36, 9, 37, 42, 41, 50, 62, 48, 67, 38, 39, 0, 34, 10, 7, 27, 66, 18, 22, 24, 45, 68, 56, 14, 61, 3, 46, 28, 12, 8, 17, 29, 1, 63, 47, 6, 5, 53, 51, 13, 60, 55, 23, 25, 20, 43, 64, 33, 4, 40, 15, 2, 11, 69, 30, 44, 49, 35, 59, 65], 'cur_cost': 3662.0}]
2025-08-05 09:52:18,647 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:18,648 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 359, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 359, 'cache_hits': 0, 'similarity_calculations': 1852, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,649 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([45,  3, 24, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60], dtype=int64), 'cur_cost': 3617.0, 'intermediate_solutions': [{'tour': array([ 8, 38, 30, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 38, 30,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 13,  8, 38, 30, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 13,  8, 38,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  4, 13,  8, 38, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,649 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3617.00)
2025-08-05 09:52:18,649 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:18,649 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,649 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,649 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3559.0
2025-08-05 09:52:18,660 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 09:52:18,660 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:18,660 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:18,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,664 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1063.0}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3119.0}, {'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2607.0}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1013.0}, {'tour': array([45,  3, 24, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60], dtype=int64), 'cur_cost': 3617.0}, {'tour': array([30, 37,  7, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20], dtype=int64), 'cur_cost': 3559.0}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 981.0}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1080.0}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0}, {'tour': [52, 16, 54, 57, 21, 19, 58, 26, 31, 32, 36, 9, 37, 42, 41, 50, 62, 48, 67, 38, 39, 0, 34, 10, 7, 27, 66, 18, 22, 24, 45, 68, 56, 14, 61, 3, 46, 28, 12, 8, 17, 29, 1, 63, 47, 6, 5, 53, 51, 13, 60, 55, 23, 25, 20, 43, 64, 33, 4, 40, 15, 2, 11, 69, 30, 44, 49, 35, 59, 65], 'cur_cost': 3662.0}]
2025-08-05 09:52:18,666 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:18,666 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 360, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 360, 'cache_hits': 0, 'similarity_calculations': 1860, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,667 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([30, 37,  7, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20], dtype=int64), 'cur_cost': 3559.0, 'intermediate_solutions': [{'tour': array([12, 36, 49, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 12, 36, 49,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3644.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 51, 12, 36, 49, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 51, 12, 36,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49,  6, 51, 12, 36, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,668 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 3559.00)
2025-08-05 09:52:18,668 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:18,668 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:18,668 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,676 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:18,676 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,677 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2221.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,678 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2221.0, 'intermediate_solutions': [{'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 48, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 65, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 24, 44, 38, 61, 53, 32, 33, 20, 16, 42, 67, 43, 29, 19, 13, 54, 48, 25, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 7, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,678 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 2221.00)
2025-08-05 09:52:18,678 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:18,678 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:18,679 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,683 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,683 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1007.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,684 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1007.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 22, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 23, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1182.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 61, 53, 32, 33, 11, 59, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 17, 67, 42, 16, 40, 5, 41, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1133.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,685 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 1007.00)
2025-08-05 09:52:18,685 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:18,685 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:18,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,688 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1046.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,690 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 22, 46, 15, 36, 35, 63], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 35, 22, 15, 46, 36, 57, 49, 50, 64, 55, 10, 47, 66, 32, 53, 61, 8, 24, 44, 38, 60, 39, 45, 26, 19, 29, 67, 43, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 51, 63], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 48, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,690 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 1046.00)
2025-08-05 09:52:18,690 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:18,690 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,690 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,691 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3686.0
2025-08-05 09:52:18,702 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:18,702 - ExploitationExpert - INFO - res_population_costs: [679, 680, 681, 682, 696, 705.0, 679]
2025-08-05 09:52:18,702 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       66, 47, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64)]
2025-08-05 09:52:18,705 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:18,706 - ExploitationExpert - INFO - populations: [{'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1063.0}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3119.0}, {'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2607.0}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1013.0}, {'tour': array([45,  3, 24, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60], dtype=int64), 'cur_cost': 3617.0}, {'tour': array([30, 37,  7, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20], dtype=int64), 'cur_cost': 3559.0}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2221.0}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1007.0}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1046.0}, {'tour': array([47, 36, 13,  3, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0], dtype=int64), 'cur_cost': 3686.0}]
2025-08-05 09:52:18,707 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 09:52:18,707 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 361, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 361, 'cache_hits': 0, 'similarity_calculations': 1869, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:18,708 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([47, 36, 13,  3, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0], dtype=int64), 'cur_cost': 3686.0, 'intermediate_solutions': [{'tour': array([54, 16, 52, 57, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([57, 54, 16, 52, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 57, 54, 16, 52, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 57, 54, 16, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 21, 57, 54, 16, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:18,709 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3686.00)
2025-08-05 09:52:18,709 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:18,709 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:18,712 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1063.0, 'intermediate_solutions': [{'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 45, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 12, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1352.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 6, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 43, 67, 45, 26, 8, 60, 39, 24, 44, 38, 61, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63], 'cur_cost': 1094.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 6, 21, 62, 58, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 13, 27, 7, 25, 48, 54, 18, 31, 2, 35, 63, 37], 'cur_cost': 1039.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3119.0, 'intermediate_solutions': [{'tour': [0, 21, 2, 9, 4, 52, 1, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 65, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1052.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [42, 40, 5, 41, 17, 3, 18, 54, 48, 25, 27, 7, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 65, 52, 4, 9, 2, 21, 0, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1062.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 7, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 35, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1158.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2607.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 51, 59, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 938.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 63, 66, 47, 10, 64, 55, 50, 35], 'cur_cost': 937.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 16, 42, 40, 5, 41, 17, 3, 6, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 26, 45, 39, 60, 38, 44, 24, 61, 53, 32, 11, 20, 33, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 1, 12, 28, 69, 34, 56, 14, 23, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35], 'cur_cost': 1017.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1013.0, 'intermediate_solutions': [{'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 61, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 54, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2832.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 15, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 64, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 20, 21, 63, 6, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2618.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 15, 64, 2, 16, 42, 52, 65, 41, 58, 3, 68, 30, 12, 9, 7, 27, 56, 14, 54, 13, 5, 40, 43, 67, 25, 33, 11, 18, 38, 17, 4, 61, 53, 32, 46, 69, 36, 57, 49, 50, 55, 10, 47, 66, 22, 35, 24, 45, 44, 28, 39, 60, 8, 26, 23, 29, 0, 34, 6, 63, 21, 20, 48, 1, 19, 31, 62, 59, 37], 'cur_cost': 2676.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([45,  3, 24, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60], dtype=int64), 'cur_cost': 3617.0, 'intermediate_solutions': [{'tour': array([ 8, 38, 30, 13,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3657.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([13,  8, 38, 30,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3592.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4, 13,  8, 38, 30, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3556.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 13,  8, 38,  4, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3531.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  4, 13,  8, 38, 62, 41, 24, 16, 36, 39, 66, 33, 58, 40, 26, 29,
       44, 50,  0, 34, 67, 15, 54, 28,  2, 52, 53, 51, 11, 68, 57, 27, 31,
       20,  7, 59, 32, 61,  5,  6, 49, 42, 10, 56, 23, 48, 60,  1, 55, 21,
       35, 14, 63, 18, 64, 45, 17, 69, 19,  3, 43, 25, 65, 12,  9, 47, 22,
       46, 37]), 'cur_cost': 3544.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 37,  7, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20], dtype=int64), 'cur_cost': 3559.0, 'intermediate_solutions': [{'tour': array([12, 36, 49, 51,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3553.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([51, 12, 36, 49,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3644.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 51, 12, 36, 49, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3617.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([49, 51, 12, 36,  6, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3638.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([49,  6, 51, 12, 36, 35, 62, 14, 67, 43, 37, 68, 18, 40, 45, 63, 24,
        1,  9, 16, 44,  4, 30,  7, 54, 60, 47, 29, 57, 27,  3, 10, 19, 32,
       41, 25, 50, 58, 52, 13, 61, 42, 48, 46, 26, 21, 53, 65,  0, 15, 59,
        5, 64, 20,  2, 39,  8, 31, 33, 23, 11, 66, 22, 38, 55, 56, 69, 34,
       17, 28]), 'cur_cost': 3636.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2221.0, 'intermediate_solutions': [{'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 48, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 65, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1186.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 24, 44, 38, 61, 53, 32, 33, 20, 16, 42, 67, 43, 29, 19, 13, 54, 48, 25, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1091.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 11, 10, 47, 66, 55, 64, 50, 49, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 7, 48, 54, 13, 19, 29, 43, 67, 42, 16, 20, 33, 32, 53, 61, 38, 44, 24, 39, 60, 8, 26, 45, 40, 5, 41, 17, 3, 51, 59, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 991.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1007.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 22, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 23, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1182.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 67, 42, 16, 40, 5, 41, 17, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 61, 53, 32, 33, 11, 59, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 2, 31, 6, 18, 25, 7, 27, 13, 19, 29, 43, 17, 67, 42, 16, 40, 5, 41, 3, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 48, 54], 'cur_cost': 1133.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1046.0, 'intermediate_solutions': [{'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 22, 46, 15, 36, 35, 63], 'cur_cost': 1025.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 35, 22, 15, 46, 36, 57, 49, 50, 64, 55, 10, 47, 66, 32, 53, 61, 8, 24, 44, 38, 60, 39, 45, 26, 19, 29, 67, 43, 18, 54, 48, 25, 27, 7, 2, 31, 6, 1, 23, 14, 56, 34, 69, 28, 12, 30, 68, 37, 58, 62, 21, 65, 52, 4, 51, 63], 'cur_cost': 1024.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 9, 3, 17, 41, 5, 40, 42, 16, 20, 48, 33, 11, 59, 51, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 54, 18, 43, 67, 29, 19, 26, 45, 39, 60, 38, 44, 24, 8, 61, 53, 32, 66, 47, 10, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 1132.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 36, 13,  3, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0], dtype=int64), 'cur_cost': 3686.0, 'intermediate_solutions': [{'tour': array([54, 16, 52, 57, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([57, 54, 16, 52, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([21, 57, 54, 16, 52, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3639.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([52, 57, 54, 16, 21, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([52, 21, 57, 54, 16, 19, 58, 26, 31, 32, 36,  9, 37, 42, 41, 50, 62,
       48, 67, 38, 39,  0, 34, 10,  7, 27, 66, 18, 22, 24, 45, 68, 56, 14,
       61,  3, 46, 28, 12,  8, 17, 29,  1, 63, 47,  6,  5, 53, 51, 13, 60,
       55, 23, 25, 20, 43, 64, 33,  4, 40, 15,  2, 11, 69, 30, 44, 49, 35,
       59, 65]), 'cur_cost': 3619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:18,712 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:18,713 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:18,716 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1007.000, 多样性=0.963
2025-08-05 09:52:18,716 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 09:52:18,716 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 09:52:18,717 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:18,717 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.09243919672223729, 'best_improvement': -0.09100758396533044}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0033068783068780634}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.019654949055556505, 'recent_improvements': [0.014289762844720577, 0.06565198369887189, -0.025020135266392435], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 679, 'new_best_cost': 679, 'quality_improvement': 0.0, 'old_diversity': 0.8408163265306122, 'new_diversity': 0.8408163265306122, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:18,718 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 09:52:18,718 - __main__ - INFO - st70 开始进化第 5 代
2025-08-05 09:52:18,718 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 09:52:18,718 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:18,719 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=1007.000, 多样性=0.963
2025-08-05 09:52:18,720 - PathExpert - INFO - 开始路径结构分析
2025-08-05 09:52:18,722 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.963
2025-08-05 09:52:18,723 - EliteExpert - INFO - 开始精英解分析
2025-08-05 09:52:18,725 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.841
2025-08-05 09:52:18,728 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 09:52:18,728 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 09:52:18,728 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 09:52:18,728 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 09:52:18,785 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.176, 适应度梯度: -312.918, 聚类评分: 0.000, 覆盖率: 0.160, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 09:52:18,785 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 09:52:18,785 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 09:52:18,785 - visualization.landscape_visualizer - INFO - 设置当前实例名: st70
2025-08-05 09:52:18,791 - visualization.landscape_visualizer - INFO - 插值约束: 323 个点被约束到最小值 679.00
2025-08-05 09:52:18,902 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\landscape_st70_iter_140_20250805_095218.html
2025-08-05 09:52:18,975 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_st70\dashboard_st70_iter_140_20250805_095218.html
2025-08-05 09:52:18,975 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 140
2025-08-05 09:52:18,976 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 09:52:18,976 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2481秒
2025-08-05 09:52:18,976 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.17647058823529413, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -312.91764705882343, 'local_optima_density': 0.17647058823529413, 'gradient_variance': 1189622.7579238755, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1597, 'fitness_entropy': 0.8438055355643016, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -312.918)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.160)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754358738.7851624, 'performance_metrics': {}}}
2025-08-05 09:52:18,976 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 09:52:18,976 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 09:52:18,976 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 09:52:18,976 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 09:52:18,977 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,977 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 09:52:18,977 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,977 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,977 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 09:52:18,977 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 09:52:18,978 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 09:52:18,978 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 09:52:18,978 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 09:52:18,978 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 09:52:18,978 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 09:52:18,978 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,980 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,981 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 931.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,982 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 18, 14, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 31, 2, 35, 63], 'cur_cost': 931.0, 'intermediate_solutions': [{'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 50, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 49, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 29, 43, 67, 8, 26, 45, 24, 38, 44, 39, 60, 63, 10, 51, 59, 50, 64, 55, 66, 47, 61, 53, 32, 33, 20, 16, 42, 41, 40, 5, 34, 65, 19, 13], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 12, 17, 3, 10, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,982 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 931.00)
2025-08-05 09:52:18,982 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 09:52:18,982 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 09:52:18,982 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,984 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,984 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,985 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1061.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,985 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 10, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 42, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 37, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 2994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 9, 49, 10, 36, 15, 46, 22, 39, 45, 26, 54, 7, 67, 5, 0, 50, 56, 29, 23, 69, 2, 33, 25, 1, 37, 16, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 54, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3186.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,986 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 1061.00)
2025-08-05 09:52:18,986 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 09:52:18,986 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 09:52:18,986 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,988 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,989 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,989 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,989 - ExplorationExpert - INFO - 探索路径生成完成，成本: 915.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,989 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 15, 17, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 10], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 44, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 8, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 34, 1, 42, 20, 16, 8, 13, 29, 31, 65, 30, 56, 17, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 33, 66, 0, 64, 67, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,989 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 915.00)
2025-08-05 09:52:18,990 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 09:52:18,990 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 09:52:18,990 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:18,992 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:18,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,992 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:18,993 - ExplorationExpert - INFO - 探索路径生成完成，成本: 929.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:18,993 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 17, 18, 6, 1, 3, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 26, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 4, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 57, 36, 15, 46, 22, 34, 69, 28, 12, 30, 68, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 56, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:18,993 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 929.00)
2025-08-05 09:52:18,993 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 09:52:18,993 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:18,993 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:18,994 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 3517.0
2025-08-05 09:52:19,003 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,004 - ExploitationExpert - INFO - res_population_costs: [679, 679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:19,004 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       66, 47, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:19,007 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,007 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 31, 2, 35, 63], 'cur_cost': 931.0}, {'tour': [0, 7, 10, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1061.0}, {'tour': [0, 15, 17, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 10], 'cur_cost': 915.0}, {'tour': [0, 10, 17, 18, 6, 1, 3, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 929.0}, {'tour': array([40, 66, 13,  8, 52, 10, 22, 17, 31, 11, 59, 19,  1, 51, 14, 21,  4,
       55, 56, 68, 15, 67, 46, 47, 61, 57, 39,  2, 62, 38, 28, 26, 35,  0,
        7, 41, 60, 42, 20, 69, 63, 43, 29, 16, 34, 58, 23, 48, 45, 64, 49,
       30, 32,  5, 33, 24,  9, 27, 54, 65, 18,  3,  6, 36, 25, 50, 44, 53,
       12, 37], dtype=int64), 'cur_cost': 3517.0}, {'tour': [30, 37, 7, 43, 40, 12, 52, 34, 18, 65, 3, 23, 48, 28, 24, 54, 49, 8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53, 2, 69, 9, 57, 11, 41, 51, 56, 13, 63, 33, 4, 15, 59, 22, 36, 66, 5, 60, 58, 47, 68, 26, 6, 1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25, 0, 20], 'cur_cost': 3559.0}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2221.0}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1007.0}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1046.0}, {'tour': [47, 36, 13, 3, 28, 5, 26, 30, 51, 49, 4, 67, 44, 10, 59, 42, 29, 35, 2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46, 25, 58, 69, 8, 7, 33, 9, 18, 6, 27, 17, 37, 38, 52, 57, 65, 15, 21, 11, 60, 56, 68, 54, 20, 1, 23, 43, 19, 22, 55, 34, 40, 64, 62, 48, 0], 'cur_cost': 3686.0}]
2025-08-05 09:52:19,008 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:19,008 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 362, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 362, 'cache_hits': 0, 'similarity_calculations': 1879, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,009 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([40, 66, 13,  8, 52, 10, 22, 17, 31, 11, 59, 19,  1, 51, 14, 21,  4,
       55, 56, 68, 15, 67, 46, 47, 61, 57, 39,  2, 62, 38, 28, 26, 35,  0,
        7, 41, 60, 42, 20, 69, 63, 43, 29, 16, 34, 58, 23, 48, 45, 64, 49,
       30, 32,  5, 33, 24,  9, 27, 54, 65, 18,  3,  6, 36, 25, 50, 44, 53,
       12, 37], dtype=int64), 'cur_cost': 3517.0, 'intermediate_solutions': [{'tour': array([24,  3, 45, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 24,  3, 45, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 52, 24,  3, 45, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([45, 52, 24,  3, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([45, 40, 52, 24,  3, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,009 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 3517.00)
2025-08-05 09:52:19,009 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 09:52:19,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3429.0
2025-08-05 09:52:19,019 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,019 - ExploitationExpert - INFO - res_population_costs: [679, 679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:19,019 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       66, 47, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:19,022 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,022 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 31, 2, 35, 63], 'cur_cost': 931.0}, {'tour': [0, 7, 10, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1061.0}, {'tour': [0, 15, 17, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 10], 'cur_cost': 915.0}, {'tour': [0, 10, 17, 18, 6, 1, 3, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 929.0}, {'tour': array([40, 66, 13,  8, 52, 10, 22, 17, 31, 11, 59, 19,  1, 51, 14, 21,  4,
       55, 56, 68, 15, 67, 46, 47, 61, 57, 39,  2, 62, 38, 28, 26, 35,  0,
        7, 41, 60, 42, 20, 69, 63, 43, 29, 16, 34, 58, 23, 48, 45, 64, 49,
       30, 32,  5, 33, 24,  9, 27, 54, 65, 18,  3,  6, 36, 25, 50, 44, 53,
       12, 37], dtype=int64), 'cur_cost': 3517.0}, {'tour': array([19, 64,  1, 54, 68, 14, 42, 63, 38, 57, 59, 16,  5, 11, 37, 20, 21,
       44, 12, 62, 28, 18, 13,  9, 65, 27, 25, 43, 52, 61, 41, 69, 30, 40,
        0, 48, 60, 50, 33, 46, 34,  3, 31,  8, 66, 10, 32,  6, 15, 55, 49,
       29, 17, 47, 56,  4, 35, 36, 23,  7, 53, 22, 58, 67,  2, 26, 45, 39,
       51, 24], dtype=int64), 'cur_cost': 3429.0}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2221.0}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1007.0}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1046.0}, {'tour': [47, 36, 13, 3, 28, 5, 26, 30, 51, 49, 4, 67, 44, 10, 59, 42, 29, 35, 2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46, 25, 58, 69, 8, 7, 33, 9, 18, 6, 27, 17, 37, 38, 52, 57, 65, 15, 21, 11, 60, 56, 68, 54, 20, 1, 23, 43, 19, 22, 55, 34, 40, 64, 62, 48, 0], 'cur_cost': 3686.0}]
2025-08-05 09:52:19,023 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:19,023 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 363, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 363, 'cache_hits': 0, 'similarity_calculations': 1890, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,024 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([19, 64,  1, 54, 68, 14, 42, 63, 38, 57, 59, 16,  5, 11, 37, 20, 21,
       44, 12, 62, 28, 18, 13,  9, 65, 27, 25, 43, 52, 61, 41, 69, 30, 40,
        0, 48, 60, 50, 33, 46, 34,  3, 31,  8, 66, 10, 32,  6, 15, 55, 49,
       29, 17, 47, 56,  4, 35, 36, 23,  7, 53, 22, 58, 67,  2, 26, 45, 39,
       51, 24], dtype=int64), 'cur_cost': 3429.0, 'intermediate_solutions': [{'tour': array([ 7, 37, 30, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3595.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  7, 37, 30, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 43,  7, 37, 30, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 43,  7, 37, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3653.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 40, 43,  7, 37, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,024 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 3429.00)
2025-08-05 09:52:19,024 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 09:52:19,025 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 09:52:19,025 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,028 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 70
2025-08-05 09:52:19,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,028 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,029 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 901.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:19,029 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 21, 5, 20, 33, 11, 59, 51, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 901.0, 'intermediate_solutions': [{'tour': [12, 49, 48, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 10, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2383.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 45, 44, 39, 34, 0, 9, 51, 24, 60, 26, 27, 46], 'cur_cost': 2279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 21, 6, 16, 29, 17, 58, 50, 64, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2183.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,029 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 901.00)
2025-08-05 09:52:19,029 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 09:52:19,030 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 09:52:19,030 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,038 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:19,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2421.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:19,040 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [17, 29, 48, 31, 4, 11, 50, 0, 22, 52, 30, 49, 68, 9, 40, 7, 13, 42, 51, 46, 59, 5, 32, 10, 57, 33, 20, 67, 8, 27, 56, 14, 28, 12, 65, 3, 37, 6, 25, 26, 39, 47, 61, 66, 24, 60, 19, 18, 34, 62, 41, 36, 63, 55, 53, 45, 44, 2, 16, 21, 15, 58, 23, 1, 54, 69, 35, 64, 38, 43], 'cur_cost': 2421.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 49, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 23, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 32, 33, 20, 11, 59, 51, 35, 65, 52, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 15, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,040 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 2421.00)
2025-08-05 09:52:19,040 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 09:52:19,040 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 09:52:19,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 09:52:19,047 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 70
2025-08-05 09:52:19,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 09:52:19,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2548.0, 路径长度: 70, 收集中间解: 3
2025-08-05 09:52:19,049 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [48, 3, 19, 33, 24, 39, 53, 32, 44, 47, 9, 15, 51, 8, 43, 17, 67, 11, 46, 30, 41, 2, 20, 60, 26, 16, 4, 28, 62, 65, 35, 21, 49, 5, 31, 40, 56, 7, 27, 58, 6, 13, 52, 14, 57, 12, 22, 1, 36, 0, 69, 18, 37, 50, 61, 66, 55, 59, 10, 38, 29, 54, 25, 45, 42, 23, 68, 34, 64, 63], 'cur_cost': 2548.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 24, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 63, 8], 'cur_cost': 1257.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 36, 15, 46, 22, 34, 69, 28, 12, 30, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 24, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 8], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 09:52:19,049 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 2548.00)
2025-08-05 09:52:19,049 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 09:52:19,049 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 09:52:19,049 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 09:52:19,049 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3474.0
2025-08-05 09:52:19,058 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 09:52:19,059 - ExploitationExpert - INFO - res_population_costs: [679, 679, 680, 681, 682, 696, 705.0]
2025-08-05 09:52:19,059 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29,
       43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32, 61,
       53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       66, 47, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49,  4, 52,  9, 51, 59, 50, 55, 64, 63, 10,
       47, 66, 53, 61, 32, 11, 33, 20, 16, 42,  8, 39, 60, 38, 44, 24, 45,
       26, 67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 41,  2, 31, 25, 48, 54,  7, 27, 13, 19,
       29, 43, 67, 26, 45, 44, 24, 38, 60, 39,  8, 42, 16, 20, 33, 11, 32,
       61, 53, 66, 47, 10, 63, 64, 55, 50, 59, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64), array([ 0, 15, 46, 36, 57, 49,  4, 52,  9, 51, 50, 64, 63, 10, 47, 66, 55,
       59, 11, 32, 53, 61, 33, 20, 16, 42,  8, 39, 60, 38, 24, 44, 45, 26,
       67, 43, 29, 19, 13, 27,  7, 54, 48, 25, 31,  2, 41, 40,  5, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 22, 12, 28, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23,
       18,  6,  1,  3, 17,  5, 40, 16, 42, 41, 31,  2,  7, 25, 54, 48, 27,
       13, 19, 29, 43, 67,  8, 26, 45, 24, 44, 39, 60, 38, 61, 53, 32, 20,
       33, 11, 59, 55, 66, 47, 10, 63, 64, 50, 51,  9, 52,  4, 49, 57, 36,
       46, 15], dtype=int64)]
2025-08-05 09:52:19,062 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 09:52:19,062 - ExploitationExpert - INFO - populations: [{'tour': [0, 18, 14, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 31, 2, 35, 63], 'cur_cost': 931.0}, {'tour': [0, 7, 10, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1061.0}, {'tour': [0, 15, 17, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 10], 'cur_cost': 915.0}, {'tour': [0, 10, 17, 18, 6, 1, 3, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 929.0}, {'tour': array([40, 66, 13,  8, 52, 10, 22, 17, 31, 11, 59, 19,  1, 51, 14, 21,  4,
       55, 56, 68, 15, 67, 46, 47, 61, 57, 39,  2, 62, 38, 28, 26, 35,  0,
        7, 41, 60, 42, 20, 69, 63, 43, 29, 16, 34, 58, 23, 48, 45, 64, 49,
       30, 32,  5, 33, 24,  9, 27, 54, 65, 18,  3,  6, 36, 25, 50, 44, 53,
       12, 37], dtype=int64), 'cur_cost': 3517.0}, {'tour': array([19, 64,  1, 54, 68, 14, 42, 63, 38, 57, 59, 16,  5, 11, 37, 20, 21,
       44, 12, 62, 28, 18, 13,  9, 65, 27, 25, 43, 52, 61, 41, 69, 30, 40,
        0, 48, 60, 50, 33, 46, 34,  3, 31,  8, 66, 10, 32,  6, 15, 55, 49,
       29, 17, 47, 56,  4, 35, 36, 23,  7, 53, 22, 58, 67,  2, 26, 45, 39,
       51, 24], dtype=int64), 'cur_cost': 3429.0}, {'tour': [0, 21, 5, 20, 33, 11, 59, 51, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 901.0}, {'tour': [17, 29, 48, 31, 4, 11, 50, 0, 22, 52, 30, 49, 68, 9, 40, 7, 13, 42, 51, 46, 59, 5, 32, 10, 57, 33, 20, 67, 8, 27, 56, 14, 28, 12, 65, 3, 37, 6, 25, 26, 39, 47, 61, 66, 24, 60, 19, 18, 34, 62, 41, 36, 63, 55, 53, 45, 44, 2, 16, 21, 15, 58, 23, 1, 54, 69, 35, 64, 38, 43], 'cur_cost': 2421.0}, {'tour': [48, 3, 19, 33, 24, 39, 53, 32, 44, 47, 9, 15, 51, 8, 43, 17, 67, 11, 46, 30, 41, 2, 20, 60, 26, 16, 4, 28, 62, 65, 35, 21, 49, 5, 31, 40, 56, 7, 27, 58, 6, 13, 52, 14, 57, 12, 22, 1, 36, 0, 69, 18, 37, 50, 61, 66, 55, 59, 10, 38, 29, 54, 25, 45, 42, 23, 68, 34, 64, 63], 'cur_cost': 2548.0}, {'tour': array([55,  3,  7, 35, 14, 31, 28,  0, 58, 18, 61, 22, 64, 27, 26, 36,  2,
       11, 42,  9, 53, 59, 15, 23, 56,  4, 10, 34, 68, 16, 33, 48,  1, 45,
       29, 13, 39,  6, 54,  5, 66, 37, 44, 25, 17, 63, 60, 43, 21, 40, 52,
       41, 24, 47, 20,  8, 30, 57, 67, 51, 46, 38, 19, 49, 50, 69, 65, 12,
       32, 62], dtype=int64), 'cur_cost': 3474.0}]
2025-08-05 09:52:19,063 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 09:52:19,063 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 364, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 364, 'cache_hits': 0, 'similarity_calculations': 1902, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 09:52:19,064 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([55,  3,  7, 35, 14, 31, 28,  0, 58, 18, 61, 22, 64, 27, 26, 36,  2,
       11, 42,  9, 53, 59, 15, 23, 56,  4, 10, 34, 68, 16, 33, 48,  1, 45,
       29, 13, 39,  6, 54,  5, 66, 37, 44, 25, 17, 63, 60, 43, 21, 40, 52,
       41, 24, 47, 20,  8, 30, 57, 67, 51, 46, 38, 19, 49, 50, 69, 65, 12,
       32, 62], dtype=int64), 'cur_cost': 3474.0, 'intermediate_solutions': [{'tour': array([13, 36, 47,  3, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 13, 36, 47, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28,  3, 13, 36, 47,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47,  3, 13, 36, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3692.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 28,  3, 13, 36,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 09:52:19,065 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 3474.00)
2025-08-05 09:52:19,065 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 09:52:19,065 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 09:52:19,068 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 14, 23, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 50, 55, 64, 10, 47, 66, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 27, 7, 25, 48, 54, 6, 1, 31, 2, 35, 63], 'cur_cost': 931.0, 'intermediate_solutions': [{'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 50, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 49, 59, 51, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 12, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 29, 43, 67, 8, 26, 45, 24, 38, 44, 39, 60, 63, 10, 51, 59, 50, 64, 55, 66, 47, 61, 53, 32, 33, 20, 16, 42, 41, 40, 5, 34, 65, 19, 13], 'cur_cost': 1165.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 12, 17, 3, 10, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 69, 28, 35, 22, 46, 15, 36, 57, 49, 9, 4, 52, 65, 34, 5, 40, 41, 42, 16, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 59, 51, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 19, 13], 'cur_cost': 1199.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 10, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 27, 25, 48, 54, 18, 3, 17, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 49, 57, 36, 46, 15, 22, 35, 50, 55, 64, 66, 47, 53, 32, 61, 38, 44, 24, 39, 60, 8, 26, 45, 67, 43, 29, 19, 13, 63], 'cur_cost': 1061.0, 'intermediate_solutions': [{'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 42, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 54, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 59, 61, 13, 41, 57, 37, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 2994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 9, 49, 10, 36, 15, 46, 22, 39, 45, 26, 54, 7, 67, 5, 0, 50, 56, 29, 23, 69, 2, 33, 25, 1, 37, 16, 20, 52, 65, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [51, 8, 11, 17, 6, 40, 62, 31, 16, 37, 1, 25, 33, 2, 69, 23, 29, 56, 50, 0, 5, 67, 7, 26, 45, 39, 22, 46, 15, 36, 10, 49, 9, 20, 52, 65, 54, 59, 61, 13, 41, 57, 42, 21, 66, 58, 48, 68, 47, 64, 24, 35, 34, 53, 32, 55, 63, 60, 4, 44, 38, 43, 18, 27, 12, 19, 3, 30, 14, 28], 'cur_cost': 3186.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 17, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 41, 5, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 22, 35, 63, 10], 'cur_cost': 915.0, 'intermediate_solutions': [{'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 44, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 8, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2636.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [55, 33, 66, 0, 64, 10, 46, 58, 35, 68, 14, 28, 34, 1, 42, 20, 16, 8, 13, 29, 31, 65, 30, 56, 17, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 67, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2531.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [55, 33, 66, 0, 64, 67, 10, 46, 58, 35, 68, 14, 28, 17, 56, 30, 65, 31, 29, 13, 8, 16, 20, 42, 1, 34, 6, 4, 52, 11, 41, 9, 40, 3, 37, 18, 23, 49, 59, 39, 24, 51, 21, 69, 25, 7, 60, 32, 63, 12, 57, 61, 26, 38, 44, 5, 2, 27, 43, 53, 45, 54, 48, 22, 62, 36, 15, 50, 47, 19], 'cur_cost': 2673.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 17, 18, 6, 1, 3, 41, 5, 40, 42, 16, 20, 33, 11, 59, 51, 9, 4, 52, 65, 21, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 31, 2, 7, 27, 25, 48, 54, 13, 19, 29, 43, 67, 8, 39, 60, 38, 44, 24, 45, 26, 61, 53, 32, 66, 47, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63], 'cur_cost': 929.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 26, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 4, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1204.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 58, 37, 57, 36, 15, 46, 22, 34, 69, 28, 12, 30, 68, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 21, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 62, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 56, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 16, 42, 40, 41, 43, 67, 29, 19, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 66, 13,  8, 52, 10, 22, 17, 31, 11, 59, 19,  1, 51, 14, 21,  4,
       55, 56, 68, 15, 67, 46, 47, 61, 57, 39,  2, 62, 38, 28, 26, 35,  0,
        7, 41, 60, 42, 20, 69, 63, 43, 29, 16, 34, 58, 23, 48, 45, 64, 49,
       30, 32,  5, 33, 24,  9, 27, 54, 65, 18,  3,  6, 36, 25, 50, 44, 53,
       12, 37], dtype=int64), 'cur_cost': 3517.0, 'intermediate_solutions': [{'tour': array([24,  3, 45, 52, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3608.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([52, 24,  3, 45, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 52, 24,  3, 45, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([45, 52, 24,  3, 40, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3616.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([45, 40, 52, 24,  3, 39, 15, 56, 34, 21, 35, 29, 44,  1, 51,  2, 38,
       41,  0, 53, 42, 49, 32, 33, 27, 43, 13, 62, 37, 19, 61, 22, 20,  9,
       28, 14, 10, 16, 47,  6, 65, 68, 58, 55, 57, 67,  8, 31, 30, 46, 50,
        7, 66, 26, 17,  4, 18, 36, 23, 11, 54, 12, 64, 59, 69,  5, 48, 25,
       63, 60]), 'cur_cost': 3619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 64,  1, 54, 68, 14, 42, 63, 38, 57, 59, 16,  5, 11, 37, 20, 21,
       44, 12, 62, 28, 18, 13,  9, 65, 27, 25, 43, 52, 61, 41, 69, 30, 40,
        0, 48, 60, 50, 33, 46, 34,  3, 31,  8, 66, 10, 32,  6, 15, 55, 49,
       29, 17, 47, 56,  4, 35, 36, 23,  7, 53, 22, 58, 67,  2, 26, 45, 39,
       51, 24], dtype=int64), 'cur_cost': 3429.0, 'intermediate_solutions': [{'tour': array([ 7, 37, 30, 43, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3595.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  7, 37, 30, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([40, 43,  7, 37, 30, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3462.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 43,  7, 37, 40, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3653.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 40, 43,  7, 37, 12, 52, 34, 18, 65,  3, 23, 48, 28, 24, 54, 49,
        8, 29, 61, 67, 50, 35, 16, 27, 19, 32, 53,  2, 69,  9, 57, 11, 41,
       51, 56, 13, 63, 33,  4, 15, 59, 22, 36, 66,  5, 60, 58, 47, 68, 26,
        6,  1, 31, 64, 17, 42, 46, 21, 62, 10, 44, 38, 55, 14, 39, 45, 25,
        0, 20]), 'cur_cost': 3558.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 5, 20, 33, 11, 59, 51, 9, 4, 52, 65, 62, 58, 37, 68, 30, 12, 28, 69, 34, 56, 14, 23, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 3, 17, 41, 40, 42, 16, 8, 39, 60, 38, 44, 24, 45, 26, 67, 43, 29, 19, 13, 32, 53, 61, 47, 66, 55, 64, 50, 49, 57, 36, 46, 15, 22, 35, 63, 10], 'cur_cost': 901.0, 'intermediate_solutions': [{'tour': [12, 49, 48, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 10, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2383.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 6, 16, 29, 17, 58, 50, 64, 21, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 45, 44, 39, 34, 0, 9, 51, 24, 60, 26, 27, 46], 'cur_cost': 2279.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 49, 10, 22, 5, 65, 30, 62, 59, 57, 36, 4, 32, 55, 63, 53, 61, 33, 38, 67, 7, 41, 42, 2, 19, 25, 13, 31, 48, 3, 1, 68, 15, 52, 37, 69, 23, 21, 6, 16, 29, 17, 58, 50, 64, 11, 43, 18, 54, 56, 14, 35, 28, 66, 47, 8, 20, 40, 51, 9, 0, 34, 39, 44, 45, 24, 60, 26, 27, 46], 'cur_cost': 2183.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [17, 29, 48, 31, 4, 11, 50, 0, 22, 52, 30, 49, 68, 9, 40, 7, 13, 42, 51, 46, 59, 5, 32, 10, 57, 33, 20, 67, 8, 27, 56, 14, 28, 12, 65, 3, 37, 6, 25, 26, 39, 47, 61, 66, 24, 60, 19, 18, 34, 62, 41, 36, 63, 55, 53, 45, 44, 2, 16, 21, 15, 58, 23, 1, 54, 69, 35, 64, 38, 43], 'cur_cost': 2421.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 49, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 23, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 9, 4, 32, 33, 20, 11, 59, 51, 35, 65, 52, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1065.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 16, 42, 40, 5, 41, 17, 1, 15, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 36, 57, 49, 9, 4, 52, 65, 35, 51, 59, 11, 20, 33, 32, 53, 61, 47, 66, 55, 64, 50, 10, 63, 60, 39, 44, 38, 24, 45, 26, 8, 67, 43, 29, 13], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [48, 3, 19, 33, 24, 39, 53, 32, 44, 47, 9, 15, 51, 8, 43, 17, 67, 11, 46, 30, 41, 2, 20, 60, 26, 16, 4, 28, 62, 65, 35, 21, 49, 5, 31, 40, 56, 7, 27, 58, 6, 13, 52, 14, 57, 12, 22, 1, 36, 0, 69, 18, 37, 50, 61, 66, 55, 59, 10, 38, 29, 54, 25, 45, 42, 23, 68, 34, 64, 63], 'cur_cost': 2548.0, 'intermediate_solutions': [{'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 57, 49, 50, 55, 64, 10, 47, 66, 24, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 63, 8], 'cur_cost': 1257.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 36, 15, 46, 22, 34, 69, 28, 12, 30, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 24, 8], 'cur_cost': 1113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 11, 4, 9, 51, 59, 32, 53, 61, 33, 20, 16, 42, 40, 5, 41, 17, 3, 1, 6, 31, 2, 7, 27, 25, 48, 54, 18, 23, 14, 56, 62, 21, 58, 37, 68, 30, 12, 28, 69, 34, 22, 46, 15, 36, 24, 57, 49, 50, 55, 64, 10, 47, 66, 63, 35, 65, 52, 43, 67, 29, 13, 26, 45, 39, 60, 38, 44, 8], 'cur_cost': 1201.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([55,  3,  7, 35, 14, 31, 28,  0, 58, 18, 61, 22, 64, 27, 26, 36,  2,
       11, 42,  9, 53, 59, 15, 23, 56,  4, 10, 34, 68, 16, 33, 48,  1, 45,
       29, 13, 39,  6, 54,  5, 66, 37, 44, 25, 17, 63, 60, 43, 21, 40, 52,
       41, 24, 47, 20,  8, 30, 57, 67, 51, 46, 38, 19, 49, 50, 69, 65, 12,
       32, 62], dtype=int64), 'cur_cost': 3474.0, 'intermediate_solutions': [{'tour': array([13, 36, 47,  3, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 3, 13, 36, 47, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3701.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28,  3, 13, 36, 47,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3637.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([47,  3, 13, 36, 28,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3692.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([47, 28,  3, 13, 36,  5, 26, 30, 51, 49,  4, 67, 44, 10, 59, 42, 29,
       35,  2, 16, 39, 63, 45, 32, 53, 12, 14, 50, 61, 41, 66, 31, 24, 46,
       25, 58, 69,  8,  7, 33,  9, 18,  6, 27, 17, 37, 38, 52, 57, 65, 15,
       21, 11, 60, 56, 68, 54, 20,  1, 23, 43, 19, 22, 55, 34, 40, 64, 62,
       48,  0]), 'cur_cost': 3704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 09:52:19,068 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 09:52:19,069 - StatsExpert - INFO - 开始统计分析
2025-08-05 09:52:19,072 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=901.000, 多样性=0.975
2025-08-05 09:52:19,072 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 09:52:19,073 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 09:52:19,073 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 09:52:19,073 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.09123455001120373, 'best_improvement': 0.10526315789473684}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012195121951219723}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.07904559021055459, 'recent_improvements': [0.06565198369887189, -0.025020135266392435, -0.09243919672223729], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 679, 'new_best_cost': 679, 'quality_improvement': 0.0, 'old_diversity': 0.8408163265306122, 'new_diversity': 0.8408163265306122, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 09:52:19,075 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 09:52:19,079 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\st70_solution.json
2025-08-05 09:52:19,079 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\st70_20250805_095219.solution
2025-08-05 09:52:19,079 - __main__ - INFO - 实例执行完成 - 运行时间: 1.82s, 最佳成本: 679
2025-08-05 09:52:19,080 - __main__ - INFO - 实例 st70 处理完成
