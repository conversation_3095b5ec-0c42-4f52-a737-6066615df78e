2025-08-05 10:28:51,008 - __main__ - INFO - composite4_33 开始进化第 1 代
2025-08-05 10:28:51,008 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:51,010 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,013 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8914.000, 多样性=0.960
2025-08-05 10:28:51,015 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:51,019 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.960
2025-08-05 10:28:51,021 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:51,025 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:51,026 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:51,026 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:51,026 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:51,040 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -5139.320, 聚类评分: 0.000, 覆盖率: 0.091, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:51,040 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:51,041 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:51,041 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 10:28:51,047 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.6%, 梯度: 1228.05 → 1134.37
2025-08-05 10:28:51,179 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_76_20250805_102851.html
2025-08-05 10:28:51,241 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_76_20250805_102851.html
2025-08-05 10:28:51,241 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 76
2025-08-05 10:28:51,241 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:51,242 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2164秒
2025-08-05 10:28:51,242 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 152, 'max_size': 500, 'hits': 0, 'misses': 152, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 509, 'misses': 260, 'hit_rate': 0.6618985695708712, 'evictions': 160, 'ttl': 7200}}
2025-08-05 10:28:51,242 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -5139.3200000000015, 'local_optima_density': 0.2, 'gradient_variance': 620178022.9056001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0911, 'fitness_entropy': 0.9232196723355077, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5139.320)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.091)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360931.0406656, 'performance_metrics': {}}}
2025-08-05 10:28:51,242 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:51,243 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:51,243 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:51,243 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:51,244 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,244 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:51,245 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,245 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,245 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:51,245 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,245 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,245 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:51,246 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:51,246 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:51,246 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:51,246 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,247 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,247 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,248 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14942.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,248 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,248 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 14942.00)
2025-08-05 10:28:51,248 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:51,248 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:51,248 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,251 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,251 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,251 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25696.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,251 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 25696.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,252 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 25696.00)
2025-08-05 10:28:51,252 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:51,252 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:51,252 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,254 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 10:28:51,254 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,254 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41195.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,255 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,255 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 41195.00)
2025-08-05 10:28:51,255 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:51,255 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:51,256 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,257 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,257 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,258 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8929.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,258 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8929.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,258 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 8929.00)
2025-08-05 10:28:51,258 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:51,259 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:51,259 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,260 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,261 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,261 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9642.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,261 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9642.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,261 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9642.00)
2025-08-05 10:28:51,261 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:51,262 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:51,262 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,265 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,267 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25275.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,267 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 25275.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,267 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 25275.00)
2025-08-05 10:28:51,267 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:51,268 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:51,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,270 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,270 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,270 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27665.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,271 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 27665.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,271 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 27665.00)
2025-08-05 10:28:51,271 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:51,271 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:51,271 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,275 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,275 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,275 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21079.0, 路径长度: 33, 收集中间解: 0
2025-08-05 10:28:51,276 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21079.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,276 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 21079.00)
2025-08-05 10:28:51,276 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:51,276 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,276 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,277 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 43934.0
2025-08-05 10:28:51,290 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:51,291 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761]
2025-08-05 10:28:51,291 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:51,293 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,293 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14942.0}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 25696.0}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0}, {'tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8929.0}, {'tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9642.0}, {'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 25275.0}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 27665.0}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21079.0}, {'tour': array([24, 30, 32, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16],
      dtype=int64), 'cur_cost': 43934.0}, {'tour': array([14, 16, 20, 19,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 55049.0}]
2025-08-05 10:28:51,294 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:51,294 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 196, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 196, 'cache_hits': 0, 'similarity_calculations': 888, 'cache_hit_rate': 0.0, 'cache_size': 888}}
2025-08-05 10:28:51,295 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([24, 30, 32, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16],
      dtype=int64), 'cur_cost': 43934.0, 'intermediate_solutions': [{'tour': array([ 1, 19,  8,  2,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  1, 19,  8,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2,  1, 19,  8, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  2,  1, 19,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  4,  2,  1, 19, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,295 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 43934.00)
2025-08-05 10:28:51,296 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:51,296 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,296 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,296 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 47619.0
2025-08-05 10:28:51,310 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:51,311 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:51,311 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-08-05 10:28:51,314 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,314 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14942.0}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 25696.0}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0}, {'tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8929.0}, {'tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9642.0}, {'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 25275.0}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 27665.0}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21079.0}, {'tour': array([24, 30, 32, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16],
      dtype=int64), 'cur_cost': 43934.0}, {'tour': array([21, 30, 18, 19, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2],
      dtype=int64), 'cur_cost': 47619.0}]
2025-08-05 10:28:51,315 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:51,315 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 197, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 197, 'cache_hits': 0, 'similarity_calculations': 889, 'cache_hit_rate': 0.0, 'cache_size': 889}}
2025-08-05 10:28:51,316 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([21, 30, 18, 19, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2],
      dtype=int64), 'cur_cost': 47619.0, 'intermediate_solutions': [{'tour': array([20, 16, 14, 19,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 47664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 20, 16, 14,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 55056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 19, 20, 16, 14, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 54988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 19, 20, 16,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 55037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  6, 19, 20, 16, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 57003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,317 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 47619.00)
2025-08-05 10:28:51,317 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:51,317 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:51,319 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14942.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 25696.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8929.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9642.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 25275.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 27665.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21079.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 30, 32, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16],
      dtype=int64), 'cur_cost': 43934.0, 'intermediate_solutions': [{'tour': array([ 1, 19,  8,  2,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2,  1, 19,  8,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 4,  2,  1, 19,  8, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 8,  2,  1, 19,  4, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54937.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 8,  4,  2,  1, 19, 28,  7, 24, 29, 10,  9, 13, 22, 26, 12, 32, 30,
       25,  6,  5, 11, 23, 27, 15,  0, 14, 20,  3, 31, 21, 18, 17, 16],
      dtype=int64), 'cur_cost': 54269.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 30, 18, 19, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2],
      dtype=int64), 'cur_cost': 47619.0, 'intermediate_solutions': [{'tour': array([20, 16, 14, 19,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 47664.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 20, 16, 14,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 55056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6, 19, 20, 16, 14, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 54988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 19, 20, 16,  6, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 55037.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  6, 19, 20, 16, 32, 13,  1, 26,  9, 22, 18, 28,  7, 10, 11,  0,
       17, 21, 29, 12, 25,  8,  4,  2, 31, 27, 30, 15, 23,  5,  3, 24],
      dtype=int64), 'cur_cost': 57003.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:51,319 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:51,320 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,322 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8929.000, 多样性=0.960
2025-08-05 10:28:51,322 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:51,322 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:51,323 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:51,323 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.1397240390115824, 'best_improvement': -0.0016827462418667265}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -1.1561579183508816e-16}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.11473382427032296, 'recent_improvements': [0.1046073294997182, -0.03916495253519512, -0.12486031904092774], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 8761.0, 'new_best_cost': 8761.0, 'quality_improvement': 0.0, 'old_diversity': 0.6713804713804714, 'new_diversity': 0.6713804713804714, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:51,324 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:51,324 - __main__ - INFO - composite4_33 开始进化第 2 代
2025-08-05 10:28:51,324 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:51,325 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,325 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8929.000, 多样性=0.960
2025-08-05 10:28:51,326 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:51,328 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.960
2025-08-05 10:28:51,328 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:51,331 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.671
2025-08-05 10:28:51,333 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:51,333 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:51,333 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:51,334 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:51,373 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.500, 适应度梯度: -3495.030, 聚类评分: 0.000, 覆盖率: 0.093, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:51,373 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:51,373 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:51,373 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 10:28:51,379 - visualization.landscape_visualizer - INFO - 插值约束: 251 个点被约束到最小值 8761.00
2025-08-05 10:28:51,381 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=3.8%, 梯度: 1201.77 → 1156.44
2025-08-05 10:28:51,487 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_77_20250805_102851.html
2025-08-05 10:28:51,540 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_77_20250805_102851.html
2025-08-05 10:28:51,541 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 77
2025-08-05 10:28:51,541 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:51,541 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2079秒
2025-08-05 10:28:51,541 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -3495.0299999999997, 'local_optima_density': 0.5, 'gradient_variance': 118450159.70510001, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0926, 'fitness_entropy': 0.7091809721940125, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3495.030)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.093)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360931.3731258, 'performance_metrics': {}}}
2025-08-05 10:28:51,542 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:51,542 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:51,542 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:51,542 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:51,542 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,543 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:51,543 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,543 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,543 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:51,543 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,543 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,543 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:51,544 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:51,544 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:51,544 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:51,544 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,545 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,545 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,546 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8934.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,546 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8934.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 6, 1, 2, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 21, 7, 5, 6, 1, 2, 3, 4, 8, 31, 30, 29, 28, 27, 26, 32, 10, 11, 16, 12, 17, 13, 14, 18, 15, 19, 22, 9, 0, 23, 24, 25], 'cur_cost': 14899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 22, 19, 15, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 18, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 17233.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,546 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 8934.00)
2025-08-05 10:28:51,546 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:51,546 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:51,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,547 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,547 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,547 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,548 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,548 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9622.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,548 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9622.0, 'intermediate_solutions': [{'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 24, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 1, 5, 16, 15, 21], 'cur_cost': 30995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 21, 15, 16, 5, 24, 25, 20, 23, 22], 'cur_cost': 25691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 29, 8, 18, 7, 10, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 27770.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,548 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 9622.00)
2025-08-05 10:28:51,548 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:51,549 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:51,549 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,549 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,549 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,550 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,550 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12337.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,550 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 12337.0, 'intermediate_solutions': [{'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 23, 18, 7, 8, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 39791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 30, 3, 1, 31, 12, 15, 22, 24, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41391.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,550 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12337.00)
2025-08-05 10:28:51,550 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:51,551 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:51,551 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,551 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 10:28:51,551 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,552 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36081.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,552 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [22, 8, 15, 9, 17, 4, 3, 10, 1, 6, 14, 13, 32, 27, 26, 28, 30, 29, 31, 0, 2, 12, 5, 11, 24, 7, 23, 20, 21, 25, 16, 18, 19], 'cur_cost': 36081.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 20, 15, 18, 14, 13, 22, 19, 21, 25, 24, 23], 'cur_cost': 23645.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 6, 1, 3, 4, 23, 24, 25, 21, 20, 22, 13, 14, 18, 15, 19, 10, 11, 16, 17, 12, 29, 30, 31, 32, 27, 26, 28, 9, 7, 8], 'cur_cost': 8929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 6, 1, 3, 4, 8, 7, 9, 2, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,552 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 36081.00)
2025-08-05 10:28:51,553 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:51,553 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:51,553 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,554 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,555 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,555 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14913.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,555 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14913.0, 'intermediate_solutions': [{'tour': [0, 18, 15, 16, 11, 17, 26, 13, 14, 19, 10, 32, 12, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 14041.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 24, 25, 23], 'cur_cost': 9702.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 15, 16, 32, 11, 17, 12, 13, 14, 19, 10, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,556 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 14913.00)
2025-08-05 10:28:51,556 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:51,556 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:51,556 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,558 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,558 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,559 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,559 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11641.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,559 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0, 'intermediate_solutions': [{'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 23, 16, 20, 21, 25, 24, 2, 1], 'cur_cost': 32627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 3, 7, 6, 29, 28, 4, 32, 26, 13, 19, 11, 17, 5, 9, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 26032.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 1, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2], 'cur_cost': 25294.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,560 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 11641.00)
2025-08-05 10:28:51,560 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:51,560 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:51,560 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,562 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,563 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11665.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,564 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11665.0, 'intermediate_solutions': [{'tour': [13, 15, 14, 29, 3, 16, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 32, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 29629.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 25, 23, 20, 21, 22, 10, 30, 28, 7, 24], 'cur_cost': 27762.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 28, 10, 22, 21, 20, 23, 25, 30, 7, 24], 'cur_cost': 27877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,564 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 11665.00)
2025-08-05 10:28:51,564 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:51,564 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:51,565 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,566 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,566 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,568 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15763.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,568 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15763.0, 'intermediate_solutions': [{'tour': [7, 30, 2, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 8, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 23, 21, 25, 0, 17, 16, 14], 'cur_cost': 21049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 28, 26, 31, 9, 4, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 20234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,569 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 15763.00)
2025-08-05 10:28:51,569 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:51,569 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,569 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,569 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 49799.0
2025-08-05 10:28:51,591 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:51,592 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:51,592 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-08-05 10:28:51,597 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,597 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8934.0}, {'tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9622.0}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 12337.0}, {'tour': [22, 8, 15, 9, 17, 4, 3, 10, 1, 6, 14, 13, 32, 27, 26, 28, 30, 29, 31, 0, 2, 12, 5, 11, 24, 7, 23, 20, 21, 25, 16, 18, 19], 'cur_cost': 36081.0}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14913.0}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0}, {'tour': [0, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11665.0}, {'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15763.0}, {'tour': array([22, 25,  6, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19],
      dtype=int64), 'cur_cost': 49799.0}, {'tour': [21, 30, 18, 19, 32, 22, 4, 12, 6, 26, 16, 23, 0, 11, 25, 15, 1, 13, 27, 9, 3, 7, 17, 10, 14, 8, 29, 28, 24, 20, 31, 5, 2], 'cur_cost': 47619.0}]
2025-08-05 10:28:51,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:51,598 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 198, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 198, 'cache_hits': 0, 'similarity_calculations': 891, 'cache_hit_rate': 0.0, 'cache_size': 891}}
2025-08-05 10:28:51,599 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([22, 25,  6, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19],
      dtype=int64), 'cur_cost': 49799.0, 'intermediate_solutions': [{'tour': array([32, 30, 24, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 32, 30, 24, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 29, 32, 30, 24, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 43927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 29, 32, 30, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 43972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 27, 29, 32, 30, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,600 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 49799.00)
2025-08-05 10:28:51,600 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:51,600 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,600 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,601 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 53846.0
2025-08-05 10:28:51,623 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:51,624 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:51,624 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-08-05 10:28:51,630 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,630 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8934.0}, {'tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9622.0}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 12337.0}, {'tour': [22, 8, 15, 9, 17, 4, 3, 10, 1, 6, 14, 13, 32, 27, 26, 28, 30, 29, 31, 0, 2, 12, 5, 11, 24, 7, 23, 20, 21, 25, 16, 18, 19], 'cur_cost': 36081.0}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14913.0}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0}, {'tour': [0, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11665.0}, {'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15763.0}, {'tour': array([22, 25,  6, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19],
      dtype=int64), 'cur_cost': 49799.0}, {'tour': array([19,  1, 12,  7,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14],
      dtype=int64), 'cur_cost': 53846.0}]
2025-08-05 10:28:51,632 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:51,632 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 199, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 199, 'cache_hits': 0, 'similarity_calculations': 894, 'cache_hit_rate': 0.0, 'cache_size': 894}}
2025-08-05 10:28:51,634 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([19,  1, 12,  7,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14],
      dtype=int64), 'cur_cost': 53846.0, 'intermediate_solutions': [{'tour': array([18, 30, 21, 19, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 49689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 18, 30, 21, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 19, 18, 30, 21, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 42407.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 19, 18, 30, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 32, 19, 18, 30, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,634 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 53846.00)
2025-08-05 10:28:51,634 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:51,635 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:51,639 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8934.0, 'intermediate_solutions': [{'tour': [0, 9, 22, 19, 15, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 6, 1, 2, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 14950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 21, 7, 5, 6, 1, 2, 3, 4, 8, 31, 30, 29, 28, 27, 26, 32, 10, 11, 16, 12, 17, 13, 14, 18, 15, 19, 22, 9, 0, 23, 24, 25], 'cur_cost': 14899.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 22, 19, 15, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 18, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 7, 21, 20, 23, 24, 25], 'cur_cost': 17233.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9622.0, 'intermediate_solutions': [{'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 24, 30, 3, 14, 19, 10, 29, 8, 18, 7, 22, 23, 20, 25, 1, 5, 16, 15, 21], 'cur_cost': 30995.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 10, 29, 8, 18, 7, 21, 15, 16, 5, 24, 25, 20, 23, 22], 'cur_cost': 25691.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [27, 26, 0, 32, 9, 13, 12, 11, 17, 28, 31, 4, 6, 2, 1, 30, 3, 14, 19, 29, 8, 18, 7, 10, 22, 23, 20, 25, 24, 5, 16, 15, 21], 'cur_cost': 27770.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 21, 25, 24, 23], 'cur_cost': 12337.0, 'intermediate_solutions': [{'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 23, 18, 7, 8, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 39791.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 30, 3, 1, 31, 12, 15, 22, 24, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41391.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 13, 14, 11, 28, 4, 6, 27, 19, 10, 8, 18, 7, 23, 5, 25, 21, 0, 24, 22, 15, 12, 31, 1, 3, 30, 29, 17, 2, 20, 16, 32, 9], 'cur_cost': 41195.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [22, 8, 15, 9, 17, 4, 3, 10, 1, 6, 14, 13, 32, 27, 26, 28, 30, 29, 31, 0, 2, 12, 5, 11, 24, 7, 23, 20, 21, 25, 16, 18, 19], 'cur_cost': 36081.0, 'intermediate_solutions': [{'tour': [0, 2, 5, 6, 1, 3, 4, 8, 7, 9, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 20, 15, 18, 14, 13, 22, 19, 21, 25, 24, 23], 'cur_cost': 23645.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 5, 6, 1, 3, 4, 23, 24, 25, 21, 20, 22, 13, 14, 18, 15, 19, 10, 11, 16, 17, 12, 29, 30, 31, 32, 27, 26, 28, 9, 7, 8], 'cur_cost': 8929.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 6, 1, 3, 4, 8, 7, 9, 2, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 8939.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14913.0, 'intermediate_solutions': [{'tour': [0, 18, 15, 16, 11, 17, 26, 13, 14, 19, 10, 32, 12, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 14041.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 15, 16, 11, 17, 12, 13, 14, 19, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 24, 25, 23], 'cur_cost': 9702.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 15, 16, 32, 11, 17, 12, 13, 14, 19, 10, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11859.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0, 'intermediate_solutions': [{'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 23, 16, 20, 21, 25, 24, 2, 1], 'cur_cost': 32627.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 3, 7, 6, 29, 28, 4, 32, 26, 13, 19, 11, 17, 5, 9, 18, 27, 12, 30, 14, 0, 8, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2, 1], 'cur_cost': 26032.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 3, 7, 6, 29, 28, 4, 9, 5, 17, 11, 19, 13, 26, 32, 18, 27, 12, 30, 14, 0, 8, 1, 31, 15, 10, 16, 23, 20, 21, 25, 24, 2], 'cur_cost': 25294.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11665.0, 'intermediate_solutions': [{'tour': [13, 15, 14, 29, 3, 16, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 32, 18, 11, 19, 2, 12, 6, 10, 22, 21, 20, 23, 25, 30, 28, 7, 24], 'cur_cost': 29629.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 25, 23, 20, 21, 22, 10, 30, 28, 7, 24], 'cur_cost': 27762.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 15, 14, 29, 3, 32, 1, 0, 26, 4, 9, 8, 27, 31, 17, 5, 16, 18, 11, 19, 2, 12, 6, 28, 10, 22, 21, 20, 23, 25, 30, 7, 24], 'cur_cost': 27877.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15763.0, 'intermediate_solutions': [{'tour': [7, 30, 2, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 8, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 21108.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 4, 28, 26, 31, 9, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 23, 21, 25, 0, 17, 16, 14], 'cur_cost': 21049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 30, 8, 27, 3, 11, 10, 29, 28, 26, 31, 9, 4, 32, 6, 1, 2, 13, 12, 19, 18, 5, 15, 22, 24, 20, 21, 23, 25, 0, 17, 16, 14], 'cur_cost': 20234.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 25,  6, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19],
      dtype=int64), 'cur_cost': 49799.0, 'intermediate_solutions': [{'tour': array([32, 30, 24, 29, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44041.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 32, 30, 24, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44175.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 29, 32, 30, 24, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 43927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 29, 32, 30, 27, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 43972.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 27, 29, 32, 30, 15, 13,  6,  4, 18,  9,  1,  7,  2, 10, 23, 17,
       14, 19,  0, 20, 31,  5, 26, 25, 11, 12, 21, 22,  3,  8, 28, 16]), 'cur_cost': 44079.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([19,  1, 12,  7,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14],
      dtype=int64), 'cur_cost': 53846.0, 'intermediate_solutions': [{'tour': array([18, 30, 21, 19, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 49689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([19, 18, 30, 21, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47665.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32, 19, 18, 30, 21, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 42407.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 19, 18, 30, 32, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47538.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21, 32, 19, 18, 30, 22,  4, 12,  6, 26, 16, 23,  0, 11, 25, 15,  1,
       13, 27,  9,  3,  7, 17, 10, 14,  8, 29, 28, 24, 20, 31,  5,  2]), 'cur_cost': 47619.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:51,640 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:51,640 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,644 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8934.000, 多样性=0.869
2025-08-05 10:28:51,645 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:51,645 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:51,645 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:51,647 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.008077348101800353, 'best_improvement': -0.0005599731212901781}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.09537166900420753}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.08944449577338875, 'recent_improvements': [-0.03916495253519512, -0.12486031904092774, 0.1397240390115824], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 8761.0, 'new_best_cost': 8761.0, 'quality_improvement': 0.0, 'old_diversity': 0.6713804713804714, 'new_diversity': 0.6713804713804714, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:51,648 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:51,649 - __main__ - INFO - composite4_33 开始进化第 3 代
2025-08-05 10:28:51,649 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:51,649 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,651 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=8934.000, 多样性=0.869
2025-08-05 10:28:51,651 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:51,656 - PathExpert - INFO - 路径结构分析完成: 公共边数量=16, 路径相似性=0.869
2025-08-05 10:28:51,658 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:51,661 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.671
2025-08-05 10:28:51,664 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:51,664 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:51,665 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:51,665 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:51,701 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.550, 适应度梯度: -2617.130, 聚类评分: 0.000, 覆盖率: 0.093, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:51,701 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:51,701 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:51,701 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 10:28:51,706 - visualization.landscape_visualizer - INFO - 插值约束: 219 个点被约束到最小值 8761.00
2025-08-05 10:28:51,708 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=3.0%, 梯度: 1600.59 → 1552.84
2025-08-05 10:28:51,830 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_78_20250805_102851.html
2025-08-05 10:28:51,889 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_78_20250805_102851.html
2025-08-05 10:28:51,889 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 78
2025-08-05 10:28:51,889 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:51,889 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2247秒
2025-08-05 10:28:51,889 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.55, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -2617.13, 'local_optima_density': 0.55, 'gradient_variance': 115919867.09709999, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0934, 'fitness_entropy': 0.5958800740904867, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2617.130)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.093)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360931.7014935, 'performance_metrics': {}}}
2025-08-05 10:28:51,889 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:51,889 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:51,890 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:51,890 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:51,890 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,890 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:51,890 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,890 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,890 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:51,890 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:51,890 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:51,891 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:51,891 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:51,891 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:51,891 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:51,891 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,892 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,892 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,893 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,893 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11767.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,893 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11767.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 9, 5, 4, 3, 2, 25, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 1, 24, 23], 'cur_cost': 21065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 23, 24, 25, 21, 20, 22, 13, 14, 18, 15], 'cur_cost': 10971.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 24, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 23], 'cur_cost': 16370.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,893 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 11767.00)
2025-08-05 10:28:51,893 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:51,893 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:51,894 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,894 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,894 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,894 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,895 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,895 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9641.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,895 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9641.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 18, 14, 32, 15, 10, 16, 11, 17, 12, 13, 19, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 6, 1, 2, 3, 8, 31, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 10601.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 0, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9613.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,895 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 9641.00)
2025-08-05 10:28:51,895 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:51,896 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:51,896 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,896 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:51,896 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,897 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,897 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12365.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,897 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12365.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 21, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 13, 25, 24, 23], 'cur_cost': 25065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 21, 20, 22, 9, 7, 6, 1, 2, 3, 4, 25, 24, 23], 'cur_cost': 18357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 22, 9, 20, 21, 25, 24, 23], 'cur_cost': 18300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,897 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12365.00)
2025-08-05 10:28:51,897 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:51,897 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,898 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,898 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 50340.0
2025-08-05 10:28:51,909 - ExploitationExpert - INFO - res_population_num: 12
2025-08-05 10:28:51,909 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:51,909 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:51,913 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,913 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11767.0}, {'tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9641.0}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12365.0}, {'tour': array([16, 32, 21, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28],
      dtype=int64), 'cur_cost': 50340.0}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 14913.0}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0}, {'tour': [0, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11665.0}, {'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 6, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 15763.0}, {'tour': [22, 25, 6, 30, 9, 1, 7, 23, 4, 14, 29, 24, 11, 5, 15, 18, 8, 10, 16, 3, 0, 20, 2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19], 'cur_cost': 49799.0}, {'tour': [19, 1, 12, 7, 6, 2, 28, 23, 9, 0, 4, 5, 16, 25, 15, 26, 8, 32, 3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14], 'cur_cost': 53846.0}]
2025-08-05 10:28:51,913 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:51,913 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 200, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 200, 'cache_hits': 0, 'similarity_calculations': 898, 'cache_hit_rate': 0.0, 'cache_size': 898}}
2025-08-05 10:28:51,914 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([16, 32, 21, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28],
      dtype=int64), 'cur_cost': 50340.0, 'intermediate_solutions': [{'tour': array([15,  8, 22,  9, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 34036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 15,  8, 22, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 36085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  9, 15,  8, 22,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 34038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  9, 15,  8, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 36106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 17,  9, 15,  8,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 35398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,914 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 50340.00)
2025-08-05 10:28:51,914 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:51,915 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:51,915 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,915 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 10:28:51,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,916 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39741.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,916 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39741.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 23, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 8, 24, 25], 'cur_cost': 26148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 3, 4, 8, 31, 30, 29, 28, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 15799.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 28, 24, 25], 'cur_cost': 20501.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,917 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 39741.00)
2025-08-05 10:28:51,917 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:51,917 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:51,917 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,918 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40460.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,918 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 0, 8, 22, 23, 16, 29, 13], 'cur_cost': 40460.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 14, 17, 16, 11, 10, 15, 18, 12, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11667.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 20, 22, 13, 14, 18, 15, 10, 11, 16, 17, 12, 29, 30, 31, 32, 21, 25, 24, 23], 'cur_cost': 17184.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,919 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 40460.00)
2025-08-05 10:28:51,919 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:51,919 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:51,919 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,921 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,921 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17472.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,922 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 17472.0, 'intermediate_solutions': [{'tour': [0, 2, 17, 4, 3, 7, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11662.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 17, 7, 3, 24, 25, 21, 20, 22, 13, 14, 18, 15, 19, 10, 11, 16, 12, 29, 30, 31, 32, 27, 26, 28, 8, 6, 1, 9, 5, 4, 23], 'cur_cost': 17695.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 22, 20, 21, 25, 24, 23], 'cur_cost': 14396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,922 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 17472.00)
2025-08-05 10:28:51,922 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:51,922 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:51,922 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:51,924 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:51,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,924 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,925 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:51,925 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19286.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:51,925 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 19286.0, 'intermediate_solutions': [{'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 16, 9, 6, 12, 17, 5, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 21220.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 1, 24, 23, 22, 21, 15, 19, 10, 11, 16, 17, 12, 6, 9, 5, 7, 2, 3, 4, 8, 32, 31, 29, 28, 27, 26, 30, 25, 18, 14, 13], 'cur_cost': 23162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 12, 17, 16, 11, 10, 19, 15, 18, 14, 6, 13], 'cur_cost': 18480.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:51,925 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 19286.00)
2025-08-05 10:28:51,925 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:51,925 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,926 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 54213.0
2025-08-05 10:28:51,937 - ExploitationExpert - INFO - res_population_num: 13
2025-08-05 10:28:51,937 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:51,937 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:51,941 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,941 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11767.0}, {'tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9641.0}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12365.0}, {'tour': array([16, 32, 21, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28],
      dtype=int64), 'cur_cost': 50340.0}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39741.0}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 0, 8, 22, 23, 16, 29, 13], 'cur_cost': 40460.0}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 17472.0}, {'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 19286.0}, {'tour': array([30,  4, 26, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11],
      dtype=int64), 'cur_cost': 54213.0}, {'tour': [19, 1, 12, 7, 6, 2, 28, 23, 9, 0, 4, 5, 16, 25, 15, 26, 8, 32, 3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14], 'cur_cost': 53846.0}]
2025-08-05 10:28:51,942 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:51,942 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 201, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 201, 'cache_hits': 0, 'similarity_calculations': 903, 'cache_hit_rate': 0.0, 'cache_size': 903}}
2025-08-05 10:28:51,943 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([30,  4, 26, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11],
      dtype=int64), 'cur_cost': 54213.0, 'intermediate_solutions': [{'tour': array([ 6, 25, 22, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 49713.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  6, 25, 22,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 49819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 30,  6, 25, 22,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 50483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 30,  6, 25,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 55009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  9, 30,  6, 25,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 55777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,943 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 54213.00)
2025-08-05 10:28:51,943 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:51,943 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:51,943 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:51,943 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 54398.0
2025-08-05 10:28:51,954 - ExploitationExpert - INFO - res_population_num: 16
2025-08-05 10:28:51,954 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761]
2025-08-05 10:28:51,954 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:51,958 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:51,958 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11767.0}, {'tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9641.0}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12365.0}, {'tour': array([16, 32, 21, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28],
      dtype=int64), 'cur_cost': 50340.0}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39741.0}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 0, 8, 22, 23, 16, 29, 13], 'cur_cost': 40460.0}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 17472.0}, {'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 19286.0}, {'tour': array([30,  4, 26, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11],
      dtype=int64), 'cur_cost': 54213.0}, {'tour': array([ 2, 19, 20, 29, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13],
      dtype=int64), 'cur_cost': 54398.0}]
2025-08-05 10:28:51,959 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:51,959 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 202, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 202, 'cache_hits': 0, 'similarity_calculations': 909, 'cache_hit_rate': 0.0, 'cache_size': 909}}
2025-08-05 10:28:51,959 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2, 19, 20, 29, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13],
      dtype=int64), 'cur_cost': 54398.0, 'intermediate_solutions': [{'tour': array([12,  1, 19,  7,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 12,  1, 19,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 56598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  7, 12,  1, 19,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 56596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19,  7, 12,  1,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53846.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19,  6,  7, 12,  1,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53840.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:51,960 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 54398.00)
2025-08-05 10:28:51,960 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:51,960 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:51,961 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11767.0, 'intermediate_solutions': [{'tour': [0, 8, 7, 9, 5, 4, 3, 2, 25, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 1, 24, 23], 'cur_cost': 21065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 19, 23, 24, 25, 21, 20, 22, 13, 14, 18, 15], 'cur_cost': 10971.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 7, 9, 5, 4, 3, 2, 1, 6, 28, 26, 27, 32, 31, 30, 29, 12, 17, 24, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 23], 'cur_cost': 16370.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9641.0, 'intermediate_solutions': [{'tour': [0, 9, 4, 18, 14, 32, 15, 10, 16, 11, 17, 12, 13, 19, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 6, 1, 2, 3, 8, 31, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 10601.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 4, 18, 14, 19, 15, 10, 16, 11, 17, 12, 13, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 0, 5, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9613.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12365.0, 'intermediate_solutions': [{'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 21, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 9, 22, 20, 13, 25, 24, 23], 'cur_cost': 25065.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 21, 20, 22, 9, 7, 6, 1, 2, 3, 4, 25, 24, 23], 'cur_cost': 18357.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 5, 12, 17, 16, 10, 19, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 7, 22, 9, 20, 21, 25, 24, 23], 'cur_cost': 18300.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 32, 21, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28],
      dtype=int64), 'cur_cost': 50340.0, 'intermediate_solutions': [{'tour': array([15,  8, 22,  9, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 34036.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 15,  8, 22, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 36085.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17,  9, 15,  8, 22,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 34038.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22,  9, 15,  8, 17,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 36106.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22, 17,  9, 15,  8,  4,  3, 10,  1,  6, 14, 13, 32, 27, 26, 28, 30,
       29, 31,  0,  2, 12,  5, 11, 24,  7, 23, 20, 21, 25, 16, 18, 19]), 'cur_cost': 35398.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39741.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 28, 29, 30, 31, 23, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 8, 24, 25], 'cur_cost': 26148.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 3, 4, 8, 31, 30, 29, 28, 2, 1, 6, 5, 9, 7, 21, 20, 23, 24, 25], 'cur_cost': 15799.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 14, 18, 19, 15, 10, 16, 11, 17, 12, 32, 26, 27, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 21, 20, 23, 28, 24, 25], 'cur_cost': 20501.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 0, 8, 22, 23, 16, 29, 13], 'cur_cost': 40460.0, 'intermediate_solutions': [{'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 14, 17, 16, 11, 10, 15, 18, 12, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11667.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 20, 22, 13, 14, 18, 15, 10, 11, 16, 17, 12, 29, 30, 31, 32, 21, 25, 24, 23], 'cur_cost': 17184.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 9, 5, 4, 3, 2, 1, 6, 7, 8, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 11, 10, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11641.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 17472.0, 'intermediate_solutions': [{'tour': [0, 2, 17, 4, 3, 7, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11662.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 17, 7, 3, 24, 25, 21, 20, 22, 13, 14, 18, 15, 19, 10, 11, 16, 12, 29, 30, 31, 32, 27, 26, 28, 8, 6, 1, 9, 5, 4, 23], 'cur_cost': 17695.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 2, 17, 7, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 12, 16, 11, 10, 19, 15, 18, 14, 22, 20, 21, 25, 24, 23], 'cur_cost': 14396.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 19286.0, 'intermediate_solutions': [{'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 16, 9, 6, 12, 17, 5, 11, 10, 19, 15, 18, 14, 13], 'cur_cost': 21220.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 1, 24, 23, 22, 21, 15, 19, 10, 11, 16, 17, 12, 6, 9, 5, 7, 2, 3, 4, 8, 32, 31, 29, 28, 27, 26, 30, 25, 18, 14, 13], 'cur_cost': 23162.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 1, 24, 23, 22, 21, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 7, 5, 9, 12, 17, 16, 11, 10, 19, 15, 18, 14, 6, 13], 'cur_cost': 18480.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([30,  4, 26, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11],
      dtype=int64), 'cur_cost': 54213.0, 'intermediate_solutions': [{'tour': array([ 6, 25, 22, 30,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 49713.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  6, 25, 22,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 49819.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 30,  6, 25, 22,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 50483.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([22, 30,  6, 25,  9,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 55009.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([22,  9, 30,  6, 25,  1,  7, 23,  4, 14, 29, 24, 11,  5, 15, 18,  8,
       10, 16,  3,  0, 20,  2, 28, 32, 21, 12, 31, 13, 26, 17, 27, 19]), 'cur_cost': 55777.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 19, 20, 29, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13],
      dtype=int64), 'cur_cost': 54398.0, 'intermediate_solutions': [{'tour': array([12,  1, 19,  7,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53870.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7, 12,  1, 19,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 56598.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 6,  7, 12,  1, 19,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 56596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19,  7, 12,  1,  6,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53846.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19,  6,  7, 12,  1,  2, 28, 23,  9,  0,  4,  5, 16, 25, 15, 26,  8,
       32,  3, 13, 30, 11, 22, 17, 24, 29, 27, 20, 18, 31, 21, 10, 14]), 'cur_cost': 53840.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:51,962 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:51,962 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,964 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9641.000, 多样性=0.953
2025-08-05 10:28:51,964 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:51,964 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:51,964 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:51,965 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.15606710964439188, 'best_improvement': -0.07913588538168793}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.09689922480620136}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.06646883357136406, 'recent_improvements': [-0.12486031904092774, 0.1397240390115824, 0.008077348101800353], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 16, 'new_count': 16, 'count_change': 0, 'old_best_cost': 8761.0, 'new_best_cost': 8761.0, 'quality_improvement': 0.0, 'old_diversity': 0.6636363636363636, 'new_diversity': 0.6636363636363636, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:51,967 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:51,967 - __main__ - INFO - composite4_33 开始进化第 4 代
2025-08-05 10:28:51,967 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:51,967 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:51,968 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9641.000, 多样性=0.953
2025-08-05 10:28:51,968 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:51,969 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.953
2025-08-05 10:28:51,970 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:51,977 - EliteExpert - INFO - 精英解分析完成: 精英解数量=16, 多样性=0.664
2025-08-05 10:28:51,981 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:51,981 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:51,981 - LandscapeExpert - INFO - 添加精英解数据: 16个精英解
2025-08-05 10:28:51,981 - LandscapeExpert - INFO - 数据提取成功: 26个路径, 26个适应度值
2025-08-05 10:28:52,072 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.615, 适应度梯度: -4592.531, 聚类评分: 0.000, 覆盖率: 0.095, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:52,073 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:52,073 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:52,073 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 10:28:52,081 - visualization.landscape_visualizer - INFO - 插值约束: 202 个点被约束到最小值 8761.00
2025-08-05 10:28:52,084 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 1822.99 → 1715.07
2025-08-05 10:28:52,185 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_79_20250805_102852.html
2025-08-05 10:28:52,234 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_79_20250805_102852.html
2025-08-05 10:28:52,234 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 79
2025-08-05 10:28:52,234 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:52,234 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2539秒
2025-08-05 10:28:52,235 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6153846153846154, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -4592.530769230769, 'local_optima_density': 0.6153846153846154, 'gradient_variance': 109292223.6921302, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0947, 'fitness_entropy': 0.5755482412453754, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4592.531)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.095)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360932.0737982, 'performance_metrics': {}}}
2025-08-05 10:28:52,235 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:52,235 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:52,235 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:52,235 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:52,235 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,235 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:52,235 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,236 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,236 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:52,236 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,236 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,236 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:52,236 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:52,236 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:52,236 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:52,236 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,237 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,237 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,237 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,238 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,238 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,238 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11866.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,238 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0, 'intermediate_solutions': [{'tour': [0, 20, 12, 21, 14, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 24394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 18, 15], 'cur_cost': 11777.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 29, 27, 28, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,238 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 11866.00)
2025-08-05 10:28:52,238 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:52,238 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:52,238 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11669.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,240 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11669.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 5, 31, 9, 30, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11517.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 10, 16, 11, 2, 3, 4, 5, 9, 31, 30, 29, 28, 27, 26, 32, 15, 19, 14, 13, 12, 17, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 10, 16, 11, 26, 17, 12, 13, 14, 19, 15, 32, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,240 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 11669.00)
2025-08-05 10:28:52,240 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:52,240 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,242 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11642.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,243 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11642.0, 'intermediate_solutions': [{'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 20, 3, 2, 1, 6, 7, 22, 4, 21, 25, 24, 23], 'cur_cost': 24345.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 9, 31, 30, 29, 28, 27, 26, 32, 14, 13, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 15072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 31, 9, 5, 4, 30, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 13342.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,243 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 11642.00)
2025-08-05 10:28:52,243 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:52,243 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,243 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,243 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 58672.0
2025-08-05 10:28:52,254 - ExploitationExpert - INFO - res_population_num: 19
2025-08-05 10:28:52,254 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761]
2025-08-05 10:28:52,254 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:52,259 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,259 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11669.0}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11642.0}, {'tour': array([ 1, 19,  2, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27],
      dtype=int64), 'cur_cost': 58672.0}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39741.0}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 0, 8, 22, 23, 16, 29, 13], 'cur_cost': 40460.0}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 17472.0}, {'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 19286.0}, {'tour': [30, 4, 26, 21, 3, 5, 25, 12, 8, 7, 6, 9, 14, 0, 16, 15, 19, 24, 13, 28, 1, 18, 22, 29, 17, 2, 32, 27, 31, 23, 10, 20, 11], 'cur_cost': 54213.0}, {'tour': [2, 19, 20, 29, 23, 9, 15, 30, 32, 25, 16, 8, 21, 17, 10, 26, 3, 1, 28, 6, 4, 0, 31, 11, 22, 12, 24, 5, 7, 14, 18, 27, 13], 'cur_cost': 54398.0}]
2025-08-05 10:28:52,259 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:52,259 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 203, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 203, 'cache_hits': 0, 'similarity_calculations': 916, 'cache_hit_rate': 0.0, 'cache_size': 916}}
2025-08-05 10:28:52,260 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 1, 19,  2, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27],
      dtype=int64), 'cur_cost': 58672.0, 'intermediate_solutions': [{'tour': array([21, 32, 16, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 48299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 21, 32, 16, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 17, 21, 32, 16, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 21, 32, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 11, 17, 21, 32, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 48130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,260 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 58672.00)
2025-08-05 10:28:52,260 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:52,260 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:52,260 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,261 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,262 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,262 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,262 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,262 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9653.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,263 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9653.0, 'intermediate_solutions': [{'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 14, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 28, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 23, 16], 'cur_cost': 39758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 7, 14, 26, 9, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 40426.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,263 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9653.00)
2025-08-05 10:28:52,263 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:52,263 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:52,263 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19397.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,266 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0, 'intermediate_solutions': [{'tour': [14, 18, 15, 20, 17, 24, 25, 30, 0, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 26, 8, 22, 23, 16, 29, 13], 'cur_cost': 41597.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 22, 8, 0, 19, 3, 11, 21, 12, 9, 10, 7, 1, 2, 6, 4, 32, 31, 5, 28, 23, 16, 29, 13], 'cur_cost': 45960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 29, 0, 8, 22, 23, 16, 13], 'cur_cost': 38384.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,266 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 19397.00)
2025-08-05 10:28:52,266 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:52,266 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,268 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:52,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,268 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,269 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,269 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21790.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,269 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 21790.0, 'intermediate_solutions': [{'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 21, 10, 32, 23, 24, 20, 25, 22, 17, 8, 1], 'cur_cost': 26855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 25, 20, 24, 23, 32, 10, 17, 11, 22, 21, 8, 1], 'cur_cost': 22697.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 15, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 24834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,269 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21790.00)
2025-08-05 10:28:52,269 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:52,269 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:52,269 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,271 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:52,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,272 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,272 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19297.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,272 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 19297.0, 'intermediate_solutions': [{'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 6, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 11, 12], 'cur_cost': 18019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 2, 4, 5, 24, 22, 30, 10, 14, 18, 19, 31, 15, 16, 13, 0, 3, 32, 26, 11, 27, 17, 28, 29, 8, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 25309.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 31, 32, 3, 0, 13, 16, 15, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 17023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,272 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 19297.00)
2025-08-05 10:28:52,272 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:52,273 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,273 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,273 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 53220.0
2025-08-05 10:28:52,292 - ExploitationExpert - INFO - res_population_num: 20
2025-08-05 10:28:52,292 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761.0]
2025-08-05 10:28:52,292 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64)]
2025-08-05 10:28:52,298 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,298 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11669.0}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11642.0}, {'tour': array([ 1, 19,  2, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27],
      dtype=int64), 'cur_cost': 58672.0}, {'tour': [0, 1, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9653.0}, {'tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 21790.0}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 19297.0}, {'tour': array([24, 29, 11, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4],
      dtype=int64), 'cur_cost': 53220.0}, {'tour': [2, 19, 20, 29, 23, 9, 15, 30, 32, 25, 16, 8, 21, 17, 10, 26, 3, 1, 28, 6, 4, 0, 31, 11, 22, 12, 24, 5, 7, 14, 18, 27, 13], 'cur_cost': 54398.0}]
2025-08-05 10:28:52,299 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:52,300 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 204, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 204, 'cache_hits': 0, 'similarity_calculations': 924, 'cache_hit_rate': 0.0, 'cache_size': 924}}
2025-08-05 10:28:52,300 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([24, 29, 11, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4],
      dtype=int64), 'cur_cost': 53220.0, 'intermediate_solutions': [{'tour': array([26,  4, 30, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 26,  4, 30,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54224.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 21, 26,  4, 30,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 21, 26,  4,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 53450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  3, 21, 26,  4,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,301 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 53220.00)
2025-08-05 10:28:52,301 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:52,301 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,301 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,301 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 44273.0
2025-08-05 10:28:52,317 - ExploitationExpert - INFO - res_population_num: 20
2025-08-05 10:28:52,318 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761.0]
2025-08-05 10:28:52,318 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64)]
2025-08-05 10:28:52,324 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,324 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11669.0}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11642.0}, {'tour': array([ 1, 19,  2, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27],
      dtype=int64), 'cur_cost': 58672.0}, {'tour': [0, 1, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9653.0}, {'tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 21790.0}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 19297.0}, {'tour': array([24, 29, 11, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4],
      dtype=int64), 'cur_cost': 53220.0}, {'tour': array([ 2,  6, 16, 30, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22],
      dtype=int64), 'cur_cost': 44273.0}]
2025-08-05 10:28:52,325 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:52,325 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 205, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 205, 'cache_hits': 0, 'similarity_calculations': 933, 'cache_hit_rate': 0.0, 'cache_size': 933}}
2025-08-05 10:28:52,326 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 2,  6, 16, 30, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22],
      dtype=int64), 'cur_cost': 44273.0, 'intermediate_solutions': [{'tour': array([20, 19,  2, 29, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 20, 19,  2, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54458.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 29, 20, 19,  2,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 53681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 29, 20, 19, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 23, 29, 20, 19,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,326 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 44273.00)
2025-08-05 10:28:52,326 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:52,327 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:52,328 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11866.0, 'intermediate_solutions': [{'tour': [0, 20, 12, 21, 14, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 24394.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 18, 15], 'cur_cost': 11777.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 12, 21, 20, 22, 23, 24, 25, 30, 26, 29, 27, 28, 31, 32, 8, 4, 3, 2, 1, 6, 5, 9, 7, 13, 17, 16, 11, 10, 19, 15, 18], 'cur_cost': 11807.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11669.0, 'intermediate_solutions': [{'tour': [0, 8, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 5, 31, 9, 30, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11517.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 18, 10, 16, 11, 2, 3, 4, 5, 9, 31, 30, 29, 28, 27, 26, 32, 15, 19, 14, 13, 12, 17, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12379.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 18, 10, 16, 11, 26, 17, 12, 13, 14, 19, 15, 32, 27, 28, 29, 30, 31, 9, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 11884.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11642.0, 'intermediate_solutions': [{'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 30, 31, 9, 5, 20, 3, 2, 1, 6, 7, 22, 4, 21, 25, 24, 23], 'cur_cost': 24345.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 9, 31, 30, 29, 28, 27, 26, 32, 14, 13, 5, 4, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 15072.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 15, 19, 10, 16, 11, 17, 12, 13, 14, 32, 26, 27, 28, 29, 31, 9, 5, 4, 30, 3, 2, 1, 6, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 13342.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 19,  2, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27],
      dtype=int64), 'cur_cost': 58672.0, 'intermediate_solutions': [{'tour': array([21, 32, 16, 17, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 48299.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 21, 32, 16, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50331.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([11, 17, 21, 32, 16, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50340.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([16, 17, 21, 32, 11, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 50337.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([16, 11, 17, 21, 32, 29, 12, 15,  1,  4,  8,  7, 24, 30, 22,  6, 13,
       10, 31,  0,  3, 27, 26, 20,  9, 18, 25,  5, 23,  2, 14, 19, 28]), 'cur_cost': 48130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9653.0, 'intermediate_solutions': [{'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 14, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 28, 26, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 39915.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 9, 7, 14, 26, 10, 15, 30, 3, 22, 13, 0, 23, 16], 'cur_cost': 39758.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 18, 21, 20, 11, 17, 24, 25, 12, 19, 27, 28, 29, 31, 32, 4, 2, 1, 6, 5, 7, 14, 26, 9, 10, 15, 30, 3, 22, 13, 0, 16, 23], 'cur_cost': 40426.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0, 'intermediate_solutions': [{'tour': [14, 18, 15, 20, 17, 24, 25, 30, 0, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 26, 8, 22, 23, 16, 29, 13], 'cur_cost': 41597.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 22, 8, 0, 19, 3, 11, 21, 12, 9, 10, 7, 1, 2, 6, 4, 32, 31, 5, 28, 23, 16, 29, 13], 'cur_cost': 45960.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [14, 18, 15, 20, 17, 24, 25, 30, 26, 27, 28, 5, 31, 32, 4, 6, 2, 1, 7, 10, 9, 12, 21, 11, 3, 19, 29, 0, 8, 22, 23, 16, 13], 'cur_cost': 38384.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 21790.0, 'intermediate_solutions': [{'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 21, 10, 32, 23, 24, 20, 25, 22, 17, 8, 1], 'cur_cost': 26855.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 15, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 25, 20, 24, 23, 32, 10, 17, 11, 22, 21, 8, 1], 'cur_cost': 22697.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 0, 2, 28, 31, 3, 7, 12, 16, 18, 14, 27, 30, 4, 5, 6, 13, 26, 19, 29, 11, 17, 10, 32, 23, 15, 24, 20, 25, 22, 21, 8, 1], 'cur_cost': 24834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 19297.0, 'intermediate_solutions': [{'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 6, 26, 32, 3, 0, 13, 16, 15, 31, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 11, 12], 'cur_cost': 18019.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 9, 2, 4, 5, 24, 22, 30, 10, 14, 18, 19, 31, 15, 16, 13, 0, 3, 32, 26, 11, 27, 17, 28, 29, 8, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 25309.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 9, 2, 4, 5, 8, 29, 28, 17, 27, 11, 26, 31, 32, 3, 0, 13, 16, 15, 19, 18, 14, 10, 30, 22, 24, 23, 20, 25, 21, 7, 6, 12], 'cur_cost': 17023.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 29, 11, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4],
      dtype=int64), 'cur_cost': 53220.0, 'intermediate_solutions': [{'tour': array([26,  4, 30, 21,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 26,  4, 30,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54224.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 21, 26,  4, 30,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54895.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 21, 26,  4,  3,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 53450.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30,  3, 21, 26,  4,  5, 25, 12,  8,  7,  6,  9, 14,  0, 16, 15, 19,
       24, 13, 28,  1, 18, 22, 29, 17,  2, 32, 27, 31, 23, 10, 20, 11]), 'cur_cost': 54213.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2,  6, 16, 30, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22],
      dtype=int64), 'cur_cost': 44273.0, 'intermediate_solutions': [{'tour': array([20, 19,  2, 29, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54403.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 20, 19,  2, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54458.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 29, 20, 19,  2,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 53681.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 29, 20, 19, 23,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 23, 29, 20, 19,  9, 15, 30, 32, 25, 16,  8, 21, 17, 10, 26,  3,
        1, 28,  6,  4,  0, 31, 11, 22, 12, 24,  5,  7, 14, 18, 27, 13]), 'cur_cost': 54375.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:52,329 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:52,329 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:52,332 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9653.000, 多样性=0.937
2025-08-05 10:28:52,332 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:52,332 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:52,332 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:52,336 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.025280731916536247, 'best_improvement': -0.0012446841613940462}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.01625441696113046}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.14789557432798714, 'recent_improvements': [0.1397240390115824, 0.008077348101800353, -0.15606710964439188], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 20, 'new_count': 20, 'count_change': 0, 'old_best_cost': 8761.0, 'new_best_cost': 8761.0, 'quality_improvement': 0.0, 'old_diversity': 0.6585326953748006, 'new_diversity': 0.6585326953748006, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:52,340 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:52,340 - __main__ - INFO - composite4_33 开始进化第 5 代
2025-08-05 10:28:52,340 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:52,341 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:52,342 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9653.000, 多样性=0.937
2025-08-05 10:28:52,343 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:52,345 - PathExpert - INFO - 路径结构分析完成: 公共边数量=5, 路径相似性=0.937
2025-08-05 10:28:52,346 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:52,354 - EliteExpert - INFO - 精英解分析完成: 精英解数量=20, 多样性=0.659
2025-08-05 10:28:52,357 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:52,357 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:52,358 - LandscapeExpert - INFO - 添加精英解数据: 20个精英解
2025-08-05 10:28:52,358 - LandscapeExpert - INFO - 数据提取成功: 30个路径, 30个适应度值
2025-08-05 10:28:52,461 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.667, 适应度梯度: -5191.987, 聚类评分: 0.000, 覆盖率: 0.096, 收敛趋势: 0.000, 多样性: 0.904
2025-08-05 10:28:52,461 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:52,461 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:52,461 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite4_33
2025-08-05 10:28:52,469 - visualization.landscape_visualizer - INFO - 插值约束: 209 个点被约束到最小值 8761.00
2025-08-05 10:28:52,470 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.1%, 梯度: 1567.57 → 1471.55
2025-08-05 10:28:52,578 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\landscape_composite4_33_iter_80_20250805_102852.html
2025-08-05 10:28:52,637 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite4_33\dashboard_composite4_33_iter_80_20250805_102852.html
2025-08-05 10:28:52,637 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 80
2025-08-05 10:28:52,637 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:52,637 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2801秒
2025-08-05 10:28:52,638 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6666666666666666, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -5191.986666666668, 'local_optima_density': 0.6666666666666666, 'gradient_variance': 163900788.46382228, 'cluster_count': 0}, 'population_state': {'diversity': 0.9039080459770115, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0959, 'fitness_entropy': 0.46531343814584825, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5191.987)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.096)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.904)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360932.4614437, 'performance_metrics': {}}}
2025-08-05 10:28:52,638 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:52,638 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:52,638 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:52,639 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:52,639 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,639 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:52,639 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,640 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,640 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:52,640 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-08-05 10:28:52,640 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,640 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:52,640 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:52,641 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:52,641 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:52,641 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,642 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,643 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12385.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,643 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 19, 4, 16, 11, 17, 12, 13, 18, 14, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12385.0, 'intermediate_solutions': [{'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 1, 31, 8, 4, 3, 2, 30, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 13780.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 7, 9, 5, 6, 1, 2, 3, 4, 22, 20, 21, 25, 24, 23], 'cur_cost': 11877.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 18, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 14597.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,643 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 12385.00)
2025-08-05 10:28:52,643 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:52,643 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:52,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,645 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:52,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,646 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24307.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,646 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [26, 2, 28, 8, 32, 30, 4, 3, 9, 0, 1, 11, 19, 12, 16, 29, 7, 14, 17, 31, 13, 10, 27, 22, 20, 24, 23, 5, 6, 18, 15, 21, 25], 'cur_cost': 24307.0, 'intermediate_solutions': [{'tour': [0, 15, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 14, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 21, 20, 22, 18, 15, 19, 10, 11, 16, 17, 13, 29, 30, 31, 32, 27, 26, 25, 24, 23], 'cur_cost': 17214.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 29, 25, 24, 23], 'cur_cost': 17161.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,646 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 24307.00)
2025-08-05 10:28:52,646 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:52,646 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:52,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,647 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9624.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,648 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 17, 19, 12, 16, 11, 10, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9624.0, 'intermediate_solutions': [{'tour': [0, 11, 9, 8, 4, 3, 23, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 2], 'cur_cost': 17677.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 16, 17, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 12, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 14373.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,648 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 9624.00)
2025-08-05 10:28:52,648 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:52,648 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,649 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,649 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 39278.0
2025-08-05 10:28:52,668 - ExploitationExpert - INFO - res_population_num: 22
2025-08-05 10:28:52,669 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761.0, 8761]
2025-08-05 10:28:52,669 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:52,677 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,677 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 4, 16, 11, 17, 12, 13, 18, 14, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12385.0}, {'tour': [26, 2, 28, 8, 32, 30, 4, 3, 9, 0, 1, 11, 19, 12, 16, 29, 7, 14, 17, 31, 13, 10, 27, 22, 20, 24, 23, 5, 6, 18, 15, 21, 25], 'cur_cost': 24307.0}, {'tour': [0, 17, 19, 12, 16, 11, 10, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9624.0}, {'tour': array([26,  2, 29, 16,  8, 25, 24, 22,  5, 19, 14, 32, 28, 11, 20, 23, 17,
        0, 18,  1,  7, 31, 13, 30, 12, 10, 27, 21,  6, 15,  4,  9,  3],
      dtype=int64), 'cur_cost': 39278.0}, {'tour': [0, 1, 18, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9653.0}, {'tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 21790.0}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 19297.0}, {'tour': [24, 29, 11, 22, 10, 13, 16, 12, 7, 15, 0, 30, 25, 3, 8, 26, 21, 17, 32, 5, 18, 9, 1, 27, 6, 19, 28, 20, 31, 23, 14, 2, 4], 'cur_cost': 53220.0}, {'tour': [2, 6, 16, 30, 18, 3, 9, 21, 25, 0, 7, 8, 4, 10, 23, 32, 24, 5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26, 1, 20, 13, 15, 22], 'cur_cost': 44273.0}]
2025-08-05 10:28:52,678 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:52,679 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 206, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 206, 'cache_hits': 0, 'similarity_calculations': 943, 'cache_hit_rate': 0.0, 'cache_size': 943}}
2025-08-05 10:28:52,679 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([26,  2, 29, 16,  8, 25, 24, 22,  5, 19, 14, 32, 28, 11, 20, 23, 17,
        0, 18,  1,  7, 31, 13, 30, 12, 10, 27, 21,  6, 15,  4,  9,  3],
      dtype=int64), 'cur_cost': 39278.0, 'intermediate_solutions': [{'tour': array([ 2, 19,  1, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2, 19,  1, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 60658.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 11,  2, 19,  1, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 11,  2, 19, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 13, 11,  2, 19, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,680 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 39278.00)
2025-08-05 10:28:52,680 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:52,680 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:52,680 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,681 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,682 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,683 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9636.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,683 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 15, 19, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9636.0, 'intermediate_solutions': [{'tour': [0, 1, 21, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 18, 25, 24, 23], 'cur_cost': 22359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 18, 10, 16, 11, 17, 14, 13, 12, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 18, 10, 28, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 11944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,683 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 9636.00)
2025-08-05 10:28:52,683 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:52,683 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:52,683 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,685 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 33
2025-08-05 10:28:52,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,686 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19856.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,687 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [25, 24, 31, 9, 27, 26, 28, 1, 7, 29, 32, 13, 12, 16, 17, 10, 14, 15, 11, 30, 5, 4, 0, 8, 18, 19, 2, 3, 22, 20, 21, 23, 6], 'cur_cost': 19856.0, 'intermediate_solutions': [{'tour': [2, 0, 3, 32, 28, 5, 30, 8, 1, 27, 6, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19408.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 3, 32, 28, 5, 19, 14, 25, 24, 23, 20, 22, 21, 4, 11, 31, 26, 29, 18, 7, 9, 15, 17, 12, 16, 10, 13, 8, 27, 1, 6, 30], 'cur_cost': 19393.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,687 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 19856.00)
2025-08-05 10:28:52,687 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:52,687 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:52,687 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,688 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 33
2025-08-05 10:28:52,688 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,689 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40623.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,690 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 17, 4, 8, 11, 30, 12, 3, 15, 10, 32, 26, 27, 28, 29, 14, 31, 13, 9, 0, 22, 20, 24, 23, 6, 18, 25, 2, 16, 1, 19, 21, 5], 'cur_cost': 40623.0, 'intermediate_solutions': [{'tour': [7, 16, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 5, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 25270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 25, 24, 22, 21, 20, 23, 14, 8, 4, 15, 1, 19], 'cur_cost': 23841.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 25, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 1, 19], 'cur_cost': 27164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,691 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 40623.00)
2025-08-05 10:28:52,691 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:52,691 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:52,691 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:52,692 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 33
2025-08-05 10:28:52,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,692 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:52,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12517.0, 路径长度: 33, 收集中间解: 3
2025-08-05 10:28:52,693 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 18, 6, 24, 20, 21, 22, 23, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 5, 9, 7, 12, 17, 16, 11, 10, 19, 15, 14, 13], 'cur_cost': 12517.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 8, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 19], 'cur_cost': 24075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 11, 17, 30, 0, 27, 15, 16, 13, 28, 4, 32, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 21250.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 15, 27, 16, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 21295.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:52,693 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12517.00)
2025-08-05 10:28:52,693 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:52,693 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,693 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,694 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 49433.0
2025-08-05 10:28:52,719 - ExploitationExpert - INFO - res_population_num: 23
2025-08-05 10:28:52,719 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761.0, 8761, 8761.0]
2025-08-05 10:28:52,719 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:52,725 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,725 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 4, 16, 11, 17, 12, 13, 18, 14, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12385.0}, {'tour': [26, 2, 28, 8, 32, 30, 4, 3, 9, 0, 1, 11, 19, 12, 16, 29, 7, 14, 17, 31, 13, 10, 27, 22, 20, 24, 23, 5, 6, 18, 15, 21, 25], 'cur_cost': 24307.0}, {'tour': [0, 17, 19, 12, 16, 11, 10, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9624.0}, {'tour': array([26,  2, 29, 16,  8, 25, 24, 22,  5, 19, 14, 32, 28, 11, 20, 23, 17,
        0, 18,  1,  7, 31, 13, 30, 12, 10, 27, 21,  6, 15,  4,  9,  3],
      dtype=int64), 'cur_cost': 39278.0}, {'tour': [0, 15, 19, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9636.0}, {'tour': [25, 24, 31, 9, 27, 26, 28, 1, 7, 29, 32, 13, 12, 16, 17, 10, 14, 15, 11, 30, 5, 4, 0, 8, 18, 19, 2, 3, 22, 20, 21, 23, 6], 'cur_cost': 19856.0}, {'tour': [7, 17, 4, 8, 11, 30, 12, 3, 15, 10, 32, 26, 27, 28, 29, 14, 31, 13, 9, 0, 22, 20, 24, 23, 6, 18, 25, 2, 16, 1, 19, 21, 5], 'cur_cost': 40623.0}, {'tour': [0, 18, 6, 24, 20, 21, 22, 23, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 5, 9, 7, 12, 17, 16, 11, 10, 19, 15, 14, 13], 'cur_cost': 12517.0}, {'tour': array([21, 31,  9, 16,  1, 27, 11,  7, 25,  0, 30,  3, 18, 19, 26,  4, 17,
        6, 14,  2, 28, 23, 29, 15, 12, 10, 20, 13, 24, 22,  8,  5, 32],
      dtype=int64), 'cur_cost': 49433.0}, {'tour': [2, 6, 16, 30, 18, 3, 9, 21, 25, 0, 7, 8, 4, 10, 23, 32, 24, 5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26, 1, 20, 13, 15, 22], 'cur_cost': 44273.0}]
2025-08-05 10:28:52,726 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:52,726 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 207, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 207, 'cache_hits': 0, 'similarity_calculations': 954, 'cache_hit_rate': 0.0, 'cache_size': 954}}
2025-08-05 10:28:52,727 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([21, 31,  9, 16,  1, 27, 11,  7, 25,  0, 30,  3, 18, 19, 26,  4, 17,
        6, 14,  2, 28, 23, 29, 15, 12, 10, 20, 13, 24, 22,  8,  5, 32],
      dtype=int64), 'cur_cost': 49433.0, 'intermediate_solutions': [{'tour': array([11, 29, 24, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 47933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 11, 29, 24, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 53207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 22, 11, 29, 24, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 55239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 22, 11, 29, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 48004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 10, 22, 11, 29, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 55308.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,727 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 49433.00)
2025-08-05 10:28:52,727 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:52,727 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:52,727 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:52,728 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 52447.0
2025-08-05 10:28:52,742 - ExploitationExpert - INFO - res_population_num: 23
2025-08-05 10:28:52,742 - ExploitationExpert - INFO - res_population_costs: [8761.0, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761, 8761, 8761, 8761, 8761, 8761.0, 8761.0, 8761, 8761.0]
2025-08-05 10:28:52,742 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-08-05 10:28:52,748 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:52,748 - ExploitationExpert - INFO - populations: [{'tour': [0, 19, 4, 16, 11, 17, 12, 13, 18, 14, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12385.0}, {'tour': [26, 2, 28, 8, 32, 30, 4, 3, 9, 0, 1, 11, 19, 12, 16, 29, 7, 14, 17, 31, 13, 10, 27, 22, 20, 24, 23, 5, 6, 18, 15, 21, 25], 'cur_cost': 24307.0}, {'tour': [0, 17, 19, 12, 16, 11, 10, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9624.0}, {'tour': array([26,  2, 29, 16,  8, 25, 24, 22,  5, 19, 14, 32, 28, 11, 20, 23, 17,
        0, 18,  1,  7, 31, 13, 30, 12, 10, 27, 21,  6, 15,  4,  9,  3],
      dtype=int64), 'cur_cost': 39278.0}, {'tour': [0, 15, 19, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9636.0}, {'tour': [25, 24, 31, 9, 27, 26, 28, 1, 7, 29, 32, 13, 12, 16, 17, 10, 14, 15, 11, 30, 5, 4, 0, 8, 18, 19, 2, 3, 22, 20, 21, 23, 6], 'cur_cost': 19856.0}, {'tour': [7, 17, 4, 8, 11, 30, 12, 3, 15, 10, 32, 26, 27, 28, 29, 14, 31, 13, 9, 0, 22, 20, 24, 23, 6, 18, 25, 2, 16, 1, 19, 21, 5], 'cur_cost': 40623.0}, {'tour': [0, 18, 6, 24, 20, 21, 22, 23, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 5, 9, 7, 12, 17, 16, 11, 10, 19, 15, 14, 13], 'cur_cost': 12517.0}, {'tour': array([21, 31,  9, 16,  1, 27, 11,  7, 25,  0, 30,  3, 18, 19, 26,  4, 17,
        6, 14,  2, 28, 23, 29, 15, 12, 10, 20, 13, 24, 22,  8,  5, 32],
      dtype=int64), 'cur_cost': 49433.0}, {'tour': array([13, 25, 15,  9,  6, 32, 17, 21, 14, 24, 10, 31,  1, 26,  8, 28, 11,
       16, 12, 29, 30, 22, 27,  2, 23,  4,  5,  7, 19,  3,  0, 20, 18],
      dtype=int64), 'cur_cost': 52447.0}]
2025-08-05 10:28:52,749 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:52,749 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 208, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 208, 'cache_hits': 0, 'similarity_calculations': 966, 'cache_hit_rate': 0.0, 'cache_size': 966}}
2025-08-05 10:28:52,750 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([13, 25, 15,  9,  6, 32, 17, 21, 14, 24, 10, 31,  1, 26,  8, 28, 11,
       16, 12, 29, 30, 22, 27,  2, 23,  4,  5,  7, 19,  3,  0, 20, 18],
      dtype=int64), 'cur_cost': 52447.0, 'intermediate_solutions': [{'tour': array([16,  6,  2, 30, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 16,  6,  2, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30, 16,  6,  2,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 43568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 30, 16,  6, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 18, 30, 16,  6,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44278.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:52,750 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 52447.00)
2025-08-05 10:28:52,750 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:52,750 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:52,752 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 4, 16, 11, 17, 12, 13, 18, 14, 15, 10, 32, 26, 27, 28, 29, 30, 31, 8, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 12385.0, 'intermediate_solutions': [{'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 1, 31, 8, 4, 3, 2, 30, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 13780.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 18, 14, 13, 32, 27, 28, 29, 30, 31, 8, 7, 9, 5, 6, 1, 2, 3, 4, 22, 20, 21, 25, 24, 23], 'cur_cost': 11877.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 10, 26, 12, 17, 16, 11, 19, 15, 14, 13, 32, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 18, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 14597.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [26, 2, 28, 8, 32, 30, 4, 3, 9, 0, 1, 11, 19, 12, 16, 29, 7, 14, 17, 31, 13, 10, 27, 22, 20, 24, 23, 5, 6, 18, 15, 21, 25], 'cur_cost': 24307.0, 'intermediate_solutions': [{'tour': [0, 15, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 29, 13, 17, 16, 11, 10, 19, 14, 18, 22, 20, 21, 25, 24, 23], 'cur_cost': 11678.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 21, 20, 22, 18, 15, 19, 10, 11, 16, 17, 13, 29, 30, 31, 32, 27, 26, 25, 24, 23], 'cur_cost': 17214.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 12, 7, 2, 3, 4, 5, 9, 1, 6, 8, 28, 26, 27, 32, 31, 30, 13, 17, 16, 11, 10, 19, 15, 18, 22, 20, 21, 29, 25, 24, 23], 'cur_cost': 17161.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 19, 12, 16, 11, 10, 15, 18, 14, 13, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9624.0, 'intermediate_solutions': [{'tour': [0, 11, 9, 8, 4, 3, 23, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 2], 'cur_cost': 17677.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 12, 16, 17, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 11652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 9, 8, 4, 3, 2, 1, 12, 6, 5, 7, 28, 26, 27, 32, 31, 30, 29, 17, 16, 10, 19, 15, 18, 14, 13, 22, 20, 21, 25, 24, 23], 'cur_cost': 14373.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([26,  2, 29, 16,  8, 25, 24, 22,  5, 19, 14, 32, 28, 11, 20, 23, 17,
        0, 18,  1,  7, 31, 13, 30, 12, 10, 27, 21,  6, 15,  4,  9,  3],
      dtype=int64), 'cur_cost': 39278.0, 'intermediate_solutions': [{'tour': array([ 2, 19,  1, 11, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58667.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([11,  2, 19,  1, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 60658.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 11,  2, 19,  1, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58686.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 1, 11,  2, 19, 13, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 1, 13, 11,  2, 19, 31,  8,  9, 17,  0, 32,  6, 14, 23, 26,  3, 25,
       10, 21, 15,  5, 20,  7, 22, 12, 24, 29, 18,  4, 28, 30, 16, 27]), 'cur_cost': 58674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 19, 18, 14, 13, 17, 12, 16, 11, 10, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 1, 6, 5, 9, 7, 22, 20, 21, 25, 24, 23], 'cur_cost': 9636.0, 'intermediate_solutions': [{'tour': [0, 1, 21, 10, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 18, 25, 24, 23], 'cur_cost': 22359.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 18, 10, 16, 11, 17, 14, 13, 12, 19, 15, 32, 26, 27, 28, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 9667.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 18, 10, 28, 16, 11, 17, 12, 13, 14, 19, 15, 32, 26, 27, 29, 30, 31, 8, 4, 3, 2, 7, 5, 9, 6, 22, 20, 21, 25, 24, 23], 'cur_cost': 11944.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [25, 24, 31, 9, 27, 26, 28, 1, 7, 29, 32, 13, 12, 16, 17, 10, 14, 15, 11, 30, 5, 4, 0, 8, 18, 19, 2, 3, 22, 20, 21, 23, 6], 'cur_cost': 19856.0, 'intermediate_solutions': [{'tour': [2, 0, 3, 32, 28, 5, 30, 8, 1, 27, 6, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19408.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 0, 3, 32, 28, 5, 19, 14, 25, 24, 23, 20, 22, 21, 4, 11, 31, 26, 29, 18, 7, 9, 15, 17, 12, 16, 10, 13, 8, 27, 1, 6, 30], 'cur_cost': 19393.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 0, 3, 32, 28, 5, 30, 6, 1, 27, 8, 13, 10, 16, 12, 17, 15, 9, 7, 18, 29, 26, 31, 11, 4, 21, 22, 20, 23, 24, 25, 14, 19], 'cur_cost': 19397.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 17, 4, 8, 11, 30, 12, 3, 15, 10, 32, 26, 27, 28, 29, 14, 31, 13, 9, 0, 22, 20, 24, 23, 6, 18, 25, 2, 16, 1, 19, 21, 5], 'cur_cost': 40623.0, 'intermediate_solutions': [{'tour': [7, 16, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 5, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 25, 1, 19], 'cur_cost': 25270.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 13, 27, 17, 16, 31, 18, 10, 12, 11, 25, 24, 22, 21, 20, 23, 14, 8, 4, 15, 1, 19], 'cur_cost': 23841.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 5, 2, 30, 6, 29, 28, 0, 9, 32, 3, 26, 25, 13, 27, 17, 16, 31, 18, 10, 12, 11, 15, 4, 8, 14, 23, 20, 21, 22, 24, 1, 19], 'cur_cost': 27164.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 6, 24, 20, 21, 22, 23, 25, 30, 26, 27, 28, 29, 31, 32, 8, 4, 3, 2, 1, 5, 9, 7, 12, 17, 16, 11, 10, 19, 15, 14, 13], 'cur_cost': 12517.0, 'intermediate_solutions': [{'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 16, 15, 27, 0, 30, 17, 11, 8, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 19], 'cur_cost': 24075.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 11, 17, 30, 0, 27, 15, 16, 13, 28, 4, 32, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 21250.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 5, 3, 9, 29, 12, 14, 31, 7, 6, 32, 4, 28, 13, 15, 27, 16, 0, 30, 17, 11, 19, 18, 26, 1, 10, 22, 20, 21, 25, 24, 23, 8], 'cur_cost': 21295.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 31,  9, 16,  1, 27, 11,  7, 25,  0, 30,  3, 18, 19, 26,  4, 17,
        6, 14,  2, 28, 23, 29, 15, 12, 10, 20, 13, 24, 22,  8,  5, 32],
      dtype=int64), 'cur_cost': 49433.0, 'intermediate_solutions': [{'tour': array([11, 29, 24, 22, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 47933.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([22, 11, 29, 24, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 53207.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10, 22, 11, 29, 24, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 55239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([24, 22, 11, 29, 10, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 48004.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([24, 10, 22, 11, 29, 13, 16, 12,  7, 15,  0, 30, 25,  3,  8, 26, 21,
       17, 32,  5, 18,  9,  1, 27,  6, 19, 28, 20, 31, 23, 14,  2,  4]), 'cur_cost': 55308.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 25, 15,  9,  6, 32, 17, 21, 14, 24, 10, 31,  1, 26,  8, 28, 11,
       16, 12, 29, 30, 22, 27,  2, 23,  4,  5,  7, 19,  3,  0, 20, 18],
      dtype=int64), 'cur_cost': 52447.0, 'intermediate_solutions': [{'tour': array([16,  6,  2, 30, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44271.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30, 16,  6,  2, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44193.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30, 16,  6,  2,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 43568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 30, 16,  6, 18,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44961.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 18, 30, 16,  6,  3,  9, 21, 25,  0,  7,  8,  4, 10, 23, 32, 24,
        5, 27, 11, 14, 29, 31, 28, 19, 12, 17, 26,  1, 20, 13, 15, 22]), 'cur_cost': 44278.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:52,752 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:52,753 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:52,755 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9624.000, 多样性=0.938
2025-08-05 10:28:52,755 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:52,756 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:52,756 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:52,759 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.02536982243921987, 'best_improvement': 0.003004247384232881}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0007183908045973899}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008601691907367953, 'recent_improvements': [0.008077348101800353, -0.15606710964439188, 0.025280731916536247], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 23, 'new_count': 23, 'count_change': 0, 'old_best_cost': 8761.0, 'new_best_cost': 8761.0, 'quality_improvement': 0.0, 'old_diversity': 0.6560067073901067, 'new_diversity': 0.6560067073901067, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:52,763 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:52,772 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite4_33_solution.json
2025-08-05 10:28:52,772 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite4_33_20250805_102852.solution
2025-08-05 10:28:52,772 - __main__ - INFO - 实例执行完成 - 运行时间: 1.77s, 最佳成本: 8761.0
2025-08-05 10:28:52,773 - __main__ - INFO - 实例 composite4_33 处理完成
