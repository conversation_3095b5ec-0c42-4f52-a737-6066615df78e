2025-08-05 10:29:07,028 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-05 10:29:07,028 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:29:07,029 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,033 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9976.000, 多样性=0.976
2025-08-05 10:29:07,035 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:07,039 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-05 10:29:07,041 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:07,043 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:29:07,043 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:07,043 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:29:07,043 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:29:07,074 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -3475.100, 聚类评分: 0.000, 覆盖率: 0.139, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:07,074 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:29:07,074 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:29:07,074 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 10:29:07,083 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.5%, 梯度: 3666.08 → 3354.03
2025-08-05 10:29:07,205 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_121_20250805_102907.html
2025-08-05 10:29:07,292 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_121_20250805_102907.html
2025-08-05 10:29:07,292 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 121
2025-08-05 10:29:07,292 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:29:07,292 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2488秒
2025-08-05 10:29:07,293 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 242, 'max_size': 500, 'hits': 0, 'misses': 242, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 808, 'misses': 420, 'hit_rate': 0.6579804560260586, 'evictions': 320, 'ttl': 7200}}
2025-08-05 10:29:07,293 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3475.100000000003, 'local_optima_density': 0.1, 'gradient_variance': 3103201374.8900003, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1391, 'fitness_entropy': 0.947730922119161, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3475.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.139)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360947.0745375, 'performance_metrics': {}}}
2025-08-05 10:29:07,294 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:07,294 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:07,294 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:07,294 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:07,296 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,296 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:29:07,296 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,296 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,296 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:07,296 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,297 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,297 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:07,297 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:07,297 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:29:07,297 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:29:07,297 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,301 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:07,301 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,301 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116297.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,301 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [45, 50, 59, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43, 8, 17, 47, 4, 38, 11, 30, 19, 23, 52, 26, 5, 6, 18, 53, 21, 55, 61, 22, 10, 7, 57, 34, 40, 33, 56, 58, 31, 16, 0, 44, 12, 46, 29, 39, 14, 62, 9, 20, 2, 54, 24, 60, 65, 25, 37, 3, 41, 27, 36, 1], 'cur_cost': 116297.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,302 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 116297.00)
2025-08-05 10:29:07,302 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:07,302 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:07,302 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,304 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,304 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,305 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12327.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,305 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12327.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,305 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 12327.00)
2025-08-05 10:29:07,306 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:07,306 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:07,306 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,308 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,308 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12756.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,309 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12756.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,309 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 12756.00)
2025-08-05 10:29:07,309 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:07,309 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:07,309 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,311 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:07,311 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,312 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107426.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,312 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 107426.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,312 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 107426.00)
2025-08-05 10:29:07,312 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:29:07,312 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,312 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,313 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 111871.0
2025-08-05 10:29:07,320 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:07,321 - ExploitationExpert - INFO - res_population_costs: [9539.0, 9538, 9527]
2025-08-05 10:29:07,321 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-05 10:29:07,322 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,322 - ExploitationExpert - INFO - populations: [{'tour': [45, 50, 59, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43, 8, 17, 47, 4, 38, 11, 30, 19, 23, 52, 26, 5, 6, 18, 53, 21, 55, 61, 22, 10, 7, 57, 34, 40, 33, 56, 58, 31, 16, 0, 44, 12, 46, 29, 39, 14, 62, 9, 20, 2, 54, 24, 60, 65, 25, 37, 3, 41, 27, 36, 1], 'cur_cost': 116297.0}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12327.0}, {'tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12756.0}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 107426.0}, {'tour': array([11, 35, 50, 21, 41, 29, 28,  1, 45, 37, 34,  8,  3, 47, 32,  5, 20,
        0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56,  9, 27, 15, 26, 23, 33,
       51,  4, 30, 57, 17, 53,  2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13,
       52, 59, 62, 10, 31, 55, 46,  6, 63,  7, 39, 61, 24, 40, 58],
      dtype=int64), 'cur_cost': 111871.0}, {'tour': array([ 5, 22, 33, 48, 28, 63, 51, 49, 11,  2, 21,  4, 10, 34, 46, 20, 27,
       36, 57, 54, 40, 39, 64,  9, 18, 50, 30, 43, 55, 37, 31, 65,  1, 62,
       14, 15, 59,  6, 12, 42, 13, 47, 61, 53, 35, 16,  0, 38, 29, 44, 25,
       56,  3,  7, 41, 45, 58, 32, 52, 60, 17, 24, 19, 23, 26,  8],
      dtype=int64), 'cur_cost': 102300.0}, {'tour': array([49, 12, 31, 54, 57, 53,  0, 14, 10,  9, 29, 13, 39, 47, 51, 42,  2,
       18, 22, 60, 58, 11, 35,  4, 63, 44, 25, 19, 15, 65, 59, 61, 28, 21,
       36, 41, 46, 56, 48, 27,  6, 16, 34, 23,  1, 45, 43,  7, 32, 40, 24,
       26, 50, 33, 20, 62,  5, 64, 52, 37, 38, 30, 17,  3,  8, 55],
      dtype=int64), 'cur_cost': 90613.0}, {'tour': array([31, 54,  1,  0, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121283.0}, {'tour': array([63, 28,  9, 19,  5, 52, 16, 23, 39, 54,  7, 55, 25, 42, 50, 34, 45,
        1, 27, 40, 21, 47, 24,  3, 17, 32, 11, 41, 14, 36,  2, 38, 49, 33,
       26, 56, 29, 65, 51, 30,  4, 46, 43, 57, 18, 48, 12, 13, 61, 37, 31,
       58, 20, 22, 64, 10, 62, 59,  6, 35, 60, 53,  0,  8, 44, 15],
      dtype=int64), 'cur_cost': 108487.0}, {'tour': array([40, 51, 25, 35, 65, 20, 14, 39, 11, 15, 33, 50, 16,  7, 42, 41, 34,
       37, 45, 49, 52, 13, 58, 38,  5, 31,  4,  0, 22, 48,  3, 46, 18, 57,
       23, 21, 32, 29, 27, 53,  8,  2, 12, 24, 63, 62, 43, 61,  9, 28, 19,
        6, 26, 47, 36, 60, 59, 55, 30, 64, 17, 44, 56, 10, 54,  1],
      dtype=int64), 'cur_cost': 103344.0}]
2025-08-05 10:29:07,325 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:07,325 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 313, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 313, 'cache_hits': 0, 'similarity_calculations': 1590, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:07,326 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([11, 35, 50, 21, 41, 29, 28,  1, 45, 37, 34,  8,  3, 47, 32,  5, 20,
        0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56,  9, 27, 15, 26, 23, 33,
       51,  4, 30, 57, 17, 53,  2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13,
       52, 59, 62, 10, 31, 55, 46,  6, 63,  7, 39, 61, 24, 40, 58],
      dtype=int64), 'cur_cost': 111871.0, 'intermediate_solutions': [{'tour': array([11, 12, 53, 20,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 11, 12, 53,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116579.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 20, 11, 12, 53,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 20, 11, 12,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 114854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53,  0, 20, 11, 12,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 115157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:07,326 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 111871.00)
2025-08-05 10:29:07,326 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:07,326 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:07,326 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,328 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:07,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102062.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,328 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102062.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,329 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 102062.00)
2025-08-05 10:29:07,329 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:07,329 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:07,329 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,331 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,331 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10401.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,331 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10401.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,332 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10401.00)
2025-08-05 10:29:07,332 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:07,332 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,332 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,332 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 119147.0
2025-08-05 10:29:07,342 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:07,342 - ExploitationExpert - INFO - res_population_costs: [9539.0, 9538, 9527]
2025-08-05 10:29:07,342 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-05 10:29:07,343 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,343 - ExploitationExpert - INFO - populations: [{'tour': [45, 50, 59, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43, 8, 17, 47, 4, 38, 11, 30, 19, 23, 52, 26, 5, 6, 18, 53, 21, 55, 61, 22, 10, 7, 57, 34, 40, 33, 56, 58, 31, 16, 0, 44, 12, 46, 29, 39, 14, 62, 9, 20, 2, 54, 24, 60, 65, 25, 37, 3, 41, 27, 36, 1], 'cur_cost': 116297.0}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12327.0}, {'tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12756.0}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 107426.0}, {'tour': array([11, 35, 50, 21, 41, 29, 28,  1, 45, 37, 34,  8,  3, 47, 32,  5, 20,
        0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56,  9, 27, 15, 26, 23, 33,
       51,  4, 30, 57, 17, 53,  2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13,
       52, 59, 62, 10, 31, 55, 46,  6, 63,  7, 39, 61, 24, 40, 58],
      dtype=int64), 'cur_cost': 111871.0}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102062.0}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10401.0}, {'tour': array([44, 23, 57, 29, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22],
      dtype=int64), 'cur_cost': 119147.0}, {'tour': array([63, 28,  9, 19,  5, 52, 16, 23, 39, 54,  7, 55, 25, 42, 50, 34, 45,
        1, 27, 40, 21, 47, 24,  3, 17, 32, 11, 41, 14, 36,  2, 38, 49, 33,
       26, 56, 29, 65, 51, 30,  4, 46, 43, 57, 18, 48, 12, 13, 61, 37, 31,
       58, 20, 22, 64, 10, 62, 59,  6, 35, 60, 53,  0,  8, 44, 15],
      dtype=int64), 'cur_cost': 108487.0}, {'tour': array([40, 51, 25, 35, 65, 20, 14, 39, 11, 15, 33, 50, 16,  7, 42, 41, 34,
       37, 45, 49, 52, 13, 58, 38,  5, 31,  4,  0, 22, 48,  3, 46, 18, 57,
       23, 21, 32, 29, 27, 53,  8,  2, 12, 24, 63, 62, 43, 61,  9, 28, 19,
        6, 26, 47, 36, 60, 59, 55, 30, 64, 17, 44, 56, 10, 54,  1],
      dtype=int64), 'cur_cost': 103344.0}]
2025-08-05 10:29:07,346 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:07,346 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 314, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 314, 'cache_hits': 0, 'similarity_calculations': 1591, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:07,347 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([44, 23, 57, 29, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22],
      dtype=int64), 'cur_cost': 119147.0, 'intermediate_solutions': [{'tour': array([ 1, 54, 31,  0, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121379.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  1, 54, 31, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 117420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32,  0,  1, 54, 31, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  0,  1, 54, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 32,  0,  1, 54, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 115419.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:07,347 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 119147.00)
2025-08-05 10:29:07,347 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:07,347 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:07,347 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,350 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,350 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,350 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12882.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,350 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12882.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,350 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12882.00)
2025-08-05 10:29:07,351 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:07,351 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:07,351 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,354 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,354 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12859.0, 路径长度: 66, 收集中间解: 0
2025-08-05 10:29:07,355 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12859.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,355 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12859.00)
2025-08-05 10:29:07,355 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:07,355 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:07,357 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [45, 50, 59, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43, 8, 17, 47, 4, 38, 11, 30, 19, 23, 52, 26, 5, 6, 18, 53, 21, 55, 61, 22, 10, 7, 57, 34, 40, 33, 56, 58, 31, 16, 0, 44, 12, 46, 29, 39, 14, 62, 9, 20, 2, 54, 24, 60, 65, 25, 37, 3, 41, 27, 36, 1], 'cur_cost': 116297.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12327.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12756.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 107426.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 35, 50, 21, 41, 29, 28,  1, 45, 37, 34,  8,  3, 47, 32,  5, 20,
        0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56,  9, 27, 15, 26, 23, 33,
       51,  4, 30, 57, 17, 53,  2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13,
       52, 59, 62, 10, 31, 55, 46,  6, 63,  7, 39, 61, 24, 40, 58],
      dtype=int64), 'cur_cost': 111871.0, 'intermediate_solutions': [{'tour': array([11, 12, 53, 20,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116605.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([20, 11, 12, 53,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116579.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 0, 20, 11, 12, 53,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 116944.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([53, 20, 11, 12,  0,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 114854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([53,  0, 20, 11, 12,  3, 46, 60, 38,  4, 30, 39, 56, 32, 44, 28, 16,
       25, 19, 10, 49, 55, 13, 41, 59, 21, 58, 54, 23, 42, 45, 63,  2, 29,
       17,  1,  5, 27, 51,  9, 24,  6, 57, 47, 43, 22, 37, 61, 65, 26, 64,
       62, 36, 15, 34, 50, 35,  8, 40, 33, 48,  7, 18, 31, 14, 52],
      dtype=int64), 'cur_cost': 115157.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102062.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10401.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 23, 57, 29, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22],
      dtype=int64), 'cur_cost': 119147.0, 'intermediate_solutions': [{'tour': array([ 1, 54, 31,  0, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121379.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 0,  1, 54, 31, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 117420.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([32,  0,  1, 54, 31, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([31,  0,  1, 54, 32, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 121284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([31, 32,  0,  1, 54, 62, 19, 37, 55, 21, 41, 64, 22, 20, 48, 15, 36,
        6, 13, 23, 46, 51,  3,  9, 61, 35, 11,  2, 40, 34, 14,  5, 26,  4,
       50, 29, 56, 18, 59, 42, 10, 52, 45, 30, 43, 39, 24, 63, 58, 33, 47,
       12, 65, 25, 53, 16, 38, 27, 49,  8, 44,  7, 60, 17, 28, 57],
      dtype=int64), 'cur_cost': 115419.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12882.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12859.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:07,357 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:07,357 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,361 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10401.000, 多样性=0.912
2025-08-05 10:29:07,361 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:29:07,361 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:29:07,362 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:07,362 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.06188677772071325, 'best_improvement': -0.04260224538893344}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.06553984132459462}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.1465266377611939, 'recent_improvements': [-0.25692158872089244, 0.1476632756608843, 0.036131686801495415], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 3, 'new_count': 3, 'count_change': 0, 'old_best_cost': 9527, 'new_best_cost': 9527, 'quality_improvement': 0.0, 'old_diversity': 0.6818181818181818, 'new_diversity': 0.6818181818181818, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:29:07,362 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:29:07,362 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-08-05 10:29:07,362 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:29:07,363 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,363 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10401.000, 多样性=0.912
2025-08-05 10:29:07,364 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:07,367 - PathExpert - INFO - 路径结构分析完成: 公共边数量=15, 路径相似性=0.912
2025-08-05 10:29:07,367 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:07,369 - EliteExpert - INFO - 精英解分析完成: 精英解数量=3, 多样性=0.682
2025-08-05 10:29:07,371 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:29:07,371 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:07,371 - LandscapeExpert - INFO - 添加精英解数据: 3个精英解
2025-08-05 10:29:07,371 - LandscapeExpert - INFO - 数据提取成功: 13个路径, 13个适应度值
2025-08-05 10:29:07,397 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.154, 适应度梯度: -4274.877, 聚类评分: 0.000, 覆盖率: 0.140, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:07,398 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:29:07,398 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:07,398 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 10:29:07,403 - visualization.landscape_visualizer - INFO - 插值约束: 187 个点被约束到最小值 9527.00
2025-08-05 10:29:07,404 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.8%, 梯度: 4332.51 → 4082.88
2025-08-05 10:29:07,511 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_122_20250805_102907.html
2025-08-05 10:29:07,590 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_122_20250805_102907.html
2025-08-05 10:29:07,591 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 122
2025-08-05 10:29:07,591 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:29:07,591 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2197秒
2025-08-05 10:29:07,591 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15384615384615385, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4274.876923076922, 'local_optima_density': 0.15384615384615385, 'gradient_variance': 1197937551.621775, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1403, 'fitness_entropy': 0.8420887815606471, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4274.877)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.140)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360947.3988135, 'performance_metrics': {}}}
2025-08-05 10:29:07,592 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:07,592 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:07,592 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:07,592 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:07,593 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,593 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:29:07,593 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,593 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,593 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:07,593 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,594 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,594 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:07,594 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:07,594 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:07,594 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,595 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,595 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 106680.0
2025-08-05 10:29:07,610 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:29:07,610 - ExploitationExpert - INFO - res_population_costs: [9527, 9538, 9539.0]
2025-08-05 10:29:07,610 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:07,613 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,613 - ExploitationExpert - INFO - populations: [{'tour': array([61,  0, 49, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13],
      dtype=int64), 'cur_cost': 106680.0}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12327.0}, {'tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12756.0}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 107426.0}, {'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 20, 0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13, 52, 59, 62, 10, 31, 55, 46, 6, 63, 7, 39, 61, 24, 40, 58], 'cur_cost': 111871.0}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102062.0}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10401.0}, {'tour': [44, 23, 57, 29, 49, 0, 12, 56, 34, 31, 38, 53, 19, 63, 5, 45, 9, 60, 59, 25, 51, 50, 4, 6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42, 13, 40, 7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36, 8, 35, 14, 52, 26, 16, 54, 32, 46, 37, 27, 3, 28, 11, 2, 1, 30, 55, 22], 'cur_cost': 119147.0}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12882.0}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12859.0}]
2025-08-05 10:29:07,614 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:07,614 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 315, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 315, 'cache_hits': 0, 'similarity_calculations': 1593, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:07,616 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([61,  0, 49, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13],
      dtype=int64), 'cur_cost': 106680.0, 'intermediate_solutions': [{'tour': array([59, 50, 45, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 112025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 59, 50, 45, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 116316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 42, 59, 50, 45, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 115923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([45, 42, 59, 50, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 116351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([45, 13, 42, 59, 50, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 118569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:07,616 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 106680.00)
2025-08-05 10:29:07,616 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:07,616 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:07,616 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,620 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,621 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,622 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,622 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12370.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,622 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12370.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 51, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 20, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 4, 5, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12991.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12425.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,623 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 12370.00)
2025-08-05 10:29:07,623 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:07,623 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:07,623 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,631 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:07,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58461.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,633 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 58461.0, 'intermediate_solutions': [{'tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 42, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 23], 'cur_cost': 15568.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 2, 4, 5, 49, 40, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 1, 3, 7, 11, 9, 6, 8, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 17665.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 2, 4, 5, 8, 6, 9, 34, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16671.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,633 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 58461.00)
2025-08-05 10:29:07,633 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:07,633 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:07,634 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,637 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,638 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,638 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,638 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12874.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,638 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12874.0, 'intermediate_solutions': [{'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 5, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 60, 62, 32, 47, 20, 23, 41], 'cur_cost': 108142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 50, 65, 60, 38, 49, 63, 29, 39, 54, 46, 64, 44, 0, 31, 58, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 109349.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 12, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 105175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,638 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12874.00)
2025-08-05 10:29:07,638 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:07,639 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:07,639 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,641 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14823.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,643 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14823.0, 'intermediate_solutions': [{'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 49, 0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 20, 36, 60, 22, 16, 14, 65, 25, 13, 52, 59, 62, 10, 31, 55, 46, 6, 63, 7, 39, 61, 24, 40, 58], 'cur_cost': 114730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 20, 0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 49, 36, 60, 22, 16, 39, 7, 63, 6, 46, 55, 31, 10, 62, 59, 52, 13, 25, 65, 14, 61, 24, 40, 58], 'cur_cost': 112287.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 20, 0, 42, 18, 43, 12, 48, 64, 54, 6, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13, 52, 59, 62, 10, 31, 55, 46, 63, 7, 39, 61, 24, 40, 58], 'cur_cost': 111565.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,643 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 14823.00)
2025-08-05 10:29:07,643 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:07,643 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:07,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,646 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:07,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89389.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,648 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [62, 20, 27, 16, 23, 15, 2, 18, 8, 4, 39, 49, 48, 5, 45, 1, 50, 0, 11, 60, 36, 55, 40, 59, 3, 25, 14, 65, 28, 30, 57, 56, 61, 54, 22, 43, 38, 64, 63, 51, 35, 26, 7, 13, 53, 52, 6, 29, 24, 32, 44, 17, 21, 10, 19, 46, 58, 9, 34, 31, 33, 37, 42, 41, 12, 47], 'cur_cost': 89389.0, 'intermediate_solutions': [{'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 40, 31, 57, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 104439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 8, 45, 5, 65, 43, 48, 38, 2, 12, 32, 14, 39, 54, 46, 49, 63, 0, 16, 36, 56, 33, 40, 31, 57, 10, 61, 55, 17, 53, 1, 29, 24, 18, 52, 34, 30, 28, 60, 58, 26, 25, 37, 27, 19, 64, 51, 20, 3, 7, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 15, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11], 'cur_cost': 102043.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,648 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 89389.00)
2025-08-05 10:29:07,649 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:07,649 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:07,649 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,655 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:07,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,656 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,657 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66221.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,657 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 66221.0, 'intermediate_solutions': [{'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 21, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 58, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 35, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14549.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,658 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 66221.00)
2025-08-05 10:29:07,658 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:07,658 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,658 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,658 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101711.0
2025-08-05 10:29:07,669 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:07,669 - ExploitationExpert - INFO - res_population_costs: [9527, 9538, 9539.0, 9527.0, 9524]
2025-08-05 10:29:07,669 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-05 10:29:07,671 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,671 - ExploitationExpert - INFO - populations: [{'tour': array([61,  0, 49, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13],
      dtype=int64), 'cur_cost': 106680.0}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12370.0}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 58461.0}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12874.0}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14823.0}, {'tour': [62, 20, 27, 16, 23, 15, 2, 18, 8, 4, 39, 49, 48, 5, 45, 1, 50, 0, 11, 60, 36, 55, 40, 59, 3, 25, 14, 65, 28, 30, 57, 56, 61, 54, 22, 43, 38, 64, 63, 51, 35, 26, 7, 13, 53, 52, 6, 29, 24, 32, 44, 17, 21, 10, 19, 46, 58, 9, 34, 31, 33, 37, 42, 41, 12, 47], 'cur_cost': 89389.0}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 66221.0}, {'tour': array([57, 15, 23, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61],
      dtype=int64), 'cur_cost': 101711.0}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12882.0}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12859.0}]
2025-08-05 10:29:07,672 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:07,673 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 316, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 316, 'cache_hits': 0, 'similarity_calculations': 1596, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:07,674 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([57, 15, 23, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61],
      dtype=int64), 'cur_cost': 101711.0, 'intermediate_solutions': [{'tour': array([57, 23, 44, 29, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 118871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 57, 23, 44, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 116892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 29, 57, 23, 44,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 119135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44, 29, 57, 23, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 119159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 49, 29, 57, 23,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 116557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:07,674 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 101711.00)
2025-08-05 10:29:07,674 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:07,674 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:07,674 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,680 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:07,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,681 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,681 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62480.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,681 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62480.0, 'intermediate_solutions': [{'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 59, 55, 61, 53, 62, 10, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 51, 38, 45, 44, 39, 43, 48, 46, 50, 41, 42], 'cur_cost': 12963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 28, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,682 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 62480.00)
2025-08-05 10:29:07,682 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:07,682 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:07,682 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,684 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,685 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12913.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,685 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0, 'intermediate_solutions': [{'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 40, 34, 33, 31, 24, 29, 32, 30, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16941.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 36, 26, 25, 37, 27, 21, 20, 13, 23, 16, 18, 17, 12, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 18, 50, 41, 42], 'cur_cost': 15737.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,685 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12913.00)
2025-08-05 10:29:07,685 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:07,685 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:07,687 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([61,  0, 49, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13],
      dtype=int64), 'cur_cost': 106680.0, 'intermediate_solutions': [{'tour': array([59, 50, 45, 42, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 112025.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([42, 59, 50, 45, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 116316.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 42, 59, 50, 45, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 115923.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([45, 42, 59, 50, 13, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 116351.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([45, 13, 42, 59, 50, 63, 28, 49, 35, 48, 15, 32, 51, 64, 43,  8, 17,
       47,  4, 38, 11, 30, 19, 23, 52, 26,  5,  6, 18, 53, 21, 55, 61, 22,
       10,  7, 57, 34, 40, 33, 56, 58, 31, 16,  0, 44, 12, 46, 29, 39, 14,
       62,  9, 20,  2, 54, 24, 60, 65, 25, 37,  3, 41, 27, 36,  1]), 'cur_cost': 118569.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12370.0, 'intermediate_solutions': [{'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 51, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 20, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17828.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 2, 4, 5, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12991.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 8, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12425.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 58461.0, 'intermediate_solutions': [{'tour': [0, 14, 2, 4, 5, 8, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 42, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 23], 'cur_cost': 15568.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 2, 4, 5, 49, 40, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10, 1, 3, 7, 11, 9, 6, 8, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 17665.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 2, 4, 5, 8, 6, 9, 34, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16671.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12874.0, 'intermediate_solutions': [{'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 5, 65, 50, 43, 12, 42, 55, 37, 56, 61, 3, 60, 62, 32, 47, 20, 23, 41], 'cur_cost': 108142.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 50, 65, 60, 38, 49, 63, 29, 39, 54, 46, 64, 44, 0, 31, 58, 43, 12, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 109349.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [45, 6, 2, 16, 8, 28, 9, 35, 12, 48, 14, 51, 13, 19, 27, 17, 59, 4, 36, 30, 57, 34, 52, 26, 24, 15, 18, 53, 21, 1, 11, 22, 10, 7, 40, 33, 25, 58, 31, 0, 44, 64, 46, 54, 39, 29, 63, 49, 38, 60, 65, 50, 43, 42, 55, 37, 56, 61, 3, 5, 62, 32, 47, 20, 23, 41], 'cur_cost': 105175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14823.0, 'intermediate_solutions': [{'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 49, 0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 20, 36, 60, 22, 16, 14, 65, 25, 13, 52, 59, 62, 10, 31, 55, 46, 6, 63, 7, 39, 61, 24, 40, 58], 'cur_cost': 114730.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 20, 0, 42, 18, 43, 12, 48, 64, 54, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 49, 36, 60, 22, 16, 39, 7, 63, 6, 46, 55, 31, 10, 62, 59, 52, 13, 25, 65, 14, 61, 24, 40, 58], 'cur_cost': 112287.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [11, 35, 50, 21, 41, 29, 28, 1, 45, 37, 34, 8, 3, 47, 32, 5, 20, 0, 42, 18, 43, 12, 48, 64, 54, 6, 19, 38, 56, 9, 27, 15, 26, 23, 33, 51, 4, 30, 57, 17, 53, 2, 44, 49, 36, 60, 22, 16, 14, 65, 25, 13, 52, 59, 62, 10, 31, 55, 46, 63, 7, 39, 61, 24, 40, 58], 'cur_cost': 111565.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [62, 20, 27, 16, 23, 15, 2, 18, 8, 4, 39, 49, 48, 5, 45, 1, 50, 0, 11, 60, 36, 55, 40, 59, 3, 25, 14, 65, 28, 30, 57, 56, 61, 54, 22, 43, 38, 64, 63, 51, 35, 26, 7, 13, 53, 52, 6, 29, 24, 32, 44, 17, 21, 10, 19, 46, 58, 9, 34, 31, 33, 37, 42, 41, 12, 47], 'cur_cost': 89389.0, 'intermediate_solutions': [{'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 24, 29, 1, 53, 17, 55, 61, 10, 40, 31, 57, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 104439.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 8, 45, 5, 65, 43, 48, 38, 2, 12, 32, 14, 39, 54, 46, 49, 63, 0, 16, 36, 56, 33, 40, 31, 57, 10, 61, 55, 17, 53, 1, 29, 24, 18, 52, 34, 30, 28, 60, 58, 26, 25, 37, 27, 19, 64, 51, 20, 3, 7, 41, 6, 4, 21, 44, 62, 47, 11, 15], 'cur_cost': 102121.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [50, 59, 42, 13, 23, 22, 9, 35, 7, 3, 20, 51, 64, 19, 27, 37, 25, 26, 58, 60, 28, 30, 34, 52, 18, 15, 24, 29, 1, 53, 17, 55, 61, 10, 57, 31, 40, 33, 56, 36, 16, 0, 63, 49, 46, 54, 39, 14, 32, 12, 2, 38, 48, 43, 65, 5, 45, 8, 41, 6, 4, 21, 44, 62, 47, 11], 'cur_cost': 102043.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 66221.0, 'intermediate_solutions': [{'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 21, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 58, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16118.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 9, 5, 8, 2, 6, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 35, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14549.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([57, 15, 23, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61],
      dtype=int64), 'cur_cost': 101711.0, 'intermediate_solutions': [{'tour': array([57, 23, 44, 29, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 118871.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([29, 57, 23, 44, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 116892.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([49, 29, 57, 23, 44,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 119135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([44, 29, 57, 23, 49,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 119159.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([44, 49, 29, 57, 23,  0, 12, 56, 34, 31, 38, 53, 19, 63,  5, 45,  9,
       60, 59, 25, 51, 50,  4,  6, 18, 47, 17, 64, 33, 10, 43, 21, 41, 42,
       13, 40,  7, 61, 48, 58, 39, 15, 65, 24, 62, 20, 36,  8, 35, 14, 52,
       26, 16, 54, 32, 46, 37, 27,  3, 28, 11,  2,  1, 30, 55, 22]), 'cur_cost': 116557.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62480.0, 'intermediate_solutions': [{'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 59, 55, 61, 53, 62, 10, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 14892.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 51, 38, 45, 44, 39, 43, 48, 46, 50, 41, 42], 'cur_cost': 12963.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 21, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 10, 55, 61, 28, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18764.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0, 'intermediate_solutions': [{'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 40, 34, 33, 31, 24, 29, 32, 30, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16941.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 36, 26, 25, 37, 27, 21, 20, 13, 23, 16, 18, 17, 12, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18652.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 19, 22, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 16, 23, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 18, 50, 41, 42], 'cur_cost': 15737.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:07,688 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:07,688 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,691 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12370.000, 多样性=0.956
2025-08-05 10:29:07,691 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:29:07,691 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:29:07,691 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:07,692 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.014980738895897544, 'best_improvement': -0.18930872031535428}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04835732742709486}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04288824897008553, 'recent_improvements': [0.1476632756608843, 0.036131686801495415, 0.06188677772071325], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9524, 'new_best_cost': 9524, 'quality_improvement': 0.0, 'old_diversity': 0.8015151515151515, 'new_diversity': 0.8015151515151515, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:29:07,692 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:29:07,692 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-08-05 10:29:07,692 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:29:07,692 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:07,693 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12370.000, 多样性=0.956
2025-08-05 10:29:07,693 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:07,696 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.956
2025-08-05 10:29:07,696 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:07,698 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.802
2025-08-05 10:29:07,700 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:29:07,701 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:07,701 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:07,701 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:07,743 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -5351.587, 聚类评分: 0.000, 覆盖率: 0.141, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:07,744 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:29:07,744 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:07,744 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 10:29:07,749 - visualization.landscape_visualizer - INFO - 插值约束: 171 个点被约束到最小值 9524.00
2025-08-05 10:29:07,751 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=4.3%, 梯度: 3369.81 → 3224.24
2025-08-05 10:29:07,860 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_123_20250805_102907.html
2025-08-05 10:29:07,923 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_123_20250805_102907.html
2025-08-05 10:29:07,924 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 123
2025-08-05 10:29:07,924 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:29:07,924 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2239秒
2025-08-05 10:29:07,924 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.06666666666666667, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -5351.586666666665, 'local_optima_density': 0.06666666666666667, 'gradient_variance': 860564999.8211557, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1412, 'fitness_entropy': 0.7773048806327832, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5351.587)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.141)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360947.74424, 'performance_metrics': {}}}
2025-08-05 10:29:07,925 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:07,925 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:07,925 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:07,925 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:07,926 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,926 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:29:07,926 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,926 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,926 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:07,926 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore']
2025-08-05 10:29:07,927 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:07,928 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:07,928 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:07,928 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:07,928 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,928 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,929 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 112337.0
2025-08-05 10:29:07,938 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:07,938 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:07,939 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:07,941 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,941 - ExploitationExpert - INFO - populations: [{'tour': array([ 5, 42,  8, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62],
      dtype=int64), 'cur_cost': 112337.0}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12370.0}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 58461.0}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12874.0}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14823.0}, {'tour': [62, 20, 27, 16, 23, 15, 2, 18, 8, 4, 39, 49, 48, 5, 45, 1, 50, 0, 11, 60, 36, 55, 40, 59, 3, 25, 14, 65, 28, 30, 57, 56, 61, 54, 22, 43, 38, 64, 63, 51, 35, 26, 7, 13, 53, 52, 6, 29, 24, 32, 44, 17, 21, 10, 19, 46, 58, 9, 34, 31, 33, 37, 42, 41, 12, 47], 'cur_cost': 89389.0}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 66221.0}, {'tour': [57, 15, 23, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51, 22, 16, 47, 27, 2, 24, 25, 65, 62, 33, 18, 0, 6, 42, 17, 45, 1, 36, 63, 31, 40, 26, 37, 8, 52, 54, 50, 9, 55, 7, 10, 3, 30, 56, 44, 4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46, 5, 61], 'cur_cost': 101711.0}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62480.0}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0}]
2025-08-05 10:29:07,942 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:07,942 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 317, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 317, 'cache_hits': 0, 'similarity_calculations': 1600, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:07,943 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 5, 42,  8, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62],
      dtype=int64), 'cur_cost': 112337.0, 'intermediate_solutions': [{'tour': array([49,  0, 61, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 106884.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 49,  0, 61, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 106640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 31, 49,  0, 61, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 108876.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([61, 31, 49,  0, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 108986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([61, 46, 31, 49,  0, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 110988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:07,943 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 112337.00)
2025-08-05 10:29:07,943 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:07,944 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:07,944 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,945 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:07,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,946 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,947 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93703.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,947 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 93703.0, 'intermediate_solutions': [{'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 38, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 1, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 22472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 61, 55, 10, 6, 2, 8, 4, 5, 11, 1, 7, 3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 17, 14, 15, 23, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 61, 53, 62, 55, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12422.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,947 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 93703.00)
2025-08-05 10:29:07,947 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:07,948 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:07,948 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,958 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:07,958 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,959 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64999.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,960 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 64999.0, 'intermediate_solutions': [{'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 38, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 14, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 63011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 55, 45, 43, 29, 30, 54], 'cur_cost': 62120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 45, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 55, 54], 'cur_cost': 60623.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,960 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 64999.00)
2025-08-05 10:29:07,960 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:07,961 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:07,961 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,971 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:07,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,972 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,973 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,974 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,974 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60891.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,974 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 60891.0, 'intermediate_solutions': [{'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 35, 27, 37, 25, 26, 36, 19, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15276.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 46, 47, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 20, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15338.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,975 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 60891.00)
2025-08-05 10:29:07,975 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:07,975 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:07,975 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:07,979 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:07,979 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,980 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:07,980 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12321.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:07,980 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12321.0, 'intermediate_solutions': [{'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 45, 65, 52, 63, 39, 44, 54, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 26099.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 64, 60, 58, 56, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 57, 54, 65, 52, 63, 64, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:07,981 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 12321.00)
2025-08-05 10:29:07,981 - experts.management.collaboration_manager - INFO - 为个体 5 生成利用路径
2025-08-05 10:29:07,981 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:07,981 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:07,981 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 107042.0
2025-08-05 10:29:07,995 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:07,995 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:07,995 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:07,998 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:07,998 - ExploitationExpert - INFO - populations: [{'tour': array([ 5, 42,  8, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62],
      dtype=int64), 'cur_cost': 112337.0}, {'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 93703.0}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 64999.0}, {'tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 60891.0}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12321.0}, {'tour': array([ 7, 64, 22, 47, 35,  3, 43, 54, 29, 61, 41, 62,  2, 36, 65, 28, 16,
       51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 14, 48, 38, 45,  8, 63,
       31, 19, 21, 20, 55,  1, 34, 13, 53, 39, 44, 52, 11,  6, 12, 27, 37,
       10, 50, 46,  0, 18, 15,  5,  4,  9, 49, 58, 30, 40, 60, 17],
      dtype=int64), 'cur_cost': 107042.0}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 66221.0}, {'tour': [57, 15, 23, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51, 22, 16, 47, 27, 2, 24, 25, 65, 62, 33, 18, 0, 6, 42, 17, 45, 1, 36, 63, 31, 40, 26, 37, 8, 52, 54, 50, 9, 55, 7, 10, 3, 30, 56, 44, 4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46, 5, 61], 'cur_cost': 101711.0}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62480.0}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0}]
2025-08-05 10:29:07,999 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:07,999 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 318, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 318, 'cache_hits': 0, 'similarity_calculations': 1605, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,000 - experts.management.collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([ 7, 64, 22, 47, 35,  3, 43, 54, 29, 61, 41, 62,  2, 36, 65, 28, 16,
       51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 14, 48, 38, 45,  8, 63,
       31, 19, 21, 20, 55,  1, 34, 13, 53, 39, 44, 52, 11,  6, 12, 27, 37,
       10, 50, 46,  0, 18, 15,  5,  4,  9, 49, 58, 30, 40, 60, 17],
      dtype=int64), 'cur_cost': 107042.0, 'intermediate_solutions': [{'tour': array([27, 20, 62, 16, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 27, 20, 62, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 16, 27, 20, 62, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89836.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([62, 16, 27, 20, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([62, 23, 16, 27, 20, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,000 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 107042.00)
2025-08-05 10:29:08,001 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:08,001 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:08,001 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,003 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,004 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,005 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14887.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,005 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14887.0, 'intermediate_solutions': [{'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 50, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 53, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 70547.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 42, 32, 35, 38, 50, 40, 39, 28, 21, 46, 25, 36, 9, 20, 37, 6, 17, 49, 53, 0, 52, 7, 4, 13, 15, 33, 18, 55, 11, 22, 16, 24, 26, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 68505.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 17, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 68854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,005 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 14887.00)
2025-08-05 10:29:08,005 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:08,006 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,006 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,006 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108826.0
2025-08-05 10:29:08,021 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,021 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,022 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,026 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,026 - ExploitationExpert - INFO - populations: [{'tour': array([ 5, 42,  8, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62],
      dtype=int64), 'cur_cost': 112337.0}, {'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 93703.0}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 64999.0}, {'tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 60891.0}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12321.0}, {'tour': array([ 7, 64, 22, 47, 35,  3, 43, 54, 29, 61, 41, 62,  2, 36, 65, 28, 16,
       51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 14, 48, 38, 45,  8, 63,
       31, 19, 21, 20, 55,  1, 34, 13, 53, 39, 44, 52, 11,  6, 12, 27, 37,
       10, 50, 46,  0, 18, 15,  5,  4,  9, 49, 58, 30, 40, 60, 17],
      dtype=int64), 'cur_cost': 107042.0}, {'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14887.0}, {'tour': array([59, 56, 44, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6],
      dtype=int64), 'cur_cost': 108826.0}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62480.0}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0}]
2025-08-05 10:29:08,029 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:08,029 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 319, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 319, 'cache_hits': 0, 'similarity_calculations': 1611, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,030 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([59, 56, 44, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6],
      dtype=int64), 'cur_cost': 108826.0, 'intermediate_solutions': [{'tour': array([23, 15, 57, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 23, 15, 57, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 14, 23, 15, 57, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 14, 23, 15, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 101682.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 35, 14, 23, 15, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 102078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,031 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 108826.00)
2025-08-05 10:29:08,031 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:08,031 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:08,031 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,034 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:08,034 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,034 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,035 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,035 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107977.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,035 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 107977.0, 'intermediate_solutions': [{'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 46, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 9, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 67308.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 64, 41, 51, 45, 1, 61, 58, 53, 56, 6, 24, 35, 29, 48, 42, 31, 16, 43, 55, 57, 65, 59, 62, 7, 17, 26, 39, 19, 47, 38], 'cur_cost': 62386.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 4, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62469.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,036 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 107977.00)
2025-08-05 10:29:08,036 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:29:08,036 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:29:08,036 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,038 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:08,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111456.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,039 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [21, 13, 18, 16, 1, 59, 20, 6, 34, 26, 58, 19, 28, 4, 24, 31, 62, 40, 35, 8, 45, 65, 22, 56, 0, 5, 50, 48, 23, 43, 30, 14, 17, 61, 3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38, 2, 29, 32, 57, 9, 46, 52, 33, 10, 51, 7, 25, 64, 63, 39, 49, 37, 53], 'cur_cost': 111456.0, 'intermediate_solutions': [{'tour': [0, 13, 21, 4, 53, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 5, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 27, 20, 19, 23, 16, 18, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,039 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 111456.00)
2025-08-05 10:29:08,039 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:08,039 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:08,041 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 42,  8, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62],
      dtype=int64), 'cur_cost': 112337.0, 'intermediate_solutions': [{'tour': array([49,  0, 61, 31, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 106884.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([31, 49,  0, 61, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 106640.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([46, 31, 49,  0, 61, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 108876.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([61, 31, 49,  0, 46, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 108986.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([61, 46, 31, 49,  0, 45,  1, 38, 17, 25, 55,  5, 65, 33, 57, 10, 44,
       30, 14, 53, 36, 52, 24,  3, 22, 32, 21,  6, 56, 35, 29, 15, 12, 54,
       20,  9, 59, 63, 43, 18, 19, 51, 34, 40, 58,  8, 47,  2, 62, 42, 16,
       28,  7, 41, 48,  4, 37, 27, 26, 39, 50, 23, 64, 60, 11, 13]), 'cur_cost': 110988.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 93703.0, 'intermediate_solutions': [{'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 38, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 1, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 22472.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 61, 55, 10, 6, 2, 8, 4, 5, 11, 1, 7, 3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 17, 14, 15, 23, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 16193.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 20, 13, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 61, 53, 62, 55, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12422.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 64999.0, 'intermediate_solutions': [{'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 38, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 14, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 45, 55, 54], 'cur_cost': 63011.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 55, 45, 43, 29, 30, 54], 'cur_cost': 62120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [60, 62, 15, 22, 18, 27, 35, 1, 59, 7, 52, 57, 14, 28, 5, 17, 25, 20, 33, 34, 2, 63, 45, 21, 6, 64, 58, 23, 19, 0, 8, 65, 10, 26, 3, 36, 24, 37, 31, 13, 4, 61, 56, 11, 53, 12, 9, 32, 49, 48, 46, 38, 41, 50, 39, 40, 42, 47, 44, 51, 16, 30, 29, 43, 55, 54], 'cur_cost': 60623.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 60891.0, 'intermediate_solutions': [{'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 35, 27, 37, 25, 26, 36, 19, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15276.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 46, 47, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12886.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 21, 2, 8, 5, 4, 9, 11, 7, 3, 20, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15338.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12321.0, 'intermediate_solutions': [{'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 45, 65, 52, 63, 39, 44, 54, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 26099.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 64, 60, 58, 56, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14885.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 8, 21, 20, 13, 23, 16, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 6, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 57, 54, 65, 52, 63, 64, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14834.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 64, 22, 47, 35,  3, 43, 54, 29, 61, 41, 62,  2, 36, 65, 28, 16,
       51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 14, 48, 38, 45,  8, 63,
       31, 19, 21, 20, 55,  1, 34, 13, 53, 39, 44, 52, 11,  6, 12, 27, 37,
       10, 50, 46,  0, 18, 15,  5,  4,  9, 49, 58, 30, 40, 60, 17],
      dtype=int64), 'cur_cost': 107042.0, 'intermediate_solutions': [{'tour': array([27, 20, 62, 16, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89496.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 27, 20, 62, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89898.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 16, 27, 20, 62, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89836.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([62, 16, 27, 20, 23, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89432.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([62, 23, 16, 27, 20, 15,  2, 18,  8,  4, 39, 49, 48,  5, 45,  1, 50,
        0, 11, 60, 36, 55, 40, 59,  3, 25, 14, 65, 28, 30, 57, 56, 61, 54,
       22, 43, 38, 64, 63, 51, 35, 26,  7, 13, 53, 52,  6, 29, 24, 32, 44,
       17, 21, 10, 19, 46, 58,  9, 34, 31, 33, 37, 42, 41, 12, 47]), 'cur_cost': 89418.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14887.0, 'intermediate_solutions': [{'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 50, 49, 17, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 53, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 70547.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 42, 32, 35, 38, 50, 40, 39, 28, 21, 46, 25, 36, 9, 20, 37, 6, 17, 49, 53, 0, 52, 7, 4, 13, 15, 33, 18, 55, 11, 22, 16, 24, 26, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 68505.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [30, 3, 14, 8, 58, 56, 10, 2, 5, 12, 26, 24, 16, 22, 11, 55, 18, 33, 15, 13, 4, 7, 52, 0, 53, 49, 6, 37, 20, 9, 36, 25, 46, 21, 28, 39, 40, 50, 38, 35, 32, 42, 48, 19, 34, 1, 59, 57, 65, 54, 60, 64, 63, 47, 17, 43, 23, 29, 44, 41, 51, 45, 31, 27, 61, 62], 'cur_cost': 68854.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([59, 56, 44, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6],
      dtype=int64), 'cur_cost': 108826.0, 'intermediate_solutions': [{'tour': array([23, 15, 57, 14, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105466.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([14, 23, 15, 57, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105280.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([35, 14, 23, 15, 57, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 105284.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([57, 14, 23, 15, 35, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 101682.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([57, 35, 14, 23, 15, 38, 48, 58, 12, 19, 11, 49, 64, 59, 13, 32, 51,
       22, 16, 47, 27,  2, 24, 25, 65, 62, 33, 18,  0,  6, 42, 17, 45,  1,
       36, 63, 31, 40, 26, 37,  8, 52, 54, 50,  9, 55,  7, 10,  3, 30, 56,
       44,  4, 43, 53, 60, 41, 29, 21, 39, 20, 34, 28, 46,  5, 61]), 'cur_cost': 102078.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 107977.0, 'intermediate_solutions': [{'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 46, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 9, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 67308.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 4, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 64, 41, 51, 45, 1, 61, 58, 53, 56, 6, 24, 35, 29, 48, 42, 31, 16, 43, 55, 57, 65, 59, 62, 7, 17, 26, 39, 19, 47, 38], 'cur_cost': 62386.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [50, 13, 33, 30, 3, 52, 10, 60, 20, 21, 36, 22, 5, 15, 18, 28, 27, 25, 37, 9, 8, 11, 54, 14, 34, 32, 0, 2, 63, 23, 40, 44, 46, 12, 49, 38, 47, 19, 39, 26, 17, 7, 62, 59, 65, 57, 55, 43, 16, 31, 42, 48, 29, 35, 24, 4, 6, 56, 53, 58, 61, 1, 45, 51, 41, 64], 'cur_cost': 62469.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [21, 13, 18, 16, 1, 59, 20, 6, 34, 26, 58, 19, 28, 4, 24, 31, 62, 40, 35, 8, 45, 65, 22, 56, 0, 5, 50, 48, 23, 43, 30, 14, 17, 61, 3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38, 2, 29, 32, 57, 9, 46, 52, 33, 10, 51, 7, 25, 64, 63, 39, 49, 37, 53], 'cur_cost': 111456.0, 'intermediate_solutions': [{'tour': [0, 13, 21, 4, 53, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 5, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 16918.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 27, 20, 19, 23, 16, 18, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 15134.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12913.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:29:08,041 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:08,041 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,045 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12321.000, 多样性=0.973
2025-08-05 10:29:08,045 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:29:08,045 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:29:08,045 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:08,046 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.17531102821400432, 'best_improvement': 0.003961196443007276}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.017957746478873122}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.010575473952798934, 'recent_improvements': [0.036131686801495415, 0.06188677772071325, 0.014980738895897544], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9524, 'new_best_cost': 9524, 'quality_improvement': 0.0, 'old_diversity': 0.8015151515151515, 'new_diversity': 0.8015151515151515, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:08,046 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:29:08,047 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-08-05 10:29:08,047 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:29:08,047 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,047 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12321.000, 多样性=0.973
2025-08-05 10:29:08,047 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:08,050 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-05 10:29:08,050 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:08,051 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.802
2025-08-05 10:29:08,053 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:29:08,053 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:08,053 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:08,053 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:08,093 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -2364.520, 聚类评分: 0.000, 覆盖率: 0.142, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:08,093 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:29:08,094 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:08,094 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 10:29:08,126 - visualization.landscape_visualizer - INFO - 插值约束: 297 个点被约束到最小值 9524.00
2025-08-05 10:29:08,128 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.5%, 梯度: 4631.26 → 4328.85
2025-08-05 10:29:08,257 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_124_20250805_102908.html
2025-08-05 10:29:08,340 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_124_20250805_102908.html
2025-08-05 10:29:08,340 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 124
2025-08-05 10:29:08,340 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:29:08,340 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2867秒
2025-08-05 10:29:08,341 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.06666666666666667, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2364.519999999999, 'local_optima_density': 0.06666666666666667, 'gradient_variance': 692834551.7429334, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1421, 'fitness_entropy': 0.7850439487623072, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2364.520)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.142)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360948.0934753, 'performance_metrics': {}}}
2025-08-05 10:29:08,341 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:08,341 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:08,341 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:08,341 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:08,342 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,342 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:29:08,343 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,343 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:08,343 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:08,343 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,343 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:08,344 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:08,344 - experts.management.collaboration_manager - INFO - 识别精英个体: {4, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:08,344 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:08,344 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,344 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,345 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 114688.0
2025-08-05 10:29:08,360 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,360 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,360 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,363 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,363 - ExploitationExpert - INFO - populations: [{'tour': array([27, 17,  4, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20],
      dtype=int64), 'cur_cost': 114688.0}, {'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 93703.0}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 64999.0}, {'tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 60891.0}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12321.0}, {'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 14, 48, 38, 45, 8, 63, 31, 19, 21, 20, 55, 1, 34, 13, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 107042.0}, {'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14887.0}, {'tour': [59, 56, 44, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29, 0, 17, 13, 25, 4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52, 43, 57, 65, 19, 23, 3, 40, 47, 31, 20, 16, 49, 39, 7, 2, 5, 12, 38, 14, 8, 32, 42, 62, 53, 1, 11, 9, 45, 55, 21, 27, 6], 'cur_cost': 108826.0}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 107977.0}, {'tour': [21, 13, 18, 16, 1, 59, 20, 6, 34, 26, 58, 19, 28, 4, 24, 31, 62, 40, 35, 8, 45, 65, 22, 56, 0, 5, 50, 48, 23, 43, 30, 14, 17, 61, 3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38, 2, 29, 32, 57, 9, 46, 52, 33, 10, 51, 7, 25, 64, 63, 39, 49, 37, 53], 'cur_cost': 111456.0}]
2025-08-05 10:29:08,365 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:08,366 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 320, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 320, 'cache_hits': 0, 'similarity_calculations': 1618, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,366 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([27, 17,  4, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20],
      dtype=int64), 'cur_cost': 114688.0, 'intermediate_solutions': [{'tour': array([ 8, 42,  5, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  8, 42,  5, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 114056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 43,  8, 42,  5, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 114270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 43,  8, 42, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112306.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 30, 43,  8, 42, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,366 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 114688.00)
2025-08-05 10:29:08,366 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:08,366 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:08,366 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,369 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,371 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12428.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,371 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0, 'intermediate_solutions': [{'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 50, 27, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 95848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 61, 17, 0, 6, 46, 48, 12, 39, 32, 29, 56, 26, 52, 55, 7, 5, 65, 50, 27, 37, 38, 25, 54, 60, 22, 23, 45, 51, 35, 42, 47, 36, 4, 41, 30, 43, 44, 57, 62, 31, 24, 8, 28, 19, 58, 64, 21, 63, 49, 10, 11, 40, 3], 'cur_cost': 93725.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 15, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 13, 40, 3], 'cur_cost': 93767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,371 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 12428.00)
2025-08-05 10:29:08,371 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:08,371 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:08,371 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,373 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-05 10:29:08,373 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,374 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95079.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,374 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95079.0, 'intermediate_solutions': [{'tour': [21, 32, 54, 30, 29, 35, 37, 13, 34, 31, 6, 3, 16, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 66799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 36, 42, 50, 40, 23, 18, 63, 0, 19, 45, 39, 65, 2, 8, 14, 49, 24, 27, 22, 55, 1, 20, 61, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 68642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 60, 4, 15, 5, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 62974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,375 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 95079.00)
2025-08-05 10:29:08,375 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:08,375 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:08,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,377 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,378 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12876.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,379 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12876.0, 'intermediate_solutions': [{'tour': [61, 55, 7, 23, 11, 37, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 65, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 62458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 30, 45, 37, 48, 41, 49, 27, 17, 18, 51, 42, 29, 5, 9, 52, 2, 56, 6, 10, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 63264.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 55, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38, 7], 'cur_cost': 61280.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,379 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12876.00)
2025-08-05 10:29:08,379 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:08,380 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:08,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,386 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:08,386 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,387 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58351.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,388 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 2, 9, 61, 59, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58351.0, 'intermediate_solutions': [{'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 64, 55, 61, 53, 62, 59, 56, 58, 60, 10, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14371.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 42, 48], 'cur_cost': 12315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 17, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14688.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,388 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 58351.00)
2025-08-05 10:29:08,388 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:08,388 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:08,388 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,398 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:08,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65484.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,401 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 65484.0, 'intermediate_solutions': [{'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 63, 48, 38, 45, 8, 14, 31, 19, 21, 20, 55, 1, 34, 13, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 108964.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 13, 34, 1, 55, 20, 21, 19, 31, 63, 8, 45, 38, 48, 14, 23, 42, 32, 26, 59, 57, 25, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 107038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 45, 23, 14, 48, 38, 8, 63, 31, 19, 21, 20, 55, 1, 34, 13, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 107096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,401 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 65484.00)
2025-08-05 10:29:08,403 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:08,403 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:08,403 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,406 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,406 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,407 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,407 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12319.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,407 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12319.0, 'intermediate_solutions': [{'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 48, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 39, 42], 'cur_cost': 14999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 6, 2, 8, 4, 5, 9, 11, 1, 7, 3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 14, 15, 17, 18, 19, 48, 42], 'cur_cost': 17482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 10, 12, 1, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17332.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,408 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 12319.00)
2025-08-05 10:29:08,408 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:08,408 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,408 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,409 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109642.0
2025-08-05 10:29:08,418 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,418 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,418 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,420 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,420 - ExploitationExpert - INFO - populations: [{'tour': array([27, 17,  4, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20],
      dtype=int64), 'cur_cost': 114688.0}, {'tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0}, {'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95079.0}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12876.0}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 2, 9, 61, 59, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58351.0}, {'tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 65484.0}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12319.0}, {'tour': array([15, 63, 30,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61],
      dtype=int64), 'cur_cost': 109642.0}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 107977.0}, {'tour': [21, 13, 18, 16, 1, 59, 20, 6, 34, 26, 58, 19, 28, 4, 24, 31, 62, 40, 35, 8, 45, 65, 22, 56, 0, 5, 50, 48, 23, 43, 30, 14, 17, 61, 3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38, 2, 29, 32, 57, 9, 46, 52, 33, 10, 51, 7, 25, 64, 63, 39, 49, 37, 53], 'cur_cost': 111456.0}]
2025-08-05 10:29:08,422 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:08,422 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 321, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 321, 'cache_hits': 0, 'similarity_calculations': 1626, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,423 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([15, 63, 30,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61],
      dtype=int64), 'cur_cost': 109642.0, 'intermediate_solutions': [{'tour': array([44, 56, 59, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 110768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 44, 56, 59, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 107134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([64, 15, 44, 56, 59, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([59, 15, 44, 56, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([59, 64, 15, 44, 56, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,423 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 109642.00)
2025-08-05 10:29:08,423 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:08,423 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:08,423 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,429 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:08,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,429 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,430 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61184.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,430 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61184.0, 'intermediate_solutions': [{'tour': [9, 29, 35, 59, 53, 31, 2, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 22, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 110141.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 58, 5, 10, 12, 49, 14, 18, 6, 48, 36, 30, 7, 15, 57, 1, 42, 25, 64, 11, 37, 46, 60, 3, 56, 39, 45, 55, 44, 33, 43, 34, 23, 61, 17, 41, 28, 2, 52, 50, 27, 38, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 109233.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 4, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 103810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,430 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 61184.00)
2025-08-05 10:29:08,431 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:08,431 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,431 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,431 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 98232.0
2025-08-05 10:29:08,442 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,442 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,442 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,444 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,444 - ExploitationExpert - INFO - populations: [{'tour': array([27, 17,  4, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20],
      dtype=int64), 'cur_cost': 114688.0}, {'tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0}, {'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95079.0}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12876.0}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 2, 9, 61, 59, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58351.0}, {'tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 65484.0}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12319.0}, {'tour': array([15, 63, 30,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61],
      dtype=int64), 'cur_cost': 109642.0}, {'tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61184.0}, {'tour': array([ 5, 47, 37, 41, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28],
      dtype=int64), 'cur_cost': 98232.0}]
2025-08-05 10:29:08,446 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:08,446 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 322, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 322, 'cache_hits': 0, 'similarity_calculations': 1635, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,447 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 5, 47, 37, 41, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28],
      dtype=int64), 'cur_cost': 98232.0, 'intermediate_solutions': [{'tour': array([18, 13, 21, 16,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 18, 13, 21,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 16, 18, 13, 21, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 16, 18, 13,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21,  1, 16, 18, 13, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 113613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,447 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 98232.00)
2025-08-05 10:29:08,447 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:08,447 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:08,450 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 17,  4, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20],
      dtype=int64), 'cur_cost': 114688.0, 'intermediate_solutions': [{'tour': array([ 8, 42,  5, 43, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112305.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([43,  8, 42,  5, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 114056.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([30, 43,  8, 42,  5, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 114270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 43,  8, 42, 30, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112306.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 30, 43,  8, 42, 17, 23, 64, 41, 61, 40, 22, 50,  7, 25, 44, 48,
       15, 54, 35, 18, 45, 28, 49, 60, 21, 55, 47,  9, 46, 37, 32, 12, 52,
       13, 34,  3, 58, 39, 26, 29, 56, 38, 19, 10,  6, 33, 16, 20, 63, 27,
       59, 57,  1, 11, 65,  2,  4,  0, 31, 53, 14, 51, 24, 36, 62]), 'cur_cost': 112086.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0, 'intermediate_solutions': [{'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 50, 27, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 40, 3], 'cur_cost': 95848.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 15, 13, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 61, 17, 0, 6, 46, 48, 12, 39, 32, 29, 56, 26, 52, 55, 7, 5, 65, 50, 27, 37, 38, 25, 54, 60, 22, 23, 45, 51, 35, 42, 47, 36, 4, 41, 30, 43, 44, 57, 62, 31, 24, 8, 28, 19, 58, 64, 21, 63, 49, 10, 11, 40, 3], 'cur_cost': 93725.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 15, 18, 16, 1, 59, 14, 53, 20, 33, 34, 2, 63, 21, 64, 58, 19, 28, 8, 24, 31, 62, 57, 44, 43, 30, 41, 4, 36, 47, 42, 35, 51, 45, 23, 22, 60, 54, 25, 38, 37, 27, 50, 65, 5, 7, 55, 52, 26, 56, 29, 32, 39, 12, 48, 46, 6, 0, 17, 61, 49, 10, 11, 13, 40, 3], 'cur_cost': 93767.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95079.0, 'intermediate_solutions': [{'tour': [21, 32, 54, 30, 29, 35, 37, 13, 34, 31, 6, 3, 16, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 66799.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 5, 60, 4, 15, 11, 56, 64, 36, 42, 50, 40, 23, 18, 63, 0, 19, 45, 39, 65, 2, 8, 14, 49, 24, 27, 22, 55, 1, 20, 61, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 68642.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [21, 32, 16, 30, 29, 35, 37, 13, 34, 31, 6, 3, 54, 17, 7, 10, 53, 60, 4, 15, 5, 11, 56, 64, 61, 20, 1, 55, 22, 27, 24, 49, 14, 8, 2, 65, 39, 45, 19, 0, 63, 18, 23, 40, 50, 42, 36, 48, 46, 26, 25, 28, 44, 43, 47, 33, 12, 38, 41, 9, 62, 59, 57, 52, 58, 51], 'cur_cost': 62974.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12876.0, 'intermediate_solutions': [{'tour': [61, 55, 7, 23, 11, 37, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 65, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 62458.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 55, 7, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 30, 45, 37, 48, 41, 49, 27, 17, 18, 51, 42, 29, 5, 9, 52, 2, 56, 6, 10, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38], 'cur_cost': 63264.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 55, 23, 11, 65, 13, 15, 22, 19, 33, 21, 26, 36, 14, 8, 3, 12, 35, 20, 43, 34, 24, 32, 16, 40, 47, 31, 28, 25, 0, 63, 54, 57, 58, 10, 6, 56, 2, 52, 9, 5, 29, 42, 51, 18, 17, 27, 49, 41, 48, 37, 45, 30, 44, 50, 1, 62, 64, 53, 60, 59, 4, 39, 46, 38, 7], 'cur_cost': 61280.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 2, 9, 61, 59, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58351.0, 'intermediate_solutions': [{'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 64, 55, 61, 53, 62, 59, 56, 58, 60, 10, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14371.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 42, 48], 'cur_cost': 12315.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 19, 21, 20, 13, 23, 16, 18, 12, 22, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 17, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14688.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 65484.0, 'intermediate_solutions': [{'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 23, 63, 48, 38, 45, 8, 14, 31, 19, 21, 20, 55, 1, 34, 13, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 108964.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 13, 34, 1, 55, 20, 21, 19, 31, 63, 8, 45, 38, 48, 14, 23, 42, 32, 26, 59, 57, 25, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 107038.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 64, 22, 47, 35, 3, 43, 54, 29, 61, 41, 62, 2, 36, 65, 28, 16, 51, 24, 33, 56, 25, 57, 59, 26, 32, 42, 45, 23, 14, 48, 38, 8, 63, 31, 19, 21, 20, 55, 1, 34, 13, 53, 39, 44, 52, 11, 6, 12, 27, 37, 10, 50, 46, 0, 18, 15, 5, 4, 9, 49, 58, 30, 40, 60, 17], 'cur_cost': 107096.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12319.0, 'intermediate_solutions': [{'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 48, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 39, 42], 'cur_cost': 14999.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 10, 12, 22, 23, 13, 20, 21, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 6, 2, 8, 4, 5, 9, 11, 1, 7, 3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 14, 15, 17, 18, 19, 48, 42], 'cur_cost': 17482.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 16, 10, 12, 1, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 17332.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 63, 30,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61],
      dtype=int64), 'cur_cost': 109642.0, 'intermediate_solutions': [{'tour': array([44, 56, 59, 15, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 110768.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([15, 44, 56, 59, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 107134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([64, 15, 44, 56, 59, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([59, 15, 44, 56, 64, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108826.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([59, 64, 15, 44, 56, 30, 18, 33, 60, 48, 37, 46, 54, 22, 24, 58, 29,
        0, 17, 13, 25,  4, 28, 50, 35, 34, 41, 36, 10, 61, 26, 63, 51, 52,
       43, 57, 65, 19, 23,  3, 40, 47, 31, 20, 16, 49, 39,  7,  2,  5, 12,
       38, 14,  8, 32, 42, 62, 53,  1, 11,  9, 45, 55, 21, 27,  6]), 'cur_cost': 108790.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61184.0, 'intermediate_solutions': [{'tour': [9, 29, 35, 59, 53, 31, 2, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 38, 27, 50, 52, 22, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 110141.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 4, 51, 65, 0, 58, 5, 10, 12, 49, 14, 18, 6, 48, 36, 30, 7, 15, 57, 1, 42, 25, 64, 11, 37, 46, 60, 3, 56, 39, 45, 55, 44, 33, 43, 34, 23, 61, 17, 41, 28, 2, 52, 50, 27, 38, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 109233.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 29, 35, 59, 53, 31, 22, 21, 26, 19, 20, 24, 62, 40, 51, 65, 0, 38, 27, 50, 52, 2, 28, 41, 17, 61, 23, 34, 43, 33, 44, 55, 45, 39, 4, 56, 3, 60, 46, 37, 11, 64, 25, 42, 1, 57, 15, 7, 30, 36, 48, 6, 18, 14, 49, 12, 10, 5, 58, 63, 13, 16, 32, 47, 54, 8], 'cur_cost': 103810.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 47, 37, 41, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28],
      dtype=int64), 'cur_cost': 98232.0, 'intermediate_solutions': [{'tour': array([18, 13, 21, 16,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111501.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 18, 13, 21,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111487.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 16, 18, 13, 21, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111448.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([21, 16, 18, 13,  1, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 111504.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([21,  1, 16, 18, 13, 59, 20,  6, 34, 26, 58, 19, 28,  4, 24, 31, 62,
       40, 35,  8, 45, 65, 22, 56,  0,  5, 50, 48, 23, 43, 30, 14, 17, 61,
        3, 15, 54, 44, 47, 12, 36, 60, 27, 42, 11, 55, 41, 38,  2, 29, 32,
       57,  9, 46, 52, 33, 10, 51,  7, 25, 64, 63, 39, 49, 37, 53]), 'cur_cost': 113613.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:08,450 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:08,450 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,453 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12319.000, 多样性=0.973
2025-08-05 10:29:08,453 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:29:08,453 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:29:08,455 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:08,455 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.05775935717014117, 'best_improvement': 0.00016232448664881097}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.00034590107229328835}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.1185989029673588, 'recent_improvements': [0.06188677772071325, 0.014980738895897544, -0.17531102821400432], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9524, 'new_best_cost': 9524, 'quality_improvement': 0.0, 'old_diversity': 0.8015151515151515, 'new_diversity': 0.8015151515151515, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:08,455 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:29:08,455 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-08-05 10:29:08,455 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:29:08,455 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,456 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12319.000, 多样性=0.973
2025-08-05 10:29:08,456 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:29:08,460 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.973
2025-08-05 10:29:08,460 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:29:08,462 - EliteExpert - INFO - 精英解分析完成: 精英解数量=5, 多样性=0.802
2025-08-05 10:29:08,463 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:29:08,464 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:29:08,464 - LandscapeExpert - INFO - 添加精英解数据: 5个精英解
2025-08-05 10:29:08,464 - LandscapeExpert - INFO - 数据提取成功: 15个路径, 15个适应度值
2025-08-05 10:29:08,506 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.067, 适应度梯度: -9498.627, 聚类评分: 0.000, 覆盖率: 0.143, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:29:08,506 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:29:08,506 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:29:08,506 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite13_66
2025-08-05 10:29:08,511 - visualization.landscape_visualizer - INFO - 插值约束: 86 个点被约束到最小值 9524.00
2025-08-05 10:29:08,513 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.4%, 梯度: 5114.59 → 4736.97
2025-08-05 10:29:08,621 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\landscape_composite13_66_iter_125_20250805_102908.html
2025-08-05 10:29:08,690 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite13_66\dashboard_composite13_66_iter_125_20250805_102908.html
2025-08-05 10:29:08,690 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 125
2025-08-05 10:29:08,691 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:29:08,691 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2277秒
2025-08-05 10:29:08,691 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.06666666666666667, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9498.626666666665, 'local_optima_density': 0.06666666666666667, 'gradient_variance': 1142985104.3139558, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.143, 'fitness_entropy': 0.8212530783391994, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9498.627)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.143)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360948.5069954, 'performance_metrics': {}}}
2025-08-05 10:29:08,691 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:29:08,692 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:29:08,692 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:29:08,692 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:29:08,693 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,693 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:29:08,693 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,693 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:08,693 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:29:08,693 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:29:08,694 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:29:08,694 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:29:08,695 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:29:08,695 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:29:08,695 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,695 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,696 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 94066.0
2025-08-05 10:29:08,713 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,713 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,713 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,717 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,717 - ExploitationExpert - INFO - populations: [{'tour': array([ 9, 31,  2, 21, 64, 65, 58, 33, 15, 12, 37, 40, 39,  8, 26,  1,  6,
       13, 24, 28, 43, 23, 22,  7, 14, 11, 63,  5, 61, 41, 62, 57,  0,  4,
       42, 25, 52, 32, 53, 54, 30,  3, 29, 44, 16, 51, 10, 59, 56, 20, 18,
       35, 60, 17, 38, 50, 34, 19, 46, 55, 49, 48, 47, 45, 36, 27],
      dtype=int64), 'cur_cost': 94066.0}, {'tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12428.0}, {'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95079.0}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12876.0}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 2, 9, 61, 59, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58351.0}, {'tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 65484.0}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12319.0}, {'tour': [15, 63, 30, 4, 26, 29, 54, 32, 35, 52, 46, 36, 14, 0, 60, 57, 59, 6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18, 1, 43, 10, 40, 21, 7, 44, 37, 51, 19, 9, 39, 23, 27, 22, 49, 17, 5, 38, 2, 13, 47, 48, 50, 11, 8, 33, 20, 65, 58, 3, 31, 24, 61], 'cur_cost': 109642.0}, {'tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61184.0}, {'tour': [5, 47, 37, 41, 16, 20, 40, 17, 6, 30, 26, 59, 65, 39, 27, 38, 32, 12, 54, 4, 2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60, 49, 36, 58, 61, 3, 53, 45, 7, 56, 13, 19, 31, 34, 22, 0, 9, 15, 23, 11, 21, 1, 18, 50, 62, 42, 35, 25, 29, 8, 44, 14, 28], 'cur_cost': 98232.0}]
2025-08-05 10:29:08,718 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:29:08,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 323, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 323, 'cache_hits': 0, 'similarity_calculations': 1645, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,719 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 9, 31,  2, 21, 64, 65, 58, 33, 15, 12, 37, 40, 39,  8, 26,  1,  6,
       13, 24, 28, 43, 23, 22,  7, 14, 11, 63,  5, 61, 41, 62, 57,  0,  4,
       42, 25, 52, 32, 53, 54, 30,  3, 29, 44, 16, 51, 10, 59, 56, 20, 18,
       35, 60, 17, 38, 50, 34, 19, 46, 55, 49, 48, 47, 45, 36, 27],
      dtype=int64), 'cur_cost': 94066.0, 'intermediate_solutions': [{'tour': array([ 4, 17, 27, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 112833.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32,  4, 17, 27,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 32,  4, 17, 27, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 32,  4, 17,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 112842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27,  9, 32,  4, 17, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,720 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 94066.00)
2025-08-05 10:29:08,720 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:29:08,720 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:29:08,720 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,724 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,724 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12417.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,726 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 7, 16, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 13, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 56, 59, 62, 53, 61, 55, 10, 6, 2, 8, 4, 5, 9, 11, 1, 7, 3, 32, 29, 24, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 14, 15, 26, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14803.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,726 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 12417.00)
2025-08-05 10:29:08,726 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:29:08,726 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:29:08,727 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,734 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:08,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,736 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57185.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,736 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 10, 1, 17, 22, 25, 5, 2, 7, 9, 53, 3, 6, 54, 8, 57, 4, 19, 13, 30, 32, 35, 33, 12, 36, 48, 15, 0, 16, 24, 46, 43, 18, 34, 49, 44, 50, 21, 26, 47, 38, 14, 45, 42, 41, 37, 29, 27, 23, 28, 20, 56, 61, 65, 64, 62, 60, 52, 55, 58, 39, 51, 31, 40, 59, 63], 'cur_cost': 57185.0, 'intermediate_solutions': [{'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 14, 62, 18, 13, 63, 21, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 51, 39, 29, 0, 19, 65, 54, 1, 20, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 97211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 48, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 46], 'cur_cost': 100645.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,737 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 57185.00)
2025-08-05 10:29:08,737 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:29:08,737 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:29:08,737 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,742 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,742 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,742 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,742 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,743 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,743 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12459.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,743 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0, 'intermediate_solutions': [{'tour': [0, 17, 62, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 20, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18386.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 44, 39, 43, 48, 46, 47, 49, 40, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 21, 13, 19, 18, 16, 23, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 45, 38, 51, 50, 41, 42], 'cur_cost': 17253.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 37, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18627.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,743 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 12459.00)
2025-08-05 10:29:08,743 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:29:08,743 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:29:08,744 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,746 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,746 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,747 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,747 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,747 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12742.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,748 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 17, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0, 'intermediate_solutions': [{'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 49, 20, 7, 58, 55, 2, 9, 61, 59, 52, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 60534.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 41, 45, 48, 37, 32, 31, 1, 21, 43, 46, 49, 59, 61, 9, 2, 55, 58, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 62573.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 59, 2, 9, 61, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,748 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 12742.00)
2025-08-05 10:29:08,748 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:29:08,748 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:29:08,748 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,752 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,752 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,753 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,754 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12922.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,754 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 18, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12922.0, 'intermediate_solutions': [{'tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 24, 45, 44, 51, 46, 37, 38, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 71936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 25, 9, 65, 14, 20, 34, 36, 31, 28, 23, 6, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 63542.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 25, 9, 65, 38, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 67829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,754 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 12922.00)
2025-08-05 10:29:08,754 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:29:08,754 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:29:08,755 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,765 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-05 10:29:08,765 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,766 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,766 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,767 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,767 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51117.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,767 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [59, 53, 65, 6, 54, 61, 64, 3, 55, 58, 22, 19, 34, 31, 17, 13, 15, 35, 25, 14, 0, 23, 30, 29, 7, 63, 2, 56, 8, 11, 1, 20, 49, 39, 42, 46, 43, 40, 12, 21, 24, 32, 37, 4, 9, 60, 44, 45, 48, 51, 36, 26, 18, 28, 16, 27, 10, 62, 57, 5, 33, 50, 41, 38, 47, 52], 'cur_cost': 51117.0, 'intermediate_solutions': [{'tour': [0, 11, 14, 52, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 18, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 20093.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 46, 41, 50, 51, 38, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 20, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,768 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 51117.00)
2025-08-05 10:29:08,768 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:29:08,768 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,768 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,769 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106072.0
2025-08-05 10:29:08,779 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,779 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,780 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,782 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,782 - ExploitationExpert - INFO - populations: [{'tour': array([ 9, 31,  2, 21, 64, 65, 58, 33, 15, 12, 37, 40, 39,  8, 26,  1,  6,
       13, 24, 28, 43, 23, 22,  7, 14, 11, 63,  5, 61, 41, 62, 57,  0,  4,
       42, 25, 52, 32, 53, 54, 30,  3, 29, 44, 16, 51, 10, 59, 56, 20, 18,
       35, 60, 17, 38, 50, 34, 19, 46, 55, 49, 48, 47, 45, 36, 27],
      dtype=int64), 'cur_cost': 94066.0}, {'tour': [0, 7, 16, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [11, 10, 1, 17, 22, 25, 5, 2, 7, 9, 53, 3, 6, 54, 8, 57, 4, 19, 13, 30, 32, 35, 33, 12, 36, 48, 15, 0, 16, 24, 46, 43, 18, 34, 49, 44, 50, 21, 26, 47, 38, 14, 45, 42, 41, 37, 29, 27, 23, 28, 20, 56, 61, 65, 64, 62, 60, 52, 55, 58, 39, 51, 31, 40, 59, 63], 'cur_cost': 57185.0}, {'tour': [0, 5, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': [0, 17, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': [0, 18, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12922.0}, {'tour': [59, 53, 65, 6, 54, 61, 64, 3, 55, 58, 22, 19, 34, 31, 17, 13, 15, 35, 25, 14, 0, 23, 30, 29, 7, 63, 2, 56, 8, 11, 1, 20, 49, 39, 42, 46, 43, 40, 12, 21, 24, 32, 37, 4, 9, 60, 44, 45, 48, 51, 36, 26, 18, 28, 16, 27, 10, 62, 57, 5, 33, 50, 41, 38, 47, 52], 'cur_cost': 51117.0}, {'tour': array([39, 55, 36,  9, 63, 37, 27, 41, 28, 34, 58,  8, 57, 59, 52,  5, 30,
       12, 18, 64, 51, 53, 54, 35, 40, 11, 44, 62,  3,  1, 23, 13, 48, 25,
       38,  4, 16, 17, 56, 47, 19, 21, 26, 61, 46, 42,  0, 10, 14, 60, 49,
       32, 45, 50, 31, 33, 65, 43,  2, 29, 15,  6, 22, 24, 20,  7],
      dtype=int64), 'cur_cost': 106072.0}, {'tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61184.0}, {'tour': [5, 47, 37, 41, 16, 20, 40, 17, 6, 30, 26, 59, 65, 39, 27, 38, 32, 12, 54, 4, 2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60, 49, 36, 58, 61, 3, 53, 45, 7, 56, 13, 19, 31, 34, 22, 0, 9, 15, 23, 11, 21, 1, 18, 50, 62, 42, 35, 25, 29, 8, 44, 14, 28], 'cur_cost': 98232.0}]
2025-08-05 10:29:08,783 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:08,783 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 324, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 324, 'cache_hits': 0, 'similarity_calculations': 1656, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,784 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([39, 55, 36,  9, 63, 37, 27, 41, 28, 34, 58,  8, 57, 59, 52,  5, 30,
       12, 18, 64, 51, 53, 54, 35, 40, 11, 44, 62,  3,  1, 23, 13, 48, 25,
       38,  4, 16, 17, 56, 47, 19, 21, 26, 61, 46, 42,  0, 10, 14, 60, 49,
       32, 45, 50, 31, 33, 65, 43,  2, 29, 15,  6, 22, 24, 20,  7],
      dtype=int64), 'cur_cost': 106072.0, 'intermediate_solutions': [{'tour': array([30, 63, 15,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 109867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 30, 63, 15, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 107973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26,  4, 30, 63, 15, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 111854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15,  4, 30, 63, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 109860.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 26,  4, 30, 63, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 111827.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,784 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 106072.00)
2025-08-05 10:29:08,784 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:29:08,785 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:29:08,785 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:29:08,787 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-05 10:29:08,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,787 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,788 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:29:08,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12729.0, 路径长度: 66, 收集中间解: 3
2025-08-05 10:29:08,788 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 15, 14, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0, 'intermediate_solutions': [{'tour': [31, 33, 4, 10, 47, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 9, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 66123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 33, 4, 10, 23, 14, 8, 22, 9, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 19, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 63994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:29:08,789 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 12729.00)
2025-08-05 10:29:08,789 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:29:08,789 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:29:08,789 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:29:08,789 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104493.0
2025-08-05 10:29:08,798 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:29:08,798 - ExploitationExpert - INFO - res_population_costs: [9524, 9527, 9527.0, 9538, 9539.0]
2025-08-05 10:29:08,798 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-08-05 10:29:08,800 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:29:08,800 - ExploitationExpert - INFO - populations: [{'tour': array([ 9, 31,  2, 21, 64, 65, 58, 33, 15, 12, 37, 40, 39,  8, 26,  1,  6,
       13, 24, 28, 43, 23, 22,  7, 14, 11, 63,  5, 61, 41, 62, 57,  0,  4,
       42, 25, 52, 32, 53, 54, 30,  3, 29, 44, 16, 51, 10, 59, 56, 20, 18,
       35, 60, 17, 38, 50, 34, 19, 46, 55, 49, 48, 47, 45, 36, 27],
      dtype=int64), 'cur_cost': 94066.0}, {'tour': [0, 7, 16, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0}, {'tour': [11, 10, 1, 17, 22, 25, 5, 2, 7, 9, 53, 3, 6, 54, 8, 57, 4, 19, 13, 30, 32, 35, 33, 12, 36, 48, 15, 0, 16, 24, 46, 43, 18, 34, 49, 44, 50, 21, 26, 47, 38, 14, 45, 42, 41, 37, 29, 27, 23, 28, 20, 56, 61, 65, 64, 62, 60, 52, 55, 58, 39, 51, 31, 40, 59, 63], 'cur_cost': 57185.0}, {'tour': [0, 5, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0}, {'tour': [0, 17, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0}, {'tour': [0, 18, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12922.0}, {'tour': [59, 53, 65, 6, 54, 61, 64, 3, 55, 58, 22, 19, 34, 31, 17, 13, 15, 35, 25, 14, 0, 23, 30, 29, 7, 63, 2, 56, 8, 11, 1, 20, 49, 39, 42, 46, 43, 40, 12, 21, 24, 32, 37, 4, 9, 60, 44, 45, 48, 51, 36, 26, 18, 28, 16, 27, 10, 62, 57, 5, 33, 50, 41, 38, 47, 52], 'cur_cost': 51117.0}, {'tour': array([39, 55, 36,  9, 63, 37, 27, 41, 28, 34, 58,  8, 57, 59, 52,  5, 30,
       12, 18, 64, 51, 53, 54, 35, 40, 11, 44, 62,  3,  1, 23, 13, 48, 25,
       38,  4, 16, 17, 56, 47, 19, 21, 26, 61, 46, 42,  0, 10, 14, 60, 49,
       32, 45, 50, 31, 33, 65, 43,  2, 29, 15,  6, 22, 24, 20,  7],
      dtype=int64), 'cur_cost': 106072.0}, {'tour': [0, 15, 14, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0}, {'tour': array([ 5, 46,  7, 59, 40,  8, 22,  9,  1,  6, 36, 27, 19, 41, 63, 57, 24,
       50, 49, 54, 43, 39, 33, 62,  2,  0, 11, 32, 20, 35, 12, 44, 15, 61,
       13, 51, 34, 60, 53, 23, 56, 48, 30, 38, 14,  3, 47, 58, 45, 21, 17,
       28, 65,  4, 55, 26, 29, 52, 10, 25, 18, 31, 37, 16, 42, 64],
      dtype=int64), 'cur_cost': 104493.0}]
2025-08-05 10:29:08,801 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:29:08,801 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 325, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 325, 'cache_hits': 0, 'similarity_calculations': 1668, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:29:08,802 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([ 5, 46,  7, 59, 40,  8, 22,  9,  1,  6, 36, 27, 19, 41, 63, 57, 24,
       50, 49, 54, 43, 39, 33, 62,  2,  0, 11, 32, 20, 35, 12, 44, 15, 61,
       13, 51, 34, 60, 53, 23, 56, 48, 30, 38, 14,  3, 47, 58, 45, 21, 17,
       28, 65,  4, 55, 26, 29, 52, 10, 25, 18, 31, 37, 16, 42, 64],
      dtype=int64), 'cur_cost': 104493.0, 'intermediate_solutions': [{'tour': array([37, 47,  5, 41, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 96626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 37, 47,  5, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 41, 37, 47,  5, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 41, 37, 47, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 16, 41, 37, 47, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:29:08,802 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 104493.00)
2025-08-05 10:29:08,802 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:29:08,803 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:29:08,805 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 31,  2, 21, 64, 65, 58, 33, 15, 12, 37, 40, 39,  8, 26,  1,  6,
       13, 24, 28, 43, 23, 22,  7, 14, 11, 63,  5, 61, 41, 62, 57,  0,  4,
       42, 25, 52, 32, 53, 54, 30,  3, 29, 44, 16, 51, 10, 59, 56, 20, 18,
       35, 60, 17, 38, 50, 34, 19, 46, 55, 49, 48, 47, 45, 36, 27],
      dtype=int64), 'cur_cost': 94066.0, 'intermediate_solutions': [{'tour': array([ 4, 17, 27, 32,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 112833.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([32,  4, 17, 27,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114695.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 32,  4, 17, 27, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114386.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 32,  4, 17,  9, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 112842.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27,  9, 32,  4, 17, 41, 23, 65, 42, 62, 43, 14,  8,  3, 26, 15, 61,
       48, 60, 39, 38, 55, 24, 36, 33, 51, 57, 18, 59, 31,  2,  5, 12, 46,
       37, 11, 50, 40, 54, 10, 64, 34, 19, 53,  7, 52, 21, 44, 13,  0, 25,
       49,  1, 30, 47, 63, 28, 29, 35, 56, 16, 45,  6, 58, 22, 20]), 'cur_cost': 114422.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 16, 12, 22, 23, 13, 20, 21, 19, 18, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12417.0, 'intermediate_solutions': [{'tour': [0, 5, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 13, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14880.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 21, 14, 15, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 56, 59, 62, 53, 61, 55, 10, 6, 2, 8, 4, 5, 9, 11, 1, 7, 3, 32, 29, 24, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 18181.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 21, 14, 15, 26, 22, 12, 17, 18, 16, 23, 19, 20, 27, 37, 25, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14803.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [11, 10, 1, 17, 22, 25, 5, 2, 7, 9, 53, 3, 6, 54, 8, 57, 4, 19, 13, 30, 32, 35, 33, 12, 36, 48, 15, 0, 16, 24, 46, 43, 18, 34, 49, 44, 50, 21, 26, 47, 38, 14, 45, 42, 41, 37, 29, 27, 23, 28, 20, 56, 61, 65, 64, 62, 60, 52, 55, 58, 39, 51, 31, 40, 59, 63], 'cur_cost': 57185.0, 'intermediate_solutions': [{'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 14, 62, 18, 13, 63, 21, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 95046.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [61, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 51, 39, 29, 0, 19, 65, 54, 1, 20, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 48, 46], 'cur_cost': 97211.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [61, 48, 55, 16, 30, 35, 12, 17, 23, 22, 7, 10, 53, 24, 4, 15, 11, 56, 20, 1, 54, 65, 19, 0, 29, 39, 51, 49, 26, 32, 28, 45, 43, 52, 47, 38, 58, 25, 60, 3, 27, 57, 41, 37, 2, 36, 5, 8, 6, 40, 34, 31, 21, 62, 18, 13, 63, 14, 44, 50, 9, 59, 33, 42, 64, 46], 'cur_cost': 100645.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 20, 17, 12, 22, 23, 16, 18, 19, 13, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12459.0, 'intermediate_solutions': [{'tour': [0, 17, 62, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 20, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18386.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 44, 39, 43, 48, 46, 47, 49, 40, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37, 27, 21, 13, 19, 18, 16, 23, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 45, 38, 51, 50, 41, 42], 'cur_cost': 17253.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 17, 20, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 37, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 21, 27, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 18627.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 17, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12742.0, 'intermediate_solutions': [{'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 49, 20, 7, 58, 55, 2, 9, 61, 59, 52, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 60534.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 41, 45, 48, 37, 32, 31, 1, 21, 43, 46, 49, 59, 61, 9, 2, 55, 58, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 62573.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [44, 47, 12, 19, 28, 36, 23, 14, 8, 17, 25, 27, 4, 10, 22, 11, 54, 56, 6, 3, 15, 29, 0, 16, 26, 18, 34, 30, 5, 60, 65, 62, 52, 20, 7, 58, 55, 59, 2, 9, 61, 49, 46, 43, 21, 1, 31, 32, 37, 48, 45, 41, 50, 42, 35, 33, 39, 13, 40, 38, 24, 53, 57, 64, 63, 51], 'cur_cost': 58399.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 21, 4, 5, 8, 2, 6, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 23, 16, 19, 13, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12922.0, 'intermediate_solutions': [{'tour': [17, 16, 25, 9, 65, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 24, 45, 44, 51, 46, 37, 38, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 71936.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [17, 16, 25, 9, 65, 14, 20, 34, 36, 31, 28, 23, 6, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 38, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 63542.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [17, 16, 25, 9, 65, 38, 14, 20, 6, 23, 28, 31, 36, 34, 1, 54, 57, 5, 10, 59, 64, 55, 12, 21, 32, 40, 39, 22, 26, 13, 2, 63, 56, 47, 41, 15, 35, 4, 62, 60, 3, 18, 7, 52, 43, 19, 49, 48, 50, 30, 11, 0, 58, 45, 44, 51, 46, 37, 24, 27, 29, 8, 53, 61, 42, 33], 'cur_cost': 67829.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [59, 53, 65, 6, 54, 61, 64, 3, 55, 58, 22, 19, 34, 31, 17, 13, 15, 35, 25, 14, 0, 23, 30, 29, 7, 63, 2, 56, 8, 11, 1, 20, 49, 39, 42, 46, 43, 40, 12, 21, 24, 32, 37, 4, 9, 60, 44, 45, 48, 51, 36, 26, 18, 28, 16, 27, 10, 62, 57, 5, 33, 50, 41, 38, 47, 52], 'cur_cost': 51117.0, 'intermediate_solutions': [{'tour': [0, 11, 14, 52, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 18, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 20093.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 46, 41, 50, 51, 38, 47, 49, 40, 43, 48, 42], 'cur_cost': 12389.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 14, 18, 16, 23, 22, 12, 17, 15, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 20, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 55, 36,  9, 63, 37, 27, 41, 28, 34, 58,  8, 57, 59, 52,  5, 30,
       12, 18, 64, 51, 53, 54, 35, 40, 11, 44, 62,  3,  1, 23, 13, 48, 25,
       38,  4, 16, 17, 56, 47, 19, 21, 26, 61, 46, 42,  0, 10, 14, 60, 49,
       32, 45, 50, 31, 33, 65, 43,  2, 29, 15,  6, 22, 24, 20,  7],
      dtype=int64), 'cur_cost': 106072.0, 'intermediate_solutions': [{'tour': array([30, 63, 15,  4, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 109867.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4, 30, 63, 15, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 107973.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26,  4, 30, 63, 15, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 111854.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15,  4, 30, 63, 26, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 109860.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 26,  4, 30, 63, 29, 54, 32, 35, 52, 46, 36, 14,  0, 60, 57, 59,
        6, 42, 28, 56, 45, 53, 12, 34, 25, 64, 16, 55, 62, 41, 18,  1, 43,
       10, 40, 21,  7, 44, 37, 51, 19,  9, 39, 23, 27, 22, 49, 17,  5, 38,
        2, 13, 47, 48, 50, 11,  8, 33, 20, 65, 58,  3, 31, 24, 61]), 'cur_cost': 111827.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 14, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12729.0, 'intermediate_solutions': [{'tour': [31, 33, 4, 10, 47, 22, 8, 14, 23, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 9, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 66123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [31, 33, 4, 10, 23, 14, 8, 22, 9, 3, 60, 54, 52, 21, 19, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 61197.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [31, 33, 4, 10, 9, 22, 8, 14, 23, 3, 60, 54, 52, 21, 28, 7, 0, 12, 6, 59, 20, 1, 63, 65, 40, 15, 16, 37, 24, 36, 18, 27, 48, 45, 43, 51, 19, 38, 30, 13, 39, 50, 42, 47, 32, 17, 2, 5, 62, 55, 58, 49, 44, 35, 29, 26, 25, 11, 64, 53, 57, 56, 46, 41, 34, 61], 'cur_cost': 63994.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 46,  7, 59, 40,  8, 22,  9,  1,  6, 36, 27, 19, 41, 63, 57, 24,
       50, 49, 54, 43, 39, 33, 62,  2,  0, 11, 32, 20, 35, 12, 44, 15, 61,
       13, 51, 34, 60, 53, 23, 56, 48, 30, 38, 14,  3, 47, 58, 45, 21, 17,
       28, 65,  4, 55, 26, 29, 52, 10, 25, 18, 31, 37, 16, 42, 64],
      dtype=int64), 'cur_cost': 104493.0, 'intermediate_solutions': [{'tour': array([37, 47,  5, 41, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 96626.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([41, 37, 47,  5, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98208.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([16, 41, 37, 47,  5, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98628.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5, 41, 37, 47, 16, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98218.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 16, 41, 37, 47, 20, 40, 17,  6, 30, 26, 59, 65, 39, 27, 38, 32,
       12, 54,  4,  2, 10, 55, 52, 57, 33, 24, 51, 64, 48, 63, 46, 43, 60,
       49, 36, 58, 61,  3, 53, 45,  7, 56, 13, 19, 31, 34, 22,  0,  9, 15,
       23, 11, 21,  1, 18, 50, 62, 42, 35, 25, 29,  8, 44, 14, 28]), 'cur_cost': 98283.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:29:08,805 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:29:08,805 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:29:08,809 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12417.000, 多样性=0.926
2025-08-05 10:29:08,809 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:29:08,809 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:29:08,809 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:29:08,810 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.10781120432115567, 'best_improvement': -0.007955191168114295}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.048788927335640116}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.02138930913712181, 'recent_improvements': [0.014980738895897544, -0.17531102821400432, 0.05775935717014117], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 5, 'new_count': 5, 'count_change': 0, 'old_best_cost': 9524, 'new_best_cost': 9524, 'quality_improvement': 0.0, 'old_diversity': 0.8015151515151515, 'new_diversity': 0.8015151515151515, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:29:08,810 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:29:08,812 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-05 10:29:08,813 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250805_102908.solution
2025-08-05 10:29:08,813 - __main__ - INFO - 实例执行完成 - 运行时间: 1.79s, 最佳成本: 9524
2025-08-05 10:29:08,813 - __main__ - INFO - 实例 composite13_66 处理完成
