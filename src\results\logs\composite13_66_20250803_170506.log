2025-08-03 17:05:06,699 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 17:05:06,699 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 17:05:06,702 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:06,713 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9923.000, 多样性=0.970
2025-08-03 17:05:06,719 - PathExpert - INFO - 开始路径结构分析
2025-08-03 17:05:06,725 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.970
2025-08-03 17:05:06,728 - EliteExpert - INFO - 开始精英解分析
2025-08-03 17:05:06,730 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 17:05:06,730 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 17:05:06,730 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 17:05:06,730 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 17:05:06,968 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -3917.330, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 17:05:06,968 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 17:05:06,969 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 17:05:07,044 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 17:05:07,329 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_170507.html
2025-08-03 17:05:07,443 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_170507.html
2025-08-03 17:05:07,443 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 17:05:07,443 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 17:05:07,443 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7132秒
2025-08-03 17:05:07,443 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 17:05:07,443 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3917.3300000000004, 'local_optima_density': 0.2, 'gradient_variance': 2583084023.1651, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.8599865470109875, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3917.330)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754211906.9682932, 'performance_metrics': {}}}
2025-08-03 17:05:07,443 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 17:05:07,443 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 17:05:07,444 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 17:05:07,444 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:07,444 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 17:05:07,444 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:07,444 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:07,444 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 17:05:07,444 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:05:07,444 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:05:07,445 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 17:05:07,445 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 3, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 17:05:07,445 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 17:05:07,445 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 17:05:07,445 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:07,461 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:07,462 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:07,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58603.0, 路径长度: 66
2025-08-03 17:05:07,599 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}
2025-08-03 17:05:07,599 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 58603.00)
2025-08-03 17:05:07,599 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 17:05:07,599 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:07,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:07,601 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 112218.0
2025-08-03 17:05:09,260 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 17:05:09,260 - ExploitationExpert - INFO - res_population_costs: [9867.0]
2025-08-03 17:05:09,260 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:05:09,261 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:09,261 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': array([41, 38, 51, 50, 45, 44, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9923.0}, {'tour': array([37, 25, 26, 36, 27, 31, 33, 28, 30, 35, 34, 32, 29, 24, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9936.0}, {'tour': array([ 1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 10,  0, 55, 61, 53, 62, 59,
       56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23,
       13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10333.0}, {'tour': array([14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([60, 51, 17, 58, 22, 36, 14, 11, 25, 21, 39,  3, 53, 20,  0,  1, 26,
       34, 43,  6, 19, 32, 54, 64,  5, 29, 52, 38, 31, 18, 15, 16,  4, 33,
       13, 63, 44, 41, 42, 61, 48,  2, 56,  7, 24, 62, 65, 10, 57, 37,  9,
       46, 50, 40, 28, 45, 30, 35, 49, 23, 12, 47, 27,  8, 55, 59],
      dtype=int64), 'cur_cost': 97844.0}, {'tour': array([36, 34, 37, 12, 10, 41,  5, 28, 49, 24,  7, 26, 64, 27, 52,  4, 17,
       39, 29,  9, 33, 35, 46, 16, 47, 20,  0,  6,  8,  1, 50, 21, 30, 51,
       53, 25, 31, 32, 23, 57, 18, 58,  3, 42, 19, 13, 61, 38, 63, 14, 60,
       40, 45, 11, 22, 55, 44, 48, 54,  2, 43, 59, 62, 65, 15, 56],
      dtype=int64), 'cur_cost': 108087.0}, {'tour': array([27, 14, 63, 32, 12, 37,  0, 30, 43, 35, 26, 54, 28,  4, 24, 34, 48,
        6, 23, 13, 52, 21, 59, 49, 22, 39, 56, 20, 57, 11, 25,  1, 40, 18,
       45, 51, 36, 19, 64, 53, 31,  9,  8, 61, 62, 10, 50, 58, 65, 55,  5,
       42, 15, 60, 47, 33,  2, 41, 16, 17,  3, 38,  7, 46, 29, 44],
      dtype=int64), 'cur_cost': 111830.0}, {'tour': array([34, 30, 11, 35, 41, 46,  9, 28, 45, 39, 21, 36,  5, 19, 50, 52, 26,
       65, 23, 49, 33,  2,  3, 62, 44,  4, 53,  8, 63, 37, 13, 29, 42, 31,
       10, 22, 14, 59, 32, 40, 17, 61, 55, 25, 58, 38, 27, 16, 64,  6, 15,
       60, 24,  7, 57,  0, 54,  1, 43, 12, 48, 51, 56, 20, 18, 47],
      dtype=int64), 'cur_cost': 109999.0}, {'tour': array([39,  3, 62, 31, 41, 64, 52, 20,  6,  0, 54, 19, 12, 35,  7, 47, 32,
       38, 33, 44, 26, 46, 17,  5, 28, 34, 42, 65, 24, 37,  2, 21, 48, 61,
       45, 11, 56, 57, 23, 10, 30, 53, 55, 63, 13, 18,  8, 36,  4, 27, 29,
       22, 49, 40,  1, 59, 25, 51, 50, 14, 58, 15, 16, 60,  9, 43],
      dtype=int64), 'cur_cost': 102943.0}, {'tour': array([34,  8, 29, 33, 24, 41, 22, 44,  9, 15, 28, 53, 19,  5, 21, 20, 50,
       57, 65, 63, 10, 36,  1, 51,  6, 35,  4, 49, 38, 17,  0, 52, 27, 58,
       64, 47, 37, 32, 40, 45, 25, 54, 42, 62, 61, 59, 13, 48, 16,  3, 18,
       43, 30, 60, 46, 12,  2, 31, 14,  7, 11, 26, 23, 56, 55, 39],
      dtype=int64), 'cur_cost': 103708.0}, {'tour': array([54, 14, 49, 64, 34, 20, 42, 41, 47, 44, 27, 59, 37, 36, 57, 33, 29,
        0, 12, 13, 56, 58, 60, 51,  7, 21, 38, 23, 39, 18, 46,  3, 35, 10,
        1, 55, 25,  2, 43, 22, 11, 19, 16, 15, 32, 31,  4, 63, 17, 48, 26,
       65, 24, 50, 30, 52, 53, 45,  8, 40, 28, 61,  5,  6,  9, 62],
      dtype=int64), 'cur_cost': 101795.0}, {'tour': array([34, 43, 44, 49, 35, 58, 30, 16, 22, 63,  9, 33, 59, 39,  0, 46, 36,
       54, 52, 10, 12, 24, 14,  8, 29, 60, 47, 28, 45, 27, 51,  1,  6, 57,
       38, 11, 17, 18, 64, 25, 31, 42, 65, 23, 19, 40, 56,  2, 61,  4, 13,
       37, 26,  5, 20, 48, 21, 50, 55, 53, 15, 41,  3,  7, 62, 32],
      dtype=int64), 'cur_cost': 106442.0}, {'tour': array([23, 45, 39, 44, 17, 11, 29, 30, 65, 19, 57, 49, 18, 63, 27,  7, 56,
        9,  5, 42, 37,  2, 59, 13, 24, 62, 50, 55,  0, 48, 60, 12,  3,  8,
       32, 54, 40,  4, 53, 61, 38,  6, 58, 14, 15,  1, 26, 36, 25, 41, 64,
       47, 34, 28, 46, 33, 21, 20, 35, 43, 31, 22, 10, 16, 51, 52],
      dtype=int64), 'cur_cost': 108937.0}, {'tour': array([33, 58,  6, 49,  7, 23, 20, 12, 22, 13, 50, 65,  2, 51, 11, 19, 21,
        0, 41,  1, 18, 29, 34, 46, 35, 60, 15, 48,  8, 56, 53, 57, 14, 24,
       44, 25, 61,  5, 17, 36, 62, 43, 54, 38, 26, 40,  9, 47, 16,  4,  3,
       39, 28, 64, 55, 27, 59, 37, 63, 10, 31, 32, 52, 30, 42, 45],
      dtype=int64), 'cur_cost': 114516.0}, {'tour': array([45, 51, 62, 42,  6, 57,  9, 35, 12, 13, 15, 25, 39, 63, 22, 48, 10,
       52, 61, 38,  5, 37, 54, 28, 18, 21, 49, 58, 29, 32, 11, 64, 26, 27,
       17, 23, 65, 16, 46,  0, 24, 43, 59, 41,  2, 31, 55, 33,  8, 56, 34,
       14, 20,  1, 30, 44, 47, 50,  4, 19, 36, 40,  7, 53,  3, 60],
      dtype=int64), 'cur_cost': 111809.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:09,268 - ExploitationExpert - INFO - 局部搜索耗时: 1.67秒
2025-08-03 17:05:09,268 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 17:05:09,268 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}
2025-08-03 17:05:09,268 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 112218.00)
2025-08-03 17:05:09,268 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 17:05:09,268 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 17:05:09,268 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:09,283 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:09,284 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:09,284 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65388.0, 路径长度: 66
2025-08-03 17:05:09,284 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}
2025-08-03 17:05:09,284 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 65388.00)
2025-08-03 17:05:09,284 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 17:05:09,284 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 17:05:09,285 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:09,297 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:09,298 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:09,298 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55471.0, 路径长度: 66
2025-08-03 17:05:09,298 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}
2025-08-03 17:05:09,298 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 55471.00)
2025-08-03 17:05:09,298 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 17:05:09,298 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:09,298 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:09,299 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 105957.0
2025-08-03 17:05:11,066 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 17:05:11,066 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0]
2025-08-03 17:05:11,066 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:11,067 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,067 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': array([14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9945.0}, {'tour': array([60, 51, 17, 58, 22, 36, 14, 11, 25, 21, 39,  3, 53, 20,  0,  1, 26,
       34, 43,  6, 19, 32, 54, 64,  5, 29, 52, 38, 31, 18, 15, 16,  4, 33,
       13, 63, 44, 41, 42, 61, 48,  2, 56,  7, 24, 62, 65, 10, 57, 37,  9,
       46, 50, 40, 28, 45, 30, 35, 49, 23, 12, 47, 27,  8, 55, 59],
      dtype=int64), 'cur_cost': 97844.0}, {'tour': array([36, 34, 37, 12, 10, 41,  5, 28, 49, 24,  7, 26, 64, 27, 52,  4, 17,
       39, 29,  9, 33, 35, 46, 16, 47, 20,  0,  6,  8,  1, 50, 21, 30, 51,
       53, 25, 31, 32, 23, 57, 18, 58,  3, 42, 19, 13, 61, 38, 63, 14, 60,
       40, 45, 11, 22, 55, 44, 48, 54,  2, 43, 59, 62, 65, 15, 56],
      dtype=int64), 'cur_cost': 108087.0}, {'tour': array([27, 14, 63, 32, 12, 37,  0, 30, 43, 35, 26, 54, 28,  4, 24, 34, 48,
        6, 23, 13, 52, 21, 59, 49, 22, 39, 56, 20, 57, 11, 25,  1, 40, 18,
       45, 51, 36, 19, 64, 53, 31,  9,  8, 61, 62, 10, 50, 58, 65, 55,  5,
       42, 15, 60, 47, 33,  2, 41, 16, 17,  3, 38,  7, 46, 29, 44],
      dtype=int64), 'cur_cost': 111830.0}, {'tour': array([34, 30, 11, 35, 41, 46,  9, 28, 45, 39, 21, 36,  5, 19, 50, 52, 26,
       65, 23, 49, 33,  2,  3, 62, 44,  4, 53,  8, 63, 37, 13, 29, 42, 31,
       10, 22, 14, 59, 32, 40, 17, 61, 55, 25, 58, 38, 27, 16, 64,  6, 15,
       60, 24,  7, 57,  0, 54,  1, 43, 12, 48, 51, 56, 20, 18, 47],
      dtype=int64), 'cur_cost': 109999.0}, {'tour': array([39,  3, 62, 31, 41, 64, 52, 20,  6,  0, 54, 19, 12, 35,  7, 47, 32,
       38, 33, 44, 26, 46, 17,  5, 28, 34, 42, 65, 24, 37,  2, 21, 48, 61,
       45, 11, 56, 57, 23, 10, 30, 53, 55, 63, 13, 18,  8, 36,  4, 27, 29,
       22, 49, 40,  1, 59, 25, 51, 50, 14, 58, 15, 16, 60,  9, 43],
      dtype=int64), 'cur_cost': 102943.0}, {'tour': array([34,  8, 29, 33, 24, 41, 22, 44,  9, 15, 28, 53, 19,  5, 21, 20, 50,
       57, 65, 63, 10, 36,  1, 51,  6, 35,  4, 49, 38, 17,  0, 52, 27, 58,
       64, 47, 37, 32, 40, 45, 25, 54, 42, 62, 61, 59, 13, 48, 16,  3, 18,
       43, 30, 60, 46, 12,  2, 31, 14,  7, 11, 26, 23, 56, 55, 39],
      dtype=int64), 'cur_cost': 103708.0}, {'tour': array([54, 14, 49, 64, 34, 20, 42, 41, 47, 44, 27, 59, 37, 36, 57, 33, 29,
        0, 12, 13, 56, 58, 60, 51,  7, 21, 38, 23, 39, 18, 46,  3, 35, 10,
        1, 55, 25,  2, 43, 22, 11, 19, 16, 15, 32, 31,  4, 63, 17, 48, 26,
       65, 24, 50, 30, 52, 53, 45,  8, 40, 28, 61,  5,  6,  9, 62],
      dtype=int64), 'cur_cost': 101795.0}, {'tour': array([34, 43, 44, 49, 35, 58, 30, 16, 22, 63,  9, 33, 59, 39,  0, 46, 36,
       54, 52, 10, 12, 24, 14,  8, 29, 60, 47, 28, 45, 27, 51,  1,  6, 57,
       38, 11, 17, 18, 64, 25, 31, 42, 65, 23, 19, 40, 56,  2, 61,  4, 13,
       37, 26,  5, 20, 48, 21, 50, 55, 53, 15, 41,  3,  7, 62, 32],
      dtype=int64), 'cur_cost': 106442.0}, {'tour': array([23, 45, 39, 44, 17, 11, 29, 30, 65, 19, 57, 49, 18, 63, 27,  7, 56,
        9,  5, 42, 37,  2, 59, 13, 24, 62, 50, 55,  0, 48, 60, 12,  3,  8,
       32, 54, 40,  4, 53, 61, 38,  6, 58, 14, 15,  1, 26, 36, 25, 41, 64,
       47, 34, 28, 46, 33, 21, 20, 35, 43, 31, 22, 10, 16, 51, 52],
      dtype=int64), 'cur_cost': 108937.0}, {'tour': array([33, 58,  6, 49,  7, 23, 20, 12, 22, 13, 50, 65,  2, 51, 11, 19, 21,
        0, 41,  1, 18, 29, 34, 46, 35, 60, 15, 48,  8, 56, 53, 57, 14, 24,
       44, 25, 61,  5, 17, 36, 62, 43, 54, 38, 26, 40,  9, 47, 16,  4,  3,
       39, 28, 64, 55, 27, 59, 37, 63, 10, 31, 32, 52, 30, 42, 45],
      dtype=int64), 'cur_cost': 114516.0}, {'tour': array([45, 51, 62, 42,  6, 57,  9, 35, 12, 13, 15, 25, 39, 63, 22, 48, 10,
       52, 61, 38,  5, 37, 54, 28, 18, 21, 49, 58, 29, 32, 11, 64, 26, 27,
       17, 23, 65, 16, 46,  0, 24, 43, 59, 41,  2, 31, 55, 33,  8, 56, 34,
       14, 20,  1, 30, 44, 47, 50,  4, 19, 36, 40,  7, 53,  3, 60],
      dtype=int64), 'cur_cost': 111809.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:11,074 - ExploitationExpert - INFO - 局部搜索耗时: 1.77秒
2025-08-03 17:05:11,074 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 17:05:11,074 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}
2025-08-03 17:05:11,074 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 105957.00)
2025-08-03 17:05:11,074 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 17:05:11,074 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 17:05:11,074 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,079 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:11,079 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,079 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12752.0, 路径长度: 66
2025-08-03 17:05:11,080 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}
2025-08-03 17:05:11,080 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12752.00)
2025-08-03 17:05:11,080 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 17:05:11,080 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 17:05:11,080 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,096 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:11,096 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,097 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64415.0, 路径长度: 66
2025-08-03 17:05:11,097 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}
2025-08-03 17:05:11,097 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 64415.00)
2025-08-03 17:05:11,097 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 17:05:11,097 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:11,097 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:11,097 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111420.0
2025-08-03 17:05:11,535 - ExploitationExpert - INFO - res_population_num: 8
2025-08-03 17:05:11,536 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0, 9550.0, 9542, 9542, 9527, 9521, 9521]
2025-08-03 17:05:11,536 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-08-03 17:05:11,538 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,538 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}, {'tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}, {'tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}, {'tour': array([27, 14, 63, 32, 12, 37,  0, 30, 43, 35, 26, 54, 28,  4, 24, 34, 48,
        6, 23, 13, 52, 21, 59, 49, 22, 39, 56, 20, 57, 11, 25,  1, 40, 18,
       45, 51, 36, 19, 64, 53, 31,  9,  8, 61, 62, 10, 50, 58, 65, 55,  5,
       42, 15, 60, 47, 33,  2, 41, 16, 17,  3, 38,  7, 46, 29, 44],
      dtype=int64), 'cur_cost': 111830.0}, {'tour': array([34, 30, 11, 35, 41, 46,  9, 28, 45, 39, 21, 36,  5, 19, 50, 52, 26,
       65, 23, 49, 33,  2,  3, 62, 44,  4, 53,  8, 63, 37, 13, 29, 42, 31,
       10, 22, 14, 59, 32, 40, 17, 61, 55, 25, 58, 38, 27, 16, 64,  6, 15,
       60, 24,  7, 57,  0, 54,  1, 43, 12, 48, 51, 56, 20, 18, 47],
      dtype=int64), 'cur_cost': 109999.0}, {'tour': array([39,  3, 62, 31, 41, 64, 52, 20,  6,  0, 54, 19, 12, 35,  7, 47, 32,
       38, 33, 44, 26, 46, 17,  5, 28, 34, 42, 65, 24, 37,  2, 21, 48, 61,
       45, 11, 56, 57, 23, 10, 30, 53, 55, 63, 13, 18,  8, 36,  4, 27, 29,
       22, 49, 40,  1, 59, 25, 51, 50, 14, 58, 15, 16, 60,  9, 43],
      dtype=int64), 'cur_cost': 102943.0}, {'tour': array([34,  8, 29, 33, 24, 41, 22, 44,  9, 15, 28, 53, 19,  5, 21, 20, 50,
       57, 65, 63, 10, 36,  1, 51,  6, 35,  4, 49, 38, 17,  0, 52, 27, 58,
       64, 47, 37, 32, 40, 45, 25, 54, 42, 62, 61, 59, 13, 48, 16,  3, 18,
       43, 30, 60, 46, 12,  2, 31, 14,  7, 11, 26, 23, 56, 55, 39],
      dtype=int64), 'cur_cost': 103708.0}, {'tour': array([54, 14, 49, 64, 34, 20, 42, 41, 47, 44, 27, 59, 37, 36, 57, 33, 29,
        0, 12, 13, 56, 58, 60, 51,  7, 21, 38, 23, 39, 18, 46,  3, 35, 10,
        1, 55, 25,  2, 43, 22, 11, 19, 16, 15, 32, 31,  4, 63, 17, 48, 26,
       65, 24, 50, 30, 52, 53, 45,  8, 40, 28, 61,  5,  6,  9, 62],
      dtype=int64), 'cur_cost': 101795.0}, {'tour': array([34, 43, 44, 49, 35, 58, 30, 16, 22, 63,  9, 33, 59, 39,  0, 46, 36,
       54, 52, 10, 12, 24, 14,  8, 29, 60, 47, 28, 45, 27, 51,  1,  6, 57,
       38, 11, 17, 18, 64, 25, 31, 42, 65, 23, 19, 40, 56,  2, 61,  4, 13,
       37, 26,  5, 20, 48, 21, 50, 55, 53, 15, 41,  3,  7, 62, 32],
      dtype=int64), 'cur_cost': 106442.0}, {'tour': array([23, 45, 39, 44, 17, 11, 29, 30, 65, 19, 57, 49, 18, 63, 27,  7, 56,
        9,  5, 42, 37,  2, 59, 13, 24, 62, 50, 55,  0, 48, 60, 12,  3,  8,
       32, 54, 40,  4, 53, 61, 38,  6, 58, 14, 15,  1, 26, 36, 25, 41, 64,
       47, 34, 28, 46, 33, 21, 20, 35, 43, 31, 22, 10, 16, 51, 52],
      dtype=int64), 'cur_cost': 108937.0}, {'tour': array([33, 58,  6, 49,  7, 23, 20, 12, 22, 13, 50, 65,  2, 51, 11, 19, 21,
        0, 41,  1, 18, 29, 34, 46, 35, 60, 15, 48,  8, 56, 53, 57, 14, 24,
       44, 25, 61,  5, 17, 36, 62, 43, 54, 38, 26, 40,  9, 47, 16,  4,  3,
       39, 28, 64, 55, 27, 59, 37, 63, 10, 31, 32, 52, 30, 42, 45],
      dtype=int64), 'cur_cost': 114516.0}, {'tour': array([45, 51, 62, 42,  6, 57,  9, 35, 12, 13, 15, 25, 39, 63, 22, 48, 10,
       52, 61, 38,  5, 37, 54, 28, 18, 21, 49, 58, 29, 32, 11, 64, 26, 27,
       17, 23, 65, 16, 46,  0, 24, 43, 59, 41,  2, 31, 55, 33,  8, 56, 34,
       14, 20,  1, 30, 44, 47, 50,  4, 19, 36, 40,  7, 53,  3, 60],
      dtype=int64), 'cur_cost': 111809.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:11,544 - ExploitationExpert - INFO - 局部搜索耗时: 0.45秒
2025-08-03 17:05:11,545 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 17:05:11,545 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}
2025-08-03 17:05:11,545 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 111420.00)
2025-08-03 17:05:11,546 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 17:05:11,546 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 17:05:11,546 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,562 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:11,562 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59193.0, 路径长度: 66
2025-08-03 17:05:11,562 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}
2025-08-03 17:05:11,563 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 59193.00)
2025-08-03 17:05:11,563 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 17:05:11,563 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 17:05:11,563 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,567 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:11,567 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,567 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12898.0, 路径长度: 66
2025-08-03 17:05:11,567 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}
2025-08-03 17:05:11,567 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12898.00)
2025-08-03 17:05:11,568 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 17:05:11,568 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:11,568 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:11,568 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 101357.0
2025-08-03 17:05:11,639 - ExploitationExpert - INFO - res_population_num: 9
2025-08-03 17:05:11,639 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0, 9550.0, 9542, 9542, 9527, 9521, 9521, 9521]
2025-08-03 17:05:11,639 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:11,643 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,643 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}, {'tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}, {'tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}, {'tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}, {'tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}, {'tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}, {'tour': array([34,  8, 29, 33, 24, 41, 22, 44,  9, 15, 28, 53, 19,  5, 21, 20, 50,
       57, 65, 63, 10, 36,  1, 51,  6, 35,  4, 49, 38, 17,  0, 52, 27, 58,
       64, 47, 37, 32, 40, 45, 25, 54, 42, 62, 61, 59, 13, 48, 16,  3, 18,
       43, 30, 60, 46, 12,  2, 31, 14,  7, 11, 26, 23, 56, 55, 39],
      dtype=int64), 'cur_cost': 103708.0}, {'tour': array([54, 14, 49, 64, 34, 20, 42, 41, 47, 44, 27, 59, 37, 36, 57, 33, 29,
        0, 12, 13, 56, 58, 60, 51,  7, 21, 38, 23, 39, 18, 46,  3, 35, 10,
        1, 55, 25,  2, 43, 22, 11, 19, 16, 15, 32, 31,  4, 63, 17, 48, 26,
       65, 24, 50, 30, 52, 53, 45,  8, 40, 28, 61,  5,  6,  9, 62],
      dtype=int64), 'cur_cost': 101795.0}, {'tour': array([34, 43, 44, 49, 35, 58, 30, 16, 22, 63,  9, 33, 59, 39,  0, 46, 36,
       54, 52, 10, 12, 24, 14,  8, 29, 60, 47, 28, 45, 27, 51,  1,  6, 57,
       38, 11, 17, 18, 64, 25, 31, 42, 65, 23, 19, 40, 56,  2, 61,  4, 13,
       37, 26,  5, 20, 48, 21, 50, 55, 53, 15, 41,  3,  7, 62, 32],
      dtype=int64), 'cur_cost': 106442.0}, {'tour': array([23, 45, 39, 44, 17, 11, 29, 30, 65, 19, 57, 49, 18, 63, 27,  7, 56,
        9,  5, 42, 37,  2, 59, 13, 24, 62, 50, 55,  0, 48, 60, 12,  3,  8,
       32, 54, 40,  4, 53, 61, 38,  6, 58, 14, 15,  1, 26, 36, 25, 41, 64,
       47, 34, 28, 46, 33, 21, 20, 35, 43, 31, 22, 10, 16, 51, 52],
      dtype=int64), 'cur_cost': 108937.0}, {'tour': array([33, 58,  6, 49,  7, 23, 20, 12, 22, 13, 50, 65,  2, 51, 11, 19, 21,
        0, 41,  1, 18, 29, 34, 46, 35, 60, 15, 48,  8, 56, 53, 57, 14, 24,
       44, 25, 61,  5, 17, 36, 62, 43, 54, 38, 26, 40,  9, 47, 16,  4,  3,
       39, 28, 64, 55, 27, 59, 37, 63, 10, 31, 32, 52, 30, 42, 45],
      dtype=int64), 'cur_cost': 114516.0}, {'tour': array([45, 51, 62, 42,  6, 57,  9, 35, 12, 13, 15, 25, 39, 63, 22, 48, 10,
       52, 61, 38,  5, 37, 54, 28, 18, 21, 49, 58, 29, 32, 11, 64, 26, 27,
       17, 23, 65, 16, 46,  0, 24, 43, 59, 41,  2, 31, 55, 33,  8, 56, 34,
       14, 20,  1, 30, 44, 47, 50,  4, 19, 36, 40,  7, 53,  3, 60],
      dtype=int64), 'cur_cost': 111809.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:11,649 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:11,650 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 17:05:11,650 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}
2025-08-03 17:05:11,650 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 101357.00)
2025-08-03 17:05:11,650 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 17:05:11,650 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 17:05:11,650 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,654 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:11,654 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98025.0, 路径长度: 66
2025-08-03 17:05:11,654 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [58, 10, 13, 7, 18, 6, 4, 57, 32, 24, 17, 64, 55, 61, 21, 12, 9, 16, 63, 40, 34, 37, 1, 52, 49, 0, 8, 44, 38, 46, 42, 51, 60, 41, 11, 43, 36, 54, 65, 20, 39, 53, 23, 22, 5, 19, 45, 62, 31, 2, 50, 15, 30, 14, 35, 25, 27, 33, 56, 29, 28, 26, 3, 48, 59, 47], 'cur_cost': 98025.0}
2025-08-03 17:05:11,655 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 98025.00)
2025-08-03 17:05:11,655 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 17:05:11,655 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 17:05:11,655 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,659 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:11,659 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,659 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12885.0, 路径长度: 66
2025-08-03 17:05:11,659 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [0, 4, 13, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}
2025-08-03 17:05:11,660 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 12885.00)
2025-08-03 17:05:11,660 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 17:05:11,660 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:11,660 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:11,660 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 121173.0
2025-08-03 17:05:11,729 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:05:11,729 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0, 9550.0, 9542, 9542, 9527, 9521, 9521, 9521, 9521, 9521]
2025-08-03 17:05:11,729 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:11,733 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,733 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}, {'tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}, {'tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}, {'tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}, {'tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}, {'tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}, {'tour': [58, 10, 13, 7, 18, 6, 4, 57, 32, 24, 17, 64, 55, 61, 21, 12, 9, 16, 63, 40, 34, 37, 1, 52, 49, 0, 8, 44, 38, 46, 42, 51, 60, 41, 11, 43, 36, 54, 65, 20, 39, 53, 23, 22, 5, 19, 45, 62, 31, 2, 50, 15, 30, 14, 35, 25, 27, 33, 56, 29, 28, 26, 3, 48, 59, 47], 'cur_cost': 98025.0}, {'tour': [0, 4, 13, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': array([47, 17, 26, 21, 40, 36, 50, 65, 19,  0, 45, 29, 61, 48, 27, 10, 15,
       55, 18, 54,  8, 31, 25, 64, 52, 32, 51, 14,  4, 23, 13, 16, 53, 22,
        7, 20, 56, 44, 37, 38, 62, 35, 30, 46, 28,  5, 49,  1, 41, 34,  6,
       33, 43,  3, 60, 39, 11, 63, 58,  2, 12, 57, 42,  9, 59, 24],
      dtype=int64), 'cur_cost': 121173.0}, {'tour': array([23, 45, 39, 44, 17, 11, 29, 30, 65, 19, 57, 49, 18, 63, 27,  7, 56,
        9,  5, 42, 37,  2, 59, 13, 24, 62, 50, 55,  0, 48, 60, 12,  3,  8,
       32, 54, 40,  4, 53, 61, 38,  6, 58, 14, 15,  1, 26, 36, 25, 41, 64,
       47, 34, 28, 46, 33, 21, 20, 35, 43, 31, 22, 10, 16, 51, 52],
      dtype=int64), 'cur_cost': 108937.0}, {'tour': array([33, 58,  6, 49,  7, 23, 20, 12, 22, 13, 50, 65,  2, 51, 11, 19, 21,
        0, 41,  1, 18, 29, 34, 46, 35, 60, 15, 48,  8, 56, 53, 57, 14, 24,
       44, 25, 61,  5, 17, 36, 62, 43, 54, 38, 26, 40,  9, 47, 16,  4,  3,
       39, 28, 64, 55, 27, 59, 37, 63, 10, 31, 32, 52, 30, 42, 45],
      dtype=int64), 'cur_cost': 114516.0}, {'tour': array([45, 51, 62, 42,  6, 57,  9, 35, 12, 13, 15, 25, 39, 63, 22, 48, 10,
       52, 61, 38,  5, 37, 54, 28, 18, 21, 49, 58, 29, 32, 11, 64, 26, 27,
       17, 23, 65, 16, 46,  0, 24, 43, 59, 41,  2, 31, 55, 33,  8, 56, 34,
       14, 20,  1, 30, 44, 47, 50,  4, 19, 36, 40,  7, 53,  3, 60],
      dtype=int64), 'cur_cost': 111809.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:11,737 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:11,737 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 17:05:11,737 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([47, 17, 26, 21, 40, 36, 50, 65, 19,  0, 45, 29, 61, 48, 27, 10, 15,
       55, 18, 54,  8, 31, 25, 64, 52, 32, 51, 14,  4, 23, 13, 16, 53, 22,
        7, 20, 56, 44, 37, 38, 62, 35, 30, 46, 28,  5, 49,  1, 41, 34,  6,
       33, 43,  3, 60, 39, 11, 63, 58,  2, 12, 57, 42,  9, 59, 24],
      dtype=int64), 'cur_cost': 121173.0}
2025-08-03 17:05:11,737 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 121173.00)
2025-08-03 17:05:11,737 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 17:05:11,737 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 17:05:11,738 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,751 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:05:11,751 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,751 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65149.0, 路径长度: 66
2025-08-03 17:05:11,751 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [41, 23, 32, 29, 15, 33, 27, 22, 36, 21, 14, 0, 56, 1, 3, 55, 63, 10, 5, 6, 8, 18, 34, 7, 13, 17, 26, 2, 58, 57, 65, 20, 30, 4, 31, 47, 43, 12, 25, 37, 42, 19, 39, 35, 49, 51, 48, 24, 11, 52, 54, 61, 16, 45, 44, 28, 46, 50, 60, 64, 53, 59, 62, 9, 40, 38], 'cur_cost': 65149.0}
2025-08-03 17:05:11,752 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 65149.00)
2025-08-03 17:05:11,752 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 17:05:11,752 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 17:05:11,752 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,755 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:11,756 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,756 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12838.0, 路径长度: 66
2025-08-03 17:05:11,756 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [0, 10, 17, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12838.0}
2025-08-03 17:05:11,756 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 12838.00)
2025-08-03 17:05:11,756 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 17:05:11,756 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:11,756 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:11,757 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 111043.0
2025-08-03 17:05:11,828 - ExploitationExpert - INFO - res_population_num: 12
2025-08-03 17:05:11,828 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0, 9550.0, 9542, 9542, 9527, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 17:05:11,829 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:11,833 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,833 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}, {'tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}, {'tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}, {'tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}, {'tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}, {'tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}, {'tour': [58, 10, 13, 7, 18, 6, 4, 57, 32, 24, 17, 64, 55, 61, 21, 12, 9, 16, 63, 40, 34, 37, 1, 52, 49, 0, 8, 44, 38, 46, 42, 51, 60, 41, 11, 43, 36, 54, 65, 20, 39, 53, 23, 22, 5, 19, 45, 62, 31, 2, 50, 15, 30, 14, 35, 25, 27, 33, 56, 29, 28, 26, 3, 48, 59, 47], 'cur_cost': 98025.0}, {'tour': [0, 4, 13, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': array([47, 17, 26, 21, 40, 36, 50, 65, 19,  0, 45, 29, 61, 48, 27, 10, 15,
       55, 18, 54,  8, 31, 25, 64, 52, 32, 51, 14,  4, 23, 13, 16, 53, 22,
        7, 20, 56, 44, 37, 38, 62, 35, 30, 46, 28,  5, 49,  1, 41, 34,  6,
       33, 43,  3, 60, 39, 11, 63, 58,  2, 12, 57, 42,  9, 59, 24],
      dtype=int64), 'cur_cost': 121173.0}, {'tour': [41, 23, 32, 29, 15, 33, 27, 22, 36, 21, 14, 0, 56, 1, 3, 55, 63, 10, 5, 6, 8, 18, 34, 7, 13, 17, 26, 2, 58, 57, 65, 20, 30, 4, 31, 47, 43, 12, 25, 37, 42, 19, 39, 35, 49, 51, 48, 24, 11, 52, 54, 61, 16, 45, 44, 28, 46, 50, 60, 64, 53, 59, 62, 9, 40, 38], 'cur_cost': 65149.0}, {'tour': [0, 10, 17, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12838.0}, {'tour': array([28, 25, 17, 41, 18, 55, 52, 16, 27, 64, 26, 35, 53,  8, 43, 33, 20,
        3,  4, 39, 58, 34, 51, 63, 37, 21, 40, 11, 56, 31, 32, 24, 50,  2,
        5, 42, 61, 12, 23, 62, 47, 14,  0, 10, 60, 54,  9, 13, 19, 59, 46,
       57, 44, 30, 22, 45,  6, 49, 29,  1, 36, 65, 38, 48, 15,  7],
      dtype=int64), 'cur_cost': 111043.0}, {'tour': array([ 0, 14, 51,  5, 27, 50, 60, 53, 54, 25, 45,  1,  9, 40, 43, 29, 44,
        3, 55, 10, 22, 48, 41, 19, 64, 13, 61, 39, 31, 42, 58, 21, 34, 17,
       47, 30, 65,  6, 16,  8, 38, 18,  2, 52, 33, 35,  7, 37, 26, 63, 56,
       46, 23, 28, 59, 24, 49, 57, 36, 62, 32, 11, 15,  4, 12, 20],
      dtype=int64), 'cur_cost': 113695.0}, {'tour': array([12, 20, 54, 11, 23, 26, 13, 63, 44, 30,  8, 59, 34, 24, 21, 33, 57,
        3, 49, 62, 48, 10,  1, 47, 52, 61,  4, 31, 17,  5, 64,  6, 19,  9,
       39, 40, 18, 53, 15, 46, 28, 36, 56, 35, 25, 14, 45, 29, 50,  0, 16,
       65, 42, 22,  2, 41, 58, 37, 51,  7, 60, 38, 55, 43, 27, 32],
      dtype=int64), 'cur_cost': 113910.0}, {'tour': array([54, 24, 34, 51, 23, 55, 57, 46, 45, 14, 37, 41, 53, 26, 38, 29, 22,
       35, 30, 50, 27, 25, 31, 65, 64, 61,  7, 18,  0, 63, 47,  4, 11, 56,
        2,  5,  1, 15, 49, 44, 48, 32, 52, 39, 40,  9, 21, 20, 36, 42,  3,
       16, 59,  6, 58, 60, 62, 10, 43, 33, 17, 12, 13, 28,  8, 19],
      dtype=int64), 'cur_cost': 87290.0}]
2025-08-03 17:05:11,836 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-08-03 17:05:11,836 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 17:05:11,836 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([28, 25, 17, 41, 18, 55, 52, 16, 27, 64, 26, 35, 53,  8, 43, 33, 20,
        3,  4, 39, 58, 34, 51, 63, 37, 21, 40, 11, 56, 31, 32, 24, 50,  2,
        5, 42, 61, 12, 23, 62, 47, 14,  0, 10, 60, 54,  9, 13, 19, 59, 46,
       57, 44, 30, 22, 45,  6, 49, 29,  1, 36, 65, 38, 48, 15,  7],
      dtype=int64), 'cur_cost': 111043.0}
2025-08-03 17:05:11,836 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 111043.00)
2025-08-03 17:05:11,837 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 17:05:11,837 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 17:05:11,837 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,840 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:05:11,840 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,840 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109656.0, 路径长度: 66
2025-08-03 17:05:11,840 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [48, 30, 16, 3, 31, 57, 23, 64, 33, 7, 61, 15, 24, 27, 62, 12, 43, 21, 34, 41, 49, 11, 65, 44, 39, 6, 63, 46, 42, 2, 9, 47, 53, 32, 36, 10, 25, 14, 54, 1, 59, 55, 13, 51, 37, 5, 8, 26, 22, 35, 60, 50, 17, 52, 58, 18, 4, 45, 29, 40, 19, 38, 0, 56, 28, 20], 'cur_cost': 109656.0}
2025-08-03 17:05:11,841 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 109656.00)
2025-08-03 17:05:11,841 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 17:05:11,841 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 17:05:11,841 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:05:11,845 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:05:11,845 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:05:11,845 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12361.0, 路径长度: 66
2025-08-03 17:05:11,845 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [0, 4, 1, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12361.0}
2025-08-03 17:05:11,846 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 12361.00)
2025-08-03 17:05:11,846 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 17:05:11,846 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:05:11,846 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:05:11,846 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 110642.0
2025-08-03 17:05:11,923 - ExploitationExpert - INFO - res_population_num: 12
2025-08-03 17:05:11,924 - ExploitationExpert - INFO - res_population_costs: [9867.0, 9551.0, 9550.0, 9542, 9542, 9527, 9521, 9521, 9521, 9521, 9521, 9521]
2025-08-03 17:05:11,924 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:05:11,929 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:05:11,929 - ExploitationExpert - INFO - populations: [{'tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}, {'tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}, {'tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}, {'tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}, {'tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}, {'tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}, {'tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}, {'tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}, {'tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}, {'tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}, {'tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}, {'tour': [58, 10, 13, 7, 18, 6, 4, 57, 32, 24, 17, 64, 55, 61, 21, 12, 9, 16, 63, 40, 34, 37, 1, 52, 49, 0, 8, 44, 38, 46, 42, 51, 60, 41, 11, 43, 36, 54, 65, 20, 39, 53, 23, 22, 5, 19, 45, 62, 31, 2, 50, 15, 30, 14, 35, 25, 27, 33, 56, 29, 28, 26, 3, 48, 59, 47], 'cur_cost': 98025.0}, {'tour': [0, 4, 13, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}, {'tour': array([47, 17, 26, 21, 40, 36, 50, 65, 19,  0, 45, 29, 61, 48, 27, 10, 15,
       55, 18, 54,  8, 31, 25, 64, 52, 32, 51, 14,  4, 23, 13, 16, 53, 22,
        7, 20, 56, 44, 37, 38, 62, 35, 30, 46, 28,  5, 49,  1, 41, 34,  6,
       33, 43,  3, 60, 39, 11, 63, 58,  2, 12, 57, 42,  9, 59, 24],
      dtype=int64), 'cur_cost': 121173.0}, {'tour': [41, 23, 32, 29, 15, 33, 27, 22, 36, 21, 14, 0, 56, 1, 3, 55, 63, 10, 5, 6, 8, 18, 34, 7, 13, 17, 26, 2, 58, 57, 65, 20, 30, 4, 31, 47, 43, 12, 25, 37, 42, 19, 39, 35, 49, 51, 48, 24, 11, 52, 54, 61, 16, 45, 44, 28, 46, 50, 60, 64, 53, 59, 62, 9, 40, 38], 'cur_cost': 65149.0}, {'tour': [0, 10, 17, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12838.0}, {'tour': array([28, 25, 17, 41, 18, 55, 52, 16, 27, 64, 26, 35, 53,  8, 43, 33, 20,
        3,  4, 39, 58, 34, 51, 63, 37, 21, 40, 11, 56, 31, 32, 24, 50,  2,
        5, 42, 61, 12, 23, 62, 47, 14,  0, 10, 60, 54,  9, 13, 19, 59, 46,
       57, 44, 30, 22, 45,  6, 49, 29,  1, 36, 65, 38, 48, 15,  7],
      dtype=int64), 'cur_cost': 111043.0}, {'tour': [48, 30, 16, 3, 31, 57, 23, 64, 33, 7, 61, 15, 24, 27, 62, 12, 43, 21, 34, 41, 49, 11, 65, 44, 39, 6, 63, 46, 42, 2, 9, 47, 53, 32, 36, 10, 25, 14, 54, 1, 59, 55, 13, 51, 37, 5, 8, 26, 22, 35, 60, 50, 17, 52, 58, 18, 4, 45, 29, 40, 19, 38, 0, 56, 28, 20], 'cur_cost': 109656.0}, {'tour': [0, 4, 1, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12361.0}, {'tour': array([50, 24, 42, 53, 38,  8, 19, 56, 32, 30, 51, 33, 36, 34, 14, 40, 18,
       27,  5, 25,  9, 43, 58, 11, 21,  6, 12, 62,  2, 23, 45, 22, 64, 54,
       26, 29, 63, 47, 16, 15, 55, 35,  1, 28,  3, 10, 48, 57, 41,  0, 20,
       13, 31, 46, 61, 39, 17,  4, 59, 60,  7, 44, 65, 52, 37, 49],
      dtype=int64), 'cur_cost': 110642.0}]
2025-08-03 17:05:11,932 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 17:05:11,932 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 17:05:11,932 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([50, 24, 42, 53, 38,  8, 19, 56, 32, 30, 51, 33, 36, 34, 14, 40, 18,
       27,  5, 25,  9, 43, 58, 11, 21,  6, 12, 62,  2, 23, 45, 22, 64, 54,
       26, 29, 63, 47, 16, 15, 55, 35,  1, 28,  3, 10, 48, 57, 41,  0, 20,
       13, 31, 46, 61, 39, 17,  4, 59, 60,  7, 44, 65, 52, 37, 49],
      dtype=int64), 'cur_cost': 110642.0}
2025-08-03 17:05:11,932 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 110642.00)
2025-08-03 17:05:11,932 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 17:05:11,932 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 17:05:11,933 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [62, 60, 13, 25, 36, 14, 3, 4, 57, 5, 7, 23, 28, 18, 33, 22, 20, 2, 64, 6, 27, 8, 55, 65, 61, 19, 35, 12, 16, 21, 31, 29, 37, 1, 52, 9, 0, 11, 34, 43, 47, 48, 45, 44, 15, 39, 17, 49, 42, 46, 26, 30, 50, 40, 41, 51, 10, 58, 56, 63, 53, 59, 38, 24, 32, 54], 'cur_cost': 58603.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 33, 44,  3, 28, 64,  1, 58,  7, 38, 51, 32, 12, 19, 41, 27, 45,
       62, 36, 42, 17,  5, 49, 18,  6, 20, 65, 13, 39,  2, 46, 61, 47, 23,
       35, 43,  4, 29, 26, 50,  0, 59, 57, 16, 56, 48, 15, 24, 31, 37, 55,
       22, 63, 14,  8, 54, 25, 52, 60, 30, 11,  9, 10, 40, 53, 21],
      dtype=int64), 'cur_cost': 112218.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [58, 10, 59, 65, 6, 0, 64, 20, 18, 32, 25, 22, 28, 1, 63, 7, 52, 61, 21, 12, 23, 35, 24, 8, 27, 37, 16, 13, 43, 40, 34, 17, 33, 15, 26, 49, 47, 19, 29, 14, 5, 3, 2, 4, 31, 39, 38, 46, 41, 45, 9, 60, 56, 53, 11, 57, 48, 50, 51, 42, 36, 44, 55, 62, 54, 30], 'cur_cost': 65388.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [48, 49, 38, 42, 18, 16, 34, 31, 30, 27, 24, 33, 19, 13, 12, 20, 28, 15, 32, 7, 10, 2, 53, 8, 59, 0, 62, 14, 40, 45, 51, 37, 4, 5, 61, 1, 9, 57, 60, 11, 23, 43, 41, 46, 25, 6, 63, 17, 26, 21, 3, 52, 55, 58, 56, 54, 64, 47, 22, 35, 29, 36, 39, 44, 50, 65], 'cur_cost': 55471.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([51, 35, 28, 14, 58,  9, 41, 23, 61, 15, 57, 59, 43, 12, 39, 62, 16,
       22, 40, 63, 27,  0, 38, 65, 30,  1, 64, 24, 21, 37, 42, 55, 50, 47,
       29, 56, 46, 17, 60, 11, 33, 52,  3, 32, 26, 36, 44, 18, 20, 45, 53,
       54,  2,  4, 49, 25, 31,  5, 13, 19, 34, 48, 10,  7,  6,  8],
      dtype=int64), 'cur_cost': 105957.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 3, 8, 2, 6, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12752.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [26, 3, 11, 14, 28, 20, 24, 7, 9, 1, 52, 55, 64, 57, 10, 16, 27, 6, 56, 15, 13, 2, 59, 17, 18, 40, 50, 38, 48, 34, 4, 58, 21, 5, 53, 54, 23, 25, 8, 61, 65, 44, 45, 12, 22, 32, 31, 30, 29, 49, 46, 43, 47, 19, 37, 36, 33, 0, 63, 62, 41, 51, 39, 42, 35, 60], 'cur_cost': 64415.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([50,  6, 41,  3, 62, 22, 36, 51, 56, 54, 42, 20, 53, 57,  1, 63,  4,
       27, 65, 11, 26, 10, 31, 39, 46,  9, 23,  7, 37, 60, 24, 28,  8, 12,
       61, 16, 64, 30, 48, 17, 15, 55, 25, 59, 33, 49, 43, 21,  2, 40, 14,
       52,  5, 19, 35, 32, 38, 29, 58,  0, 44, 13, 18, 47, 45, 34],
      dtype=int64), 'cur_cost': 111420.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [54, 14, 24, 30, 34, 23, 36, 18, 26, 21, 15, 17, 22, 2, 63, 59, 61, 65, 13, 37, 20, 11, 9, 5, 55, 6, 52, 10, 12, 7, 58, 19, 31, 40, 16, 43, 48, 39, 44, 28, 33, 27, 4, 1, 25, 35, 3, 29, 46, 51, 50, 45, 41, 38, 32, 49, 56, 60, 62, 53, 57, 64, 0, 8, 47, 42], 'cur_cost': 59193.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 16, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12898.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 13, 10, 28,  2, 34, 59, 15,  5, 23, 53, 42, 26,  9, 39, 51, 50,
       44, 55, 58,  0,  3, 54, 37, 41, 24, 47, 45, 57, 17,  8, 32, 46, 31,
       36, 14, 40, 65, 33, 49, 22, 63,  4, 20, 38, 64, 29, 16, 48, 19, 12,
       21, 52, 60, 62, 18, 43, 30,  1, 56, 61, 35,  7,  6, 25, 27],
      dtype=int64), 'cur_cost': 101357.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [58, 10, 13, 7, 18, 6, 4, 57, 32, 24, 17, 64, 55, 61, 21, 12, 9, 16, 63, 40, 34, 37, 1, 52, 49, 0, 8, 44, 38, 46, 42, 51, 60, 41, 11, 43, 36, 54, 65, 20, 39, 53, 23, 22, 5, 19, 45, 62, 31, 2, 50, 15, 30, 14, 35, 25, 27, 33, 56, 29, 28, 26, 3, 48, 59, 47], 'cur_cost': 98025.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 13, 7, 3, 9, 11, 1, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 19, 21, 20, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12885.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([47, 17, 26, 21, 40, 36, 50, 65, 19,  0, 45, 29, 61, 48, 27, 10, 15,
       55, 18, 54,  8, 31, 25, 64, 52, 32, 51, 14,  4, 23, 13, 16, 53, 22,
        7, 20, 56, 44, 37, 38, 62, 35, 30, 46, 28,  5, 49,  1, 41, 34,  6,
       33, 43,  3, 60, 39, 11, 63, 58,  2, 12, 57, 42,  9, 59, 24],
      dtype=int64), 'cur_cost': 121173.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [41, 23, 32, 29, 15, 33, 27, 22, 36, 21, 14, 0, 56, 1, 3, 55, 63, 10, 5, 6, 8, 18, 34, 7, 13, 17, 26, 2, 58, 57, 65, 20, 30, 4, 31, 47, 43, 12, 25, 37, 42, 19, 39, 35, 49, 51, 48, 24, 11, 52, 54, 61, 16, 45, 44, 28, 46, 50, 60, 64, 53, 59, 62, 9, 40, 38], 'cur_cost': 65149.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 17, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12838.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 25, 17, 41, 18, 55, 52, 16, 27, 64, 26, 35, 53,  8, 43, 33, 20,
        3,  4, 39, 58, 34, 51, 63, 37, 21, 40, 11, 56, 31, 32, 24, 50,  2,
        5, 42, 61, 12, 23, 62, 47, 14,  0, 10, 60, 54,  9, 13, 19, 59, 46,
       57, 44, 30, 22, 45,  6, 49, 29,  1, 36, 65, 38, 48, 15,  7],
      dtype=int64), 'cur_cost': 111043.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [48, 30, 16, 3, 31, 57, 23, 64, 33, 7, 61, 15, 24, 27, 62, 12, 43, 21, 34, 41, 49, 11, 65, 44, 39, 6, 63, 46, 42, 2, 9, 47, 53, 32, 36, 10, 25, 14, 54, 1, 59, 55, 13, 51, 37, 5, 8, 26, 22, 35, 60, 50, 17, 52, 58, 18, 4, 45, 29, 40, 19, 38, 0, 56, 28, 20], 'cur_cost': 109656.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 1, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12361.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([50, 24, 42, 53, 38,  8, 19, 56, 32, 30, 51, 33, 36, 34, 14, 40, 18,
       27,  5, 25,  9, 43, 58, 11, 21,  6, 12, 62,  2, 23, 45, 22, 64, 54,
       26, 29, 63, 47, 16, 15, 55, 35,  1, 28,  3, 10, 48, 57, 41,  0, 20,
       13, 31, 46, 61, 39, 17,  4, 59, 60,  7, 44, 65, 52, 37, 49],
      dtype=int64), 'cur_cost': 110642.0}}]
2025-08-03 17:05:11,934 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 17:05:11,934 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:05:11,944 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=12361.000, 多样性=0.965
2025-08-03 17:05:11,944 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 17:05:11,944 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 17:05:11,944 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 17:05:11,946 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.07126201468076672, 'best_improvement': -0.24569182706842688}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.005669679539852165}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 12, 'new_count': 12, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8218549127640037, 'new_diversity': 0.8218549127640037, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 17:05:11,949 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 17:05:11,956 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 17:05:11,956 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_170511.solution
2025-08-03 17:05:11,956 - __main__ - INFO - 实例 composite13_66 处理完成
