# -*- coding: utf-8 -*-
"""
策略选择专家模块

包含StrategyExpert类，负责为每个个体分配最适合的策略。
"""

import numpy as np
from typing import List, Dict, Any, Optional, Union
from experts.base.expert_base import ExpertBase
from experts.prompts.experts_prompt import STRATEGY_PROMPT, generate_strategy_expert_prompt, parse_expert_response


class DynamicStrategyCalculator:
    """动态策略比例计算器"""

    @staticmethod
    def calculate_dynamic_explore_ratio(iteration: int, total_iterations: int,
                                      landscape_features: Dict[str, Any],
                                      performance_history: List[float]) -> float:
        """动态计算探索/利用比例"""
        # 基础比例随迭代递减
        base_ratio = 0.8 - (iteration / total_iterations) * 0.5  # 0.8 -> 0.3

        # 根据景观特征调整
        diversity_bonus = 0.0
        if 'diversity' in landscape_features:
            diversity_val = landscape_features['diversity'].get('diversity_index', 0.5)
            if diversity_val > 0.9:
                diversity_bonus = 0.1  # 高多样性时增加探索
            elif diversity_val < 0.3:
                diversity_bonus = -0.1  # 低多样性时减少探索

        # 根据性能历史调整
        performance_penalty = 0.0
        if len(performance_history) >= 3:
            recent_improvements = performance_history[-3:]
            if all(imp < 0 for imp in recent_improvements):  # 连续恶化
                performance_penalty = 0.15  # 增加探索跳出局部最优

        # 根据局部最优密度调整
        optima_adjustment = 0.0
        if 'local_optima' in landscape_features:
            optima_density = landscape_features['local_optima'].get('local_optima_density', 0.5)
            if optima_density > 0.3:
                optima_adjustment = 0.1  # 高密度时增加探索

        # 计算最终比例
        final_ratio = base_ratio + diversity_bonus + performance_penalty + optima_adjustment
        return max(0.2, min(0.8, final_ratio))  # 限制在[0.2, 0.8]范围内


class IntelligentAssignmentCalculator:
    """智能个体策略分配计算器"""

    @staticmethod
    def assign_individual_strategies(populations: List[Dict[str, Any]], explore_ratio: float,
                                   landscape_features: Dict[str, Any]) -> List[str]:
        """基于个体特征和适应度的智能策略分配"""
        n_individuals = len(populations)
        n_explore = int(n_individuals * explore_ratio)

        # 计算个体适应度排名
        fitness_ranks = IntelligentAssignmentCalculator._calculate_fitness_ranks(populations)

        # 计算个体多样性贡献
        diversity_contributions = IntelligentAssignmentCalculator._calculate_diversity_contributions(populations)

        assignments = []
        for i in range(n_individuals):
            # 综合评分：适应度排名 + 多样性贡献
            fitness_score = (n_individuals - fitness_ranks[i]) / n_individuals
            diversity_score = diversity_contributions[i]

            # 根据景观特征调整权重
            diversity_val = 0.5
            if 'diversity' in landscape_features:
                # 适配新的景观特征结构
                diversity_feature = landscape_features['diversity']
                if isinstance(diversity_feature, dict):
                    diversity_val = diversity_feature.get('diversity_index', 0.5)
                else:
                    diversity_val = diversity_feature

            if diversity_val < 0.5:
                # 低多样性时优先保护多样性贡献高的个体
                combined_score = 0.3 * fitness_score + 0.7 * diversity_score
            else:
                # 高多样性时平衡考虑
                combined_score = 0.6 * fitness_score + 0.4 * diversity_score

            assignments.append((i, combined_score))

        # 按综合评分排序，前n_explore个分配探索策略
        assignments.sort(key=lambda x: x[1], reverse=True)

        strategy_assignments = ['exploit'] * n_individuals
        for i in range(n_explore):
            individual_idx = assignments[i][0]
            strategy_assignments[individual_idx] = 'explore'

        return strategy_assignments

    @staticmethod
    def _calculate_fitness_ranks(populations: List[Dict[str, Any]]) -> List[int]:
        """计算个体适应度排名"""
        fitness_values = []
        for ind in populations:
            if isinstance(ind, dict):
                fitness = ind.get('cur_cost', float('inf'))
            else:
                fitness = getattr(ind, 'cur_cost', float('inf'))

            # 确保数值类型
            try:
                fitness = float(fitness)
            except (ValueError, TypeError):
                fitness = float('inf')
            fitness_values.append(fitness)

        # 排名（适应度越小排名越高）
        sorted_indices = sorted(range(len(fitness_values)), key=lambda i: fitness_values[i])
        ranks = [0] * len(fitness_values)
        for rank, idx in enumerate(sorted_indices):
            ranks[idx] = rank

        return ranks

    @staticmethod
    def _calculate_diversity_contributions(populations: List[Dict[str, Any]]) -> List[float]:
        """计算个体多样性贡献"""
        n = len(populations)
        if n < 2:
            return [1.0] * n

        # 提取路径信息
        paths = []
        for ind in populations:
            if isinstance(ind, dict):
                path = ind.get('tour', [])
            else:
                path = getattr(ind, 'tour', [])
            paths.append(path)

        # 计算每个个体与其他个体的平均距离
        diversity_scores = []
        for i in range(n):
            total_distance = 0
            for j in range(n):
                if i != j:
                    distance = IntelligentAssignmentCalculator._hamming_distance(paths[i], paths[j])
                    total_distance += distance

            avg_distance = total_distance / (n - 1) if n > 1 else 0
            diversity_scores.append(avg_distance)

        # 归一化到[0, 1]
        if max(diversity_scores) > 0:
            diversity_scores = [score / max(diversity_scores) for score in diversity_scores]

        return diversity_scores

    @staticmethod
    def _hamming_distance(path1: List[int], path2: List[int]) -> int:
        """计算汉明距离"""
        if len(path1) != len(path2):
            return max(len(path1), len(path2))

        distance = 0
        for i in range(len(path1)):
            if path1[i] != path2[i]:
                distance += 1
        return distance


class StrategyExpert(ExpertBase):
    """策略选择专家，为每个个体分配最适合的策略"""

    def __init__(self, interface_llm, total_iterations: int = 10):
        super().__init__()
        self.interface_llm = interface_llm
        self.total_iterations = total_iterations
        self.performance_history = []
        # Use the optimized strategy prompt template from experts_prompt.py
        self.prompt_template = STRATEGY_PROMPT

        # 初始化计算器
        self.dynamic_calculator = DynamicStrategyCalculator()
        self.assignment_calculator = IntelligentAssignmentCalculator()

        # 景观特征集成增强 - 新增属性
        self.landscape_weights = {}
        self.complexity_threshold = {'low': 0.3, 'high': 0.7}
        self.landscape_features_used = 0  # 跟踪使用的景观特征数量

    def calculate_landscape_complexity(self, landscape_features):
        """计算景观复杂度指数"""
        ruggedness = landscape_features.get('ruggedness', 0)
        gradient_strength = abs(landscape_features.get('gradient_strength', 0))
        coverage = landscape_features.get('coverage', 0)

        # 归一化梯度强度
        normalized_gradient = min(gradient_strength / 20000, 1.0)

        # 计算复杂度指数
        complexity = (
            ruggedness * 0.4 +
            normalized_gradient * 0.3 +
            (1 - min(coverage * 20, 1.0)) * 0.3
        )

        return complexity

    def adaptive_exploration_ratio(self, landscape_features, iteration_progress):
        """基于景观特征的自适应探索比例"""
        complexity = self.calculate_landscape_complexity(landscape_features)

        # 基础探索比例随迭代递减
        base_ratio = 0.8 * (1 - iteration_progress * 0.4)

        # 复杂度调整
        if complexity > self.complexity_threshold['high']:
            adjustment = 0.15  # 高复杂度增加探索
        elif complexity < self.complexity_threshold['low']:
            adjustment = -0.1  # 低复杂度减少探索
        else:
            adjustment = 0.05  # 中等复杂度轻微增加

        return max(0.2, min(0.9, base_ratio + adjustment))

    def intelligent_strategy_allocation(self, population_data, landscape_report=None):
        """增强的智能策略分配"""
        if landscape_report:
            landscape_features = landscape_report.get('search_space_features', {})
            iteration_info = landscape_report.get('iteration_info', {})

            # 计算自适应探索比例
            iteration_progress = iteration_info.get('progress', 0)
            exploration_ratio = self.adaptive_exploration_ratio(
                landscape_features, iteration_progress)

            # 记录景观特征使用情况
            self.landscape_features_used = len([
                f for f in ['ruggedness', 'gradient_strength', 'coverage']
                if f in landscape_features
            ])

            self.logger.info(f"景观特征考虑: {self.landscape_features_used}项特征")
            self.logger.info(f"景观复杂度: {self.calculate_landscape_complexity(landscape_features):.3f}")
        else:
            exploration_ratio = self.calculate_dynamic_exploration_ratio(population_data)
            self.landscape_features_used = 0

        # 执行策略分配逻辑
        if landscape_report:
            strategy_assignments = self.assignment_calculator.assign_individual_strategies(
                population_data, exploration_ratio, landscape_features)
        else:
            strategy_assignments = self.assignment_calculator.assign_individual_strategies(
                population_data, exploration_ratio, {})

        return strategy_assignments

    def calculate_dynamic_exploration_ratio(self, population_data):
        """回退方法：基于种群数据计算动态探索比例"""
        # 简单的基于种群大小的探索比例
        return max(0.3, min(0.8, 0.7 - len(population_data) * 0.01))

    def analyze(self, landscape_report, populations, iteration, strategy_feedback=None):
        """基于景观分析，为每个个体分配策略"""
        self.logger.info("开始策略分配分析")

        try:
            # 1. 使用增强的智能策略分配
            landscape_features = self._extract_landscape_features(landscape_report)

            # 添加迭代信息到景观报告
            enhanced_landscape_report = landscape_report.copy() if landscape_report else {}
            enhanced_landscape_report['iteration_info'] = {
                'current': iteration,
                'total': self.total_iterations,
                'progress': iteration / max(self.total_iterations, 1)
            }
            enhanced_landscape_report['search_space_features'] = landscape_features

            # 使用新的智能策略分配方法
            strategy_selection = self.intelligent_strategy_allocation(
                population_data=populations,
                landscape_report=enhanced_landscape_report
            )

            self.logger.info(f"智能策略分配完成: {strategy_selection}")

            # 2. 计算探索比例用于报告
            iteration_progress = iteration / max(self.total_iterations, 1)
            explore_ratio = self.adaptive_exploration_ratio(landscape_features, iteration_progress)

            self.logger.info(f"自适应探索比例: {explore_ratio:.3f}")

            # 3. 生成策略分配报告
            strategy_response = self._generate_strategy_response(
                strategy_selection, explore_ratio, landscape_features
            )

            # 4. 更新性能历史（如果有适应度信息）
            self._update_performance_history(populations)

            return strategy_selection, strategy_response

        except Exception as e:
            self.logger.error(f"策略分配过程中出错: {str(e)}")
            # 回退到简单的动态比例分配
            return self._fallback_strategy_assignment(populations, iteration)

    def _extract_landscape_features(self, landscape_report: Dict[str, Any]) -> Dict[str, Any]:
        """从景观报告中提取特征"""
        features = {}

        if not landscape_report:
            return features

        # 提取多样性特征
        if 'diversity_preservation' in landscape_report:
            features['diversity'] = landscape_report['diversity_preservation']

        # 提取局部最优特征
        if 'local_optima_density' in landscape_report:
            features['ruggedness'] = landscape_report['local_optima_density']

        # 提取梯度强度特征
        if 'gradient_strength' in landscape_report:
            features['gradient_strength'] = landscape_report['gradient_strength']

        # 提取覆盖率特征
        if 'coverage' in landscape_report:
            features['coverage'] = landscape_report['coverage']

        # 提取收敛特征
        if 'convergence_trend' in landscape_report:
            features['convergence_trend'] = landscape_report['convergence_trend']

        # 提取进化阶段
        if 'evolution_phase' in landscape_report:
            features['evolution_phase'] = landscape_report['evolution_phase']

        return features

    def _generate_strategy_response(self, strategy_selection: List[str],
                                  explore_ratio: float,
                                  landscape_features: Dict[str, Any]) -> str:
        """生成策略分配响应"""
        explore_count = strategy_selection.count('explore')
        exploit_count = strategy_selection.count('exploit')

        response = f"""策略分配结果：
- 动态探索比例: {explore_ratio:.3f}
- 探索个体数量: {explore_count}
- 利用个体数量: {exploit_count}
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: {len(landscape_features)}项特征"""

        return response

    def _update_performance_history(self, populations: List[Dict[str, Any]]):
        """更新性能历史"""
        if not populations:
            return

        # 计算当前代的最佳适应度
        best_fitness = float('inf')
        for ind in populations:
            if isinstance(ind, dict):
                fitness = ind.get('cur_cost', float('inf'))
            else:
                fitness = getattr(ind, 'cur_cost', float('inf'))

            try:
                fitness = float(fitness)
                best_fitness = min(best_fitness, fitness)
            except (ValueError, TypeError):
                continue

        # 计算改进量
        if len(self.performance_history) > 0:
            improvement = self.performance_history[-1] - best_fitness
            self.performance_history.append(improvement)
        else:
            self.performance_history.append(0.0)

        # 保持历史长度
        if len(self.performance_history) > 10:
            self.performance_history.pop(0)

    def _fallback_strategy_assignment(self, populations: List[Dict[str, Any]],
                                    iteration: int) -> tuple:
        """回退策略分配"""
        self.logger.warning("使用回退策略分配")

        # 简单的动态比例
        base_ratio = 0.8 - (iteration / self.total_iterations) * 0.5
        explore_ratio = max(0.2, min(0.8, base_ratio))

        n_explore = int(len(populations) * explore_ratio)
        strategy_selection = ['explore'] * n_explore + ['exploit'] * (len(populations) - n_explore)

        strategy_response = f"回退策略分配 - 探索比例: {explore_ratio:.3f}"

        return strategy_selection, strategy_response
    
    def generate_report(self, analysis_result):
        """生成策略分配报告"""
        strategy_selection, strategy_response = analysis_result
        return {
            "strategy_selection": strategy_selection,
            "strategy_response": strategy_response
        }
