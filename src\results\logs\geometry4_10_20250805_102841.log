2025-08-05 10:28:41,580 - __main__ - INFO - geometry4_10 开始进化第 1 代
2025-08-05 10:28:41,580 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:41,581 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,582 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=73.000, 多样性=0.900
2025-08-05 10:28:41,583 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:41,583 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.900
2025-08-05 10:28:41,612 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:41,614 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:41,614 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:41,614 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:41,614 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:41,619 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.300, 适应度梯度: -3.120, 聚类评分: 0.000, 覆盖率: 0.054, 收敛趋势: 0.000, 多样性: 0.900
2025-08-05 10:28:41,620 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:41,620 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:41,620 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry4_10
2025-08-05 10:28:41,628 - visualization.landscape_visualizer - INFO - 插值约束: 271 个点被约束到最小值 73.00
2025-08-05 10:28:41,630 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.2%, 梯度: 3.04 → 2.85
2025-08-05 10:28:41,737 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\landscape_geometry4_10_iter_46_20250805_102841.html
2025-08-05 10:28:41,802 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\dashboard_geometry4_10_iter_46_20250805_102841.html
2025-08-05 10:28:41,803 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 46
2025-08-05 10:28:41,803 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:41,803 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1883秒
2025-08-05 10:28:41,803 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 92, 'max_size': 500, 'hits': 0, 'misses': 92, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 313, 'misses': 150, 'hit_rate': 0.6760259179265659, 'evictions': 50, 'ttl': 7200}}
2025-08-05 10:28:41,804 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -3.1200000000000006, 'local_optima_density': 0.3, 'gradient_variance': 876.6096, 'cluster_count': 0}, 'population_state': {'diversity': 0.9, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0542, 'fitness_entropy': 0.9674887648835618, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -3.120)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.054)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 0.900)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360921.620784, 'performance_metrics': {}}}
2025-08-05 10:28:41,804 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:41,804 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:41,805 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:41,805 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:41,805 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,805 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:41,806 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,806 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,806 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:41,806 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:41,806 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:41,807 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:41,807 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:41,807 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:41,808 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:41,808 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,809 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,809 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,809 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,809 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,810 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 90.00)
2025-08-05 10:28:41,810 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:41,810 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:41,810 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,811 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,811 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,812 - ExplorationExpert - INFO - 探索路径生成完成，成本: 84.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,812 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [4, 9, 5, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 84.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,812 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 84.00)
2025-08-05 10:28:41,812 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:41,813 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:41,813 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,813 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:41,814 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,814 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,814 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 3, 2, 6, 1, 9, 5, 8, 4], 'cur_cost': 95.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,814 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 95.00)
2025-08-05 10:28:41,814 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:41,815 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,815 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,815 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 118.0
2025-08-05 10:28:41,826 - ExploitationExpert - INFO - res_population_num: 3
2025-08-05 10:28:41,827 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72]
2025-08-05 10:28:41,827 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64)]
2025-08-05 10:28:41,828 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,828 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0}, {'tour': [4, 9, 5, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 84.0}, {'tour': [0, 7, 3, 2, 6, 1, 9, 5, 8, 4], 'cur_cost': 95.0}, {'tour': array([9, 7, 2, 8, 4, 0, 3, 5, 1, 6], dtype=int64), 'cur_cost': 118.0}, {'tour': array([0, 3, 7, 1, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 133.0}, {'tour': array([0, 7, 6, 9, 3, 2, 8, 5, 4, 1], dtype=int64), 'cur_cost': 115.0}, {'tour': array([7, 1, 5, 4, 6, 9, 3, 8, 0, 2], dtype=int64), 'cur_cost': 118.0}, {'tour': array([8, 9, 7, 2, 5, 3, 4, 0, 6, 1], dtype=int64), 'cur_cost': 114.0}, {'tour': array([2, 0, 6, 8, 4, 7, 5, 1, 9, 3], dtype=int64), 'cur_cost': 123.0}, {'tour': array([5, 6, 0, 7, 3, 9, 4, 8, 2, 1], dtype=int64), 'cur_cost': 112.0}]
2025-08-05 10:28:41,830 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:41,830 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 118, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 118, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,831 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([9, 7, 2, 8, 4, 0, 3, 5, 1, 6], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([5, 1, 8, 6, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 1, 8, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 5, 1, 8, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 5, 1, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 9, 6, 5, 1, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,832 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 118.00)
2025-08-05 10:28:41,832 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:41,832 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:41,832 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:41,832 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 138.0
2025-08-05 10:28:41,844 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:41,844 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:41,845 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:41,846 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:41,846 - ExploitationExpert - INFO - populations: [{'tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0}, {'tour': [4, 9, 5, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 84.0}, {'tour': [0, 7, 3, 2, 6, 1, 9, 5, 8, 4], 'cur_cost': 95.0}, {'tour': array([9, 7, 2, 8, 4, 0, 3, 5, 1, 6], dtype=int64), 'cur_cost': 118.0}, {'tour': array([0, 8, 1, 5, 9, 2, 4, 7, 6, 3], dtype=int64), 'cur_cost': 138.0}, {'tour': array([0, 7, 6, 9, 3, 2, 8, 5, 4, 1], dtype=int64), 'cur_cost': 115.0}, {'tour': array([7, 1, 5, 4, 6, 9, 3, 8, 0, 2], dtype=int64), 'cur_cost': 118.0}, {'tour': array([8, 9, 7, 2, 5, 3, 4, 0, 6, 1], dtype=int64), 'cur_cost': 114.0}, {'tour': array([2, 0, 6, 8, 4, 7, 5, 1, 9, 3], dtype=int64), 'cur_cost': 123.0}, {'tour': array([5, 6, 0, 7, 3, 9, 4, 8, 2, 1], dtype=int64), 'cur_cost': 112.0}]
2025-08-05 10:28:41,848 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:41,848 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 119, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 119, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:41,848 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([0, 8, 1, 5, 9, 2, 4, 7, 6, 3], dtype=int64), 'cur_cost': 138.0, 'intermediate_solutions': [{'tour': array([7, 3, 0, 1, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 7, 3, 0, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 7, 3, 0, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 7, 3, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 8, 1, 7, 3, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:41,849 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 138.00)
2025-08-05 10:28:41,849 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:41,849 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:41,849 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,850 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,850 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,850 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 4, 0, 9, 1, 6, 7, 2, 8, 5], 'cur_cost': 103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,851 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 103.00)
2025-08-05 10:28:41,851 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:41,851 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:41,851 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,852 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,852 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,852 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,852 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 6, 4, 5, 7, 2, 1, 0, 9, 8], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,852 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 101.00)
2025-08-05 10:28:41,853 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:41,853 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:41,853 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,853 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,853 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,854 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,854 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 6, 0, 1, 8, 5, 9, 2, 3, 7], 'cur_cost': 108.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,854 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 108.00)
2025-08-05 10:28:41,854 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:41,854 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:41,854 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,854 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:41,855 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,855 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,855 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 7, 6, 9, 3, 4, 8, 5, 0, 2], 'cur_cost': 98.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,855 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 98.00)
2025-08-05 10:28:41,855 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:41,855 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:41,855 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:41,856 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:41,856 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:41,857 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87.0, 路径长度: 10, 收集中间解: 0
2025-08-05 10:28:41,857 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 9, 6, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 87.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:41,857 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 87.00)
2025-08-05 10:28:41,857 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:41,857 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:41,858 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [4, 9, 5, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 84.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 3, 2, 6, 1, 9, 5, 8, 4], 'cur_cost': 95.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 7, 2, 8, 4, 0, 3, 5, 1, 6], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([5, 1, 8, 6, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([6, 5, 1, 8, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 6, 5, 1, 8, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 6, 5, 1, 9, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 9, 6, 5, 1, 0, 3, 2, 4, 7], dtype=int64), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([0, 8, 1, 5, 9, 2, 4, 7, 6, 3], dtype=int64), 'cur_cost': 138.0, 'intermediate_solutions': [{'tour': array([7, 3, 0, 1, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([1, 7, 3, 0, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 136.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([8, 1, 7, 3, 0, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 1, 7, 3, 8, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 8, 1, 7, 3, 6, 9, 2, 5, 4], dtype=int64), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 0, 9, 1, 6, 7, 2, 8, 5], 'cur_cost': 103.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 6, 4, 5, 7, 2, 1, 0, 9, 8], 'cur_cost': 101.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 6, 0, 1, 8, 5, 9, 2, 3, 7], 'cur_cost': 108.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 7, 6, 9, 3, 4, 8, 5, 0, 2], 'cur_cost': 98.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 6, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 87.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:41,859 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:41,859 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,860 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=84.000, 多样性=0.911
2025-08-05 10:28:41,860 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:41,860 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:41,860 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:41,860 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.04870153198147311, 'best_improvement': -0.1506849315068493}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.012345679012345881}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.07771326877569758, 'recent_improvements': [-0.06688949970024997, 0.03169801842646528, 0.0885370378511452], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:41,861 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:41,861 - __main__ - INFO - geometry4_10 开始进化第 2 代
2025-08-05 10:28:41,861 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:41,861 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:41,861 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=84.000, 多样性=0.911
2025-08-05 10:28:41,861 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:41,862 - PathExpert - INFO - 路径结构分析完成: 公共边数量=2, 路径相似性=0.911
2025-08-05 10:28:41,862 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:41,863 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:41,864 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:41,864 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:41,864 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:41,864 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:41,873 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 0.100, 聚类评分: 0.000, 覆盖率: 0.055, 收敛趋势: 0.000, 多样性: 0.641
2025-08-05 10:28:41,873 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:41,873 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:41,873 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry4_10
2025-08-05 10:28:41,876 - visualization.landscape_visualizer - INFO - 插值约束: 15 个点被约束到最小值 72.00
2025-08-05 10:28:41,878 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.3%, 梯度: 2.15 → 1.95
2025-08-05 10:28:42,000 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\landscape_geometry4_10_iter_47_20250805_102841.html
2025-08-05 10:28:42,086 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\dashboard_geometry4_10_iter_47_20250805_102841.html
2025-08-05 10:28:42,086 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 47
2025-08-05 10:28:42,086 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:42,086 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2227秒
2025-08-05 10:28:42,086 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': 0.10000000000000016, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 416.8557142857143, 'cluster_count': 0}, 'population_state': {'diversity': 0.641287284144427, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0553, 'fitness_entropy': 0.9357849740192012, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: 0.100)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.055)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360921.8731246, 'performance_metrics': {}}}
2025-08-05 10:28:42,087 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:42,087 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:42,087 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:42,087 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:42,087 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:42,087 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:42,087 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:42,088 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,088 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:42,088 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-08-05 10:28:42,088 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,088 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:42,088 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 9} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:42,088 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:42,088 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:42,088 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,089 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,089 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,089 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,090 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,090 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,090 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 9, 1, 4, 7, 2, 6, 0, 5, 8], 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 9, 0, 6, 7, 4, 2, 1], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 9, 0, 6, 7, 1, 2, 3], 'cur_cost': 80.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,090 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 110.00)
2025-08-05 10:28:42,090 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:42,091 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:42,091 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,091 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,091 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,091 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,092 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,092 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [1, 4, 9, 8, 3, 7, 2, 6, 0, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [5, 9, 4, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 2, 7, 0, 1, 6, 5, 9], 'cur_cost': 84.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 5, 6, 1, 0, 4, 7, 2, 3, 8], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,092 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 102.00)
2025-08-05 10:28:42,092 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:42,093 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:42,093 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,094 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:42,094 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,095 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,095 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,096 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 2, 8, 4, 9, 0, 6, 1, 3, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [0, 7, 5, 2, 6, 1, 9, 3, 8, 4], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 2, 4, 8, 5, 9, 1, 6], 'cur_cost': 95.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 2, 6, 1, 9, 8, 4, 5], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,096 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 102.00)
2025-08-05 10:28:42,096 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:42,096 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,096 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,097 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 108.0
2025-08-05 10:28:42,110 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,110 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,110 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,112 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,112 - ExploitationExpert - INFO - populations: [{'tour': [3, 9, 1, 4, 7, 2, 6, 0, 5, 8], 'cur_cost': 110.0}, {'tour': [1, 4, 9, 8, 3, 7, 2, 6, 0, 5], 'cur_cost': 102.0}, {'tour': [7, 2, 8, 4, 9, 0, 6, 1, 3, 5], 'cur_cost': 102.0}, {'tour': array([1, 0, 8, 6, 7, 3, 2, 4, 9, 5], dtype=int64), 'cur_cost': 108.0}, {'tour': [0, 8, 1, 5, 9, 2, 4, 7, 6, 3], 'cur_cost': 138.0}, {'tour': [3, 4, 0, 9, 1, 6, 7, 2, 8, 5], 'cur_cost': 103.0}, {'tour': [3, 6, 4, 5, 7, 2, 1, 0, 9, 8], 'cur_cost': 101.0}, {'tour': [4, 6, 0, 1, 8, 5, 9, 2, 3, 7], 'cur_cost': 108.0}, {'tour': [1, 7, 6, 9, 3, 4, 8, 5, 0, 2], 'cur_cost': 98.0}, {'tour': [1, 9, 6, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 87.0}]
2025-08-05 10:28:42,113 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:42,113 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 120, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 120, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,114 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([1, 0, 8, 6, 7, 3, 2, 4, 9, 5], dtype=int64), 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': array([2, 7, 9, 8, 4, 0, 3, 5, 1, 6]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 2, 7, 9, 4, 0, 3, 5, 1, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 2, 7, 9, 0, 3, 5, 1, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 8, 2, 7, 4, 0, 3, 5, 1, 6]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 4, 8, 2, 7, 0, 3, 5, 1, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,114 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 108.00)
2025-08-05 10:28:42,114 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:42,114 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,114 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,115 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 111.0
2025-08-05 10:28:42,124 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,124 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,124 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,126 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,126 - ExploitationExpert - INFO - populations: [{'tour': [3, 9, 1, 4, 7, 2, 6, 0, 5, 8], 'cur_cost': 110.0}, {'tour': [1, 4, 9, 8, 3, 7, 2, 6, 0, 5], 'cur_cost': 102.0}, {'tour': [7, 2, 8, 4, 9, 0, 6, 1, 3, 5], 'cur_cost': 102.0}, {'tour': array([1, 0, 8, 6, 7, 3, 2, 4, 9, 5], dtype=int64), 'cur_cost': 108.0}, {'tour': array([6, 7, 1, 3, 5, 0, 4, 8, 9, 2], dtype=int64), 'cur_cost': 111.0}, {'tour': [3, 4, 0, 9, 1, 6, 7, 2, 8, 5], 'cur_cost': 103.0}, {'tour': [3, 6, 4, 5, 7, 2, 1, 0, 9, 8], 'cur_cost': 101.0}, {'tour': [4, 6, 0, 1, 8, 5, 9, 2, 3, 7], 'cur_cost': 108.0}, {'tour': [1, 7, 6, 9, 3, 4, 8, 5, 0, 2], 'cur_cost': 98.0}, {'tour': [1, 9, 6, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 87.0}]
2025-08-05 10:28:42,127 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,127 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 121, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 121, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,127 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([6, 7, 1, 3, 5, 0, 4, 8, 9, 2], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 5, 9, 2, 4, 7, 6, 3]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 8, 0, 9, 2, 4, 7, 6, 3]), 'cur_cost': 138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 5, 1, 8, 0, 2, 4, 7, 6, 3]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 8, 9, 2, 4, 7, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 5, 1, 8, 2, 4, 7, 6, 3]), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,128 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 111.00)
2025-08-05 10:28:42,128 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:42,128 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:42,128 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,128 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,129 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,129 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,129 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 4, 6, 8, 5, 9, 0, 1, 7, 3], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 2, 1, 6, 7, 9, 8, 5], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 7, 6, 1, 9, 0, 4, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 9, 1, 6, 3, 7, 2, 8, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,130 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 102.00)
2025-08-05 10:28:42,130 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:42,130 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:42,130 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 88.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,131 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 3, 4, 8, 2, 1, 0, 9, 5, 6], 'cur_cost': 88.0, 'intermediate_solutions': [{'tour': [3, 6, 4, 5, 9, 2, 1, 0, 7, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 5, 4, 7, 2, 1, 0, 9, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 5, 7, 2, 1, 8, 0, 9], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,132 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 88.00)
2025-08-05 10:28:42,132 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:42,132 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:42,132 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,132 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,133 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 131.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,133 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [2, 9, 8, 7, 0, 5, 1, 4, 6, 3], 'cur_cost': 131.0, 'intermediate_solutions': [{'tour': [4, 6, 0, 1, 8, 2, 9, 5, 3, 7], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 0, 1, 7, 3, 2, 9, 5, 8], 'cur_cost': 94.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 8, 5, 9, 2, 4, 3, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,134 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 131.00)
2025-08-05 10:28:42,134 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:42,134 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:42,134 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,134 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,134 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,135 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 84.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,135 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [5, 6, 1, 0, 7, 2, 3, 8, 4, 9], 'cur_cost': 84.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 9, 3, 4, 5, 8, 0, 2], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 0, 5, 8, 4, 3, 9, 2], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 9, 0, 3, 4, 8, 5, 2], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,136 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 84.00)
2025-08-05 10:28:42,136 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:42,136 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:42,136 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,137 - ExplorationExpert - INFO - 探索路径生成完成，成本: 128.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,137 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [2, 9, 0, 7, 1, 5, 8, 3, 6, 4], 'cur_cost': 128.0, 'intermediate_solutions': [{'tour': [1, 9, 6, 0, 7, 8, 4, 3, 2, 5], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 1, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 86.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 5, 8, 9, 4, 3, 2, 7], 'cur_cost': 81.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,138 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 128.00)
2025-08-05 10:28:42,138 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:42,138 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:42,140 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 1, 4, 7, 2, 6, 0, 5, 8], 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': [3, 8, 5, 9, 0, 6, 7, 4, 2, 1], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 5, 9, 0, 6, 7, 1, 2, 3], 'cur_cost': 80.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 8, 5, 9, 0, 6, 7, 3, 2, 1], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 9, 8, 3, 7, 2, 6, 0, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [5, 9, 4, 6, 1, 0, 7, 2, 3, 8], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 8, 3, 2, 7, 0, 1, 6, 5, 9], 'cur_cost': 84.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 5, 6, 1, 0, 4, 7, 2, 3, 8], 'cur_cost': 92.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 2, 8, 4, 9, 0, 6, 1, 3, 5], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [0, 7, 5, 2, 6, 1, 9, 3, 8, 4], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 3, 2, 4, 8, 5, 9, 1, 6], 'cur_cost': 95.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 3, 2, 6, 1, 9, 8, 4, 5], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([1, 0, 8, 6, 7, 3, 2, 4, 9, 5], dtype=int64), 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': array([2, 7, 9, 8, 4, 0, 3, 5, 1, 6]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 2, 7, 9, 4, 0, 3, 5, 1, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 2, 7, 9, 0, 3, 5, 1, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 8, 2, 7, 4, 0, 3, 5, 1, 6]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 4, 8, 2, 7, 0, 3, 5, 1, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 7, 1, 3, 5, 0, 4, 8, 9, 2], dtype=int64), 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': array([1, 8, 0, 5, 9, 2, 4, 7, 6, 3]), 'cur_cost': 127.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([5, 1, 8, 0, 9, 2, 4, 7, 6, 3]), 'cur_cost': 138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 5, 1, 8, 0, 2, 4, 7, 6, 3]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([0, 5, 1, 8, 9, 2, 4, 7, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([0, 9, 5, 1, 8, 2, 4, 7, 6, 3]), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 4, 6, 8, 5, 9, 0, 1, 7, 3], 'cur_cost': 102.0, 'intermediate_solutions': [{'tour': [3, 4, 0, 2, 1, 6, 7, 9, 8, 5], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 8, 2, 7, 6, 1, 9, 0, 4, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [4, 0, 9, 1, 6, 3, 7, 2, 8, 5], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 3, 4, 8, 2, 1, 0, 9, 5, 6], 'cur_cost': 88.0, 'intermediate_solutions': [{'tour': [3, 6, 4, 5, 9, 2, 1, 0, 7, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 5, 4, 7, 2, 1, 0, 9, 8], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 6, 4, 5, 7, 2, 1, 8, 0, 9], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 8, 7, 0, 5, 1, 4, 6, 3], 'cur_cost': 131.0, 'intermediate_solutions': [{'tour': [4, 6, 0, 1, 8, 2, 9, 5, 3, 7], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [4, 6, 0, 1, 7, 3, 2, 9, 5, 8], 'cur_cost': 94.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 1, 8, 5, 9, 2, 4, 3, 7], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 1, 0, 7, 2, 3, 8, 4, 9], 'cur_cost': 84.0, 'intermediate_solutions': [{'tour': [1, 7, 6, 9, 3, 4, 5, 8, 0, 2], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 0, 5, 8, 4, 3, 9, 2], 'cur_cost': 96.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 7, 6, 9, 0, 3, 4, 8, 5, 2], 'cur_cost': 106.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 0, 7, 1, 5, 8, 3, 6, 4], 'cur_cost': 128.0, 'intermediate_solutions': [{'tour': [1, 9, 6, 0, 7, 8, 4, 3, 2, 5], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 1, 0, 5, 8, 4, 3, 2, 7], 'cur_cost': 86.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 6, 0, 5, 8, 9, 4, 3, 2, 7], 'cur_cost': 81.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:42,140 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:42,140 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,142 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=84.000, 多样性=0.911
2025-08-05 10:28:42,142 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:42,143 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:42,143 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:42,143 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 70, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'slight_improvement', 'improvement_rate': 0.00255726670259133, 'best_improvement': 0.0}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -2.4370749321040014e-16}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.040199775203969196, 'recent_improvements': [0.03169801842646528, 0.0885370378511452, -0.04870153198147311], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['改进缓慢，建议调整策略平衡或增加扰动强度', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配']}
2025-08-05 10:28:42,143 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:42,144 - __main__ - INFO - geometry4_10 开始进化第 3 代
2025-08-05 10:28:42,144 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:42,144 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,145 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=84.000, 多样性=0.911
2025-08-05 10:28:42,145 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:42,146 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.911
2025-08-05 10:28:42,146 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:42,147 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:42,149 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:42,149 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:42,149 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:42,149 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:42,158 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: -1.471, 聚类评分: 0.000, 覆盖率: 0.056, 收敛趋势: 0.000, 多样性: 0.633
2025-08-05 10:28:42,159 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:42,159 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:42,159 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry4_10
2025-08-05 10:28:42,165 - visualization.landscape_visualizer - INFO - 插值约束: 66 个点被约束到最小值 72.00
2025-08-05 10:28:42,166 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.5%, 梯度: 2.55 → 2.41
2025-08-05 10:28:42,303 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\landscape_geometry4_10_iter_48_20250805_102842.html
2025-08-05 10:28:42,363 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\dashboard_geometry4_10_iter_48_20250805_102842.html
2025-08-05 10:28:42,363 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 48
2025-08-05 10:28:42,363 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:42,363 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2140秒
2025-08-05 10:28:42,363 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -1.4714285714285702, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 255.16346938775513, 'cluster_count': 0}, 'population_state': {'diversity': 0.6326530612244898, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0561, 'fitness_entropy': 0.9780416657815159, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -1.471)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.056)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360922.1594117, 'performance_metrics': {}}}
2025-08-05 10:28:42,363 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:42,363 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:42,364 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:42,364 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:42,364 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:42,364 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:42,364 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:42,365 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,365 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:42,365 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-08-05 10:28:42,365 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,365 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:42,366 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:42,366 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:42,366 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:42,366 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,366 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,367 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 7, 8, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 94.0, 'intermediate_solutions': [{'tour': [3, 4, 1, 9, 7, 2, 6, 0, 5, 8], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 1, 4, 7, 0, 6, 2, 5, 8], 'cur_cost': 130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 1, 4, 8, 7, 2, 6, 0, 5], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,367 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 94.00)
2025-08-05 10:28:42,367 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:42,367 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,368 - ExplorationExpert - INFO - 探索路径生成完成，成本: 124.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,368 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [6, 9, 0, 4, 1, 8, 3, 7, 2, 5], 'cur_cost': 124.0, 'intermediate_solutions': [{'tour': [1, 5, 9, 8, 3, 7, 2, 6, 0, 4], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 5, 0, 6, 2, 7, 3, 8, 9], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 9, 8, 3, 7, 6, 0, 5], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,369 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 124.00)
2025-08-05 10:28:42,369 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:42,369 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:42,369 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,369 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,370 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0, 'intermediate_solutions': [{'tour': [7, 2, 8, 4, 9, 0, 5, 1, 3, 6], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 1, 6, 0, 9, 4, 8, 2, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 2, 9, 0, 6, 1, 3, 5], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,370 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 106.00)
2025-08-05 10:28:42,370 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:42,370 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,371 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,371 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 9, 7, 3, 2, 4, 6, 5], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 8, 0, 3, 2, 4, 9, 5], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 8, 6, 7, 3, 4, 2, 9, 5], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,371 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 108.00)
2025-08-05 10:28:42,372 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:42,372 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,372 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,372 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 110.0
2025-08-05 10:28:42,380 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,380 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,380 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,381 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,381 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 8, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 94.0}, {'tour': [6, 9, 0, 4, 1, 8, 3, 7, 2, 5], 'cur_cost': 124.0}, {'tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0}, {'tour': array([9, 0, 7, 3, 6, 1, 8, 2, 4, 5], dtype=int64), 'cur_cost': 110.0}, {'tour': [2, 4, 6, 8, 5, 9, 0, 1, 7, 3], 'cur_cost': 102.0}, {'tour': [7, 3, 4, 8, 2, 1, 0, 9, 5, 6], 'cur_cost': 88.0}, {'tour': [2, 9, 8, 7, 0, 5, 1, 4, 6, 3], 'cur_cost': 131.0}, {'tour': [5, 6, 1, 0, 7, 2, 3, 8, 4, 9], 'cur_cost': 84.0}, {'tour': [2, 9, 0, 7, 1, 5, 8, 3, 6, 4], 'cur_cost': 128.0}]
2025-08-05 10:28:42,381 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,381 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 122, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 122, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,382 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([9, 0, 7, 3, 6, 1, 8, 2, 4, 5], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([1, 7, 6, 3, 5, 0, 4, 8, 9, 2]), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 7, 6, 5, 0, 4, 8, 9, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 1, 7, 6, 0, 4, 8, 9, 2]), 'cur_cost': 121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 1, 7, 5, 0, 4, 8, 9, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 5, 3, 1, 7, 0, 4, 8, 9, 2]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,382 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 110.00)
2025-08-05 10:28:42,382 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:42,382 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:42,382 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,383 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [2, 4, 6, 8, 5, 1, 0, 9, 7, 3], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 7, 1, 0, 9, 5, 8, 6, 4], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 6, 8, 5, 7, 9, 0, 1, 3], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,383 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 101.00)
2025-08-05 10:28:42,384 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,384 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,385 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 6, 2, 4, 8, 9, 5, 7, 3, 1], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [7, 9, 4, 8, 2, 1, 0, 3, 5, 6], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 9, 0, 1, 2, 8, 4, 6], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 4, 2, 1, 8, 0, 9, 5, 6], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,385 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 101.00)
2025-08-05 10:28:42,385 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:42,385 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,385 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,385 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 122.0
2025-08-05 10:28:42,391 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,391 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,391 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,392 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,392 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 8, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 94.0}, {'tour': [6, 9, 0, 4, 1, 8, 3, 7, 2, 5], 'cur_cost': 124.0}, {'tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0}, {'tour': array([9, 0, 7, 3, 6, 1, 8, 2, 4, 5], dtype=int64), 'cur_cost': 110.0}, {'tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0}, {'tour': [0, 6, 2, 4, 8, 9, 5, 7, 3, 1], 'cur_cost': 101.0}, {'tour': array([2, 0, 7, 4, 9, 5, 8, 1, 3, 6], dtype=int64), 'cur_cost': 122.0}, {'tour': [5, 6, 1, 0, 7, 2, 3, 8, 4, 9], 'cur_cost': 84.0}, {'tour': [2, 9, 0, 7, 1, 5, 8, 3, 6, 4], 'cur_cost': 128.0}]
2025-08-05 10:28:42,393 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,393 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 123, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 123, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,393 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([2, 0, 7, 4, 9, 5, 8, 1, 3, 6], dtype=int64), 'cur_cost': 122.0, 'intermediate_solutions': [{'tour': array([8, 9, 2, 7, 0, 5, 1, 4, 6, 3]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 9, 2, 0, 5, 1, 4, 6, 3]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 8, 9, 2, 5, 1, 4, 6, 3]), 'cur_cost': 152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 8, 9, 0, 5, 1, 4, 6, 3]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 7, 8, 9, 5, 1, 4, 6, 3]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,393 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 122.00)
2025-08-05 10:28:42,393 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:42,393 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,394 - ExplorationExpert - INFO - 探索路径生成完成，成本: 119.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,395 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 2, 8, 9, 7, 4, 5, 6, 3, 0], 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 0, 7, 2, 3, 8, 5, 9], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 1, 6, 5, 3, 8, 4, 9], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 1, 0, 7, 2, 8, 4, 9, 3], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,395 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 119.00)
2025-08-05 10:28:42,395 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:42,395 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,395 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 112.0
2025-08-05 10:28:42,401 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,401 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,401 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,402 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,402 - ExploitationExpert - INFO - populations: [{'tour': [5, 7, 8, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 94.0}, {'tour': [6, 9, 0, 4, 1, 8, 3, 7, 2, 5], 'cur_cost': 124.0}, {'tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0}, {'tour': array([9, 0, 7, 3, 6, 1, 8, 2, 4, 5], dtype=int64), 'cur_cost': 110.0}, {'tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0}, {'tour': [0, 6, 2, 4, 8, 9, 5, 7, 3, 1], 'cur_cost': 101.0}, {'tour': array([2, 0, 7, 4, 9, 5, 8, 1, 3, 6], dtype=int64), 'cur_cost': 122.0}, {'tour': [1, 2, 8, 9, 7, 4, 5, 6, 3, 0], 'cur_cost': 119.0}, {'tour': array([2, 6, 0, 8, 7, 9, 4, 5, 3, 1], dtype=int64), 'cur_cost': 112.0}]
2025-08-05 10:28:42,403 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,403 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 124, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 124, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,403 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([2, 6, 0, 8, 7, 9, 4, 5, 3, 1], dtype=int64), 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': array([0, 9, 2, 7, 1, 5, 8, 3, 6, 4]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 9, 2, 1, 5, 8, 3, 6, 4]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 0, 9, 2, 5, 8, 3, 6, 4]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 0, 9, 1, 5, 8, 3, 6, 4]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 7, 0, 9, 5, 8, 3, 6, 4]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,404 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 112.00)
2025-08-05 10:28:42,404 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:42,404 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:42,405 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 8, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 94.0, 'intermediate_solutions': [{'tour': [3, 4, 1, 9, 7, 2, 6, 0, 5, 8], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 1, 4, 7, 0, 6, 2, 5, 8], 'cur_cost': 130.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 1, 4, 8, 7, 2, 6, 0, 5], 'cur_cost': 110.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 0, 4, 1, 8, 3, 7, 2, 5], 'cur_cost': 124.0, 'intermediate_solutions': [{'tour': [1, 5, 9, 8, 3, 7, 2, 6, 0, 4], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 4, 5, 0, 6, 2, 7, 3, 8, 9], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 4, 9, 8, 3, 7, 6, 0, 5], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0, 'intermediate_solutions': [{'tour': [7, 2, 8, 4, 9, 0, 5, 1, 3, 6], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 1, 6, 0, 9, 4, 8, 2, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 4, 2, 9, 0, 6, 1, 3, 5], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [1, 0, 8, 9, 7, 3, 2, 4, 6, 5], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 7, 6, 8, 0, 3, 2, 4, 9, 5], 'cur_cost': 119.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 0, 8, 6, 7, 3, 4, 2, 9, 5], 'cur_cost': 114.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 0, 7, 3, 6, 1, 8, 2, 4, 5], dtype=int64), 'cur_cost': 110.0, 'intermediate_solutions': [{'tour': array([1, 7, 6, 3, 5, 0, 4, 8, 9, 2]), 'cur_cost': 109.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([3, 1, 7, 6, 5, 0, 4, 8, 9, 2]), 'cur_cost': 108.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 3, 1, 7, 6, 0, 4, 8, 9, 2]), 'cur_cost': 121.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 3, 1, 7, 5, 0, 4, 8, 9, 2]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 5, 3, 1, 7, 0, 4, 8, 9, 2]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [2, 4, 6, 8, 5, 1, 0, 9, 7, 3], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 3, 7, 1, 0, 9, 5, 8, 6, 4], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 4, 6, 8, 5, 7, 9, 0, 1, 3], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 2, 4, 8, 9, 5, 7, 3, 1], 'cur_cost': 101.0, 'intermediate_solutions': [{'tour': [7, 9, 4, 8, 2, 1, 0, 3, 5, 6], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [7, 3, 5, 9, 0, 1, 2, 8, 4, 6], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 3, 4, 2, 1, 8, 0, 9, 5, 6], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 0, 7, 4, 9, 5, 8, 1, 3, 6], dtype=int64), 'cur_cost': 122.0, 'intermediate_solutions': [{'tour': array([8, 9, 2, 7, 0, 5, 1, 4, 6, 3]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 8, 9, 2, 0, 5, 1, 4, 6, 3]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([0, 7, 8, 9, 2, 5, 1, 4, 6, 3]), 'cur_cost': 152.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 8, 9, 0, 5, 1, 4, 6, 3]), 'cur_cost': 115.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 0, 7, 8, 9, 5, 1, 4, 6, 3]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 8, 9, 7, 4, 5, 6, 3, 0], 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': [4, 6, 1, 0, 7, 2, 3, 8, 5, 9], 'cur_cost': 90.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 7, 0, 1, 6, 5, 3, 8, 4, 9], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 6, 1, 0, 7, 2, 8, 4, 9, 3], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 6, 0, 8, 7, 9, 4, 5, 3, 1], dtype=int64), 'cur_cost': 112.0, 'intermediate_solutions': [{'tour': array([0, 9, 2, 7, 1, 5, 8, 3, 6, 4]), 'cur_cost': 118.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([7, 0, 9, 2, 1, 5, 8, 3, 6, 4]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 7, 0, 9, 2, 5, 8, 3, 6, 4]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 7, 0, 9, 1, 5, 8, 3, 6, 4]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 1, 7, 0, 9, 5, 8, 3, 6, 4]), 'cur_cost': 110.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:42,405 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:42,405 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,406 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94.000, 多样性=0.893
2025-08-05 10:28:42,406 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:42,407 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:42,407 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:42,407 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.03156439332198039, 'best_improvement': -0.11904761904761904}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.019512195121951344}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.04298988557427693, 'recent_improvements': [0.0885370378511452, -0.04870153198147311, 0.00255726670259133], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:42,407 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:42,407 - __main__ - INFO - geometry4_10 开始进化第 4 代
2025-08-05 10:28:42,407 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:42,407 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,408 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=94.000, 多样性=0.893
2025-08-05 10:28:42,408 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:42,408 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.893
2025-08-05 10:28:42,409 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:42,409 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:42,410 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:42,411 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:42,411 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:42,411 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:42,419 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.286, 适应度梯度: 0.971, 聚类评分: 0.000, 覆盖率: 0.057, 收敛趋势: 0.000, 多样性: 0.634
2025-08-05 10:28:42,419 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:42,419 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:42,419 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry4_10
2025-08-05 10:28:42,424 - visualization.landscape_visualizer - INFO - 插值约束: 85 个点被约束到最小值 72.00
2025-08-05 10:28:42,425 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.7%, 梯度: 2.44 → 2.25
2025-08-05 10:28:42,535 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\landscape_geometry4_10_iter_49_20250805_102842.html
2025-08-05 10:28:42,629 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\dashboard_geometry4_10_iter_49_20250805_102842.html
2025-08-05 10:28:42,629 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 49
2025-08-05 10:28:42,629 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:42,629 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2192秒
2025-08-05 10:28:42,629 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2857142857142857, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 0.9714285714285719, 'local_optima_density': 0.2857142857142857, 'gradient_variance': 149.1134693877551, 'cluster_count': 0}, 'population_state': {'diversity': 0.6342229199372057, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0569, 'fitness_entropy': 0.9546444483376649, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.057)', 'recommendation': '增加探索性操作'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 0.971)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360922.4194086, 'performance_metrics': {}}}
2025-08-05 10:28:42,629 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:42,629 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:42,630 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:42,630 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:42,630 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,630 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:42,630 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,630 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,630 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:42,630 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,630 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,630 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:42,631 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:42,631 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:42,631 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:42,631 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,631 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,632 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 7, 2, 0, 1, 5, 9, 3, 4, 8], 'cur_cost': 100.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 0, 6, 1, 4, 3, 2, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 0, 6, 1, 4, 3, 2, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 89.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,632 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 100.00)
2025-08-05 10:28:42,632 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:42,632 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,632 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,633 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 122.0
2025-08-05 10:28:42,639 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,639 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,639 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,640 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,640 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 2, 0, 1, 5, 9, 3, 4, 8], 'cur_cost': 100.0}, {'tour': array([8, 1, 5, 0, 7, 2, 4, 3, 9, 6], dtype=int64), 'cur_cost': 122.0}, {'tour': [1, 2, 6, 3, 9, 8, 4, 7, 5, 0], 'cur_cost': 106.0}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0}, {'tour': [9, 0, 7, 3, 6, 1, 8, 2, 4, 5], 'cur_cost': 110.0}, {'tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0}, {'tour': [0, 6, 2, 4, 8, 9, 5, 7, 3, 1], 'cur_cost': 101.0}, {'tour': [2, 0, 7, 4, 9, 5, 8, 1, 3, 6], 'cur_cost': 122.0}, {'tour': [1, 2, 8, 9, 7, 4, 5, 6, 3, 0], 'cur_cost': 119.0}, {'tour': [2, 6, 0, 8, 7, 9, 4, 5, 3, 1], 'cur_cost': 112.0}]
2025-08-05 10:28:42,640 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,640 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 125, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 125, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,641 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([8, 1, 5, 0, 7, 2, 4, 3, 9, 6], dtype=int64), 'cur_cost': 122.0, 'intermediate_solutions': [{'tour': array([0, 9, 6, 4, 1, 8, 3, 7, 2, 5]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 9, 6, 1, 8, 3, 7, 2, 5]), 'cur_cost': 105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 0, 9, 6, 8, 3, 7, 2, 5]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 0, 9, 1, 8, 3, 7, 2, 5]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 1, 4, 0, 9, 8, 3, 7, 2, 5]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,641 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 122.00)
2025-08-05 10:28:42,641 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:42,641 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:42,641 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,642 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 5, 8, 4, 9, 6, 1, 7, 2], 'cur_cost': 104.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 3, 9, 8, 4, 5, 7, 0], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 2, 1, 9, 8, 4, 7, 5, 0], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 6, 3, 9, 8, 5, 4, 7, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,643 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 104.00)
2025-08-05 10:28:42,643 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,643 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,644 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 9, 0, 8, 3, 7, 2, 6, 5], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [4, 8, 6, 9, 1, 7, 3, 2, 5, 0], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 6, 9, 1, 7, 0, 5, 4, 3], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,644 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 113.00)
2025-08-05 10:28:42,644 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:42,644 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:42,644 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 91.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,645 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 7, 2, 3, 8, 4, 9, 0, 6, 1], 'cur_cost': 91.0, 'intermediate_solutions': [{'tour': [0, 9, 7, 3, 6, 1, 8, 2, 4, 5], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 7, 3, 6, 8, 1, 2, 4, 5], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 0, 3, 7, 6, 1, 8, 2, 4, 5], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,645 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 91.00)
2025-08-05 10:28:42,645 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,646 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,647 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [6, 9, 3, 4, 7, 0, 5, 8, 2, 1], 'cur_cost': 106.0, 'intermediate_solutions': [{'tour': [3, 5, 0, 2, 8, 4, 9, 7, 1, 6], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 0, 2, 1, 6, 9, 4, 8, 7], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,647 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 106.00)
2025-08-05 10:28:42,647 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:42,647 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:42,647 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,647 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,647 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,648 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,648 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [3, 9, 0, 1, 6, 7, 4, 5, 2, 8], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [0, 6, 2, 7, 8, 9, 5, 4, 3, 1], 'cur_cost': 88.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 2, 4, 8, 9, 1, 3, 7, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 4, 9, 5, 8, 7, 3, 1], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,648 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 108.00)
2025-08-05 10:28:42,648 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:42,648 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,648 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,648 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 139.0
2025-08-05 10:28:42,654 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,654 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,654 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,655 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,655 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 2, 0, 1, 5, 9, 3, 4, 8], 'cur_cost': 100.0}, {'tour': array([8, 1, 5, 0, 7, 2, 4, 3, 9, 6], dtype=int64), 'cur_cost': 122.0}, {'tour': [0, 3, 5, 8, 4, 9, 6, 1, 7, 2], 'cur_cost': 104.0}, {'tour': [1, 4, 9, 0, 8, 3, 7, 2, 6, 5], 'cur_cost': 113.0}, {'tour': [5, 7, 2, 3, 8, 4, 9, 0, 6, 1], 'cur_cost': 91.0}, {'tour': [6, 9, 3, 4, 7, 0, 5, 8, 2, 1], 'cur_cost': 106.0}, {'tour': [3, 9, 0, 1, 6, 7, 4, 5, 2, 8], 'cur_cost': 108.0}, {'tour': array([9, 0, 8, 2, 5, 1, 7, 4, 6, 3], dtype=int64), 'cur_cost': 139.0}, {'tour': [1, 2, 8, 9, 7, 4, 5, 6, 3, 0], 'cur_cost': 119.0}, {'tour': [2, 6, 0, 8, 7, 9, 4, 5, 3, 1], 'cur_cost': 112.0}]
2025-08-05 10:28:42,656 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,656 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 126, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 126, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,656 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([9, 0, 8, 2, 5, 1, 7, 4, 6, 3], dtype=int64), 'cur_cost': 139.0, 'intermediate_solutions': [{'tour': array([7, 0, 2, 4, 9, 5, 8, 1, 3, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 0, 2, 9, 5, 8, 1, 3, 6]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 7, 0, 2, 5, 8, 1, 3, 6]), 'cur_cost': 137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 7, 0, 9, 5, 8, 1, 3, 6]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 9, 4, 7, 0, 5, 8, 1, 3, 6]), 'cur_cost': 125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,657 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 139.00)
2025-08-05 10:28:42,657 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:42,657 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,657 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,657 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 134.0
2025-08-05 10:28:42,663 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,663 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,663 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,664 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,664 - ExploitationExpert - INFO - populations: [{'tour': [6, 7, 2, 0, 1, 5, 9, 3, 4, 8], 'cur_cost': 100.0}, {'tour': array([8, 1, 5, 0, 7, 2, 4, 3, 9, 6], dtype=int64), 'cur_cost': 122.0}, {'tour': [0, 3, 5, 8, 4, 9, 6, 1, 7, 2], 'cur_cost': 104.0}, {'tour': [1, 4, 9, 0, 8, 3, 7, 2, 6, 5], 'cur_cost': 113.0}, {'tour': [5, 7, 2, 3, 8, 4, 9, 0, 6, 1], 'cur_cost': 91.0}, {'tour': [6, 9, 3, 4, 7, 0, 5, 8, 2, 1], 'cur_cost': 106.0}, {'tour': [3, 9, 0, 1, 6, 7, 4, 5, 2, 8], 'cur_cost': 108.0}, {'tour': array([9, 0, 8, 2, 5, 1, 7, 4, 6, 3], dtype=int64), 'cur_cost': 139.0}, {'tour': array([6, 9, 1, 8, 4, 2, 0, 3, 5, 7], dtype=int64), 'cur_cost': 134.0}, {'tour': [2, 6, 0, 8, 7, 9, 4, 5, 3, 1], 'cur_cost': 112.0}]
2025-08-05 10:28:42,665 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,665 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 127, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 127, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,666 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([6, 9, 1, 8, 4, 2, 0, 3, 5, 7], dtype=int64), 'cur_cost': 134.0, 'intermediate_solutions': [{'tour': array([8, 2, 1, 9, 7, 4, 5, 6, 3, 0]), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 8, 2, 1, 7, 4, 5, 6, 3, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 9, 8, 2, 1, 4, 5, 6, 3, 0]), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 9, 8, 2, 7, 4, 5, 6, 3, 0]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 9, 8, 2, 4, 5, 6, 3, 0]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,666 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 134.00)
2025-08-05 10:28:42,666 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:42,666 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:42,666 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,666 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,667 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,667 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [1, 3, 7, 8, 4, 6, 0, 9, 5, 2], 'cur_cost': 105.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 8, 7, 2, 4, 5, 3, 1], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 4, 9, 7, 8, 0, 3, 1], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 8, 7, 9, 4, 2, 5, 3, 1], 'cur_cost': 127.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,668 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 105.00)
2025-08-05 10:28:42,668 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:42,668 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:42,669 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 7, 2, 0, 1, 5, 9, 3, 4, 8], 'cur_cost': 100.0, 'intermediate_solutions': [{'tour': [5, 7, 8, 0, 6, 1, 4, 3, 2, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 8, 0, 6, 1, 4, 3, 2, 9], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [7, 8, 5, 0, 6, 1, 2, 3, 4, 9], 'cur_cost': 89.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([8, 1, 5, 0, 7, 2, 4, 3, 9, 6], dtype=int64), 'cur_cost': 122.0, 'intermediate_solutions': [{'tour': array([0, 9, 6, 4, 1, 8, 3, 7, 2, 5]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 0, 9, 6, 1, 8, 3, 7, 2, 5]), 'cur_cost': 105.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([1, 4, 0, 9, 6, 8, 3, 7, 2, 5]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 4, 0, 9, 1, 8, 3, 7, 2, 5]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 1, 4, 0, 9, 8, 3, 7, 2, 5]), 'cur_cost': 112.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 8, 4, 9, 6, 1, 7, 2], 'cur_cost': 104.0, 'intermediate_solutions': [{'tour': [1, 2, 6, 3, 9, 8, 4, 5, 7, 0], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 6, 2, 1, 9, 8, 4, 7, 5, 0], 'cur_cost': 115.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 2, 6, 3, 9, 8, 5, 4, 7, 0], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 9, 0, 8, 3, 7, 2, 6, 5], 'cur_cost': 113.0, 'intermediate_solutions': [{'tour': [4, 8, 6, 9, 1, 7, 3, 2, 5, 0], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 8, 6, 9, 1, 7, 0, 5, 4, 3], 'cur_cost': 107.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [2, 8, 6, 9, 1, 7, 3, 4, 5, 0], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 7, 2, 3, 8, 4, 9, 0, 6, 1], 'cur_cost': 91.0, 'intermediate_solutions': [{'tour': [0, 9, 7, 3, 6, 1, 8, 2, 4, 5], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 0, 7, 3, 6, 8, 1, 2, 4, 5], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 0, 3, 7, 6, 1, 8, 2, 4, 5], 'cur_cost': 108.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [6, 9, 3, 4, 7, 0, 5, 8, 2, 1], 'cur_cost': 106.0, 'intermediate_solutions': [{'tour': [3, 5, 0, 2, 8, 4, 9, 7, 1, 6], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 5, 0, 2, 1, 6, 9, 4, 8, 7], 'cur_cost': 97.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 5, 0, 2, 8, 4, 9, 6, 1, 7], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [3, 9, 0, 1, 6, 7, 4, 5, 2, 8], 'cur_cost': 108.0, 'intermediate_solutions': [{'tour': [0, 6, 2, 7, 8, 9, 5, 4, 3, 1], 'cur_cost': 88.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 2, 4, 8, 9, 1, 3, 7, 5], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 2, 4, 9, 5, 8, 7, 3, 1], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([9, 0, 8, 2, 5, 1, 7, 4, 6, 3], dtype=int64), 'cur_cost': 139.0, 'intermediate_solutions': [{'tour': array([7, 0, 2, 4, 9, 5, 8, 1, 3, 6]), 'cur_cost': 124.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([4, 7, 0, 2, 9, 5, 8, 1, 3, 6]), 'cur_cost': 134.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([9, 4, 7, 0, 2, 5, 8, 1, 3, 6]), 'cur_cost': 137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([2, 4, 7, 0, 9, 5, 8, 1, 3, 6]), 'cur_cost': 122.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([2, 9, 4, 7, 0, 5, 8, 1, 3, 6]), 'cur_cost': 125.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 9, 1, 8, 4, 2, 0, 3, 5, 7], dtype=int64), 'cur_cost': 134.0, 'intermediate_solutions': [{'tour': array([8, 2, 1, 9, 7, 4, 5, 6, 3, 0]), 'cur_cost': 130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([9, 8, 2, 1, 7, 4, 5, 6, 3, 0]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 9, 8, 2, 1, 4, 5, 6, 3, 0]), 'cur_cost': 131.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([1, 9, 8, 2, 7, 4, 5, 6, 3, 0]), 'cur_cost': 117.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([1, 7, 9, 8, 2, 4, 5, 6, 3, 0]), 'cur_cost': 123.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [1, 3, 7, 8, 4, 6, 0, 9, 5, 2], 'cur_cost': 105.0, 'intermediate_solutions': [{'tour': [9, 6, 0, 8, 7, 2, 4, 5, 3, 1], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [2, 6, 5, 4, 9, 7, 8, 0, 3, 1], 'cur_cost': 120.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 0, 8, 7, 9, 4, 2, 5, 3, 1], 'cur_cost': 127.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:42,669 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:42,669 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,670 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=91.000, 多样性=0.916
2025-08-05 10:28:42,671 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:42,671 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:42,671 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:42,671 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0372807580083116, 'best_improvement': 0.031914893617021274}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.024875621890547428}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.008568569329746353, 'recent_improvements': [-0.04870153198147311, 0.00255726670259133, -0.03156439332198039], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:42,671 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:42,671 - __main__ - INFO - geometry4_10 开始进化第 5 代
2025-08-05 10:28:42,671 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:42,672 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,672 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=91.000, 多样性=0.916
2025-08-05 10:28:42,673 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:42,673 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.916
2025-08-05 10:28:42,673 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:42,674 - EliteExpert - INFO - 精英解分析完成: 精英解数量=4, 多样性=0.667
2025-08-05 10:28:42,676 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:42,676 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:42,676 - LandscapeExpert - INFO - 添加精英解数据: 4个精英解
2025-08-05 10:28:42,676 - LandscapeExpert - INFO - 数据提取成功: 14个路径, 14个适应度值
2025-08-05 10:28:42,684 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.357, 适应度梯度: 0.700, 聚类评分: 0.000, 覆盖率: 0.058, 收敛趋势: 0.000, 多样性: 0.641
2025-08-05 10:28:42,684 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:42,684 - LandscapeExpert - INFO - 提取到 4 个精英解
2025-08-05 10:28:42,685 - visualization.landscape_visualizer - INFO - 设置当前实例名: geometry4_10
2025-08-05 10:28:42,688 - visualization.landscape_visualizer - INFO - 插值约束: 151 个点被约束到最小值 72.00
2025-08-05 10:28:42,689 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.2%, 梯度: 2.71 → 2.48
2025-08-05 10:28:42,818 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\landscape_geometry4_10_iter_50_20250805_102842.html
2025-08-05 10:28:42,863 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_geometry4_10\dashboard_geometry4_10_iter_50_20250805_102842.html
2025-08-05 10:28:42,864 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 50
2025-08-05 10:28:42,864 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:42,864 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.1881秒
2025-08-05 10:28:42,864 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.35714285714285715, 'modality': 'bi-modal', 'deceptiveness': 'low', 'gradient_strength': 0.6999999999999995, 'local_optima_density': 0.35714285714285715, 'gradient_variance': 544.6557142857143, 'cluster_count': 0}, 'population_state': {'diversity': 0.640502354788069, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0577, 'fitness_entropy': 0.929906376575379, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.058)', 'recommendation': '增加探索性操作'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360922.6845825, 'performance_metrics': {}}}
2025-08-05 10:28:42,864 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:42,864 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:42,864 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:42,864 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:42,865 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,865 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:42,865 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore']
2025-08-05 10:28:42,865 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:42,865 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:42,865 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:42,865 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,866 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,867 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 1, 0, 7, 3, 8, 4, 9, 5, 2], 'cur_cost': 94.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 0, 4, 5, 9, 3, 1, 8], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 9, 5, 1, 0, 2, 3, 4, 8], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 0, 1, 5, 9, 3, 7, 4, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,867 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 94.00)
2025-08-05 10:28:42,867 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:42,867 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,867 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,867 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 118.0
2025-08-05 10:28:42,874 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,874 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,874 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,876 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,876 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 0, 7, 3, 8, 4, 9, 5, 2], 'cur_cost': 94.0}, {'tour': array([2, 4, 5, 1, 9, 8, 7, 3, 0, 6], dtype=int64), 'cur_cost': 118.0}, {'tour': [0, 3, 5, 8, 4, 9, 6, 1, 7, 2], 'cur_cost': 104.0}, {'tour': [1, 4, 9, 0, 8, 3, 7, 2, 6, 5], 'cur_cost': 113.0}, {'tour': [5, 7, 2, 3, 8, 4, 9, 0, 6, 1], 'cur_cost': 91.0}, {'tour': [6, 9, 3, 4, 7, 0, 5, 8, 2, 1], 'cur_cost': 106.0}, {'tour': [3, 9, 0, 1, 6, 7, 4, 5, 2, 8], 'cur_cost': 108.0}, {'tour': [9, 0, 8, 2, 5, 1, 7, 4, 6, 3], 'cur_cost': 139.0}, {'tour': [6, 9, 1, 8, 4, 2, 0, 3, 5, 7], 'cur_cost': 134.0}, {'tour': [1, 3, 7, 8, 4, 6, 0, 9, 5, 2], 'cur_cost': 105.0}]
2025-08-05 10:28:42,877 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,877 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 128, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 128, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,878 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([2, 4, 5, 1, 9, 8, 7, 3, 0, 6], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([5, 1, 8, 0, 7, 2, 4, 3, 9, 6]), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 1, 8, 7, 2, 4, 3, 9, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 5, 1, 8, 2, 4, 3, 9, 6]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 5, 1, 7, 2, 4, 3, 9, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 0, 5, 1, 2, 4, 3, 9, 6]), 'cur_cost': 120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,878 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 118.00)
2025-08-05 10:28:42,878 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:42,878 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:42,878 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,878 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,878 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,878 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,879 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,879 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,879 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 9, 2, 6, 8, 4, 3, 1, 0, 5], 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 8, 4, 6, 9, 1, 7, 2], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 8, 4, 9, 6, 1, 2, 7], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 5, 8, 9, 6, 1, 7, 2], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,879 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 111.00)
2025-08-05 10:28:42,879 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:42,879 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:42,879 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,880 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,880 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [7, 9, 1, 0, 2, 6, 8, 4, 3, 5], 'cur_cost': 116.0, 'intermediate_solutions': [{'tour': [1, 4, 9, 0, 8, 3, 5, 2, 6, 7], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 9, 4, 1, 3, 7, 2, 6, 5], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 9, 0, 8, 3, 7, 6, 5, 2], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,881 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 116.00)
2025-08-05 10:28:42,881 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 10
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,881 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,882 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,882 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,882 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 9, 3, 6, 1, 7, 8, 4, 5, 0], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [5, 7, 2, 3, 8, 4, 0, 9, 6, 1], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 4, 8, 3, 9, 0, 6, 1], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 8, 3, 4, 9, 0, 6, 1], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,882 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 107.00)
2025-08-05 10:28:42,882 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:42,882 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:42,882 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,882 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,883 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [5, 6, 2, 1, 7, 3, 8, 9, 4, 0], 'cur_cost': 99.0, 'intermediate_solutions': [{'tour': [6, 9, 3, 8, 7, 0, 5, 4, 2, 1], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 3, 4, 7, 0, 5, 8, 1, 2], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 9, 3, 4, 7, 0, 8, 2, 1], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,883 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 99.00)
2025-08-05 10:28:42,883 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:42,883 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 10
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,885 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [5, 8, 7, 3, 1, 0, 9, 2, 6, 4], 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': [3, 9, 0, 1, 5, 7, 4, 6, 2, 8], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 0, 1, 6, 7, 4, 5, 8, 2], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 0, 1, 5, 6, 7, 4, 2, 8], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,885 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 109.00)
2025-08-05 10:28:42,885 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-05 10:28:42,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,885 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,885 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 139.0
2025-08-05 10:28:42,891 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,891 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,891 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,892 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,892 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 0, 7, 3, 8, 4, 9, 5, 2], 'cur_cost': 94.0}, {'tour': array([2, 4, 5, 1, 9, 8, 7, 3, 0, 6], dtype=int64), 'cur_cost': 118.0}, {'tour': [7, 9, 2, 6, 8, 4, 3, 1, 0, 5], 'cur_cost': 111.0}, {'tour': [7, 9, 1, 0, 2, 6, 8, 4, 3, 5], 'cur_cost': 116.0}, {'tour': [2, 9, 3, 6, 1, 7, 8, 4, 5, 0], 'cur_cost': 107.0}, {'tour': [5, 6, 2, 1, 7, 3, 8, 9, 4, 0], 'cur_cost': 99.0}, {'tour': [5, 8, 7, 3, 1, 0, 9, 2, 6, 4], 'cur_cost': 109.0}, {'tour': array([3, 6, 9, 0, 7, 5, 2, 8, 1, 4], dtype=int64), 'cur_cost': 139.0}, {'tour': [6, 9, 1, 8, 4, 2, 0, 3, 5, 7], 'cur_cost': 134.0}, {'tour': [1, 3, 7, 8, 4, 6, 0, 9, 5, 2], 'cur_cost': 105.0}]
2025-08-05 10:28:42,893 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,893 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 129, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 129, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,893 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([3, 6, 9, 0, 7, 5, 2, 8, 1, 4], dtype=int64), 'cur_cost': 139.0, 'intermediate_solutions': [{'tour': array([8, 0, 9, 2, 5, 1, 7, 4, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 8, 0, 9, 5, 1, 7, 4, 6, 3]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 8, 0, 9, 1, 7, 4, 6, 3]), 'cur_cost': 137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 2, 8, 0, 5, 1, 7, 4, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 2, 8, 0, 1, 7, 4, 6, 3]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,893 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 139.00)
2025-08-05 10:28:42,893 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:42,893 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:42,894 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:42,894 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 119.0
2025-08-05 10:28:42,899 - ExploitationExpert - INFO - res_population_num: 4
2025-08-05 10:28:42,899 - ExploitationExpert - INFO - res_population_costs: [72.0, 72, 72, 72.0]
2025-08-05 10:28:42,899 - ExploitationExpert - INFO - res_populations: [array([0, 1, 6, 7, 2, 3, 8, 4, 5, 9], dtype=int64), array([0, 1, 6, 7, 2, 3, 4, 8, 9, 5], dtype=int64), array([0, 5, 9, 8, 4, 3, 7, 2, 1, 6], dtype=int64), array([0, 6, 1, 2, 7, 3, 8, 4, 5, 9], dtype=int64)]
2025-08-05 10:28:42,900 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:42,900 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 0, 7, 3, 8, 4, 9, 5, 2], 'cur_cost': 94.0}, {'tour': array([2, 4, 5, 1, 9, 8, 7, 3, 0, 6], dtype=int64), 'cur_cost': 118.0}, {'tour': [7, 9, 2, 6, 8, 4, 3, 1, 0, 5], 'cur_cost': 111.0}, {'tour': [7, 9, 1, 0, 2, 6, 8, 4, 3, 5], 'cur_cost': 116.0}, {'tour': [2, 9, 3, 6, 1, 7, 8, 4, 5, 0], 'cur_cost': 107.0}, {'tour': [5, 6, 2, 1, 7, 3, 8, 9, 4, 0], 'cur_cost': 99.0}, {'tour': [5, 8, 7, 3, 1, 0, 9, 2, 6, 4], 'cur_cost': 109.0}, {'tour': array([3, 6, 9, 0, 7, 5, 2, 8, 1, 4], dtype=int64), 'cur_cost': 139.0}, {'tour': array([6, 2, 4, 9, 8, 5, 7, 1, 0, 3], dtype=int64), 'cur_cost': 119.0}, {'tour': [1, 3, 7, 8, 4, 6, 0, 9, 5, 2], 'cur_cost': 105.0}]
2025-08-05 10:28:42,901 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:42,901 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 130, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 130, 'cache_hits': 0, 'similarity_calculations': 576, 'cache_hit_rate': 0.0, 'cache_size': 576}}
2025-08-05 10:28:42,901 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([6, 2, 4, 9, 8, 5, 7, 1, 0, 3], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([1, 9, 6, 8, 4, 2, 0, 3, 5, 7]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 9, 6, 4, 2, 0, 3, 5, 7]), 'cur_cost': 146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 1, 9, 6, 2, 0, 3, 5, 7]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 8, 1, 9, 4, 2, 0, 3, 5, 7]), 'cur_cost': 140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 8, 1, 9, 2, 0, 3, 5, 7]), 'cur_cost': 138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:42,901 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 119.00)
2025-08-05 10:28:42,901 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:42,901 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 10
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,902 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:42,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93.0, 路径长度: 10, 收集中间解: 3
2025-08-05 10:28:42,903 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [8, 4, 5, 7, 2, 1, 0, 6, 9, 3], 'cur_cost': 93.0, 'intermediate_solutions': [{'tour': [2, 3, 7, 8, 4, 6, 0, 9, 5, 1], 'cur_cost': 95.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 8, 9, 0, 6, 4, 5, 2], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 7, 8, 4, 6, 0, 5, 9, 2], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:42,903 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 93.00)
2025-08-05 10:28:42,903 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:42,903 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:42,904 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 0, 7, 3, 8, 4, 9, 5, 2], 'cur_cost': 94.0, 'intermediate_solutions': [{'tour': [6, 7, 2, 0, 4, 5, 9, 3, 1, 8], 'cur_cost': 116.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 7, 9, 5, 1, 0, 2, 3, 4, 8], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 2, 0, 1, 5, 9, 3, 7, 4, 8], 'cur_cost': 109.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([2, 4, 5, 1, 9, 8, 7, 3, 0, 6], dtype=int64), 'cur_cost': 118.0, 'intermediate_solutions': [{'tour': array([5, 1, 8, 0, 7, 2, 4, 3, 9, 6]), 'cur_cost': 129.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([0, 5, 1, 8, 7, 2, 4, 3, 9, 6]), 'cur_cost': 114.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([7, 0, 5, 1, 8, 2, 4, 3, 9, 6]), 'cur_cost': 126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([8, 0, 5, 1, 7, 2, 4, 3, 9, 6]), 'cur_cost': 116.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([8, 7, 0, 5, 1, 2, 4, 3, 9, 6]), 'cur_cost': 120.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 2, 6, 8, 4, 3, 1, 0, 5], 'cur_cost': 111.0, 'intermediate_solutions': [{'tour': [0, 3, 5, 8, 4, 6, 9, 1, 7, 2], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 5, 8, 4, 9, 6, 1, 2, 7], 'cur_cost': 102.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 3, 5, 8, 9, 6, 1, 7, 2], 'cur_cost': 103.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [7, 9, 1, 0, 2, 6, 8, 4, 3, 5], 'cur_cost': 116.0, 'intermediate_solutions': [{'tour': [1, 4, 9, 0, 8, 3, 5, 2, 6, 7], 'cur_cost': 122.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 0, 9, 4, 1, 3, 7, 2, 6, 5], 'cur_cost': 113.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 4, 9, 0, 8, 3, 7, 6, 5, 2], 'cur_cost': 118.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 9, 3, 6, 1, 7, 8, 4, 5, 0], 'cur_cost': 107.0, 'intermediate_solutions': [{'tour': [5, 7, 2, 3, 8, 4, 0, 9, 6, 1], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 7, 2, 4, 8, 3, 9, 0, 6, 1], 'cur_cost': 104.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 7, 2, 8, 3, 4, 9, 0, 6, 1], 'cur_cost': 99.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 2, 1, 7, 3, 8, 9, 4, 0], 'cur_cost': 99.0, 'intermediate_solutions': [{'tour': [6, 9, 3, 8, 7, 0, 5, 4, 2, 1], 'cur_cost': 105.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [6, 9, 3, 4, 7, 0, 5, 8, 1, 2], 'cur_cost': 112.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [6, 5, 9, 3, 4, 7, 0, 8, 2, 1], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 7, 3, 1, 0, 9, 2, 6, 4], 'cur_cost': 109.0, 'intermediate_solutions': [{'tour': [3, 9, 0, 1, 5, 7, 4, 6, 2, 8], 'cur_cost': 123.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [3, 9, 0, 1, 6, 7, 4, 5, 8, 2], 'cur_cost': 98.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [3, 9, 0, 1, 5, 6, 7, 4, 2, 8], 'cur_cost': 121.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([3, 6, 9, 0, 7, 5, 2, 8, 1, 4], dtype=int64), 'cur_cost': 139.0, 'intermediate_solutions': [{'tour': array([8, 0, 9, 2, 5, 1, 7, 4, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([2, 8, 0, 9, 5, 1, 7, 4, 6, 3]), 'cur_cost': 119.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([5, 2, 8, 0, 9, 1, 7, 4, 6, 3]), 'cur_cost': 137.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([9, 2, 8, 0, 5, 1, 7, 4, 6, 3]), 'cur_cost': 135.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([9, 5, 2, 8, 0, 1, 7, 4, 6, 3]), 'cur_cost': 128.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([6, 2, 4, 9, 8, 5, 7, 1, 0, 3], dtype=int64), 'cur_cost': 119.0, 'intermediate_solutions': [{'tour': array([1, 9, 6, 8, 4, 2, 0, 3, 5, 7]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([8, 1, 9, 6, 4, 2, 0, 3, 5, 7]), 'cur_cost': 146.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([4, 8, 1, 9, 6, 2, 0, 3, 5, 7]), 'cur_cost': 132.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([6, 8, 1, 9, 4, 2, 0, 3, 5, 7]), 'cur_cost': 140.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([6, 4, 8, 1, 9, 2, 0, 3, 5, 7]), 'cur_cost': 138.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [8, 4, 5, 7, 2, 1, 0, 6, 9, 3], 'cur_cost': 93.0, 'intermediate_solutions': [{'tour': [2, 3, 7, 8, 4, 6, 0, 9, 5, 1], 'cur_cost': 95.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [1, 3, 7, 8, 9, 0, 6, 4, 5, 2], 'cur_cost': 111.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [1, 3, 7, 8, 4, 6, 0, 5, 9, 2], 'cur_cost': 101.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:42,904 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:42,904 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:42,905 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=93.000, 多样性=0.920
2025-08-05 10:28:42,905 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:42,906 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:42,906 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:42,906 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0022755022755022677, 'best_improvement': -0.02197802197802198}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.004854368932038987}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.019919012355451463, 'recent_improvements': [0.00255726670259133, -0.03156439332198039, -0.0372807580083116], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 4, 'new_count': 4, 'count_change': 0, 'old_best_cost': 72.0, 'new_best_cost': 72.0, 'quality_improvement': 0.0, 'old_diversity': 0.6666666666666667, 'new_diversity': 0.6666666666666667, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:42,906 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:42,908 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry4_10_solution.json
2025-08-05 10:28:42,908 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\geometry4_10_20250805_102842.solution
2025-08-05 10:28:42,908 - __main__ - INFO - 实例执行完成 - 运行时间: 1.33s, 最佳成本: 72.0
2025-08-05 10:28:42,908 - __main__ - INFO - 实例 geometry4_10 处理完成
