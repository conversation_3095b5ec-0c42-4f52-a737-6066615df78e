"""
Hybrid strategy implementations for the intelligent strategy selection system.

This module implements hybrid strategies that combine exploration and exploitation
techniques for adaptive and flexible search behavior.
"""

import random
import time
import numpy as np
from typing import List, Dict, Optional, Any, Callable, Tuple
from abc import ABC, abstractmethod
import logging

from ..core.individual_state import IndividualState, IndividualContext
from ..core.data_structures import (
    StrategyAssignment, ExecutionResult, ExplorationParameters, 
    ExploitationParameters, StrategyType, ExecutionStatus
)
from ..core.strategy_interfaces import StrategyExecutionInterface
from .exploration_strategies import ExplorationStrategy
from .exploitation_strategies import ExploitationStrategy


class HybridStrategy(ABC):
    """
    Abstract base class for hybrid strategies.
    
    Hybrid strategies combine exploration and exploitation techniques
    to provide adaptive search behavior.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the hybrid strategy."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.execution_count = 0
        self.success_count = 0
        self.exploration_count = 0
        self.exploitation_count = 0
    
    @abstractmethod
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """
        Execute the hybrid strategy.
        
        Args:
            assignment: Strategy assignment with parameters
            context: Individual context for execution
            fitness_function: Fitness evaluation function
            
        Returns:
            Execution result
        """
        pass
    
    def get_success_rate(self) -> float:
        """Get the success rate of this strategy."""
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count


class AdaptiveHybridStrategy(HybridStrategy):
    """
    Adaptive hybrid strategy that switches between exploration and exploitation.
    
    This strategy dynamically decides between exploration and exploitation
    based on the current context and recent performance.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute adaptive hybrid strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Analyze context to determine strategy mix
            strategy_plan = self._analyze_and_plan(assignment, context)
            
            # Execute planned strategy sequence
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            total_operations = 0
            executed_phases = []
            
            for phase in strategy_plan:
                phase_result = self._execute_phase(
                    phase, best_solution, best_fitness, fitness_function
                )
                
                total_operations += phase_result['operations']
                executed_phases.append(phase['type'])
                
                if phase_result['improved']:
                    best_solution = phase_result['solution']
                    best_fitness = phase_result['fitness']
                
                # Update counts
                if phase['type'] == 'exploration':
                    self.exploration_count += 1
                else:
                    self.exploitation_count += 1
                
                # Check for significant improvement
                improvement = context.current_fitness - best_fitness
                if improvement > 0.02 * abs(context.current_fitness):
                    break  # Early termination on good improvement
                
                # Check time budget
                if time.time() - start_time > assignment.time_budget * 0.9:
                    break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=len(executed_phases),
                operations_count=total_operations,
                metadata={
                    'strategy_plan': [p['type'] for p in strategy_plan],
                    'executed_phases': executed_phases,
                    'exploration_ratio': self.exploration_count / max(1, self.execution_count),
                    'exploitation_ratio': self.exploitation_count / max(1, self.execution_count)
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in adaptive hybrid strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _analyze_and_plan(self, 
                         assignment: StrategyAssignment,
                         context: IndividualContext) -> List[Dict[str, Any]]:
        """Analyze context and plan strategy sequence."""
        plan = []
        
        # Get parameters
        exploration_params = assignment.exploration_params or ExplorationParameters()
        exploitation_params = assignment.exploitation_params or ExploitationParameters()
        
        # Decision factors
        stagnation_level = context.individual_state.stagnation_level.value
        stagnation_duration = context.individual_state.stagnation_duration
        improvement_potential = context.improvement_potential
        local_ruggedness = context.local_ruggedness
        gradient_strength = context.gradient_strength
        
        # Decision logic
        exploration_score = self._calculate_exploration_score(
            stagnation_level, stagnation_duration, local_ruggedness, gradient_strength
        )
        
        exploitation_score = self._calculate_exploitation_score(
            improvement_potential, local_ruggedness, gradient_strength
        )
        
        # Plan strategy sequence based on scores
        if exploration_score > exploitation_score:
            # Start with exploration
            if exploration_score > 0.7:
                plan.append({
                    'type': 'exploration',
                    'intensity': 'high',
                    'params': exploration_params,
                    'time_fraction': 0.6
                })
                plan.append({
                    'type': 'exploitation',
                    'intensity': 'moderate',
                    'params': exploitation_params,
                    'time_fraction': 0.4
                })
            else:
                plan.append({
                    'type': 'exploration',
                    'intensity': 'moderate',
                    'params': exploration_params,
                    'time_fraction': 0.5
                })
                plan.append({
                    'type': 'exploitation',
                    'intensity': 'moderate',
                    'params': exploitation_params,
                    'time_fraction': 0.5
                })
        else:
            # Start with exploitation
            if exploitation_score > 0.7:
                plan.append({
                    'type': 'exploitation',
                    'intensity': 'high',
                    'params': exploitation_params,
                    'time_fraction': 0.7
                })
                plan.append({
                    'type': 'exploration',
                    'intensity': 'low',
                    'params': exploration_params,
                    'time_fraction': 0.3
                })
            else:
                plan.append({
                    'type': 'exploitation',
                    'intensity': 'moderate',
                    'params': exploitation_params,
                    'time_fraction': 0.6
                })
                plan.append({
                    'type': 'exploration',
                    'intensity': 'moderate',
                    'params': exploration_params,
                    'time_fraction': 0.4
                })
        
        return plan
    
    def _calculate_exploration_score(self, 
                                   stagnation_level: str,
                                   stagnation_duration: int,
                                   local_ruggedness: float,
                                   gradient_strength: float) -> float:
        """Calculate exploration necessity score."""
        score = 0.0
        
        # Stagnation factor
        stagnation_scores = {
            'NONE': 0.1,
            'LOW': 0.3,
            'MODERATE': 0.6,
            'HIGH': 0.8,
            'CRITICAL': 1.0
        }
        score += stagnation_scores.get(stagnation_level, 0.5) * 0.4
        
        # Duration factor
        duration_score = min(1.0, stagnation_duration / 20.0)
        score += duration_score * 0.3
        
        # Landscape factors
        score += local_ruggedness * 0.2  # High ruggedness favors exploration
        score += (1.0 - gradient_strength) * 0.1  # Weak gradients favor exploration
        
        return min(1.0, score)
    
    def _calculate_exploitation_score(self, 
                                    improvement_potential: float,
                                    local_ruggedness: float,
                                    gradient_strength: float) -> float:
        """Calculate exploitation opportunity score."""
        score = 0.0
        
        # Improvement potential factor
        score += improvement_potential * 0.5
        
        # Landscape factors
        score += (1.0 - local_ruggedness) * 0.3  # Smooth landscape favors exploitation
        score += gradient_strength * 0.2  # Strong gradients favor exploitation
        
        return min(1.0, score)
    
    def _execute_phase(self, 
                      phase: Dict[str, Any],
                      current_solution: List[int],
                      current_fitness: float,
                      fitness_function: Callable) -> Dict[str, Any]:
        """Execute a single phase of the hybrid strategy."""
        phase_type = phase['type']
        intensity = phase['intensity']
        time_fraction = phase['time_fraction']
        
        if phase_type == 'exploration':
            return self._execute_exploration_phase(
                current_solution, current_fitness, fitness_function,
                phase['params'], intensity, time_fraction
            )
        else:  # exploitation
            return self._execute_exploitation_phase(
                current_solution, current_fitness, fitness_function,
                phase['params'], intensity, time_fraction
            )
    
    def _execute_exploration_phase(self, 
                                 solution: List[int],
                                 fitness: float,
                                 fitness_function: Callable,
                                 params: ExplorationParameters,
                                 intensity: str,
                                 time_fraction: float) -> Dict[str, Any]:
        """Execute exploration phase."""
        best_solution = solution.copy()
        best_fitness = fitness
        operations = 0
        
        # Adjust parameters based on intensity
        intensity_multiplier = {'low': 0.5, 'moderate': 1.0, 'high': 1.5}[intensity]
        num_perturbations = max(3, int(10 * params.exploration_intensity * intensity_multiplier))
        
        for _ in range(num_perturbations):
            # Generate perturbation based on intensity
            if intensity == 'high':
                perturbed = self._strong_perturbation(solution, params)
            elif intensity == 'moderate':
                perturbed = self._moderate_perturbation(solution, params)
            else:  # low
                perturbed = self._light_perturbation(solution, params)
            
            new_fitness = fitness_function(perturbed)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = perturbed
                best_fitness = new_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < fitness,
            'operations': operations
        }
    
    def _execute_exploitation_phase(self, 
                                  solution: List[int],
                                  fitness: float,
                                  fitness_function: Callable,
                                  params: ExploitationParameters,
                                  intensity: str,
                                  time_fraction: float) -> Dict[str, Any]:
        """Execute exploitation phase."""
        best_solution = solution.copy()
        best_fitness = fitness
        operations = 0
        
        # Adjust parameters based on intensity
        if intensity == 'high':
            search_depth = max(15, params.search_depth)
            neighborhood_size = 15
        elif intensity == 'moderate':
            search_depth = max(8, params.search_depth)
            neighborhood_size = 10
        else:  # low
            search_depth = max(3, params.search_depth // 2)
            neighborhood_size = 5
        
        # Local search
        no_improvement_count = 0
        
        for iteration in range(search_depth):
            # Generate neighborhood
            neighbors = self._generate_local_neighborhood(
                best_solution, neighborhood_size
            )
            
            # Evaluate neighbors
            improved = False
            for neighbor in neighbors:
                new_fitness = fitness_function(neighbor)
                operations += 1
                
                if new_fitness < best_fitness:
                    best_solution = neighbor
                    best_fitness = new_fitness
                    improved = True
                    no_improvement_count = 0
                    break
            
            if not improved:
                no_improvement_count += 1
                if no_improvement_count >= params.patience:
                    break
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': best_fitness < fitness,
            'operations': operations
        }
    
    def _strong_perturbation(self, solution: List[int], params: ExplorationParameters) -> List[int]:
        """Generate strong perturbation."""
        perturbed = solution.copy()
        n = len(solution)
        
        num_changes = max(2, int(n * params.perturbation_strength * 0.5))
        
        for _ in range(num_changes):
            i, j = sorted(random.sample(range(n), 2))
            perturbed[i:j+1] = perturbed[i:j+1][::-1]
        
        return perturbed
    
    def _moderate_perturbation(self, solution: List[int], params: ExplorationParameters) -> List[int]:
        """Generate moderate perturbation."""
        perturbed = solution.copy()
        n = len(solution)
        
        num_changes = max(1, int(n * params.perturbation_strength * 0.3))
        
        for _ in range(num_changes):
            if random.random() < 0.6:
                # 2-opt move
                i, j = sorted(random.sample(range(n), 2))
                if j - i > 1:
                    perturbed[i:j] = perturbed[i:j][::-1]
            else:
                # Swap move
                i, j = random.sample(range(n), 2)
                perturbed[i], perturbed[j] = perturbed[j], perturbed[i]
        
        return perturbed
    
    def _light_perturbation(self, solution: List[int], params: ExplorationParameters) -> List[int]:
        """Generate light perturbation."""
        perturbed = solution.copy()
        n = len(solution)
        
        # Single small move
        if random.random() < params.mutation_probability:
            # Adjacent swap
            i = random.randint(0, n-2)
            perturbed[i], perturbed[i+1] = perturbed[i+1], perturbed[i]
        else:
            # Small reversal
            i = random.randint(0, n-3)
            j = i + random.randint(1, 2)
            perturbed[i:j+1] = perturbed[i:j+1][::-1]
        
        return perturbed
    
    def _generate_local_neighborhood(self, solution: List[int], size: int) -> List[List[int]]:
        """Generate local neighborhood for exploitation."""
        neighbors = []
        n = len(solution)
        
        for _ in range(size):
            neighbor = solution.copy()
            
            # Local moves only
            move_type = random.random()
            if move_type < 0.4:
                # Adjacent swap
                i = random.randint(0, n-2)
                neighbor[i], neighbor[i+1] = neighbor[i+1], neighbor[i]
            elif move_type < 0.8:
                # Small 2-opt
                i = random.randint(0, n-4)
                j = i + random.randint(2, min(4, n-i-1))
                neighbor[i:j] = neighbor[i:j][::-1]
            else:
                # Random swap
                i, j = random.sample(range(n), 2)
                neighbor[i], neighbor[j] = neighbor[j], neighbor[i]
            
            neighbors.append(neighbor)
        
        return neighbors


class IterativeHybridStrategy(HybridStrategy):
    """
    Iterative hybrid strategy with alternating exploration and exploitation.
    
    This strategy alternates between exploration and exploitation phases
    in a structured manner to balance search behavior.
    """
    
    def execute(self, 
               assignment: StrategyAssignment,
               context: IndividualContext,
               fitness_function: Callable) -> ExecutionResult:
        """Execute iterative hybrid strategy."""
        start_time = time.time()
        self.execution_count += 1
        
        try:
            # Plan alternating phases
            max_phases = 6
            exploration_params = assignment.exploration_params or ExplorationParameters()
            exploitation_params = assignment.exploitation_params or ExploitationParameters()
            
            # Execute alternating phases
            best_solution = context.current_solution.copy()
            best_fitness = context.current_fitness
            total_operations = 0
            phase_results = []
            
            for phase_idx in range(max_phases):
                # Alternate between exploration and exploitation
                is_exploration = (phase_idx % 2 == 0)
                
                if is_exploration:
                    phase_result = self._exploration_iteration(
                        best_solution, best_fitness, fitness_function, exploration_params
                    )
                    self.exploration_count += 1
                else:
                    phase_result = self._exploitation_iteration(
                        best_solution, best_fitness, fitness_function, exploitation_params
                    )
                    self.exploitation_count += 1
                
                total_operations += phase_result['operations']
                phase_results.append({
                    'type': 'exploration' if is_exploration else 'exploitation',
                    'improved': phase_result['improved'],
                    'improvement': phase_result.get('improvement', 0.0)
                })
                
                if phase_result['improved']:
                    best_solution = phase_result['solution']
                    best_fitness = phase_result['fitness']
                
                # Early termination conditions
                if phase_result.get('improvement', 0.0) > 0.01 * abs(context.current_fitness):
                    break  # Significant improvement found
                
                if time.time() - start_time > assignment.time_budget * 0.9:
                    break  # Time budget nearly exhausted
                
                # Adaptive termination: if last 2 phases showed no improvement
                if len(phase_results) >= 2 and not any(p['improved'] for p in phase_results[-2:]):
                    break
            
            # Determine success
            final_improvement = context.current_fitness - best_fitness
            success = final_improvement > 1e-6
            
            if success:
                self.success_count += 1
            
            # Create execution result
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.COMPLETED,
                execution_time=execution_time,
                success=success,
                improvement=final_improvement,
                old_fitness=context.current_fitness,
                new_fitness=best_fitness,
                iterations_performed=len(phase_results),
                operations_count=total_operations,
                metadata={
                    'phase_results': phase_results,
                    'phases_completed': len(phase_results),
                    'exploration_phases': sum(1 for p in phase_results if p['type'] == 'exploration'),
                    'exploitation_phases': sum(1 for p in phase_results if p['type'] == 'exploitation')
                }
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in iterative hybrid strategy: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                individual_id=assignment.individual_id,
                strategy_type=assignment.strategy_type,
                status=ExecutionStatus.FAILED,
                execution_time=execution_time,
                success=False,
                old_fitness=context.current_fitness,
                new_fitness=context.current_fitness,
                error_message=str(e)
            )
    
    def _exploration_iteration(self, 
                             solution: List[int],
                             fitness: float,
                             fitness_function: Callable,
                             params: ExplorationParameters) -> Dict[str, Any]:
        """Execute one exploration iteration."""
        best_solution = solution.copy()
        best_fitness = fitness
        operations = 0
        
        # Generate multiple perturbations
        num_perturbations = max(3, int(8 * params.exploration_intensity))
        
        for _ in range(num_perturbations):
            # Moderate perturbation
            perturbed = self._moderate_perturbation(solution, params)
            new_fitness = fitness_function(perturbed)
            operations += 1
            
            if new_fitness < best_fitness:
                best_solution = perturbed
                best_fitness = new_fitness
        
        improvement = fitness - best_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': improvement > 1e-6,
            'improvement': improvement,
            'operations': operations
        }
    
    def _exploitation_iteration(self, 
                              solution: List[int],
                              fitness: float,
                              fitness_function: Callable,
                              params: ExploitationParameters) -> Dict[str, Any]:
        """Execute one exploitation iteration."""
        best_solution = solution.copy()
        best_fitness = fitness
        operations = 0
        
        # Local search with 2-opt
        improved = True
        local_iterations = 0
        max_local_iterations = max(5, params.search_depth // 2)
        
        while improved and local_iterations < max_local_iterations:
            improved = False
            local_iterations += 1
            
            # Generate 2-opt neighborhood
            neighbors = self._generate_local_neighborhood(best_solution, 8)
            
            for neighbor in neighbors:
                new_fitness = fitness_function(neighbor)
                operations += 1
                
                if new_fitness < best_fitness:
                    best_solution = neighbor
                    best_fitness = new_fitness
                    improved = True
                    break
        
        improvement = fitness - best_fitness
        
        return {
            'solution': best_solution,
            'fitness': best_fitness,
            'improved': improvement > 1e-6,
            'improvement': improvement,
            'operations': operations
        }
