2025-08-05 10:28:56,452 - __main__ - INFO - composite7_42 开始进化第 1 代
2025-08-05 10:28:56,453 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:56,454 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:56,457 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=14676.000, 多样性=0.938
2025-08-05 10:28:56,459 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:56,462 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.938
2025-08-05 10:28:56,465 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:56,468 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:56,469 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:56,469 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:56,469 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:56,493 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -13114.200, 聚类评分: 0.000, 覆盖率: 0.108, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:56,493 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:56,494 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:56,494 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite7_42
2025-08-05 10:28:56,501 - visualization.landscape_visualizer - INFO - 插值约束: 20 个点被约束到最小值 14676.00
2025-08-05 10:28:56,503 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.5%, 梯度: 2652.65 → 2506.06
2025-08-05 10:28:56,627 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\landscape_composite7_42_iter_91_20250805_102856.html
2025-08-05 10:28:56,679 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\dashboard_composite7_42_iter_91_20250805_102856.html
2025-08-05 10:28:56,679 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 91
2025-08-05 10:28:56,679 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:56,679 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2104秒
2025-08-05 10:28:56,679 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 182, 'max_size': 500, 'hits': 0, 'misses': 182, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 606, 'misses': 316, 'hit_rate': 0.6572668112798264, 'evictions': 216, 'ttl': 7200}}
2025-08-05 10:28:56,680 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -13114.2, 'local_optima_density': 0.1, 'gradient_variance': 2488830546.4399996, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1084, 'fitness_entropy': 0.9477309221191609, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -13114.200)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.108)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360936.4938464, 'performance_metrics': {}}}
2025-08-05 10:28:56,680 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:56,680 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:56,680 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:56,680 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:56,681 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,681 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:56,681 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,681 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:56,681 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:56,681 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,682 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:56,682 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:56,682 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:56,682 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:56,682 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:56,682 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,684 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,684 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24969.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,684 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24969.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,684 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 24969.00)
2025-08-05 10:28:56,684 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:56,685 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:56,685 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,686 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:56,686 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,686 - ExplorationExpert - INFO - 探索路径生成完成，成本: 69271.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,686 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 69271.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,687 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 69271.00)
2025-08-05 10:28:56,687 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:56,687 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:56,687 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,691 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:56,691 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51640.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,691 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 51640.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,691 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 51640.00)
2025-08-05 10:28:56,691 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:56,692 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:56,692 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,693 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,693 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18679.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,693 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18679.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,694 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 18679.00)
2025-08-05 10:28:56,694 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:56,694 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:56,694 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:56,694 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 98551.0
2025-08-05 10:28:56,704 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:28:56,704 - ExploitationExpert - INFO - res_population_costs: [14454.0, 14454, 14454, 14454, 14442]
2025-08-05 10:28:56,704 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64)]
2025-08-05 10:28:56,706 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:56,707 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24969.0}, {'tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 69271.0}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 51640.0}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18679.0}, {'tour': array([ 5,  1,  6,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16], dtype=int64), 'cur_cost': 98551.0}, {'tour': array([39, 21, 29, 35, 26, 36, 37,  4, 24, 14, 31, 40, 38, 17,  3, 30, 27,
       34, 28, 16, 11, 33, 10, 18,  0, 41,  5, 15, 20, 12,  7,  8, 13, 25,
       32,  9,  6,  2, 19, 23, 22,  1], dtype=int64), 'cur_cost': 96263.0}, {'tour': array([18, 31, 23,  5,  3, 15,  4, 39, 12, 17, 38, 26,  6, 13, 20, 16, 36,
        2, 30, 37,  1, 11, 41, 34,  0,  7, 24, 19, 35, 21, 22, 14, 28, 27,
       40, 25,  9, 10, 33, 32, 29,  8], dtype=int64), 'cur_cost': 107410.0}, {'tour': array([12, 33, 14, 26, 29, 24, 31,  3,  1, 37,  0, 34, 19, 41, 20,  6, 17,
       16, 38,  2, 22, 28, 36, 35, 27,  5, 23, 40, 13,  9, 39, 11, 15,  8,
       18, 30, 25, 21, 10,  4, 32,  7], dtype=int64), 'cur_cost': 106800.0}, {'tour': array([38,  2, 31, 40,  1, 20, 33, 32,  7,  0, 39, 17, 23, 26,  3, 22, 30,
       24, 16, 11, 18,  4, 14, 36, 28,  5,  9,  6, 34, 35, 19, 15, 21, 41,
       27, 29, 25, 10, 37, 13,  8, 12], dtype=int64), 'cur_cost': 100847.0}, {'tour': array([11, 35,  9, 36, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 128058.0}]
2025-08-05 10:28:56,709 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:56,709 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 235, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 235, 'cache_hits': 0, 'similarity_calculations': 1122, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:56,709 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 5,  1,  6,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16], dtype=int64), 'cur_cost': 98551.0, 'intermediate_solutions': [{'tour': array([36, 33, 18,  9, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 36, 33, 18, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 114843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37,  9, 36, 33, 18, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18,  9, 36, 33, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108851.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 37,  9, 36, 33, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 114834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:56,710 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 98551.00)
2025-08-05 10:28:56,710 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:56,710 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:56,710 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,711 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,711 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,712 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19371.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,712 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19371.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,712 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 19371.00)
2025-08-05 10:28:56,712 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:56,712 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:56,712 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,715 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:56,716 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55003.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,716 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55003.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,716 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 55003.00)
2025-08-05 10:28:56,716 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:56,716 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:56,716 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,718 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,718 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,718 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25022.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,718 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25022.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,718 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 25022.00)
2025-08-05 10:28:56,718 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:56,719 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:56,719 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,720 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,720 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,720 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23949.0, 路径长度: 42, 收集中间解: 0
2025-08-05 10:28:56,720 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23949.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,720 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 23949.00)
2025-08-05 10:28:56,720 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:56,720 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:56,721 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:56,721 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108891.0
2025-08-05 10:28:56,732 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:28:56,732 - ExploitationExpert - INFO - res_population_costs: [14454.0, 14454, 14454, 14454, 14442, 14419.0, 14419]
2025-08-05 10:28:56,733 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:56,735 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:56,735 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24969.0}, {'tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 69271.0}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 51640.0}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18679.0}, {'tour': array([ 5,  1,  6,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16], dtype=int64), 'cur_cost': 98551.0}, {'tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19371.0}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55003.0}, {'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25022.0}, {'tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23949.0}, {'tour': array([30,  5, 14, 16, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39], dtype=int64), 'cur_cost': 108891.0}]
2025-08-05 10:28:56,736 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:56,736 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 236, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 236, 'cache_hits': 0, 'similarity_calculations': 1123, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:56,736 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([30,  5, 14, 16, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39], dtype=int64), 'cur_cost': 108891.0, 'intermediate_solutions': [{'tour': array([ 9, 35, 11, 36, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 128095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  9, 35, 11, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 127956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 36,  9, 35, 11, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 127717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 36,  9, 35, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 128067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 14, 36,  9, 35, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 123734.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:56,737 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 108891.00)
2025-08-05 10:28:56,737 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:56,737 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:56,738 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24969.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 69271.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 51640.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18679.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  1,  6,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16], dtype=int64), 'cur_cost': 98551.0, 'intermediate_solutions': [{'tour': array([36, 33, 18,  9, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108804.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 9, 36, 33, 18, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 114843.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([37,  9, 36, 33, 18, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108092.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18,  9, 36, 33, 37, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 108851.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 37,  9, 36, 33, 21, 24, 34,  6, 16, 27,  2, 39, 41, 32, 13, 23,
        0,  8,  5,  3, 26, 25,  1, 15, 17, 10, 31, 11, 22, 38, 14, 19,  4,
       29,  7, 20, 30, 12, 35, 28, 40], dtype=int64), 'cur_cost': 114834.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19371.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55003.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25022.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23949.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([30,  5, 14, 16, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39], dtype=int64), 'cur_cost': 108891.0, 'intermediate_solutions': [{'tour': array([ 9, 35, 11, 36, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 128095.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([36,  9, 35, 11, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 127956.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 36,  9, 35, 11, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 127717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 36,  9, 35, 14, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 128067.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 14, 36,  9, 35, 25, 15, 39,  2, 24, 16, 33, 29, 13, 26, 19, 22,
       12, 32, 30, 10,  8, 27,  4, 21, 18, 23, 38, 28,  1, 37, 34, 41,  3,
        6, 17,  0, 31,  7,  5, 40, 20], dtype=int64), 'cur_cost': 123734.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:56,738 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:56,739 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:56,741 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=18679.000, 多样性=0.881
2025-08-05 10:28:56,741 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:56,741 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:56,741 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:56,742 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.08650776300757235, 'best_improvement': -0.27275824475333876}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0603496897913141}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.1686543859044732, 'recent_improvements': [-0.28180788837893544, 0.08558460179050871, 0.055500883430010965], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 14419.0, 'new_best_cost': 14419.0, 'quality_improvement': 0.0, 'old_diversity': 0.7913832199546486, 'new_diversity': 0.7913832199546486, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:56,742 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:56,742 - __main__ - INFO - composite7_42 开始进化第 2 代
2025-08-05 10:28:56,742 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:56,743 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:56,743 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=18679.000, 多样性=0.881
2025-08-05 10:28:56,743 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:56,745 - PathExpert - INFO - 路径结构分析完成: 公共边数量=10, 路径相似性=0.881
2025-08-05 10:28:56,745 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:56,747 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.791
2025-08-05 10:28:56,748 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:56,749 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:56,749 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:28:56,749 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:28:56,787 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.176, 适应度梯度: -9259.729, 聚类评分: 0.000, 覆盖率: 0.110, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:56,788 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:56,788 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:56,788 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite7_42
2025-08-05 10:28:56,794 - visualization.landscape_visualizer - INFO - 插值约束: 73 个点被约束到最小值 14419.00
2025-08-05 10:28:56,797 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.8%, 梯度: 2940.94 → 2682.62
2025-08-05 10:28:56,923 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\landscape_composite7_42_iter_92_20250805_102856.html
2025-08-05 10:28:56,978 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\dashboard_composite7_42_iter_92_20250805_102856.html
2025-08-05 10:28:56,978 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 92
2025-08-05 10:28:56,978 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:56,978 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2299秒
2025-08-05 10:28:56,979 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.17647058823529413, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9259.729411764705, 'local_optima_density': 0.17647058823529413, 'gradient_variance': 729860472.573841, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.11, 'fitness_entropy': 0.7585674225535729, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9259.729)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.110)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360936.7889535, 'performance_metrics': {}}}
2025-08-05 10:28:56,979 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:56,979 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:56,979 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:56,979 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:56,979 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,979 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:56,979 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,980 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:56,980 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:56,980 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:56,980 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:56,980 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:56,980 - experts.management.collaboration_manager - INFO - 识别精英个体: {3, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:56,980 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:56,980 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:56,981 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,983 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:56,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,983 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,984 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,984 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56030.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:56,984 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56030.0, 'intermediate_solutions': [{'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 39, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 3, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 43423.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 41, 34, 35, 33, 40, 36, 37, 32, 38, 39, 23, 31, 28, 30, 25, 27, 26], 'cur_cost': 25176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 28, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 33085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,984 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 56030.00)
2025-08-05 10:28:56,984 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:56,984 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:56,984 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,985 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:56,985 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,986 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,986 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,986 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,986 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98242.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:56,986 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98242.0, 'intermediate_solutions': [{'tour': [19, 14, 16, 28, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 20, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 74145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 30, 12, 29, 4, 1, 13, 24, 18, 17, 41, 26, 40, 39, 15, 21, 35, 27, 32, 34, 6], 'cur_cost': 68416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 16, 12, 30, 6], 'cur_cost': 69595.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,987 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 98242.00)
2025-08-05 10:28:56,987 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:56,987 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:56,987 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,988 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:56,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,988 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,989 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21974.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:56,989 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21974.0, 'intermediate_solutions': [{'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 28, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 15, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 61599.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 36, 37, 33, 38, 35, 34, 41, 26, 27, 7, 16, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 57945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 18, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 12, 13], 'cur_cost': 56914.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,989 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21974.00)
2025-08-05 10:28:56,989 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:56,989 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:56,989 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:56,990 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:56,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,990 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,991 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:56,991 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102886.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:56,991 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [29, 6, 9, 37, 14, 26, 35, 1, 12, 20, 22, 40, 11, 23, 21, 16, 28, 33, 13, 10, 25, 2, 4, 30, 8, 0, 3, 39, 41, 32, 7, 18, 19, 17, 27, 5, 38, 24, 36, 15, 34, 31], 'cur_cost': 102886.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 28, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 11, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 34741.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 28, 30, 25, 27, 26, 24, 29, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 30, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 26861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:56,991 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 102886.00)
2025-08-05 10:28:56,991 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:56,991 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:56,991 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:56,992 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 110444.0
2025-08-05 10:28:57,003 - ExploitationExpert - INFO - res_population_num: 8
2025-08-05 10:28:57,003 - ExploitationExpert - INFO - res_population_costs: [14419.0, 14419, 14442, 14454.0, 14454, 14454, 14454, 14419.0]
2025-08-05 10:28:57,003 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64)]
2025-08-05 10:28:57,006 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,007 - ExploitationExpert - INFO - populations: [{'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56030.0}, {'tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98242.0}, {'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21974.0}, {'tour': [29, 6, 9, 37, 14, 26, 35, 1, 12, 20, 22, 40, 11, 23, 21, 16, 28, 33, 13, 10, 25, 2, 4, 30, 8, 0, 3, 39, 41, 32, 7, 18, 19, 17, 27, 5, 38, 24, 36, 15, 34, 31], 'cur_cost': 102886.0}, {'tour': array([ 7, 41, 40,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22], dtype=int64), 'cur_cost': 110444.0}, {'tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19371.0}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55003.0}, {'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25022.0}, {'tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23949.0}, {'tour': [30, 5, 14, 16, 23, 32, 25, 6, 1, 10, 24, 35, 28, 40, 0, 29, 20, 26, 31, 34, 9, 22, 37, 33, 17, 38, 19, 15, 18, 3, 7, 8, 36, 11, 41, 12, 21, 13, 4, 27, 2, 39], 'cur_cost': 108891.0}]
2025-08-05 10:28:57,007 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,007 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 237, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 237, 'cache_hits': 0, 'similarity_calculations': 1125, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,008 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 7, 41, 40,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22], dtype=int64), 'cur_cost': 110444.0, 'intermediate_solutions': [{'tour': array([ 6,  1,  5,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 94918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  6,  1,  5, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  4,  6,  1,  5, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  4,  6,  1, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98635.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 14,  4,  6,  1, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 102899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,008 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 110444.00)
2025-08-05 10:28:57,008 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:57,008 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:57,008 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,009 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19316.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,010 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19316.0, 'intermediate_solutions': [{'tour': [0, 15, 17, 8, 7, 10, 11, 39, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 13, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 35252.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 5, 1, 2, 3, 4, 20, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24263.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 17, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24705.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,011 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 19316.00)
2025-08-05 10:28:57,011 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:57,011 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:57,011 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,012 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,013 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,013 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21836.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,013 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21836.0, 'intermediate_solutions': [{'tour': [35, 38, 36, 41, 25, 26, 28, 30, 15, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 17, 8, 5, 1, 23, 34], 'cur_cost': 54974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 31, 33, 32, 40, 39, 24, 0, 3, 11, 16, 13, 21, 2, 9, 19, 6, 18, 12, 10, 4, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 60049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 14, 7, 4, 10, 12, 18, 6, 20, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,014 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 21836.00)
2025-08-05 10:28:57,014 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:57,014 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:57,014 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,018 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:57,018 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,018 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,018 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,019 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62472.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,019 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62472.0, 'intermediate_solutions': [{'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 36, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 29, 40, 33, 35, 34, 41], 'cur_cost': 29564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 38, 39, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 4, 3, 1, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 35326.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 17, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 15, 35, 34, 41], 'cur_cost': 31687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,019 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 62472.00)
2025-08-05 10:28:57,019 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:57,019 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:57,019 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,022 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:57,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,023 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,023 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61805.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,024 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 61805.0, 'intermediate_solutions': [{'tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 28, 4, 2, 23, 3, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 40748.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 16, 21, 18, 19, 15, 22, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 22, 15, 19, 26, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 29420.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,024 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 61805.00)
2025-08-05 10:28:57,024 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:57,024 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,024 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,024 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 120391.0
2025-08-05 10:28:57,037 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:57,037 - ExploitationExpert - INFO - res_population_costs: [14419.0, 14419, 14442, 14454.0, 14454, 14454, 14454, 14419.0, 14408]
2025-08-05 10:28:57,037 - ExploitationExpert - INFO - res_populations: [array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-08-05 10:28:57,040 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,040 - ExploitationExpert - INFO - populations: [{'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56030.0}, {'tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98242.0}, {'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21974.0}, {'tour': [29, 6, 9, 37, 14, 26, 35, 1, 12, 20, 22, 40, 11, 23, 21, 16, 28, 33, 13, 10, 25, 2, 4, 30, 8, 0, 3, 39, 41, 32, 7, 18, 19, 17, 27, 5, 38, 24, 36, 15, 34, 31], 'cur_cost': 102886.0}, {'tour': array([ 7, 41, 40,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22], dtype=int64), 'cur_cost': 110444.0}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19316.0}, {'tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21836.0}, {'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62472.0}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 61805.0}, {'tour': array([29, 20,  1, 16,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4], dtype=int64), 'cur_cost': 120391.0}]
2025-08-05 10:28:57,041 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,041 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 238, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 238, 'cache_hits': 0, 'similarity_calculations': 1128, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,042 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([29, 20,  1, 16,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4], dtype=int64), 'cur_cost': 120391.0, 'intermediate_solutions': [{'tour': array([14,  5, 30, 16, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 113938.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 14,  5, 30, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 16, 14,  5, 30, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 16, 14,  5, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108890.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 23, 16, 14,  5, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,042 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 120391.00)
2025-08-05 10:28:57,042 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:57,043 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:57,044 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56030.0, 'intermediate_solutions': [{'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 39, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 3, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 43423.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 41, 34, 35, 33, 40, 36, 37, 32, 38, 39, 23, 31, 28, 30, 25, 27, 26], 'cur_cost': 25176.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 1, 15, 19, 18, 21, 14, 16, 20, 17, 13, 6, 11, 8, 7, 28, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 33085.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98242.0, 'intermediate_solutions': [{'tour': [19, 14, 16, 28, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 20, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 12, 30, 6], 'cur_cost': 74145.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 14, 16, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 30, 12, 29, 4, 1, 13, 24, 18, 17, 41, 26, 40, 39, 15, 21, 35, 27, 32, 34, 6], 'cur_cost': 68416.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 14, 20, 8, 7, 10, 5, 3, 2, 22, 25, 31, 23, 38, 37, 36, 33, 11, 0, 9, 28, 34, 32, 27, 35, 21, 15, 39, 40, 26, 41, 17, 18, 24, 13, 1, 4, 29, 16, 12, 30, 6], 'cur_cost': 69595.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21974.0, 'intermediate_solutions': [{'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 28, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 15, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 61599.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 36, 37, 33, 38, 35, 34, 41, 26, 27, 7, 16, 39, 28, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 18, 12, 13], 'cur_cost': 57945.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 10, 9, 2, 14, 8, 3, 1, 6, 4, 20, 15, 11, 0, 21, 5, 16, 7, 27, 26, 41, 34, 35, 38, 33, 37, 36, 39, 28, 18, 29, 31, 24, 22, 32, 25, 40, 23, 30, 17, 12, 13], 'cur_cost': 56914.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [29, 6, 9, 37, 14, 26, 35, 1, 12, 20, 22, 40, 11, 23, 21, 16, 28, 33, 13, 10, 25, 2, 4, 30, 8, 0, 3, 39, 41, 32, 7, 18, 19, 17, 27, 5, 38, 24, 36, 15, 34, 31], 'cur_cost': 102886.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 28, 8, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 30, 11, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 34741.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 7, 10, 9, 4, 3, 2, 22, 28, 30, 25, 27, 26, 24, 29, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18780.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 19, 18, 21, 14, 15, 20, 16, 17, 12, 13, 6, 11, 8, 30, 7, 10, 9, 4, 3, 2, 22, 29, 24, 26, 27, 25, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 26861.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 41, 40,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22], dtype=int64), 'cur_cost': 110444.0, 'intermediate_solutions': [{'tour': array([ 6,  1,  5,  4, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 94918.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 4,  6,  1,  5, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98549.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  4,  6,  1,  5, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98568.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 5,  4,  6,  1, 14, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 98635.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 5, 14,  4,  6,  1, 21, 22, 35, 29, 36, 15, 40,  7,  9,  3, 11, 28,
       34, 13,  0, 25, 39, 12, 33,  8, 31,  2, 37, 41, 24, 30, 18, 19, 17,
       10, 32, 38, 27, 26, 20, 23, 16]), 'cur_cost': 102899.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19316.0, 'intermediate_solutions': [{'tour': [0, 15, 17, 8, 7, 10, 11, 39, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 13, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 35252.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 17, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 5, 1, 2, 3, 4, 20, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24263.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 8, 7, 10, 11, 13, 6, 12, 9, 14, 21, 19, 18, 16, 20, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 17, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24705.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21836.0, 'intermediate_solutions': [{'tour': [35, 38, 36, 41, 25, 26, 28, 30, 15, 20, 14, 7, 4, 10, 12, 18, 6, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 17, 8, 5, 1, 23, 34], 'cur_cost': 54974.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 20, 14, 7, 31, 33, 32, 40, 39, 24, 0, 3, 11, 16, 13, 21, 2, 9, 19, 6, 18, 12, 10, 4, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 60049.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [35, 38, 36, 41, 25, 26, 28, 30, 17, 14, 7, 4, 10, 12, 18, 6, 20, 19, 9, 2, 21, 13, 16, 11, 3, 0, 24, 39, 40, 32, 33, 31, 27, 29, 37, 22, 15, 8, 5, 1, 23, 34], 'cur_cost': 55020.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62472.0, 'intermediate_solutions': [{'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 36, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 29, 40, 33, 35, 34, 41], 'cur_cost': 29564.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 2, 17, 15, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 38, 39, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 4, 3, 1, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 35326.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 9, 2, 17, 19, 18, 21, 14, 16, 20, 12, 13, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 15, 35, 34, 41], 'cur_cost': 31687.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 61805.0, 'intermediate_solutions': [{'tour': [0, 14, 22, 15, 19, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 28, 4, 2, 23, 3, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 40748.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 16, 21, 18, 19, 15, 22, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 26, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23928.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 22, 15, 19, 26, 18, 21, 16, 20, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 3, 4, 2, 23, 28, 31, 27, 25, 24, 29, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 29420.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([29, 20,  1, 16,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4], dtype=int64), 'cur_cost': 120391.0, 'intermediate_solutions': [{'tour': array([14,  5, 30, 16, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 113938.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16, 14,  5, 30, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([23, 16, 14,  5, 30, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108893.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([30, 16, 14,  5, 23, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108890.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([30, 23, 16, 14,  5, 32, 25,  6,  1, 10, 24, 35, 28, 40,  0, 29, 20,
       26, 31, 34,  9, 22, 37, 33, 17, 38, 19, 15, 18,  3,  7,  8, 36, 11,
       41, 12, 21, 13,  4, 27,  2, 39]), 'cur_cost': 108717.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:57,044 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:57,045 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,047 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=19316.000, 多样性=0.923
2025-08-05 10:28:57,047 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:57,047 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:57,047 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:57,048 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.16773198931531383, 'best_improvement': -0.03410246801220622}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.04741896758703485}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.000461580608531816, 'recent_improvements': [0.08558460179050871, 0.055500883430010965, 0.08650776300757235], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 14408, 'new_best_cost': 14408, 'quality_improvement': 0.0, 'old_diversity': 0.8154761904761905, 'new_diversity': 0.8154761904761905, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:57,049 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:57,049 - __main__ - INFO - composite7_42 开始进化第 3 代
2025-08-05 10:28:57,049 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:57,049 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,050 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=19316.000, 多样性=0.923
2025-08-05 10:28:57,050 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:57,052 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.923
2025-08-05 10:28:57,052 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:57,056 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.815
2025-08-05 10:28:57,057 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:57,057 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:57,057 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 10:28:57,058 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 10:28:57,103 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.105, 适应度梯度: -8352.600, 聚类评分: 0.000, 覆盖率: 0.111, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:57,103 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:57,104 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:57,104 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite7_42
2025-08-05 10:28:57,110 - visualization.landscape_visualizer - INFO - 插值约束: 90 个点被约束到最小值 14408.00
2025-08-05 10:28:57,112 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=7.7%, 梯度: 4471.73 → 4129.19
2025-08-05 10:28:57,250 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\landscape_composite7_42_iter_93_20250805_102857.html
2025-08-05 10:28:57,324 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\dashboard_composite7_42_iter_93_20250805_102857.html
2025-08-05 10:28:57,324 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 93
2025-08-05 10:28:57,324 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:57,324 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2674秒
2025-08-05 10:28:57,324 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.10526315789473684, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -8352.6, 'local_optima_density': 0.10526315789473684, 'gradient_variance': 1200235425.8947368, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1109, 'fitness_entropy': 0.685972951096462, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -8352.600)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.111)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360937.1038218, 'performance_metrics': {}}}
2025-08-05 10:28:57,324 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:57,324 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:57,324 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:57,325 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:57,325 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,325 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:57,325 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,325 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:57,326 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:57,326 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,326 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:57,326 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:57,327 - experts.management.collaboration_manager - INFO - 识别精英个体: {5, 6} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:57,327 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:57,327 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:57,327 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,328 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,328 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,329 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19098.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,329 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0, 'intermediate_solutions': [{'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 26, 25, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56031.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 30, 37, 34, 36, 16, 22, 23, 33, 27, 38, 41, 26, 25, 39, 28, 18, 3, 9, 19, 2, 4, 8, 17, 10, 0, 5, 7, 6, 21, 11, 14, 20, 15, 40, 32, 24, 35, 12, 13, 1], 'cur_cost': 61981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 22, 38, 27, 33, 23, 16, 12, 13, 1], 'cur_cost': 58203.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,330 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 19098.00)
2025-08-05 10:28:57,330 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:57,330 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:57,330 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,331 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:57,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,331 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 83527.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,332 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83527.0, 'intermediate_solutions': [{'tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 19, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 11, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 97258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 9, 20, 34, 36, 14, 1, 29, 39, 18, 21, 35, 19, 2, 4, 8, 17, 26, 0, 33, 16, 6, 3, 23, 11, 15, 22, 40, 32, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98360.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 9, 20, 34, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 36, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 104144.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,332 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 83527.00)
2025-08-05 10:28:57,332 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:57,332 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:57,333 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,334 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,334 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,335 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17993.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,335 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 17993.0, 'intermediate_solutions': [{'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 41, 39, 38, 32, 37, 36, 40, 33, 35, 34, 23], 'cur_cost': 22154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 19, 8, 12, 13, 11, 10, 7, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 22017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 36, 38, 32, 37, 40, 33, 35, 34, 41], 'cur_cost': 22036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,335 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 17993.00)
2025-08-05 10:28:57,335 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:57,336 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,336 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,336 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 108701.0
2025-08-05 10:28:57,349 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:57,349 - ExploitationExpert - INFO - res_population_costs: [14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:57,349 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:57,352 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,352 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0}, {'tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83527.0}, {'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 17993.0}, {'tour': array([32, 10,  2, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26], dtype=int64), 'cur_cost': 108701.0}, {'tour': [7, 41, 40, 5, 15, 23, 31, 20, 4, 25, 9, 17, 8, 16, 26, 36, 1, 30, 27, 32, 28, 6, 2, 34, 39, 11, 37, 14, 0, 12, 19, 3, 10, 24, 33, 21, 35, 38, 13, 18, 29, 22], 'cur_cost': 110444.0}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19316.0}, {'tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21836.0}, {'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62472.0}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 61805.0}, {'tour': [29, 20, 1, 16, 8, 31, 33, 19, 6, 39, 3, 28, 37, 14, 32, 5, 24, 35, 13, 27, 22, 38, 26, 9, 25, 36, 40, 12, 10, 41, 11, 2, 17, 34, 18, 21, 30, 7, 23, 0, 15, 4], 'cur_cost': 120391.0}]
2025-08-05 10:28:57,353 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,353 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 239, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 239, 'cache_hits': 0, 'similarity_calculations': 1132, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,354 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([32, 10,  2, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26], dtype=int64), 'cur_cost': 108701.0, 'intermediate_solutions': [{'tour': array([ 9,  6, 29, 37, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 103660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37,  9,  6, 29, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 103265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 37,  9,  6, 29, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 102831.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 37,  9,  6, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 98244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14, 37,  9,  6, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 102873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,354 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 108701.00)
2025-08-05 10:28:57,354 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:57,354 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,354 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,355 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 110314.0
2025-08-05 10:28:57,374 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:57,374 - ExploitationExpert - INFO - res_population_costs: [14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454, 14408]
2025-08-05 10:28:57,374 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64)]
2025-08-05 10:28:57,378 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,378 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0}, {'tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83527.0}, {'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 17993.0}, {'tour': array([32, 10,  2, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26], dtype=int64), 'cur_cost': 108701.0}, {'tour': array([ 6,  2, 31, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20], dtype=int64), 'cur_cost': 110314.0}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 19316.0}, {'tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21836.0}, {'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62472.0}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 61805.0}, {'tour': [29, 20, 1, 16, 8, 31, 33, 19, 6, 39, 3, 28, 37, 14, 32, 5, 24, 35, 13, 27, 22, 38, 26, 9, 25, 36, 40, 12, 10, 41, 11, 2, 17, 34, 18, 21, 30, 7, 23, 0, 15, 4], 'cur_cost': 120391.0}]
2025-08-05 10:28:57,379 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,379 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 240, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 240, 'cache_hits': 0, 'similarity_calculations': 1137, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,380 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 6,  2, 31, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20], dtype=int64), 'cur_cost': 110314.0, 'intermediate_solutions': [{'tour': array([40, 41,  7,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 105498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 40, 41,  7, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15,  5, 40, 41,  7, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110433.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  5, 40, 41, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 15,  5, 40, 41, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 106233.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,380 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 110314.00)
2025-08-05 10:28:57,380 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:57,380 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:57,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,382 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,382 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,383 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18708.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,383 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18708.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 38, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 19, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 32703.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 37, 32, 38, 36, 40, 33, 35, 34, 41], 'cur_cost': 19356.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 7, 36, 40, 33, 35, 34, 41], 'cur_cost': 28092.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,383 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 18708.00)
2025-08-05 10:28:57,383 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:57,384 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:57,384 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23048.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,385 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23048.0, 'intermediate_solutions': [{'tour': [0, 20, 11, 15, 19, 21, 14, 16, 18, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21921.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 41, 34, 35, 33], 'cur_cost': 21949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 11, 15, 10, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25219.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,386 - experts.management.collaboration_manager - INFO - 个体 6 保留原路径 (成本: 23048.00)
2025-08-05 10:28:57,386 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:57,386 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:57,386 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50464.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,389 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50464.0, 'intermediate_solutions': [{'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 12, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 20, 5, 3, 32, 34], 'cur_cost': 59674.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 26, 14, 18, 6, 1, 2, 13, 0, 19, 10, 20, 29, 27, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 17, 4, 7, 21, 9, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 23, 34], 'cur_cost': 64677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,390 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 50464.00)
2025-08-05 10:28:57,390 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:57,390 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:57,390 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,391 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,391 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,392 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18005.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,392 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 18005.0, 'intermediate_solutions': [{'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 29, 38, 24, 36, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 66182.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 12, 39, 25, 31, 41, 34, 28, 37, 23, 8, 4, 5, 19, 33, 27, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 69790.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 8, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 58768.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,392 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 18005.00)
2025-08-05 10:28:57,392 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:57,392 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,392 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,393 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95549.0
2025-08-05 10:28:57,407 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:57,407 - ExploitationExpert - INFO - res_population_costs: [14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454, 14408]
2025-08-05 10:28:57,407 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64)]
2025-08-05 10:28:57,410 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,410 - ExploitationExpert - INFO - populations: [{'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0}, {'tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83527.0}, {'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 17993.0}, {'tour': array([32, 10,  2, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26], dtype=int64), 'cur_cost': 108701.0}, {'tour': array([ 6,  2, 31, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20], dtype=int64), 'cur_cost': 110314.0}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18708.0}, {'tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23048.0}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50464.0}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 18005.0}, {'tour': array([25, 16, 39,  1, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10], dtype=int64), 'cur_cost': 95549.0}]
2025-08-05 10:28:57,411 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,411 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 241, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 241, 'cache_hits': 0, 'similarity_calculations': 1143, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,412 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([25, 16, 39,  1, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10], dtype=int64), 'cur_cost': 95549.0, 'intermediate_solutions': [{'tour': array([ 1, 20, 29, 16,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 116728.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16,  1, 20, 29,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 16,  1, 20, 29, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 115446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 16,  1, 20,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29,  8, 16,  1, 20, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,412 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 95549.00)
2025-08-05 10:28:57,413 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:57,413 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:57,415 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0, 'intermediate_solutions': [{'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 26, 25, 41, 38, 27, 33, 23, 22, 16, 12, 13, 1], 'cur_cost': 56031.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [29, 31, 30, 37, 34, 36, 16, 22, 23, 33, 27, 38, 41, 26, 25, 39, 28, 18, 3, 9, 19, 2, 4, 8, 17, 10, 0, 5, 7, 6, 21, 11, 14, 20, 15, 40, 32, 24, 35, 12, 13, 1], 'cur_cost': 61981.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [29, 31, 30, 37, 34, 36, 35, 24, 32, 40, 15, 20, 14, 11, 21, 6, 7, 5, 0, 10, 17, 8, 4, 2, 19, 9, 3, 18, 28, 39, 25, 26, 41, 22, 38, 27, 33, 23, 16, 12, 13, 1], 'cur_cost': 58203.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83527.0, 'intermediate_solutions': [{'tour': [5, 10, 9, 20, 34, 36, 14, 1, 32, 40, 22, 15, 19, 23, 3, 6, 16, 33, 0, 26, 17, 8, 4, 2, 11, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 97258.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 9, 20, 34, 36, 14, 1, 29, 39, 18, 21, 35, 19, 2, 4, 8, 17, 26, 0, 33, 16, 6, 3, 23, 11, 15, 22, 40, 32, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 98360.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 9, 20, 34, 14, 1, 32, 40, 22, 15, 11, 23, 3, 6, 16, 33, 0, 26, 17, 36, 8, 4, 2, 19, 35, 21, 18, 39, 29, 25, 41, 27, 38, 24, 13, 7, 30, 28, 12, 37, 31], 'cur_cost': 104144.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 17993.0, 'intermediate_solutions': [{'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 41, 39, 38, 32, 37, 36, 40, 33, 35, 34, 23], 'cur_cost': 22154.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 6, 19, 8, 12, 13, 11, 10, 7, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 22017.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 6, 19, 8, 7, 10, 11, 13, 12, 9, 17, 15, 20, 16, 18, 21, 14, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 36, 38, 32, 37, 40, 33, 35, 34, 41], 'cur_cost': 22036.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 10,  2, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26], dtype=int64), 'cur_cost': 108701.0, 'intermediate_solutions': [{'tour': array([ 9,  6, 29, 37, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 103660.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([37,  9,  6, 29, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 103265.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14, 37,  9,  6, 29, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 102831.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 37,  9,  6, 14, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 98244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29, 14, 37,  9,  6, 26, 35,  1, 12, 20, 22, 40, 11, 23, 21, 16, 28,
       33, 13, 10, 25,  2,  4, 30,  8,  0,  3, 39, 41, 32,  7, 18, 19, 17,
       27,  5, 38, 24, 36, 15, 34, 31]), 'cur_cost': 102873.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6,  2, 31, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20], dtype=int64), 'cur_cost': 110314.0, 'intermediate_solutions': [{'tour': array([40, 41,  7,  5, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 105498.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 5, 40, 41,  7, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110069.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15,  5, 40, 41,  7, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110433.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 7,  5, 40, 41, 15, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 110376.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 7, 15,  5, 40, 41, 23, 31, 20,  4, 25,  9, 17,  8, 16, 26, 36,  1,
       30, 27, 32, 28,  6,  2, 34, 39, 11, 37, 14,  0, 12, 19,  3, 10, 24,
       33, 21, 35, 38, 13, 18, 29, 22]), 'cur_cost': 106233.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18708.0, 'intermediate_solutions': [{'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 38, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 19, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 32703.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 7, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 37, 32, 38, 36, 40, 33, 35, 34, 41], 'cur_cost': 19356.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 20, 12, 13, 6, 11, 8, 10, 9, 17, 15, 19, 21, 14, 16, 4, 3, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 7, 36, 40, 33, 35, 34, 41], 'cur_cost': 28092.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23048.0, 'intermediate_solutions': [{'tour': [0, 20, 11, 15, 19, 21, 14, 16, 18, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21921.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 18, 11, 15, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 41, 34, 35, 33], 'cur_cost': 21949.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 18, 11, 15, 10, 19, 21, 14, 16, 20, 17, 12, 13, 6, 9, 8, 7, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25219.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50464.0, 'intermediate_solutions': [{'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 27, 29, 12, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 20, 5, 3, 32, 34], 'cur_cost': 59674.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 17, 4, 7, 21, 9, 23, 24, 37, 41, 31, 30, 26, 14, 18, 6, 1, 2, 13, 0, 19, 10, 20, 29, 27, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 34], 'cur_cost': 62480.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 17, 4, 7, 21, 9, 24, 37, 41, 31, 30, 27, 29, 20, 10, 19, 0, 13, 2, 1, 6, 18, 14, 26, 25, 28, 39, 35, 38, 33, 40, 36, 22, 16, 11, 15, 12, 5, 3, 32, 23, 34], 'cur_cost': 64677.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 18005.0, 'intermediate_solutions': [{'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 29, 38, 24, 36, 27, 33, 19, 5, 4, 8, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 66182.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 12, 39, 25, 31, 41, 34, 28, 37, 23, 8, 4, 5, 19, 33, 27, 11, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 69790.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 20, 16, 10, 14, 15, 17, 3, 1, 22, 30, 35, 36, 38, 24, 29, 27, 33, 19, 5, 4, 23, 37, 28, 34, 41, 31, 25, 39, 12, 11, 8, 21, 7, 0, 13, 6, 2, 18, 26, 32, 40], 'cur_cost': 58768.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 16, 39,  1, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10], dtype=int64), 'cur_cost': 95549.0, 'intermediate_solutions': [{'tour': array([ 1, 20, 29, 16,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 116728.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([16,  1, 20, 29,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120742.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 8, 16,  1, 20, 29, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 115446.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([29, 16,  1, 20,  8, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120396.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([29,  8, 16,  1, 20, 31, 33, 19,  6, 39,  3, 28, 37, 14, 32,  5, 24,
       35, 13, 27, 22, 38, 26,  9, 25, 36, 40, 12, 10, 41, 11,  2, 17, 34,
       18, 21, 30,  7, 23,  0, 15,  4]), 'cur_cost': 120414.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:57,415 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:57,415 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,417 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17993.000, 多样性=0.903
2025-08-05 10:28:57,417 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:57,417 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:57,418 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:57,419 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 90, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.11488603201553711, 'best_improvement': 0.0684924414992752}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.022349570200572984}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.11161643637266239, 'recent_improvements': [0.055500883430010965, 0.08650776300757235, -0.16773198931531383], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 14408, 'new_best_cost': 14408, 'quality_improvement': 0.0, 'old_diversity': 0.834920634920635, 'new_diversity': 0.834920634920635, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:57,420 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:57,420 - __main__ - INFO - composite7_42 开始进化第 4 代
2025-08-05 10:28:57,420 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:57,420 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,421 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=17993.000, 多样性=0.903
2025-08-05 10:28:57,421 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:57,423 - PathExpert - INFO - 路径结构分析完成: 公共边数量=9, 路径相似性=0.903
2025-08-05 10:28:57,423 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:57,426 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.835
2025-08-05 10:28:57,429 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:57,429 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:57,430 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:57,430 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:57,500 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.200, 适应度梯度: -11537.070, 聚类评分: 0.000, 覆盖率: 0.112, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:57,501 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:57,501 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:57,501 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite7_42
2025-08-05 10:28:57,509 - visualization.landscape_visualizer - INFO - 插值约束: 71 个点被约束到最小值 14408.00
2025-08-05 10:28:57,512 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.7%, 梯度: 3669.49 → 3350.97
2025-08-05 10:28:57,628 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\landscape_composite7_42_iter_94_20250805_102857.html
2025-08-05 10:28:57,670 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\dashboard_composite7_42_iter_94_20250805_102857.html
2025-08-05 10:28:57,670 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 94
2025-08-05 10:28:57,670 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:57,670 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2408秒
2025-08-05 10:28:57,671 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -11537.07, 'local_optima_density': 0.2, 'gradient_variance': 925991597.9930996, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.112, 'fitness_entropy': 0.5563308144749579, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -11537.070)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.112)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360937.5017874, 'performance_metrics': {}}}
2025-08-05 10:28:57,671 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:57,671 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:57,671 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:57,671 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:57,672 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,672 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:57,672 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,672 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:57,672 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:57,672 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:57,672 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:57,673 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:57,673 - experts.management.collaboration_manager - INFO - 识别精英个体: {8, 2} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:57,673 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:57,673 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:57,673 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,674 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,675 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,675 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21128.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,676 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21128.0, 'intermediate_solutions': [{'tour': [0, 14, 21, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 1, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19783.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 16, 21, 18, 19, 15, 17, 41, 34, 35, 33, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 25763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,676 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 21128.00)
2025-08-05 10:28:57,676 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:57,676 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:57,676 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,677 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,677 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,678 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,678 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25061.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,678 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25061.0, 'intermediate_solutions': [{'tour': [5, 10, 40, 22, 29, 25, 31, 20, 26, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83531.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 4, 38, 24, 12, 9, 23, 2, 14, 1], 'cur_cost': 88606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 40, 22, 29, 26, 31, 25, 30, 28, 13, 8, 39, 3, 6, 20, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 78874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,678 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 25061.00)
2025-08-05 10:28:57,678 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:57,679 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:57,679 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,680 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,680 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,681 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24882.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,681 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24882.0, 'intermediate_solutions': [{'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 41, 34, 35, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 18056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 3, 1, 5, 10, 9, 8, 11, 6, 13, 12, 14, 21, 4, 2], 'cur_cost': 22296.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 34, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 23899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,681 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 24882.00)
2025-08-05 10:28:57,681 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:57,681 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,681 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,682 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 91970.0
2025-08-05 10:28:57,697 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:57,697 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:57,697 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:57,700 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,701 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21128.0}, {'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25061.0}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24882.0}, {'tour': array([26, 17, 37,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35], dtype=int64), 'cur_cost': 91970.0}, {'tour': [6, 2, 31, 18, 3, 19, 8, 36, 12, 23, 39, 16, 14, 17, 5, 4, 37, 41, 0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33, 9, 40, 34, 15, 28, 1, 7, 30, 29, 20], 'cur_cost': 110314.0}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18708.0}, {'tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23048.0}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50464.0}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 18005.0}, {'tour': [25, 16, 39, 1, 38, 18, 40, 41, 28, 26, 29, 23, 35, 6, 36, 31, 32, 30, 14, 11, 27, 9, 33, 4, 5, 3, 22, 34, 20, 17, 8, 7, 13, 2, 0, 24, 12, 19, 15, 21, 37, 10], 'cur_cost': 95549.0}]
2025-08-05 10:28:57,701 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,701 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 242, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 242, 'cache_hits': 0, 'similarity_calculations': 1150, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,702 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([26, 17, 37,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35], dtype=int64), 'cur_cost': 91970.0, 'intermediate_solutions': [{'tour': array([ 2, 10, 32, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 106700.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33,  2, 10, 32, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 108702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 33,  2, 10, 32, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 106534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 33,  2, 10, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 107254.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 22, 33,  2, 10, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 108661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,702 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 91970.00)
2025-08-05 10:28:57,702 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:57,703 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,703 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,703 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 112463.0
2025-08-05 10:28:57,717 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:57,717 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:57,717 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:57,719 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,720 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21128.0}, {'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25061.0}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24882.0}, {'tour': array([26, 17, 37,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35], dtype=int64), 'cur_cost': 91970.0}, {'tour': array([11, 28, 23, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37], dtype=int64), 'cur_cost': 112463.0}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18708.0}, {'tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23048.0}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50464.0}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 18005.0}, {'tour': [25, 16, 39, 1, 38, 18, 40, 41, 28, 26, 29, 23, 35, 6, 36, 31, 32, 30, 14, 11, 27, 9, 33, 4, 5, 3, 22, 34, 20, 17, 8, 7, 13, 2, 0, 24, 12, 19, 15, 21, 37, 10], 'cur_cost': 95549.0}]
2025-08-05 10:28:57,720 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,720 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 243, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 243, 'cache_hits': 0, 'similarity_calculations': 1158, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,721 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([11, 28, 23, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37], dtype=int64), 'cur_cost': 112463.0, 'intermediate_solutions': [{'tour': array([31,  2,  6, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110273.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 31,  2,  6,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 109689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 18, 31,  2,  6, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 18, 31,  2,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 106591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3, 18, 31,  2, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110300.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,721 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 112463.00)
2025-08-05 10:28:57,722 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:57,722 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:57,722 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,725 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:57,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,725 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62694.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,726 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62694.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 40, 36, 37, 33, 35, 34, 41], 'cur_cost': 18714.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 37, 32, 38, 39, 23, 31, 28, 30, 25, 27, 36, 40, 33, 35, 34, 41], 'cur_cost': 20897.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 30, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,726 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 62694.00)
2025-08-05 10:28:57,726 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:57,726 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:57,726 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,727 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:57,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,728 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,728 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85952.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,729 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85952.0, 'intermediate_solutions': [{'tour': [14, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 0, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23424.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 35, 33, 34, 41], 'cur_cost': 23099.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 4, 8, 36, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 40, 33, 35, 34, 41], 'cur_cost': 31820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,729 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 85952.00)
2025-08-05 10:28:57,729 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:57,729 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:57,729 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,730 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:57,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,731 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15333.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,731 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 15333.0, 'intermediate_solutions': [{'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 11, 7, 13, 19, 5, 1, 6, 8, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50495.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 15, 14, 16, 33, 3, 18, 28, 38, 34, 10], 'cur_cost': 56757.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 19, 5, 1, 13, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50468.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,731 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 15333.00)
2025-08-05 10:28:57,732 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:57,732 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:57,732 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:57,734 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:57,734 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,735 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:57,735 - ExplorationExpert - INFO - 探索路径生成完成，成本: 70280.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:57,735 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70280.0, 'intermediate_solutions': [{'tour': [0, 41, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 8, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 24194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 5, 9, 10, 7, 11, 1, 3, 4, 2], 'cur_cost': 24308.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 37, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 24787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:57,736 - experts.management.collaboration_manager - INFO - 个体 8 保留原路径 (成本: 70280.00)
2025-08-05 10:28:57,736 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:57,736 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:57,736 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:57,736 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108244.0
2025-08-05 10:28:57,748 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:57,748 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:57,748 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:57,751 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:57,751 - ExploitationExpert - INFO - populations: [{'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21128.0}, {'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25061.0}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24882.0}, {'tour': array([26, 17, 37,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35], dtype=int64), 'cur_cost': 91970.0}, {'tour': array([11, 28, 23, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37], dtype=int64), 'cur_cost': 112463.0}, {'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62694.0}, {'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85952.0}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 15333.0}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70280.0}, {'tour': array([11, 35, 22, 26, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36], dtype=int64), 'cur_cost': 108244.0}]
2025-08-05 10:28:57,753 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:57,753 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 244, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 244, 'cache_hits': 0, 'similarity_calculations': 1167, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:57,753 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([11, 35, 22, 26, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36], dtype=int64), 'cur_cost': 108244.0, 'intermediate_solutions': [{'tour': array([39, 16, 25,  1, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 94921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 39, 16, 25, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 90529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38,  1, 39, 16, 25, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 95210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25,  1, 39, 16, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 95248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 38,  1, 39, 16, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 90561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:57,754 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 108244.00)
2025-08-05 10:28:57,754 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:57,754 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:57,756 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21128.0, 'intermediate_solutions': [{'tour': [0, 14, 21, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 1, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19783.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 16, 21, 18, 19, 15, 17, 41, 34, 35, 33, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 25763.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 14, 1, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 20, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2], 'cur_cost': 19098.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25061.0, 'intermediate_solutions': [{'tour': [5, 10, 40, 22, 29, 25, 31, 20, 26, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 83531.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [5, 10, 40, 22, 29, 26, 31, 20, 25, 30, 28, 13, 8, 39, 3, 6, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 4, 38, 24, 12, 9, 23, 2, 14, 1], 'cur_cost': 88606.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [5, 10, 40, 22, 29, 26, 31, 25, 30, 28, 13, 8, 39, 3, 6, 20, 37, 36, 0, 33, 35, 34, 41, 17, 15, 19, 18, 21, 16, 32, 7, 11, 27, 24, 38, 4, 12, 9, 23, 2, 14, 1], 'cur_cost': 78874.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24882.0, 'intermediate_solutions': [{'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 41, 34, 35, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 18056.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 3, 1, 5, 10, 9, 8, 11, 6, 13, 12, 14, 21, 4, 2], 'cur_cost': 22296.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 34, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2], 'cur_cost': 23899.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([26, 17, 37,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35], dtype=int64), 'cur_cost': 91970.0, 'intermediate_solutions': [{'tour': array([ 2, 10, 32, 33, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 106700.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33,  2, 10, 32, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 108702.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([22, 33,  2, 10, 32, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 106534.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 33,  2, 10, 22, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 107254.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32, 22, 33,  2, 10, 35, 13, 25, 20, 37, 27, 38,  3, 17,  7,  5, 21,
       23,  8, 28, 31, 30, 18,  6, 40, 24,  9, 34, 11, 39, 29,  0,  4, 36,
       41, 16, 19, 15, 14,  1, 12, 26]), 'cur_cost': 108661.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 28, 23, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37], dtype=int64), 'cur_cost': 112463.0, 'intermediate_solutions': [{'tour': array([31,  2,  6, 18,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110273.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 31,  2,  6,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 109689.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3, 18, 31,  2,  6, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110320.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 6, 18, 31,  2,  3, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 106591.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 6,  3, 18, 31,  2, 19,  8, 36, 12, 23, 39, 16, 14, 17,  5,  4, 37,
       41,  0, 22, 27, 13, 38, 35, 11, 21, 26, 10, 25, 32, 24, 33,  9, 40,
       34, 15, 28,  1,  7, 30, 29, 20]), 'cur_cost': 110300.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62694.0, 'intermediate_solutions': [{'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 40, 36, 37, 33, 35, 34, 41], 'cur_cost': 18714.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 37, 32, 38, 39, 23, 31, 28, 30, 25, 27, 36, 40, 33, 35, 34, 41], 'cur_cost': 20897.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 1, 20, 16, 19, 18, 21, 14, 15, 17, 30, 12, 13, 6, 11, 8, 7, 10, 9, 5, 3, 4, 2, 22, 29, 24, 26, 27, 25, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23862.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85952.0, 'intermediate_solutions': [{'tour': [14, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 0, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 23424.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 4, 8, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 35, 33, 34, 41], 'cur_cost': 23099.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 4, 8, 36, 7, 10, 11, 13, 6, 12, 9, 17, 15, 19, 18, 21, 14, 16, 5, 1, 3, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 40, 33, 35, 34, 41], 'cur_cost': 31820.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 15333.0, 'intermediate_solutions': [{'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 11, 7, 13, 19, 5, 1, 6, 8, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50495.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 13, 19, 5, 1, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 15, 14, 16, 33, 3, 18, 28, 38, 34, 10], 'cur_cost': 56757.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 31, 35, 36, 17, 9, 12, 21, 20, 8, 7, 19, 5, 1, 13, 6, 11, 0, 4, 2, 24, 41, 32, 30, 40, 26, 29, 23, 25, 27, 39, 37, 33, 16, 14, 15, 3, 18, 28, 38, 34, 10], 'cur_cost': 50468.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70280.0, 'intermediate_solutions': [{'tour': [0, 41, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 8, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 24194.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 5, 9, 10, 7, 11, 1, 3, 4, 2], 'cur_cost': 24308.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 19, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 37, 14, 12, 13, 6, 11, 7, 10, 9, 5, 1, 3, 4, 2], 'cur_cost': 24787.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([11, 35, 22, 26, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36], dtype=int64), 'cur_cost': 108244.0, 'intermediate_solutions': [{'tour': array([39, 16, 25,  1, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 94921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 39, 16, 25, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 90529.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([38,  1, 39, 16, 25, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 95210.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25,  1, 39, 16, 38, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 95248.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25, 38,  1, 39, 16, 18, 40, 41, 28, 26, 29, 23, 35,  6, 36, 31, 32,
       30, 14, 11, 27,  9, 33,  4,  5,  3, 22, 34, 20, 17,  8,  7, 13,  2,
        0, 24, 12, 19, 15, 21, 37, 10]), 'cur_cost': 90561.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:57,756 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:57,756 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,760 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=15333.000, 多样性=0.939
2025-08-05 10:28:57,760 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:57,760 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:57,760 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:57,761 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.001585696186415295, 'best_improvement': 0.14783526927138332}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.03985932004689321}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.014189134503982383, 'recent_improvements': [0.08650776300757235, -0.16773198931531383, 0.11488603201553711], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 14408, 'new_best_cost': 14408, 'quality_improvement': 0.0, 'old_diversity': 0.834920634920635, 'new_diversity': 0.834920634920635, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:57,763 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:57,763 - __main__ - INFO - composite7_42 开始进化第 5 代
2025-08-05 10:28:57,763 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:57,764 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:57,764 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=15333.000, 多样性=0.939
2025-08-05 10:28:57,765 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:57,766 - PathExpert - INFO - 路径结构分析完成: 公共边数量=3, 路径相似性=0.939
2025-08-05 10:28:57,767 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:57,768 - EliteExpert - INFO - 精英解分析完成: 精英解数量=10, 多样性=0.835
2025-08-05 10:28:57,770 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:57,770 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:57,770 - LandscapeExpert - INFO - 添加精英解数据: 10个精英解
2025-08-05 10:28:57,770 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-05 10:28:57,837 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: -14627.230, 聚类评分: 0.000, 覆盖率: 0.113, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:57,837 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:57,837 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:57,838 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite7_42
2025-08-05 10:28:57,871 - visualization.landscape_visualizer - INFO - 插值约束: 108 个点被约束到最小值 14408.00
2025-08-05 10:28:57,874 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=9.2%, 梯度: 5091.82 → 4624.41
2025-08-05 10:28:57,986 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\landscape_composite7_42_iter_95_20250805_102857.html
2025-08-05 10:28:58,032 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite7_42\dashboard_composite7_42_iter_95_20250805_102857.html
2025-08-05 10:28:58,032 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 95
2025-08-05 10:28:58,032 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:58,032 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2623秒
2025-08-05 10:28:58,033 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -14627.230000000001, 'local_optima_density': 0.15, 'gradient_variance': 1268799067.1971002, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1128, 'fitness_entropy': 0.7237824896619229, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -14627.230)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.113)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360937.8370552, 'performance_metrics': {}}}
2025-08-05 10:28:58,033 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:58,033 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:58,033 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:58,033 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:58,033 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:58,034 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:58,034 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'explore', 'exploit']
2025-08-05 10:28:58,034 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 7} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:58,034 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:58,034 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:58,034 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,036 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21989.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,036 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 16, 7, 15, 19, 18, 21, 14, 20, 17, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21989.0, 'intermediate_solutions': [{'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 27, 4, 2, 22, 29, 24, 26, 3, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 38049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 9, 14, 21, 19, 16, 18, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 2, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 29491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,037 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 21989.00)
2025-08-05 10:28:58,037 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:58,037 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:58,037 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,038 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 42
2025-08-05 10:28:58,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,038 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103086.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,039 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [26, 12, 37, 18, 24, 21, 10, 14, 23, 17, 2, 19, 0, 36, 29, 7, 15, 5, 4, 3, 22, 25, 33, 6, 34, 41, 35, 9, 27, 39, 1, 13, 40, 31, 30, 11, 20, 28, 32, 38, 8, 16], 'cur_cost': 103086.0, 'intermediate_solutions': [{'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 17, 19, 18, 16, 20, 15, 21, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25092.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 41, 34, 35, 33, 40, 36, 37, 32, 38], 'cur_cost': 25160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 3, 9, 8, 7, 10, 26, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 33148.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,039 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 103086.00)
2025-08-05 10:28:58,040 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:58,040 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:58,040 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,041 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:58,041 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,042 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21565.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,042 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 10, 3, 1, 5, 4, 2, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 9, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21565.0, 'intermediate_solutions': [{'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 33, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 11, 35, 34, 41], 'cur_cost': 42132.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 38, 39, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 4, 2, 1, 5, 9, 10, 7, 8, 11, 6, 13, 17, 16, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 31513.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 24, 26, 27, 25, 30, 28, 29, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,043 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 21565.00)
2025-08-05 10:28:58,043 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:58,043 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,043 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,043 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 123666.0
2025-08-05 10:28:58,056 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:58,056 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:58,057 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:58,060 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,060 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 7, 15, 19, 18, 21, 14, 20, 17, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21989.0}, {'tour': [26, 12, 37, 18, 24, 21, 10, 14, 23, 17, 2, 19, 0, 36, 29, 7, 15, 5, 4, 3, 22, 25, 33, 6, 34, 41, 35, 9, 27, 39, 1, 13, 40, 31, 30, 11, 20, 28, 32, 38, 8, 16], 'cur_cost': 103086.0}, {'tour': [0, 10, 3, 1, 5, 4, 2, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 9, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21565.0}, {'tour': array([ 9, 17,  5, 18, 36, 21, 32,  7, 19, 33, 11, 29,  6, 10,  1, 23, 26,
        2, 41,  4, 22, 20, 28,  3, 34, 38, 31, 24, 15, 25,  0, 39, 14,  8,
       12, 35, 13, 30, 27, 16, 40, 37], dtype=int64), 'cur_cost': 123666.0}, {'tour': [11, 28, 23, 39, 24, 20, 12, 38, 34, 16, 2, 19, 6, 13, 29, 41, 26, 7, 30, 15, 9, 25, 33, 0, 22, 4, 36, 18, 21, 10, 1, 5, 40, 3, 35, 17, 14, 31, 32, 27, 8, 37], 'cur_cost': 112463.0}, {'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62694.0}, {'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85952.0}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 15333.0}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70280.0}, {'tour': [11, 35, 22, 26, 15, 19, 39, 16, 2, 37, 13, 0, 9, 41, 40, 1, 8, 32, 6, 17, 33, 5, 30, 20, 10, 12, 18, 31, 4, 7, 3, 24, 34, 28, 14, 21, 38, 25, 23, 27, 29, 36], 'cur_cost': 108244.0}]
2025-08-05 10:28:58,060 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:58,060 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 245, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 245, 'cache_hits': 0, 'similarity_calculations': 1177, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,061 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 9, 17,  5, 18, 36, 21, 32,  7, 19, 33, 11, 29,  6, 10,  1, 23, 26,
        2, 41,  4, 22, 20, 28,  3, 34, 38, 31, 24, 15, 25,  0, 39, 14,  8,
       12, 35, 13, 30, 27, 16, 40, 37], dtype=int64), 'cur_cost': 123666.0, 'intermediate_solutions': [{'tour': array([37, 17, 26,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 90614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 37, 17, 26, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28,  8, 37, 17, 26,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26,  8, 37, 17, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 28,  8, 37, 17,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 87323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,061 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 123666.00)
2025-08-05 10:28:58,062 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:58,062 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,062 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,062 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 95551.0
2025-08-05 10:28:58,076 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:58,076 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:58,076 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:58,079 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,079 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 7, 15, 19, 18, 21, 14, 20, 17, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21989.0}, {'tour': [26, 12, 37, 18, 24, 21, 10, 14, 23, 17, 2, 19, 0, 36, 29, 7, 15, 5, 4, 3, 22, 25, 33, 6, 34, 41, 35, 9, 27, 39, 1, 13, 40, 31, 30, 11, 20, 28, 32, 38, 8, 16], 'cur_cost': 103086.0}, {'tour': [0, 10, 3, 1, 5, 4, 2, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 9, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21565.0}, {'tour': array([ 9, 17,  5, 18, 36, 21, 32,  7, 19, 33, 11, 29,  6, 10,  1, 23, 26,
        2, 41,  4, 22, 20, 28,  3, 34, 38, 31, 24, 15, 25,  0, 39, 14,  8,
       12, 35, 13, 30, 27, 16, 40, 37], dtype=int64), 'cur_cost': 123666.0}, {'tour': array([15,  7, 20, 23, 10, 39, 21,  8,  2, 22,  1,  0, 28, 30, 14, 26, 31,
       11,  5,  9, 38, 16, 18, 24, 27,  4, 25, 12,  3, 17,  6, 19, 32, 37,
       35, 29, 36, 34, 41, 33, 40, 13], dtype=int64), 'cur_cost': 95551.0}, {'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62694.0}, {'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85952.0}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 15333.0}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70280.0}, {'tour': [11, 35, 22, 26, 15, 19, 39, 16, 2, 37, 13, 0, 9, 41, 40, 1, 8, 32, 6, 17, 33, 5, 30, 20, 10, 12, 18, 31, 4, 7, 3, 24, 34, 28, 14, 21, 38, 25, 23, 27, 29, 36], 'cur_cost': 108244.0}]
2025-08-05 10:28:58,080 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:58,080 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 246, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 246, 'cache_hits': 0, 'similarity_calculations': 1188, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,081 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([15,  7, 20, 23, 10, 39, 21,  8,  2, 22,  1,  0, 28, 30, 14, 26, 31,
       11,  5,  9, 38, 16, 18, 24, 27,  4, 25, 12,  3, 17,  6, 19, 32, 37,
       35, 29, 36, 34, 41, 33, 40, 13], dtype=int64), 'cur_cost': 95551.0, 'intermediate_solutions': [{'tour': array([23, 28, 11, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 112509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 23, 28, 11, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 111126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 39, 23, 28, 11, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 108244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 39, 23, 28, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 111733.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 24, 39, 23, 28, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 112473.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,081 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 95551.00)
2025-08-05 10:28:58,081 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:58,081 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:58,081 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,084 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:58,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,085 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,085 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62585.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,085 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [16, 19, 5, 21, 3, 6, 20, 12, 0, 13, 22, 37, 26, 30, 36, 32, 27, 24, 40, 23, 31, 25, 29, 34, 28, 15, 9, 11, 18, 14, 10, 17, 2, 7, 1, 4, 39, 38, 35, 41, 33, 8], 'cur_cost': 62585.0, 'intermediate_solutions': [{'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 12, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 15, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62340.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 30, 23, 24, 40, 22, 3, 10, 13, 21, 12, 25, 36, 38, 39, 27, 33, 35, 41, 26, 0, 9, 18, 16, 6, 1, 4, 20, 15, 5, 14, 8, 17, 7, 19, 34, 32, 29, 11, 2, 31, 37], 'cur_cost': 67714.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 6, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 68695.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,086 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 62585.00)
2025-08-05 10:28:58,086 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-05 10:28:58,086 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-05 10:28:58,086 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,087 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 42
2025-08-05 10:28:58,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,087 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,088 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18608.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,088 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 2, 14, 21, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18608.0, 'intermediate_solutions': [{'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 24, 6, 36, 33, 2, 17, 38, 22, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 41, 40, 29, 34, 20, 11, 21], 'cur_cost': 81243.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 9, 18, 19, 7, 10, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 15, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 86334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,088 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 18608.00)
2025-08-05 10:28:58,089 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:58,089 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:58,089 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,092 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:58,093 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,093 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,093 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,093 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,093 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62448.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,094 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [15, 19, 21, 1, 11, 10, 2, 14, 0, 17, 4, 24, 23, 40, 30, 25, 39, 41, 37, 29, 34, 35, 36, 28, 33, 16, 9, 8, 5, 6, 20, 13, 18, 3, 22, 27, 31, 38, 26, 32, 12, 7], 'cur_cost': 62448.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 10, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 22, 9, 5, 1, 2, 4], 'cur_cost': 26418.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 18, 16, 20, 15, 17, 41, 34, 35, 33, 40, 36, 37, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 22048.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 18, 5, 1, 2, 4], 'cur_cost': 16012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,094 - experts.management.collaboration_manager - INFO - 个体 7 保留原路径 (成本: 62448.00)
2025-08-05 10:28:58,094 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:58,094 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:58,094 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:58,097 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 42
2025-08-05 10:28:58,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,098 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:58,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49601.0, 路径长度: 42, 收集中间解: 3
2025-08-05 10:28:58,098 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [31, 25, 30, 36, 26, 34, 20, 13, 4, 2, 0, 16, 10, 6, 9, 19, 21, 3, 17, 23, 40, 28, 38, 41, 37, 22, 29, 33, 24, 27, 14, 11, 8, 15, 5, 1, 18, 12, 7, 35, 32, 39], 'cur_cost': 49601.0, 'intermediate_solutions': [{'tour': [13, 1, 15, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 21, 19, 12, 4, 34], 'cur_cost': 70383.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 9, 3, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70326.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 38, 5, 22, 23, 29, 27, 33, 39, 15, 19, 12, 4, 34], 'cur_cost': 76646.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:58,099 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 49601.00)
2025-08-05 10:28:58,099 - experts.management.collaboration_manager - INFO - 为个体 9 生成利用路径
2025-08-05 10:28:58,099 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:58,099 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:58,099 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 111129.0
2025-08-05 10:28:58,118 - ExploitationExpert - INFO - res_population_num: 10
2025-08-05 10:28:58,118 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14419.0, 14419, 14419.0, 14442, 14454.0, 14454, 14454, 14454]
2025-08-05 10:28:58,118 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6,  9, 11,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 17, 15, 19, 14,
       21, 18, 16, 20, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 31, 28, 23,
       24, 26, 27, 30, 25, 29, 22,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 17, 15, 14,
       21, 19, 18, 16, 20, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 40, 36, 33, 37,
       32, 41, 35, 34, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 22, 29, 25, 30, 27, 26, 24, 23, 28, 31, 39, 38, 32, 37, 40, 36,
       33, 35, 34, 41, 20, 16, 19, 18, 21, 14, 15, 17, 12, 13,  6,  9,  8,
       11,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 17, 15, 14,
       21, 18, 19, 16, 20, 41, 34, 35, 33, 36, 40, 37, 32, 38, 39, 31, 28,
       23, 24, 26, 27, 30, 25, 29, 22], dtype=int64)]
2025-08-05 10:28:58,123 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:58,124 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 7, 15, 19, 18, 21, 14, 20, 17, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21989.0}, {'tour': [26, 12, 37, 18, 24, 21, 10, 14, 23, 17, 2, 19, 0, 36, 29, 7, 15, 5, 4, 3, 22, 25, 33, 6, 34, 41, 35, 9, 27, 39, 1, 13, 40, 31, 30, 11, 20, 28, 32, 38, 8, 16], 'cur_cost': 103086.0}, {'tour': [0, 10, 3, 1, 5, 4, 2, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 9, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21565.0}, {'tour': array([ 9, 17,  5, 18, 36, 21, 32,  7, 19, 33, 11, 29,  6, 10,  1, 23, 26,
        2, 41,  4, 22, 20, 28,  3, 34, 38, 31, 24, 15, 25,  0, 39, 14,  8,
       12, 35, 13, 30, 27, 16, 40, 37], dtype=int64), 'cur_cost': 123666.0}, {'tour': array([15,  7, 20, 23, 10, 39, 21,  8,  2, 22,  1,  0, 28, 30, 14, 26, 31,
       11,  5,  9, 38, 16, 18, 24, 27,  4, 25, 12,  3, 17,  6, 19, 32, 37,
       35, 29, 36, 34, 41, 33, 40, 13], dtype=int64), 'cur_cost': 95551.0}, {'tour': [16, 19, 5, 21, 3, 6, 20, 12, 0, 13, 22, 37, 26, 30, 36, 32, 27, 24, 40, 23, 31, 25, 29, 34, 28, 15, 9, 11, 18, 14, 10, 17, 2, 7, 1, 4, 39, 38, 35, 41, 33, 8], 'cur_cost': 62585.0}, {'tour': [0, 3, 2, 14, 21, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18608.0}, {'tour': [15, 19, 21, 1, 11, 10, 2, 14, 0, 17, 4, 24, 23, 40, 30, 25, 39, 41, 37, 29, 34, 35, 36, 28, 33, 16, 9, 8, 5, 6, 20, 13, 18, 3, 22, 27, 31, 38, 26, 32, 12, 7], 'cur_cost': 62448.0}, {'tour': [31, 25, 30, 36, 26, 34, 20, 13, 4, 2, 0, 16, 10, 6, 9, 19, 21, 3, 17, 23, 40, 28, 38, 41, 37, 22, 29, 33, 24, 27, 14, 11, 8, 15, 5, 1, 18, 12, 7, 35, 32, 39], 'cur_cost': 49601.0}, {'tour': array([15, 26, 34, 40, 41,  0, 39, 29, 17, 12, 38, 22, 27, 11, 36,  2,  5,
       35,  3, 24, 37,  7, 18, 19, 25, 16, 30, 13, 23,  1, 20, 31, 28,  6,
        8, 10, 32,  9,  4, 33, 14, 21], dtype=int64), 'cur_cost': 111129.0}]
2025-08-05 10:28:58,126 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:58,126 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 247, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 247, 'cache_hits': 0, 'similarity_calculations': 1200, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:58,126 - experts.management.collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([15, 26, 34, 40, 41,  0, 39, 29, 17, 12, 38, 22, 27, 11, 36,  2,  5,
       35,  3, 24, 37,  7, 18, 19, 25, 16, 30, 13, 23,  1, 20, 31, 28,  6,
        8, 10, 32,  9,  4, 33, 14, 21], dtype=int64), 'cur_cost': 111129.0, 'intermediate_solutions': [{'tour': array([22, 35, 11, 26, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 109055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26, 22, 35, 11, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 103926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 26, 22, 35, 11, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108908.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 26, 22, 35, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 15, 26, 22, 35, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:58,127 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 111129.00)
2025-08-05 10:28:58,127 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:58,127 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:58,130 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 7, 15, 19, 18, 21, 14, 20, 17, 12, 13, 6, 11, 8, 9, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21989.0, 'intermediate_solutions': [{'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 27, 4, 2, 22, 29, 24, 26, 3, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 38049.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 13, 9, 14, 21, 19, 16, 18, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 2, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21168.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 13, 9, 14, 21, 19, 18, 16, 20, 15, 17, 12, 6, 11, 8, 7, 10, 5, 1, 3, 4, 22, 29, 24, 26, 27, 25, 30, 2, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 29491.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [26, 12, 37, 18, 24, 21, 10, 14, 23, 17, 2, 19, 0, 36, 29, 7, 15, 5, 4, 3, 22, 25, 33, 6, 34, 41, 35, 9, 27, 39, 1, 13, 40, 31, 30, 11, 20, 28, 32, 38, 8, 16], 'cur_cost': 103086.0, 'intermediate_solutions': [{'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 17, 19, 18, 16, 20, 15, 21, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 25092.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 11, 3, 9, 8, 7, 10, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 41, 34, 35, 33, 40, 36, 37, 32, 38], 'cur_cost': 25160.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 11, 3, 9, 8, 7, 10, 26, 13, 6, 12, 14, 21, 19, 18, 16, 20, 15, 17, 4, 2, 1, 5, 22, 29, 24, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 33148.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 3, 1, 5, 4, 2, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 9, 23, 28, 31, 26, 27, 25, 24, 29, 22, 30, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 21565.0, 'intermediate_solutions': [{'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 33, 8, 7, 10, 9, 5, 1, 2, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 11, 35, 34, 41], 'cur_cost': 42132.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 38, 39, 23, 31, 28, 30, 25, 27, 26, 24, 29, 22, 4, 2, 1, 5, 9, 10, 7, 8, 11, 6, 13, 17, 16, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 31513.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 3, 18, 19, 21, 14, 15, 20, 16, 17, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4, 22, 24, 26, 27, 25, 30, 28, 29, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 24993.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 17,  5, 18, 36, 21, 32,  7, 19, 33, 11, 29,  6, 10,  1, 23, 26,
        2, 41,  4, 22, 20, 28,  3, 34, 38, 31, 24, 15, 25,  0, 39, 14,  8,
       12, 35, 13, 30, 27, 16, 40, 37], dtype=int64), 'cur_cost': 123666.0, 'intermediate_solutions': [{'tour': array([37, 17, 26,  8, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 90614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 8, 37, 17, 26, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91164.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([28,  8, 37, 17, 26,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91980.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([26,  8, 37, 17, 28,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 91975.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([26, 28,  8, 37, 17,  1, 11,  6, 23, 20, 30,  4,  0, 36, 40, 32, 13,
       16,  7, 12, 22, 24, 29, 33, 34, 21, 27, 25, 38,  3,  2,  5, 18, 31,
        9, 15, 10, 19, 39, 14, 41, 35]), 'cur_cost': 87323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([15,  7, 20, 23, 10, 39, 21,  8,  2, 22,  1,  0, 28, 30, 14, 26, 31,
       11,  5,  9, 38, 16, 18, 24, 27,  4, 25, 12,  3, 17,  6, 19, 32, 37,
       35, 29, 36, 34, 41, 33, 40, 13], dtype=int64), 'cur_cost': 95551.0, 'intermediate_solutions': [{'tour': array([23, 28, 11, 39, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 112509.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([39, 23, 28, 11, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 111126.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 39, 23, 28, 11, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 108244.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 39, 23, 28, 24, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 111733.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 24, 39, 23, 28, 20, 12, 38, 34, 16,  2, 19,  6, 13, 29, 41, 26,
        7, 30, 15,  9, 25, 33,  0, 22,  4, 36, 18, 21, 10,  1,  5, 40,  3,
       35, 17, 14, 31, 32, 27,  8, 37]), 'cur_cost': 112473.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [16, 19, 5, 21, 3, 6, 20, 12, 0, 13, 22, 37, 26, 30, 36, 32, 27, 24, 40, 23, 31, 25, 29, 34, 28, 15, 9, 11, 18, 14, 10, 17, 2, 7, 1, 4, 39, 38, 35, 41, 33, 8], 'cur_cost': 62585.0, 'intermediate_solutions': [{'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 12, 20, 4, 1, 6, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 38, 36, 25, 15, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 62340.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [28, 30, 23, 24, 40, 22, 3, 10, 13, 21, 12, 25, 36, 38, 39, 27, 33, 35, 41, 26, 0, 9, 18, 16, 6, 1, 4, 20, 15, 5, 14, 8, 17, 7, 19, 34, 32, 29, 11, 2, 31, 37], 'cur_cost': 67714.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [28, 30, 23, 24, 40, 22, 29, 32, 34, 19, 7, 17, 8, 14, 5, 15, 20, 4, 1, 16, 18, 9, 0, 26, 41, 35, 33, 27, 39, 6, 38, 36, 25, 12, 21, 13, 10, 3, 11, 2, 31, 37], 'cur_cost': 68695.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 14, 21, 19, 18, 16, 20, 15, 17, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 4, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41], 'cur_cost': 18608.0, 'intermediate_solutions': [{'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 24, 6, 36, 33, 2, 17, 38, 22, 13, 4, 31, 0, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 85950.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [12, 9, 18, 19, 7, 10, 15, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 30, 41, 40, 29, 34, 20, 11, 21], 'cur_cost': 81243.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [12, 9, 18, 19, 7, 10, 25, 14, 16, 5, 3, 1, 35, 23, 39, 37, 32, 8, 28, 27, 26, 22, 6, 36, 33, 2, 17, 38, 24, 13, 4, 31, 0, 15, 30, 11, 20, 34, 29, 40, 41, 21], 'cur_cost': 86334.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [15, 19, 21, 1, 11, 10, 2, 14, 0, 17, 4, 24, 23, 40, 30, 25, 39, 41, 37, 29, 34, 35, 36, 28, 33, 16, 9, 8, 5, 6, 20, 13, 18, 3, 22, 27, 31, 38, 26, 32, 12, 7], 'cur_cost': 62448.0, 'intermediate_solutions': [{'tour': [0, 3, 19, 10, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 18, 21, 14, 12, 13, 6, 11, 8, 7, 22, 9, 5, 1, 2, 4], 'cur_cost': 26418.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 18, 16, 20, 15, 17, 41, 34, 35, 33, 40, 36, 37, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 5, 1, 2, 4], 'cur_cost': 22048.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 19, 22, 29, 24, 26, 27, 25, 30, 28, 31, 23, 39, 38, 32, 37, 36, 40, 33, 35, 34, 41, 17, 15, 20, 16, 21, 14, 12, 13, 6, 11, 8, 7, 10, 9, 18, 5, 1, 2, 4], 'cur_cost': 16012.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [31, 25, 30, 36, 26, 34, 20, 13, 4, 2, 0, 16, 10, 6, 9, 19, 21, 3, 17, 23, 40, 28, 38, 41, 37, 22, 29, 33, 24, 27, 14, 11, 8, 15, 5, 1, 18, 12, 7, 35, 32, 39], 'cur_cost': 49601.0, 'intermediate_solutions': [{'tour': [13, 1, 15, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 5, 22, 23, 29, 27, 33, 38, 39, 21, 19, 12, 4, 34], 'cur_cost': 70383.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 9, 3, 8, 5, 22, 23, 29, 27, 33, 38, 39, 15, 19, 12, 4, 34], 'cur_cost': 70326.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [13, 1, 21, 14, 17, 16, 7, 0, 10, 18, 11, 2, 24, 32, 30, 31, 40, 28, 37, 36, 25, 26, 41, 35, 20, 6, 3, 9, 8, 38, 5, 22, 23, 29, 27, 33, 39, 15, 19, 12, 4, 34], 'cur_cost': 76646.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 26, 34, 40, 41,  0, 39, 29, 17, 12, 38, 22, 27, 11, 36,  2,  5,
       35,  3, 24, 37,  7, 18, 19, 25, 16, 30, 13, 23,  1, 20, 31, 28,  6,
        8, 10, 32,  9,  4, 33, 14, 21], dtype=int64), 'cur_cost': 111129.0, 'intermediate_solutions': [{'tour': array([22, 35, 11, 26, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 109055.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([26, 22, 35, 11, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 103926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([15, 26, 22, 35, 11, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108908.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([11, 26, 22, 35, 15, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([11, 15, 26, 22, 35, 19, 39, 16,  2, 37, 13,  0,  9, 41, 40,  1,  8,
       32,  6, 17, 33,  5, 30, 20, 10, 12, 18, 31,  4,  7,  3, 24, 34, 28,
       14, 21, 38, 25, 23, 27, 29, 36]), 'cur_cost': 108921.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}]
2025-08-05 10:28:58,130 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:58,131 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:58,135 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=18608.000, 多样性=0.949
2025-08-05 10:28:58,135 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:58,136 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:58,136 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:58,137 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.13256854239742438, 'best_improvement': -0.21359159981738735}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.011273957158962794}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.08307314656444927, 'recent_improvements': [-0.16773198931531383, 0.11488603201553711, -0.001585696186415295], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 10, 'new_count': 10, 'count_change': 0, 'old_best_cost': 14408, 'new_best_cost': 14408, 'quality_improvement': 0.0, 'old_diversity': 0.834920634920635, 'new_diversity': 0.834920634920635, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:58,139 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:58,143 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite7_42_solution.json
2025-08-05 10:28:58,144 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite7_42_20250805_102858.solution
2025-08-05 10:28:58,144 - __main__ - INFO - 实例执行完成 - 运行时间: 1.69s, 最佳成本: 14408
2025-08-05 10:28:58,144 - __main__ - INFO - 实例 composite7_42 处理完成
