2025-08-03 17:07:20,198 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-08-03 17:07:20,198 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-03 17:07:20,201 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:07:20,218 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=9936.000, 多样性=0.976
2025-08-03 17:07:20,224 - PathExpert - INFO - 开始路径结构分析
2025-08-03 17:07:20,231 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.976
2025-08-03 17:07:20,242 - EliteExpert - INFO - 开始精英解分析
2025-08-03 17:07:20,245 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/1)
2025-08-03 17:07:20,247 - LandscapeExpert - INFO - 使用直接传入的种群数据: 20个个体
2025-08-03 17:07:20,249 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-03 17:07:20,250 - LandscapeExpert - INFO - 数据提取成功: 20个路径, 20个适应度值
2025-08-03 17:07:20,553 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.150, 适应度梯度: 829.520, 聚类评分: 0.000, 覆盖率: 0.002, 收敛趋势: 0.000, 多样性: 1.000
2025-08-03 17:07:20,554 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-03 17:07:20,554 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-03 17:07:20,621 - visualization.landscape_visualizer - INFO - 已添加 3 个精英解标记
2025-08-03 17:07:20,939 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\landscape_unknown_instance_iter_1_20250803_170720.html
2025-08-03 17:07:20,989 - visualization.landscape_visualizer - INFO - 图表已保存: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\visualization_output\dashboard_unknown_instance_iter_1_20250803_170720.html
2025-08-03 17:07:20,990 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 1
2025-08-03 17:07:20,990 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-03 17:07:20,991 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.7456秒
2025-08-03 17:07:20,991 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 2, 'max_size': 500, 'hits': 0, 'misses': 2, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 2, 'max_size': 100, 'hits': 2, 'misses': 2, 'hit_rate': 0.5, 'evictions': 0, 'ttl': 7200}}
2025-08-03 17:07:20,992 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.15, 'modality': 'uni-modal', 'deceptiveness': 'low', 'gradient_strength': 829.5200000000004, 'local_optima_density': 0.15, 'gradient_variance': 2732055925.2935996, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.002, 'fitness_entropy': 0.9630603734213402, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.002)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}, {'type': 'strong_gradient', 'potential': 'high', 'description': '强梯度区域 (梯度强度: 829.520)', 'recommendation': '增加利用性操作'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 1, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754212040.5540488, 'performance_metrics': {}}}
2025-08-03 17:07:20,993 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-03 17:07:20,994 - experts.strategy.enhanced_strategy_expert - INFO - 开始增强策略分配分析 (迭代 0)
2025-08-03 17:07:20,994 - experts.analysis.individual_state_analyzer - INFO - 开始分析种群状态 (迭代 0, 种群大小: 20)
2025-08-03 17:07:20,995 - experts.strategy.enhanced_strategy_expert - ERROR - 策略分配过程中发生错误: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:07:20,995 - experts.strategy.enhanced_strategy_expert - WARNING - 使用回退策略分配
2025-08-03 17:07:20,995 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:07:20,996 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:07:20,997 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-03 17:07:20,997 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-08-03 17:07:20,997 - __main__ - INFO - 策略分配完整报告: 错误回退: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-08-03 17:07:20,998 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-03 17:07:20,998 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 2, 4, 5} (总数: 4, 保护比例: 0.20)
2025-08-03 17:07:20,998 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-03 17:07:20,998 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-03 17:07:20,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:21,003 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:07:21,003 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:21,184 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114840.0, 路径长度: 66
2025-08-03 17:07:21,184 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}
2025-08-03 17:07:21,185 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 114840.00)
2025-08-03 17:07:21,185 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-03 17:07:21,186 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:21,187 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:21,188 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 102492.0
2025-08-03 17:07:23,185 - ExploitationExpert - INFO - res_population_num: 1
2025-08-03 17:07:23,186 - ExploitationExpert - INFO - res_population_costs: [9821.0]
2025-08-03 17:07:23,186 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-08-03 17:07:23,187 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:23,187 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': array([51, 38, 45, 50, 44, 41, 39, 47, 49, 40, 43, 48, 46, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9976.0}, {'tour': array([53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 61, 55,  2,  8,  5,
        4,  6,  9, 11,  7,  3,  1,  0, 10, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29,
       32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
      dtype=int64), 'cur_cost': 10049.0}, {'tour': array([22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10030.0}, {'tour': array([39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9948.0}, {'tour': array([11, 31, 46, 15, 38, 43, 49, 35,  5, 56, 12, 52, 18, 19, 24,  3, 16,
       64,  8, 48, 20, 53, 51, 63, 36, 58, 30, 44, 39, 25, 57, 65, 34, 14,
       13, 55, 45, 62, 10, 41, 37,  9, 26,  4,  6, 40, 50, 23, 59, 21, 54,
        7,  2,  0, 27,  1, 42, 22, 28, 60, 61, 32, 47, 17, 33, 29],
      dtype=int64), 'cur_cost': 109385.0}, {'tour': array([13, 49,  4, 18, 40, 54, 31, 57, 46,  9, 43, 34,  8, 51, 41, 53, 42,
       58, 22, 29, 61, 56, 27, 30,  1, 62, 48, 60, 16, 59, 10,  2, 63, 26,
        0, 44, 47, 52, 25, 11, 33, 35, 20,  7, 50,  3, 21, 36, 65, 28, 14,
       37,  5, 17, 12, 45, 24, 38, 15,  6, 23, 32, 19, 39, 55, 64],
      dtype=int64), 'cur_cost': 119043.0}, {'tour': array([23, 50,  0, 13,  9, 28, 56, 27, 31,  3,  4, 53, 43, 55, 59, 40, 16,
        5, 49, 62, 41, 14, 15, 48, 57, 37, 19, 11, 52, 29, 61, 30, 33, 64,
       25, 51, 39, 38,  6, 63, 10,  7, 42, 22,  1, 44, 46, 58, 36, 65,  2,
       35, 60, 32, 47, 18, 12, 17,  8, 54, 34, 45, 24, 20, 21, 26],
      dtype=int64), 'cur_cost': 112514.0}, {'tour': array([10, 37, 55, 45,  2, 15, 44, 40, 21, 32, 11, 49, 52, 39, 63, 58,  6,
       59, 25, 64, 57,  9, 12, 65, 47,  8, 38,  5, 29, 51,  7, 30, 17, 61,
        4, 53, 20, 50, 27, 36, 35, 34, 60, 46, 13, 18, 31, 62,  0, 48, 56,
       42, 54, 19,  3, 33, 24, 41, 23, 26, 43,  1, 16, 22, 14, 28],
      dtype=int64), 'cur_cost': 113282.0}, {'tour': array([50,  1, 15, 37, 19, 47, 29, 14, 40, 36, 54, 51, 32, 41, 64, 43, 38,
       28, 21, 59, 17, 34, 20, 30, 12, 39, 62, 16, 46, 45, 18,  7, 65, 55,
       57, 23,  8, 10,  6,  9, 60, 52, 61, 56, 25, 33, 53, 24, 26, 13, 35,
       63,  5,  0, 49, 42, 11,  2,  3, 31, 44, 27, 22, 58, 48,  4],
      dtype=int64), 'cur_cost': 96177.0}, {'tour': array([26, 12, 29, 40,  6, 38, 50, 16, 34,  9, 62, 28, 63, 41, 31, 39, 15,
       22, 14, 23, 21, 36, 13, 24,  3, 49, 30,  7, 51, 47, 56, 53, 44, 65,
       10, 17, 58, 59, 27, 57, 54, 52, 55, 35, 64,  2,  8, 48, 32,  5, 11,
        4, 25,  0, 46, 45, 33,  1, 42, 60, 20, 61, 19, 18, 37, 43],
      dtype=int64), 'cur_cost': 104050.0}, {'tour': array([57, 32, 11, 13, 62, 36, 39, 18, 15, 45, 20, 19, 40, 33, 14, 60,  6,
       63, 44, 10, 21, 47, 59, 48,  5, 12, 50, 23, 43, 38,  1,  0, 24, 34,
       54,  9, 41,  2, 58, 52, 31, 17,  4,  8,  7, 27, 29,  3, 42, 16, 37,
       56, 64, 55, 49, 51, 25, 65, 46, 28, 26, 61, 22, 53, 30, 35],
      dtype=int64), 'cur_cost': 105933.0}, {'tour': array([36, 59, 57, 12,  7, 64, 46, 45, 51, 15, 21, 52,  8, 19, 48, 18, 54,
        4, 11, 28,  1, 38, 22, 61, 50, 55, 26, 14, 60, 32,  9,  6, 41, 34,
        2, 49, 24, 58, 39, 35, 63,  3,  5, 16, 62, 23, 42, 10, 29, 17, 20,
       47, 53, 33, 44, 65, 30, 13, 37, 25, 43, 40, 27, 31, 56,  0],
      dtype=int64), 'cur_cost': 111767.0}, {'tour': array([53, 18,  8, 45, 38, 36, 22, 24, 32, 33, 57, 61, 48,  6, 19, 47, 12,
       20, 30,  1, 51, 26, 21, 63,  2, 13, 64, 52, 37, 27, 34, 62, 29, 65,
       54, 46, 41, 39,  9, 43, 10, 59, 40, 50, 25, 15, 35, 28,  3, 55,  4,
       58, 31,  7, 44, 49, 16,  0, 60, 23, 14,  5, 42, 11, 56, 17],
      dtype=int64), 'cur_cost': 98442.0}, {'tour': array([44, 10, 33, 30, 11, 47, 13, 48, 60, 21, 46, 43,  8, 28, 27, 20, 52,
        5,  1, 64, 38,  7, 49,  6, 56,  2, 59, 58, 54, 39, 24, 18, 26, 61,
       40, 25, 31, 36, 41, 12, 42, 62, 22, 14,  9, 23, 50,  3,  4, 45, 57,
       19, 37, 35, 63, 34, 32, 51, 55, 53,  0, 17, 29, 65, 16, 15],
      dtype=int64), 'cur_cost': 102262.0}, {'tour': array([21, 33, 29, 59, 52, 25, 24, 28, 48, 43,  9, 37, 46, 47, 49, 23, 41,
       36, 18, 44, 30, 60, 64, 56,  0,  4, 42,  8, 14, 50, 19, 51, 54, 17,
       40,  7, 62, 63, 57, 13, 20, 38, 32, 27, 53, 22, 35, 15, 16,  5, 39,
       10, 12, 61, 58,  2, 31, 55, 26,  6,  1, 45, 11, 65,  3, 34],
      dtype=int64), 'cur_cost': 94082.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:23,200 - ExploitationExpert - INFO - 局部搜索耗时: 2.01秒
2025-08-03 17:07:23,200 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-08-03 17:07:23,201 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}
2025-08-03 17:07:23,201 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 102492.00)
2025-08-03 17:07:23,202 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-03 17:07:23,202 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-03 17:07:23,202 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:23,207 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:23,207 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:23,208 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12330.0, 路径长度: 66
2025-08-03 17:07:23,208 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}
2025-08-03 17:07:23,209 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 12330.00)
2025-08-03 17:07:23,209 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-03 17:07:23,209 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-03 17:07:23,209 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:23,230 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:07:23,231 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:23,231 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61577.0, 路径长度: 66
2025-08-03 17:07:23,231 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}
2025-08-03 17:07:23,232 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 61577.00)
2025-08-03 17:07:23,232 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-03 17:07:23,232 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:23,233 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:23,233 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 106048.0
2025-08-03 17:07:25,516 - ExploitationExpert - INFO - res_population_num: 2
2025-08-03 17:07:25,517 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0]
2025-08-03 17:07:25,518 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:07:25,520 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:25,520 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': array([39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9948.0}, {'tour': array([11, 31, 46, 15, 38, 43, 49, 35,  5, 56, 12, 52, 18, 19, 24,  3, 16,
       64,  8, 48, 20, 53, 51, 63, 36, 58, 30, 44, 39, 25, 57, 65, 34, 14,
       13, 55, 45, 62, 10, 41, 37,  9, 26,  4,  6, 40, 50, 23, 59, 21, 54,
        7,  2,  0, 27,  1, 42, 22, 28, 60, 61, 32, 47, 17, 33, 29],
      dtype=int64), 'cur_cost': 109385.0}, {'tour': array([13, 49,  4, 18, 40, 54, 31, 57, 46,  9, 43, 34,  8, 51, 41, 53, 42,
       58, 22, 29, 61, 56, 27, 30,  1, 62, 48, 60, 16, 59, 10,  2, 63, 26,
        0, 44, 47, 52, 25, 11, 33, 35, 20,  7, 50,  3, 21, 36, 65, 28, 14,
       37,  5, 17, 12, 45, 24, 38, 15,  6, 23, 32, 19, 39, 55, 64],
      dtype=int64), 'cur_cost': 119043.0}, {'tour': array([23, 50,  0, 13,  9, 28, 56, 27, 31,  3,  4, 53, 43, 55, 59, 40, 16,
        5, 49, 62, 41, 14, 15, 48, 57, 37, 19, 11, 52, 29, 61, 30, 33, 64,
       25, 51, 39, 38,  6, 63, 10,  7, 42, 22,  1, 44, 46, 58, 36, 65,  2,
       35, 60, 32, 47, 18, 12, 17,  8, 54, 34, 45, 24, 20, 21, 26],
      dtype=int64), 'cur_cost': 112514.0}, {'tour': array([10, 37, 55, 45,  2, 15, 44, 40, 21, 32, 11, 49, 52, 39, 63, 58,  6,
       59, 25, 64, 57,  9, 12, 65, 47,  8, 38,  5, 29, 51,  7, 30, 17, 61,
        4, 53, 20, 50, 27, 36, 35, 34, 60, 46, 13, 18, 31, 62,  0, 48, 56,
       42, 54, 19,  3, 33, 24, 41, 23, 26, 43,  1, 16, 22, 14, 28],
      dtype=int64), 'cur_cost': 113282.0}, {'tour': array([50,  1, 15, 37, 19, 47, 29, 14, 40, 36, 54, 51, 32, 41, 64, 43, 38,
       28, 21, 59, 17, 34, 20, 30, 12, 39, 62, 16, 46, 45, 18,  7, 65, 55,
       57, 23,  8, 10,  6,  9, 60, 52, 61, 56, 25, 33, 53, 24, 26, 13, 35,
       63,  5,  0, 49, 42, 11,  2,  3, 31, 44, 27, 22, 58, 48,  4],
      dtype=int64), 'cur_cost': 96177.0}, {'tour': array([26, 12, 29, 40,  6, 38, 50, 16, 34,  9, 62, 28, 63, 41, 31, 39, 15,
       22, 14, 23, 21, 36, 13, 24,  3, 49, 30,  7, 51, 47, 56, 53, 44, 65,
       10, 17, 58, 59, 27, 57, 54, 52, 55, 35, 64,  2,  8, 48, 32,  5, 11,
        4, 25,  0, 46, 45, 33,  1, 42, 60, 20, 61, 19, 18, 37, 43],
      dtype=int64), 'cur_cost': 104050.0}, {'tour': array([57, 32, 11, 13, 62, 36, 39, 18, 15, 45, 20, 19, 40, 33, 14, 60,  6,
       63, 44, 10, 21, 47, 59, 48,  5, 12, 50, 23, 43, 38,  1,  0, 24, 34,
       54,  9, 41,  2, 58, 52, 31, 17,  4,  8,  7, 27, 29,  3, 42, 16, 37,
       56, 64, 55, 49, 51, 25, 65, 46, 28, 26, 61, 22, 53, 30, 35],
      dtype=int64), 'cur_cost': 105933.0}, {'tour': array([36, 59, 57, 12,  7, 64, 46, 45, 51, 15, 21, 52,  8, 19, 48, 18, 54,
        4, 11, 28,  1, 38, 22, 61, 50, 55, 26, 14, 60, 32,  9,  6, 41, 34,
        2, 49, 24, 58, 39, 35, 63,  3,  5, 16, 62, 23, 42, 10, 29, 17, 20,
       47, 53, 33, 44, 65, 30, 13, 37, 25, 43, 40, 27, 31, 56,  0],
      dtype=int64), 'cur_cost': 111767.0}, {'tour': array([53, 18,  8, 45, 38, 36, 22, 24, 32, 33, 57, 61, 48,  6, 19, 47, 12,
       20, 30,  1, 51, 26, 21, 63,  2, 13, 64, 52, 37, 27, 34, 62, 29, 65,
       54, 46, 41, 39,  9, 43, 10, 59, 40, 50, 25, 15, 35, 28,  3, 55,  4,
       58, 31,  7, 44, 49, 16,  0, 60, 23, 14,  5, 42, 11, 56, 17],
      dtype=int64), 'cur_cost': 98442.0}, {'tour': array([44, 10, 33, 30, 11, 47, 13, 48, 60, 21, 46, 43,  8, 28, 27, 20, 52,
        5,  1, 64, 38,  7, 49,  6, 56,  2, 59, 58, 54, 39, 24, 18, 26, 61,
       40, 25, 31, 36, 41, 12, 42, 62, 22, 14,  9, 23, 50,  3,  4, 45, 57,
       19, 37, 35, 63, 34, 32, 51, 55, 53,  0, 17, 29, 65, 16, 15],
      dtype=int64), 'cur_cost': 102262.0}, {'tour': array([21, 33, 29, 59, 52, 25, 24, 28, 48, 43,  9, 37, 46, 47, 49, 23, 41,
       36, 18, 44, 30, 60, 64, 56,  0,  4, 42,  8, 14, 50, 19, 51, 54, 17,
       40,  7, 62, 63, 57, 13, 20, 38, 32, 27, 53, 22, 35, 15, 16,  5, 39,
       10, 12, 61, 58,  2, 31, 55, 26,  6,  1, 45, 11, 65,  3, 34],
      dtype=int64), 'cur_cost': 94082.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:25,533 - ExploitationExpert - INFO - 局部搜索耗时: 2.30秒
2025-08-03 17:07:25,533 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-08-03 17:07:25,534 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}
2025-08-03 17:07:25,534 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 106048.00)
2025-08-03 17:07:25,535 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-03 17:07:25,535 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-03 17:07:25,535 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:25,541 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:25,541 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:25,542 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12439.0, 路径长度: 66
2025-08-03 17:07:25,542 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}
2025-08-03 17:07:25,543 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 12439.00)
2025-08-03 17:07:25,544 - experts.management.collaboration_manager - INFO - 为个体 6 生成探索路径
2025-08-03 17:07:25,544 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-08-03 17:07:25,545 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:25,553 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:25,554 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:25,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10326.0, 路径长度: 66
2025-08-03 17:07:25,554 - experts.management.collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}
2025-08-03 17:07:25,555 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 10326.00)
2025-08-03 17:07:25,555 - experts.management.collaboration_manager - INFO - 为个体 7 生成利用路径
2025-08-03 17:07:25,555 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:25,556 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:25,556 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101457.0
2025-08-03 17:07:26,124 - ExploitationExpert - INFO - res_population_num: 3
2025-08-03 17:07:26,124 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9547.0]
2025-08-03 17:07:26,125 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-08-03 17:07:26,127 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:26,127 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}, {'tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}, {'tour': array([23, 50,  0, 13,  9, 28, 56, 27, 31,  3,  4, 53, 43, 55, 59, 40, 16,
        5, 49, 62, 41, 14, 15, 48, 57, 37, 19, 11, 52, 29, 61, 30, 33, 64,
       25, 51, 39, 38,  6, 63, 10,  7, 42, 22,  1, 44, 46, 58, 36, 65,  2,
       35, 60, 32, 47, 18, 12, 17,  8, 54, 34, 45, 24, 20, 21, 26],
      dtype=int64), 'cur_cost': 112514.0}, {'tour': array([10, 37, 55, 45,  2, 15, 44, 40, 21, 32, 11, 49, 52, 39, 63, 58,  6,
       59, 25, 64, 57,  9, 12, 65, 47,  8, 38,  5, 29, 51,  7, 30, 17, 61,
        4, 53, 20, 50, 27, 36, 35, 34, 60, 46, 13, 18, 31, 62,  0, 48, 56,
       42, 54, 19,  3, 33, 24, 41, 23, 26, 43,  1, 16, 22, 14, 28],
      dtype=int64), 'cur_cost': 113282.0}, {'tour': array([50,  1, 15, 37, 19, 47, 29, 14, 40, 36, 54, 51, 32, 41, 64, 43, 38,
       28, 21, 59, 17, 34, 20, 30, 12, 39, 62, 16, 46, 45, 18,  7, 65, 55,
       57, 23,  8, 10,  6,  9, 60, 52, 61, 56, 25, 33, 53, 24, 26, 13, 35,
       63,  5,  0, 49, 42, 11,  2,  3, 31, 44, 27, 22, 58, 48,  4],
      dtype=int64), 'cur_cost': 96177.0}, {'tour': array([26, 12, 29, 40,  6, 38, 50, 16, 34,  9, 62, 28, 63, 41, 31, 39, 15,
       22, 14, 23, 21, 36, 13, 24,  3, 49, 30,  7, 51, 47, 56, 53, 44, 65,
       10, 17, 58, 59, 27, 57, 54, 52, 55, 35, 64,  2,  8, 48, 32,  5, 11,
        4, 25,  0, 46, 45, 33,  1, 42, 60, 20, 61, 19, 18, 37, 43],
      dtype=int64), 'cur_cost': 104050.0}, {'tour': array([57, 32, 11, 13, 62, 36, 39, 18, 15, 45, 20, 19, 40, 33, 14, 60,  6,
       63, 44, 10, 21, 47, 59, 48,  5, 12, 50, 23, 43, 38,  1,  0, 24, 34,
       54,  9, 41,  2, 58, 52, 31, 17,  4,  8,  7, 27, 29,  3, 42, 16, 37,
       56, 64, 55, 49, 51, 25, 65, 46, 28, 26, 61, 22, 53, 30, 35],
      dtype=int64), 'cur_cost': 105933.0}, {'tour': array([36, 59, 57, 12,  7, 64, 46, 45, 51, 15, 21, 52,  8, 19, 48, 18, 54,
        4, 11, 28,  1, 38, 22, 61, 50, 55, 26, 14, 60, 32,  9,  6, 41, 34,
        2, 49, 24, 58, 39, 35, 63,  3,  5, 16, 62, 23, 42, 10, 29, 17, 20,
       47, 53, 33, 44, 65, 30, 13, 37, 25, 43, 40, 27, 31, 56,  0],
      dtype=int64), 'cur_cost': 111767.0}, {'tour': array([53, 18,  8, 45, 38, 36, 22, 24, 32, 33, 57, 61, 48,  6, 19, 47, 12,
       20, 30,  1, 51, 26, 21, 63,  2, 13, 64, 52, 37, 27, 34, 62, 29, 65,
       54, 46, 41, 39,  9, 43, 10, 59, 40, 50, 25, 15, 35, 28,  3, 55,  4,
       58, 31,  7, 44, 49, 16,  0, 60, 23, 14,  5, 42, 11, 56, 17],
      dtype=int64), 'cur_cost': 98442.0}, {'tour': array([44, 10, 33, 30, 11, 47, 13, 48, 60, 21, 46, 43,  8, 28, 27, 20, 52,
        5,  1, 64, 38,  7, 49,  6, 56,  2, 59, 58, 54, 39, 24, 18, 26, 61,
       40, 25, 31, 36, 41, 12, 42, 62, 22, 14,  9, 23, 50,  3,  4, 45, 57,
       19, 37, 35, 63, 34, 32, 51, 55, 53,  0, 17, 29, 65, 16, 15],
      dtype=int64), 'cur_cost': 102262.0}, {'tour': array([21, 33, 29, 59, 52, 25, 24, 28, 48, 43,  9, 37, 46, 47, 49, 23, 41,
       36, 18, 44, 30, 60, 64, 56,  0,  4, 42,  8, 14, 50, 19, 51, 54, 17,
       40,  7, 62, 63, 57, 13, 20, 38, 32, 27, 53, 22, 35, 15, 16,  5, 39,
       10, 12, 61, 58,  2, 31, 55, 26,  6,  1, 45, 11, 65,  3, 34],
      dtype=int64), 'cur_cost': 94082.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:26,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.58秒
2025-08-03 17:07:26,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-08-03 17:07:26,138 - experts.management.collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}
2025-08-03 17:07:26,138 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 101457.00)
2025-08-03 17:07:26,139 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-03 17:07:26,139 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-03 17:07:26,139 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,145 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:26,146 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14841.0, 路径长度: 66
2025-08-03 17:07:26,148 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}
2025-08-03 17:07:26,151 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 14841.00)
2025-08-03 17:07:26,152 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-03 17:07:26,152 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-03 17:07:26,152 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,159 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:26,161 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12225.0, 路径长度: 66
2025-08-03 17:07:26,162 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}
2025-08-03 17:07:26,163 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12225.00)
2025-08-03 17:07:26,163 - experts.management.collaboration_manager - INFO - 为个体 10 生成利用路径
2025-08-03 17:07:26,164 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:26,164 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:26,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 10 处的路径，新成本: 103899.0
2025-08-03 17:07:26,238 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:07:26,238 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9547.0, 9542, 9542, 9541, 9540, 9540, 9534, 9534, 9521]
2025-08-03 17:07:26,239 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:07:26,245 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:26,245 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}, {'tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}, {'tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}, {'tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}, {'tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}, {'tour': array([26, 12, 29, 40,  6, 38, 50, 16, 34,  9, 62, 28, 63, 41, 31, 39, 15,
       22, 14, 23, 21, 36, 13, 24,  3, 49, 30,  7, 51, 47, 56, 53, 44, 65,
       10, 17, 58, 59, 27, 57, 54, 52, 55, 35, 64,  2,  8, 48, 32,  5, 11,
        4, 25,  0, 46, 45, 33,  1, 42, 60, 20, 61, 19, 18, 37, 43],
      dtype=int64), 'cur_cost': 104050.0}, {'tour': array([57, 32, 11, 13, 62, 36, 39, 18, 15, 45, 20, 19, 40, 33, 14, 60,  6,
       63, 44, 10, 21, 47, 59, 48,  5, 12, 50, 23, 43, 38,  1,  0, 24, 34,
       54,  9, 41,  2, 58, 52, 31, 17,  4,  8,  7, 27, 29,  3, 42, 16, 37,
       56, 64, 55, 49, 51, 25, 65, 46, 28, 26, 61, 22, 53, 30, 35],
      dtype=int64), 'cur_cost': 105933.0}, {'tour': array([36, 59, 57, 12,  7, 64, 46, 45, 51, 15, 21, 52,  8, 19, 48, 18, 54,
        4, 11, 28,  1, 38, 22, 61, 50, 55, 26, 14, 60, 32,  9,  6, 41, 34,
        2, 49, 24, 58, 39, 35, 63,  3,  5, 16, 62, 23, 42, 10, 29, 17, 20,
       47, 53, 33, 44, 65, 30, 13, 37, 25, 43, 40, 27, 31, 56,  0],
      dtype=int64), 'cur_cost': 111767.0}, {'tour': array([53, 18,  8, 45, 38, 36, 22, 24, 32, 33, 57, 61, 48,  6, 19, 47, 12,
       20, 30,  1, 51, 26, 21, 63,  2, 13, 64, 52, 37, 27, 34, 62, 29, 65,
       54, 46, 41, 39,  9, 43, 10, 59, 40, 50, 25, 15, 35, 28,  3, 55,  4,
       58, 31,  7, 44, 49, 16,  0, 60, 23, 14,  5, 42, 11, 56, 17],
      dtype=int64), 'cur_cost': 98442.0}, {'tour': array([44, 10, 33, 30, 11, 47, 13, 48, 60, 21, 46, 43,  8, 28, 27, 20, 52,
        5,  1, 64, 38,  7, 49,  6, 56,  2, 59, 58, 54, 39, 24, 18, 26, 61,
       40, 25, 31, 36, 41, 12, 42, 62, 22, 14,  9, 23, 50,  3,  4, 45, 57,
       19, 37, 35, 63, 34, 32, 51, 55, 53,  0, 17, 29, 65, 16, 15],
      dtype=int64), 'cur_cost': 102262.0}, {'tour': array([21, 33, 29, 59, 52, 25, 24, 28, 48, 43,  9, 37, 46, 47, 49, 23, 41,
       36, 18, 44, 30, 60, 64, 56,  0,  4, 42,  8, 14, 50, 19, 51, 54, 17,
       40,  7, 62, 63, 57, 13, 20, 38, 32, 27, 53, 22, 35, 15, 16,  5, 39,
       10, 12, 61, 58,  2, 31, 55, 26,  6,  1, 45, 11, 65,  3, 34],
      dtype=int64), 'cur_cost': 94082.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:26,258 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 17:07:26,258 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-08-03 17:07:26,259 - experts.management.collaboration_manager - INFO - 个体 10 利用路径生成报告: {'new_tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}
2025-08-03 17:07:26,260 - experts.management.collaboration_manager - INFO - 个体 10 接受新路径 (成本: 103899.00)
2025-08-03 17:07:26,260 - experts.management.collaboration_manager - INFO - 为个体 11 生成探索路径
2025-08-03 17:07:26,260 - ExplorationExpert - INFO - 开始为个体 11 生成探索路径（算法实现）
2025-08-03 17:07:26,261 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,265 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-08-03 17:07:26,266 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,266 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14211.0, 路径长度: 66
2025-08-03 17:07:26,266 - experts.management.collaboration_manager - INFO - 个体 11 探索路径生成报告: {'new_tour': [0, 7, 24, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14211.0}
2025-08-03 17:07:26,267 - experts.management.collaboration_manager - INFO - 个体 11 接受新路径 (成本: 14211.00)
2025-08-03 17:07:26,267 - experts.management.collaboration_manager - INFO - 为个体 12 生成探索路径
2025-08-03 17:07:26,267 - ExplorationExpert - INFO - 开始为个体 12 生成探索路径（算法实现）
2025-08-03 17:07:26,267 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,271 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:07:26,271 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,272 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86428.0, 路径长度: 66
2025-08-03 17:07:26,272 - experts.management.collaboration_manager - INFO - 个体 12 探索路径生成报告: {'new_tour': [8, 7, 15, 13, 55, 53, 6, 58, 17, 40, 59, 63, 57, 9, 52, 21, 36, 1, 12, 5, 3, 23, 27, 50, 47, 14, 25, 39, 62, 26, 42, 51, 45, 38, 34, 65, 2, 16, 0, 4, 49, 30, 28, 11, 56, 10, 60, 43, 44, 19, 20, 64, 35, 54, 61, 32, 46, 24, 18, 33, 31, 37, 29, 41, 22, 48], 'cur_cost': 86428.0}
2025-08-03 17:07:26,272 - experts.management.collaboration_manager - INFO - 个体 12 接受新路径 (成本: 86428.00)
2025-08-03 17:07:26,272 - experts.management.collaboration_manager - INFO - 为个体 13 生成利用路径
2025-08-03 17:07:26,272 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:26,273 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:26,273 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 13 处的路径，新成本: 99800.0
2025-08-03 17:07:26,355 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:07:26,356 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9547.0, 9542, 9542, 9541, 9540, 9540, 9534, 9534, 9521]
2025-08-03 17:07:26,356 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:07:26,364 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:26,364 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}, {'tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}, {'tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}, {'tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}, {'tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}, {'tour': [0, 7, 24, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14211.0}, {'tour': [8, 7, 15, 13, 55, 53, 6, 58, 17, 40, 59, 63, 57, 9, 52, 21, 36, 1, 12, 5, 3, 23, 27, 50, 47, 14, 25, 39, 62, 26, 42, 51, 45, 38, 34, 65, 2, 16, 0, 4, 49, 30, 28, 11, 56, 10, 60, 43, 44, 19, 20, 64, 35, 54, 61, 32, 46, 24, 18, 33, 31, 37, 29, 41, 22, 48], 'cur_cost': 86428.0}, {'tour': array([63, 54, 59, 51, 39, 22,  1, 21, 10, 53,  3, 30, 37, 17, 33, 31, 52,
       11, 47, 26, 64, 43,  7, 57, 29, 42, 23, 58, 38,  8, 12,  4, 14, 45,
       50, 18, 55, 36, 27, 25, 28, 49, 24, 40,  2, 35, 15,  5, 41, 19,  9,
        0, 62, 34, 44, 61, 20, 48, 46, 32, 56, 13, 16, 60,  6, 65],
      dtype=int64), 'cur_cost': 99800.0}, {'tour': array([53, 18,  8, 45, 38, 36, 22, 24, 32, 33, 57, 61, 48,  6, 19, 47, 12,
       20, 30,  1, 51, 26, 21, 63,  2, 13, 64, 52, 37, 27, 34, 62, 29, 65,
       54, 46, 41, 39,  9, 43, 10, 59, 40, 50, 25, 15, 35, 28,  3, 55,  4,
       58, 31,  7, 44, 49, 16,  0, 60, 23, 14,  5, 42, 11, 56, 17],
      dtype=int64), 'cur_cost': 98442.0}, {'tour': array([44, 10, 33, 30, 11, 47, 13, 48, 60, 21, 46, 43,  8, 28, 27, 20, 52,
        5,  1, 64, 38,  7, 49,  6, 56,  2, 59, 58, 54, 39, 24, 18, 26, 61,
       40, 25, 31, 36, 41, 12, 42, 62, 22, 14,  9, 23, 50,  3,  4, 45, 57,
       19, 37, 35, 63, 34, 32, 51, 55, 53,  0, 17, 29, 65, 16, 15],
      dtype=int64), 'cur_cost': 102262.0}, {'tour': array([21, 33, 29, 59, 52, 25, 24, 28, 48, 43,  9, 37, 46, 47, 49, 23, 41,
       36, 18, 44, 30, 60, 64, 56,  0,  4, 42,  8, 14, 50, 19, 51, 54, 17,
       40,  7, 62, 63, 57, 13, 20, 38, 32, 27, 53, 22, 35, 15, 16,  5, 39,
       10, 12, 61, 58,  2, 31, 55, 26,  6,  1, 45, 11, 65,  3, 34],
      dtype=int64), 'cur_cost': 94082.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:26,371 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:07:26,372 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-08-03 17:07:26,373 - experts.management.collaboration_manager - INFO - 个体 13 利用路径生成报告: {'new_tour': array([63, 54, 59, 51, 39, 22,  1, 21, 10, 53,  3, 30, 37, 17, 33, 31, 52,
       11, 47, 26, 64, 43,  7, 57, 29, 42, 23, 58, 38,  8, 12,  4, 14, 45,
       50, 18, 55, 36, 27, 25, 28, 49, 24, 40,  2, 35, 15,  5, 41, 19,  9,
        0, 62, 34, 44, 61, 20, 48, 46, 32, 56, 13, 16, 60,  6, 65],
      dtype=int64), 'cur_cost': 99800.0}
2025-08-03 17:07:26,374 - experts.management.collaboration_manager - INFO - 个体 13 接受新路径 (成本: 99800.00)
2025-08-03 17:07:26,374 - experts.management.collaboration_manager - INFO - 为个体 14 生成探索路径
2025-08-03 17:07:26,374 - ExplorationExpert - INFO - 开始为个体 14 生成探索路径（算法实现）
2025-08-03 17:07:26,375 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,392 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:07:26,394 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,395 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56040.0, 路径长度: 66
2025-08-03 17:07:26,395 - experts.management.collaboration_manager - INFO - 个体 14 探索路径生成报告: {'new_tour': [23, 24, 26, 19, 4, 3, 65, 60, 64, 20, 25, 21, 32, 17, 16, 9, 55, 0, 6, 11, 7, 18, 28, 35, 33, 34, 5, 54, 39, 51, 22, 36, 40, 15, 47, 41, 45, 46, 38, 50, 49, 37, 2, 58, 57, 10, 53, 56, 62, 61, 1, 8, 52, 12, 27, 31, 29, 13, 14, 43, 44, 42, 30, 48, 59, 63], 'cur_cost': 56040.0}
2025-08-03 17:07:26,396 - experts.management.collaboration_manager - INFO - 个体 14 接受新路径 (成本: 56040.00)
2025-08-03 17:07:26,396 - experts.management.collaboration_manager - INFO - 为个体 15 生成探索路径
2025-08-03 17:07:26,397 - ExplorationExpert - INFO - 开始为个体 15 生成探索路径（算法实现）
2025-08-03 17:07:26,397 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,401 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:07:26,401 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,401 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103448.0, 路径长度: 66
2025-08-03 17:07:26,402 - experts.management.collaboration_manager - INFO - 个体 15 探索路径生成报告: {'new_tour': [52, 3, 31, 46, 10, 4, 55, 53, 19, 17, 40, 59, 56, 60, 41, 20, 1, 54, 18, 11, 37, 27, 25, 30, 26, 42, 63, 21, 43, 36, 65, 58, 6, 49, 16, 34, 28, 50, 62, 33, 38, 57, 48, 64, 32, 5, 23, 45, 22, 12, 8, 24, 13, 51, 9, 29, 0, 44, 35, 7, 61, 47, 15, 39, 14, 2], 'cur_cost': 103448.0}
2025-08-03 17:07:26,402 - experts.management.collaboration_manager - INFO - 个体 15 接受新路径 (成本: 103448.00)
2025-08-03 17:07:26,402 - experts.management.collaboration_manager - INFO - 为个体 16 生成利用路径
2025-08-03 17:07:26,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:26,402 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:26,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 16 处的路径，新成本: 98910.0
2025-08-03 17:07:26,487 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:07:26,487 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9547.0, 9542, 9542, 9541, 9540, 9540, 9534, 9534, 9521]
2025-08-03 17:07:26,487 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:07:26,494 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:26,494 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}, {'tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}, {'tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}, {'tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}, {'tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}, {'tour': [0, 7, 24, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14211.0}, {'tour': [8, 7, 15, 13, 55, 53, 6, 58, 17, 40, 59, 63, 57, 9, 52, 21, 36, 1, 12, 5, 3, 23, 27, 50, 47, 14, 25, 39, 62, 26, 42, 51, 45, 38, 34, 65, 2, 16, 0, 4, 49, 30, 28, 11, 56, 10, 60, 43, 44, 19, 20, 64, 35, 54, 61, 32, 46, 24, 18, 33, 31, 37, 29, 41, 22, 48], 'cur_cost': 86428.0}, {'tour': array([63, 54, 59, 51, 39, 22,  1, 21, 10, 53,  3, 30, 37, 17, 33, 31, 52,
       11, 47, 26, 64, 43,  7, 57, 29, 42, 23, 58, 38,  8, 12,  4, 14, 45,
       50, 18, 55, 36, 27, 25, 28, 49, 24, 40,  2, 35, 15,  5, 41, 19,  9,
        0, 62, 34, 44, 61, 20, 48, 46, 32, 56, 13, 16, 60,  6, 65],
      dtype=int64), 'cur_cost': 99800.0}, {'tour': [23, 24, 26, 19, 4, 3, 65, 60, 64, 20, 25, 21, 32, 17, 16, 9, 55, 0, 6, 11, 7, 18, 28, 35, 33, 34, 5, 54, 39, 51, 22, 36, 40, 15, 47, 41, 45, 46, 38, 50, 49, 37, 2, 58, 57, 10, 53, 56, 62, 61, 1, 8, 52, 12, 27, 31, 29, 13, 14, 43, 44, 42, 30, 48, 59, 63], 'cur_cost': 56040.0}, {'tour': [52, 3, 31, 46, 10, 4, 55, 53, 19, 17, 40, 59, 56, 60, 41, 20, 1, 54, 18, 11, 37, 27, 25, 30, 26, 42, 63, 21, 43, 36, 65, 58, 6, 49, 16, 34, 28, 50, 62, 33, 38, 57, 48, 64, 32, 5, 23, 45, 22, 12, 8, 24, 13, 51, 9, 29, 0, 44, 35, 7, 61, 47, 15, 39, 14, 2], 'cur_cost': 103448.0}, {'tour': array([49,  8, 26, 39, 13, 12, 62, 33, 27, 31, 46, 28, 59, 60, 30,  6, 54,
       29, 65,  2, 21, 41, 22, 45, 23, 52, 58, 55, 47, 53,  3,  1, 25, 16,
        7, 56, 44, 42, 20,  0, 17, 10, 37, 38, 48, 51,  4, 14, 19, 57, 50,
       11, 24, 18, 34,  9, 63, 64,  5, 15, 35, 36, 43, 40, 32, 61],
      dtype=int64), 'cur_cost': 98910.0}, {'tour': array([54,  3, 48, 13,  8, 16, 53, 12, 24, 10, 63,  0, 65, 35, 50, 34,  9,
       21, 57, 30, 19, 25, 43, 28, 46, 64, 38, 17, 52, 55, 58, 51, 59,  4,
       11, 41, 32, 15, 42,  1, 49, 26, 61, 14, 36,  5, 40, 22, 20, 56, 37,
       60, 39, 31, 23,  2, 27, 47,  7, 45, 62, 18,  6, 44, 33, 29],
      dtype=int64), 'cur_cost': 122014.0}, {'tour': array([26, 12, 10, 65, 16, 30, 56, 42, 34, 23, 44, 53, 45, 18, 55, 41, 17,
       50, 31, 58, 15,  4, 51, 35, 36, 52, 11, 25, 47,  6, 48, 62, 43, 49,
       46,  7, 61, 38, 57, 21,  3, 13, 28, 39,  0, 27,  9, 29, 14, 19,  2,
       24, 37, 40, 32, 63,  8, 20, 60, 59, 22, 54, 64,  5,  1, 33],
      dtype=int64), 'cur_cost': 111947.0}, {'tour': array([ 1, 26, 58, 52, 21, 48, 20, 59, 19, 14, 23, 46, 18, 30, 29, 43, 25,
       24, 10, 39, 31,  3,  0, 13, 62, 16, 49,  6, 65, 27, 37,  7,  2, 45,
       32, 47, 22, 60, 36, 63, 64, 33,  5, 35, 44, 38, 17, 51, 56, 42, 61,
        4,  9, 34, 53, 41, 12, 50,  8, 55, 15, 57, 28, 11, 54, 40],
      dtype=int64), 'cur_cost': 112709.0}]
2025-08-03 17:07:26,502 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-08-03 17:07:26,502 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-08-03 17:07:26,502 - experts.management.collaboration_manager - INFO - 个体 16 利用路径生成报告: {'new_tour': array([49,  8, 26, 39, 13, 12, 62, 33, 27, 31, 46, 28, 59, 60, 30,  6, 54,
       29, 65,  2, 21, 41, 22, 45, 23, 52, 58, 55, 47, 53,  3,  1, 25, 16,
        7, 56, 44, 42, 20,  0, 17, 10, 37, 38, 48, 51,  4, 14, 19, 57, 50,
       11, 24, 18, 34,  9, 63, 64,  5, 15, 35, 36, 43, 40, 32, 61],
      dtype=int64), 'cur_cost': 98910.0}
2025-08-03 17:07:26,503 - experts.management.collaboration_manager - INFO - 个体 16 接受新路径 (成本: 98910.00)
2025-08-03 17:07:26,503 - experts.management.collaboration_manager - INFO - 为个体 17 生成探索路径
2025-08-03 17:07:26,504 - ExplorationExpert - INFO - 开始为个体 17 生成探索路径（算法实现）
2025-08-03 17:07:26,504 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,522 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-08-03 17:07:26,523 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,523 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64399.0, 路径长度: 66
2025-08-03 17:07:26,523 - experts.management.collaboration_manager - INFO - 个体 17 探索路径生成报告: {'new_tour': [14, 15, 28, 32, 24, 7, 60, 58, 56, 2, 4, 53, 23, 17, 29, 9, 18, 20, 13, 16, 31, 10, 22, 21, 26, 1, 52, 5, 6, 64, 62, 49, 41, 47, 34, 3, 54, 59, 12, 30, 11, 36, 35, 46, 39, 51, 19, 25, 42, 40, 44, 37, 8, 57, 63, 61, 43, 45, 38, 48, 33, 27, 50, 0, 55, 65], 'cur_cost': 64399.0}
2025-08-03 17:07:26,525 - experts.management.collaboration_manager - INFO - 个体 17 接受新路径 (成本: 64399.00)
2025-08-03 17:07:26,525 - experts.management.collaboration_manager - INFO - 为个体 18 生成探索路径
2025-08-03 17:07:26,526 - ExplorationExpert - INFO - 开始为个体 18 生成探索路径（算法实现）
2025-08-03 17:07:26,526 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-03 17:07:26,530 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-08-03 17:07:26,531 - ExplorationExpert - INFO - 计算路径成本
2025-08-03 17:07:26,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104856.0, 路径长度: 66
2025-08-03 17:07:26,533 - experts.management.collaboration_manager - INFO - 个体 18 探索路径生成报告: {'new_tour': [29, 3, 64, 23, 13, 53, 55, 58, 15, 18, 20, 56, 63, 24, 57, 27, 60, 40, 14, 22, 16, 17, 50, 5, 6, 25, 2, 11, 62, 26, 33, 61, 34, 21, 36, 43, 39, 47, 8, 38, 46, 31, 48, 42, 1, 65, 12, 59, 7, 19, 44, 10, 4, 30, 52, 32, 45, 35, 51, 9, 41, 37, 49, 28, 0, 54], 'cur_cost': 104856.0}
2025-08-03 17:07:26,533 - experts.management.collaboration_manager - INFO - 个体 18 接受新路径 (成本: 104856.00)
2025-08-03 17:07:26,534 - experts.management.collaboration_manager - INFO - 为个体 19 生成利用路径
2025-08-03 17:07:26,534 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-03 17:07:26,535 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-03 17:07:26,535 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 19 处的路径，新成本: 104086.0
2025-08-03 17:07:26,612 - ExploitationExpert - INFO - res_population_num: 11
2025-08-03 17:07:26,612 - ExploitationExpert - INFO - res_population_costs: [9821.0, 9577.0, 9547.0, 9542, 9542, 9541, 9540, 9540, 9534, 9534, 9521]
2025-08-03 17:07:26,612 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 12, 17, 15, 14, 22, 23, 16, 19, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 62, 59, 60, 58, 56, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40,
       21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-08-03 17:07:26,621 - ExploitationExpert - INFO - populations_num: 20
2025-08-03 17:07:26,621 - ExploitationExpert - INFO - populations: [{'tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}, {'tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}, {'tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}, {'tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}, {'tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}, {'tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}, {'tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}, {'tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}, {'tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}, {'tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}, {'tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}, {'tour': [0, 7, 24, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14211.0}, {'tour': [8, 7, 15, 13, 55, 53, 6, 58, 17, 40, 59, 63, 57, 9, 52, 21, 36, 1, 12, 5, 3, 23, 27, 50, 47, 14, 25, 39, 62, 26, 42, 51, 45, 38, 34, 65, 2, 16, 0, 4, 49, 30, 28, 11, 56, 10, 60, 43, 44, 19, 20, 64, 35, 54, 61, 32, 46, 24, 18, 33, 31, 37, 29, 41, 22, 48], 'cur_cost': 86428.0}, {'tour': array([63, 54, 59, 51, 39, 22,  1, 21, 10, 53,  3, 30, 37, 17, 33, 31, 52,
       11, 47, 26, 64, 43,  7, 57, 29, 42, 23, 58, 38,  8, 12,  4, 14, 45,
       50, 18, 55, 36, 27, 25, 28, 49, 24, 40,  2, 35, 15,  5, 41, 19,  9,
        0, 62, 34, 44, 61, 20, 48, 46, 32, 56, 13, 16, 60,  6, 65],
      dtype=int64), 'cur_cost': 99800.0}, {'tour': [23, 24, 26, 19, 4, 3, 65, 60, 64, 20, 25, 21, 32, 17, 16, 9, 55, 0, 6, 11, 7, 18, 28, 35, 33, 34, 5, 54, 39, 51, 22, 36, 40, 15, 47, 41, 45, 46, 38, 50, 49, 37, 2, 58, 57, 10, 53, 56, 62, 61, 1, 8, 52, 12, 27, 31, 29, 13, 14, 43, 44, 42, 30, 48, 59, 63], 'cur_cost': 56040.0}, {'tour': [52, 3, 31, 46, 10, 4, 55, 53, 19, 17, 40, 59, 56, 60, 41, 20, 1, 54, 18, 11, 37, 27, 25, 30, 26, 42, 63, 21, 43, 36, 65, 58, 6, 49, 16, 34, 28, 50, 62, 33, 38, 57, 48, 64, 32, 5, 23, 45, 22, 12, 8, 24, 13, 51, 9, 29, 0, 44, 35, 7, 61, 47, 15, 39, 14, 2], 'cur_cost': 103448.0}, {'tour': array([49,  8, 26, 39, 13, 12, 62, 33, 27, 31, 46, 28, 59, 60, 30,  6, 54,
       29, 65,  2, 21, 41, 22, 45, 23, 52, 58, 55, 47, 53,  3,  1, 25, 16,
        7, 56, 44, 42, 20,  0, 17, 10, 37, 38, 48, 51,  4, 14, 19, 57, 50,
       11, 24, 18, 34,  9, 63, 64,  5, 15, 35, 36, 43, 40, 32, 61],
      dtype=int64), 'cur_cost': 98910.0}, {'tour': [14, 15, 28, 32, 24, 7, 60, 58, 56, 2, 4, 53, 23, 17, 29, 9, 18, 20, 13, 16, 31, 10, 22, 21, 26, 1, 52, 5, 6, 64, 62, 49, 41, 47, 34, 3, 54, 59, 12, 30, 11, 36, 35, 46, 39, 51, 19, 25, 42, 40, 44, 37, 8, 57, 63, 61, 43, 45, 38, 48, 33, 27, 50, 0, 55, 65], 'cur_cost': 64399.0}, {'tour': [29, 3, 64, 23, 13, 53, 55, 58, 15, 18, 20, 56, 63, 24, 57, 27, 60, 40, 14, 22, 16, 17, 50, 5, 6, 25, 2, 11, 62, 26, 33, 61, 34, 21, 36, 43, 39, 47, 8, 38, 46, 31, 48, 42, 1, 65, 12, 59, 7, 19, 44, 10, 4, 30, 52, 32, 45, 35, 51, 9, 41, 37, 49, 28, 0, 54], 'cur_cost': 104856.0}, {'tour': array([51, 56,  4, 41, 42, 20, 40, 37,  0, 61, 28, 19, 31, 49,  9, 35, 18,
       34, 52, 45, 22,  6, 59,  2, 24,  8, 43, 62, 57,  1, 60, 27, 32, 47,
       46, 10,  7, 30, 21, 23, 12, 44, 25, 39, 16, 33, 55, 48, 11, 36, 29,
       38,  3, 14, 64, 53, 50, 17, 63, 54,  5, 13, 65, 58, 26, 15],
      dtype=int64), 'cur_cost': 104086.0}]
2025-08-03 17:07:26,628 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-08-03 17:07:26,629 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-08-03 17:07:26,630 - experts.management.collaboration_manager - INFO - 个体 19 利用路径生成报告: {'new_tour': array([51, 56,  4, 41, 42, 20, 40, 37,  0, 61, 28, 19, 31, 49,  9, 35, 18,
       34, 52, 45, 22,  6, 59,  2, 24,  8, 43, 62, 57,  1, 60, 27, 32, 47,
       46, 10,  7, 30, 21, 23, 12, 44, 25, 39, 16, 33, 55, 48, 11, 36, 29,
       38,  3, 14, 64, 53, 50, 17, 63, 54,  5, 13, 65, 58, 26, 15],
      dtype=int64), 'cur_cost': 104086.0}
2025-08-03 17:07:26,631 - experts.management.collaboration_manager - INFO - 个体 19 接受新路径 (成本: 104086.00)
2025-08-03 17:07:26,631 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 16, 'rejected': 4, 'elite_protected': 4}
2025-08-03 17:07:26,632 - experts.management.collaboration_manager - INFO - 接受率: 16/20 (80.0%)
2025-08-03 17:07:26,633 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [29, 8, 49, 54, 31, 46, 24, 2, 28, 48, 53, 55, 58, 10, 40, 35, 38, 0, 12, 16, 15, 14, 45, 9, 41, 43, 20, 32, 23, 65, 18, 64, 37, 33, 59, 27, 50, 5, 30, 6, 1, 47, 52, 25, 57, 62, 26, 42, 4, 19, 63, 61, 51, 21, 44, 17, 36, 7, 56, 22, 3, 60, 11, 39, 34, 13], 'cur_cost': 114840.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 41, 65, 10, 64, 19, 54, 63, 12, 46,  5, 59,  2, 21,  6, 62,  3,
       57,  9, 53, 33, 50, 36, 22, 16, 51, 43, 52, 45,  8, 17, 30, 61, 58,
        4,  0, 27, 60, 13, 29, 39, 25, 44, 56, 47, 38, 42, 55, 32, 20, 49,
       28, 23, 15,  7, 48, 37, 40, 24, 18, 11, 31, 35, 26,  1, 34],
      dtype=int64), 'cur_cost': 102492.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12330.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [2, 52, 23, 9, 8, 64, 56, 10, 11, 55, 6, 15, 13, 26, 25, 18, 20, 7, 63, 16, 24, 4, 59, 60, 40, 21, 12, 36, 1, 54, 5, 65, 3, 37, 17, 19, 32, 27, 47, 49, 14, 30, 35, 42, 39, 46, 45, 51, 48, 41, 50, 33, 34, 22, 43, 38, 0, 61, 62, 58, 53, 44, 31, 29, 28, 57], 'cur_cost': 61577.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 26, 62,  0, 54,  7, 64, 21, 34, 63, 61, 22, 53,  5,  1,  3, 17,
       25, 50, 29,  4, 28, 24, 48, 44, 37, 51, 13, 38, 27, 45, 47, 52, 12,
       11, 49,  8, 55,  9, 33, 30, 40, 14, 35, 19, 16, 39,  6, 20, 31, 18,
       42, 59, 43, 58, 60, 32, 41, 56, 46, 57, 10, 36, 23, 65, 15],
      dtype=int64), 'cur_cost': 106048.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 3, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 7, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12439.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 7, 3, 9, 11, 1, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10326.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([22,  9, 58, 60,  7, 52, 42, 11, 26, 24, 27,  1, 54, 57,  4, 47, 30,
       35, 23, 37, 20, 55, 34, 45, 18, 12, 50, 49, 19, 65, 63, 40,  6, 48,
       61, 31, 13, 39, 15, 14,  2, 44, 16, 41, 53, 51, 33, 36,  0,  3, 21,
       28,  8, 10, 62, 46, 25, 38, 43, 17, 56, 32, 64,  5, 29, 59],
      dtype=int64), 'cur_cost': 101457.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 1, 20, 21, 13, 23, 22, 12, 17, 15, 14, 18, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14841.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 11, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12225.0}}, {'individual': 10, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 36, 29, 14, 38, 22,  8,  9, 54, 10, 41, 44, 43, 61,  2,  5, 62,
       26, 17, 56, 53, 64, 30, 51, 48, 24, 35, 65, 20,  4, 46, 58, 23, 39,
        1,  3, 45, 59, 34, 16, 33, 31, 13, 18, 28, 63, 49, 52, 21, 55, 11,
        7, 57, 32, 60, 12, 50, 19, 27, 40,  0, 47, 25, 42,  6, 37],
      dtype=int64), 'cur_cost': 103899.0}}, {'individual': 11, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 24, 23, 16, 18, 12, 22, 15, 14, 20, 21, 13, 19, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14211.0}}, {'individual': 12, 'strategy': 'explore', 'path_data': {'new_tour': [8, 7, 15, 13, 55, 53, 6, 58, 17, 40, 59, 63, 57, 9, 52, 21, 36, 1, 12, 5, 3, 23, 27, 50, 47, 14, 25, 39, 62, 26, 42, 51, 45, 38, 34, 65, 2, 16, 0, 4, 49, 30, 28, 11, 56, 10, 60, 43, 44, 19, 20, 64, 35, 54, 61, 32, 46, 24, 18, 33, 31, 37, 29, 41, 22, 48], 'cur_cost': 86428.0}}, {'individual': 13, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 54, 59, 51, 39, 22,  1, 21, 10, 53,  3, 30, 37, 17, 33, 31, 52,
       11, 47, 26, 64, 43,  7, 57, 29, 42, 23, 58, 38,  8, 12,  4, 14, 45,
       50, 18, 55, 36, 27, 25, 28, 49, 24, 40,  2, 35, 15,  5, 41, 19,  9,
        0, 62, 34, 44, 61, 20, 48, 46, 32, 56, 13, 16, 60,  6, 65],
      dtype=int64), 'cur_cost': 99800.0}}, {'individual': 14, 'strategy': 'explore', 'path_data': {'new_tour': [23, 24, 26, 19, 4, 3, 65, 60, 64, 20, 25, 21, 32, 17, 16, 9, 55, 0, 6, 11, 7, 18, 28, 35, 33, 34, 5, 54, 39, 51, 22, 36, 40, 15, 47, 41, 45, 46, 38, 50, 49, 37, 2, 58, 57, 10, 53, 56, 62, 61, 1, 8, 52, 12, 27, 31, 29, 13, 14, 43, 44, 42, 30, 48, 59, 63], 'cur_cost': 56040.0}}, {'individual': 15, 'strategy': 'explore', 'path_data': {'new_tour': [52, 3, 31, 46, 10, 4, 55, 53, 19, 17, 40, 59, 56, 60, 41, 20, 1, 54, 18, 11, 37, 27, 25, 30, 26, 42, 63, 21, 43, 36, 65, 58, 6, 49, 16, 34, 28, 50, 62, 33, 38, 57, 48, 64, 32, 5, 23, 45, 22, 12, 8, 24, 13, 51, 9, 29, 0, 44, 35, 7, 61, 47, 15, 39, 14, 2], 'cur_cost': 103448.0}}, {'individual': 16, 'strategy': 'exploit', 'path_data': {'new_tour': array([49,  8, 26, 39, 13, 12, 62, 33, 27, 31, 46, 28, 59, 60, 30,  6, 54,
       29, 65,  2, 21, 41, 22, 45, 23, 52, 58, 55, 47, 53,  3,  1, 25, 16,
        7, 56, 44, 42, 20,  0, 17, 10, 37, 38, 48, 51,  4, 14, 19, 57, 50,
       11, 24, 18, 34,  9, 63, 64,  5, 15, 35, 36, 43, 40, 32, 61],
      dtype=int64), 'cur_cost': 98910.0}}, {'individual': 17, 'strategy': 'explore', 'path_data': {'new_tour': [14, 15, 28, 32, 24, 7, 60, 58, 56, 2, 4, 53, 23, 17, 29, 9, 18, 20, 13, 16, 31, 10, 22, 21, 26, 1, 52, 5, 6, 64, 62, 49, 41, 47, 34, 3, 54, 59, 12, 30, 11, 36, 35, 46, 39, 51, 19, 25, 42, 40, 44, 37, 8, 57, 63, 61, 43, 45, 38, 48, 33, 27, 50, 0, 55, 65], 'cur_cost': 64399.0}}, {'individual': 18, 'strategy': 'explore', 'path_data': {'new_tour': [29, 3, 64, 23, 13, 53, 55, 58, 15, 18, 20, 56, 63, 24, 57, 27, 60, 40, 14, 22, 16, 17, 50, 5, 6, 25, 2, 11, 62, 26, 33, 61, 34, 21, 36, 43, 39, 47, 8, 38, 46, 31, 48, 42, 1, 65, 12, 59, 7, 19, 44, 10, 4, 30, 52, 32, 45, 35, 51, 9, 41, 37, 49, 28, 0, 54], 'cur_cost': 104856.0}}, {'individual': 19, 'strategy': 'exploit', 'path_data': {'new_tour': array([51, 56,  4, 41, 42, 20, 40, 37,  0, 61, 28, 19, 31, 49,  9, 35, 18,
       34, 52, 45, 22,  6, 59,  2, 24,  8, 43, 62, 57,  1, 60, 27, 32, 47,
       46, 10,  7, 30, 21, 23, 12, 44, 25, 39, 16, 33, 55, 48, 11, 36, 29,
       38,  3, 14, 64, 53, 50, 17, 63, 54,  5, 13, 65, 58, 26, 15],
      dtype=int64), 'cur_cost': 104086.0}}]
2025-08-03 17:07:26,636 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-03 17:07:26,636 - StatsExpert - INFO - 开始统计分析
2025-08-03 17:07:26,652 - StatsExpert - INFO - 统计分析完成: 种群大小=20, 最优成本=10326.000, 多样性=0.953
2025-08-03 17:07:26,652 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-03 17:07:26,653 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-03 17:07:26,653 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-03 17:07:26,655 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 1, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.04656484100472592, 'best_improvement': -0.0392512077294686}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.023535180191223373}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 11, 'new_count': 11, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8333333333333334, 'new_diversity': 0.8333333333333334, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-03 17:07:26,656 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-03 17:07:26,697 - __main__ - INFO - 最终结果已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_solution.json
2025-08-03 17:07:26,697 - __main__ - INFO - 解集文件已保存到: C:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite13_66_20250803_170726.solution
2025-08-03 17:07:26,698 - __main__ - INFO - 实例 composite13_66 处理完成
