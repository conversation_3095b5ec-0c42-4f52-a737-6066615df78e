"""
Advanced prompt engineering for LLM-based strategy selection.

This module provides sophisticated prompt construction and response parsing
capabilities for optimal LLM interaction in strategy selection scenarios.
"""

import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

from ..core.individual_state import StagnationLevel
from ..core.data_structures import StrategyType


@dataclass
class PromptTemplate:
    """Template for LLM prompts."""
    system_prompt: str
    user_prompt_template: str
    response_format: Dict[str, Any]
    examples: List[Dict[str, str]]


class AdvancedPromptEngineer:
    """
    Advanced prompt engineering for strategy selection.
    
    This class provides sophisticated prompt construction techniques
    to maximize LLM performance in strategy selection tasks.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the prompt engineer."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Prompt configuration
        self.use_few_shot = self.config.get('use_few_shot_examples', True)
        self.use_chain_of_thought = self.config.get('use_chain_of_thought', True)
        self.include_examples = self.config.get('include_examples', True)
        
        # Load prompt templates
        self.templates = self._load_prompt_templates()
    
    def construct_strategy_selection_prompt(self, 
                                          landscape_context: Dict[str, Any],
                                          individual_context: Dict[str, Any],
                                          population_context: Dict[str, Any],
                                          historical_context: Dict[str, Any]) -> str:
        """
        Construct an optimized prompt for strategy selection.
        
        Args:
            landscape_context: Fitness landscape characteristics
            individual_context: Individual-specific information
            population_context: Population-level context
            historical_context: Historical performance data
            
        Returns:
            Optimized prompt string
        """
        # Select appropriate template based on context
        template = self._select_prompt_template(individual_context, landscape_context)
        
        # Construct context sections
        landscape_section = self._format_landscape_context(landscape_context)
        individual_section = self._format_individual_context(individual_context)
        population_section = self._format_population_context(population_context)
        historical_section = self._format_historical_context(historical_context)
        
        # Add strategy descriptions
        strategy_section = self._format_strategy_descriptions()
        
        # Add examples if configured
        examples_section = ""
        if self.include_examples:
            examples_section = self._format_examples()
        
        # Add chain of thought instructions
        reasoning_section = ""
        if self.use_chain_of_thought:
            reasoning_section = self._format_reasoning_instructions()
        
        # Construct final prompt
        prompt = f"""
{template.system_prompt}

## Current Situation Analysis

### Fitness Landscape Characteristics
{landscape_section}

### Individual Status
{individual_section}

### Population Context
{population_section}

### Historical Performance
{historical_section}

## Available Strategies
{strategy_section}

{examples_section}

{reasoning_section}

## Response Format
Please respond with a JSON object following this exact format:
{json.dumps(template.response_format, indent=2)}

## Instructions
Analyze the provided context carefully and select the most appropriate strategy.
Consider the individual's current state, landscape characteristics, and historical performance.
Provide detailed reasoning for your choice and set appropriate parameters.
"""
        
        return prompt.strip()
    
    def _load_prompt_templates(self) -> Dict[str, PromptTemplate]:
        """Load prompt templates for different scenarios."""
        templates = {}
        
        # Standard template
        templates['standard'] = PromptTemplate(
            system_prompt="""You are an expert in evolutionary optimization and fitness landscape analysis.
Your task is to select the most appropriate search strategy for individuals in a TSP optimization algorithm.
You have deep knowledge of exploration vs exploitation trade-offs, landscape characteristics, and adaptive search strategies.""",
            
            user_prompt_template="",  # Will be constructed dynamically
            
            response_format={
                "strategy_type": "STRATEGY_NAME",
                "confidence": 0.8,
                "reasoning": "Detailed explanation of strategy choice",
                "exploration_params": {
                    "exploration_intensity": 0.5,
                    "perturbation_strength": 0.5,
                    "diversification_bias": 0.5,
                    "search_radius": 0.3,
                    "mutation_probability": 0.2,
                    "region_focus_weight": 0.4,
                    "novelty_seeking_factor": 0.6
                },
                "exploitation_params": {
                    "local_search_intensity": 0.5,
                    "convergence_threshold": 1e-6,
                    "elite_guidance_weight": 0.7,
                    "step_size": 0.1,
                    "search_depth": 10,
                    "patience": 5,
                    "gradient_following_strength": 0.6
                },
                "priority": 0.5,
                "time_budget": 1.0
            },
            
            examples=[]
        )
        
        # Stagnation-focused template
        templates['stagnation'] = PromptTemplate(
            system_prompt="""You are an expert in helping optimization algorithms escape from local optima and stagnation.
Your specialty is analyzing stagnated individuals and recommending escape strategies.
Focus on exploration strategies and diversification techniques.""",
            
            user_prompt_template="",
            response_format=templates['standard'].response_format,
            examples=[]
        )
        
        # Exploitation-focused template
        templates['exploitation'] = PromptTemplate(
            system_prompt="""You are an expert in local search optimization and exploitation strategies.
Your specialty is identifying promising regions and recommending intensive local search strategies.
Focus on exploitation strategies and convergence acceleration.""",
            
            user_prompt_template="",
            response_format=templates['standard'].response_format,
            examples=[]
        )
        
        return templates
    
    def _select_prompt_template(self, 
                              individual_context: Dict[str, Any],
                              landscape_context: Dict[str, Any]) -> PromptTemplate:
        """Select the most appropriate prompt template."""
        # Check for stagnation
        stagnation_level = individual_context.get('stagnation_level', 'NONE')
        stagnation_duration = individual_context.get('stagnation_duration', 0)
        
        if stagnation_level in ['HIGH', 'CRITICAL'] or stagnation_duration > 15:
            return self.templates['stagnation']
        
        # Check for exploitation opportunity
        improvement_potential = individual_context.get('improvement_potential', 0.0)
        local_ruggedness = landscape_context.get('local_ruggedness', 0.5)
        
        if improvement_potential > 0.7 and local_ruggedness < 0.3:
            return self.templates['exploitation']
        
        # Default to standard template
        return self.templates['standard']
    
    def _format_landscape_context(self, context: Dict[str, Any]) -> str:
        """Format landscape context for prompt."""
        return f"""
- Global Ruggedness: {context.get('global_ruggedness', 0.0):.3f} (0=smooth, 1=very rugged)
- Modality: {context.get('modality', 0.0):.3f} (0=unimodal, 1=highly multimodal)
- Deceptiveness: {context.get('deceptiveness', 0.0):.3f} (0=honest, 1=very deceptive)
- Gradient Strength: {context.get('gradient_strength', 0.0):.3f} (0=flat, 1=strong gradients)
- Local Ruggedness: {context.get('local_ruggedness', 0.0):.3f} (around this individual)
- Diversity Level: {context.get('diversity_level', 0.0):.3f} (0=converged, 1=diverse)
- Convergence Level: {context.get('convergence_level', 0.0):.3f} (0=diverse, 1=converged)
- Exploration Coverage: {context.get('exploration_coverage', 0.0):.3f} (0=unexplored, 1=well-explored)
"""
    
    def _format_individual_context(self, context: Dict[str, Any]) -> str:
        """Format individual context for prompt."""
        return f"""
- Fitness Value: {context.get('fitness_value', 0.0):.4f}
- Population Rank: {context.get('fitness_rank', 0)} (percentile: {context.get('fitness_percentile', 0.0):.1%})
- Stagnation Duration: {context.get('stagnation_duration', 0)} iterations
- Stagnation Level: {context.get('stagnation_level', 'NONE')}
- Performance Trend: {context.get('performance_trend', 0.0):.3f} (negative=improving)
- Recent Improvements: {context.get('recent_improvements', [])}
- Strategy Success Rate: {context.get('strategy_success_rate', 0.0):.1%}
- Last Strategy: {context.get('last_strategy_type', 'None')} (success: {context.get('last_strategy_success', False)})
- Improvement Potential: {context.get('improvement_potential', 0.0):.3f} (0=low, 1=high)
"""
    
    def _format_population_context(self, context: Dict[str, Any]) -> str:
        """Format population context for prompt."""
        return f"""
- Population Size: {context.get('population_size', 'N/A')}
- Best Fitness: {context.get('best_fitness', 'N/A')}
- Average Fitness: {context.get('average_fitness', 'N/A')}
- Current Iteration: {context.get('iteration', 'N/A')}
- Stagnated Individuals: {context.get('stagnated_individuals', 'N/A')}
"""
    
    def _format_historical_context(self, context: Dict[str, Any]) -> str:
        """Format historical context for prompt."""
        if not context.get('has_history', False):
            return "- No historical data available for this individual"
        
        return f"""
- Historical Entries: {context.get('recent_entries', 0)}
- Average Success Rate: {context.get('average_success_rate', 0.0):.1%}
- Performance Trend: {context.get('performance_trend', 'unknown')}
- Preferred Strategies: {context.get('preferred_strategies', [])}
"""
    
    def _format_strategy_descriptions(self) -> str:
        """Format detailed strategy descriptions."""
        return """
1. **STRONG_EXPLORATION**: High-intensity exploration for escaping deep local optima
   - Use when: High stagnation, rugged landscape, need for diversification
   - Characteristics: Large perturbations, high mutation rates, wide search radius

2. **BALANCED_EXPLORATION**: Moderate exploration with controlled exploitation
   - Use when: Moderate stagnation, uncertain landscape characteristics
   - Characteristics: Balanced parameters, adaptive search radius

3. **INTELLIGENT_EXPLORATION**: Adaptive exploration based on landscape analysis
   - Use when: Complex landscape, need for smart exploration
   - Characteristics: Landscape-guided parameters, adaptive intensity

4. **CAUTIOUS_EXPLOITATION**: Conservative local search with small steps
   - Use when: Near good solutions, smooth local landscape
   - Characteristics: Small step sizes, high patience, gradient following

5. **MODERATE_EXPLOITATION**: Standard local optimization
   - Use when: Good improvement potential, moderate local ruggedness
   - Characteristics: Balanced local search, moderate depth

6. **AGGRESSIVE_EXPLOITATION**: Intensive local search for rapid improvement
   - Use when: High improvement potential, smooth landscape
   - Characteristics: Large step sizes, deep search, low patience

7. **INTENSIVE_EXPLOITATION**: Maximum exploitation of current region
   - Use when: Very high improvement potential, very smooth landscape
   - Characteristics: Maximum intensity, deepest search

8. **HYBRID**: Combination of exploration and exploitation
   - Use when: Uncertain about best approach, need for flexibility
   - Characteristics: Adaptive switching between exploration and exploitation
"""
    
    def _format_examples(self) -> str:
        """Format few-shot examples for prompt."""
        if not self.use_few_shot:
            return ""
        
        return """
## Example Strategy Selections

### Example 1: Stagnated Individual in Rugged Landscape
**Context**: Individual stagnated for 15 iterations, high local ruggedness (0.8), low improvement potential (0.2)
**Selection**: STRONG_EXPLORATION with high perturbation strength (0.9) and diversification bias (0.8)
**Reasoning**: High stagnation and rugged landscape require aggressive exploration to escape local optimum

### Example 2: High-Performing Individual in Smooth Region
**Context**: Top 10% individual, low local ruggedness (0.2), high improvement potential (0.8)
**Selection**: AGGRESSIVE_EXPLOITATION with high local search intensity (0.9) and deep search (depth=20)
**Reasoning**: Smooth landscape and high potential suggest intensive local search will yield good results

### Example 3: Average Individual in Deceptive Landscape
**Context**: Middle-ranked individual, high deceptiveness (0.7), moderate stagnation (5 iterations)
**Selection**: INTELLIGENT_EXPLORATION with adaptive parameters based on landscape features
**Reasoning**: Deceptive landscape requires careful, adaptive exploration to avoid misleading gradients
"""
    
    def _format_reasoning_instructions(self) -> str:
        """Format chain-of-thought reasoning instructions."""
        if not self.use_chain_of_thought:
            return ""
        
        return """
## Reasoning Process
Please follow this reasoning process:

1. **Situation Assessment**: Analyze the individual's current state and performance
2. **Landscape Analysis**: Evaluate the fitness landscape characteristics
3. **Strategy Matching**: Match the situation to appropriate strategy types
4. **Parameter Tuning**: Set specific parameters based on the analysis
5. **Confidence Evaluation**: Assess confidence in the recommendation
6. **Risk Assessment**: Consider potential risks and mitigation strategies

Include your step-by-step reasoning in the "reasoning" field of your response.
"""
