# Intelligent Strategy Selection System - Integration Guide

This guide explains how to integrate the Intelligent Strategy Selection System with the EoH-TSP-Solver framework.

## Overview

The Intelligent Strategy Selection System provides fitness landscape-driven strategy selection using LLM reasoning and advanced coordination mechanisms. It seamlessly integrates with the existing EoH-TSP-Solver framework to enhance optimization performance.

## Quick Start

### 1. Basic Integration

```python
from intelligent_strategy.integration import integrate_with_eoh_evolution

# In your EoH collaboration manager's run_evolution_phase method:
def run_evolution_phase(self, populations, strategies, landscape_report, 
                       distance_matrix, res_populations, iteration):
    
    # Integrate intelligent strategy selection
    updated_populations = integrate_with_eoh_evolution(
        populations=populations,
        strategies=strategies,
        landscape_report=landscape_report,
        distance_matrix=distance_matrix,
        iteration=iteration,
        res_populations=res_populations
    )
    
    return updated_populations
```

### 2. Advanced Integration

```python
from intelligent_strategy import IntelligentStrategySystem
from intelligent_strategy.integration import get_eoh_integrator

# Initialize the system with custom configuration
config = {
    'enabled': True,
    'integration_mode': 'full',
    'llm_interface': {
        'provider': 'openai',
        'model': 'gpt-4',
        'api_key': 'your-api-key'
    }
}

# Get the integrator
integrator = get_eoh_integrator(config)

# Use in your evolution loop
strategy_assignments = integrator.integrate_evolution_phase(
    populations=populations,
    strategies=strategies,
    landscape_report=landscape_report,
    distance_matrix=distance_matrix,
    iteration=iteration,
    res_populations=res_populations
)
```

## Integration Points

### 1. Evolution Phase Integration

The system integrates at the evolution phase level, between strategy selection and strategy execution:

```
EoH Framework Flow:
1. Analysis Phase (Landscape Analysis)
2. Strategy Phase (Strategy Selection) 
3. Evolution Phase (Strategy Execution) ← INTEGRATION POINT
4. Assessment Phase (Performance Evaluation)
```

### 2. Data Flow

```
Input:  EoH Population + Strategy Assignments + Landscape Report
   ↓
Intelligent Strategy System:
   - Individual State Monitoring
   - Landscape Analysis Enhancement  
   - LLM-based Strategy Selection
   - Collaborative Coordination
   - Strategy Execution
   ↓
Output: Updated Population with Improved Solutions
```

## Configuration

### Environment Variables

```bash
# Required for LLM integration
export OPENAI_API_KEY="your-openai-api-key"

# Optional: Custom configuration file
export INTELLIGENT_STRATEGY_CONFIG="config/custom_config.yaml"
```

### Configuration File

Create `config/intelligent_strategy_config.yaml`:

```yaml
system:
  enabled: true
  integration_mode: "full"
  max_workers: 8

llm_interface:
  provider: "openai"
  model: "gpt-4"
  temperature: 0.1
  max_tokens: 2000

strategies:
  STRONG_EXPLORATION:
    perturbation_intensity: 0.8
  BALANCED_EXPLORATION:
    perturbation_intensity: 0.5
  # ... other strategy configurations
```

## Integration Modes

### 1. Full Integration (`integration_mode: "full"`)
- Complete intelligent strategy selection
- LLM-based reasoning
- Collaborative coordination
- Performance monitoring

### 2. Analysis Only (`integration_mode: "analysis_only"`)
- Enhanced landscape analysis
- Individual state monitoring
- No strategy execution changes

### 3. Strategies Only (`integration_mode: "strategies_only"`)
- Intelligent strategy selection
- No coordination mechanisms
- Minimal overhead

## Performance Considerations

### 1. Resource Usage
- CPU: ~10-20% additional overhead
- Memory: ~100-200MB additional usage
- Network: LLM API calls (configurable frequency)

### 2. Optimization Settings

```yaml
optimization:
  enable_multiprocessing: true
  max_parallel_strategies: 4
  strategy_result_cache_size: 500
  default_time_budget: 1.0
```

### 3. Caching

The system includes intelligent caching:
- Landscape analysis results
- Strategy execution outcomes
- LLM responses (when appropriate)

## Monitoring and Debugging

### 1. Enable Logging

```python
import logging
logging.basicConfig(level=logging.INFO)

# The system will log integration activities
```

### 2. Performance Statistics

```python
integrator = get_eoh_integrator()
stats = integrator.get_system_status()

print(f"Strategy selections: {stats['adapter_statistics']['integration_stats']['total_strategy_selections']}")
print(f"Success rate: {stats['adapter_statistics']['integration_stats']['successful_executions']}")
```

### 3. Dashboard (Optional)

Enable the monitoring dashboard:

```yaml
dashboard:
  enabled: true
  host: "localhost"
  port: 8050
```

Access at: http://localhost:8050

## Troubleshooting

### Common Issues

1. **LLM API Errors**
   - Check API key configuration
   - Verify network connectivity
   - Enable fallback mode: `fallback_enabled: true`

2. **Performance Issues**
   - Reduce `max_parallel_strategies`
   - Enable caching: `enable_caching: true`
   - Use lighter integration mode

3. **Memory Issues**
   - Reduce cache sizes
   - Enable garbage collection: `garbage_collection_frequency: 100`

### Fallback Behavior

The system includes robust fallback mechanisms:
- LLM unavailable → Rule-based strategy selection
- Integration errors → Return original population
- Resource constraints → Graceful degradation

## Testing

### Run Integration Demo

```bash
cd src/intelligent_strategy/demo
python integration_demo.py
```

### Unit Tests

```bash
cd src
python -m pytest intelligent_strategy/tests/
```

## API Reference

### Main Integration Function

```python
def integrate_with_eoh_evolution(
    populations: List[Dict],
    strategies: Dict[int, str], 
    landscape_report: Dict[str, Any],
    distance_matrix: np.ndarray,
    iteration: int,
    res_populations: List[Dict],
    config: Optional[Dict] = None
) -> List[Dict]:
    """
    Integrate intelligent strategy selection with EoH evolution.
    
    Args:
        populations: Current population
        strategies: EoH strategy assignments
        landscape_report: Landscape analysis results
        distance_matrix: TSP distance matrix
        iteration: Current iteration number
        res_populations: Elite solutions
        config: Optional configuration override
        
    Returns:
        Updated population after intelligent strategy execution
    """
```

### System Class

```python
class IntelligentStrategySystem:
    def select_strategies(self, population, iteration, **kwargs) -> Dict[int, StrategyAssignment]
    def execute_strategies(self, assignments, population, **kwargs) -> List[ExecutionResult]
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the demo script for examples
3. Enable debug logging for detailed information
4. Consult the design document for theoretical background

## Version Compatibility

- EoH-TSP-Solver: Compatible with current framework
- Python: 3.8+
- Dependencies: See requirements.txt

## License

This integration is part of the EoH-TSP-Solver project.
