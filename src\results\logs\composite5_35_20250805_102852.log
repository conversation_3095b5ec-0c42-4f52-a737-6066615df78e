2025-08-05 10:28:52,775 - __main__ - INFO - composite5_35 开始进化第 1 代
2025-08-05 10:28:52,775 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-08-05 10:28:52,776 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:52,778 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9280.000, 多样性=0.917
2025-08-05 10:28:52,780 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:52,782 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.917
2025-08-05 10:28:52,784 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:52,786 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 0/5)
2025-08-05 10:28:52,786 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:52,787 - LandscapeExpert - INFO - 添加精英解数据: 0个精英解
2025-08-05 10:28:52,787 - LandscapeExpert - INFO - 数据提取成功: 10个路径, 10个适应度值
2025-08-05 10:28:52,801 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.100, 适应度梯度: -9344.120, 聚类评分: 0.000, 覆盖率: 0.097, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:52,801 - LandscapeExpert - INFO - 开始更新可视化 (迭代 0)
2025-08-05 10:28:52,801 - LandscapeExpert - INFO - 提取到 3 个精英解
2025-08-05 10:28:52,801 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 10:28:52,806 - visualization.landscape_visualizer - INFO - 插值约束: 7 个点被约束到最小值 9280.00
2025-08-05 10:28:52,807 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.2%, 梯度: 1425.32 → 1337.36
2025-08-05 10:28:52,915 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_81_20250805_102852.html
2025-08-05 10:28:52,993 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_81_20250805_102852.html
2025-08-05 10:28:52,994 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 81
2025-08-05 10:28:52,994 - LandscapeExpert - INFO - 可视化已更新 (迭代 0)
2025-08-05 10:28:52,994 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2079秒
2025-08-05 10:28:52,994 - experts.management.collaboration_manager - INFO - 缓存统计: {'global_cache': {'size': 0, 'max_size': 1000, 'hits': 0, 'misses': 0, 'hit_rate': 0, 'evictions': 0, 'ttl': 3600}, 'population_cache': {'size': 162, 'max_size': 500, 'hits': 0, 'misses': 162, 'hit_rate': 0.0, 'evictions': 0, 'ttl': 1800}, 'distance_cache': {'size': 100, 'max_size': 100, 'hits': 542, 'misses': 278, 'hit_rate': 0.6609756097560976, 'evictions': 178, 'ttl': 7200}}
2025-08-05 10:28:52,995 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.1, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -9344.12, 'local_optima_density': 0.1, 'gradient_variance': 590678768.7696, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0968, 'fitness_entropy': 0.947730922119161, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -9344.120)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.097)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 0, 'total': 5, 'progress': 0.0}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360932.8011944, 'performance_metrics': {}}}
2025-08-05 10:28:52,995 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:52,995 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:52,995 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:52,995 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:52,997 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:52,997 - StrategyExpert - INFO - 自适应探索比例: 0.850
2025-08-05 10:28:52,997 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:52,997 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,997 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:52,997 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:52,998 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.850
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:52,998 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:52,998 - experts.management.collaboration_manager - INFO - 识别精英个体: {0, 1} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:52,999 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:52,999 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:52,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,001 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:53,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61926.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,002 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [19, 31, 2, 30, 18, 20, 17, 22, 7, 24, 25, 21, 4, 26, 34, 13, 32, 12, 5, 29, 10, 27, 8, 0, 23, 3, 1, 33, 11, 9, 16, 28, 6, 14, 15], 'cur_cost': 61926.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,002 - experts.management.collaboration_manager - INFO - 个体 0 保留原路径 (成本: 61926.00)
2025-08-05 10:28:53,003 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:53,003 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:53,003 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,004 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,005 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9688.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,005 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9688.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,006 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 9688.00)
2025-08-05 10:28:53,006 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:53,006 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:53,006 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,010 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,011 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,012 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14445.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,012 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 22, 13, 7, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,013 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 14445.00)
2025-08-05 10:28:53,013 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:53,013 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:53,013 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,015 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9746.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,016 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9746.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,017 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 9746.00)
2025-08-05 10:28:53,017 - experts.management.collaboration_manager - INFO - 为个体 4 生成利用路径
2025-08-05 10:28:53,017 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,017 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,018 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 57570.0
2025-08-05 10:28:53,028 - ExploitationExpert - INFO - res_population_num: 5
2025-08-05 10:28:53,029 - ExploitationExpert - INFO - res_population_costs: [9095.0, 9095, 9090, 9090, 9061]
2025-08-05 10:28:53,029 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64)]
2025-08-05 10:28:53,031 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,031 - ExploitationExpert - INFO - populations: [{'tour': [19, 31, 2, 30, 18, 20, 17, 22, 7, 24, 25, 21, 4, 26, 34, 13, 32, 12, 5, 29, 10, 27, 8, 0, 23, 3, 1, 33, 11, 9, 16, 28, 6, 14, 15], 'cur_cost': 61926.0}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9688.0}, {'tour': [0, 22, 13, 7, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14445.0}, {'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9746.0}, {'tour': array([ 0,  9, 16, 10, 24, 22,  5, 33, 12, 15, 23,  7, 11, 26, 13,  8, 28,
       20, 21, 29, 30, 34, 14, 32,  3, 19,  2, 27, 25,  4, 18,  1, 17, 31,
        6], dtype=int64), 'cur_cost': 57570.0}, {'tour': array([ 5, 15, 27, 20, 11, 32, 12, 23,  9, 30, 33, 25, 10, 21, 24,  2, 18,
        4,  1,  3, 28, 19, 22, 17,  7, 26,  6,  0, 13, 29, 34,  8, 14, 31,
       16], dtype=int64), 'cur_cost': 57396.0}, {'tour': array([27, 31, 20, 18, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 64928.0}, {'tour': array([14, 30,  9,  2, 31, 12, 17, 16, 29,  4,  6, 34, 11, 21, 22,  0, 24,
       19, 18, 23,  8, 15,  5, 20, 28,  7, 25, 33, 32,  1, 13, 26, 27,  3,
       10], dtype=int64), 'cur_cost': 51317.0}, {'tour': array([18, 28, 22, 23, 30,  1, 20, 26,  4,  2, 11,  8, 24, 10,  7,  6, 32,
       15,  0, 31, 13, 12,  5, 29, 14, 17, 27,  3,  9, 33, 25, 34, 21, 16,
       19], dtype=int64), 'cur_cost': 56549.0}, {'tour': array([11, 19, 28,  6,  3, 33, 13, 15, 25, 24, 14,  9, 32,  0,  4, 16, 29,
        5, 20, 23, 18,  8,  2, 26,  7, 27, 31, 34, 21, 30,  1, 17, 10, 22,
       12], dtype=int64), 'cur_cost': 57698.0}]
2025-08-05 10:28:53,034 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 209, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 209, 'cache_hits': 0, 'similarity_calculations': 966, 'cache_hit_rate': 0.0, 'cache_size': 966}}
2025-08-05 10:28:53,035 - experts.management.collaboration_manager - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 0,  9, 16, 10, 24, 22,  5, 33, 12, 15, 23,  7, 11, 26, 13,  8, 28,
       20, 21, 29, 30, 34, 14, 32,  3, 19,  2, 27, 25,  4, 18,  1, 17, 31,
        6], dtype=int64), 'cur_cost': 57570.0, 'intermediate_solutions': [{'tour': array([ 7,  4, 32, 34,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 61909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34,  7,  4, 32,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 34,  7,  4, 32, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 34,  7,  4,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 61512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32,  1, 34,  7,  4, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,035 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 57570.00)
2025-08-05 10:28:53,035 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:53,035 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:53,036 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,038 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:53,039 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33153.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,039 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [9, 2, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 33153.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,039 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 33153.00)
2025-08-05 10:28:53,039 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:53,039 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,039 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,040 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 71283.0
2025-08-05 10:28:53,055 - ExploitationExpert - INFO - res_population_num: 6
2025-08-05 10:28:53,055 - ExploitationExpert - INFO - res_population_costs: [9095.0, 9095, 9090, 9090, 9061, 9061]
2025-08-05 10:28:53,056 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64)]
2025-08-05 10:28:53,059 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,060 - ExploitationExpert - INFO - populations: [{'tour': [19, 31, 2, 30, 18, 20, 17, 22, 7, 24, 25, 21, 4, 26, 34, 13, 32, 12, 5, 29, 10, 27, 8, 0, 23, 3, 1, 33, 11, 9, 16, 28, 6, 14, 15], 'cur_cost': 61926.0}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9688.0}, {'tour': [0, 22, 13, 7, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14445.0}, {'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9746.0}, {'tour': array([ 0,  9, 16, 10, 24, 22,  5, 33, 12, 15, 23,  7, 11, 26, 13,  8, 28,
       20, 21, 29, 30, 34, 14, 32,  3, 19,  2, 27, 25,  4, 18,  1, 17, 31,
        6], dtype=int64), 'cur_cost': 57570.0}, {'tour': [9, 2, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 33153.0}, {'tour': array([ 2, 29, 30, 12, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21], dtype=int64), 'cur_cost': 71283.0}, {'tour': array([14, 30,  9,  2, 31, 12, 17, 16, 29,  4,  6, 34, 11, 21, 22,  0, 24,
       19, 18, 23,  8, 15,  5, 20, 28,  7, 25, 33, 32,  1, 13, 26, 27,  3,
       10], dtype=int64), 'cur_cost': 51317.0}, {'tour': array([18, 28, 22, 23, 30,  1, 20, 26,  4,  2, 11,  8, 24, 10,  7,  6, 32,
       15,  0, 31, 13, 12,  5, 29, 14, 17, 27,  3,  9, 33, 25, 34, 21, 16,
       19], dtype=int64), 'cur_cost': 56549.0}, {'tour': array([11, 19, 28,  6,  3, 33, 13, 15, 25, 24, 14,  9, 32,  0,  4, 16, 29,
        5, 20, 23, 18,  8,  2, 26,  7, 27, 31, 34, 21, 30,  1, 17, 10, 22,
       12], dtype=int64), 'cur_cost': 57698.0}]
2025-08-05 10:28:53,063 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,063 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 210, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 210, 'cache_hits': 0, 'similarity_calculations': 967, 'cache_hit_rate': 0.0, 'cache_size': 967}}
2025-08-05 10:28:53,064 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 2, 29, 30, 12, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21], dtype=int64), 'cur_cost': 71283.0, 'intermediate_solutions': [{'tour': array([20, 31, 27, 18, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 67018.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 20, 31, 27, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 66837.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 18, 20, 31, 27,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 64958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 18, 20, 31, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 70304.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 24, 18, 20, 31,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 68328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,064 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 71283.00)
2025-08-05 10:28:53,064 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:53,065 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:53,065 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,069 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:53,070 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32536.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,070 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32536.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,071 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 32536.00)
2025-08-05 10:28:53,071 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:53,071 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:53,071 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,074 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:53,074 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 38662.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,074 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 38662.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,074 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 38662.00)
2025-08-05 10:28:53,075 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:53,075 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:53,075 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,076 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,076 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12407.0, 路径长度: 35, 收集中间解: 0
2025-08-05 10:28:53,076 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12407.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,076 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12407.00)
2025-08-05 10:28:53,076 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:53,076 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:53,078 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [19, 31, 2, 30, 18, 20, 17, 22, 7, 24, 25, 21, 4, 26, 34, 13, 32, 12, 5, 29, 10, 27, 8, 0, 23, 3, 1, 33, 11, 9, 16, 28, 6, 14, 15], 'cur_cost': 61926.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9688.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 13, 7, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14445.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9746.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  9, 16, 10, 24, 22,  5, 33, 12, 15, 23,  7, 11, 26, 13,  8, 28,
       20, 21, 29, 30, 34, 14, 32,  3, 19,  2, 27, 25,  4, 18,  1, 17, 31,
        6], dtype=int64), 'cur_cost': 57570.0, 'intermediate_solutions': [{'tour': array([ 7,  4, 32, 34,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 61909.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([34,  7,  4, 32,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65195.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 1, 34,  7,  4, 32, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([32, 34,  7,  4,  1, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 61512.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([32,  1, 34,  7,  4, 17, 14,  2, 13, 23, 29, 30, 31, 22,  6, 11, 19,
       33,  8, 25, 20, 27, 12, 18, 15, 21, 28,  3, 26,  5,  0,  9, 10, 16,
       24], dtype=int64), 'cur_cost': 65167.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [9, 2, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 33153.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 2, 29, 30, 12, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21], dtype=int64), 'cur_cost': 71283.0, 'intermediate_solutions': [{'tour': array([20, 31, 27, 18, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 67018.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([18, 20, 31, 27, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 66837.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([24, 18, 20, 31, 27,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 64958.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([27, 18, 20, 31, 24,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 70304.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([27, 24, 18, 20, 31,  7,  8,  2,  9,  0, 26, 23, 19, 25, 14, 21, 28,
       17, 12, 33, 22, 15, 11, 30,  1, 32, 13,  4, 29,  5,  6, 16,  3, 34,
       10], dtype=int64), 'cur_cost': 68328.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32536.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 38662.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12407.0, 'intermediate_solutions': [], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:53,078 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:53,078 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,080 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9688.000, 多样性=0.924
2025-08-05 10:28:53,081 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-08-05 10:28:53,081 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-08-05 10:28:53,081 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:53,081 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.032316697141702155, 'best_improvement': -0.04396551724137931}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.008310249307479242}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.09071846604180589, 'recent_improvements': [-0.15606710964439188, 0.025280731916536247, 0.02536982243921987], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7466666666666667, 'new_diversity': 0.7466666666666667, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-08-05 10:28:53,082 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-08-05 10:28:53,082 - __main__ - INFO - composite5_35 开始进化第 2 代
2025-08-05 10:28:53,082 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-08-05 10:28:53,082 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,083 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9688.000, 多样性=0.924
2025-08-05 10:28:53,083 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:53,084 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.924
2025-08-05 10:28:53,084 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:53,086 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.747
2025-08-05 10:28:53,088 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 1/5)
2025-08-05 10:28:53,088 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:53,088 - LandscapeExpert - INFO - 添加精英解数据: 6个精英解
2025-08-05 10:28:53,088 - LandscapeExpert - INFO - 数据提取成功: 16个路径, 16个适应度值
2025-08-05 10:28:53,114 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.125, 适应度梯度: -2080.037, 聚类评分: 0.000, 覆盖率: 0.098, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:53,114 - LandscapeExpert - INFO - 开始更新可视化 (迭代 1)
2025-08-05 10:28:53,114 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:53,115 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 10:28:53,119 - visualization.landscape_visualizer - INFO - 插值约束: 87 个点被约束到最小值 9061.00
2025-08-05 10:28:53,120 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=6.4%, 梯度: 2502.87 → 2342.59
2025-08-05 10:28:53,234 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_82_20250805_102853.html
2025-08-05 10:28:53,293 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_82_20250805_102853.html
2025-08-05 10:28:53,293 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 82
2025-08-05 10:28:53,293 - LandscapeExpert - INFO - 可视化已更新 (迭代 1)
2025-08-05 10:28:53,293 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2051秒
2025-08-05 10:28:53,293 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.125, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -2080.0374999999995, 'local_optima_density': 0.125, 'gradient_variance': 424601919.13609374, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0983, 'fitness_entropy': 0.6958688724473057, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -2080.037)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.098)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['mutation', 'crossover', 'perturbation'], 'parameters': {'mutation_rate': 0.3, 'crossover_rate': 0.7, 'perturbation_strength': 0.8}}, 'iteration_info': {'current': 1, 'total': 5, 'progress': 0.2}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360933.1140938, 'performance_metrics': {}}}
2025-08-05 10:28:53,293 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:53,294 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:53,294 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:53,294 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:53,294 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:53,294 - StrategyExpert - INFO - 自适应探索比例: 0.818
2025-08-05 10:28:53,295 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:53,295 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,295 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:53,295 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:53,295 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.818
- 探索个体数量: 8
- 利用个体数量: 2
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,295 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:53,296 - experts.management.collaboration_manager - INFO - 识别精英个体: {1, 3} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:53,296 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:53,296 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,296 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,297 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 63659.0
2025-08-05 10:28:53,314 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:28:53,314 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9090, 9090, 9095.0, 9095, 9061.0]
2025-08-05 10:28:53,314 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 10:28:53,317 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,318 - ExploitationExpert - INFO - populations: [{'tour': array([15, 26, 21, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7], dtype=int64), 'cur_cost': 63659.0}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9688.0}, {'tour': [0, 22, 13, 7, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14445.0}, {'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9746.0}, {'tour': [0, 9, 16, 10, 24, 22, 5, 33, 12, 15, 23, 7, 11, 26, 13, 8, 28, 20, 21, 29, 30, 34, 14, 32, 3, 19, 2, 27, 25, 4, 18, 1, 17, 31, 6], 'cur_cost': 57570.0}, {'tour': [9, 2, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 33153.0}, {'tour': [2, 29, 30, 12, 26, 11, 5, 13, 27, 9, 32, 14, 22, 1, 18, 19, 3, 0, 8, 31, 7, 16, 34, 17, 6, 15, 4, 24, 25, 10, 20, 33, 23, 28, 21], 'cur_cost': 71283.0}, {'tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32536.0}, {'tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 38662.0}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12407.0}]
2025-08-05 10:28:53,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 211, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 211, 'cache_hits': 0, 'similarity_calculations': 969, 'cache_hit_rate': 0.0, 'cache_size': 969}}
2025-08-05 10:28:53,320 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([15, 26, 21, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7], dtype=int64), 'cur_cost': 63659.0, 'intermediate_solutions': [{'tour': array([ 2, 31, 19, 30, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  2, 31, 19, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 59820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30,  2, 31, 19, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 30,  2, 31, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 18, 30,  2, 31, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,320 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 63659.00)
2025-08-05 10:28:53,320 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:53,320 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:53,320 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,322 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:53,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53280.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,324 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 53280.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 12, 14, 9, 23, 10, 13, 3, 5, 1, 7, 6, 4, 2, 15, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 32, 27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 29, 33, 26], 'cur_cost': 15183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 18, 31, 27, 32, 29, 33, 26], 'cur_cost': 15194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,324 - experts.management.collaboration_manager - INFO - 个体 1 保留原路径 (成本: 53280.00)
2025-08-05 10:28:53,324 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:53,325 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:53,325 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,326 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,327 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14357.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,328 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14357.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 9, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 7, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17643.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 29, 32, 27, 31, 28, 34, 25, 30, 12, 10, 8, 15, 9, 14, 11, 24, 16, 20, 18, 19, 21, 17, 23, 5, 3, 4, 6, 2, 1, 7, 13, 22, 33, 26], 'cur_cost': 17805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 7, 1, 2, 6, 4, 5, 3, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,328 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 14357.00)
2025-08-05 10:28:53,328 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:53,328 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:53,328 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,332 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:53,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,332 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,333 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,333 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,333 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33095.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,333 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 33095.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 29, 28, 31, 27, 32, 34, 33, 26], 'cur_cost': 9919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 14, 11, 4, 3, 27, 32, 29, 33, 26], 'cur_cost': 13588.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 18, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9722.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,334 - experts.management.collaboration_manager - INFO - 个体 3 保留原路径 (成本: 33095.00)
2025-08-05 10:28:53,334 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:53,334 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:53,334 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,336 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,336 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,337 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9683.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,337 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9683.0, 'intermediate_solutions': [{'tour': [0, 9, 16, 10, 24, 22, 5, 33, 12, 15, 23, 7, 11, 26, 13, 8, 6, 20, 21, 29, 30, 34, 14, 32, 3, 19, 2, 27, 25, 4, 18, 1, 17, 31, 28], 'cur_cost': 54570.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 16, 10, 30, 29, 21, 20, 28, 8, 13, 26, 11, 7, 23, 15, 12, 33, 5, 22, 24, 34, 14, 32, 3, 19, 2, 27, 25, 4, 18, 1, 17, 31, 6], 'cur_cost': 60810.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 0, 9, 16, 10, 24, 22, 5, 33, 12, 15, 23, 7, 11, 26, 13, 8, 28, 20, 21, 29, 30, 34, 14, 32, 3, 2, 27, 25, 4, 18, 1, 17, 31, 6], 'cur_cost': 57522.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,337 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 9683.00)
2025-08-05 10:28:53,337 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:53,337 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:53,338 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,339 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,339 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,340 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11726.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,341 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [9, 11, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 2, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 35787.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 2, 23, 0, 22, 25, 28, 33, 26, 30, 3, 21, 17, 18, 14, 11, 15, 12, 4, 16, 7, 6, 5, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 36451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 2, 23, 0, 22, 5, 6, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 7, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 36809.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,341 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 11726.00)
2025-08-05 10:28:53,341 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:53,341 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,342 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,342 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 69590.0
2025-08-05 10:28:53,359 - ExploitationExpert - INFO - res_population_num: 7
2025-08-05 10:28:53,359 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9090, 9090, 9095.0, 9095, 9061.0]
2025-08-05 10:28:53,359 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 10:28:53,361 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,362 - ExploitationExpert - INFO - populations: [{'tour': array([15, 26, 21, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7], dtype=int64), 'cur_cost': 63659.0}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 53280.0}, {'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14357.0}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 33095.0}, {'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9683.0}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0}, {'tour': array([ 0, 20, 19,  2, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10], dtype=int64), 'cur_cost': 69590.0}, {'tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32536.0}, {'tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 38662.0}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12407.0}]
2025-08-05 10:28:53,363 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,363 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 212, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 212, 'cache_hits': 0, 'similarity_calculations': 972, 'cache_hit_rate': 0.0, 'cache_size': 972}}
2025-08-05 10:28:53,364 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 0, 20, 19,  2, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10], dtype=int64), 'cur_cost': 69590.0, 'intermediate_solutions': [{'tour': array([30, 29,  2, 12, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 30, 29,  2, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 12, 30, 29,  2, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 12, 30, 29, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 68020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 26, 12, 30, 29, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71268.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,364 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 69590.00)
2025-08-05 10:28:53,364 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:53,364 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:53,364 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,365 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,365 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,366 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12282.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,366 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12282.0, 'intermediate_solutions': [{'tour': [32, 5, 17, 24, 18, 21, 11, 0, 20, 4, 2, 7, 16, 10, 15, 12, 23, 6, 22, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32569.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 25, 26, 31, 28, 29, 27, 33, 34, 30, 3, 1, 19, 14], 'cur_cost': 32615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 5, 17, 24, 28, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 37837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,367 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12282.00)
2025-08-05 10:28:53,367 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:53,367 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:53,367 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,368 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:53,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,368 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,369 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,369 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57387.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,369 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [18, 11, 17, 1, 14, 2, 5, 25, 33, 28, 12, 7, 34, 9, 20, 8, 6, 4, 26, 27, 13, 0, 31, 3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23, 15], 'cur_cost': 57387.0, 'intermediate_solutions': [{'tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 34, 2, 12, 7, 21, 9, 29, 28, 31, 22], 'cur_cost': 38634.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 32, 6, 17, 27, 33, 30, 11, 14, 16, 3, 18, 0, 15, 10, 8, 13, 24, 4, 19, 23, 1, 5, 20, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 44055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 32, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 6, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 41555.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,369 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 57387.00)
2025-08-05 10:28:53,370 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:53,370 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:53,370 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,371 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,371 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,372 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9381.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,372 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9381.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 26, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 16], 'cur_cost': 19787.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 25, 30, 12, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18358.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 18, 20, 16, 22, 24, 11, 14, 9, 8, 19, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17131.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,372 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 9381.00)
2025-08-05 10:28:53,373 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:53,373 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:53,374 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 26, 21, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7], dtype=int64), 'cur_cost': 63659.0, 'intermediate_solutions': [{'tour': array([ 2, 31, 19, 30, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61989.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([30,  2, 31, 19, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 59820.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([18, 30,  2, 31, 19, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61927.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([19, 30,  2, 31, 18, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61926.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([19, 18, 30,  2, 31, 20, 17, 22,  7, 24, 25, 21,  4, 26, 34, 13, 32,
       12,  5, 29, 10, 27,  8,  0, 23,  3,  1, 33, 11,  9, 16, 28,  6, 14,
       15]), 'cur_cost': 61941.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 53280.0, 'intermediate_solutions': [{'tour': [0, 8, 11, 12, 14, 9, 23, 10, 13, 3, 5, 1, 7, 6, 4, 2, 15, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16996.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 32, 27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 29, 33, 26], 'cur_cost': 15183.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 8, 11, 12, 14, 9, 15, 10, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 20, 16, 22, 24, 30, 25, 34, 28, 18, 31, 27, 32, 29, 33, 26], 'cur_cost': 15194.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14357.0, 'intermediate_solutions': [{'tour': [0, 22, 13, 9, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 7, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17643.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 29, 32, 27, 31, 28, 34, 25, 30, 12, 10, 8, 15, 9, 14, 11, 24, 16, 20, 18, 19, 21, 17, 23, 5, 3, 4, 6, 2, 1, 7, 13, 22, 33, 26], 'cur_cost': 17805.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 22, 13, 7, 1, 2, 6, 4, 5, 3, 23, 17, 21, 19, 18, 20, 16, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14436.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 33095.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 29, 28, 31, 27, 32, 34, 33, 26], 'cur_cost': 9919.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 21, 17, 23, 18, 16, 22, 24, 6, 7, 1, 2, 5, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 14, 11, 4, 3, 27, 32, 29, 33, 26], 'cur_cost': 13588.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 18, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9722.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9683.0, 'intermediate_solutions': [{'tour': [0, 9, 16, 10, 24, 22, 5, 33, 12, 15, 23, 7, 11, 26, 13, 8, 6, 20, 21, 29, 30, 34, 14, 32, 3, 19, 2, 27, 25, 4, 18, 1, 17, 31, 28], 'cur_cost': 54570.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 9, 16, 10, 30, 29, 21, 20, 28, 8, 13, 26, 11, 7, 23, 15, 12, 33, 5, 22, 24, 34, 14, 32, 3, 19, 2, 27, 25, 4, 18, 1, 17, 31, 6], 'cur_cost': 60810.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [19, 0, 9, 16, 10, 24, 22, 5, 33, 12, 15, 23, 7, 11, 26, 13, 8, 28, 20, 21, 29, 30, 34, 14, 32, 3, 2, 27, 25, 4, 18, 1, 17, 31, 6], 'cur_cost': 57522.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [9, 11, 23, 0, 22, 5, 6, 7, 16, 4, 12, 15, 2, 14, 18, 17, 21, 3, 30, 26, 33, 28, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 35787.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 2, 23, 0, 22, 25, 28, 33, 26, 30, 3, 21, 17, 18, 14, 11, 15, 12, 4, 16, 7, 6, 5, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 36451.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 2, 23, 0, 22, 5, 6, 16, 4, 12, 15, 11, 14, 18, 17, 21, 3, 30, 26, 33, 28, 7, 25, 31, 34, 27, 32, 19, 24, 13, 8, 1, 20, 10, 29], 'cur_cost': 36809.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 20, 19,  2, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10], dtype=int64), 'cur_cost': 69590.0, 'intermediate_solutions': [{'tour': array([30, 29,  2, 12, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71323.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([12, 30, 29,  2, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71270.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([26, 12, 30, 29,  2, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71321.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 2, 12, 30, 29, 26, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 68020.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 2, 26, 12, 30, 29, 11,  5, 13, 27,  9, 32, 14, 22,  1, 18, 19,  3,
        0,  8, 31,  7, 16, 34, 17,  6, 15,  4, 24, 25, 10, 20, 33, 23, 28,
       21]), 'cur_cost': 71268.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12282.0, 'intermediate_solutions': [{'tour': [32, 5, 17, 24, 18, 21, 11, 0, 20, 4, 2, 7, 16, 10, 15, 12, 23, 6, 22, 8, 13, 9, 33, 27, 29, 28, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 32569.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [32, 5, 17, 24, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 25, 26, 31, 28, 29, 27, 33, 34, 30, 3, 1, 19, 14], 'cur_cost': 32615.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [32, 5, 17, 24, 28, 18, 21, 11, 0, 22, 4, 2, 7, 16, 10, 15, 12, 23, 6, 20, 8, 13, 9, 33, 27, 29, 31, 26, 25, 34, 30, 3, 1, 19, 14], 'cur_cost': 37837.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [18, 11, 17, 1, 14, 2, 5, 25, 33, 28, 12, 7, 34, 9, 20, 8, 6, 4, 26, 27, 13, 0, 31, 3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23, 15], 'cur_cost': 57387.0, 'intermediate_solutions': [{'tour': [26, 32, 6, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 34, 2, 12, 7, 21, 9, 29, 28, 31, 22], 'cur_cost': 38634.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [26, 32, 6, 17, 27, 33, 30, 11, 14, 16, 3, 18, 0, 15, 10, 8, 13, 24, 4, 19, 23, 1, 5, 20, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 44055.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [26, 32, 17, 20, 5, 1, 23, 19, 4, 24, 13, 8, 6, 10, 15, 0, 18, 3, 16, 14, 11, 30, 33, 27, 25, 31, 2, 12, 7, 21, 9, 29, 28, 34, 22], 'cur_cost': 41555.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9381.0, 'intermediate_solutions': [{'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 26, 22, 24, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 16], 'cur_cost': 19787.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 8, 10, 13, 25, 30, 12, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18358.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 3, 4, 1, 7, 6, 2, 5, 23, 17, 21, 18, 20, 16, 22, 24, 11, 14, 9, 8, 19, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17131.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:53,375 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:53,375 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,377 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9381.000, 多样性=0.858
2025-08-05 10:28:53,378 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-08-05 10:28:53,378 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-08-05 10:28:53,378 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:53,379 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.025290547282971216, 'best_improvement': 0.03168868703550785}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.0721153846153846}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 0.0035179826125829503, 'recent_improvements': [0.025280731916536247, 0.02536982243921987, 0.032316697141702155], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 7, 'new_count': 7, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.745578231292517, 'new_diversity': 0.745578231292517, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启']}
2025-08-05 10:28:53,380 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-08-05 10:28:53,380 - __main__ - INFO - composite5_35 开始进化第 3 代
2025-08-05 10:28:53,380 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-08-05 10:28:53,381 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,382 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9381.000, 多样性=0.858
2025-08-05 10:28:53,382 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:53,385 - PathExpert - INFO - 路径结构分析完成: 公共边数量=8, 路径相似性=0.858
2025-08-05 10:28:53,385 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:53,388 - EliteExpert - INFO - 精英解分析完成: 精英解数量=7, 多样性=0.746
2025-08-05 10:28:53,391 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 2/5)
2025-08-05 10:28:53,391 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:53,391 - LandscapeExpert - INFO - 添加精英解数据: 7个精英解
2025-08-05 10:28:53,391 - LandscapeExpert - INFO - 数据提取成功: 17个路径, 17个适应度值
2025-08-05 10:28:53,419 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.235, 适应度梯度: -4681.518, 聚类评分: 0.000, 覆盖率: 0.099, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:53,420 - LandscapeExpert - INFO - 开始更新可视化 (迭代 2)
2025-08-05 10:28:53,420 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:53,420 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 10:28:53,425 - visualization.landscape_visualizer - INFO - 插值约束: 93 个点被约束到最小值 9061.00
2025-08-05 10:28:53,426 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=5.9%, 梯度: 2155.69 → 2028.56
2025-08-05 10:28:53,533 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_83_20250805_102853.html
2025-08-05 10:28:53,609 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_83_20250805_102853.html
2025-08-05 10:28:53,609 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 83
2025-08-05 10:28:53,609 - LandscapeExpert - INFO - 可视化已更新 (迭代 2)
2025-08-05 10:28:53,609 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2177秒
2025-08-05 10:28:53,609 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.23529411764705882, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -4681.517647058824, 'local_optima_density': 0.23529411764705882, 'gradient_variance': 421035217.4073356, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.0994, 'fitness_entropy': 0.6608034791941734, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -4681.518)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.099)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 2, 'total': 5, 'progress': 0.4}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360933.420688, 'performance_metrics': {}}}
2025-08-05 10:28:53,610 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:53,610 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:53,610 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:53,610 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:53,610 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,610 - StrategyExpert - INFO - 自适应探索比例: 0.786
2025-08-05 10:28:53,610 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,611 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,611 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:53,611 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,611 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.786
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,611 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:53,611 - experts.management.collaboration_manager - INFO - 识别精英个体: {9, 4} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:53,612 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:53,612 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,612 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,612 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 62672.0
2025-08-05 10:28:53,623 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:53,623 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9090, 9090, 9095.0, 9095, 9061, 9061]
2025-08-05 10:28:53,623 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 10:28:53,626 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,626 - ExploitationExpert - INFO - populations: [{'tour': array([25,  7, 22,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19], dtype=int64), 'cur_cost': 62672.0}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 53280.0}, {'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14357.0}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 33095.0}, {'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9683.0}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0}, {'tour': [0, 20, 19, 2, 14, 17, 4, 18, 34, 9, 31, 1, 24, 27, 7, 11, 3, 13, 16, 26, 12, 8, 32, 6, 30, 5, 25, 21, 29, 22, 33, 23, 28, 15, 10], 'cur_cost': 69590.0}, {'tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12282.0}, {'tour': [18, 11, 17, 1, 14, 2, 5, 25, 33, 28, 12, 7, 34, 9, 20, 8, 6, 4, 26, 27, 13, 0, 31, 3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23, 15], 'cur_cost': 57387.0}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9381.0}]
2025-08-05 10:28:53,627 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒，最大迭代次数: 10
2025-08-05 10:28:53,627 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 213, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 213, 'cache_hits': 0, 'similarity_calculations': 976, 'cache_hit_rate': 0.0, 'cache_size': 976}}
2025-08-05 10:28:53,627 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([25,  7, 22,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19], dtype=int64), 'cur_cost': 62672.0, 'intermediate_solutions': [{'tour': array([21, 26, 15, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 21, 26, 15, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 60936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 25, 21, 26, 15, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 25, 21, 26, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 13, 25, 21, 26, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 57687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,628 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 62672.00)
2025-08-05 10:28:53,628 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:53,628 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:53,628 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,629 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,629 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,630 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14322.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,630 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14322.0, 'intermediate_solutions': [{'tour': [9, 13, 7, 11, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 17, 16, 26, 23], 'cur_cost': 51365.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 26, 16, 11, 23], 'cur_cost': 53217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 29, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 56988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,630 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 14322.00)
2025-08-05 10:28:53,630 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:53,630 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:53,630 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,631 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,631 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,632 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9654.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,632 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9654.0, 'intermediate_solutions': [{'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 25, 15, 8, 10, 12, 30, 9, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 26358.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 21, 19, 18, 13, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14319.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 11, 33, 26], 'cur_cost': 20327.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,633 - experts.management.collaboration_manager - INFO - 个体 2 接受新路径 (成本: 9654.00)
2025-08-05 10:28:53,633 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:53,633 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:53,633 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,636 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:53,636 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,637 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37217.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,638 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 37217.0, 'intermediate_solutions': [{'tour': [8, 0, 5, 3, 18, 10, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 32753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 24, 32, 25, 26, 28, 31, 27, 34, 29, 33, 30, 19, 22, 23, 16, 1, 15], 'cur_cost': 35129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 9, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 34368.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,638 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 37217.00)
2025-08-05 10:28:53,638 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:53,638 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:53,638 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,640 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,640 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,641 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11726.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,642 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 32, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 14, 29, 33, 26], 'cur_cost': 21762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 1, 7, 6, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9724.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 20, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 21, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14252.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,642 - experts.management.collaboration_manager - INFO - 个体 4 保留原路径 (成本: 11726.00)
2025-08-05 10:28:53,642 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:53,643 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:53,643 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,644 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,644 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,645 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,645 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9644.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,645 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9644.0, 'intermediate_solutions': [{'tour': [20, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 0, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16214.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 11, 24, 22, 20, 18, 19, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16400.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 23, 27, 32, 29, 33, 26], 'cur_cost': 17214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,646 - experts.management.collaboration_manager - INFO - 个体 5 接受新路径 (成本: 9644.00)
2025-08-05 10:28:53,646 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:53,646 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,646 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,646 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 60314.0
2025-08-05 10:28:53,663 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:53,664 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9090, 9090, 9095.0, 9095, 9061, 9061]
2025-08-05 10:28:53,664 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 10:28:53,669 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,669 - ExploitationExpert - INFO - populations: [{'tour': array([25,  7, 22,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19], dtype=int64), 'cur_cost': 62672.0}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14322.0}, {'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9654.0}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 37217.0}, {'tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9644.0}, {'tour': array([14, 21, 26, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12], dtype=int64), 'cur_cost': 60314.0}, {'tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12282.0}, {'tour': [18, 11, 17, 1, 14, 2, 5, 25, 33, 28, 12, 7, 34, 9, 20, 8, 6, 4, 26, 27, 13, 0, 31, 3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23, 15], 'cur_cost': 57387.0}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9381.0}]
2025-08-05 10:28:53,670 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,671 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 214, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 214, 'cache_hits': 0, 'similarity_calculations': 981, 'cache_hit_rate': 0.0, 'cache_size': 981}}
2025-08-05 10:28:53,671 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([14, 21, 26, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12], dtype=int64), 'cur_cost': 60314.0, 'intermediate_solutions': [{'tour': array([19, 20,  0,  2, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 19, 20,  0, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  2, 19, 20,  0, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 66942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  2, 19, 20, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 14,  2, 19, 20, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 67587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,672 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 60314.00)
2025-08-05 10:28:53,672 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:53,672 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:53,672 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,673 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,674 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,674 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12441.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,675 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12441.0, 'intermediate_solutions': [{'tour': [0, 2, 13, 5, 3, 34, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 1, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 13, 31, 28, 34, 25, 30, 12, 10, 8, 15, 9, 14, 11, 24, 22, 16, 20, 18, 19, 21, 17, 23, 4, 6, 7, 1, 3, 5, 27, 32, 29, 33, 26], 'cur_cost': 15750.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 25, 9, 15, 8, 10, 12, 30, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18307.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,675 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 12441.00)
2025-08-05 10:28:53,675 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:53,675 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,675 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,675 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 55751.0
2025-08-05 10:28:53,687 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:53,687 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9090, 9090, 9095.0, 9095, 9061, 9061]
2025-08-05 10:28:53,688 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64)]
2025-08-05 10:28:53,692 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,692 - ExploitationExpert - INFO - populations: [{'tour': array([25,  7, 22,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19], dtype=int64), 'cur_cost': 62672.0}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14322.0}, {'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9654.0}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 37217.0}, {'tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9644.0}, {'tour': array([14, 21, 26, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12], dtype=int64), 'cur_cost': 60314.0}, {'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12441.0}, {'tour': array([33, 34, 10, 28, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8], dtype=int64), 'cur_cost': 55751.0}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9381.0}]
2025-08-05 10:28:53,694 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,695 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 215, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 215, 'cache_hits': 0, 'similarity_calculations': 987, 'cache_hit_rate': 0.0, 'cache_size': 987}}
2025-08-05 10:28:53,696 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([33, 34, 10, 28, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8], dtype=int64), 'cur_cost': 55751.0, 'intermediate_solutions': [{'tour': array([17, 11, 18,  1, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 17, 11, 18, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  1, 17, 11, 18,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 54696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18,  1, 17, 11, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 54724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 14,  1, 17, 11,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,696 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 55751.00)
2025-08-05 10:28:53,696 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:53,697 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:53,697 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:53,698 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:53,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,699 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,700 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:53,700 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12609.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:53,700 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12609.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 34, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 19, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 20196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 32, 27, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 29, 33, 26], 'cur_cost': 15433.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 20, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14783.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:53,701 - experts.management.collaboration_manager - INFO - 个体 9 保留原路径 (成本: 12609.00)
2025-08-05 10:28:53,701 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:53,701 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:53,704 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([25,  7, 22,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19], dtype=int64), 'cur_cost': 62672.0, 'intermediate_solutions': [{'tour': array([21, 26, 15, 25, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63572.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([25, 21, 26, 15, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 60936.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([13, 25, 21, 26, 15, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63614.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([15, 25, 21, 26, 13, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 63659.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([15, 13, 25, 21, 26, 31,  6, 34, 33,  4, 30, 23, 22,  9, 18,  1, 19,
       32, 27, 11, 10, 29, 24, 16,  0, 17,  3, 28, 12,  5,  2, 14, 20,  8,
        7]), 'cur_cost': 57687.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14322.0, 'intermediate_solutions': [{'tour': [9, 13, 7, 11, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 17, 16, 26, 23], 'cur_cost': 51365.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 29, 5, 12, 30, 0, 26, 16, 11, 23], 'cur_cost': 53217.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 13, 7, 17, 2, 15, 10, 3, 1, 29, 6, 4, 20, 14, 18, 27, 31, 8, 34, 25, 21, 22, 33, 32, 19, 24, 28, 5, 12, 30, 0, 11, 16, 26, 23], 'cur_cost': 56988.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9654.0, 'intermediate_solutions': [{'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 25, 15, 8, 10, 12, 30, 9, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 26358.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 21, 19, 18, 13, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14319.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 13, 18, 19, 21, 17, 23, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 14, 9, 15, 8, 10, 12, 30, 25, 34, 28, 31, 27, 32, 29, 11, 33, 26], 'cur_cost': 20327.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 37217.0, 'intermediate_solutions': [{'tour': [8, 0, 5, 3, 18, 10, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 32753.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 9, 21, 13, 7, 12, 24, 32, 25, 26, 28, 31, 27, 34, 29, 33, 30, 19, 22, 23, 16, 1, 15], 'cur_cost': 35129.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 10, 5, 3, 18, 0, 11, 17, 4, 14, 6, 2, 20, 21, 13, 7, 12, 30, 33, 29, 34, 27, 31, 28, 26, 9, 25, 32, 24, 19, 22, 23, 16, 1, 15], 'cur_cost': 34368.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0, 'intermediate_solutions': [{'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 32, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 14, 29, 33, 26], 'cur_cost': 21762.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 20, 21, 19, 18, 17, 23, 16, 22, 24, 1, 7, 6, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9724.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 20, 19, 18, 17, 23, 16, 22, 24, 6, 7, 1, 4, 3, 5, 11, 14, 9, 15, 8, 10, 13, 21, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14252.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9644.0, 'intermediate_solutions': [{'tour': [20, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 19, 18, 0, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16214.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 23, 17, 21, 11, 24, 22, 20, 18, 19, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16400.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 16, 1, 2, 6, 4, 3, 5, 17, 21, 19, 18, 20, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 23, 27, 32, 29, 33, 26], 'cur_cost': 17214.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 21, 26, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12], dtype=int64), 'cur_cost': 60314.0, 'intermediate_solutions': [{'tour': array([19, 20,  0,  2, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69184.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 2, 19, 20,  0, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69590.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  2, 19, 20,  0, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 66942.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 0,  2, 19, 20, 14, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 69178.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 0, 14,  2, 19, 20, 17,  4, 18, 34,  9, 31,  1, 24, 27,  7, 11,  3,
       13, 16, 26, 12,  8, 32,  6, 30,  5, 25, 21, 29, 22, 33, 23, 28, 15,
       10]), 'cur_cost': 67587.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12441.0, 'intermediate_solutions': [{'tour': [0, 2, 13, 5, 3, 34, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 12, 30, 25, 1, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19683.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 2, 13, 31, 28, 34, 25, 30, 12, 10, 8, 15, 9, 14, 11, 24, 22, 16, 20, 18, 19, 21, 17, 23, 4, 6, 7, 1, 3, 5, 27, 32, 29, 33, 26], 'cur_cost': 15750.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 2, 13, 5, 3, 1, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 25, 9, 15, 8, 10, 12, 30, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 18307.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([33, 34, 10, 28, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8], dtype=int64), 'cur_cost': 55751.0, 'intermediate_solutions': [{'tour': array([17, 11, 18,  1, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57384.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 17, 11, 18, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57378.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([14,  1, 17, 11, 18,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 54696.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([18,  1, 17, 11, 14,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 54724.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([18, 14,  1, 17, 11,  2,  5, 25, 33, 28, 12,  7, 34,  9, 20,  8,  6,
        4, 26, 27, 13,  0, 31,  3, 19, 30, 32, 10, 24, 21, 22, 16, 29, 23,
       15]), 'cur_cost': 57388.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12609.0, 'intermediate_solutions': [{'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 34, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 19, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 20196.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 32, 27, 31, 28, 34, 25, 30, 12, 13, 10, 8, 15, 9, 29, 33, 26], 'cur_cost': 15433.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 1, 6, 7, 2, 4, 3, 23, 17, 21, 19, 18, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 12, 30, 20, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14783.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:53,704 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:53,704 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,707 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9644.000, 多样性=0.841
2025-08-05 10:28:53,707 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-08-05 10:28:53,708 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment (Algorithm-based) ---
2025-08-05 10:28:53,708 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:53,709 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 2, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.07172268424880758, 'best_improvement': -0.028035390683296024}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.019245003700962205}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'stable', 'trend_strength': 3.963757812433059e-05, 'recent_improvements': [0.02536982243921987, 0.032316697141702155, 0.025290547282971216], 'convergence_status': 'converged'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7698412698412698, 'new_diversity': 0.7698412698412698, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化', '算法可能已收敛，建议考虑早停或增加扰动重启', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:53,709 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-08-05 10:28:53,709 - __main__ - INFO - composite5_35 开始进化第 4 代
2025-08-05 10:28:53,710 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-08-05 10:28:53,710 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:53,711 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9644.000, 多样性=0.841
2025-08-05 10:28:53,711 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:53,713 - PathExpert - INFO - 路径结构分析完成: 公共边数量=15, 路径相似性=0.841
2025-08-05 10:28:53,713 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:53,717 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.770
2025-08-05 10:28:53,719 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 3/5)
2025-08-05 10:28:53,720 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:53,720 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 10:28:53,720 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 10:28:53,761 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.316, 适应度梯度: -5104.653, 聚类评分: 0.000, 覆盖率: 0.101, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:53,761 - LandscapeExpert - INFO - 开始更新可视化 (迭代 3)
2025-08-05 10:28:53,761 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:53,761 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 10:28:53,765 - visualization.landscape_visualizer - INFO - 插值约束: 187 个点被约束到最小值 9061.00
2025-08-05 10:28:53,767 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=8.8%, 梯度: 1951.03 → 1778.41
2025-08-05 10:28:53,874 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_84_20250805_102853.html
2025-08-05 10:28:53,971 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_84_20250805_102853.html
2025-08-05 10:28:53,971 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 84
2025-08-05 10:28:53,972 - LandscapeExpert - INFO - 可视化已更新 (迭代 3)
2025-08-05 10:28:53,972 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2531秒
2025-08-05 10:28:53,972 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3157894736842105, 'modality': 'bi-modal', 'deceptiveness': 'high', 'gradient_strength': -5104.652631578947, 'local_optima_density': 0.3157894736842105, 'gradient_variance': 295165358.0877562, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1005, 'fitness_entropy': 0.529138521613167, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -5104.653)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.101)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'exploitation', 'evolution_direction': {'recommended_focus': 'exploitation', 'operators': ['local_search', 'hill_climbing'], 'parameters': {'local_search_intensity': 0.8, 'hill_climbing_steps': 100}}, 'iteration_info': {'current': 3, 'total': 5, 'progress': 0.6}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360933.761473, 'performance_metrics': {}}}
2025-08-05 10:28:53,972 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:53,972 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:53,973 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:53,973 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:53,973 - StrategyExpert - INFO - 智能策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,973 - StrategyExpert - INFO - 自适应探索比例: 0.754
2025-08-05 10:28:53,974 - experts.management.collaboration_manager - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,974 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,974 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:53,974 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-08-05 10:28:53,974 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.754
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:53,975 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:53,975 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:53,975 - experts.management.collaboration_manager - INFO - 为个体 0 生成利用路径
2025-08-05 10:28:53,975 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:53,975 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:53,976 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 53169.0
2025-08-05 10:28:53,992 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:53,992 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:53,992 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:53,996 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:53,997 - ExploitationExpert - INFO - populations: [{'tour': array([ 8, 12,  2,  0, 31, 30, 10, 26, 13,  7, 29, 34, 15, 21, 11, 20, 18,
       32, 19, 25,  6, 23, 24, 17,  5, 33, 28, 22, 16,  4,  3, 14,  9, 27,
        1], dtype=int64), 'cur_cost': 53169.0}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14322.0}, {'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9654.0}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 37217.0}, {'tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11726.0}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9644.0}, {'tour': [14, 21, 26, 17, 9, 11, 2, 32, 3, 18, 23, 24, 28, 30, 33, 13, 22, 27, 19, 34, 8, 25, 10, 4, 6, 29, 20, 0, 31, 15, 16, 1, 7, 5, 12], 'cur_cost': 60314.0}, {'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12441.0}, {'tour': [33, 34, 10, 28, 27, 25, 12, 23, 7, 17, 15, 4, 5, 24, 26, 16, 29, 22, 20, 3, 30, 31, 0, 6, 19, 14, 11, 32, 21, 1, 18, 13, 9, 2, 8], 'cur_cost': 55751.0}, {'tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12609.0}]
2025-08-05 10:28:53,998 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:53,998 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 216, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 216, 'cache_hits': 0, 'similarity_calculations': 994, 'cache_hit_rate': 0.0, 'cache_size': 994}}
2025-08-05 10:28:53,999 - experts.management.collaboration_manager - INFO - 个体 0 利用路径生成报告: {'new_tour': array([ 8, 12,  2,  0, 31, 30, 10, 26, 13,  7, 29, 34, 15, 21, 11, 20, 18,
       32, 19, 25,  6, 23, 24, 17,  5, 33, 28, 22, 16,  4,  3, 14,  9, 27,
        1], dtype=int64), 'cur_cost': 53169.0, 'intermediate_solutions': [{'tour': array([22,  7, 25,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 60653.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 22,  7, 25,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 63027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  1, 22,  7, 25, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 59362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25,  1, 22,  7,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 62704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25,  3,  1, 22,  7, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 62674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:53,999 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 53169.00)
2025-08-05 10:28:53,999 - experts.management.collaboration_manager - INFO - 为个体 1 生成探索路径
2025-08-05 10:28:53,999 - ExplorationExpert - INFO - 开始为个体 1 生成探索路径（算法实现）
2025-08-05 10:28:53,999 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,001 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:54,001 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,001 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,002 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65235.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,002 - experts.management.collaboration_manager - INFO - 个体 1 探索路径生成报告: {'new_tour': [3, 18, 20, 21, 17, 6, 30, 5, 4, 23, 27, 9, 31, 12, 1, 19, 26, 10, 34, 11, 2, 15, 25, 8, 29, 32, 0, 16, 24, 33, 14, 7, 13, 22, 28], 'cur_cost': 65235.0, 'intermediate_solutions': [{'tour': [0, 23, 14, 9, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 19, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 27, 31, 32, 29, 33, 26], 'cur_cost': 14322.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 29, 27, 32, 33, 26], 'cur_cost': 14361.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,003 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 65235.00)
2025-08-05 10:28:54,003 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:54,003 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:54,003 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,005 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:54,005 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,006 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11696.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,007 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11696.0, 'intermediate_solutions': [{'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 21, 5, 1, 7, 6, 4, 2, 23, 17, 12, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16403.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 15, 13, 10, 11, 14, 22, 16, 20, 18, 19, 21, 17, 23, 2, 4, 6, 7, 1, 5, 12, 8, 9, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14411.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 27, 31, 32, 29, 33, 26], 'cur_cost': 9654.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,007 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 11696.00)
2025-08-05 10:28:54,007 - experts.management.collaboration_manager - INFO - 为个体 3 生成探索路径
2025-08-05 10:28:54,007 - ExplorationExpert - INFO - 开始为个体 3 生成探索路径（算法实现）
2025-08-05 10:28:54,007 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,009 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:54,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,009 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,010 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54150.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,010 - experts.management.collaboration_manager - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 18, 9, 7, 10, 15, 27, 23, 20, 19, 26, 34, 1, 14, 8, 29, 0, 12, 2, 28, 22, 30, 6, 24, 11, 13, 33, 32, 16, 17, 21, 31, 5, 25, 4], 'cur_cost': 54150.0, 'intermediate_solutions': [{'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 29, 33, 24], 'cur_cost': 35155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 21, 25, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 42605.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 2, 14, 19, 30, 27, 26, 28, 31, 16, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 40258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,010 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 54150.00)
2025-08-05 10:28:54,011 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:54,011 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:54,011 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,014 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:54,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,015 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,016 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36176.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,016 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 12, 6, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 14, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 30, 24, 22, 16, 18, 21, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 12, 14, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 9, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16433.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,017 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 36176.00)
2025-08-05 10:28:54,017 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:54,017 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:54,017 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,019 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:54,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,019 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,020 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,020 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9639.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,020 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0, 'intermediate_solutions': [{'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 16, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 3, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14178.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 25, 30, 24, 22, 16, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14986.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 24, 30, 25, 22, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14965.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,022 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 9639.00)
2025-08-05 10:28:54,022 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:54,022 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,022 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,023 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 63597.0
2025-08-05 10:28:54,039 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:54,039 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:54,039 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:54,042 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,042 - ExploitationExpert - INFO - populations: [{'tour': array([ 8, 12,  2,  0, 31, 30, 10, 26, 13,  7, 29, 34, 15, 21, 11, 20, 18,
       32, 19, 25,  6, 23, 24, 17,  5, 33, 28, 22, 16,  4,  3, 14,  9, 27,
        1], dtype=int64), 'cur_cost': 53169.0}, {'tour': [3, 18, 20, 21, 17, 6, 30, 5, 4, 23, 27, 9, 31, 12, 1, 19, 26, 10, 34, 11, 2, 15, 25, 8, 29, 32, 0, 16, 24, 33, 14, 7, 13, 22, 28], 'cur_cost': 65235.0}, {'tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11696.0}, {'tour': [3, 18, 9, 7, 10, 15, 27, 23, 20, 19, 26, 34, 1, 14, 8, 29, 0, 12, 2, 28, 22, 30, 6, 24, 11, 13, 33, 32, 16, 17, 21, 31, 5, 25, 4], 'cur_cost': 54150.0}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0}, {'tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0}, {'tour': array([12, 27,  1, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6], dtype=int64), 'cur_cost': 63597.0}, {'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12441.0}, {'tour': [33, 34, 10, 28, 27, 25, 12, 23, 7, 17, 15, 4, 5, 24, 26, 16, 29, 22, 20, 3, 30, 31, 0, 6, 19, 14, 11, 32, 21, 1, 18, 13, 9, 2, 8], 'cur_cost': 55751.0}, {'tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12609.0}]
2025-08-05 10:28:54,043 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:54,043 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 217, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 217, 'cache_hits': 0, 'similarity_calculations': 1002, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,044 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([12, 27,  1, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6], dtype=int64), 'cur_cost': 63597.0, 'intermediate_solutions': [{'tour': array([26, 21, 14, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 62885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 26, 21, 14,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 17, 26, 21, 14, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60306.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 17, 26, 21,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  9, 17, 26, 21, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60262.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,044 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 63597.00)
2025-08-05 10:28:54,044 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:54,044 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:54,044 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,047 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:54,047 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,048 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,049 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30536.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,049 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0, 'intermediate_solutions': [{'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 33, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 19, 26], 'cur_cost': 23344.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 34, 25, 30, 13, 10, 8, 15, 9, 14, 11, 24, 22, 16, 20, 18, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17874.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 12, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 2, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12778.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,049 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 30536.00)
2025-08-05 10:28:54,049 - experts.management.collaboration_manager - INFO - 为个体 8 生成利用路径
2025-08-05 10:28:54,050 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,050 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,050 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 54008.0
2025-08-05 10:28:54,061 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:54,062 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:54,062 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:54,065 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,065 - ExploitationExpert - INFO - populations: [{'tour': array([ 8, 12,  2,  0, 31, 30, 10, 26, 13,  7, 29, 34, 15, 21, 11, 20, 18,
       32, 19, 25,  6, 23, 24, 17,  5, 33, 28, 22, 16,  4,  3, 14,  9, 27,
        1], dtype=int64), 'cur_cost': 53169.0}, {'tour': [3, 18, 20, 21, 17, 6, 30, 5, 4, 23, 27, 9, 31, 12, 1, 19, 26, 10, 34, 11, 2, 15, 25, 8, 29, 32, 0, 16, 24, 33, 14, 7, 13, 22, 28], 'cur_cost': 65235.0}, {'tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11696.0}, {'tour': [3, 18, 9, 7, 10, 15, 27, 23, 20, 19, 26, 34, 1, 14, 8, 29, 0, 12, 2, 28, 22, 30, 6, 24, 11, 13, 33, 32, 16, 17, 21, 31, 5, 25, 4], 'cur_cost': 54150.0}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0}, {'tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0}, {'tour': array([12, 27,  1, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6], dtype=int64), 'cur_cost': 63597.0}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0}, {'tour': array([ 9, 19, 15, 27, 33, 20,  6,  8, 31, 21, 10, 26, 14,  5,  3,  0,  7,
       34, 29, 30,  2, 25, 28,  1, 16,  4, 22, 12, 32, 13, 11, 18, 24, 23,
       17], dtype=int64), 'cur_cost': 54008.0}, {'tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12609.0}]
2025-08-05 10:28:54,066 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:54,066 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 218, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 218, 'cache_hits': 0, 'similarity_calculations': 1011, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,067 - experts.management.collaboration_manager - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 9, 19, 15, 27, 33, 20,  6,  8, 31, 21, 10, 26, 14,  5,  3,  0,  7,
       34, 29, 30,  2, 25, 28,  1, 16,  4, 22, 12, 32, 13, 11, 18, 24, 23,
       17], dtype=int64), 'cur_cost': 54008.0, 'intermediate_solutions': [{'tour': array([10, 34, 33, 28, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 49778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 10, 34, 33, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 28, 10, 34, 33, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 28, 10, 34, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 27, 28, 10, 34, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,067 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 54008.00)
2025-08-05 10:28:54,067 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:54,067 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:54,067 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,068 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:54,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,069 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,069 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12467.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,069 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12467.0, 'intermediate_solutions': [{'tour': [0, 12, 1, 10, 11, 14, 9, 15, 30, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 8, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 21349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 22, 16, 20, 18, 19, 21, 17, 23, 4, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14890.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 1, 10, 11, 14, 16, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17350.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,070 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 12467.00)
2025-08-05 10:28:54,070 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:54,070 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:54,072 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 12,  2,  0, 31, 30, 10, 26, 13,  7, 29, 34, 15, 21, 11, 20, 18,
       32, 19, 25,  6, 23, 24, 17,  5, 33, 28, 22, 16,  4,  3, 14,  9, 27,
        1], dtype=int64), 'cur_cost': 53169.0, 'intermediate_solutions': [{'tour': array([22,  7, 25,  1,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 60653.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 1, 22,  7, 25,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 63027.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 3,  1, 22,  7, 25, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 59362.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([25,  1, 22,  7,  3, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 62704.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([25,  3,  1, 22,  7, 33,  0, 27, 12, 17, 26,  9, 10, 20,  4, 23, 24,
       32, 34, 14, 16, 13, 18,  2, 21, 30, 11,  6, 31,  5, 28, 29, 15,  8,
       19]), 'cur_cost': 62674.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [3, 18, 20, 21, 17, 6, 30, 5, 4, 23, 27, 9, 31, 12, 1, 19, 26, 10, 34, 11, 2, 15, 25, 8, 29, 32, 0, 16, 24, 33, 14, 7, 13, 22, 28], 'cur_cost': 65235.0, 'intermediate_solutions': [{'tour': [0, 23, 14, 9, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 19, 15, 8, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19013.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 27, 31, 32, 29, 33, 26], 'cur_cost': 14322.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 23, 14, 19, 18, 20, 21, 17, 16, 22, 24, 6, 7, 1, 2, 5, 3, 4, 11, 10, 13, 9, 15, 8, 12, 30, 25, 34, 28, 31, 29, 27, 32, 33, 26], 'cur_cost': 14361.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11696.0, 'intermediate_solutions': [{'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 21, 5, 1, 7, 6, 4, 2, 23, 17, 12, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16403.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 3, 15, 13, 10, 11, 14, 22, 16, 20, 18, 19, 21, 17, 23, 2, 4, 6, 7, 1, 5, 12, 8, 9, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14411.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 3, 15, 13, 10, 11, 14, 9, 8, 12, 5, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 27, 31, 32, 29, 33, 26], 'cur_cost': 9654.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 18, 9, 7, 10, 15, 27, 23, 20, 19, 26, 34, 1, 14, 8, 29, 0, 12, 2, 28, 22, 30, 6, 24, 11, 13, 33, 32, 16, 17, 21, 31, 5, 25, 4], 'cur_cost': 54150.0, 'intermediate_solutions': [{'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 25, 21, 18, 15, 9, 8, 29, 33, 24], 'cur_cost': 35155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 16, 2, 14, 19, 30, 27, 26, 28, 31, 32, 34, 21, 25, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 42605.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [22, 4, 3, 11, 12, 6, 7, 10, 17, 0, 5, 23, 20, 13, 1, 2, 14, 19, 30, 27, 26, 28, 31, 16, 32, 34, 25, 21, 18, 15, 9, 8, 24, 33, 29], 'cur_cost': 40258.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0, 'intermediate_solutions': [{'tour': [0, 20, 19, 12, 6, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 14, 4, 2, 23, 17, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17994.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 20, 19, 12, 14, 9, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 30, 24, 22, 16, 18, 21, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17070.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 20, 19, 12, 14, 15, 8, 10, 11, 13, 3, 5, 1, 7, 6, 4, 2, 23, 17, 9, 21, 18, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 16433.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0, 'intermediate_solutions': [{'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 16, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 3, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14178.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 25, 30, 24, 22, 16, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14986.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 4, 11, 15, 9, 14, 10, 13, 8, 12, 3, 5, 1, 7, 6, 2, 23, 17, 21, 19, 18, 20, 16, 24, 30, 25, 22, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14965.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([12, 27,  1, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6], dtype=int64), 'cur_cost': 63597.0, 'intermediate_solutions': [{'tour': array([26, 21, 14, 17,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 62885.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([17, 26, 21, 14,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60279.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 17, 26, 21, 14, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60306.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([14, 17, 26, 21,  9, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60314.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([14,  9, 17, 26, 21, 11,  2, 32,  3, 18, 23, 24, 28, 30, 33, 13, 22,
       27, 19, 34,  8, 25, 10,  4,  6, 29, 20,  0, 31, 15, 16,  1,  7,  5,
       12]), 'cur_cost': 60262.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0, 'intermediate_solutions': [{'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 33, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 30, 25, 34, 28, 31, 27, 32, 29, 19, 26], 'cur_cost': 23344.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 7, 12, 2, 1, 4, 6, 5, 3, 23, 17, 21, 19, 34, 25, 30, 13, 10, 8, 15, 9, 14, 11, 24, 22, 16, 20, 18, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17874.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 7, 12, 1, 4, 6, 5, 3, 23, 17, 21, 19, 18, 20, 16, 22, 24, 11, 14, 9, 15, 8, 10, 13, 2, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12778.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 9, 19, 15, 27, 33, 20,  6,  8, 31, 21, 10, 26, 14,  5,  3,  0,  7,
       34, 29, 30,  2, 25, 28,  1, 16,  4, 22, 12, 32, 13, 11, 18, 24, 23,
       17], dtype=int64), 'cur_cost': 54008.0, 'intermediate_solutions': [{'tour': array([10, 34, 33, 28, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 49778.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([28, 10, 34, 33, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55823.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([27, 28, 10, 34, 33, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55811.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([33, 28, 10, 34, 27, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55767.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([33, 27, 28, 10, 34, 25, 12, 23,  7, 17, 15,  4,  5, 24, 26, 16, 29,
       22, 20,  3, 30, 31,  0,  6, 19, 14, 11, 32, 21,  1, 18, 13,  9,  2,
        8]), 'cur_cost': 55685.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12467.0, 'intermediate_solutions': [{'tour': [0, 12, 1, 10, 11, 14, 9, 15, 30, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 16, 22, 24, 8, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 21349.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 12, 1, 10, 11, 14, 9, 15, 8, 13, 3, 5, 2, 7, 6, 22, 16, 20, 18, 19, 21, 17, 23, 4, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14890.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 12, 1, 10, 11, 14, 16, 9, 15, 8, 13, 3, 5, 2, 7, 6, 4, 23, 17, 21, 19, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17350.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:54,072 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:54,072 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,074 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9639.000, 多样性=0.952
2025-08-05 10:28:54,074 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-08-05 10:28:54,074 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment (Algorithm-based) ---
2025-08-05 10:28:54,075 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:54,075 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 60, 'iteration': 3, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.13489896229426326, 'best_improvement': 0.0005184570717544588}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.13207547169811312}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'improving', 'trend_strength': 0.019702993553552723, 'recent_improvements': [0.032316697141702155, 0.025290547282971216, 0.07172268424880758], 'convergence_status': 'accelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7698412698412698, 'new_diversity': 0.7698412698412698, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:54,076 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-08-05 10:28:54,076 - __main__ - INFO - composite5_35 开始进化第 5 代
2025-08-05 10:28:54,076 - experts.management.collaboration_manager - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-08-05 10:28:54,077 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,077 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9639.000, 多样性=0.952
2025-08-05 10:28:54,077 - PathExpert - INFO - 开始路径结构分析
2025-08-05 10:28:54,079 - PathExpert - INFO - 路径结构分析完成: 公共边数量=1, 路径相似性=0.952
2025-08-05 10:28:54,079 - EliteExpert - INFO - 开始精英解分析
2025-08-05 10:28:54,080 - EliteExpert - INFO - 精英解分析完成: 精英解数量=9, 多样性=0.770
2025-08-05 10:28:54,082 - LandscapeExpert - INFO - 开始算法化景观分析 (迭代 4/5)
2025-08-05 10:28:54,082 - LandscapeExpert - INFO - 使用直接传入的种群数据: 10个个体
2025-08-05 10:28:54,082 - LandscapeExpert - INFO - 添加精英解数据: 9个精英解
2025-08-05 10:28:54,082 - LandscapeExpert - INFO - 数据提取成功: 19个路径, 19个适应度值
2025-08-05 10:28:54,117 - LandscapeExpert - INFO - 景观特征提取 - 局部最优密度: 0.263, 适应度梯度: -7157.558, 聚类评分: 0.000, 覆盖率: 0.102, 收敛趋势: 0.000, 多样性: 1.000
2025-08-05 10:28:54,117 - LandscapeExpert - INFO - 开始更新可视化 (迭代 4)
2025-08-05 10:28:54,117 - LandscapeExpert - INFO - 提取到 5 个精英解
2025-08-05 10:28:54,117 - visualization.landscape_visualizer - INFO - 设置当前实例名: composite5_35
2025-08-05 10:28:54,122 - visualization.landscape_visualizer - INFO - 插值约束: 49 个点被约束到最小值 9061.00
2025-08-05 10:28:54,123 - visualization.landscape_visualizer - INFO - 表面平滑完成: 方法=gaussian_filter, 平滑度改善=11.4%, 梯度: 2228.22 → 1973.30
2025-08-05 10:28:54,230 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\landscape_composite5_35_iter_85_20250805_102854.html
2025-08-05 10:28:54,284 - visualization.landscape_visualizer - INFO - 图表已保存到实例文件夹: visualization_output\instance_composite5_35\dashboard_composite5_35_iter_85_20250805_102854.html
2025-08-05 10:28:54,284 - visualization.landscape_visualizer - INFO - 可视化已更新，当前迭代: 85
2025-08-05 10:28:54,285 - LandscapeExpert - INFO - 可视化已更新 (迭代 4)
2025-08-05 10:28:54,285 - LandscapeExpert - INFO - 算法化景观分析完成，用时: 0.2030秒
2025-08-05 10:28:54,285 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.2631578947368421, 'modality': 'uni-modal', 'deceptiveness': 'high', 'gradient_strength': -7157.557894736844, 'local_optima_density': 0.2631578947368421, 'gradient_variance': 332566729.5519115, 'cluster_count': 0}, 'population_state': {'diversity': 1.0, 'convergence': 0.0, 'clustering': 0.0, 'coverage': 0.1015, 'fitness_entropy': 0.685972951096462, 'coverage_uniformity': 0.5, 'convergence_rate': 0.0}, 'difficult_regions': [{'type': 'flat_landscape', 'severity': 'medium', 'description': '平坦景观区域 (梯度强度: -7157.558)', 'recommendation': '使用大步长搜索'}, {'type': 'poor_clustering', 'severity': 'medium', 'description': '聚类质量差 (轮廓系数: 0.000)', 'recommendation': '增加种群多样性'}], 'opportunity_regions': [{'type': 'unexplored_space', 'potential': 'high', 'description': '未充分探索的搜索空间 (覆盖率: 0.102)', 'recommendation': '增加探索性操作'}, {'type': 'diverse_population', 'potential': 'medium', 'description': '高多样性种群区域 (多样性指数: 1.000)', 'recommendation': '平衡探索与利用'}], 'evolution_phase': 'convergence', 'evolution_direction': {'recommended_focus': 'intensification', 'operators': ['fine_tuning', 'local_optimization'], 'parameters': {'fine_tuning_precision': 0.9, 'local_optimization_depth': 50}}, 'iteration_info': {'current': 4, 'total': 5, 'progress': 0.8}, 'analysis_metadata': {'algorithm': 'algorithmic_landscape_analysis', 'version': '2.0', 'timestamp': 1754360934.117696, 'performance_metrics': {}}}
2025-08-05 10:28:54,285 - experts.management.collaboration_manager - INFO - 开始策略分配阶段
2025-08-05 10:28:54,286 - StrategyExpert - INFO - 开始策略分配分析
2025-08-05 10:28:54,286 - StrategyExpert - INFO - 景观特征考虑: 0项特征
2025-08-05 10:28:54,286 - StrategyExpert - INFO - 景观复杂度: 0.300
2025-08-05 10:28:54,287 - StrategyExpert - INFO - 智能策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:54,287 - StrategyExpert - INFO - 自适应探索比例: 0.722
2025-08-05 10:28:54,287 - experts.management.collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:54,287 - experts.management.collaboration_manager - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,287 - experts.management.collaboration_manager - INFO - 策略分配阶段完成
2025-08-05 10:28:54,287 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'explore']
2025-08-05 10:28:54,288 - __main__ - INFO - 策略分配完整报告: 策略分配结果：
- 动态探索比例: 0.722
- 探索个体数量: 7
- 利用个体数量: 3
- 分配依据: 基于适应度排名和多样性贡献的综合评分
- 景观特征考虑: 1项特征
2025-08-05 10:28:54,288 - experts.management.collaboration_manager - INFO - 开始进化阶段
2025-08-05 10:28:54,288 - experts.management.collaboration_manager - INFO - 识别精英个体: {2, 5} (总数: 2, 保护比例: 0.20)
2025-08-05 10:28:54,288 - experts.management.collaboration_manager - INFO - 为个体 0 生成探索路径
2025-08-05 10:28:54,289 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-08-05 10:28:54,289 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,290 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:54,290 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,291 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,292 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51341.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,292 - experts.management.collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [15, 12, 19, 21, 31, 30, 27, 26, 4, 7, 29, 9, 5, 14, 1, 20, 18, 10, 34, 11, 6, 23, 25, 17, 33, 32, 28, 22, 24, 13, 3, 8, 2, 16, 0], 'cur_cost': 51341.0, 'intermediate_solutions': [{'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 25, 15, 21, 11, 20, 18, 32, 19, 34, 6, 23, 24, 17, 5, 33, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 53155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 34, 20, 11, 21, 15, 18, 32, 19, 25, 6, 23, 24, 17, 5, 33, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 55259.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 34, 15, 21, 11, 18, 32, 19, 25, 6, 23, 24, 17, 5, 33, 20, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 58609.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,292 - experts.management.collaboration_manager - INFO - 个体 0 接受新路径 (成本: 51341.00)
2025-08-05 10:28:54,293 - experts.management.collaboration_manager - INFO - 为个体 1 生成利用路径
2025-08-05 10:28:54,293 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,293 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,293 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 67790.0
2025-08-05 10:28:54,309 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:54,309 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:54,309 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:54,314 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,314 - ExploitationExpert - INFO - populations: [{'tour': [15, 12, 19, 21, 31, 30, 27, 26, 4, 7, 29, 9, 5, 14, 1, 20, 18, 10, 34, 11, 6, 23, 25, 17, 33, 32, 28, 22, 24, 13, 3, 8, 2, 16, 0], 'cur_cost': 51341.0}, {'tour': array([19, 13, 18,  4, 14,  0, 20,  6, 16, 21,  5,  9, 17, 11,  2, 32, 26,
       23, 27, 28, 24, 34,  7,  1, 15, 31, 22,  8, 30, 12, 25,  3, 29, 10,
       33], dtype=int64), 'cur_cost': 67790.0}, {'tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11696.0}, {'tour': [3, 18, 9, 7, 10, 15, 27, 23, 20, 19, 26, 34, 1, 14, 8, 29, 0, 12, 2, 28, 22, 30, 6, 24, 11, 13, 33, 32, 16, 17, 21, 31, 5, 25, 4], 'cur_cost': 54150.0}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0}, {'tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0}, {'tour': [12, 27, 1, 33, 9, 3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24, 23, 7, 15, 34, 10, 17, 21, 0, 5, 4, 2, 16, 19, 32, 30, 8, 22, 6], 'cur_cost': 63597.0}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0}, {'tour': [9, 19, 15, 27, 33, 20, 6, 8, 31, 21, 10, 26, 14, 5, 3, 0, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 54008.0}, {'tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12467.0}]
2025-08-05 10:28:54,315 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒，最大迭代次数: 10
2025-08-05 10:28:54,315 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 219, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 219, 'cache_hits': 0, 'similarity_calculations': 1021, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,316 - experts.management.collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([19, 13, 18,  4, 14,  0, 20,  6, 16, 21,  5,  9, 17, 11,  2, 32, 26,
       23, 27, 28, 24, 34,  7,  1, 15, 31, 22,  8, 30, 12, 25,  3, 29, 10,
       33], dtype=int64), 'cur_cost': 67790.0, 'intermediate_solutions': [{'tour': array([20, 18,  3, 21, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 67259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 20, 18,  3, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 67239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 21, 20, 18,  3,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 64964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 21, 20, 18, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 65219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 17, 21, 20, 18,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 65234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,317 - experts.management.collaboration_manager - INFO - 个体 1 接受新路径 (成本: 67790.00)
2025-08-05 10:28:54,317 - experts.management.collaboration_manager - INFO - 为个体 2 生成探索路径
2025-08-05 10:28:54,317 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-08-05 10:28:54,317 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,322 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:54,322 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,323 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35453.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,324 - experts.management.collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 2, 1, 24, 13, 3, 4, 6, 12, 16, 10, 14, 0, 15, 11, 8, 5, 19, 17, 7, 23, 25, 28, 26, 34, 31, 33, 27, 30, 29, 22, 20, 21, 9, 32], 'cur_cost': 35453.0, 'intermediate_solutions': [{'tour': [0, 16, 19, 9, 14, 11, 4, 13, 8, 15, 12, 3, 5, 1, 7, 6, 10, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 31, 28, 34, 25, 30, 27, 32, 29, 33, 26], 'cur_cost': 11834.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 30, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,324 - experts.management.collaboration_manager - INFO - 个体 2 保留原路径 (成本: 35453.00)
2025-08-05 10:28:54,325 - experts.management.collaboration_manager - INFO - 为个体 3 生成利用路径
2025-08-05 10:28:54,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,325 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 55159.0
2025-08-05 10:28:54,344 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:54,344 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:54,344 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:54,348 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,348 - ExploitationExpert - INFO - populations: [{'tour': [15, 12, 19, 21, 31, 30, 27, 26, 4, 7, 29, 9, 5, 14, 1, 20, 18, 10, 34, 11, 6, 23, 25, 17, 33, 32, 28, 22, 24, 13, 3, 8, 2, 16, 0], 'cur_cost': 51341.0}, {'tour': array([19, 13, 18,  4, 14,  0, 20,  6, 16, 21,  5,  9, 17, 11,  2, 32, 26,
       23, 27, 28, 24, 34,  7,  1, 15, 31, 22,  8, 30, 12, 25,  3, 29, 10,
       33], dtype=int64), 'cur_cost': 67790.0}, {'tour': [18, 2, 1, 24, 13, 3, 4, 6, 12, 16, 10, 14, 0, 15, 11, 8, 5, 19, 17, 7, 23, 25, 28, 26, 34, 31, 33, 27, 30, 29, 22, 20, 21, 9, 32], 'cur_cost': 35453.0}, {'tour': array([ 0, 33, 10, 26, 12, 34, 32, 16, 24, 25, 11, 15,  1, 19,  3, 30, 14,
       17, 18,  8, 23, 13,  4,  7,  5, 20, 22, 21, 27, 31, 28,  6, 29,  9,
        2], dtype=int64), 'cur_cost': 55159.0}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 28, 27, 29, 6, 22], 'cur_cost': 36176.0}, {'tour': [0, 5, 10, 9, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9639.0}, {'tour': [12, 27, 1, 33, 9, 3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24, 23, 7, 15, 34, 10, 17, 21, 0, 5, 4, 2, 16, 19, 32, 30, 8, 22, 6], 'cur_cost': 63597.0}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0}, {'tour': [9, 19, 15, 27, 33, 20, 6, 8, 31, 21, 10, 26, 14, 5, 3, 0, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 54008.0}, {'tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12467.0}]
2025-08-05 10:28:54,350 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:54,350 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 220, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 220, 'cache_hits': 0, 'similarity_calculations': 1032, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,351 - experts.management.collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 0, 33, 10, 26, 12, 34, 32, 16, 24, 25, 11, 15,  1, 19,  3, 30, 14,
       17, 18,  8, 23, 13,  4,  7,  5, 20, 22, 21, 27, 31, 28,  6, 29,  9,
        2], dtype=int64), 'cur_cost': 55159.0, 'intermediate_solutions': [{'tour': array([ 9, 18,  3,  7, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 54147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  9, 18,  3, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 54049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  7,  9, 18,  3, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 57130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  7,  9, 18, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 53745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 10,  7,  9, 18, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 56751.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,351 - experts.management.collaboration_manager - INFO - 个体 3 接受新路径 (成本: 55159.00)
2025-08-05 10:28:54,351 - experts.management.collaboration_manager - INFO - 为个体 4 生成探索路径
2025-08-05 10:28:54,352 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-08-05 10:28:54,352 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,356 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:54,357 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,357 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,358 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32090.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,358 - experts.management.collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [20, 1, 7, 16, 17, 23, 6, 10, 11, 14, 9, 2, 22, 12, 13, 19, 3, 8, 0, 21, 4, 24, 5, 26, 25, 34, 28, 27, 31, 33, 32, 30, 29, 18, 15], 'cur_cost': 32090.0, 'intermediate_solutions': [{'tour': [15, 3, 2, 1, 24, 20, 5, 28, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 10, 27, 29, 6, 22], 'cur_cost': 45576.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 28, 30, 13, 21, 18, 7, 33, 31, 32, 34, 26, 25, 4, 9, 16, 19, 14, 11, 0, 12, 23, 17, 8, 27, 29, 6, 22], 'cur_cost': 42236.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 27, 21, 13, 30, 28, 29, 6, 22], 'cur_cost': 41684.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,359 - experts.management.collaboration_manager - INFO - 个体 4 接受新路径 (成本: 32090.00)
2025-08-05 10:28:54,359 - experts.management.collaboration_manager - INFO - 为个体 5 生成探索路径
2025-08-05 10:28:54,359 - ExplorationExpert - INFO - 开始为个体 5 生成探索路径（算法实现）
2025-08-05 10:28:54,359 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,363 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 35
2025-08-05 10:28:54,363 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,364 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28962.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,365 - experts.management.collaboration_manager - INFO - 个体 5 探索路径生成报告: {'new_tour': [25, 33, 3, 5, 23, 18, 1, 6, 22, 20, 10, 7, 12, 0, 11, 9, 15, 21, 19, 16, 4, 24, 17, 30, 26, 34, 31, 28, 29, 2, 14, 13, 8, 32, 27], 'cur_cost': 28962.0, 'intermediate_solutions': [{'tour': [13, 5, 10, 9, 14, 11, 12, 15, 8, 0, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12350.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 23, 2, 4, 6, 7, 1, 3, 13, 8, 15, 12, 11, 14, 9, 10, 5, 0, 32, 29, 33, 26], 'cur_cost': 9701.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 9, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,365 - experts.management.collaboration_manager - INFO - 个体 5 保留原路径 (成本: 28962.00)
2025-08-05 10:28:54,365 - experts.management.collaboration_manager - INFO - 为个体 6 生成利用路径
2025-08-05 10:28:54,365 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-08-05 10:28:54,365 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-08-05 10:28:54,366 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 54077.0
2025-08-05 10:28:54,384 - ExploitationExpert - INFO - res_population_num: 9
2025-08-05 10:28:54,385 - ExploitationExpert - INFO - res_population_costs: [9061, 9061, 9061.0, 9061, 9061, 9090, 9090, 9095.0, 9095]
2025-08-05 10:28:54,385 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  3, 12, 15,  8,  9, 14, 11, 10, 13, 23, 17, 18, 20, 19, 21,
       16, 22, 24, 34, 28, 31, 27, 32, 29, 33, 26, 25, 30,  2,  7,  6,  4,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 30, 26,
       25, 34, 28, 31, 27, 32, 29, 33, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 27, 31, 28,
       26, 25, 34, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  4,  6,  7,  2, 30, 25, 26, 33, 29, 32, 31, 27, 28, 34, 24,
       22, 16, 21, 19, 20, 18, 17, 23, 13, 10, 11, 14,  9,  8, 15, 12,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 33, 29, 32, 31, 27, 28,
       34, 25, 26, 30, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  1,  2,  7,  6,  4, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  4,  6,  7,  2,
        1], dtype=int64), array([ 0,  2,  7,  1,  4,  6, 23, 17, 18, 20, 19, 21, 16, 22, 24, 34, 28,
       27, 31, 32, 29, 33, 26, 25, 30, 12, 15,  8,  9, 14, 11, 10, 13,  3,
        5], dtype=int64), array([ 0,  5,  3, 13, 10, 11, 14,  9,  8, 15, 12, 30, 25, 26, 33, 29, 32,
       27, 31, 28, 34, 24, 22, 16, 21, 19, 20, 18, 17, 23,  6,  4,  1,  7,
        2], dtype=int64)]
2025-08-05 10:28:54,390 - ExploitationExpert - INFO - populations_num: 10
2025-08-05 10:28:54,390 - ExploitationExpert - INFO - populations: [{'tour': [15, 12, 19, 21, 31, 30, 27, 26, 4, 7, 29, 9, 5, 14, 1, 20, 18, 10, 34, 11, 6, 23, 25, 17, 33, 32, 28, 22, 24, 13, 3, 8, 2, 16, 0], 'cur_cost': 51341.0}, {'tour': array([19, 13, 18,  4, 14,  0, 20,  6, 16, 21,  5,  9, 17, 11,  2, 32, 26,
       23, 27, 28, 24, 34,  7,  1, 15, 31, 22,  8, 30, 12, 25,  3, 29, 10,
       33], dtype=int64), 'cur_cost': 67790.0}, {'tour': [18, 2, 1, 24, 13, 3, 4, 6, 12, 16, 10, 14, 0, 15, 11, 8, 5, 19, 17, 7, 23, 25, 28, 26, 34, 31, 33, 27, 30, 29, 22, 20, 21, 9, 32], 'cur_cost': 35453.0}, {'tour': array([ 0, 33, 10, 26, 12, 34, 32, 16, 24, 25, 11, 15,  1, 19,  3, 30, 14,
       17, 18,  8, 23, 13,  4,  7,  5, 20, 22, 21, 27, 31, 28,  6, 29,  9,
        2], dtype=int64), 'cur_cost': 55159.0}, {'tour': [20, 1, 7, 16, 17, 23, 6, 10, 11, 14, 9, 2, 22, 12, 13, 19, 3, 8, 0, 21, 4, 24, 5, 26, 25, 34, 28, 27, 31, 33, 32, 30, 29, 18, 15], 'cur_cost': 32090.0}, {'tour': [25, 33, 3, 5, 23, 18, 1, 6, 22, 20, 10, 7, 12, 0, 11, 9, 15, 21, 19, 16, 4, 24, 17, 30, 26, 34, 31, 28, 29, 2, 14, 13, 8, 32, 27], 'cur_cost': 28962.0}, {'tour': array([20,  0,  5,  1, 13, 26, 12, 17,  3,  6, 23,  4, 18,  9, 21,  8, 10,
       25, 15, 19, 16, 31, 27, 33, 29, 24,  7,  2, 30, 34, 32, 11, 28, 22,
       14], dtype=int64), 'cur_cost': 54077.0}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30536.0}, {'tour': [9, 19, 15, 27, 33, 20, 6, 8, 31, 21, 10, 26, 14, 5, 3, 0, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 54008.0}, {'tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12467.0}]
2025-08-05 10:28:54,392 - ExploitationExpert - INFO - 局部搜索耗时: 0.03秒，最大迭代次数: 10
2025-08-05 10:28:54,392 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 221, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 221, 'cache_hits': 0, 'similarity_calculations': 1044, 'cache_hit_rate': 0.0, 'cache_size': 1000}}
2025-08-05 10:28:54,393 - experts.management.collaboration_manager - INFO - 个体 6 利用路径生成报告: {'new_tour': array([20,  0,  5,  1, 13, 26, 12, 17,  3,  6, 23,  4, 18,  9, 21,  8, 10,
       25, 15, 19, 16, 31, 27, 33, 29, 24,  7,  2, 30, 34, 32, 11, 28, 22,
       14], dtype=int64), 'cur_cost': 54077.0, 'intermediate_solutions': [{'tour': array([ 1, 27, 12, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33,  1, 27, 12,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 60902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 33,  1, 27, 12,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 33,  1, 27,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  9, 33,  1, 27,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 61032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}
2025-08-05 10:28:54,393 - experts.management.collaboration_manager - INFO - 个体 6 接受新路径 (成本: 54077.00)
2025-08-05 10:28:54,394 - experts.management.collaboration_manager - INFO - 为个体 7 生成探索路径
2025-08-05 10:28:54,394 - ExplorationExpert - INFO - 开始为个体 7 生成探索路径（算法实现）
2025-08-05 10:28:54,394 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,396 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 35
2025-08-05 10:28:54,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,396 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,397 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57643.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,397 - experts.management.collaboration_manager - INFO - 个体 7 探索路径生成报告: {'new_tour': [12, 21, 3, 26, 7, 29, 2, 5, 15, 1, 18, 34, 11, 6, 23, 25, 30, 33, 31, 22, 28, 4, 8, 9, 13, 0, 16, 19, 27, 14, 20, 10, 32, 17, 24], 'cur_cost': 57643.0, 'intermediate_solutions': [{'tour': [20, 30, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 18, 28, 2, 14, 9, 13, 26], 'cur_cost': 39281.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 9, 14, 2, 28, 30, 33, 32, 27, 31, 29, 34, 25, 19, 0, 3, 24, 5, 8, 7, 13, 26], 'cur_cost': 30540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 5, 7, 8, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,398 - experts.management.collaboration_manager - INFO - 个体 7 接受新路径 (成本: 57643.00)
2025-08-05 10:28:54,398 - experts.management.collaboration_manager - INFO - 为个体 8 生成探索路径
2025-08-05 10:28:54,398 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-08-05 10:28:54,398 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,399 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:54,399 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,400 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11767.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,401 - experts.management.collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 24, 18, 4, 1, 7, 6, 2, 5, 3, 23, 17, 21, 19, 20, 16, 22, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11767.0, 'intermediate_solutions': [{'tour': [9, 19, 15, 27, 33, 20, 6, 8, 31, 21, 13, 26, 14, 5, 3, 0, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 10, 11, 18, 24, 23, 17], 'cur_cost': 53970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 19, 15, 27, 29, 34, 7, 0, 3, 5, 14, 26, 10, 21, 31, 8, 6, 20, 33, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 53977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 19, 15, 27, 0, 33, 20, 6, 8, 31, 21, 10, 26, 14, 5, 3, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 57756.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,401 - experts.management.collaboration_manager - INFO - 个体 8 接受新路径 (成本: 11767.00)
2025-08-05 10:28:54,401 - experts.management.collaboration_manager - INFO - 为个体 9 生成探索路径
2025-08-05 10:28:54,401 - ExplorationExpert - INFO - 开始为个体 9 生成探索路径（算法实现）
2025-08-05 10:28:54,401 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-08-05 10:28:54,402 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 35
2025-08-05 10:28:54,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,402 - ExplorationExpert - INFO - 计算路径成本
2025-08-05 10:28:54,403 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9645.0, 路径长度: 35, 收集中间解: 3
2025-08-05 10:28:54,403 - experts.management.collaboration_manager - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 6, 21, 19, 18, 20, 16, 22, 24, 23, 17, 4, 7, 2, 5, 3, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9645.0, 'intermediate_solutions': [{'tour': [0, 15, 18, 24, 22, 30, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 16, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 31, 28, 34, 25, 30, 12, 27, 32, 29, 33, 26], 'cur_cost': 18469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 24, 18, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12506.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}
2025-08-05 10:28:54,403 - experts.management.collaboration_manager - INFO - 个体 9 接受新路径 (成本: 9645.00)
2025-08-05 10:28:54,403 - experts.management.collaboration_manager - INFO - 适应度导向选择统计: {'accepted': 8, 'rejected': 2, 'elite_protected': 2}
2025-08-05 10:28:54,403 - experts.management.collaboration_manager - INFO - 接受率: 8/10 (80.0%)
2025-08-05 10:28:54,405 - experts.management.collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [15, 12, 19, 21, 31, 30, 27, 26, 4, 7, 29, 9, 5, 14, 1, 20, 18, 10, 34, 11, 6, 23, 25, 17, 33, 32, 28, 22, 24, 13, 3, 8, 2, 16, 0], 'cur_cost': 51341.0, 'intermediate_solutions': [{'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 25, 15, 21, 11, 20, 18, 32, 19, 34, 6, 23, 24, 17, 5, 33, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 53155.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 34, 20, 11, 21, 15, 18, 32, 19, 25, 6, 23, 24, 17, 5, 33, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 55259.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [8, 12, 2, 0, 31, 30, 10, 26, 13, 7, 29, 34, 15, 21, 11, 18, 32, 19, 25, 6, 23, 24, 17, 5, 33, 20, 28, 22, 16, 4, 3, 14, 9, 27, 1], 'cur_cost': 58609.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 13, 18,  4, 14,  0, 20,  6, 16, 21,  5,  9, 17, 11,  2, 32, 26,
       23, 27, 28, 24, 34,  7,  1, 15, 31, 22,  8, 30, 12, 25,  3, 29, 10,
       33], dtype=int64), 'cur_cost': 67790.0, 'intermediate_solutions': [{'tour': array([20, 18,  3, 21, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 67259.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([21, 20, 18,  3, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 67239.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([17, 21, 20, 18,  3,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 64964.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3, 21, 20, 18, 17,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 65219.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 17, 21, 20, 18,  6, 30,  5,  4, 23, 27,  9, 31, 12,  1, 19, 26,
       10, 34, 11,  2, 15, 25,  8, 29, 32,  0, 16, 24, 33, 14,  7, 13, 22,
       28]), 'cur_cost': 65234.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 2, 1, 24, 13, 3, 4, 6, 12, 16, 10, 14, 0, 15, 11, 8, 5, 19, 17, 7, 23, 25, 28, 26, 34, 31, 33, 27, 30, 29, 22, 20, 21, 9, 32], 'cur_cost': 35453.0, 'intermediate_solutions': [{'tour': [0, 16, 19, 9, 14, 11, 4, 13, 8, 15, 12, 3, 5, 1, 7, 6, 10, 2, 23, 17, 21, 18, 20, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 17839.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 31, 28, 34, 25, 30, 27, 32, 29, 33, 26], 'cur_cost': 11834.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 30, 16, 19, 9, 14, 11, 10, 13, 8, 15, 12, 3, 5, 1, 7, 6, 4, 2, 23, 17, 21, 18, 20, 22, 24, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14975.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0, 33, 10, 26, 12, 34, 32, 16, 24, 25, 11, 15,  1, 19,  3, 30, 14,
       17, 18,  8, 23, 13,  4,  7,  5, 20, 22, 21, 27, 31, 28,  6, 29,  9,
        2], dtype=int64), 'cur_cost': 55159.0, 'intermediate_solutions': [{'tour': array([ 9, 18,  3,  7, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 54147.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([ 7,  9, 18,  3, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 54049.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([10,  7,  9, 18,  3, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 57130.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([ 3,  7,  9, 18, 10, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 53745.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([ 3, 10,  7,  9, 18, 15, 27, 23, 20, 19, 26, 34,  1, 14,  8, 29,  0,
       12,  2, 28, 22, 30,  6, 24, 11, 13, 33, 32, 16, 17, 21, 31,  5, 25,
        4]), 'cur_cost': 56751.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [20, 1, 7, 16, 17, 23, 6, 10, 11, 14, 9, 2, 22, 12, 13, 19, 3, 8, 0, 21, 4, 24, 5, 26, 25, 34, 28, 27, 31, 33, 32, 30, 29, 18, 15], 'cur_cost': 32090.0, 'intermediate_solutions': [{'tour': [15, 3, 2, 1, 24, 20, 5, 28, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 21, 13, 30, 10, 27, 29, 6, 22], 'cur_cost': 45576.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 28, 30, 13, 21, 18, 7, 33, 31, 32, 34, 26, 25, 4, 9, 16, 19, 14, 11, 0, 12, 23, 17, 8, 27, 29, 6, 22], 'cur_cost': 42236.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [15, 3, 2, 1, 24, 20, 5, 10, 8, 17, 23, 12, 0, 11, 14, 19, 16, 9, 4, 25, 26, 34, 32, 31, 33, 7, 18, 27, 21, 13, 30, 28, 29, 6, 22], 'cur_cost': 41684.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [25, 33, 3, 5, 23, 18, 1, 6, 22, 20, 10, 7, 12, 0, 11, 9, 15, 21, 19, 16, 4, 24, 17, 30, 26, 34, 31, 28, 29, 2, 14, 13, 8, 32, 27], 'cur_cost': 28962.0, 'intermediate_solutions': [{'tour': [13, 5, 10, 9, 14, 11, 12, 15, 8, 0, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12350.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [27, 31, 28, 34, 25, 30, 24, 22, 16, 20, 18, 19, 21, 17, 23, 2, 4, 6, 7, 1, 3, 13, 8, 15, 12, 11, 14, 9, 10, 5, 0, 32, 29, 33, 26], 'cur_cost': 9701.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 5, 10, 14, 11, 12, 15, 8, 13, 3, 1, 7, 6, 4, 2, 23, 17, 21, 19, 18, 20, 16, 9, 22, 24, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 14401.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([20,  0,  5,  1, 13, 26, 12, 17,  3,  6, 23,  4, 18,  9, 21,  8, 10,
       25, 15, 19, 16, 31, 27, 33, 29, 24,  7,  2, 30, 34, 32, 11, 28, 22,
       14], dtype=int64), 'cur_cost': 54077.0, 'intermediate_solutions': [{'tour': array([ 1, 27, 12, 33,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63139.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_2'}, {'tour': array([33,  1, 27, 12,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 60902.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_3'}, {'tour': array([ 9, 33,  1, 27, 12,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63597.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_0_4'}, {'tour': array([12, 33,  1, 27,  9,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 63596.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_3'}, {'tour': array([12,  9, 33,  1, 27,  3, 26, 20, 11, 29, 18, 31, 13, 28, 25, 14, 24,
       23,  7, 15, 34, 10, 17, 21,  0,  5,  4,  2, 16, 19, 32, 30,  8, 22,
        6]), 'cur_cost': 61032.0, 'strategy_type': 'exploitation_2opt', 'generation_method': '2opt_1_4'}], 'strategy_type': 'exploitation_local_search'}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [12, 21, 3, 26, 7, 29, 2, 5, 15, 1, 18, 34, 11, 6, 23, 25, 30, 33, 31, 22, 28, 4, 8, 9, 13, 0, 16, 19, 27, 14, 20, 10, 32, 17, 24], 'cur_cost': 57643.0, 'intermediate_solutions': [{'tour': [20, 30, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 7, 8, 5, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 18, 28, 2, 14, 9, 13, 26], 'cur_cost': 39281.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 9, 14, 2, 28, 30, 33, 32, 27, 31, 29, 34, 25, 19, 0, 3, 24, 5, 8, 7, 13, 26], 'cur_cost': 30540.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [20, 18, 4, 23, 1, 22, 6, 16, 17, 21, 10, 15, 12, 11, 5, 7, 8, 24, 3, 0, 19, 25, 34, 29, 31, 27, 32, 33, 30, 28, 2, 14, 9, 13, 26], 'cur_cost': 30175.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 18, 4, 1, 7, 6, 2, 5, 3, 23, 17, 21, 19, 20, 16, 22, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 11767.0, 'intermediate_solutions': [{'tour': [9, 19, 15, 27, 33, 20, 6, 8, 31, 21, 13, 26, 14, 5, 3, 0, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 10, 11, 18, 24, 23, 17], 'cur_cost': 53970.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [9, 19, 15, 27, 29, 34, 7, 0, 3, 5, 14, 26, 10, 21, 31, 8, 6, 20, 33, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 53977.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [9, 19, 15, 27, 0, 33, 20, 6, 8, 31, 21, 10, 26, 14, 5, 3, 7, 34, 29, 30, 2, 25, 28, 1, 16, 4, 22, 12, 32, 13, 11, 18, 24, 23, 17], 'cur_cost': 57756.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 21, 19, 18, 20, 16, 22, 24, 23, 17, 4, 7, 2, 5, 3, 11, 14, 9, 15, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 9645.0, 'intermediate_solutions': [{'tour': [0, 15, 18, 24, 22, 30, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 16, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 19808.0, 'strategy_type': 'exploration_candidate_0', 'generation_method': 'swap'}, {'tour': [0, 15, 18, 24, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 31, 28, 34, 25, 30, 12, 27, 32, 29, 33, 26], 'cur_cost': 18469.0, 'strategy_type': 'exploration_candidate_1', 'generation_method': '2opt'}, {'tour': [0, 15, 24, 18, 22, 16, 21, 19, 20, 17, 23, 6, 7, 1, 2, 5, 3, 4, 11, 14, 9, 8, 10, 13, 12, 30, 25, 34, 28, 31, 27, 32, 29, 33, 26], 'cur_cost': 12506.0, 'strategy_type': 'exploration_candidate_2', 'generation_method': 'insertion'}], 'strategy_type': 'exploration'}}]
2025-08-05 10:28:54,406 - experts.management.collaboration_manager - INFO - 进化阶段完成
2025-08-05 10:28:54,406 - StatsExpert - INFO - 开始统计分析
2025-08-05 10:28:54,408 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9645.000, 多样性=0.952
2025-08-05 10:28:54,408 - experts.management.collaboration_manager - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-08-05 10:28:54,408 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment (Algorithm-based) ---
2025-08-05 10:28:54,408 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-08-05 10:28:54,409 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 30, 'iteration': 4, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.024571544732318814, 'best_improvement': -0.0006224712107065049}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.0}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'deteriorating', 'trend_strength': 0.08009475478861723, 'recent_improvements': [0.025290547282971216, 0.07172268424880758, -0.13489896229426326], 'convergence_status': 'decelerating'}, 'elite_analysis': {'old_count': 9, 'new_count': 9, 'count_change': 0, 'old_best_cost': 9061, 'new_best_cost': 9061, 'quality_improvement': 0.0, 'old_diversity': 0.7698412698412698, 'new_diversity': 0.7698412698412698, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化', '收敛速度放缓，建议调整参数或改变策略分配', '精英解数量未增加，建议调整精英解选择标准']}
2025-08-05 10:28:54,410 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-08-05 10:28:54,413 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite5_35_solution.json
2025-08-05 10:28:54,413 - __main__ - INFO - 解集文件已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - src-0701\src\results\solutions\composite5_35_20250805_102854.solution
2025-08-05 10:28:54,413 - __main__ - INFO - 实例执行完成 - 运行时间: 1.64s, 最佳成本: 9061
2025-08-05 10:28:54,413 - __main__ - INFO - 实例 composite5_35 处理完成
